<?php

namespace App\Exports;

use App\Models\Branch;
use App\Models\PlusCard;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class PlusCardReportExport extends ExcelHeaderLogoAdder implements FromCollection , WithHeadings, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function headings(): array
    {
        return [
            'Şube',
            'Toplam',
            'Kullanılan',
            'Kalan',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1    => [
                'font' => [
                    'bold' => true,
                    'color'=>[
                        'rgb'=>Color::COLOR_WHITE
                    ]
                ],
                'fill' => [
                    'fillType'   => Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => Color::COLOR_RED
                    ],
                ],
            ],
        ];
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        ini_set('memory_limit', '20024M');
        $exports = [];

        $branches = Branch::where('status',1)->select('kisa_ad','id')->get();
        foreach ($branches as $branch){
            $toplam = PlusCard::where('branch_id',$branch->id)->count() ?? 0;
            $kullanilan = PlusCard::where('branch_id',$branch->id)->where('customer_id','!=',null)->count() ?? 0;
            $kalan = $toplam - $kullanilan;
            $export = [
                'sube' => $branch->kisa_ad,
                'toplam' => (string)$toplam ?? '0',
                'kullanilan' => (string)$kullanilan ?? '0',
                'kalan' => (string)$kalan ?? '0'
            ];

            array_push($exports, $export);
        }

        return collect($exports);
    }
}
