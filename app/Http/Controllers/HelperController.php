<?php

namespace App\Http\Controllers;

use App\Imports\BranchsImport;
use App\Imports\CampaignsImport;
use App\Imports\CarCaseTypesImport;
use App\Imports\CarFuelsImport;
use App\Imports\CarGearsImport;
use App\Imports\CarGroupsImport;
use App\Imports\CarsImport;
use App\Imports\CustomerGroupsImport;
use App\Imports\CustomersImport;
use App\Imports\CustomerTypesImport;
use App\Imports\ExpertisesImport;
use App\Imports\StockGroupsImport;
use App\Imports\StocksImport;
use App\Imports\StockTypesImport;
use App\Imports\StockUnitsImport;
use App\Imports\UsersImport;
use App\Models\Branch;
use App\Models\BranchSavedFilter;
use App\Models\Campaign;
use App\Models\Car;
use App\Models\CarCaseType;
use App\Models\CarFuel;
use App\Models\CarGear;
use App\Models\CarGroup;
use App\Models\CarSavedFilter;
use App\Models\CheckListControl;
use App\Models\CheckOldExpertisDowland;
use App\Models\City;
use App\Models\Customer;
use App\Models\CustomerCar;
use App\Models\CustomerSavedFilter;
use App\Models\ExcelDownload;
use App\Models\Expertise;
use App\Models\ExpertisePayment;
use App\Models\ExpertiseReportDownload;
use App\Models\ExpertiseSavedFilter;
use App\Models\ExpertiseStock;
use App\Models\Log;
use App\Models\PaymentLog;
use App\Models\PlusCard;
use App\Models\PlusCardAgreement;
use App\Models\PlusCardCampaign;
use App\Models\PlusCardCrediAndPuanAdd;
use App\Models\PlusCardCrediAndPuanRemove;
use App\Models\PlusCardsDefinitions;
use App\Models\PlusCardsSavedFilter;
use App\Models\QueryLog;
use App\Models\QueryLogExtra;
use App\Models\Setting;
use App\Models\SmsVerification;
use App\Models\Stock;
use App\Models\StockPrice;
use App\Models\StockType;
use App\Models\T404KUPONTAKIP;
use App\Models\Town;
use App\Models\User;
use App\Models\UserRoleGroup;
use App\Models\UserSavedFilter;
use App\Models\ZoneBranch;
use App\Services\PlusCardCampaignService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use OTPHP\TOTP;
use SoapClient;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;




class HelperController extends Controller
{
    public function kvkkOnay(){
        return view('pages.kvkk');
    }

    public function forgotPassword(){
        return view('pages.forgot_password');
    }

    public function getCustomerPlusCards(Request $request)
    {
        $plusCards = PlusCard::where('plus_cards.customer_id',$request->customer_id)->get();
        $items = [];
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        if(!empty($request->servis_bayi)){
            $authUserBranchIds = [$request->servis_bayi];
        }
        foreach ($plusCards as $plusCard){
            $items[$plusCard->id] = [
                'id' => $plusCard->id,
                'sube' => $plusCard->getBranch ? $plusCard->getBranch->kisa_ad : '-',
                'valid_date' => \Carbon\Carbon::make($plusCard->valid_date)->format('d.m.Y'),
                'kredi' =>  $plusCard->getTotalBalanceStock($request->stock_id,$authUserBranchIds)['credits'],
                'puan' => $plusCard->getTotalBalanceStock($request->stock_id,$authUserBranchIds)['points'],
                'system_id' => $plusCard->system_id ?? '',
                'stock_id' => $request->stock_id,
                'card_id' => $plusCard->id,
                'no' => $plusCard->no,

            ];

        }

        return response()->json(['success'=>'true','items'=>$items]);
    }

    public function forgotPasswordPost(Request $request){
        if ($request->password_reset_token){
            $user = User::where('password_reset_token',$request->password_reset_token)->first();
            if ($user && $request->password_reset_token != ''){
                $user->password_reset_token = strtoupper(Str::random(10));
                $user->save();

                if ($request->password == $request->password_again){
                    $user->password = bcrypt($request->password);
                    $user->save();

                    Auth::login($user);

                    logRecord("reset_password",$user->name . " adlı kullanıcı şifresini sıfırladı.",$user->id);

                    Auth::logout();
                    $request->session()->invalidate();

                    $request->session()->regenerateToken();
                }else{
                    return redirect()->route('forgotPassword')->with('error','Girilen Şifreler Uyuşmuyor!');
                }


                return redirect()->route('index')->with('success','Şifreniz Sıfırlandı!');
            }
            return redirect()->route('forgotPassword')->with('error','Geçersiz Bağlantı Linki');
        }elseif(!empty($request->sms_code) && $request->sms_code){
            $user = User::where('password_reset_code',$request->sms_code)->first();
            if(!empty($user)){
                return redirect()->route('forgotPassword',['token'=>$user->password_reset_token]);
            }else{
                return redirect()->route('forgotPassword')->with('error','Geçersiz Sms Kodu');
            }
        }else{
            $tel = str_replace(['(',')',' '],'',$request->telephone);
            $user = User::where('telephone',$tel)
                //->orWhere('second_telephone',$tel)
            ->first();
            if ($user){
                $reset_code =mt_rand(100000, 999999);
                $user->password_reset_token = strtoupper(Str::random(10));
                $user->password_reset_code = $reset_code;
                $user->save();

                $settings = Setting::first();
                if ($settings->netgsm_active == 1){
                    if (substr($tel, 0, 1) === '0') {
                        $tel = substr($tel, 1);
                    }
                    $message ="Şifre Yenileme Kodunuz '".$reset_code."' Lütfen, kimseyle paylaşmayınız.";
                    $return = sendSmsOTP($settings->netgsm_usercode,$settings->netgsm_password,$settings->netgsm_msgheader,$message,$tel);

                }

                return redirect()->route('forgotPassword',['sms_send'=>'success'])->with('success','Şifre Yenileme Kodunuz Sms Olarak Gönderildi');
            }
            return redirect()->route('forgotPassword')->with('error','Kayıtlı Telefon Bulunamadı!');
        }

    }

    public function menu(){
        if (!\auth()->user()->id != 1)
            return redirect()->route('index')->with('error','Yetkiniz Yok!');
        return view('pages.menu_list');
    }

    public function importExcel(){
        return view('pages.import_excel');
    }

    public function importExcelPost(Request $request){
        if (!$request->hasFile('file'))
            return back()->with('error','Dosya Yüklemediniz!');
        if ($request->file->getSize() / 1024 / 1024 > 2){
            return back()->with('error','En Fazla 2MB Dosya Yüklemeye İzin Verilir!');
        }
        $authUser = \auth()->user();
        $authUserRoleGroup = $authUser?->getUserRoleGroup;
        if ($request->type == 'branch'){
            if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_branch'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new BranchsImport(),$request->file);
        }

        elseif ($request->type == 'campaign'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_campaign'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new CampaignsImport(),$request->file);
        }

        elseif ($request->type == 'car_case_type'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_car_case_type'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new CarCaseTypesImport(),$request->file);
        }

        elseif ($request->type == 'car_fuel'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_car_fuel'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new CarFuelsImport(),$request->file);
        }

        elseif ($request->type == 'car_group'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_car_group'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new CarGroupsImport(),$request->file);
        }

        elseif ($request->type == 'customer_group'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_customer_group'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new CustomerGroupsImport(),$request->file);
        }

        elseif ($request->type == 'customer'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_customer'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new CustomersImport(),$request->file);
        }

        elseif ($request->type == 'customer_type'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_customer_type'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new CustomerTypesImport(),$request->file);
        }

        elseif ($request->type == 'stock_group'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_stock_group'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new StockGroupsImport(),$request->file);
        }

        elseif ($request->type == 'stock'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_stock'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new StocksImport(),$request->file);
        }

        elseif ($request->type == 'stock_type'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_stock_type'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new StockTypesImport(),$request->file);
        }

        elseif ($request->type == 'stock_unit'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_stock_unit'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new StockUnitsImport(),$request->file);
        }

        elseif ($request->type == 'user'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_user'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new UsersImport(),$request->file);
        }

        elseif ($request->type == 'car'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_car'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new CarsImport(),$request->file);
        }

        elseif ($request->type == 'car_gear'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_car_gear'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
            Excel::import(new CarGearsImport(),$request->file);
        }

        elseif ($request->type == 'expertise'){
              if (!$authUserRoleGroup || !$authUserRoleGroup->getRoles->whereIn('key',['upload_excel_expertise'])->first())
                return redirect()->route('index')->with('error','Yetkiniz Yok!');
//            $excel = $request->file('file')->store('excel','public2');
//            Excel::import(new ExpertisesImport(),public_path('/storage/'.$excel));
            Excel::import(new ExpertisesImport(),$request->file);
        }



        return redirect()->route('importExcel')->with('success','Başarıyla Aktarıldı!');
    }

    public function kvkk(Request $request){
        $customer = Customer::where('id',auth('customer')->user()->id)->first();
        $customer->kvkk_verified = 1;
        $customer->save();

        return response()->json(['success'=>'true']);
    }

    public function setNotificationToken(Request $request){
        $user = User::where('id',$request->user_id)->first();
        if ($user){
            $user->notification_token = $request->notification_token;
            $user->save();

            return response()->json(['success'=>'true']);
        }
        return response()->json(['success'=>'false']);
    }

    public function tcNoDogrula(Request $request)
    {
        $identityNumber = $request->input('tc');

        // Öncelikle yazılan tc matematiksel olarak doğru mu kontrol edecek ve sonra istek atacak şekilde ilerlemeli
        $pattern = '/^[0-9]{11}$/';

        if (preg_match($pattern, $identityNumber) == false) {
            return response()->json(['success' => 'false']);
        }

        if ((int)$identityNumber[0] == 0) {
            return response()->json(['success' => 'false']);
        }

        $odds = (int)$identityNumber[0] + (int)$identityNumber[2] + (int)$identityNumber[4] + (int)$identityNumber[6] + (int)$identityNumber[8];
        $evens = (int)$identityNumber[1] + (int)$identityNumber[3] + (int)$identityNumber[5] + (int)$identityNumber[7];
        $digit10 = ($odds * 7 - $evens) % 10;
        $total = ($odds + $evens + (int)$identityNumber[9]) % 10;

        if ($digit10 != (int)$identityNumber[9] || $total != (int)$identityNumber[10]) {
            //return response()->json(['success' => 'false']);
        }
        // Tc matematiksel olarak doğruysa canlıda kontrol et
        $oturum_izinli = false;
        if (substr($identityNumber, 0, 2) === "99") {
            $oturum_izinli = true;
        }
        if($oturum_izinli){
            $client = new SoapClient("https://tckimlik.nvi.gov.tr/Service/KPSPublicYabanciDogrula.asmx?WSDL");

            $customer_tc_count = Customer::where('tc_no',$identityNumber)->where('status',1)->first();
            if(!empty($customer_tc_count))
                return response()->json([
                    'success' => 'false',
                    'tc_uniq'=>2,
                    'message'=>'T.C. Kimlik No İle Kayıtlı Cari mevcut',
                    'old_customer_id'=>$customer_tc_count->id ?? 0,
                    'customer_type' => $customer_tc_count->type ?? '',
                ]);

            $result = $client->YabanciKimlikNoDogrula([
                'KimlikNo' => $identityNumber,
                'Ad' => $request->ad,
                'Soyad' => $request->soyad,
                'DogumGun' => Carbon::make($request->dogum_tarihi)->format('d'),
                'DogumAy' => Carbon::make($request->dogum_tarihi)->format('m'),
                'DogumYil' => Carbon::make($request->dogum_tarihi)->format('Y'),
            ]);
            return response()->json(['success' => $result->YabanciKimlikNoDogrulaResult ? 'true' : 'false']);
        }else{

            $client = new SoapClient("https://tckimlik.nvi.gov.tr/Service/KPSPublic.asmx?WSDL");

            $customer_tc_count = Customer::where('tc_no',$identityNumber)->where('status',1)->first();
            if(!empty($customer_tc_count))
                return response()->json([
                    'success' => 'false',
                    'tc_uniq'=>2,
                    'message'=>'T.C. Kimlik No İle Kayıtlı Cari mevcut',
                    'old_customer_id'=>$customer_tc_count->id ?? 0,
                    'customer_type' => $customer_tc_count->type ?? '',
                ]);

            $result = $client->TCKimlikNoDogrula([
                'TCKimlikNo' => $identityNumber,
                'Ad' => $request->ad,
                'Soyad' => $request->soyad,
                'DogumYili' => Carbon::make($request->dogum_tarihi)->format('Y'),
            ]);

            return response()->json(['success' => $result->TCKimlikNoDogrulaResult ? 'true' : 'false']);
        }

    }

    public function personalTcControl(Request $request)
    {
        //düzeltme
        $tc = preg_replace("/[^0-9]/", "", $request->tc);
        $user = User::query()->where('tc', $tc)->first();

        if ($user) {
            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false]);
        }
    }

    public function phoneControl(Request $request)
    {
        $phone = preg_replace("/[^0-9]/", "", $request->telephone_login);

        if (strlen($phone) > 10) {
            $phone = substr($phone, -10);
        }

        $user = User::query()
            ->whereRaw("REPLACE(telephone, ' ', '') LIKE ?", ["%{$phone}%"])->first();

        if ($user) {
            return response()->json(['success' => true]);
        } else {
            return response()->json(['success' => false]);
        }
    }


    public function getTowns(Request $request)
    {
        $items = Town::where('ilce_sehirkey',$request->city_id)->get();
        return response()->json(['success'=>'true','items'=>$items]);
    }

    public function voiceRecord(Request $request)
    {
        if ($request->audioFile){
            $file = $request->file('audioFile')->store('expertise','public2');
            $expertise = Expertise::where('uuid',$request->uuid)->first();
            if(!empty($expertise)){
                $expertise->audio_url = $file;
                $expertise->save();
            }

            return response()->json(['success'=>'true','audio_url'=>$file]);
        }
    }

    public function vergiNoKontrol(Request $request)
    {
        $customer = Customer::where('vergi_no',$request->vergi_no)->orWhere('tc_no',$request->vergi_no)->where('status',1)->first();
        if($customer){
            return response()->json(['success'=>'false','message'=>'Vergi No Kayıtlı! Yönlendiriliyorsunuz','customer_id'=>$customer->id]);
        }else
            return response()->json(['success'=>'true']);
    }


    public function saveToSession(Request $request)
    {
        $formData = $request->all();

        // Session'a form verilerini ekle
        Cache::put($request->session_name,$formData);

        return response()->json(['success' => true]);
    }

    public function forgotAuthCode()
    {
        if (!isset($_GET['token']) || $_GET['token'] == '')
            return back()->with('error','Geçersiz Token!');

        $user = User::where('login_token',$_GET['token'])->first();
        if (!$user)
            return back()->with('error','Geçersiz Kullanıcı!');



        if (env('MAIL_CONFIGURED')){
            $otp = TOTP::createFromSecret($user->totp_secret);

            $otp->setLabel('Umran OTO');
            $grCodeUri = $otp->getQrCodeUri(
                'https://api.qrserver.com/v1/create-qr-code/?data=[DATA]&size=150x150&ecc=M',
                '[DATA]'
            );
            \Illuminate\Support\Facades\Mail::send("email.forgot_qr",["email"=>$user->email,"qr"=>$grCodeUri],function ($message) use ($user){
                $message->to($user->email,"Umran OTO Giriş")->subject("Umran OTO Giriş");
            });
        }

        return redirect()->route('loginPage')->with('success','Mail Adresinize Bağlantı Gönderilmiştir');
    }

    public function saveFilters(Request $request)
    {
        if ($request->type == 'expertise'){
            $saveFilter = new ExpertiseSavedFilter();
            $saveFilter->user_id = \auth()->user()->id;
            $saveFilter->name = $request->name;
            $saveFilter->belge_tarihi_baslangic = $request->start_date;
            $saveFilter->belge_tarihi_bitis = $request->end_date;
            $saveFilter->belge_no = $request->belge_no;
            $saveFilter->search = $request->search;
            $saveFilter->branch_id = $request->branch_id;
            $saveFilter->save();

            return response()->json(['success'=>'true','id'=>$saveFilter->id,'name'=>$request->name]);
        }
        elseif ($request->type == 'customer'){
            $saveFilter = new CustomerSavedFilter();
            $saveFilter->user_id = \auth()->user()->id;
            $saveFilter->name = $request->name;
            $saveFilter->unvan = $request->unvan;
            $saveFilter->kod = $request->kod;
            $saveFilter->telefon = $request->telefon;
            $saveFilter->eposta = $request->eposta;
            $saveFilter->web = $request->web;
            $saveFilter->il_kodu = $request->il_kodu;
            $saveFilter->ilce_kodu = $request->ilce_kodu;
            $saveFilter->plus_kart = $request->plus_kart;
            $saveFilter->sms_gonder = $request->sms_gonder;
            $saveFilter->type = $request->customer_type;
            $saveFilter->branch_id = $request->branch_id;
            $saveFilter->save();

            return response()->json(['success'=>'true','id'=>$saveFilter->id,'name'=>$request->name]);
        } elseif ($request->type == 'branch') {
            $saveFilter = BranchSavedFilter::where('user_id', auth()->user()->id)
                ->where('id', $request->filter_id)
                ->first();

            if (empty($saveFilter)) {
                $saveFilter = new BranchSavedFilter();
                $saveFilter->user_id = auth()->user()->id;
            }

            $saveFilter->name = $request->name;
            $saveFilter->unvan = $request->unvan;
            $saveFilter->kisa_ad = $request->kisa_ad;
            $saveFilter->kod = $request->kod;
            $saveFilter->il_kodu = $request->il_kodu;
            $saveFilter->ilce_kodu = $request->ilce_kodu;

            try {
                $saveFilter->save();
            } catch (\Exception $e) {
                return response()->json(['success' => 'false', 'message' => $e->getMessage()]);
            }

            return response()->json(['success' => 'true', 'id' => $saveFilter->id, 'name' => $request->name]);
        } elseif ($request->type == 'car') {
            $saveFilter = new CarSavedFilter();
            $saveFilter->user_id = \auth()->user()->id;
            $saveFilter->name = $request->name;
            $saveFilter->plaka = $request->plaka;
            $saveFilter->sase_no = $request->sase_no;
            $saveFilter->customer_name = $request->customer_name;
            $saveFilter->branch_id = $request->branch_id;
            $saveFilter->save();

            return response()->json(['success'=>'true','id'=>$saveFilter->id,'name'=>$request->name]);
        } elseif ($request->type == 'user') {
            $saveFilter = UserSavedFilter::where('user_id', auth()->user()->id)
                ->where('id', $request->filter_id)
                ->first();

            if (empty($saveFilter)) {
                $saveFilter = new UserSavedFilter();
                $saveFilter->user_id = auth()->user()->id;
            }

            $saveFilter->name = $request->name;
            $saveFilter->user_name = $request->user_name;
            $saveFilter->second_name = $request->second_name;
            $saveFilter->surname = $request->surname;
            $saveFilter->email = $request->email;
            $saveFilter->tc = $request->tc;
            $saveFilter->telephone = $request->telephone;
            $saveFilter->branch_id = $request->branch_id;

            try {
                $saveFilter->save();
            } catch (\Exception $e) {
                return response()->json(['success' => 'false', 'message' => $e->getMessage()]);
            }

            return response()->json(['success' => 'true', 'id' => $saveFilter->id, 'name' => $request->name]);
        }
        elseif ($request->type == "plus_kart"){
            $saveFilter = new PlusCardsSavedFilter();
            $saveFilter->user_id = \auth()->user()->id;
            $saveFilter->name = $request->name;
            $saveFilter->card_no = $request->card_no;
            $saveFilter->branch_id = $request->branch_id;
            $saveFilter->customer_code = $request->customer_code;
            $saveFilter->customer_name = $request->customer_name;
            $saveFilter->save();

            return response()->json(['success'=>'true','id'=>$saveFilter->id,'name'=>$request->name]);
        }

        return response()->json(['success'=>'false']);
    }

    public function report($type)
    {
        $authUser = auth()->user();
        if ($type == 'stock'){
            if ($authUser->type == 'admin' || $authUser->zone_id > 0){
                return view('pages.report.stock',$this->stockReport());
            }else{
                return back()->with('error','Yetkiniz Yok!');
            }
        }elseif ($type == 'customer'){
            return view('pages.report.customer',$this->customerReport());
        }elseif ($type == 'branch'){
            if ($authUser->type == 'admin' || $authUser->zone_id > 0){
                return view('pages.report.branch',$this->branchReport());
            }else{
                return back()->with('error','Yetkiniz Yok!');
            }
        }elseif ($type == 'query'){
            if ($authUser->type == 'admin' || $authUser->zone_id > 0){
                return view('pages.report.query',$this->queryReport());
            }else{
                return back()->with('error','Yetkiniz Yok!');
            }
        }elseif ($type == 'car'){
            return view('pages.report.car',$this->carReport());
        }elseif ($type == 'user'){
            return view('pages.report.user',$this->userReport());
        }elseif($type == 'expertise'){
            return view('pages.report.expertise',$this->expertiseReport());
        }elseif($type == "endofdays"){
            return view('pages.report.endofday',$this->endDayOfReport());
        }

    }

    private function queryReport()
    {
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        $startDate = isset($_GET['start_date']) && $_GET['start_date'] != '' ? Carbon::make($_GET['start_date'])->startOfDay() : Carbon::now()->startOfDay();
        $endDate = isset($_GET['end_date']) && $_GET['end_date'] != '' ? Carbon::make($_GET['end_date'])->endOfDay() : Carbon::now()->endOfDay();
        $branchId = isset($_GET['branch_id']) && $_GET['branch_id'] > 0 ? $_GET['branch_id'] : null;

        $branches = Branch::when($branchId, function ($query) use ($branchId) {
            return $query->where('id', $branchId);
        })->select('id', 'kisa_ad','kod')->get();

        $branches = $branches->map(function ($branch) use ($startDate, $endDate) {
            $queries = QueryLog::whereBetween('created_at', [$startDate, $endDate])
                ->whereHas('ekspertiz', function ($query) use ($branch) {
                    $query->where('branch_id', $branch->id);
                })
                ->when(isset($_GET['hasar_sorgu_firma']) && $_GET['hasar_sorgu_firma'] != '', function ($query){
                    if ($_GET['hasar_sorgu_firma'] == 'otosorgu.com') {
                       return $query->whereNot('hasar', 'like', "%arabasorgula.com%");
                    }else{
                       return $query->where('hasar', 'like', "%arabasorgula.com%");
                    }
                })
                ->when(isset($_GET['kilometre_sorgu_firma']) && $_GET['kilometre_sorgu_firma'] != '', function ($query){
                    if ($_GET['kilometre_sorgu_firma'] == 'otosorgu.com') {
                        return $query->whereNot('kilometre', 'like', "%arabasorgula.com%");
                    }else{
                        return $query->where('kilometre', 'like', "%arabasorgula.com%");
                    }
                })
                ->when(isset($_GET['borc_sorgu_firma']) && $_GET['borc_sorgu_firma'] != '', function ($query){
                    if ($_GET['borc_sorgu_firma'] == 'otosorgu.com') {
                        return $query->whereNot('borc', 'like', "%arabasorgula.com%");
                    }else{
                        return $query->where('borc', 'like', "%arabasorgula.com%");
                    }
                })
                ->when(isset($_GET['ruhsat_sorgu_firma']) && $_GET['ruhsat_sorgu_firma'] != '', function ($query){
                    if ($_GET['ruhsat_sorgu_firma'] == 'otosorgu.com') {
                        return $query->whereNot('ruhsat', 'like', "%arabasorgula.com%");
                    }else{
                        return $query->where('ruhsat', 'like', "%arabasorgula.com%");
                    }
                })
                ->when(isset($_GET['degisen_sorgu_firma']) && $_GET['degisen_sorgu_firma'] != '', function ($query){
                    if ($_GET['degisen_sorgu_firma'] == 'otosorgu.com') {
                        return $query->whereNot('degisen', 'like', "%arabasorgula.com%");
                    }else{
                        return $query->where('degisen', 'like', "%arabasorgula.com%");
                    }
                })
                ->when(isset($_GET['detail_sorgu_firma']) && $_GET['detail_sorgu_firma'] != '', function ($query){
                    if ($_GET['detail_sorgu_firma'] == 'otosorgu.com') {
                        return $query->whereNot('detail', 'like', "%arabasorgula.com%");
                    }else{
                        return $query->where('detail', 'like', "%arabasorgula.com%");
                    }
                })
                ->get();

            $extraQueries = QueryLogExtra::whereBetween('created_at', [$startDate, $endDate])
                ->whereHas('ekspertiz', function ($query) use ($branch) {
                    $query->where('branch_id', $branch->id);
                })->get();

            $fields = ['borc', 'hasar', 'kilometre', 'ruhsat', 'degisen', 'detail'];
            $totals = [];
            $counts = [];
            $data = [];

            // Tüm alanlar için başlangıç değerleri
            foreach ($fields as $field) {
                $totals[$field] = 0;
                $counts[$field] = 0;
                $data[$field] = [];

                if (isset($_GET[$field.'_sorgu_firma']) && $_GET[$field.'_sorgu_firma'] != '') {
                    if ($_GET[$field.'_sorgu_firma'] == 'otosorgu.com') {
                        $extraQueries = $extraQueries->reject(function ($item) use ($field) {
                            return $item->type === $field && str_contains($item->result, 'arabasorgula.com');
                        });
                    } else {
                        $extraQueries = $extraQueries->reject(function ($item) use ($field)  {
                            return $item->type === $field && !str_contains($item->result, 'arabasorgula.com');
                        });
                    }
                }
            }

            foreach ($extraQueries as $extraQuery){
                if (isset($_GET['sorgu_firma']) && $_GET['sorgu_firma'] != '') {
                    if ($_GET['sorgu_firma'] == 'otosorgu.com'){
                        if (!isset($extraQuery['result']['from'])){
                            if (!is_null($extraQuery['amount'])){
                                $price = $extraQuery['amount'] ?? 0;
                            }else{
                                $result = $extraQuery['result'];
                                $price = $result['data']['amount'] ?? 0;
                            }
                            if (isset($_GET['add_commissions']) && $_GET['add_commissions'] == 1)
                                $price += $extraQuery['commission'] ?? 0;

                            $totals[$extraQuery['type']] += $price;
                            $extraQuery[$extraQuery['type'].'_komisyon_dahil_ucret'] = $price;

                            $counts[$extraQuery['type']]++;
                            $data[$extraQuery['type']][] = $extraQuery;
                        }
                    }
                    elseif ($_GET['sorgu_firma'] == 'arabasorgula.com'){
                        if (isset($extraQuery['result']['from']) && $extraQuery['result']['from'] == 'arabasorgula.com'){
                            if (!is_null($extraQuery['amount'])){
                                $price = $extraQuery['amount'] ?? 0;
                            }else{
                                $result = $extraQuery['result'];
                                $price = $result['data']['amount'] ?? 0;
                            }
                            if (isset($_GET['add_commissions']) && $_GET['add_commissions'] == 1)
                                $price += $extraQuery['commission'] ?? 0;

                            $totals[$extraQuery['type']] += $price;
                            $extraQuery[$extraQuery['type'].'_komisyon_dahil_ucret'] = $price;

                            $counts[$extraQuery['type']]++;
                            $data[$extraQuery['type']][] = $extraQuery;
                        }
                    }
                }
                else{
                    if (!is_null($extraQuery['amount'])){
                        $price = $extraQuery['amount'] ?? 0;
                    }else{
                        $result = $extraQuery['result'];
                        $price = $result['data']['amount'] ?? 0;
                    }
                    if (isset($_GET['add_commissions']) && $_GET['add_commissions'] == 1)
                        $price += $extraQuery['commission'] ?? 0;

                    $totals[$extraQuery['type']] += $price;
                    $extraQuery[$extraQuery['type'].'_komisyon_dahil_ucret'] = $price;
                    $counts[$extraQuery['type']]++;
                    $data[$extraQuery['type']][] = $extraQuery;
                }
            }

            foreach ($queries as $query) {
                foreach ($fields as $field) {
                    if (!is_null($query->$field)) {
                        if (isset($_GET['sorgu_firma']) && $_GET['sorgu_firma'] != '') {
                            if ($_GET['sorgu_firma'] == 'otosorgu.com'){
                                if (!isset(json_decode($query->$field, true)['from'])){
                                    if (!is_null($query[$field.'_ucret'])){
                                        $price = $query[$field.'_ucret'] ?? 0;
                                    }else{
                                        $result = json_decode($query->$field, true);
                                        $price = $result['data']['amount'] ?? 0;
                                    }
                                    if (isset($_GET['add_commissions']) && $_GET['add_commissions'] == 1)
                                        $price += $query[$field.'_komisyon'] ?? 0;

                                    $totals[$field] += $price;
                                    $query[$field.'_komisyon_dahil_ucret'] = $price;

                                    $counts[$field]++;
                                    $data[$field][] = $query;
                                }
                            }
                            elseif ($_GET['sorgu_firma'] == 'arabasorgula.com'){
                                if (isset(json_decode($query->$field, true)['from']) && json_decode($query->$field, true)['from'] == 'arabasorgula.com'){
                                    if (!is_null($query[$field.'_ucret'])){
                                        $price = $query[$field.'_ucret'] ?? 0;
                                    }else{
                                        $result = json_decode($query->$field, true);
                                        $price = $result['data']['amount'] ?? 0;
                                    }
                                    if (isset($_GET['add_commissions']) && $_GET['add_commissions'] == 1)
                                        $price += $query[$field.'_komisyon'] ?? 0;

                                    $totals[$field] += $price;
                                    $query[$field.'_komisyon_dahil_ucret'] = $price;

                                    $counts[$field]++;
                                    $data[$field][] = $query;
                                }
                            }
                        }
                        else{
                            if (!is_null($query[$field.'_ucret'])){
                                $price = $query[$field.'_ucret'] ?? 0;
                            }else{
                                $result = json_decode($query->$field, true);
                                $price = $result['data']['amount'] ?? 0;
                            }
                            if (isset($_GET['add_commissions']) && $_GET['add_commissions'] == 1)
                                $price += $query[$field.'_komisyon'] ?? 0;

                            $totals[$field] += $price;
                            $query[$field.'_komisyon_dahil_ucret'] = $price;

                            $counts[$field]++;
                            $data[$field][] = $query;
                        }
                    }
                }
            }

            return [
                'id' => $branch->id,
                'kod' => $branch->kod,
                'kisa_ad' => $branch->kisa_ad,
                'borcAdet' => $counts['borc'] ?? 0,
                'hasarAdet' => $counts['hasar'] ?? 0,
                'kilometreAdet' => $counts['kilometre'] ?? 0,
                'ruhsatAdet' => $counts['ruhsat'] ?? 0,
                'degisenAdet' => $counts['degisen'] ?? 0,
                'detayAdet' => $counts['detail'] ?? 0,
                'borcTutar' => $totals['borc'] ?? 0,
                'hasarTutar' => $totals['hasar'] ?? 0,
                'kilometreTutar' => $totals['kilometre'] ?? 0,
                'ruhsatTutar' => $totals['ruhsat'] ?? 0,
                'degisenTutar' => $totals['degisen'] ?? 0,
                'detayTutar' => $totals['detail'] ?? 0,
                'borc' => $data['borc'],
                'hasar' => $data['hasar'],
                'kilometre' => $data['kilometre'],
                'ruhsat' => $data['ruhsat'],
                'degisen' => $data['degisen'],
                'detail' => $data['detail'],
            ];
        });

        $filters['startDate'] = $startDate->format('Y-m-d');
        $filters['endDate'] = $endDate->format('Y-m-d');

        return compact('branches', 'filters');
    }

    private function stockReport()
    {
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        $startDate = isset($_GET['start_date']) && $_GET['start_date'] != '' ? Carbon::make($_GET['start_date']) : Carbon::now()->subDays(7);
        $endDate = isset($_GET['end_date']) && $_GET['end_date'] != '' ? Carbon::make($_GET['end_date']) : Carbon::now();
        $branchID = isset($_GET['branch_id']) ? [$_GET['branch_id']] : $authUserBranchIds;
        $diffInDays = $endDate->diffInDays($startDate);

        if ($diffInDays >= 7)
            $interval = ceil($diffInDays / 7);
        elseif ($diffInDays >= 5)
            $interval = ceil($diffInDays / 5);
        elseif ($diffInDays >= 3)
            $interval = ceil($diffInDays / 5);
        elseif ($diffInDays >= 0)
            $interval = ceil($diffInDays / 1);

        $stock = Stock::query();
        $totalStockCount = $stock->count();
        $activeStockCount = $stock->where('status',1)->count();
        $newAddedStockCount = $stock->whereDate('created_at','>=',$startDate)->whereDate('created_at','<=',$endDate)->count();
        $mostUsedStockId = ExpertiseStock::select('stock_id', \DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2)use($branchID){return $query2->whereIn('branch_id',$branchID);});})
            ->groupBy('stock_id')
            ->orderByDesc('count')
            ->first()
            ->stock_id ?? 0;
        $mostUsedStockSellCount = $mostUsedStockId ? ExpertiseStock::where('stock_id',$mostUsedStockId)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2)use($branchID){return $query2->whereIn('branch_id',$branchID);});})
            ->whereDate('created_at','>=',$startDate)
            ->whereDate('created_at','<=',$endDate)
            ->count() : 0;

        $lessUsedStockId = ExpertiseStock::select('stock_id', \DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2)use($branchID){return $query2->whereIn('branch_id',$branchID);});})
            ->groupBy('stock_id')
            ->orderBy('count')
            ->first()
            ->stock_id ?? 0;
        $lessUsedStockSellCount = $lessUsedStockId ? ExpertiseStock::where('stock_id',$lessUsedStockId)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2)use($branchID){return $query2->whereIn('branch_id',$branchID);});})
            ->whereDate('created_at','>=',$startDate)
            ->whereDate('created_at','<=',$endDate)
            ->count() : 0;

        $mostSells = [];
        $lessSells = [];
        $currentStartDate = $startDate;
        for ($i = 0; $i < 7; $i++) {
            // Parçanın bitiş tarihini hesaplayın
            $currentEndDate = $currentStartDate->copy()->addDays($interval - 1);

            // Eğer son parçaysa ve bitiş tarihi bitiş tarihini aşıyorsa, son bitiş tarihini ayarlayın
            if ($i == 6 && $currentEndDate->gt($endDate)) {
                $currentEndDate = $endDate;
            }

            // Parça aralığında satılan miktarı hesaplayın
            $mostSellCount = ExpertiseStock::where('stock_id', $mostUsedStockId)
                ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2)use($branchID){return $query2->whereIn('branch_id',$branchID);});})
                ->whereBetween('created_at', [$currentStartDate, $currentEndDate])
                ->count();

            // Parçayı saklayın
            $mostSells[] = [
                'start_date' => $currentStartDate->format('d.m.Y'),
                'end_date' => $currentEndDate->format('d.m.Y'),
                'sell_count' => $mostSellCount
            ];

            // Parça aralığında satılan miktarı hesaplayın
            $lessSellCount = ExpertiseStock::where('stock_id', $lessUsedStockId)
                ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2)use($branchID){return $query2->whereIn('branch_id',$branchID);});})
                ->whereBetween('created_at', [$currentStartDate, $currentEndDate])
                ->count();

            // Parçayı saklayın
            $lessSells[] = [
                'start_date' => $currentStartDate->format('d.m.Y'),
                'end_date' => $currentEndDate->format('d.m.Y'),
                'sell_count' => $lessSellCount
            ];

            // Bir sonraki parçanın başlangıç tarihini hesaplayın
            $currentStartDate = $currentEndDate->copy()->addDay();
        }

        $results = ExpertiseStock::select('stock_id', \DB::raw('SUM(hizmet_tutari) as total_hizmet_tutari'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2)use($branchID){return $query2->whereIn('branch_id',$branchID);});})
            ->groupBy('stock_id')
            ->get();

// En fazla ve en az 5 değeri bulmak için gruplanmış verileri sırala
        $resultsSorted = $results->sortBy('total_hizmet_tutari');

        $top5SellStocks = $resultsSorted->take(-5)->reverse()->map(function ($item) {
            return [
                'stock_id' => $item->stock_id,
                'stock_name' => $item->getStock->ad,
                'total_hizmet_tutari' => $item->total_hizmet_tutari,
            ];
        })->values()->all();

// En az 5 değeri al ve stok adını getir
        $least5SellStocks = $resultsSorted->take(5)->map(function ($item) {
            return [
                'stock_id' => $item->stock_id,
                'stock_name' => $item->getStock->ad,
                'total_hizmet_tutari' => $item->total_hizmet_tutari,
            ];
        })->values()->all();

        $mostUsed5Stocks = ExpertiseStock::select('stock_id', DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2)use($branchID){return $query2->whereIn('branch_id',$branchID);});})
            ->groupBy('stock_id')
            ->orderByDesc('count')
            ->take(5) // En fazla 5 değeri al
            ->get();

// En az 5 değeri bulmak için ExpertiseStock modelinden sorgu yap
        $leastUsed5Stocks = ExpertiseStock::select('stock_id', DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2)use($branchID){return $query2->whereIn('branch_id',$branchID);});})
            ->groupBy('stock_id')
            ->orderBy('count')
            ->take(5) // En az 5 değeri al
            ->get();

        $mostUsed5Stocks = $mostUsed5Stocks->map(function ($item) {
            return [
                'stock' => $item->getStock->ad,
                'count' => $item->count,
            ];
        })->values()->all();

        $leastUsed5Stocks = $leastUsed5Stocks->map(function ($item) {
            return [
                'stock' => $item->getStock->ad,
                'count' => $item->count,
            ];
        })->values()->all();

        $stockTypes = StockType::where('status',1)->get();

        $filters['startDate'] = $startDate->format('Y-m-d');
        $filters['endDate'] = $endDate->format('Y-m-d');

        return compact([
            'totalStockCount',
            'activeStockCount',
            'newAddedStockCount',
            'mostUsedStockId',
            'lessUsedStockId',
            'mostUsedStockSellCount',
            'lessUsedStockSellCount',
            'mostSells',
            'lessSells',
            'top5SellStocks',
            'least5SellStocks',
            'mostUsed5Stocks',
            'leastUsed5Stocks',
            'stockTypes',
            'filters'
        ]);
    }

    private function customerReport()
    {
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        $startDate = isset($_GET['start_date']) && $_GET['start_date'] != '' ? Carbon::make($_GET['start_date']) : Carbon::now()->subDays(7);
        $endDate = isset($_GET['end_date']) && $_GET['end_date'] != '' ? Carbon::make($_GET['end_date']) : Carbon::now();
        $branchID = isset($_GET['branch_id']) ? [$_GET['branch_id']] : $authUserBranchIds;
        $diffInDays = $endDate->diffInDays($startDate);

        $totalCustomerCount = Customer::when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
        ->count();
        $newAddedCustomerCount = Customer::whereBetween('created_at',[$startDate,$endDate])
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->count();
        $activeCustomerCount = Customer::where('status',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->count();
        $mostExpertiseCustomer = Expertise::select('cari_id', DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->groupBy('cari_id')
            ->orderByDesc('count')
            ->value('cari_id');

        $mostExpertiseCustomerExpertiseCount = (int)$mostExpertiseCustomer > 0 ? Expertise::where('cari_id',(int)$mostExpertiseCustomer)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->count() : 0;

        $top15CustomerCountForCity = Customer::where('il_kodu','!=','')
            ->where('il_kodu','!=',0)
            ->select('il_kodu', DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->groupBy('il_kodu')
            ->orderByDesc('count')
            ->take(15) // En fazla 15 tanesini al
            ->get();

        $top15CustomerCountForCity = $top15CustomerCountForCity->map(function ($query){
            return [
                'il_kodu' => $query->il_kodu,
                'count' => $query->count,
                'sehir' => City::where('id',$query->il_kodu)->first()->title,
            ];
        });

        $customersByType = Customer::select('type', DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->groupBy('type')
            ->pluck('count', 'type');

        $mostCarsOwners = CustomerCar::select('customer_id', DB::raw('COUNT(*) as car_count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getCar',function ($query2)use ($branchID){$query2->whereIn('branch_id',$branchID);});})
            ->groupBy('customer_id')
            ->orderByDesc('car_count')
            ->take(15) // En fazla 15 kişiyi al
            ->get();

        $carCountByCustomer = [];
        $maxCarOwnerCarCount = count($mostCarsOwners) > 0 ? $mostCarsOwners[0]['car_count'] : 0;

        for ($maxCarOwnerCarCount;$maxCarOwnerCarCount > 0;$maxCarOwnerCarCount--){
            $carCountByCustomer[$maxCarOwnerCarCount] = $mostCarsOwners->where('car_count',$maxCarOwnerCarCount)->count();
        }

        $filters['startDate'] = $startDate->format('Y-m-d');
        $filters['endDate'] = $endDate->format('Y-m-d');

        return compact([
            'totalCustomerCount',
            'newAddedCustomerCount',
            'activeCustomerCount',
            'mostExpertiseCustomer',
            'top15CustomerCountForCity',
            'mostExpertiseCustomerExpertiseCount',
            'customersByType',
            'carCountByCustomer',
            'filters'
        ]);
    }

    private function branchReport()
    {
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        $startDate = isset($_GET['start_date']) && $_GET['start_date'] != '' ? Carbon::make($_GET['start_date']) : Carbon::now()->subDays(7);
        $endDate = isset($_GET['end_date']) && $_GET['end_date'] != '' ? Carbon::make($_GET['end_date']) : Carbon::now();
        $branchID = isset($_GET['branch_id']) ? [$_GET['branch_id']] : $authUserBranchIds;
        $diffInDays = $endDate->diffInDays($startDate);

        $totalBranchCount = Branch::count();
        $newAddedBranchCount = Branch::whereBetween('created_at',[$startDate,$endDate])->count();
        $activeBranchCount = Branch::where('status',1)->count();

        $mostExpertiseBranch = Expertise::select('branch_id', DB::raw('COUNT(*) as count'))
            ->groupBy('branch_id')
            ->orderByDesc('count')
            ->value('branch_id');

        $mostExpertiseBranchExpertiseCount = (int)$mostExpertiseBranch > 0 ? Expertise::where('branch_id',(int)$mostExpertiseBranch)->count() : 0;
        $mostExpertiseBranchExpertiseCountSelectedDate = (int)$mostExpertiseBranch > 0 ? Expertise::where('branch_id',(int)$mostExpertiseBranch)->whereBetween('created_at',[$startDate,$endDate])->count() : 0;

        $mostCustomerBranch = Customer::select('branch_id', DB::raw('COUNT(*) as count'))
            ->groupBy('branch_id')
            ->orderByDesc('count')
            ->value('branch_id');

        $mostCustomerBranchCustomerCount = (int)$mostCustomerBranch > 0 ? Customer::where('branch_id',(int)$mostCustomerBranch)->count() : 0;
        $mostCustomerBranchCustomerCountSelectedDate  = (int)$mostCustomerBranch > 0 ? Customer::where('branch_id',(int)$mostCustomerBranch)->whereBetween('created_at',[$startDate,$endDate])->count() : 0;

        $lessExpertiseBranch = Expertise::select('branch_id', DB::raw('COUNT(*) as count'))
            ->groupBy('branch_id')
            ->orderBy('count')
            ->value('branch_id');

        $lessExpertiseBranchExpertiseCount = (int)$lessExpertiseBranch > 0 ? Expertise::where('branch_id',(int)$lessExpertiseBranch)->count() : 0;
        $lessExpertiseBranchExpertiseCountSelectedDate  = (int)$lessExpertiseBranch > 0 ? Expertise::where('branch_id',(int)$lessExpertiseBranch)->whereBetween('created_at',[$startDate,$endDate])->count() : 0;

        $lessCustomerBranch = Customer::select('branch_id', DB::raw('COUNT(*) as count'))
            ->groupBy('branch_id')
            ->orderByDesc('count')
            ->value('branch_id');

        $lessCustomerBranchCustomerCount = (int)$lessCustomerBranch > 0 ? Customer::where('branch_id',(int)$lessCustomerBranch)->count() : 0;
        $lessCustomerBranchCustomerCountSelectedDate = (int)$lessCustomerBranch > 0 ? Customer::where('branch_id',(int)$lessCustomerBranch)->whereBetween('created_at',[$startDate,$endDate])->count() : 0;

        $branchCountsByCity = Branch::select('il_kodu', DB::raw('COUNT(*) as branch_count'))
            ->groupBy('il_kodu')
            ->where('il_kodu','!=',0)
            ->get();

        $branchCountsByCity = $branchCountsByCity->map(function ($query){
            return [
                'il_kodu' => $query->il_kodu,
                'branch_count' => $query->branch_count,
                'il' => City::where('id',$query->il_kodu)->first()->title,
            ];
        });

        if ($diffInDays >= 7)
            $interval = ceil($diffInDays / 7);
        elseif ($diffInDays >= 5)
            $interval = ceil($diffInDays / 5);
        elseif ($diffInDays >= 3)
            $interval = ceil($diffInDays / 5);
        elseif ($diffInDays >= 0)
            $interval = ceil($diffInDays / 1);

        $expertises = [];
        $currentStartDate = $startDate;
        for ($i = 0; $i < 7; $i++) {
            // Parçanın bitiş tarihini hesaplayın
            $currentEndDate = $currentStartDate->copy()->addDays($interval - 1);

            // Eğer son parçaysa ve bitiş tarihi bitiş tarihini aşıyorsa, son bitiş tarihini ayarlayın
            if ($i == 6 && $currentEndDate->gt($endDate)) {
                $currentEndDate = $endDate;
            }

            // Parça aralığında satılan miktarı hesaplayın
            if (is_null($branchID))
                $expertiseCount = Expertise::whereBetween('created_at', [$currentStartDate, $currentEndDate])->count();
            else
                $expertiseCount = Expertise::whereIn('branch_id',$branchID)->whereBetween('created_at', [$currentStartDate, $currentEndDate])->count();
            // Parçayı saklayın
            $expertises[] = [
                'start_date' => $currentStartDate->format('d.m.Y'),
                'end_date' => $currentEndDate->format('d.m.Y'),
                'count' => $expertiseCount
            ];

            // Bir sonraki parçanın başlangıç tarihini hesaplayın
            $currentStartDate = $currentEndDate->copy()->addDay();
        }

        $filters['startDate'] = $startDate->format('Y-m-d');
        $filters['endDate'] = $endDate->format('Y-m-d');

        return compact([
            'totalBranchCount',
            'newAddedBranchCount',
            'activeBranchCount',
            'mostExpertiseBranch',
            'mostExpertiseBranchExpertiseCount',
            'mostExpertiseBranchExpertiseCountSelectedDate',
            'lessExpertiseBranch',
            'lessExpertiseBranchExpertiseCount',
            'lessExpertiseBranchExpertiseCountSelectedDate',
            'mostCustomerBranch',
            'mostCustomerBranchCustomerCount',
            'mostCustomerBranchCustomerCountSelectedDate',
            'lessCustomerBranch',
            'lessCustomerBranchCustomerCount',
            'lessCustomerBranchCustomerCountSelectedDate',
            'branchCountsByCity',
            'expertises',
            'filters'
        ]);
    }

    private function carReport()
    {
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        $startDate = isset($_GET['start_date']) && $_GET['start_date'] != '' ? Carbon::make($_GET['start_date']) : Carbon::now()->subDays(7);
        $endDate = isset($_GET['end_date']) && $_GET['end_date'] != '' ? Carbon::make($_GET['end_date']) : Carbon::now();
        $branchID = isset($_GET['branch_id']) ? [$_GET['branch_id']] : $authUserBranchIds;
        $diffInDays = $endDate->diffInDays($startDate);

        $totalCarCount = Car::when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
        ->count();
        $newAddedCarCount = Car::whereBetween('created_at',[$startDate,$endDate])
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->count();
        $activeCarCount = Car::where('status',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->count();
        $mostExpertiseCar = Expertise::select('car_id', DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->groupBy('car_id')
            ->orderByDesc('count')
            ->value('car_id');

        $mostExpertiseCarExpertiseCount = (int)$mostExpertiseCar > 0 ? Expertise::where('car_id',(int)$mostExpertiseCar)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->count() : 0;
        $mostExpertiseCarExpertiseCountSelectedDate = (int)$mostExpertiseCar > 0 ? Expertise::where('car_id',(int)$mostExpertiseCar)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count() : 0;

        $mostCustomerCar = CustomerCar::select('customer_id')
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getCar',function ($query2)use ($branchID){$query2->whereIn('branch_id',$branchID);});})
            ->selectRaw('COUNT(*) as car_count')
            ->groupBy('customer_id')
            ->orderByDesc('car_count')
            ->first();

        $carCaseTypes = CarCaseType::where('status',1)->select('id','name')->get();
        foreach ($carCaseTypes as $carCaseType){
            $carCaseType['car_count'] = Car::where('car_case_type_id',$carCaseType['id'])
                ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
                ->count();
        }

        $carFuels = CarFuel::where('status',1)->select('id','name')->get();
        foreach ($carFuels as $carFuel){
            $carFuel['car_count'] = Car::where('car_fuels_id',$carFuel['id'])
                ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
                ->count();
        }

        $carGears = CarGear::where('status',1)->select('id','name')->get();
        foreach ($carGears as $carGear){
            $carGear['car_count'] = Car::where('car_fuels_id',$carGear['id'])
                ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
                ->count();
        }

        $carGears = CarGear::where('status',1)->select('id','name')->get();
        foreach ($carGears as $carGear){
            $carGear['car_count'] = Car::where('car_fuels_id',$carGear['id'])
                ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
                ->count();
        }

        $carGroups = Car::where('car_group_model_id','!=',null)->select('car_group_model_id', DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->groupBy('car_group_model_id')
            ->orderByDesc('count')
            ->take(5)
            ->get();

        foreach ($carGroups as $mostUsedCarGroupModelId){
            $mostUsedCarGroupModelId['group'] = CarGroup::where('id',$mostUsedCarGroupModelId['car_group_model_id'])->first()->name;
        }

        $filters['startDate'] = $startDate->format('Y-m-d');
        $filters['endDate'] = $endDate->format('Y-m-d');

        return compact([
            'totalCarCount',
            'newAddedCarCount',
            'activeCarCount',
            'mostExpertiseCar',
            'mostExpertiseCarExpertiseCount',
            'mostExpertiseCarExpertiseCountSelectedDate',
            'mostCustomerCar',
            'carCaseTypes',
            'carFuels',
            'carGears',
            'carGroups',
            'filters'
        ]);
    }

    private function userReport()
    {
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        $startDate = isset($_GET['start_date']) && $_GET['start_date'] != '' ? Carbon::make($_GET['start_date']) : Carbon::now()->subDays(7);
        $endDate = isset($_GET['end_date']) && $_GET['end_date'] != '' ? Carbon::make($_GET['end_date']) : Carbon::now();
        $branchID = isset($_GET['branch_id']) ? [$_GET['branch_id']] : $authUserBranchIds;
        $diffInDays = $endDate->diffInDays($startDate);


        $totalPersonelCount = User::when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->count();
        $newAddedPersonelCount = User::whereBetween('created_at',[$startDate,$endDate])
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->count();
        $activePersonelCount = User::where('status',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->count();

        $mostLogPersonel = Log::select('user_id', DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->groupBy('user_id')
            ->orderByDesc('count')
            ->value('user_id');

        $mostLogPersonelLogCount = Log::where('user_id',$mostLogPersonel)->count();
        $mostLogPersonelLogCountSelectedDate = Log::where('user_id',$mostLogPersonel)->whereBetween('created_at',[$startDate,$endDate])->count();

        $mostOpenExpertisePersonel = Expertise::where('user_id','!=',0)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->select('user_id', DB::raw('COUNT(*) as count'))
            ->groupBy('user_id')
            ->orderByDesc('count')->first();
        $personel = User::where('id',$mostOpenExpertisePersonel->user_id ?? 0)->first();
        $mostOpenExpertisePersonel['isim'] = $personel ? $personel->name . ' ' .$personel->second_name . ' ' . $personel->surname : '';
        $mostExpertisePersonelExpertiseCountSelectedDate = isset($mostOpenExpertisePersonel->user_id) ? Expertise::where('user_id',$mostOpenExpertisePersonel->user_id)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count() : 0;


        $mostExpertiseCarUser = Expertise::where('arac_kontrol_user','!=',0)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->select('arac_kontrol_user', DB::raw('COUNT(*) as car_count, arac_kontrol_user as user_id'))
            ->groupBy('arac_kontrol_user')
            ->orderByDesc('car_count')->first();
        $personel = User::where('id',$mostExpertiseCarUser->user_id ?? 0)->first();
        $mostExpertiseCarUser['isim'] = $personel ? $personel->name . ' ' .$personel->second_name . ' ' . $personel->surname : '';
        $mostExpertiseCarUserCarCountSelectedDate = isset($mostExpertiseCarUser->user_id) ? Expertise::where('arac_kontrol_user',$mostExpertiseCarUser->user_id)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count() : 0;

        $mostExpertiseBrakeUser = Expertise::where('fren_kontrol_user','!=',0)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->select('fren_kontrol_user', DB::raw('COUNT(*) as car_count, fren_kontrol_user as user_id'))
            ->groupBy('fren_kontrol_user')
            ->orderByDesc('car_count')->first();
        $personel = User::where('id',$mostExpertiseBrakeUser->user_id ?? 0)->first();
        $mostExpertiseBrakeUser['isim'] = $personel ? $personel->name . ' ' .$personel->second_name . ' ' . $personel->surname : '';
        $mostExpertiseBrakeUserBrakeCountSelectedDate = isset($mostExpertiseBrakeUser->user_id) ? Expertise::where('fren_kontrol_user',$mostExpertiseBrakeUser->user_id)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count() : 0;

        $mostExpertiseBodyworkUser = Expertise::where('kaporta_kontrol_user','!=',0)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->select('kaporta_kontrol_user', DB::raw('COUNT(*) as car_count, kaporta_kontrol_user as user_id'))
            ->groupBy('kaporta_kontrol_user')
            ->orderByDesc('car_count')->first();
        $personel = User::where('id',$mostExpertiseBodyworkUser->user_id ?? 0)->first();
        $mostExpertiseBodyworkUser['isim'] = $personel ? $personel->name . ' ' .$personel->second_name . ' ' . $personel->surname : '';
        $mostExpertiseBodyworkUserBodyworkCountSelectedDate = isset($mostExpertiseBodyworkUser->user_id) ? Expertise::where('kaporta_kontrol_user',$mostExpertiseBodyworkUser->user_id)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count() : 0;

        $mostExpertiseDiagnosticUser = Expertise::where('diagnostic_kontrol_user','!=',0)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->select('diagnostic_kontrol_user', DB::raw('COUNT(*) as car_count, diagnostic_kontrol_user as user_id'))
            ->groupBy('diagnostic_kontrol_user')
            ->orderByDesc('car_count')->first();
        $personel = User::where('id',$mostExpertiseDiagnosticUser->user_id ?? 0)->first();
        $mostExpertiseDiagnosticUser['isim'] = $personel ? $personel->name . ' ' .$personel->second_name . ' ' . $personel->surname : '';
        $mostExpertiseDiagnosticUserDiagnosticCountSelectedDate = isset($mostExpertiseDiagnosticUser->user_id) ? Expertise::where('diagnostic_kontrol_user',$mostExpertiseDiagnosticUser->user_id)
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count() : 0;

        $mostExpertiseInternalUser = Expertise::where('ic_kontrol_user','!=',0)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->select('ic_kontrol_user', DB::raw('COUNT(*) as car_count, ic_kontrol_user as user_id'))
            ->groupBy('ic_kontrol_user')
            ->orderByDesc('car_count')
            ->first();
        $personel = User::where('id',$mostExpertiseInternalUser->user_id ?? 0)->first();
        $mostExpertiseInternalUser['isim'] = $personel ? $personel->name . ' ' .$personel->second_name . ' ' . $personel->surname : '';
        $mostExpertiseInternalUserInternalCountSelectedDate = isset($mostExpertiseInternalUser->user_id) ? Expertise::where('ic_kontrol_user',$mostExpertiseInternalUser->user_id)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count() : 0;

        $mostExpertiseTireAndRimUser = Expertise::where('lastik_jant_kontrol_user','!=',0)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->select('lastik_jant_kontrol_user', DB::raw('COUNT(*) as car_count, lastik_jant_kontrol_user as user_id'))
            ->groupBy('lastik_jant_kontrol_user')
            ->orderByDesc('car_count')
            ->first();
        $personel = User::where('id',$mostExpertiseTireAndRimUser->user_id ?? 0)->first();
        $mostExpertiseTireAndRimUser['isim'] = $personel ? $personel->name . ' ' .$personel->second_name . ' ' . $personel->surname : '';
        $mostExpertiseTireAndRimUserTireAndRimCountSelectedDate = isset($mostExpertiseTireAndRimUser->user_id) ? Expertise::where('lastik_jant_kontrol_user',$mostExpertiseTireAndRimUser->user_id)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count() : 0;

        $mostExpertiseSubControlUser = Expertise::where('alt_motor_kontrol_user','!=',0)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->select('alt_motor_kontrol_user', DB::raw('COUNT(*) as car_count, alt_motor_kontrol_user as user_id'))
            ->groupBy('alt_motor_kontrol_user')
            ->orderByDesc('car_count')
            ->first();
        $personel = User::where('id',$mostExpertiseSubControlUser->user_id ?? 0)->first();
        $mostExpertiseSubControlUser['isim'] = $personel ? $personel->name . ' ' .$personel->second_name . ' ' . $personel->surname : '';
        $mostExpertiseSubControlUserSubControlCountSelectedDate = isset($mostExpertiseSubControlUser->user_id) ? Expertise::where('alt_motor_kontrol_user',$mostExpertiseSubControlUser->user_id)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count() : 0;

        $mostExpertiseComponentUser = Expertise::where('komponent_kontrol_user','!=',0)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->select('komponent_kontrol_user', DB::raw('COUNT(*) as car_count, komponent_kontrol_user as user_id'))
            ->groupBy('komponent_kontrol_user')
            ->orderByDesc('car_count')->first();
        $personel = User::where('id',$mostExpertiseComponentUser->user_id ?? 0)->first();
        $mostExpertiseComponentUser['isim'] = $personel ? $personel->name . ' ' .$personel->second_name . ' ' . $personel->surname : '';
        $mostExpertiseComponentUserComponentCountSelectedDate = isset($mostExpertiseComponentUser->user_id) ? Expertise::where('komponent_kontrol_user',$mostExpertiseComponentUser->user_id)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count() : 0;


        $usersByBranches = User::select('branch_id', DB::raw('COUNT(*) as count'))
            ->whereIn('branch_id',$authUserBranchIds)
            ->groupBy('branch_id')
            ->get();

        foreach ($usersByBranches as $usersByBranch){
            $usersByBranch['branch'] = Branch::where('id',$usersByBranch->branch_id)->first()->kisa_ad;
        }

        $userRoleGroups = UserRoleGroup::where('status',1)->get();
        foreach ($userRoleGroups as $userRoleGroup){
            $userRoleGroup['count'] = User::where('user_role_group_id',$userRoleGroup->id)
                ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
                ->count();
        }

        $usersByYears = User::where('work_start_date','!=',null)->select(DB::raw('YEAR(work_start_date) as year'), DB::raw('COUNT(*) as count'))
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->groupBy(DB::raw('YEAR(work_start_date)'))
            ->orderBy('year', 'asc')
            ->get();

        $filters['startDate'] = $startDate->format('Y-m-d');
        $filters['endDate'] = $endDate->format('Y-m-d');

        return compact([
            'totalPersonelCount',
            'newAddedPersonelCount',
            'activePersonelCount',
            'mostLogPersonel',
            'mostLogPersonelLogCount',
            'mostLogPersonelLogCountSelectedDate',
            'mostOpenExpertisePersonel',
            'mostExpertiseCarUser',
            'mostExpertisePersonelExpertiseCountSelectedDate',
            'mostExpertiseCarUserCarCountSelectedDate',
            'mostExpertiseBrakeUserBrakeCountSelectedDate',
            'mostExpertiseBrakeUser',
            'mostExpertiseBodyworkUser',
            'mostExpertiseBodyworkUserBodyworkCountSelectedDate',
            'mostExpertiseDiagnosticUser',
            'mostExpertiseDiagnosticUserDiagnosticCountSelectedDate',
            'mostExpertiseInternalUser',
            'mostExpertiseInternalUserInternalCountSelectedDate',
            'mostExpertiseTireAndRimUser',
            'mostExpertiseTireAndRimUserTireAndRimCountSelectedDate',
            'mostExpertiseSubControlUser',
            'mostExpertiseSubControlUserSubControlCountSelectedDate',
            'mostExpertiseComponentUser',
            'mostExpertiseComponentUserComponentCountSelectedDate',
            'usersByBranches',
            'userRoleGroups',
            'usersByYears',
            'filters'
        ]);
    }

    private function expertiseReport()
    {
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        $startDate = isset($_GET['start_date']) && $_GET['start_date'] != '' ? Carbon::make($_GET['start_date']) : Carbon::now()->subDays(7);
        $endDate = isset($_GET['end_date']) && $_GET['end_date'] != '' ? Carbon::make($_GET['end_date']) : Carbon::now();
        $branchID = isset($_GET['branch_id']) ? [(int)$_GET['branch_id']] : $authUserBranchIds;
        $belgeKod = Branch::where('id',$branchID)->first()?->belge_kod;
        $diffInDays = $endDate->diffInDays($startDate);

        $totalExpertiseCount = Expertise::where('manuel_save',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])->where('status',1)
            ->count();

        if (!env('APP_LOCAL')){
            $c = \DB::connection('mysql2')->select("SELECT COUNT(*) as totalExpertisecount FROM T400_SRVBASLIK WHERE T400_subekodu = '$belgeKod' AND T400_belgetarihi BETWEEN '$startDate' AND '$endDate' ;");
            $totalExpertiseCount += $c[0]->totalExpertisecount;
        }
        $newAddedExpertiseCount = Expertise::where('manuel_save',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->count();
        if (!env('APP_LOCAL')){
            $c = \DB::connection('mysql2')->select("SELECT COUNT(*) as totalExpertisecount FROM T400_SRVBASLIK WHERE T400_subekodu = '$belgeKod' AND T400_belgetarihi BETWEEN '$startDate' AND '$endDate';");
            $newAddedExpertiseCount += $c[0]->totalExpertisecount;
        }

        $activeExpertiseCount = Expertise::where('manuel_save',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])->where('status',1)
            ->count();
        if (!env('APP_LOCAL')){
            $c = \DB::connection('mysql2')->select("SELECT COUNT(*) as totalExpertisecount FROM T400_SRVBASLIK WHERE T400_subekodu = '$belgeKod' AND T400_belgetarihi BETWEEN '$startDate' AND '$endDate';");
            $activeExpertiseCount += $c[0]->totalExpertisecount;
        }

        $mostExpertiseBranchQuery = Expertise::where('branch_id','!=',0)
            ->where('manuel_save',1)
            ->whereBetween('created_at',[$startDate,$endDate])
            ->select('branch_id', DB::raw('COUNT(*) as count'))
            ->groupBy('branch_id')
            ->orderByDesc('count')
            ->first();

        $mostExpertiseBranchCount = $mostExpertiseBranchQuery ? $mostExpertiseBranchQuery->count : 0;

        $mostExpertiseBranchQuery2 = null;
        if (!env('APP_LOCAL')){
            $mostExpertiseBranchQuery2 = DB::connection('mysql2')->select("SELECT *, (SELECT COUNT(*) FROM T400_SRVBASLIK WHERE T400_subekodu = T103_kod AND T400_belgetarihi BETWEEN '$startDate' AND '$endDate') AS KayitSayisi FROM T103_SUBELER WHERE T103_kod IN (SELECT T400_subekodu FROM (SELECT T400_subekodu, COUNT(*) AS count FROM T400_SRVBASLIK WHERE T400_belgetarihi BETWEEN '$startDate' AND '$endDate' GROUP BY T400_subekodu ORDER BY count DESC LIMIT 1) AS t);");
            if (isset($mostExpertiseBranchQuery2[0]) && $mostExpertiseBranchQuery?->count < $mostExpertiseBranchQuery2[0]->KayitSayisi){
                $mostExpertiseBranch = Branch::where('belge_kod',$mostExpertiseBranchQuery2[0]?->T103_kod)->first();
                $mostExpertiseBranchCount = $mostExpertiseBranchQuery2[0]->KayitSayisi;
            }else{
                $mostExpertiseBranch = Branch::where('id',$mostExpertiseBranchQuery?->branch_id)->first();
            }
        }else{
            $mostExpertiseBranch = Branch::where('id',$mostExpertiseBranchQuery?->branch_id)->first();
        }

        $lessExpertiseBranchQuery = Expertise::select('branch_id', DB::raw('COUNT(*) as count'))
            ->where('manuel_save',1)
            ->whereBetween('created_at',[$startDate,$endDate])
            ->groupBy('branch_id')
            ->orderBy('count')
            ->first();
        $lessExpertiseBranchCount = $lessExpertiseBranchQuery ? $lessExpertiseBranchQuery->count : 0;
        $lessExpertiseBranchQuery2 = null;
        if (!env('APP_LOCAL')){
            $lessExpertiseBranchQuery2 = DB::connection('mysql2')->select("SELECT *, (SELECT COUNT(*) FROM T400_SRVBASLIK WHERE T400_subekodu = T103_kod AND T400_belgetarihi BETWEEN '$startDate' AND '$endDate') AS KayitSayisi FROM T103_SUBELER WHERE T103_kod IN (SELECT T400_subekodu FROM (SELECT T400_subekodu, COUNT(*) AS count FROM T400_SRVBASLIK WHERE T400_belgetarihi BETWEEN '$startDate' AND '$endDate' GROUP BY T400_subekodu ORDER BY count LIMIT 1) AS t);");
            if (isset($lessExpertiseBranchQuery2[0]) && $lessExpertiseBranchQuery?->count < $lessExpertiseBranchQuery2[0]->KayitSayisi ){
                $lessExpertiseBranch = Branch::where('belge_kod',$lessExpertiseBranchQuery2[0]?->T103_kod)->first();
                $lessExpertiseBranchCount = $lessExpertiseBranchQuery2[0]->KayitSayisi;
            }else{
                $lessExpertiseBranch = Branch::where('id',$lessExpertiseBranchQuery?->branch_id)->first();
            }
        }else{
            $lessExpertiseBranch = Branch::where('id',$lessExpertiseBranchQuery?->branch_id)->first();
        }
//        $lessExpertiseBranch = isset($lessExpertiseBranchQuery->branch_id) ? Branch::where('id',$lessExpertiseBranchQuery->branch_id)->first() : null;

        $mostExpertiseCustomerQuery = Expertise::where('cari_id','!=',0)
            ->where('manuel_save',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->select('cari_id', DB::raw('COUNT(*) as count'))
            ->groupBy('cari_id')
            ->orderByDesc('count')
            ->first();
        $mostExpertiseCustomerCount = $mostExpertiseCustomerQuery ? $mostExpertiseCustomerQuery->count : 0;
        if (!env('APP_LOCAL')){
            $mostExpertiseCustomerQuery2 = DB::connection('mysql2')->select("SELECT T400_satici_UQ, COUNT(*) AS count FROM T400_SRVBASLIK WHERE T400_subekodu = $belgeKod AND T400_belgetarihi BETWEEN '$startDate' AND '$endDate' GROUP BY T400_satici_UQ ORDER BY count DESC LIMIT 2;");
            if (isset($mostExpertiseCustomerQuery2[1]) && ($mostExpertiseCustomerQuery2[1]->count > $mostExpertiseCustomerQuery?->count)){
                $mostExpertiseCustomer = Customer::where('kod',$mostExpertiseCustomerQuery2[1]->T400_satici_UQ)->first();
                $mostExpertiseCustomerCount = $mostExpertiseCustomerQuery2[1]->count;
            }else{
                $mostExpertiseCustomer = Customer::where('id',$mostExpertiseCustomerQuery?->cari_id)->first();
            }
        }else{
            $mostExpertiseCustomer = Customer::where('id',$mostExpertiseCustomerQuery?->cari_id)->first();
        }



        $mostExpertiseSaticiQuery = Expertise::where('satici_id','!=',0)
            ->where('manuel_save',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->select('satici_id', DB::raw('COUNT(*) as count'))
            ->groupBy('satici_id')
            ->orderByDesc('count')
            ->first();
        $mostExpertiseSaticiCount = $mostExpertiseSaticiQuery ? $mostExpertiseSaticiQuery->count : 0;
        if (!env('APP_LOCAL')){
            if (isset($mostExpertiseCustomerQuery2[1]) && ($mostExpertiseCustomerQuery2[1]->count > $mostExpertiseCustomerQuery?->count)){
                $mostExpertiseSatici = Customer::where('kod',$mostExpertiseCustomerQuery2[1]->T400_satici_UQ)->first();
                $mostExpertiseSaticiCount = $mostExpertiseCustomerQuery2[1]->count;
            }else{
                $mostExpertiseSatici = Customer::where('id',$mostExpertiseSaticiQuery?->cari_id)->first();
            }
        }else{
            $mostExpertiseSatici = Customer::where('id',$mostExpertiseSaticiQuery?->cari_id)->first();
        }

        $mostExpertiseAliciQuery = Expertise::where('alici_id','!=',0)
            ->where('manuel_save',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->select('alici_id', DB::raw('COUNT(*) as count'))
            ->groupBy('alici_id')
            ->orderByDesc('count')
            ->first();
        $mostExpertiseAliciCount = $mostExpertiseAliciQuery ? $mostExpertiseAliciQuery->count : 0;
        if (!env('APP_LOCAL')){
            $mostExpertiseAliciQuery2 = DB::connection('mysql2')->select("SELECT T400_musteri_UQ, COUNT(*) AS count FROM T400_SRVBASLIK WHERE T400_subekodu = $belgeKod AND T400_belgetarihi BETWEEN '$startDate' AND '$endDate' GROUP BY T400_musteri_UQ ORDER BY count DESC LIMIT 2;");
            if (isset($mostExpertiseAliciQuery2[1]) && ($mostExpertiseAliciQuery2[1]->count > $mostExpertiseAliciQuery?->count)){
                $mostExpertiseAlici = Customer::where('kod',$mostExpertiseAliciQuery2[1]->T400_musteri_UQ)->first();
                $mostExpertiseAliciCount = $mostExpertiseAliciQuery2[1]->count;
            }else{
                $mostExpertiseAlici = isset($mostExpertiseAliciQuery->alici_id) ? Customer::where('id',$mostExpertiseAliciQuery->alici_id)->first() : null;
            }
        }else{
            $mostExpertiseAlici = isset($mostExpertiseAliciQuery->alici_id) ? Customer::where('id',$mostExpertiseAliciQuery->alici_id)->first() : null;
        }

        $mostExpertiseCarQuery = Expertise::where('car_id','!=',0)
            ->where('manuel_save',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->select('car_id', DB::raw('COUNT(*) as count'))
            ->groupBy('car_id')
            ->orderByDesc('count')
            ->first();
        $mostExpertiseCarCount = $mostExpertiseCarQuery ? $mostExpertiseCarQuery->count : 0;
        if (!env('APP_LOCAL')){
            $mostExpertiseCarQuery2 = DB::connection('mysql2')->select("SELECT T400_arac_UQ, COUNT(*) AS count FROM T400_SRVBASLIK WHERE (T400_subekodu = $belgeKod) AND T400_belgetarihi BETWEEN '$startDate' AND '$endDate' GROUP BY T400_arac_UQ ORDER BY count DESC LIMIT 1;");
            if (isset($mostExpertiseCarQuery2[0]) && ($mostExpertiseCarQuery2[0]->count > $mostExpertiseCarQuery?->count)){
                $mostExpertiseCar = Car::where('UQ',$mostExpertiseCarQuery2[0]->T400_arac_UQ)->first();
                $mostExpertiseCarCount = $mostExpertiseCarQuery2[0]->count;
            }else{
                $mostExpertiseCar = isset($mostExpertiseCarQuery->car_id) ? Car::where('id',$mostExpertiseCarQuery->car_id)->first() : null;
            }
        }else{
            $mostExpertiseCar = isset($mostExpertiseCarQuery->car_id) ? Car::where('id',$mostExpertiseCarQuery->car_id)->first() : null;
        }



        $mostExpertiseComeFrom = Expertise::where('nereden_ulastiniz','!=','')
            ->where('manuel_save',1)
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
            ->whereBetween('created_at',[$startDate,$endDate])
            ->select('nereden_ulastiniz', DB::raw('COUNT(*) as count'))
            ->groupBy('nereden_ulastiniz')
            ->orderByDesc('count')
            ->value('nereden_ulastiniz');

        $mostExpertisePaymentType = ExpertisePayment::where('type','!=','')
            ->whereBetween('created_at',[$startDate,$endDate])
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2) use ($branchID){return $query2->whereIn('branch_id',$branchID);});})
            ->select('type', DB::raw('COUNT(*) as count'))
            ->groupBy('type')
            ->orderByDesc('count')
            ->value('type');

        $mostExpertiseStock = ExpertiseStock::where('stock_id','!=',0)
            ->whereBetween('created_at',[$startDate,$endDate])
            ->when(count($branchID) > 0,function ($query) use ($branchID){return $query->whereHas('getExpertise',function ($query2) use ($branchID){return $query2->whereIn('branch_id',$branchID);});})
            ->select('stock_id', DB::raw('COUNT(*) as count'))
            ->groupBy('stock_id')
            ->orderByDesc('count')
            ->first();
        $mostExpertiseStockCount = $mostExpertiseStock ? $mostExpertiseStock->count : 0;

        if (!env('APP_LOCAL')){
            $mostExpertiseStock2 = DB::connection('mysql2')->select("SELECT T400_hizmet_UQ, COUNT(*) AS count FROM T400_SRVBASLIK WHERE (T400_subekodu = $belgeKod) AND T400_belgetarihi BETWEEN '$startDate' AND '$endDate' GROUP BY T400_hizmet_UQ ORDER BY count DESC LIMIT 1;");
            if (isset($mostExpertiseStock2[0]) && ($mostExpertiseStock2[0]->count > $mostExpertiseStock?->count)){
                $mostExpertiseStock['stock'] = DB::connection('mysql2')->select("SELECT T203_stokadi FROM T203_STOKLAR WHERE T203_UQ = '".$mostExpertiseStock2[0]->T400_hizmet_UQ."' LIMIT 1")[0]?->T203_stokadi;
                $mostExpertiseStockCount = $mostExpertiseStock2[0]->count;
            }else{
                if ($mostExpertiseStock)
                    $mostExpertiseStock['stock'] = Stock::where('id',$mostExpertiseStock->stock_id)->first()->ad;
                else
                    $mostExpertiseStock['stock'] = '';
            }
        }else{
            if ($mostExpertiseStock)
                $mostExpertiseStock['stock'] = Stock::where('id',$mostExpertiseStock->stock_id)->first()->ad;
            else
                $mostExpertiseStock['stock'] = '';
        }


        if ($diffInDays >= 7){
            $divide = 7;
            $interval = ceil($diffInDays / 7);
        }elseif ($diffInDays >= 5){
            $divide = 5;
            $interval = ceil($diffInDays / 5);
        }elseif ($diffInDays >= 3){
            $divide = 3;
            $interval = ceil($diffInDays / 5);
        }elseif ($diffInDays >= 0){
            $divide = 1;
            $interval = ceil($diffInDays / 1);
        }

        $expertises = [];
        $expertisePayments = [];
        for ($i = 0; $i < $divide; $i++) {
            $currentStartDate = $startDate->copy()->addDays($i * $interval);
            $currentEndDate = $currentStartDate->copy()->addDays($interval);
            // Parça aralığında satılan miktarı hesaplayın
            if (is_null($branchID))
                $expertiseCount = Expertise::where('manuel_save',1)
                    ->whereBetween('created_at', [$currentStartDate, $currentEndDate])
                    ->count();
            else
                $expertiseCount = Expertise::where('manuel_save',1)
                    ->whereIn('branch_id',$branchID)
                    ->whereBetween('created_at', [$currentStartDate, $currentEndDate])
                    ->count();
            // Parçayı saklayın

            $expertises[] = [
                'start_date' => $currentStartDate->format('d.m.Y'),
                'end_date' => $currentEndDate->format('d.m.Y'),
                'count' => $expertiseCount
            ];

            $paymentsCount = [];
            foreach (__('arrays.payment_types') as $paymentKey => $paymentType){
                if (is_null($branchID)){
                    $paymentsCount[$paymentKey]['count'] = ExpertisePayment::where('type',$paymentKey)
                        ->whereBetween('created_at', [$currentStartDate, $currentEndDate])
                        ->count();
                    $paymentsCount[$paymentKey]['total'] = round(ExpertisePayment::where('type',$paymentKey)
                        ->whereBetween('created_at', [$currentStartDate, $currentEndDate])
                        ->sum('amount'),2);
                }else{
                    $paymentsCount[$paymentKey]['count'] = ExpertisePayment::whereHas('getExpertise',function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
                        ->where('type',$paymentKey)
                        ->whereBetween('created_at', [$currentStartDate, $currentEndDate])
                        ->count();
                    $paymentsCount[$paymentKey]['total'] = round(ExpertisePayment::whereHas('getExpertise',function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
                        ->where('type',$paymentKey)
                        ->whereBetween('created_at', [$currentStartDate, $currentEndDate])
                        ->sum('amount'),2);
                }
            }


            $expertisePayments[] = [
                'start_date' => $currentStartDate->format('d.m.Y'),
                'end_date' => $currentEndDate->format('d.m.Y'),
                'counts' => $paymentsCount
            ];

        }

        $filters['startDate'] = $startDate->format('Y-m-d');
        $filters['endDate'] = $endDate->format('Y-m-d');

        return compact([
            'totalExpertiseCount',
            'newAddedExpertiseCount',
            'activeExpertiseCount',
            'mostExpertiseBranchQuery',
            'mostExpertiseBranch',
            'lessExpertiseBranchQuery',
            'lessExpertiseBranch',
            'mostExpertiseCustomerQuery',
            'mostExpertiseCustomer',
            'mostExpertiseSaticiQuery',
            'mostExpertiseSatici',
            'mostExpertiseAliciQuery',
            'mostExpertiseAlici',
            'mostExpertiseCarQuery',
            'mostExpertiseCar',
            'mostExpertiseComeFrom',
            'mostExpertisePaymentType',
            'mostExpertiseStock',
            'expertises',
            'expertisePayments',
            'mostExpertiseBranchCount',
            'lessExpertiseBranchCount',
            'mostExpertiseCustomerCount',
            'mostExpertiseSaticiCount',
            'mostExpertiseAliciCount',
            'mostExpertiseCarCount',
            'mostExpertiseStockCount',
            'filters'
        ]);
    }

    public function deleteCache(Request $request)
    {
        if (Cache::has($request->type))
            Cache::forget($request->type);

        return redirect(url()->previous());
    }

    public function campaingPrices(Request $request){
        if(!empty($request->stock_id)){
            $campaign_price = StockPrice::select('campaigns.id','campaigns.name','stock_prices.kdv_dahil_fiyat','stock_prices.kdv_haric_fiyat')
                ->join('campaigns','campaigns.id','=','stock_prices.campaign_id')
                ->where('campaigns.status',1);
            if(!empty($request->campaign_id)){
                $campaign_price= $campaign_price->where('stock_prices.campaign_id',$request->campaign_id);
            }else{
                $campaign_price= $campaign_price->where('stock_prices.campaign_id','!=',0);

            }
            if(!empty($request->stock_id)){
                $campaign_price= $campaign_price->where('stock_prices.stock_id',$request->stock_id);
            }
            $campaign_price= $campaign_price->get();

            $stocks = Stock::find($request->stock_id);
            foreach($campaign_price as $cp){
                $max_iskonto_amount = 0;
               /* if (($request->stock_id == 9 || $request->stock_id == 10) && ((!empty($request->campaign_id) && $request->campaign_id == 261) || $cp->name == "CO2 Kaçak Testi"))
                    $conta = true;
                else
                    $conta = false;
                if(auth()->user()->getBranch->iskonto_tip == 1){
                    if ($conta && auth()->user()->getBranch->max_indirim_tutari > 150)
                        $max_iskonto_amount = 150;
                    else
                        $max_iskonto_amount = auth()->user()->getBranch->max_indirim_tutari;
                }else{
                    if(!empty($cp->kdv_haric_fiyat) && $cp->kdv_haric_fiyat != 0 ){
                        if ($conta && ($cp->kdv_haric_fiyat * auth()->user()->getBranch->max_indirim_orani) / 100 > 150)
                            $max_iskonto_amount = 150;
                        else
                            $max_iskonto_amount = ($cp->kdv_haric_fiyat * auth()->user()->getBranch->max_indirim_orani) / 100;
                    }else{
                        $max_iskonto_amount = 0;
                    }
                }*/
               if($cp->id == 261 && !empty($stocks)){
                   $max_iskonto_amount = $stocks->iskonto_tutari;
               }
                $cp->max_iskonto_amount = $max_iskonto_amount;
            }
            return response()->json(['success'=>'true','campaigns'=> !empty($campaign_price) ? $campaign_price:array()]);
        }else{
            return response()->json(['success'=>'false']);
        }
    }

    public function createOldExpertisDowland(Request $request){
        $dowlandUuid = $request->uuid;
        $referralUuid = $request->ref_uuid;
        $userId = auth()->user()->id;

        $existingRecord = CheckOldExpertisDowland::where('dowland_uuid', $dowlandUuid)
            ->where('referral_uuid', $referralUuid)
            ->where('user_id', $userId)
            ->where('created_at', '>', Carbon::now()->subMinutes(30))
            ->first();

        if (!$existingRecord) {
            $expertis_dowland = new CheckOldExpertisDowland();
            $expertis_dowland->dowland_uuid = $dowlandUuid;
            $expertis_dowland->referral_uuid = $referralUuid;
            $expertis_dowland->user_id = $userId;
            $expertis_dowland->save();
        }

        return response()->json(['success' => 'true']);


    }
    public function checkOldExpertisDowland(Request $request){
        $dowlandUuid = $request->uuid;
        $referralUuid = $request->ref_uuid;
        $userId = auth()->user()->id;

        $existingRecord = CheckOldExpertisDowland::
            where('dowland_uuid', $dowlandUuid)
            ->where('referral_uuid', $referralUuid)
            ->where('user_id', $userId)
            ->where('created_at', '>', Carbon::now()->subMinutes(120))
            ->first();
        if(!empty($existingRecord)){
            return response()->json(['success' => 'true']);
        }else{
            return response()->json(['success' => 'false','w'=>$existingRecord]);
        }
    }
    public function deleteStockCampaign(Request $request){
        $stock_prices = StockPrice::where('id',$request->stock_prices_id)->first();
        if(!empty($stock_prices)){
            $stock_prices->delete();
            return response()->json(['success' => true]);
        }else{
            return response()->json(['success' => false]);
        }
    }

    public function deletePlusCardBalance(Request $request){
        $balance = PlusCardCrediAndPuanAdd::find($request->plus_card_balance_id);

        if(!empty($balance)){
            // buraya yüklemeye ait harcama var mı diye kontrol eklenecek tablosu yapıldığı zaman
            $balance->delete();
            return response()->json(['success' => true]);
        }else{
            return response()->json(['success' => false,'message' => 'Yükleme Bulunamadı Sayfayı Yenileyin!']);
        }
    }

    public function deletePlusCard(Request $request){
        $plus_card = PlusCard::find($request->id);
        if(!empty($plus_card)){
            $plus_card->delete();
            return response()->json(['success' => true]);
        }else{
            return response()->json(['success' => false]);
        }
    }
    public function plusCardsDefinitionsDelete(Request $request){
        $definitions = PlusCardsDefinitions::find($request->id);
        if(!empty($definitions)){
            $definitions->delete();
            return response()->json(['success' => true]);
        }else{
            return response()->json(['success' => false]);
        }
    }

    public function plusCardBalance(Request $request){
        $plus_card = PlusCard::find($request->card_id);
        $plus_card_credi_and_puan_add = PlusCardCrediAndPuanAdd::where('card_id',$request->card_id);
        if(!empty($request->balance_id)){
            $plus_card_credi_and_puan_add = $plus_card_credi_and_puan_add->where('id',$request->balance_id);
        }
        $plus_card_credi_and_puan_add = $plus_card_credi_and_puan_add->orderBy('created_at','desc')->get();

        $array_result = array();
        foreach($plus_card_credi_and_puan_add as $pcc){
            $miktar = 0;
            $amount = 0;
            $name = date('d.m.Y',strtotime($pcc->created_at))." Tarihli ";
            $type = "";
            $price = 0;
            if($pcc->balance_type != "credits"){
                $name .= $pcc->puan." Adet Puan Yükleme";
                $miktar = $pcc->puan;
                $type = "Hediye";
            }else{
                $name .= $pcc->credi." Adet ".$pcc->getDefinitions->definition_name." Kredi Yükleme";
                $miktar = $pcc->credi;
                $amount = $pcc->credits_amount;
                $price = $pcc->unit_price;
                $type = "Kredi";
            }
            if($pcc->payment_type == "yeni_sistem_bakiye_aktirimi"){
                $name.="(ESKİ SİSTEMDEN BAKİYE AKTARIMI)";
            }
            $array_result[$pcc->id] = array(
                'id' => $pcc->id,
                'name' => $name,
                'buy_stock_service' =>$pcc->getUser->getBranch->kisa_ad,
                'belge_tarihi' => date('d.m.Y H:i:s',strtotime($pcc->created_at)),
                'belge_no' => '',
                'son_kullanma' => $pcc->valid_if == 1 ? date('d.m.Y',strtotime($pcc->valid_date)) : '',
                'kupon_kodu' => !empty($pcc->getDefinitions->stock_kodu) ? $pcc->getDefinitions->stock_kodu : '',
                'definition_name' => !empty($pcc->getDefinitions->definition_name) ? $pcc->getDefinitions->definition_name : '',
                'ozel_bolge_kodu' =>'',
                'kampanya_turu' => 'Standart',
                'islem_tipi' => 'Yükleme',
                'unit' => $miktar,
                'price' => $price,
                'amount' => $amount,
                'type' => $type,
                'yuklenen' => $pcc->credi,
                'plaka' => '',
                'marka' => '',
                'model' => '',
                'islemler' => array(),
            );

            $expertise_payment = ExpertisePayment::where('type','plus_kart')->where('plus_card_id',$request->card_id)->where('plus_card_odeme_id',$pcc->id)->orderBy('created_at','desc')->get();
            foreach($expertise_payment as $ep){
                $array_result[$pcc->id]['islemler'][] = array(
                    'id' => $pcc->id,
                    'expertise_url' => $ep->getExpertise ? route('expertises.edit',$ep->getExpertise?->uuid) : '#' ,
                    'name' => $name,
                    'buy_stock_service' => $ep->getExpertise?->getBranch->kisa_ad,
                    'belge_tarihi' => date('d.m.Y H:i:s',strtotime($ep->getExpertise?->belge_tarihi)),
                    'belge_no' => $ep->expertise->belge_no ?? '',
                    'son_kullanma' => $pcc->valid_if == 1 ? date('d.m.Y',strtotime($pcc->valid_date)) : '',
                    'definition_name' => $pcc->getDefinitions->definition_name,
                    'kupon_kodu' =>!empty($pcc->getDefinitions->stock_kodu) ? $pcc->getDefinitions->stock_kodu : '',
                    'ozel_bolge_kodu'=>'',
                    'kampanya_turu' => 'Standart',
                    'islem_tipi' => 'Harcama',
                    'unit' => '-1',
                    'price' => $ep->getExpertise?->getPayment->getPlusCardOdeme->unit_price ?? 0,
                    'amount' => $ep->getExpertise?->getPayment->getPlusCardOdeme->unit_price ?? 0,
                    'yuklenen' => '',
                    'type' => $type,
                    'plaka' => !empty($ep->getExpertise?->getCar->plaka) ? $ep->getExpertise?->getCar->plaka : '',
                    'marka' => !empty($ep->getExpertise?->getCar->getMarka->name) ? $ep->getExpertise?->getCar->getMarka->name:'',
                    'model' =>!empty($ep->getExpertise?->getCar->getModel->name) ? $ep->getExpertise?->getCar->getModel->name:'',
                );
            }
        }

        if(!env('APP_LOCAL') && !empty($plus_card->getCustomer->kod) && empty($request->balance_id)){
            $old_data = T404KUPONTAKIP::search($plus_card->getCustomer->kod)
                ->where('T404_hesap_UQ',$plus_card->getCustomer->kod)
                ->where('T404_islemtipi', '2')
                ->orderBy('T404_belgetarihi','DESC')
                ->get();
            foreach($old_data as $data){
                $name = date('d.m.Y',strtotime($data->T404_belgetarihi))." Tarihli ".(int)$data->T404_miktar." Adet";
                $type = "";
                $stok_kodu= '';
                $stok_kodu_data = DB::connection('mysql2')->table('T203_STOKLAR')
                    ->where('T203_UQ',$data->T404_stok_UQ)->first();
                if($data->T404_kayittipi == 1){
                    $name.=" Kredi Yüklemesi";
                    $type = "Kredi";
                }elseif($data->T404_kayittipi == 2){
                    $name.=" Puan Yüklemesi";
                    $type = "Puan";
                }
                if(!empty($stok_kodu_data)){
                    $stok_kodu = $stok_kodu_data->T203_stokkodu;
                }
                $array_result[$data->T404_ID] = array(
                    'id' => $data->T404_ID."-)Deha Verileri",
                    'name' => $name,
                    'buy_stock_service' =>$data->getBranches->kisa_ad,
                    'belge_tarihi' => date('d.m.Y H:i:s',strtotime($data->T404_belgetarihi)),
                    'belge_no' => $data->T404_belgeno ?? '',
                    'son_kullanma' => $data->T404_sonkullanimtarihi ?? '',
                    'definition_name' => !empty($data->getStockNew->ad) ? $data->getStockNew->ad : '',
                    'kupon_kodu' => $stok_kodu,
                    'ozel_bolge_kodu'=>$data->T404_belgeozelkodu == 1 ? 'Onaylı':'Onaysız',
                    'kampanya_turu' => 'Standart',
                    'islem_tipi' => 'Yükleme',
                    'unit' => (int)$data->T404_miktar,
                    'price' => !empty($data->T404_fiyat) ? (float)$data->T404_fiyat : '',
                    'amount' => !empty($data->T404_tutar) ? (float)$data->T404_tutar : '',
                    'yuklenen' => (int)$data->T404_miktar,
                    'type' => $type,
                    'plaka' => '',
                    'marka' => '',
                    'model' => '',
                    'islemler' => array(),
                );


                $process = T404KUPONTAKIP::search($plus_card->getCustomer->kod)
                    ->where('T404_hesap_UQ',$plus_card->getCustomer->kod)
                    ->where('T404_iliski_UQ',$data->T404_UQ)
                    ->where('T404_islemtipi','-1')
                    ->where('T404_kayittipi',$data->T404_kayittipi)
                    ->orderBy('T404_ID','DESC')
                    ->get();

                foreach($process as $p){

                    $model = '';
                    $marka = '';
                    $stok_kodu= '';
                    $stok_kodu_data = DB::connection('mysql2')->table('T203_STOKLAR')
                        ->where('T203_UQ',$data->T404_stok_UQ)->first();
                    if(!empty($stok_kodu_data)){
                        $stok_kodu = $stok_kodu_data->T203_stokkodu;
                    }
                    if(!empty($p->getSrvBaslik->t202Araclar->T202_aracgrupkodu)){
                        $model_db = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')
                            ->where('T107_kod',$p->getSrvBaslik->t202Araclar->T202_aracgrupkodu)
                            ->first();
                        if(!empty($model_db)){
                            $model = $model_db->T107_aciklama;
                        }

                        $marka_kod = substr($p->getSrvBaslik->t202Araclar->T202_aracgrupkodu, 0, -4) . "0000";
                        $marka_db = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')
                            ->where('T107_kod',$marka_kod)
                            ->first();
                        if(!empty($marka_db)){
                            $marka = $marka_db->T107_aciklama;
                        }
                    }

                    $description = date('d.m.Y',strtotime($data->T404_belgetarihi))." Tarihli Harcama";

                    $array_result[$data->T404_ID]['islemler'][] = array(
                        'id' => $data->T404_ID."-)Deha Verileri",
                        'expertise_url' => route('ekspertizRaporu',$p->getSrvBaslik->T400_UQ),
                        'name' => $description,
                        'buy_stock_service' => $p->getBranches->kisa_ad,
                        'belge_tarihi' => date('d.m.Y H:i:s',strtotime($p->T404_belgetarihi)),
                        'belge_no' => $p->T404_belgeno ?? '',
                        'son_kullanma' => $data->T404_sonkullanimtarihi ?? '',
                        'definition_name' => !empty($p->getStockNew->ad) ? $p->getStockNew->ad : '',
                        'kupon_kodu' =>$stok_kodu,
                        'ozel_bolge_kodu'=> $p->T404_belgeozelkodu == 1 ? 'Onaylı':'Onaysız',
                        'kampanya_turu' => 'Standart',
                        'islem_tipi' => 'Harcama',
                        'unit' => !empty($p->T404_miktar) ? "-".(int)$p->T404_miktar : '-1',
                        'amount' => '',
                        'price' =>!empty($p->T404_fiyat) ? (float)$p->T404_fiyat : '',
                        'yuklenen' => '',
                        'type' => $type,
                        'plaka' => $p->getSrvBaslik->t202Araclar->T202_plakano ?? '',
                        'marka' => $marka,
                        'model' => $model,
                    );
                }

            }
        }

        return response()->json(['success' => true,'balances' =>$array_result ]);
    }

    public function  smsVerification(Request $request){
        $code = 'UMR-'.rand(10,99).'-'.rand(10,99);
        $sms_verification = new SmsVerification();
        $sms_verification->phone = $request->phone;
        $sms_verification->page = $request->page;
        $sms_verification->code = $code;
        $sms_verification->save();

        $settings = Setting::first();
        if ($settings->netgsm_active == 1){
            $message = "UMRAN Plus, Kod: $code, bu kod Umran uygulaması için güvenlik kodudur.";
            sendSmsOTP($settings->netgsm_usercode,$settings->netgsm_password,$settings->netgsm_msgheader,$message,str_replace(['(',')',' '],'',$request->phone));
        }

        return response()->json(['success' => true,'code'=>$code,'sms_verification_id' =>$sms_verification->id]);


    }

    public function smsVerificationUse(Request $request){
        $check_sms = SmsVerification::where('code',$request->code)
            ->where('page',$request->page)
            ->where('id',$request->sms_verification_id)
            ->first();
        if(!empty($check_sms)){
            $check_sms->use_code = 1;
            $check_sms->save();
            return response()->json(['success' => true]);
        }else{
            return response()->json(['success' => false]);
        }
    }

    public function expertiseFtpOk(Request $request){
        $authUser = auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['ftp_expertise'])->first()){
            return response()->json(['success' => false]);
        }

        $expertise = Expertise::where('uuid',$request->uuid)->first();

        $stocks = ExpertiseStock::where('expertise_id',$expertise->id)->get();
        $car = 0;
        $brake = 0;
        $bodywork = 0;
        $diagnostic = 0;
        $internal = 0;
        $tire = 0;
        $sub = 0;
        $component = 0;
        $co2 = 0;
        foreach ($stocks as $stock){
            $stok = $stock->getStock;
            if ($stok->car == 1)
                $car = 1;
            if ($stok->brake == 1)
                $brake= 1;
            if ($stok->bodywork == 1)
                $bodywork = 1;
            if ($stok->internal_control == 1)
                $internal = 1;
            if ($stok->tire_and_rim == 1)
                $tire = 1;
            if ($stok->sub_control_and_engine == 1)
                $sub = 1;
            if ($stok->component == 1)
                $component = 1;
            if ($stok->diagnostic == 1)
                $diagnostic = 1;
            if ($stok->co2 == 1)
                $co2 = 1;
        }
        if ($car && $expertise->arac_kontrol == 0){
            return ['success' => 'false','message'=>'Araç Kontrol Tamamlanmadı!'];
        }
        if ($brake && $expertise->fren_kontrol == 0){
            return ['success' => 'false','message'=>'Fren Kontrol Tamamlanmadı!'];
        }

        if ($bodywork && $expertise->kaporta_kontrol == 0){
            return ['success' => 'false','message'=>'Kaporta Kontrol Tamamlanmadı!'];
        }

        if ($diagnostic && $expertise->diagnostic_kontrol == 0){
            return ['success' => 'false','message'=>'Diagnostic Kontrol Tamamlanmadı!'];
        }
        if ($internal && $expertise->ic_kontrol == 0){
            return ['success' => 'false','message'=>'İç Kontroller Tamamlanmadı!'];
        }
        if ($tire && $expertise->lastik_jant_kontrol == 0){
            return ['success' => 'false','message'=>'Lastik ve Jant Kontrol Tamamlanmadı!'];
        }
        if ($sub && $expertise->alt_motor_kontrol == 0){
            return ['success' => 'false','message'=>'Alt Kontroller ve Motor Tamamlanmadı!'];
        }
        if ($component && $expertise->komponent_kontrol == 0){
            return ['success' => 'false','message'=>'Component Kontrol Tamamlanmadı!'];
        }
        if ($co2 && $expertise->co2_kontrol == 0){
            return ['success' => 'false','message'=>'CO2 Kontrol Tamamlanmadı!'];
        }

        if(!empty($expertise)){

            if(!empty($request->type) && $request->type == "hasar_raporu"){
                $html_data = "<meta charset='UTF-8'><div id='divOnGosterim'>".$request->html_data."</div>";
                $masked_html_data = "<meta charset='UTF-8'><div id='divOnGosterim'>".$request->maskes_data_html."</div>";
            }else{
                $html_data = "<meta charset='UTF-8'><div id='divRaporEkrani'>".$request->html_data."</div>";
                $masked_html_data = "<meta charset='UTF-8'><div id='divRaporEkrani'>".$request->maskes_data_html."</div>";
            }

            $file = 'expertises_ftp/'.$request->uuid.'/non_masked/'.$request->uuid.'.html'; // Yazılacak dosyanın adı
            $file_masked = 'expertises_ftp/'.$request->uuid.'/masked/'.$request->uuid.'.html'; // Yazılacak dosyanın adı


            if(!Storage::exists($file)){
                Storage::put($file, $html_data);
            }

            if(!Storage::exists($file_masked)){
                Storage::put($file_masked, $masked_html_data);
            }




            /*Dosyaları Kapat*/

            if($expertise->getStockhasOne->getStock->car == 1){
                $expertise->arac_kontrol = 1;
            }
            if($expertise->getStockhasOne->getStock->brake == 1){
                $expertise->fren_kontrol = 1;
            }
            if($expertise->getStockhasOne->getStock->bodywork == 1){
                $expertise->kaporta_kontrol = 1;
            }
            if($expertise->getStockhasOne->getStock->tire_and_rim == 1){
                $expertise->lastik_jant_kontrol = 1;
            }
            if($expertise->getStockhasOne->getStock->diagnostic == 1){
                $expertise->diagnostic_kontrol = 1;
            }
            if($expertise->getStockhasOne->getStock->internal_control == 1){
                $expertise->ic_kontrol = 1;
            }
            if($expertise->getStockhasOne->getStock->sub_control_and_engine == 1){
                $expertise->alt_motor_kontrol = 1;
            }
            if($expertise->getStockhasOne->getStock->component == 1){
                $expertise->komponent_kontrol = 1;
            }


            /*Dosyaları Kapat*/


            $expertise->ftp_ok = 1;
            $expertise->ftp_date = date('Y-m-d H:i:s');
            $expertise->cikis_tarihi = date('Y-m-d H:i:s');
            $expertise->save();


            return response()->json(['success' => true]);

        }else{
            return response()->json(['success' => false]);
        }
    }

    public function userInformationUpdate(Request $request){
        $customer = Customer::find($request->customer_id);
        if(!empty($customer)){

            $customer->mahalle = $request->mahalle;
            $customer->cadde = $request->cadde;
            $customer->sokak = $request->sokak;
            $customer->semt = $request->semt;
            $customer->il_kodu = $request->city_id;
            $customer->ilce_kodu = $request->ilce_id;
            $customer->telefon = str_replace(['(',')',' '],'',$request->telefon);
            $customer->vergi_dairesi = $request->vergi_dairesi;
            $customer->tc_no = $request->tc_no;
            $customer->vergi_no = $request->tc_no;
            $customer->save();
            return response()->json(['success' => true]);
        }else{
            return response()->json(['success' => false]);
        }
    }

    public function sendAgreement(Request $request){
        $authUser = auth()->user();
        $settings = Setting::find(1);
        $customer = Customer::find($request->customer_id);

        $current_time = Carbon::now();
        $ten_minutes_ago = $current_time->subMinutes(10);
        $plus_card_agreement = PlusCardAgreement::where('created_at', '>=', $ten_minutes_ago);
        $definition = PlusCardsDefinitions::where('id',$request->definition_id)
            ->where('show_in_view',1)->first();

        // Plus card campaign
        $campaignId = $request->input('campaign_id');
        $campaign = PlusCardCampaign::find($campaignId);

        if(!empty($request->delayed_id)){
            $plus_card_agreement = $plus_card_agreement->where('delayed_id',$request->delayed_id);
            $plus_card_agreement = $plus_card_agreement->where('plus_card_id',$request->plus_card_id)
                ->where('user_id',$authUser->id)
                ->first();

            $payment_check = PaymentLog::where('file_uuid',$request->delayed_id)->where('status',1)->count();
            if(!empty($plus_card_agreement) && $payment_check == 0){
                $plus_card_agreement->delayed_id = $request->delayed_id;
                $plus_card_agreement->plus_card_id = $request->plus_card_id;
                $plus_card_agreement->customer_id = $request->customer_id;
                if(in_array($authUser->id,[224,847])){
                    $plus_card_agreement->unit_quantity = $request->unit_quantity;
                    $plus_card_agreement->payment_amount = $request->payment_amount;

                    if (!empty($campaignId) && $campaign) {
                        $plus_card_agreement->payment_amount = $campaign->applyDiscount($request->payment_amount);
                    }
                }else{
                    $plus_card_agreement->unit_quantity = $definition->unit_quantity;
                    $plus_card_agreement->payment_amount = $definition->unit_quantity * $definition->unit_price;

                    if (!empty($campaignId) && $campaign) {
                        $plus_card_agreement->payment_amount = $campaign->applyDiscount($plus_card_agreement->payment_amount);
                    }
                }

                $plus_card_agreement->valid_date = $request->valid_date;
                $plus_card_agreement->payment_type = $request->payment_type;
                $plus_card_agreement->user_id = $authUser->id;
                $plus_card_agreement->kvkk_approval = 2;
                $plus_card_agreement->agreement_approval = 2;
                $plus_card_agreement->save();

                $file = 'plus_card_agreement/'.$request->delayed_id.'/'.$request->delayed_id.'.html';
                if (Storage::exists($file)) {
                    Storage::delete($file);
                }
                if ($settings->netgsm_active == 1){
                    $telefon = str_replace(['(', ')', ' '], '', $customer->telefon);
                    if (substr($telefon, 0, 1) === '0') {
                        $telefon = substr($telefon, 1);
                    }
                    $message = "Sözleşmeyi Onaylamak için lütfen linke tıklayınız. https://umram.online/plus-card-agreement/".$plus_card_agreement->delayed_id;
                    $return = sendSmsOTP($settings->netgsm_usercode,$settings->netgsm_password,$settings->netgsm_msgheader3,$message,$telefon);
                }

                return response()->json(['success' => true,'delayed_id'=>$plus_card_agreement->delayed_id]);
            }
        }

        $delayed_id = Str::random(7);
        $plus_card_agreement = new PlusCardAgreement();
        $plus_card_agreement->delayed_id = $delayed_id;
        $plus_card_agreement->plus_card_id = $request->plus_card_id;
        $plus_card_agreement->customer_id = $request->customer_id;
        if(in_array($authUser->id,[224,847])){
            $plus_card_agreement->unit_quantity = $request->unit_quantity;
            $plus_card_agreement->payment_amount = $request->payment_amount;

            if (!empty($campaignId) && $campaign) {
                $plus_card_agreement->payment_amount = $campaign->applyDiscount($request->payment_amount);
            }
        }else{
            $plus_card_agreement->unit_quantity = $definition->unit_quantity;
            $plus_card_agreement->payment_amount = $definition->unit_quantity * $definition->unit_price;

            if (!empty($campaignId) && $campaign) {
                $plus_card_agreement->payment_amount = $campaign->applyDiscount($plus_card_agreement->payment_amount);
            }
        }
        $plus_card_agreement->valid_date = $request->valid_date;
        $plus_card_agreement->user_id = $authUser->id;
        $plus_card_agreement->save();


        if ($settings->netgsm_active == 1){
            $telefon = str_replace(['(', ')', ' '], '', $customer->telefon);
            if (substr($telefon, 0, 1) === '0') {
                $telefon = substr($telefon, 1);
            }
            $message = "Sözleşmeyi Onaylamak için lütfen linke tıklayınız. https://umram.online/plus-card-agreement/".$delayed_id;
            $return = sendSmsOTP($settings->netgsm_usercode,$settings->netgsm_password,$settings->netgsm_msgheader3,$message,$telefon);
        }

        return response()->json(['success' => true,'delayed_id'=>$delayed_id]);
    }

    public function sendAgreementCustomer(Request $request){
        $agreement = PlusCardAgreement::where('delayed_id',$request->id)->first();
        if(!empty($agreement)){
            $html_data = "<meta charset='UTF-8'>".$request->agreement_body;
            $file = 'plus_card_agreement/'.$request->id.'/'.$request->id.'.html';
            if(!Storage::exists($file)){
                Storage::put($file, $html_data);
            }
            $agreement->kvkk_approval = 1;
            $agreement->agreement_approval = 1;
            $agreement->save();
            return response()->json(['success' => true]);
        }else{
            return response()->json(['success' => false]);
        }
    }

    public function sendAgreementCheck(Request $request, PlusCardCampaignService $campaignService)
    {
        $agreement = PlusCardAgreement::where('delayed_id', $request->delayed_id)->first();

        if (!empty($agreement)) {
            if ($agreement->kvkk_approval == 1 && $agreement->agreement_approval == 1) {
                $definition = PlusCardsDefinitions::where('id', $request->plus_cards_definitions)
                    ->where('show_in_view', 1)->first();

                if (!$definition)
                    return response()->json(['success' => false]);

//                if (in_array(\auth()->user()->id, [224, 847])) {
//                    // Plus card campaign
//                    $campaignId = $request->input('campaign_id');
//                    $plusCardId = $request->input('plus_card_id');
//                    $campaign = PlusCardCampaign::find($campaignId);
//
//                    $unitPrice = $agreement->payment_amount;
//
//                    if (!empty($campaignId) && $campaign) {
//                        $result = $campaignService->applyDiscount($campaignId, $plusCardId, $agreement->payment_amount, null, true);
//
//                        if ($result['success']) {
//                            $unitPrice = $result['discounted_amount'];
////                            $unitPrice = str_replace('.', '', $result['discounted_amount']);
//                        }
//                    }
//
//                    return response()->json(['success' => true, 'pay' => $unitPrice * 100]);
//                } else {
                    // Plus card campaign
                    $campaignId = $request->input('campaign_id');
                    $plusCardId = $request->input('plus_card_id');
                    $quantity = $request->input('quantity');
                    $campaign = PlusCardCampaign::find($campaignId);

                    $amount = $definition->unit_quantity * $definition->unit_price;

                    // TODO we check qty from backend
                    if ($quantity != $definition->unit_quantity) {
                        $amount = $quantity * $definition->unit_price;
                    }

                    if (!empty($campaignId) && $campaign) {
                        $result = $campaignService->applyDiscount($campaignId, $plusCardId, $amount, null, false);

                        if ($result['success']) {
                            $amount = $result['discounted_amount'];
//                            $unitPrice = str_replace('.', '', $result['discounted_amount']);
//                            $amount = str_replace(',', '.', $amount);
//                            $cleanAmount = floatval($amount);
                        }
                    }

//                    return response()->json(['success' => true, 'pay' => $amount * 100]);
                return response()->json(['success' => true, 'pay' => $this->cleanPaynetAmount($amount)]);
//                }

            } else {
                return response()->json(['success' => false]);
            }
        } else {
            return response()->json(['success' => false]);
        }
    }

    function cleanPaynetAmount($amount)
    {
        // 1. Nokta ve boşlukları sil (binlik ayraç olabilir)
//        $amount = str_replace(['.', ' ', '₺', 'TL', '$'], '', $amount);

        // 2. Virgülü noktaya çevir (opsiyonel)
//        $amount = str_replace(',', '.', $amount);

        // 3. Ondalık varsa, kuruşu ekleyip tam sayıya çevir
        $float = floatval($amount);
        $integerAmount = intval(round($float * 100)); // Paynet genellikle kuruşla birlikte tam sayı bekler

//        12187.5
//                121.875.00
        return $integerAmount; // Örn: 1234.56 ₺ → 123456
    }

    public function endDayOfReport(){
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        $branch_ids = $authUserBranchIds;
        $branches = Branch::whereIn('id', $branch_ids)->get();

        $start_date = !empty($_GET['start_date']) ? Carbon::make($_GET['start_date']) : Carbon::now();
        $finish_date = !empty($_GET['end_date']) ? Carbon::make($_GET['end_date']) : Carbon::now();
        $devir_bakiye = $_GET['devir_bakiye'] ?? 2;
        $fiyat_ayri = $_GET['fiyat_ayri'] ?? 2;
        $kupon_fiyat = $_GET['kupon_fiyat'] ?? 1;
        $selected_branch = $authUserBranchIds[0];
        if(!empty($_GET['branch_id'])){
            $selected_branch  = $_GET['branch_id'];
        }

        $expertise = ExpertisePayment::select('expertise_stocks.stock_id');

        $expertise->join('expertises', 'expertises.id', '=', 'expertise_payments.expertise_id')
            ->join('expertise_stocks', 'expertises.id', '=', 'expertise_stocks.expertise_id')
            ->join('stocks', 'stocks.id', '=', 'expertise_stocks.stock_id')
            ->where('expertises.branch_id', $selected_branch)
            ->whereNull('expertise_stocks.deleted_at')
            ->whereDate('expertises.created_at', '>=', $start_date)
            ->whereDate('expertises.created_at', '<=', $finish_date);

        if($fiyat_ayri == 1){
            $expertise->select('expertise_stocks.stock_id', 'expertise_payments.amount')
                ->groupBy('expertise_stocks.stock_id', 'expertise_payments.amount');
        }else{
            $expertise->groupBy('expertise_stocks.stock_id');
        }
        $expertise = $expertise->get();
        $expertise_name = $expertise->pluck('stock_id')->toArray();
        $stocks_name = Stock::whereIn('id', $expertise_name)->pluck('ad', 'id')->toArray();
        $stocks = [];

        foreach ($expertise as $data) {
            $id = $data->stock_id;
            $amount = $data->amount;
            if($fiyat_ayri != 1){
                $amount_data = ExpertisePayment::select('expertise_stocks.stock_id');

                $amount_data->join('expertises', 'expertises.id', '=', 'expertise_payments.expertise_id')
                    ->join('expertise_stocks', 'expertises.id', '=', 'expertise_stocks.expertise_id')
                    ->join('stocks', 'stocks.id', '=', 'expertise_stocks.stock_id')
                    ->where('expertises.branch_id', $selected_branch)
                    ->whereDate('expertises.created_at', '>=', $start_date)
                    ->whereDate('expertises.created_at', '<=', $finish_date)
                    ->where('expertise_stocks.stock_id', $id)
                    ->select('expertise_payments.amount')
                    ->orderBy('expertise_payments.amount', 'desc')
                    ->first();
                $amount = $amount_data->first()->amount;
            }
            $stock_array = array(
                'id' => $id,
                'amount' => $amount,
            );
            $stocks[] = $stock_array;
        }

        $report_data = [];

        foreach ($stocks as $key => $data) {
            $expertise_sql = ExpertisePayment::select('expertise_stocks.stock_id', 'expertises.id', 'expertise_payments.amount');
            $expertise_sql = $expertise_sql
                ->join('expertises', 'expertises.id', '=', 'expertise_payments.expertise_id')
                ->join('expertise_stocks', function ($join) {
                    $join->on('expertises.id', '=', 'expertise_stocks.expertise_id')
                        ->whereRaw('expertise_stocks.id = (
                SELECT MIN(es.id)
                FROM expertise_stocks es
                WHERE es.expertise_id = expertises.id AND es.deleted_at IS NULL
            )');
                })
                ->join('stocks', 'stocks.id', '=', 'expertise_stocks.stock_id')
                ->where('expertises.branch_id', $selected_branch)
                ->where('expertises.manuel_save', 1)
                ->whereNull('expertises.deleted_at')
                ->whereNull('expertise_payments.deleted_at')
                ->whereDate('expertises.created_at', '>=', $start_date)
                ->whereDate('expertises.created_at', '<=', $finish_date);


            if(isset($data['amount']) && $fiyat_ayri == 1){
                $expertise_sql = $expertise_sql->where('amount', $data['amount']);
            }

            $expertise_sql = $expertise_sql->where('expertise_stocks.stock_id', $data['id']);
            $expertise_count_sql = clone $expertise_sql;
            $expertise_count = $expertise_count_sql->count();
            $expertise_total_amount_nakit = clone $expertise_sql;
            $expertise_total_count_nakit = clone $expertise_sql;

            $expertise_total_amount = clone $expertise_sql;
            $expertise_total_amount_banka = clone $expertise_sql;
            $expertise_total_count_banka = clone $expertise_sql;
            $expertise_total_amount_plus_kart = clone $expertise_sql;
            $expertise_total_count_plus_kart = clone $expertise_sql;

            $expertise_total_amount_nakit = $expertise_total_amount_nakit->where('expertise_payments.type', 'nakit')->sum('amount');
            $expertise_total_amount_banka = $expertise_total_amount_banka->whereIn('expertise_payments.type', ['banka', 'kredi_karti'])->sum('amount');
            $expertise_total_amount_plus_kart = $expertise_total_amount_plus_kart->where('expertise_payments.type', 'plus_kart')->sum('amount');

            $expertise_total_count_nakit = $expertise_total_count_nakit->where('expertise_payments.type', 'nakit')->count();
            $expertise_total_count_banka = $expertise_total_count_banka->whereIn('expertise_payments.type', ['banka', 'kredi_karti'])->count();
            $expertise_total_count_plus_kart = $expertise_total_count_plus_kart->where('expertise_payments.type', 'plus_kart')->count();

            if($kupon_fiyat == 1){
                $expertise_total_amount = $expertise_total_amount->where('expertise_payments.type', '!=', 'plus_kart')->sum('amount');
            }else{
                $expertise_total_amount = $expertise_total_amount->sum('amount');
            }

            $report_data[] = [
                'name' => $stocks_name[$data['id']],
                'count' => $expertise_count,
                'amount' => $data['amount'],
                'fiyat' => $expertise_total_amount,
                'nakit_count' => $expertise_total_count_nakit,
                'nakit_tutar' => $expertise_total_amount_nakit,
                'kredi_banka_count' => $expertise_total_count_banka,
                'kredi_banka_tutar' => $expertise_total_amount_banka,
                'plus_kart_count' => $expertise_total_count_plus_kart,
                'plus_kart_tutar' => $expertise_total_amount_plus_kart,
            ];
        }

        $plus_card_points = PlusCardCrediAndPuanAdd::select('card_id','unit_price','puan','puan_branche_id')
            ->where('balance_type','points')
            ->where('puan_branche_id', $selected_branch)
            ->whereDate('created_at', '>=', $start_date)
            ->whereDate('created_at', '<=', $finish_date)->get();

        $expense = array(
            'plus_card_puan' =>$plus_card_points,
        );


        $plus_card_sales = PlusCardCrediAndPuanAdd::select('plus_card_credi_and_puan_add.*')
            ->join('users','users.id','=','plus_card_credi_and_puan_add.user_id')
            ->where('users.branch_id',$selected_branch)
            ->whereDate('plus_card_credi_and_puan_add.created_at', '>=', $start_date)
            ->whereDate('plus_card_credi_and_puan_add.created_at', '<=', $finish_date)
            ->where('payment_type','yeni_sistem_bakiye_aktirimi');


        $plus_card_sales_nakit = clone $plus_card_sales;
        $plus_card_sales_banka_kredi = clone $plus_card_sales;

        $plus_card_sales_nakit = $plus_card_sales_nakit->where('payment_type','nakit');
        $plus_card_sales_banka_kredi = $plus_card_sales_banka_kredi->whereIn('payment_type',['banka','kredi_karti']);

        $plus_card_data = array();

        $definitions = PlusCardsDefinitions::get();
        foreach($definitions as $d){
            $plus_card_data[] = array(
                'name' => $d->definition_name,
                'definition_sales_amount' => $d->unit_quantity * $d->unit_price,
                'sales_count' => $plus_card_sales->where('definitions_id',$d->id)->count(),
                'sales_amount' => $plus_card_sales->where('definitions_id',$d->id)->sum('credits_amount'),
                'sales_nakit_count' => $plus_card_sales->where('definitions_id',$d->id)->where('payment_type','nakit')->count(),
                'sales_nakit_amount' => $plus_card_sales->where('definitions_id',$d->id)->where('payment_type','nakit')->sum('credits_amount'),
                'sales_banka_kredi_count' => $plus_card_sales->where('definitions_id',$d->id)->whereIn('payment_type',['banka','kredi_karti'])->count(),
                'sales_banka_kredi_amount' => $plus_card_sales->where('definitions_id',$d->id)->whereIn('payment_type',['banka','kredi_karti'])->sum('credits_amount'),
            );
        }

        $filters['startDate'] = $start_date->format('Y-m-d');
        $filters['endDate'] = $finish_date->format('Y-m-d');

        return compact([
            'branches',
            'start_date',
            'finish_date',
            'selected_branch',
            'devir_bakiye',
            'fiyat_ayri',
            'kupon_fiyat',
            'report_data',
            'expense',
            'plus_card_data',
            'filters'
        ]);
    }

    public function PrintCikisTarihi(Request $request)
    {
        $expertise = Expertise::where('uuid',$request->uuid)->first();

        // If ftp is not ok, set cikis_tarihi
        if (!empty($expertise) && !empty($expertise->ftp_ok) && $expertise->ftp_ok == 2 && !auth()->user()->isAdmin()) {
            $expertise->cikis_tarihi = date('Y-m-d H:i:s');
            $expertise->save();
        }
    }

    public function excelDownloads(){
        return view('pages.excelDownloads.excel_download');
    }
    public function excelDownload(Request $request){
        if(!empty($request->excel_code)){
            $excel = ExcelDownload::where('code',$request->excel_code)->first();
            if(!empty($excel)){
                $file = "public/".$excel->file_name;
                $fileToDownload = $excel->type."_excel_".date('d_m_Y_H_i_s').".xlsx";
                $excel->download_use = 1;
                $excel->download_user = Auth()->user()->id;
                $excel->save();
                return Storage::download($file, $fileToDownload);
            }else{
                return redirect()->route('excelDownloads')->with('error','Excel Kodunuz Eksik');
            }
        }else{
            return redirect()->route('excelDownloads')->with('error','Excel Kodunuz Eksik');
        }
    }

    public function checkListUpdate(Request $request)
    {
        if (isset($request->id)){
            $check = CheckListControl::where('expertise_id',$request->id)->first();
            if (!$check){
                $check = new CheckListControl();
                $check->expertise_id = $request->id;
            }
            $check->note = $request->val;
            $check->save();
        }else{
            $check = CheckListControl::where('expertise_id',$request->val)->first();
            if ($check)
                $check->delete();
            else{
                $check = new CheckListControl();
                $check->expertise_id = $request->val;
                $check->save();
            }
        }


        return response()->json(['success'=>'true'],200);
    }

    public function plusCardCreditPointValidDateUpdater()
    {
        $plusCardBalanceAdds = [];
        $plusCardBalanceRemovals = [];
        $plusCardDefinitions = PlusCardsDefinitions::where('show_in_view',1)->get()->keyBy('id');

        PlusCardCrediAndPuanAdd::where('valid_date','<',now())
            ->where('balance_type','credits')
            ->where('credi','>',0)
            ->where('transfered',0)
            ->where('unit_price','>',0)
            ->chunk(100,function ($validCredits) use (&$plusCardBalanceAdds, &$plusCardBalanceRemovals,$plusCardDefinitions) {
                $toUpdate = [];
                foreach ($validCredits as $validCredit){
                    if (isset($plusCardDefinitions[$validCredit->definitions_id]) && $plusCardDefinitions[$validCredit->definitions_id]->unit_price && $plusCardDefinitions[$validCredit->definitions_id]->unit_price != $validCredit->unit_price){
                        $expertisePayments = ExpertisePayment::where('plus_card_odeme_id',$validCredit->id)->count();
                        $removes = PlusCardCrediAndPuanRemove::where('plus_card_credi_and_puan_add_id',$validCredit->id)->sum('amount');
                        $remain = $validCredit->credi - $expertisePayments - $removes;
                        if ($remain > 0){
                            $currentPrice = $plusCardDefinitions[$validCredit->definitions_id]->unit_price;
                            $payedPrice = $remain * $validCredit->unit_price;
                            $newCreditCount = round($payedPrice / $currentPrice);
                            if ($newCreditCount > 0){
                                $plusCardBalanceAdds[] = [
                                    'user_id' => 0,
                                    'balance_type' => $validCredit->balance_type,
                                    'stock_id' => $validCredit->stock_id,
                                    'definitions_id' => $validCredit->definitions_id,
                                    'card_id' => $validCredit->card_id,
                                    'credi' => $newCreditCount,
                                    'puan' => 0,
                                    'puan_branche_id' => 0,
                                    'odenen_kdv_dahil_fiyat' => 0,
                                    'unit_price' => $currentPrice,
                                    'payment_type' => 'expired_transfer',
                                    'devir_aciklama' => $validCredit->created_at->translatedFormat('d F Y') .' tarihinde yapılan yükleme('.$validCredit->id.') son kullanma tarihi dolduğu için '.now()->translatedFormat('d F Y').' tarihinde '.$currentPrice.'₺ fiyat değişikliğine göre kalan bakiye ve tutara dönüştürülmüştür.',
                                    'case' => 0,
                                    'valid_date' => now()->addMonths(6),
                                    'created_at' => now(),
                                    'updated_at' => now()
                                ];

                                $plusCardBalanceRemovals[] = [
                                    'user_id' => 0,
                                    'plus_card_credi_and_puan_add_id' => $validCredit->id,
                                    'amount' => $remain,
                                    'created_at' => now(),
                                    'updated_at' => now()
                                ];
                            }
                        }
                        $toUpdate[] = $validCredit->id;
                    }
                }

                // Perform bulk inserts for each chunk to keep memory usage low
                if (!empty($plusCardBalanceAdds)) {
                    PlusCardCrediAndPuanAdd::insert($plusCardBalanceAdds);
                    $plusCardBalanceAdds = []; // Reset array after insert
                }
                if (!empty($plusCardBalanceRemovals)) {
                    PlusCardCrediAndPuanRemove::insert($plusCardBalanceRemovals);
                    $plusCardBalanceRemovals = []; // Reset array after insert
                }

                if (!empty($toUpdate)) {
                    PlusCardCrediAndPuanAdd::whereIn('id', $toUpdate)->update(['transfered' => 1]);
                }
            });
    }
}
