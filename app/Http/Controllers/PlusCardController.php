<?php

namespace App\Http\Controllers;

use App\Jobs\PlusCardExcel;
use App\Jobs\PlusCardReportExportJob;
use App\Models\Branch;
use App\Models\City;
use App\Models\CustomerBuyExpertise;
use App\Models\CustomerPlusCardsIndex;
use App\Models\Expertise;
use App\Models\ExpertisePayment;
use App\Models\PaymentLog;
use App\Models\PlusCard;
use App\Models\PlusCardAgreement;
use App\Models\PlusCardCampaign;
use App\Models\PlusCardCampaignUsage;
use App\Models\PlusCardCrediAndPuanAdd;
use App\Models\PlusCardCrediAndPuanRemove;
use App\Models\PlusCardDefinitionPrice;
use App\Models\PlusCardsDefinitions;
use App\Models\PlusCardsSavedFilter;
use App\Models\PlusCardStock;
use App\Models\Setting;
use App\Models\Stock;
use App\Models\StockPrice;
use App\Models\T404KUPONTAKIP;
use App\Models\Town;
use App\Models\ZoneBranch;
use App\Services\ChargeParameters;
use App\Services\PaynetClient;
use App\Services\PlusCardCampaignService;
use App\Services\BranchService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\PlusCardBalance;
use Illuminate\Support\Facades\DB;

class PlusCardController extends Controller
{
    /**
     * Display a listing of the resource...
     */
    public function index(Request $request)
    {
        $savedFilters = PlusCardsSavedFilter::where('user_id',auth()->user()->id)->get();
        $selectedSavedFilter = isset($_GET['saved_filter_id']) && (int)$_GET['saved_filter_id'] > 0 ? PlusCardsSavedFilter::where(['user_id'=>auth()->user()->id,'id'=>(int)$_GET['saved_filter_id']])->first() : null;

        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');

        if (!empty($startDate)) {
            $startDate = Carbon::make($request->get('start_date'))->format('Y-m-d');
        } else {
            $startDate = null;
        }

        if (!empty($endDate)) {
            $endDate = Carbon::make($request->get('end_date'))->format('Y-m-d');
        } else {
            $endDate = null;
        }

        return view('pages.plus_card.index', [
            'savedFilters' => $savedFilters,
            'selectedSavedFilter' => $selectedSavedFilter,
            'filters' => ['startDate' => $startDate, 'endDate' => $endDate]
        ]);
    }

    public function indexAjax(Request $request)
    {
        // Increase memory limit for large datasets for admin users
        ini_set('memory_limit', '512M');
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        $isBayi = auth()->user()->type !== 'admin'; // suppose everyone is a bayi user except admin
        if ($isBayi) {
            $limit = $request->input('length', 10);
            $start = $request->input('start', 0);
            $allowedLimits = [10, 25,50, 100];
            if (!in_array($limit, $allowedLimits)) {
            $limit = 10;
            } // For filtering and pagination
                $orderColumn = $request->input('order.0.column');
                $orderDirection = $request->input('order.0.dir', 'asc');
                $columns = [
                    0 => 'customer_telefon',
                    1 => 'customer_name',
                    2 => 'no',
                    3 => 'balance_credits',
                    4 => 'balance_points',
                    5 => 'last_updated_date',
                    6 => 'customer_code',
                    7 => 'first_upload_date',
                    8 => 'branch_name',
                    9 => 'system_id'
                ];
        } else {
            $limit = $request->input('length');
            $start = $request->input('start');
        }
        $customer_name_filter = null;
        $customer_code_filter = null;
        $card_no_filter = null;
        $branch_id_filter = null;
        $start_date_filter = !empty($request->start_date) ? Carbon::make($request->start_date)->format('Y-m-d') : null;
        $end_date_filter = !empty($request->end_date) ? Carbon::make($request->end_date)->format('Y-m-d') : null;

        if (!empty($request->input('branch_id')) && $request->input('branch_id') > 0 && in_array($request->input('branch_id'),$authUserBranchIds))
            $branch_id = [$request->input('branch_id')];
        else
            $branch_id = $authUserBranchIds;


        $items = PlusCard::query();

        if(!empty($request->input('saved_filter_id')))
        {
            $savedFilter = PlusCardsSavedFilter::where('id',$request->input('saved_filter_id'))->first();
            if(!empty($savedFilter)){
                $customer_name_filter = $savedFilter->customer_name;
                $customer_code_filter = $savedFilter->customer_code;
                $card_no_filter = $savedFilter->card_no;
                $branch_id_filter = $savedFilter->branch_id;
            }
        }else{
           if(!empty($request->input('customer_name'))){
               $customer_name_filter = mb_strtoupper($request->input('customer_name','UTF-8'));
           }
            if(!empty($request->input('customer_telephone'))){
                $customer_telephone_filter = $request->input('customer_telephone');
            }
            if(!empty($request->input('system_id'))){
                $system_id_filter = $request->input('system_id');
            }
           if(!empty($request->input('customer_code'))){
               $customer_code_filter = $request->input('customer_code');
           }
            if(!empty($request->input('customer_code_old'))){
                $customer_code_old_filter = $request->input('customer_code_old');
            }
            if(!empty($request->input('card_no'))){
                $card_no_filter = $request->input('card_no');
            }
            if(!empty($request->input('branch_id'))){
                $branch_id_filter = $request->input('branch_id');
            }
            if(!empty($request->input('saved_filter_status'))){
                $saved_filter_status = $request->input('saved_filter_status');
            }
            if(!empty($request->input('created_at'))){
                $created_at_filter = $request->input('created_at');
            }
        }

        if(!empty($card_no_filter)){
            $items = $items->where('no', 'LIKE', '%' . $card_no_filter . '%');
        }

        if(!empty($system_id_filter)){
            $items = $items->where('system_id', $system_id_filter);
        }

        if(!empty($created_at_filter)){
            $items = $items->where('created_at','>=', $created_at_filter);
        }

        if(!empty($customer_name_filter) || !empty($customer_telephone_filter)) {
            $items = $items->join('customers', 'customers.id', '=', 'plus_cards.customer_id');
        }

        if(!empty($customer_name_filter)){
            $items = $items->where('customers.unvan', 'LIKE', '%' . $customer_name_filter . '%');
        }

        if(!empty($customer_telephone_filter)){

            $telefon_no_zero = null;
            $telefon = str_replace(['(',')',' '],'',$customer_telephone_filter);
            $old_telefon = formatTelephoneNumber($telefon);
            if (substr($telefon, 0, 1) === '0') {
                $telefon_no_zero = substr($telefon, 1);
            }

            $items = $items->where(function($query) use ($telefon_no_zero, $telefon, $old_telefon) {
                $query->where('customers.telefon', '=', "$telefon_no_zero")
                    ->orWhere('customers.telefon', '=', "$telefon")
                    ->orWhere('customers.telefon', '=', "$old_telefon")
                    ->orWhere('customers.cep', '=', "$telefon_no_zero")
                    ->orWhere('customers.cep', '=', "$telefon")
                    ->orWhere('customers.cep', '=', "$old_telefon");
            });
        }

        if(!empty($customer_code_filter)){
            if(empty($customer_name_filter)){
                $items = $items->join('customers', 'customers.id', '=', 'plus_cards.customer_id');
            }
            $items = $items->where('customers.cari_kod', 'LIKE', '%' . $customer_code_filter . '%');
        }
        if(!empty($customer_code_old_filter)){
            if(empty($customer_name_filter)){
                $items = $items->join('customers', 'customers.id', '=', 'plus_cards.customer_id');
            }
            $items = $items->where('customers.eski_cari_kod', 'LIKE', '%' . $customer_code_old_filter . '%');
        }


        if(!empty($branch_id_filter)){
            $items = $items->join('branches', 'branches.id', '=', 'plus_cards.branch_id')
                ->where('branches.id', '=',$branch_id_filter);
        }
        if (!empty($start_date_filter) && !empty($end_date_filter)) {
            $items = $items->join('expertise_payments', 'expertise_payments.plus_card_id', '=', 'plus_cards.id')
                ->join('customers', 'customers.id', '=', 'plus_cards.customer_id')
                ->where('expertise_payments.type', 'plus_kart')
                ->groupBy('plus_cards.id','plus_cards.created_at','plus_cards.updated_at','plus_cards.system_id','plus_cards.no','plus_cards.branch_id','plus_cards.customer_id','customers.telefon', 'customers.unvan','customers.cari_kod','customers.eski_cari_kod')
                ->whereBetween('expertise_payments.created_at', [$start_date_filter . ' 00:00:00', $end_date_filter . ' 23:59:59']);
        }

        if(!empty($saved_filter_status)){
            if($saved_filter_status == 1){
                $items = $items->where('customer_id', ">",0);
            }elseif($saved_filter_status == 2){
                $items = $items->where(function ($query){
                    return $query->where('customer_id', 0)->orWhere('customer_id',null);
                });
            }
        }

        if ($request->input('unused_cards')) {
            $items = $items->whereRaw("system_id REGEXP '^[0-9]{7,12}$'")
                ->whereRaw("no REGEXP '^04445417[0-9]{8}$'");
        }

        $items = $items->whereIn('plus_cards.branch_id',$branch_id);
        $total = $items->count();
        // Custom sorting for  (non-admin) users
        if ($isBayi) {
            $items = BranchService::getBayiSorting($items, $orderColumn, $orderDirection, $columns, $start, $limit);
        } else {
            // Default order for admin
            $items = $items->offset($start)
                ->limit($limit)
                ->orderBy('plus_cards.id', 'desc')
                ->select(
                'plus_cards.id',
                'plus_cards.system_id',
                'plus_cards.no',
                'plus_cards.branch_id',
                'plus_cards.customer_id',
                'plus_cards.created_at',
                'plus_cards.updated_at'
            );
        }
        $items = $items->get();
        $data = array();
        if(!empty($items))
        {
            foreach ($items as $item)
            {
                $disabled = "";

                if((!auth()->user()->getUserRoleGroup && !auth()->user()->getUserRoleGroup->getRoles->where('key','edit_plus_card')->first())){
                    $disabled = "disabled=''";
                }
                if (auth()->user()->type != 'admin')
                    $disabled = "disabled=''";
                $hlxPlusCard = DB::table('hlx_plus_try')->where('no',$item->no)->first();
                $dehaBakiye = auth()->user()->type != 'admin' ? 'Yok' : 0;
                if ($hlxPlusCard){
                    $eskiSistemKredi = DB::connection('mysql')
                        ->table('plus_card_credi_and_puan_add_try')
                        ->where('card_id',$hlxPlusCard->id)
                        ->where('balance_type','credits')
                        ->where('payment_type','yeni_sistem_bakiye_aktirimi')
                        ->sum('credi');
                    $harcamalar = DB::connection('mysql')
                        ->table('expertise_payments_try')
                        ->where('plus_card_id',$hlxPlusCard->id)
                        ->count();
                    if ($eskiSistemKredi - $harcamalar > 0)
                        $dehaBakiye = auth()->user()->type != 'admin' ? 'Var' : $eskiSistemKredi - $harcamalar;
                }
                $balance = $item->getTotalBalance();
                if ($isBayi) {
                    // Bayi (non-admin) column order
                    $nestedData['customer_telefon'] = $item->getCustomer?->telefon ?? '';
                    $nestedData['customer_name'] = $item->getCustomer?->fullName ?? '';
                    $nestedData['no'] = $item->no;
                    $nestedData['balance_credits'] = $balance['credits'];
                    $nestedData['balance_points'] = $balance['points'];
                    $nestedData['last_updated_date'] = $item->getLastUpload?->updated_at ? Carbon::make($item->getLastUpload->created_at)->format('d.m.Y') : '';
                    $nestedData['customer_code'] = $item->getCustomer?->cari_kod ?? '';
                    $nestedData['first_upload_date'] = $item->getFirstUpload?->created_at ? Carbon::make($item->getFirstUpload->created_at)->format('d.m.Y') : '';
                    $nestedData['branch_name'] = $item->getFirstUpload?->getUser?->branches->first()?->kisa_ad ?? '';
                    $nestedData['system_id'] = "<input class='form-control form-control-sm system_id' data-plusCard='".$item->id."' $disabled value=".$item->system_id." />";
                    $nestedData['cleared_system_id'] = $item->system_id;
                    $nestedData['customer_code_old'] = $item->getCustomer?->eski_cari_kod ?? '';
                    $nestedData['options'] = ''; // Will be set below
                } else {
                    // Admin or other roles, keep previous order
                    $nestedData['system_id'] = "<input class='form-control form-control-sm system_id' data-plusCard='".$item->id."' $disabled value=".$item->system_id." />";
                    $nestedData['cleared_system_id'] = $item->system_id;
                    $nestedData['id'] = $item->id;
                    $nestedData['no'] = $item->no;
                    $nestedData['deha_bakiye'] = $dehaBakiye;

                  //$nestedData['balance_credits'] = auth()->user()->type != 'admin' ? ($balance['credits'] > 0 ? 'Var' : 'Yok') : $balance['credits'];
                  //$nestedData['balance_points'] = auth()->user()->type != 'admin' ? ($balance['points'] > 0 ? 'Var' : 'Yok') : $balance['points'];
                    $nestedData['balance_credits'] = $balance['credits'];
                    $nestedData['balance_points'] = $balance['points'];
                    $nestedData['branch_name'] = $item->getBranch?->kisa_ad ?? '';
                    $nestedData['customer_code'] = $item->getCustomer?->cari_kod ?? '';
                    $nestedData['customer_code_old'] = $item->getCustomer?->eski_cari_kod ?? '';
                    $nestedData['customer_name'] = $item->getCustomer?->fullName ?? '';
                    $nestedData['customer_telefon'] = $item->getCustomer?->telefon ?? '';
                    $nestedData['last_updated_date'] = $item->updated_at ? $item->updated_at->format('d.m.Y H:i') : '';
                    $nestedData['first_upload_date'] = $item->created_at ? $item->created_at->format('d.m.Y H:i') : '';
                }
                $html = '<div class="d-flex" style="flex-direction: row;gap:1px">';
                if(auth()->user()->getUserRoleGroup && auth()->user()->getUserRoleGroup->getRoles->where('key','edit_plus_card')->first()){
                    $html .= '<a class="btn btn-sm btn-success" href="'.route('plus-cards.edit',$item->id).'"><i class="fa fa-edit"></i></a>';
                    $html .= '<button type="button" class="btn btn-sm btn-warning plus_card_balance_detail_upload" data-id="'.$item->id.'"><i class="fa fa-eye"></i></button>';
                }

                if(auth()->user()->getUserRoleGroup && auth()->user()->getUserRoleGroup->getRoles->where('key','credits_plus_card')->first()){
                    $html .= '<a class="btn btn-sm btn-success" href="'.route('plus-cards.edit',$item->id).'"><i class="fa fa-upload"></i></a>';
                }

                if(auth()->user()->getUserRoleGroup && auth()->user()->getUserRoleGroup->getRoles->where('key','delete_plus_card')->first()){
                    $html .= '<button class="btn btn-sm btn-danger deletePlusCard" type="button"  data-id="'.$item->id.'"><i class="fa fa-trash"></i></button>';
                }
                $html .= "</div>";
                $nestedData['options'] = $html;
                $data[] = $nestedData;

            }
        }

        $json_data = array(
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($total),
            "recordsFiltered" => intval($total),
            "data"            => $data,
            'filters'          => ['startDate' => $start_date_filter, 'endDate' => $end_date_filter]
        );
        echo  json_encode($json_data);
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $branches = Branch::where('status',1)->get();
        $stocks = Stock::where('show_on_expertise',1)->where('status',1)->get();

        return view('pages.plus_card.create',compact(['branches','stocks']));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {

        $cardNumber = str_replace(' ', '', $request->input('no'));

        // Validate the card number format
        if (!PlusCard::isValidCardNumber($cardNumber)) {
            return back()->with('error', 'Kart numarası geçersiz! Format: 0444 5417 XXXX XXXX');
        }

        // validate if no exists before
        $check = PlusCard::where(function ($query) use ($cardNumber, $request) {
            $query->where('no', $cardNumber)
                    ->orWhere('no', $request->no);
        })->first();

        if (!empty($check)) {
            return back()->with('error', 'Bu kart numarası kullanımda!');
        }

        // Checking if there is already a PlusCard with the same branch_id and customer_id
        if ($request->customer_id && $request->branch_id){
            $checkBranchCustomer = PlusCard::where('branch_id', $request->branch_id)
                ->where('customer_id', $request->customer_id)
                ->first();

            if (!empty($checkBranchCustomer)) {
                return back()->with('error', 'Aynı müşteri ve şube için zaten bir PlusCard mevcut!');
            }
        }


        $createdAt = now();

        if (isset($request->multiple) && $request->multiple == 'on') {
            $request->no = str_replace(' ', '', $request->no);
            for ($a = 0; $a < $request->count; $a++) {
                $plusCard = new PlusCard();
                $plusCard->no = sprintf('0%s', intval($request->no) + $a);
                $plusCard->branch_id = $request->branch_id;
                $plusCard->customer_id = !empty($request->customer_id) ? $request->customer_id : 0;
                $plusCard->valid_date_if = $request->valid_date_if;
                $plusCard->valid_date = Carbon::make($request->valid_date);
                $plusCard->save();

                if (!empty($request->stocks)) {
                    foreach ($request->stocks as $key => $stock) {
                        if (!empty($stock)) {
                            $stock_price = StockPrice::where('stock_id', $stock)->where('campaign_id', 0)->first();
                            $total_price = !empty($stock_price->kdv_dahil_fiyat) ? $stock_price->kdv_dahil_fiyat * $request->stock_price[$key] : 0;
                            $plus_card_balance_add = new PlusCardCrediAndPuanAdd();
                            $plus_card_balance_add->user_id = auth()->user()->id;
                            $plus_card_balance_add->stock_id = $stock;
                            $plus_card_balance_add->card_id = $plusCard->id;
                            $plus_card_balance_add->credi = $request->stock_price[$key];
                            $plus_card_balance_add->puan = $request->stock_puan[$key];
                            $plus_card_balance_add->puan_branche_id = !empty($request->stocks_branches[$key]) ? $request->stocks_branches[$key] : 0;
                            $plus_card_balance_add->odenen_kdv_dahil_fiyat = $total_price;
                            $plus_card_balance_add->save();
                        }
                    }
                }

                logRecord("add_plus_card", auth()->user()->name . " adlı kullanıcı $plusCard->no no'lu plus card ekledi", $plusCard->id);
            }
        } else {
            $request->no = str_replace(' ', '', $request->no);

            $plusCard = new PlusCard();
            $plusCard->no = $request->no;
            $plusCard->branch_id = $request->branch_id;
            $plusCard->customer_id = !empty($request->customer_id) ? $request->customer_id : 0;
            $plusCard->valid_date_if = $request->valid_date_if;
            $plusCard->valid_date = carbon::make($request->valid_date);
            $plusCard->save();

            if ($request->stocks) {
                foreach ($request->stocks as $key => $stock) {
                    if (!empty($stock)) {
                        $stock_price = StockPrice::where('stock_id', $stock)->where('campaign_id', 0)->first();
                        $total_price = !empty($stock_price->kdv_dahil_fiyat) ? $stock_price->kdv_dahil_fiyat * $request->stock_price[$key] : 0;
                        $plus_card_balance_add = new PlusCardCrediAndPuanAdd();
                        $plus_card_balance_add->user_id = auth()->user()->id;
                        $plus_card_balance_add->stock_id = $stock;
                        $plus_card_balance_add->card_id = $plusCard->id;
                        $plus_card_balance_add->credi = $request->stock_price[$key];
                        $plus_card_balance_add->puan = $request->stock_puan[$key];
                        $plus_card_balance_add->puan_branche_id = !empty($request->stocks_branches[$key]) ? $request->stocks_branches[$key] : 0;
                        $plus_card_balance_add->odenen_kdv_dahil_fiyat = $total_price;
                        $plus_card_balance_add->save();
                    }
                }
            }

            logRecord("add_plus_card", auth()->user()->name . " adlı kullanıcı $plusCard->no no'lu plus card ekledi", $plusCard->id);
        }

        if (Cache::has('plus_card_create')) {
            Cache::forget('plus_card_create');
        }

        return redirect('/plus-cards?created_at=' . $createdAt)->with('success', 'Başarıyla Eklendi!');
    }


    /**
     * Display the specified resource.
     */
    public function show(PlusCard $plusCard)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PlusCard $plusCard, PlusCardCampaignService $campaignService)
    {
        $branches = Branch::where('status',1)
            ->whereIn('id',auth()->user()->getBranchIds())
            ->get();


        $stocks = Stock::where('show_on_expertise',1)->where('status',1)->get();

        $plusCardDefinitions = PlusCardsDefinitions::orderBy('created_at','desc')->get();
        $cities = City::get();
        $counties = array();
        $getCustomer = $plusCard->getCustomer;
        if(!empty($getCustomer) && !empty($getCustomer->il_kodu)){
            $counties = Town::where('ilce_sehirkey',$getCustomer->il_kodu)->get();
        }

        $empty_user_information = 2;
        $balance_add_disabled = 0;
        if(!empty($getCustomer)){
            if(
                empty($getCustomer->mahalle) ||
                empty($getCustomer->cadde) ||
                empty($getCustomer->sokak) ||
                empty($getCustomer->semt) ||
                empty($getCustomer->il_kodu) ||
                empty($getCustomer->ilce_kodu) ||
                empty($getCustomer->telefon) ||
                empty($getCustomer->vergi_dairesi) ||
                empty($getCustomer->vergi_no)
            ){
                $empty_user_information = 1;
            }
        }else{
            $balance_add_disabled = 1;
        }

        $current_time = Carbon::now();
        $ten_minutes_ago = $current_time->subMinutes(10);
        $plus_card_agreement = PlusCardAgreement::where('created_at', '>=', $ten_minutes_ago)->where('plus_card_id',$plusCard->id)
            ->where('user_id',auth()->user()->id)
            ->where('kvkk_approval',2)
            ->where('agreement_approval',2)
            ->first();
//        if ($plus_card_agreement && !$plus_card_agreement->delayed_id){
//            $plus_card_agreement->delayed_id = Str::random(7);
//            $plus_card_agreement->save();
//        }
        //dd($plus_card_agreement);

//        if ($plusCard->id == 173303) {
            // get eligible campaigns
            $campaigns = $campaignService->getActiveCampaignsForCard($plusCard->id);
//        } else {
//            $campaigns = collect();
//        }

        return view('pages.plus_card.edit', compact([
            'plusCard', 'branches', 'stocks', 'plusCardDefinitions',
            'cities', 'counties', 'balance_add_disabled', 'empty_user_information', 'plus_card_agreement',
            'campaigns'
        ]));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PlusCard $plusCard)
    {
        // Validate the card ID format
        if (!PlusCard::isValidCardID($request->system_id)) {
            return back()->with('error', 'Sistem ID formatı geçersiz!');
        }

        $cardNumber = str_replace(' ', '', $request->input('no'));

        // Validate the card number format
        if (!PlusCard::isValidCardNumber($cardNumber)) {
            return back()->with('error', 'Kart numarası geçersiz! Format: 0444 5417 XXXX XXXX');
        }

        $check = PlusCard::where('no',$request->no)->orWhere('no', $cardNumber)->where('id','!=',$plusCard->id)->whereNotNull('system_id')->first();

        if (!empty($check) && $check->id != $plusCard->id && ($check->customer_id != 0 || $check->customer_id != null))
            return back()->with(['error' => 'Bu Numara Kullanımda!', 'redirect' => route('plus-cards.edit', $check)]);

        if (!$check || $check->id == $plusCard->id) {
            $check = PlusCard::where('system_id',$request->system_id)->where('id','!=',$plusCard->id)->whereNotNull('system_id')->first();
            if (!empty($check) && $check->id != $plusCard->id)
                return back()->with(['error'=>'Bu Sistem ID Kullanımda!','redirect'=>route('plus-cards.edit',$check)]);
        }

        if ($check && $check->customer_id != null)
            return back()->with(['error'=>'Bu Numara Başka Caride Tanımlı!','redirect'=>route('plus-cards.edit', $check->id)]);


        $log = "";
        $update_data = 2;

        if (auth()->user()->type == 'admin'){
            if ($check){
                $log .= "sistem id'sini $plusCard->system_id => $check->system_id olarak güncelledi.";
                $plusCard->system_id = $check->system_id;
                $update_data = 1;

                $log .= "kart no $plusCard->no => $check->no olarak güncelledi.";
                $plusCard->no = $check->no;
                // $update_data = 1;
                $check->delete();
            }else{
                if (!empty($request->system_id) && $request->system_id != $plusCard->system_id){
                    $log .= "sistem id'sini $plusCard->system_id => $request->system_id olarak güncelledi.";
                    $plusCard->system_id = $request->system_id;
                    $update_data = 1;
                }
                if (!empty($cardNumber) && $cardNumber != $plusCard->no){
                    $log .= "kart no $plusCard->no => $cardNumber olarak güncelledi.";
                    $plusCard->no = $cardNumber;
                    $update_data = 1;
                }
            }
        }
        // controlle the selected card and assign it if it is empty
        if ($check && $check->customer_id == null) {
            if (!empty($request->system_id) && $request->system_id != $plusCard->system_id) {
                $log .= "sistem id'sini $plusCard->system_id => $request->system_id olarak güncelledi.";
                $plusCard->system_id = $request->system_id;
                $update_data = 1;
            }
            if (!empty($cardNumber) && $cardNumber != $plusCard->no) {
                $log .= "kart no $plusCard->no => $cardNumber olarak güncelledi.";
                $plusCard->no = $cardNumber;
                $update_data = 1;
            }
            $check->delete();
        }
        if (!empty($request->branch_id) && $request->branch_id != $plusCard->branch_id)
        {
            $log .= "şube id'sini $plusCard->branch_id => $request->branch_id olarak güncelledi.";
            $plusCard->branch_id = $request->branch_id;
            $update_data = 1;
        }

        if (!empty($request->customer_id) && $request->customer_id != $plusCard->customer_id)
        {
            $log .= "cari id'sini $plusCard->customer_id => $request->customer_id olarak güncelledi.";
            $plusCard->customer_id = $request->customer_id;
            $update_data = 1;


        }

        if (!empty($request->valid_date) && $request->valid_date != $plusCard->valid_date) {
            $log .= "geçerlilik tarihini $plusCard->valid_date => $request->valid_date olarak güncelledi.";
            $plusCard->valid_date = Carbon::make($request->valid_date);
            $update_data = 1;
        }

        if (isset($request->valid_date_if) && $request->valid_date_if != $plusCard->valid_date_if) {
            $log .= "geçerlilik tarihini uygunlansını $plusCard->valid_date_if => $request->valid_date_if olarak güncelledi.";
            $plusCard->valid_date_if = !empty($request->valid_date_if) ? $request->valid_date_if : 0;
            $update_data = 1;
        }

        if ($update_data == 1) {
            $plusCard->save();

            if (!empty($request->customer_id)) {
                $customer_plus_card_index = CustomerPlusCardsIndex::find($request->customer_id);
                $customer_plus_card_index->searchable();
            }
        }



        if (count($plusCard->getChanges()) > 0)
            logRecord("edit_plus_card",auth()->user()->name . " adlı kullanıcı $plusCard->no no'lu plus card ".$log,$plusCard->id);

        if ($request->quantity){
            foreach ($request->quantity as $stockID => $value){
                $check = PlusCardStock::where(['plus_card_id'=>$plusCard->id,'stock_id'=>$stockID])->first();
                if ($check){
                    if ($value != $check->quantity)
                        logRecord("edit_plus_card_stock",auth()->user()->name . " adlı kullanıcı $plusCard->no no'lu plus card'tan  ".$check->getStock->ad . " adlı stoğun kalan adetini $plusCard->quantity => $value olarak güncelledi.",$check->id);
                    $check->quantity = $value;
                    $check->save();
                }elseif($value > 0){
                    $plusCardStock = new PlusCardStock();
                    $plusCardStock->plus_card_id = $plusCard->id;
                    $plusCardStock->stock_id = $stockID;
                    $plusCardStock->quantity = $value;
                    $plusCardStock->save();

                    logRecord("add_plus_card_stock",auth()->user()->name . " adlı kullanıcı $plusCard->no no'lu plus card'a ".$plusCardStock->getStock->ad . " adlı stoğu $value kadar kullanım hakkı ekledi.",$plusCardStock->id);
                }
            }
        }
        else{
            $plusCardStocks = PlusCardStock::where('plus_card_id',$plusCard->id)->get();
            foreach ($plusCardStocks as $plusCardStock){
                $plusCardStock->delete();
                logRecord("delete_plus_card_stock",auth()->user()->name . " adlı kullanıcı $plusCard->no no'lu plus card'tan  ".$plusCardStock->getStock->ad . " adlı stoğu sildi.",$plusCardStock->id);
            }
        }
        return redirect()->back()->with('success','Başarıyla Güncelledi!');

        //return redirect()->route('plus-cards.index')->with('success','Başarıyla Güncelledi!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PlusCard $plusCard)
    {
        die(print_r($plusCard));
        $plusCard->delete();

        logRecord("delete_plus_card",auth()->user()->name . " adlı kullanıcı $plusCard->no no'lu plus card'ı sildi.",$plusCard->id);

        return redirect()->route('plus-cards.index')->with('success','Başarıyla Silindi!');
    }

    /**
     * PAYNET CALLBACK
     *
     * @param Request $request
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Foundation\Application|\Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function plus_cards_balance(Request $request, PlusCardCampaignService $campaignService)
    {
        $campaignId = $request->input('campaign_id');
        $quantity = $request->input('unit_quantity');

        $plusCard = PlusCard::find($request->plus_card_id);

        if (!empty($plusCard)) {
            if ($request->balance_type == "credits") {
                if (empty($request->plus_cards_definitions)) {
                    return redirect()->back()->with('error', 'Kredi Tipi Zorunludur.');
                }

                if (empty($request->unit_quantity)) {
                    return redirect()->back()->with('error', 'Kredi Miktarı Zorunludur.');
                }

                if (empty($request->payment_amount)) {
                    return redirect()->back()->with('error', 'Tahsilat Tutarı Zorunludur.');
                }

                $definition = PlusCardsDefinitions::where('id', $request->plus_cards_definitions)
                    ->where('show_in_view', 1)->first();

                if (!$definition) {
                    return redirect()->back()->with('error', 'Kredi Tipi Zorunludur.');
                }

                // Total Item
                $amount = $definition->unit_quantity * $definition->unit_price;

                if ($quantity != $definition->unit_quantity) {
                    $amount = $quantity * $definition->unit_price;
                }

                $paynetAmount = (float) str_replace(
                    ",",
                    ".",
                    str_replace(",", ".", str_replace(["₺", "."], "", $request->payment_amount))
                );

                if ($amount != $paynetAmount) {
                    // TODO burayi cozelim
                    // return redirect()->back()->with('error', 'Tutarlar Eşleşmiyor!');
                }

                if ($request->payment_type == 'kredi_karti'){
//                    $checkPayment = null;
//                    if ($request->delayed_id)
//                        $checkPayment = PaymentLog::where('payment_id',$request->delayed_id)->where('status',1)->first();
//
//                    if (!$checkPayment)
//                        return redirect()->back()->with('error','Ödeme Alınamadı!');

                    $paynetxy = new PaynetClient('sck_Vl2wgbV3Binka1CPI0_NX4XFP-kp', true);
                    $paynetxy->agent_id = auth()->user()->getBranch->paynet_code ?? "1697";
                    $paynetxy->user_name = "barisuca";

                    $tdsChargeParams = new ChargeParameters();
                    $tdsChargeParams->session_id = $request->session_id;
                    $tdsChargeParams->token_id = $request->token_id;
                    $tdsChargeParams->transaction_type = 1;
                    $tdsChargeParams->amount = ((float) str_replace(",", ".", str_replace(",", ".", str_replace(["₺", "."], "", $request->payment_amount)))) * 100;
                    $tdsChargeParams->add_comission_amount = $request->add_comission_amount;
//        $tdsChargeParams->installments = $request->installments;
                    $tdsChargeParams->no_instalment = $request->no_instalment;
                    $tdsChargeParams->tds_required = $request->tds_required;

                    $result = $paynetxy->ChargePost($tdsChargeParams);

                    if($result->is_succeed && $result->amount == (float) str_replace(",", ".", str_replace(",", ".", str_replace(["₺", "."], "", $request->payment_amount)))){
                        $paymentLog = new PaymentLog();
                        $paymentLog->payment_session_id = $request->session_id;
                        $paymentLog->payment_token_id = $request->token_id;
                        $paymentLog->file_uuid = $result->agent_reference_no;
                        $paymentLog->payment_id = $result->agent_reference_no;
                        $paymentLog->payment_price = $result->amount;
                        $paymentLog->payment_req = json_encode($tdsChargeParams);
                        $paymentLog->payment_res = json_encode($result);
                        $paymentLog->payment_type = 2;
                        $paymentLog->status = 1;
                        $paymentLog->save();

                        $check = PlusCardCrediAndPuanAdd::where('delayed_id',$request->delayed_id)->first();
                        if(empty($check)){
                            $credits_add = new PlusCardCrediAndPuanAdd();
                            $credits_add->user_id = auth()->user()->id;
                            $credits_add->stock_id = $request->balance_stock_id;
                            $credits_add->definitions_id = $request->plus_cards_definitions;
                            $credits_add->balance_type = $request->balance_type;
                            $credits_add->card_id = $request->plus_card_id;
                            $credits_add->unit_price = (float) str_replace(",", ".", str_replace(",", ".", str_replace(["₺", "."], "", $request->unit_price)));
                            $credits_add->commission =(float) str_replace(",", ".", str_replace(",", ".", str_replace(["₺", "."], "", $request->commission)));
                            $credits_add->credits_amount =(float) str_replace(",", ".", str_replace(",", ".", str_replace(["₺", "."], "", $request->credits_amount))) ;
                            $credits_add->credi = $request->unit_quantity;
                            $credits_add->puan_branche_id = $request->valid_branch;
                            $credits_add->odenen_kdv_dahil_fiyat =(float) str_replace(",", ".", str_replace(",", ".", str_replace(["₺", "."], "", $request->payment_amount)));
                            $credits_add->payment_type = $request->payment_type;
                            $credits_add->valid_date = !empty($request->valid_date) ? $request->valid_date : null;
                            $credits_add->delayed_id = $request->delayed_id;
                            $credits_add->case = 0;
                            $credits_add->save();
                        }

                        // If there is a campaign
                        $campaign = PlusCardCampaign::find($campaignId);

                        if ($campaign) {
                            $result = $campaignService->applyDiscount(
                                $campaign->id,
                                $plusCard->id,
                                $amount,
                                null,
                                true
                            );

                            if ($result['success']) {
                                return redirect($request->return_url)->with('success', 'Kampanyalı Satın Alım Başarılı');
                            }
                        }

                        return redirect($request->return_url)->with('success','Satın Alım Başarılı');
                    }else{
                        return redirect($request->return_url)->with('error','İşlem Başarısız Oldu!');
                    }
                }

                if(!empty($request->return_url)){
                    return redirect($request->return_url)->with('success','Bakiye Yüklendi');
                }else{
                    return redirect()->back()->with('success','Bakiye Yüklendi');
                }
            }
            elseif ($request->balance_type == 'devir_credits'){
                if(empty($request->devir_unit_quantity))
                    return redirect()->back()->with('error','Kredi Miktarı Zorunludur.');

                $credits_add = new PlusCardCrediAndPuanAdd();
                $credits_add->user_id = auth()->user()->id;
                $credits_add->stock_id = $request->balance_stock_id;
                $credits_add->definitions_id = 7;
                $credits_add->balance_type = "credits";
                $credits_add->card_id = $request->plus_card_id;
                $credits_add->unit_price = (float) str_replace(",", ".", str_replace(",", ".", str_replace(["₺", "."], "", $request->devir_unit_price)));
                $credits_add->commission =(float) str_replace(",", ".", str_replace(",", ".", str_replace(["₺", "."], "", $request->devir_commission)));
                $credits_add->credits_amount =(float) str_replace(",", ".", str_replace(",", ".", str_replace(["₺", "."], "", $request->devir_credits_amount))) ;
                $credits_add->credi = $request->devir_unit_quantity;
                $credits_add->puan_branche_id = $request->devir_valid_branch;
                $credits_add->odenen_kdv_dahil_fiyat =(float) str_replace(",", ".", str_replace(",", ".", str_replace(["₺", "."], "", ($request->devir_payment_amount ?? 0))));
                $credits_add->payment_type = $request->devir_payment_type;
                $credits_add->valid_date = !empty($request->devir_valid_date) ? $request->devir_valid_date : null;
                $credits_add->case = 0;
//                $credits_add->devir_miktar = $request->devir_unit_quantity;
//                $credits_add->devir_tarih = $request->devir_tarih;
                $credits_add->devir_aciklama = $request->devir_aciklama ?? '';
                $credits_add->save();
                if(!empty($request->return_url)){
                    return redirect($request->return_url)->with('success','Bakiye Yüklendi');
                }else{
                    return redirect()->back()->with('success','Bakiye Yüklendi');
                }
            }
            elseif ($request->balance_type == 'devir_points'){
                if(empty($request->devir_point_branche))
                    return redirect()->back()->with('error','Puan Geçerlilik Şube Zorunludur.');
                if(empty($request->devir_point_quantity))
                    return redirect()->back()->with('error','Puan Zorunludur.');

                $stock_price = Stock::where('id',$request->point_stocks_id)->first();

                $credits_add = new PlusCardCrediAndPuanAdd();
                $credits_add->user_id = auth()->user()->id;
                $credits_add->balance_type = "points";
                $credits_add->card_id = $request->plus_card_id;
                $credits_add->definitions_id = 8;
                $credits_add->puan_branche_id = $request->devir_point_branche;
                $credits_add->stock_id = $request->point_stocks_id;
                $credits_add->valid_date = $request->devir_point_valid_date;
                $credits_add->puan = $request->devir_point_quantity;;
                $credits_add->unit_price = $stock_price->getStandartPrices->kdv_dahil_fiyat ?? 0;
//                $credits_add->devir_miktar = $request->devir_point_quantity;
//                $credits_add->devir_tarih = $request->devir_tarih;
                $credits_add->devir_aciklama = $request->devir_aciklama ?? '';
                $credits_add->save();
                if(!empty($request->return_url)){
                    return redirect($request->return_url)->with('success','Bakiye Yüklendi');
                }else{
                    return redirect()->back()->with('success','Bakiye Yüklendi');
                }
            }
            else{
                if(empty($request->point_branche))
                    return redirect()->back()->with('error','Puan Geçerlilik Şube Zorunludur.');
                if(empty($request->plus_cards_definitions_point))
                    return redirect()->back()->with('error','Kredi Tipi Zorunludur.');
                if(empty($request->point_quantity))
                    return redirect()->back()->with('error','Puan Zorunludur.');

                $stock_price = Stock::where('id',$request->point_stocks_id)->first();

                $credits_add = new PlusCardCrediAndPuanAdd();
                $credits_add->user_id = auth()->user()->id;
                $credits_add->balance_type = $request->balance_type;
                $credits_add->card_id = $request->plus_card_id;
                $credits_add->definitions_id = $request->plus_cards_definitions_point;
                $credits_add->puan_branche_id = $request->point_branche;
                $credits_add->stock_id = $request->point_stocks_id;
                $credits_add->valid_date = $request->point_valid_date;
                $credits_add->puan = $request->point_quantity;;
                $credits_add->unit_price = $stock_price->getStandartPrices->kdv_dahil_fiyat ?? 0;
                $credits_add->save();
                if(!empty($request->return_url)){
                    return redirect($request->return_url)->with('success','Bakiye Yüklendi');
                }else{
                    return redirect()->back()->with('success','Bakiye Yüklendi');
                }
            }
        }else{
            return redirect()->back()->with('error','Bir Hata Oluştu Tekrar Deneyiniz!');
        }
    }

    public function plusCardAgreement($id){
        $current_time = Carbon::now();
        $ten_minutes_ago = $current_time->subMinutes(10);
        $agreement = PlusCardAgreement::where('updated_at', '>=', $ten_minutes_ago)
            ->where('delayed_id',$id)
            ->first();
        if(!empty($agreement)){
            return view('pages.customer_plus_card_agreement',compact('agreement','id'));
        }else
        {
            $type = "customer";
            return view('errors.404',compact('type'));
        }
    }

    public function plusCardCustomerExport($id){

        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['download_excel_plus_card'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');


        $plus_card = PlusCard::find($id);
        if(!empty($plus_card)){
            $plus_card_credi_and_puan_add = PlusCardCrediAndPuanAdd::search($id)->where('card_id',$id);
            $plus_card_credi_and_puan_add = $plus_card_credi_and_puan_add->orderBy('created_at','desc')->get();

            $array_result = array();
            foreach($plus_card_credi_and_puan_add as $pcc){
                $miktar = 0;
                $amount = 0;
                $name = date('d.m.Y',strtotime($pcc->created_at))." Tarihli ";
                $type = "";
                $price = 0;
                if($pcc->balance_type != "credits"){
                    $name .= $pcc->puan." Adet Puan Yükleme";
                    $miktar = $pcc->puan;
                    $type = "Hediye";
                }else{
                    $name .= $pcc->credi." Adet ".$pcc->getDefinitions->definition_name." Kredi Yükleme";
                    $miktar = $pcc->credi;
                    $amount = $pcc->credits_amount;
                    $price = $pcc->unit_price;
                    $type = "Kredi";
                }
                if($pcc->payment_type == "yeni_sistem_bakiye_aktirimi"){
                    $name.="(ESKİ SİSTEMDEN BAKİYE AKTARIMI)";
                }
                $array_result[$pcc->id] = array(
                    'id' => $pcc->id,
                    'name' => $name,
                    'buy_stock_service' =>$pcc->getUser->getBranch->kisa_ad,
                    'cari_unvan' => $pcc->getUser->unvan ?? '',
                    'belge_tarihi' => date('d.m.Y H:i:s',strtotime($pcc->created_at)),
                    'belge_no' => '',
                    'son_kullanma' => $pcc->valid_if == 1 ? date('d.m.Y',strtotime($pcc->valid_date)) : '',
                    'kupon_kodu' => !empty($pcc->getDefinitions->stock_kodu) ? $pcc->getDefinitions->stock_kodu : '',
                    'definition_name' => !empty($pcc->getDefinitions->definition_name) ? $pcc->getDefinitions->definition_name : '',
                    'ozel_bolge_kodu' =>'',
                    'kampanya_turu' => 'Standart',
                    'islem_tipi' => 'Yükleme',
                    'unit' => $miktar,
                    'price' => $price,
                    'amount' => $amount,
                    'type' => $type,
                    'yuklenen' => $pcc->credi,
                    'plaka' => '',
                    'marka' => '',
                    'model' => '',
                    'islemler' => array(),
                );

                $expertise_payment = ExpertisePayment::where('type','plus_kart')->where('plus_card_id',$id)->where('plus_card_odeme_id',$pcc->id)->orderBy('created_at','desc')->get();
                foreach($expertise_payment as $ep){
                    $array_result[$pcc->id]['islemler'][] = array(
                        'id' => $pcc->id,
                        'name' => $name,
                        'buy_stock_service' => $ep->getExpertise->getBranch->kisa_ad,
                        'cari_unvan' => $pcc->getUser->unvan ?? '',
                        'belge_tarihi' => date('d.m.Y H:i:s',strtotime($ep->created_at)),
                        'belge_no' => '',
                        'son_kullanma' => $pcc->valid_if == 1 ? date('d.m.Y',strtotime($pcc->valid_date)) : '',
                        'definition_name' => $pcc->getDefinitions->definition_name,
                        'kupon_kodu' => !empty($pcc->getDefinitions->stock_kodu) ? $pcc->getDefinitions->stock_kodu : '',
                        'ozel_bolge_kodu'=>'',
                        'kampanya_turu' => 'Standart',
                        'islem_tipi' => 'Harcama',
                        'unit' => '-1',
                        'price' => $ep->getExpertise->getPayment->getPlusCardOdeme->unit_price ?? 0,
                        'amount' => $ep->getExpertise->getPayment->getPlusCardOdeme->unit_price ?? 0,
                        'yuklenen' => '',
                        'type' => $type,
                        'plaka' => !empty($ep->getExpertise->getCar->plaka) ? $ep->getExpertise->getCar->plaka : '',
                        'marka' => !empty($ep->getExpertise->getCar->getMarka->name) ? $ep->getExpertise->getCar->getMarka->name:'',
                        'model' =>!empty($ep->getExpertise->getCar->getModel->name) ? $ep->getExpertise->getCar->getModel->name:'',
                    );
                }
            }

            if(!env('APP_LOCAL') && !empty($plus_card->getCustomer->kod) ){
                $old_data = T404KUPONTAKIP::search($plus_card->getCustomer->kod)
                    ->where('T404_hesap_UQ',$plus_card->getCustomer->kod)
                    ->where('T404_islemtipi', '2')
                    ->orderBy('T404_belgetarihi','ASC')
                    ->get();
                foreach($old_data as $data){
                    $name = date('d.m.Y',strtotime($data->T404_belgetarihi))." Tarihli ".(int)$data->T404_miktar." Adet";
                    $type = "";
                    $stok_kodu= '';
                    $stok_kodu_data = DB::connection('mysql2')->table('T203_STOKLAR')
                        ->where('T203_UQ',$data->T404_stok_UQ)->first();
                    if($data->T404_kayittipi == 1){
                        $name.=" Kredi Yüklemesi";
                        $type = "Kredi";
                    }elseif($data->T404_kayittipi == 2){
                        $name.=" Puan Yüklemesi";
                        $type = "Puan";
                    }
                    if(!empty($stok_kodu_data)){
                        $stok_kodu = $stok_kodu_data->T203_stokkodu;
                    }
                    $array_result[$data->T404_ID] = array(
                        'id' => $data->T404_ID."-)Deha Verileri",
                        'name' => $name,
                        'buy_stock_service' =>$data->getBranches->kisa_ad,
                        'cari_unvan' => !empty($data->getCari->unvan) ? $data->getCari->unvan : '',
                        'belge_tarihi' => date('d.m.Y H:i:s',strtotime($data->T404_belgetarihi)),
                        'belge_no' => $data->T404_belgeno ?? '',
                        'son_kullanma' => $data->T404_sonkullanimtarihi ?? '',
                        'definition_name' => !empty($data->getStockNew->ad) ? $data->getStockNew->ad : '',
                        'kupon_kodu' => $stok_kodu,
                        'ozel_bolge_kodu'=>$data->T404_belgeozelkodu == 1 ? 'Onaylı':'Onaysız',
                        'kampanya_turu' => 'Standart',
                        'islem_tipi' => 'Yükleme',
                        'unit' => (int)$data->T404_miktar,
                        'price' => !empty($data->T404_fiyat) ?$data->T404_fiyat : '',
                        'amount' => !empty($data->T404_tutar) ?$data->T404_tutar : '',
                        'yuklenen' => (int)$data->T404_miktar,
                        'type' => $type,
                        'plaka' => '',
                        'marka' => '',
                        'model' => '',
                        'islemler' => array(),
                    );


                    $process = T404KUPONTAKIP::search($plus_card->getCustomer->kod)
                        ->where('T404_hesap_UQ',$plus_card->getCustomer->kod)
                        ->where('T404_iliski_UQ',$data->T404_UQ)
                        ->where('T404_islemtipi','-1')
                        ->where('T404_kayittipi',$data->T404_kayittipi)
                        ->orderBy('T404_belgetarihi','desc')
                        ->get();

                    foreach($process as $p){

                        $model = '';
                        $marka = '';
                        $stok_kodu= '';
                        $stok_kodu_data = DB::connection('mysql2')->table('T203_STOKLAR')
                            ->where('T203_UQ',$data->T404_stok_UQ)->first();
                        if(!empty($stok_kodu_data)){
                            $stok_kodu = $stok_kodu_data->T203_stokkodu;
                        }
                        if(!empty($p->getSrvBaslik->t202Araclar->T202_aracgrupkodu)){
                            $model_db = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')
                                ->where('T107_kod',$p->getSrvBaslik->t202Araclar->T202_aracgrupkodu)
                                ->first();
                            if(!empty($model_db)){
                                $model = $model_db->T107_aciklama;
                            }

                            $marka_kod = substr($p->getSrvBaslik->t202Araclar->T202_aracgrupkodu, 0, -4) . "0000";
                            $marka_db = DB::connection('mysql2')->table('T107_GRUPKIRILIMLI')
                                ->where('T107_kod',$marka_kod)
                                ->first();
                            if(!empty($marka_db)){
                                $marka = $marka_db->T107_aciklama;
                            }
                        }

                        $description = date('d.m.Y',strtotime($data->T404_belgetarihi))." Tarihli Harcama";

                        $array_result[$data->T404_ID]['islemler'][] = array(
                            'id' => $data->T404_ID."-)Deha Verileri",
                            'name' => $description,
                            'buy_stock_service' => $p->getBranches->kisa_ad,
                            'cari_unvan' => !empty($p->getCari->unvan) ? $p->getCari->unvan : '',
                            'belge_tarihi' => date('d.m.Y H:i:s',strtotime($p->T404_belgetarihi)),
                            'belge_no' => $p->T404_belgeno ?? '',
                            'son_kullanma' => $data->T404_sonkullanimtarihi ?? '',
                            'definition_name' => !empty($p->getStockNew->ad) ? $p->getStockNew->ad : '',
                            'kupon_kodu' =>$stok_kodu,
                            'ozel_bolge_kodu'=> $p->T404_belgeozelkodu == 1 ? 'Onaylı':'Onaysız',
                            'kampanya_turu' => 'Standart',
                            'islem_tipi' => 'Harcama',
                            'unit' => '-1',
                            'amount' => '',
                            'price' =>!empty($p->T404_fiyat) ? $p->T404_fiyat : '',
                            'yuklenen' => '',
                            'type' => $type,
                            'plaka' => $p->getSrvBaslik->t202Araclar->T202_plakano ?? '',
                            'marka' => $marka,
                            'model' => $model,
                        );
                    }

                }
            }
            $plus_card_name = $plus_card->getCustomer->unvan." ".date("d.m.Y H:i:s");
            return Excel::download(new PlusCardBalance($array_result), $plus_card_name.'.xlsx');

        }else{
            return response()->back()->with('error','Bir Hata Oluştu!');
        }

    }
    public function export(){
        $authUser = auth()->user();

        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['download_excel_plus_card'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $customer_name = $_GET['customer_name'] ?? '';
        $customer_telephone = $_GET['customer_telephone'] ?? '';
        $customer_code = $_GET['customer_code'] ?? '';
        $customer_code_old = $_GET['customer_code_old'] ?? '';
        $card_no = $_GET['card_no'] ?? '';
        $branch_id = $_GET['branch_id'] ?? '';
        $start_date = isset($_GET['start_date']) && $_GET['start_date'] != '' ? $_GET['start_date'] : date('2024-01-01');
        $end_date = isset($_GET['end_date']) && $_GET['end_date'] != '' ? $_GET['end_date'] : now()->format('Y-m-d') ;
        $telephone = $authUser->telephone ?? '';
        $random_uuid = rand(0000000,9999999);
        $type = $_GET['type'] ?? 1;
        PlusCardExcel::dispatch($customer_name,$customer_telephone,$customer_code,$customer_code_old,$card_no,$branch_id,$start_date,$end_date,$random_uuid,$telephone,$type);



        return redirect()->back()->with('success','Excel İndirme kodu sms olarak gönderilecektir.');
    }

    public function setPlusCardSystemID(Request $request)
    {
        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['edit_plus_card'])->first())
            return response()->json(['success'=>'false','message'=>'Yetkiniz Yok']);

        $plusCard = PlusCard::where('id',$request->plus_card_id)->first();

        // Check if the system_id already exists and not itself
        $existingCard = PlusCard::where('system_id', $request->value)->where('id', '!=', $plusCard->id)->first();
        if ($existingCard) {
            return response()->json(['success' => 'false', 'message' => 'Bu Sistem ID zaten kullanımda!']);
        }

        if ($plusCard){
            $plusCard->system_id = $request->value;
            $plusCard->save();
        }

        return response()->json(['success'=>'true','message'=>'Kayıt Güncellendi']);
    }

    public function removePointOrCredits(Request $request)
    {
        $addBalance = PlusCardCrediAndPuanAdd::where('id',$request->id)->first();
        if (!$addBalance)
            return response()->json(['success'=>'false','message'=>'İşlem Bulunamadı!','balance'=>'-']);

        $balance = $addBalance->credi > 0 ? $addBalance->credi : $addBalance->puan;
        if ($balance == 0)
            return response()->json(['success'=>'false','message'=>'Silinecek Bakiye Bulunamadı!','balance'=>$balance]);

        $getRemoves = PlusCardCrediAndPuanRemove::where('plus_card_credi_and_puan_add_id',$request->id)->get();
        foreach ($getRemoves as $getRemove){
            $balance -= $getRemove->amount;
        }

        $balance -= $addBalance->getUsages->count();

        if ($balance < $request->amount){
            return response()->json(['success'=>'false','message'=>'En Fazla '.$balance.' bakiye silebilirsiniz!','balance'=>$balance]);
        }

        $removeBalance = new PlusCardCrediAndPuanRemove();
        $removeBalance->user_id = auth()->user()->id;
        $removeBalance->plus_card_credi_and_puan_add_id = $request->id;
        $removeBalance->amount = $request->amount;
        $removeBalance->save();

        return response()->json(['success'=>'true','message'=>$request->amount . ' bakiye düşüldü.','balance'=>$balance]);
    }

    public function plusCardReport()
    {
        $branches = Branch::where('status',1)->select('kisa_ad','id')->get();
        foreach ($branches as $branch){
            $branch['toplam'] = PlusCard::where('branch_id',$branch->id)->count();
            $branch['kullanilan'] = PlusCard::where('branch_id',$branch->id)->where('customer_id','!=',null)->count();
        }
        return view('pages.plus_card.report',['branches'=>$branches]);
    }

    public function reportExport()
    {
        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['download_excel_plus_card'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $random_uuid = rand(0000000,9999999);
        $telephone = Auth()->user()->telephone ?? '';
        PlusCardReportExportJob::dispatch($random_uuid,$telephone);
        return redirect()->back()->with('success','Excel İndirme kodu sms olarak gönderilecektir.');
    }

    public function checkDefinitionPrices(Request $request)
    {
        $prices = PlusCardDefinitionPrice::where('definition_id',$request->id)->where('min_count','<=',$request->count)->orderBy('min_count','desc')->first();

        if ($prices){
            // Plus card campaign
            $campaignId = $request->input('campaign_id');
            $campaign = PlusCardCampaign::find($campaignId);

            if (!empty($campaignId) && $campaign) {
                $prices->price = $campaign->applyDiscount($prices->price);
            }

            return response()->json(['success'=>'true','price'=>$prices->price]);
        }else{
            return response()->json(['success'=>'false']);
        }
    }

    public function plusCardPoint()
    {
        $items = DB::table('plus_cards_puan')->get();
        return view('pages.plus_card_points',['items'=>$items]);
    }

    public function plusCardPointUpdate(Request $request)
    {
        DB::table('plus_cards_puan')->where('id',$request->id)->update([
            'aciklama'=>$request->aciklama,
            'varyok'=>$request->varyok,
        ]);

        return response()->json(['success'=>'true'],200);
    }


    /**
     * Select Campaign
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function selectCampaign(Request $request, PlusCardCampaignService $campaignService)
    {
        $plusCardId = $request->input('plus_card_id');
        $campaignId = $request->input('campaign_id');
        $definitionId = $request->input('definition_id');
        $quantity = $request->input('quantity');

        $campaign = PlusCardCampaign::find($campaignId);

        if (!$campaign) {
            return response()->json(['success' => false, 'message' => 'Kampanya bulunamadı'], 200);
        }

        $plusCardDefinition = PlusCardsDefinitions::find($definitionId);

        if (!$plusCardDefinition) {
            return response()->json(['success' => false, 'message' => 'Kredi tipi seçilmedi!'], 200);
        }

        if (empty($quantity)) {
            $quantity = $plusCardDefinition->unit_quantity;
        }

        $unitPrice = $plusCardDefinition->unit_price;
        $commissionRate = $plusCardDefinition->commission;
        $invoiceCode = $plusCardDefinition->definition_invoice_code;
        $stockId = $plusCardDefinition->stock_id;
        $creditsAmount = $unitPrice * $quantity;
        $commissionPrice = ($creditsAmount * $commissionRate) / 100;

        $result = $campaignService->applyDiscount($campaignId, $plusCardId, $creditsAmount, null, false);

        if ($result['success']) {
            $creditsAmount = $result['discounted_amount'];
            $unitPrice = $campaign->applyDiscount($unitPrice);
            $commissionPrice = ($creditsAmount * $commissionRate) / 100;

            $success = $result['success'];
            $message = $result['message'];
        } else {
            $success = $result['success'];
            $message = $result['message'];
        }

        return response()->json([
            'success' => $success,
            'message' => $message,
            'campaignId' => $campaignId,
            'discountedAmount' => [
                'unitPrice' => $unitPrice,
                'creditsAmount' => $creditsAmount,
                'commissionPrice' => $commissionPrice,
            ]
        ]);
    }
}
