<?php

namespace App\Http\Controllers;

use App\Jobs\QueryExportJob;
use App\Models\QueryLogExtra;
use App\Models\Setting;
use App\Models\ZoneBranch;
use Carbon\Carbon;
use Illuminate\Http\Request;
use App\Models\QueryLog;

class QueryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $startDate = isset($request['start_date']) && $request['start_date'] != '' ? Carbon::parse($request['start_date'])->startOfDay() : now()->startOfDay();
        $endDate = isset($request['end_date']) && $request['end_date'] != '' ? Carbon::parse($request['end_date'])->endOfDay() : now()->endOfDay();

        $filters['startDate'] = $startDate->format('Y-m-d');
        $filters['endDate'] = $endDate->format('Y-m-d');

//        $queries = QueryLog::with("ekspertiz")->get();
        return view('pages.query.index', compact('filters'));
    }

    public function indexAjax(Request $request)
    {
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        $limit = $request->input('length');
        $start = $request->input('start');
        $startDate = isset($request['start_date']) && $request['start_date'] != '' ? Carbon::parse($request['start_date'])->startOfDay() : now()->startOfDay();
        $endDate = isset($request['end_date']) && $request['end_date'] != '' ? Carbon::parse($request['end_date'])->endOfDay() : now()->endOfDay();

        $queries = QueryLog::query();
        $queries = $queries->whereDate('created_at','>=',$startDate);

        $queries = $queries->whereDate('created_at','<=',$endDate);
        if (isset($request->unvan) && $request->unvan != '')
            $queries = $queries->whereHas('ekspertiz',function ($expertise) use ($request){
                return $expertise->whereHas('getAlici',function ($alici) use ($request){
                    return $alici->where('unvan','like',"%{$request->unvan}%")
                        ->orWhere('ad','like',"%{$request->unvan}%")
                        ->orWhere('soyad','like',"%{$request->unvan}%");
                });
            });
        if (isset($request->plaka) && $request->plaka != ''){
            $request->plaka = str_replace(' ','',$request->plaka);
            $queries = $queries->whereHas('ekspertiz',function ($expertise) use ($request){
                return $expertise->whereHas('getCar',function ($car) use ($request){
                    return $car->where('plaka','like','%'.$request->plaka.'%');
                });
            });
        }
        if (isset($request->sase_no) && $request->sase_no != ''){
            $request->sase_no = str_replace(' ','',$request->sase_no);
            $queries = $queries->whereHas('ekspertiz',function ($expertise) use ($request){
                return $expertise->whereHas('getCar',function ($car) use ($request){
                    return $car->where('sase_no','like','%'.$request->sase_no.'%');
                });
            });
        }
        if (isset($request->belge_no) && $request->belge_no != ''){
            $queries = $queries->whereHas('ekspertiz',function ($expertise) use ($request){
               return $expertise->where('belge_no','like','%'.$request->belge_no.'%');
            });
        }
        if (isset($request->belge_no) && $request->belge_no != ''){
            $queries = $queries->whereHas('ekspertiz',function ($expertise) use ($request){
                return $expertise->where('belge_no','like','%'.$request->belge_no.'%');
            });
        }

        if (isset($request['branch_id']) && $request['branch_id'] != 0 && in_array($request['branch_id'],$authUserBranchIds)){
            $queries = $queries->whereHas('ekspertiz',function ($expertise) use ($request){
                return $expertise->where('branch_id',$request->branch_id);
            });
        }else{
            $queries = $queries->whereHas('ekspertiz',function ($expertise) use ($authUserBranchIds){
                return $expertise->whereIn('branch_id',$authUserBranchIds);
            });
        }

        $fields = ['hasar','kilometre', 'borc', 'detail', 'degisen', 'ruhsat'];

        foreach ($fields as $field) {
            if (isset($request->$field) && $request->$field != '') {
                if ($request->$field == 1) {
                    $queries = $queries->whereNotNull($field);
                } elseif ($request->$field == 0) {
                    $queries = $queries->whereNull($field);
                }
            }
            $fieldFirma = $field . '_firma';
            if (isset($request->$fieldFirma) && $request->$fieldFirma != '') {
                if ($request->$fieldFirma == 'otosorgu.com') {
                    $queries = $queries->whereNot($field, 'like', "%arabasorgula.com%");
                } elseif ($request->$fieldFirma == 'arabasorgula.com') {
                    $queries = $queries->where($field, 'like', "%arabasorgula.com%");
                }
            }
        }



        $total = $queries->count();
        $queries = $queries->offset($start)
            ->limit($limit)->orderBy('id','desc')->get();
        $queries = $queries->map(function ($query) use ($authUser) {
            $extraQueries = QueryLogExtra::where('expertise_uuid',$query->uuid)->get();
            $price = 0;
            if($query->hasar == null)
                $hasar = '-';
            else{
                if (!empty($query->hasar_ucret))
                    $fiyat = $query->hasar_ucret + $query->hasar_komisyon;
                else
                    $fiyat = (json_decode($query->hasar)->data->amount ?? 0) + $query->hasar_komisyon;

                $fiyat += $extraQueries->where('type','hasar')->sum(function ($extra){
                    return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                });

                if (isset(json_decode($query->hasar,true)['from']) && json_decode($query->hasar,true)['from'] == "arabasorgula.com"){
                    $bgColor = '#e2626b';
                }else{
                    $bgColor = '#fecc16';
                }
                if ($authUser->type != 'admin')
                    $bgColor = 'transparent';
                $hasar = "<span style='background-color: {$bgColor}'>".$fiyat.'₺'."</span>";
                $price += $fiyat;
            }
            if($query->kilometre == null)
                $kilometre = '-';
            else{
                if (!empty($query->kilometre_ucret))
                    $fiyat = $query->kilometre_ucret + $query->kilometre_komisyon;
                else
                    $fiyat = (json_decode($query->kilometre)->data->amount ?? 0) + $query->kilometre_komisyon;

                $fiyat += $extraQueries->where('type','kilometre')->sum(function ($extra){
                    return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                });

                if (isset(json_decode($query->kilometre)->from) && json_decode($query->kilometre)->from == "arabasorgula.com"){
                    $bgColor = '#e2626b';
                }else{
                    $bgColor = '#fecc16';
                }
                if ($authUser->type != 'admin')
                    $bgColor = 'transparent';
                $kilometre = "<span style='background-color: {$bgColor}'>".$fiyat.'₺'."</span>";
                $price += $fiyat;
            }
            if($query->borc == null)
                $borc = '-';
            else{
                if (!empty($query->borc_ucret))
                    $fiyat = $query->borc_ucret + $query->borc_komisyon;
                else
                    $fiyat = (json_decode($query->borc)->data->amount ?? 0) + $query->borc_komisyon;

                $fiyat += $extraQueries->where('type','borc')->sum(function ($extra){
                    return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                });

                if (isset(json_decode($query->borc)->from) && json_decode($query->borc)->from == "arabasorgula.com"){
                    $bgColor = '#e2626b';
                }else{
                    $bgColor = '#fecc16';
                }
                if ($authUser->type != 'admin')
                    $bgColor = 'transparent';
                $borc = "<span style='background-color: {$bgColor}'>".$fiyat.'₺'."</span>";
                $price += $fiyat;
            }
            if($query->detail == null)
                $detail = '-';
            else{
                if (!empty($query->detail_ucret))
                    $fiyat = $query->detail_ucret + $query->detail_komisyon;
                else
                    $fiyat = (json_decode($query->detail)->data->amount ?? 0) + $query->detail_komisyon;

                $fiyat += $extraQueries->where('type','detail')->sum(function ($extra){
                    return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                });

                if (isset(json_decode($query->detail)->from) && json_decode($query->detail)->from == "arabasorgula.com"){
                    $bgColor = '#e2626b';
                }else{
                    $bgColor = '#fecc16';
                }
                if ($authUser->type != 'admin')
                    $bgColor = 'transparent';
                $detail = "<span style='background-color: {$bgColor}'>".$fiyat.'₺'."</span>";

                $price += $fiyat;
            }
            if($query->degisen == null)
                $degisen = '-';
            else{
                $degisenler = json_decode($query->degisen);

                if (!is_array($degisenler)){
                    if (!empty($query->degisen_ucret))
                        $fiyat = $query->degisen_ucret + $query->degisen_komisyon;
                    else
                        $fiyat = ($degisenler->data->amount ?? 0) + $query->degisen_komisyon;

                    $fiyat += $extraQueries->where('type','degisen')->sum(function ($extra){
                        return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                    });

                    if (isset($degisenler->from) && $degisenler->from == "arabasorgula.com"){
                        $bgColor = '#e2626b';
                    }else{
                        $bgColor = '#fecc16';
                    }
                    if ($authUser->type != 'admin')
                        $bgColor = 'transparent';
                    $degisen = "<span style='background-color: {$bgColor}'>".$fiyat.'₺'."</span>";
                    $price += $fiyat;
                }else{

                    $fiyat = 0;
                    foreach ($degisenler as $degis){
                        $fiyat += $degis->data->amount ?? 0;


                    }
                    $fiyat += $query->degisen_komisyon;
                    $fiyat += $extraQueries->where('type','degisen')->sum(function ($extra){
                        return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                    });
                    $price += $fiyat;

                    if (isset($degisenler[0]->from) && $degisenler[0]->from == "arabasorgula.com"){
                        $bgColor = '#e2626b';
                    }else{
                        $bgColor = '#fecc16';
                    }
                    if ($authUser->type != 'admin')
                        $bgColor = 'transparent';
                    $degisen = "<span style='background-color: {$bgColor}'>".$fiyat.'₺'."</span>";
                }

            }
            if($query->ruhsat == null)
                $ruhsat = '-';
            else{
                if (!empty($query->ruhsat_ucret))
                    $fiyat = $query->ruhsat_ucret + $query->ruhsat_komisyon;
                else
                    $fiyat = (json_decode($query->ruhsat)->data->amount ?? 0) + $query->ruhsat_komisyon;
                $fiyat += $extraQueries->where('type','ruhsat')->sum(function ($extra){
                    return ($extra->amount ?? 0) + ($extra->commission ?? 0);
                });
                if (isset(json_decode($query->ruhsat)->from) && json_decode($query->ruhsat)->from == "arabasorgula.com"){
                    $bgColor = '#e2626b';
                }else{
                    $bgColor = '#fecc16';
                }
                if ($authUser->type != 'admin')
                    $bgColor = 'transparent';
                $ruhsat = "<span style='background-color: {$bgColor}'>".$fiyat.'₺'."</span>";
                $price += $fiyat;
            }
           return [
             'alici' => $query->ekspertiz->getCari->unvan ?? "",
             'plaka' => $query->ekspertiz->getCar->plaka ?? "",
             'sase_no' => $query->ekspertiz->getCar->sase_no ?? "",
             'belge_no' => $query->ekspertiz->belge_no ?? "" ,
             'branch_name' => $query->ekspertiz->getBranch->kisa_ad ?? '',
             'maliyet' => $price.'₺',
             'hasar' => $hasar,
             'kilometre' => $kilometre,
             'detail' => $detail,
             'degisen' => $degisen,
             'ruhsat' => $ruhsat,
             'borc' => $borc,
             'tarih' => $query->updated_at->format("d.m.Y H:i"),
             'islem' => '<a class="btn btn-primary btn-sm" href="'.url("/expertise-details?type=query&expertise_id=".$query->uuid).'">Görüntüle</a> <a class="btn btn-primary btn-sm" href="'.url("/hasar-raporu/".$query->uuid).'">Raporu Aç</a>',
           ];
        });

        $filters['startDate'] = $startDate->format('Y-m-d');
        $filters['endDate'] = $endDate->format('Y-m-d');

        $json_data = array(
            "draw"            => intval($request->input('draw')),
            "recordsTotal"    => intval($total),
            "recordsFiltered" => intval($total),
            "data"            => $queries,
            'filters'          => $filters,
        );
        echo  json_encode($json_data);
    }

    public function export()
    {
        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['download_excel_expertise'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $random_uuid = rand(0000000,9999999);
        $telephone = Auth()->user()->telephone ?? '';
        QueryExportJob::dispatch($random_uuid,$telephone,$_GET['unvan'],$_GET['plaka'],$_GET['sase_no'],$_GET['belge_no'],$_GET['branch_id'],$_GET['hasar'],$_GET['kilometre'],$_GET['borc'],$_GET['detail'],$_GET['degisen'],$_GET['ruhsat'],$_GET['start_date'],$_GET['end_date']);
        return redirect()->back()->with('success','Excel İndirme kodu sms olarak gönderilecektir.');
    }
}
