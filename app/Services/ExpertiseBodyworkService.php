<?php

namespace App\Services;

use App\Models\Expertise;
use App\Models\ExpertiseBodywork;

/**
 * Class ExpertiseBodyworkService
 */
class ExpertiseBodyworkService
{
    /**
     * Get expert controls
     *
     * @param Expertise $expertise
     * @return array
     */
    public function getExpertControls(Expertise $expertise): array
    {
        $controls = [];

        $savedControls = ExpertiseBodywork::where('expertise_id', $expertise->id)->get()->keyBy('key');

        // Generate expert controls
        foreach (__('arrays.bodyworks') as $controlKey => $controlTitle) {
            $savedControl = $savedControls->get($controlKey);

            $controls[$controlKey] = [
                'orijinal' => $savedControl?->orijinal ?? 0,
                'boyali' => $savedControl?->boyali ?? 0,
                'degisen' => $savedControl?->degisen ?? 0,
                'duz' => $savedControl?->duz ?? 0,
                'note' => $savedControl?->note ?? '',
                'title' => $controlTitle
            ];
        }

        return $controls;
    }

    /**
     * Get template coordinates
     *
     * @return array $coordinates
     */
    public function getTemplateCoordinates($vehicleType = 4)
    {
        $coordinates = [
            // vehicle right side
            'sag_on_camurluk'   => [
                'key' => 'sag_on_camurluk',
                'name' => 'Sağ Ön Çamurluk',
                'coordinates' => [
                    'orijinal' => ['x' => '485', 'y' => '120', 'color' => ''],
                    'boyali'  => ['x' => '485', 'y' => '140', 'color' => ''],
                    'degisen' => ['x' => '485', 'y' => '160', 'color' => ''],
                    'duz'     => ['x' => '485', 'y' => '180', 'color' => '']
                ]
            ],
            'sag_on_kapi'       => [
                'key' => 'sag_on_kapi',
                'name' => 'Sağ Ön Kapı',
                'coordinates' => [
                    'orijinal' => ['x' => '510', 'y' => '265', 'color' => ''],
                    'boyali'  => ['x' => '510', 'y' => '285', 'color' => ''],
                    'degisen' => ['x' => '510', 'y' => '305', 'color' => ''],
                    'duz'     => ['x' => '510', 'y' => '325', 'color' => '']
                ]
            ],
            'sag_arka_kapi'     => [
                'key' => 'sag_arka_kapi',
                'name' => 'Sağ Arka Kapı',
                'coordinates' => [
                    'orijinal' => ['x' => '510', 'y' => '400', 'color' => ''],
                    'boyali'  => ['x' => '510', 'y' => '420', 'color' => ''],
                    'degisen' => ['x' => '510', 'y' => '440', 'color' => ''],
                    'duz'     => ['x' => '510', 'y' => '460', 'color' => '']
                ]
            ],
            'sag_arka_camurluk' => [
                'key' => 'sag_arka_camurluk',
                'name' => 'Sağ Arka Çamurluk',
                'coordinates' => [
                    'orijinal' => ['x' => '470', 'y' => '530', 'color' => ''],
                    'boyali'  => ['x' => '470', 'y' => '550', 'color' => ''],
                    'degisen' => ['x' => '470', 'y' => '570', 'color' => ''],
                    'duz'     => ['x' => '470', 'y' => '590', 'color' => '']
                ]
            ],
            // vehicle left side
            'sol_on_camurluk'   => [
                'key' => 'sol_on_camurluk',
                'name' => 'Sol Ön Çamurluk',
                'coordinates' => [
                    'orijinal' => ['x' => '115', 'y' => '120', 'color' => ''],
                    'boyali'  => ['x' => '115', 'y' => '140', 'color' => ''],
                    'degisen' => ['x' => '115', 'y' => '160', 'color' => ''],
                    'duz'     => ['x' => '115', 'y' => '180', 'color' => '']
                ]
            ],
            'sol_on_kapi'       => [
                'key' => 'sol_on_kapi',
                'name' => 'Sol Ön Kapı',
                'coordinates' => [
                    'orijinal' => ['x' => '90', 'y' => '265', 'color' => ''],
                    'boyali'  => ['x' => '90', 'y' => '285', 'color' => ''],
                    'degisen' => ['x' => '90', 'y' => '305', 'color' => ''],
                    'duz'     => ['x' => '90', 'y' => '325', 'color' => '']
                ]
            ],
            'sol_arka_kapi'     => [
                'key' => 'sol_arka_kapi',
                'name' => 'Sol Arka Kapı',
                'coordinates' => [
                    'orijinal' => ['x' => '90', 'y' => '400', 'color' => ''],
                    'boyali'  => ['x' => '90', 'y' => '420', 'color' => ''],
                    'degisen' => ['x' => '90', 'y' => '440', 'color' => ''],
                    'duz'     => ['x' => '90', 'y' => '460', 'color' => '']
                ]
            ],
            'sol_arka_camurluk' => [
                'key' => 'sol_arka_camurluk',
                'name' => 'Sol Arka Çamurluk',
                'coordinates' => [
                    'orijinal' => ['x' => '130', 'y' => '530', 'color' => ''],
                    'boyali'  => ['x' => '130', 'y' => '550', 'color' => ''],
                    'degisen' => ['x' => '130', 'y' => '570', 'color' => ''],
                    'duz'     => ['x' => '130', 'y' => '590', 'color' => '']
                ]
            ],
            // vehicle front side
            // 'on_panel'          => [
            //     'key' => 'on_panel',
            //     'name' => 'Ön Panel',
            //     'coordinates' => [
            //         'orijinal' => ['x' => '300', 'y' => '20', 'color' => ''],
            //         'boyali'  => ['x' => '300', 'y' => '20', 'color' => ''],
            //         'degisen' => ['x' => '300', 'y' => '20', 'color' => ''],
            //         'duz'     => ['x' => '300', 'y' => '20', 'color' => '']
            //     ]
            // ],
            'motor_kaputu'      => [
                'key' => 'motor_kaputu',
                'name' => 'Motor Kaputu',
                'coordinates' => [
                    'orijinal' => ['x' => '300', 'y' => '60', 'color' => ''],
                    'boyali'  => ['x' => '300', 'y' => '80', 'color' => ''],
                    'degisen' => ['x' => '300', 'y' => '100', 'color' => ''],
                    'duz'     => ['x' => '300', 'y' => '120', 'color' => '']
                ]
            ],
            // vehicle back side
            // 'arka_panel'        => [
            //     'key' => 'arka_panel',
            //     'name' => 'Arka Panel',
            //     'coordinates' => [
            //         'orijinal' => ['x' => '300', 'y' => '640', 'color' => ''],
            //         'boyali'  => ['x' => '300', 'y' => '640', 'color' => ''],
            //         'degisen' => ['x' => '300', 'y' => '640', 'color' => ''],
            //         'duz'     => ['x' => '300', 'y' => '640', 'color' => '']
            //     ]
            // ],
            // 'bagaj_havuz_saci'  => [
            //     'key' => 'bagaj_havuz_saci',
            //     'name' => 'Bagaj Havuz Sacı',
            //     'coordinates' => [
            //         'orijinal' => ['x' => '300', 'y' => '580', 'color' => ''],
            //         'boyali'  => ['x' => '300', 'y' => '580', 'color' => ''],
            //         'degisen' => ['x' => '300', 'y' => '580', 'color' => ''],
            //         'duz'     => ['x' => '300', 'y' => '580', 'color' => '']
            //     ]
            // ],
            'bagaj_kapagi'      => [
                'key' => 'bagaj_kapagi',
                'name' => 'Bagaj Kapağı',
                'coordinates' => [
                    'orijinal' => ['x' => '300', 'y' => '560', 'color' => ''],
                    'boyali'  => ['x' => '300', 'y' => '580', 'color' => ''],
                    'degisen' => ['x' => '300', 'y' => '600', 'color' => ''],
                    'duz'     => ['x' => '300', 'y' => '620', 'color' => '']
                ]
            ],
            // vehicle top side
            'tavan' => [
                'key' => 'tavan',
                'name' => 'Tavan',
                'coordinates' => [
                    'orijinal' => ['x' => '300', 'y' => '360', 'color' => ''],
                    'boyali'  => ['x' => '300', 'y' => '380', 'color' => ''],
                    'degisen' => ['x' => '300', 'y' => '400', 'color' => ''],
                    'duz'     => ['x' => '300', 'y' => '420', 'color' => '']
                ]
            ],
            'sag_marsbiyel' => [
                'key' => 'sag_marsbiyel',
                'name' => 'Sağ Marşbiyel',
                'coordinates' => [
                    'orijinal' => ['x' => '555', 'y' => '315', 'color' => ''],
                    'boyali'  => ['x' => '555', 'y' => '335', 'color' => ''],
                    'degisen' => ['x' => '555', 'y' => '355', 'color' => ''],
                    'duz'     => ['x' => '555', 'y' => '375', 'color' => '']
                ]
            ],
            'sol_marsbiyel'     => [
                'key' => 'sol_marsbiyel',
                'name' => 'Sol Marşbiyel',
                'coordinates' => [
                    'orijinal' => ['x' => '40', 'y' => '315', 'color' => ''],
                    'boyali'  => ['x' => '40', 'y' => '335', 'color' => ''],
                    'degisen' => ['x' => '40', 'y' => '355', 'color' => ''],
                    'duz'     => ['x' => '40', 'y' => '375', 'color' => '']
                ]
            ],
            // vehicle
            // 'sag_on_direk'      => ['key' => 'sag_on_direk', 'name' => 'Sağ Ön Direk', 'x' => '300', 'y' => '100', 'color' => ''],
            // 'sag_orta_direk'    => ['key' => 'sag_orta_direk', 'name' => 'Sağ Orta Direk', 'x' => '300', 'y' => '100', 'color' => ''],
            // 'sag_tavan_diregi'  => ['key' => 'sag_tavan_diregi', 'name' => 'Sağ Tavan Direği', 'x' => '300', 'y' => '100', 'color' => ''],
            // 'sag_on_cam_diregi' => ['key' => 'sag_on_cam_diregi', 'name' => 'Sağ Ön Cam Direği', 'x' => '300', 'y' => '100', 'color' => ''],
            // 'sol_on_direk'      => ['key' => 'sol_on_direk', 'name' => 'Sol Ön Direk', 'x' => '300', 'y' => '100', 'color' => ''],
            // 'sol_orta_direk'    => ['key' => 'sol_orta_direk', 'name' => 'Sol Orta Direk', 'x' => '300', 'y' => '100', 'color' => ''],
            // 'sol_tavan_diregi'  => ['key' => 'sol_tavan_diregi', 'name' => 'Sol Tavan Direği', 'x' => '300', 'y' => '100', 'color' => ''],
            // 'sol_on_cam_diregi' => ['key' => 'sol_on_cam_diregi', 'name' => 'Sol Ön Cam Direği', 'x' => '300', 'y' => '100', 'color' => ''],
            // vehicle chassis
            // 'sag_sase'          => ['key' => 'sag_sase', 'name' => 'Sağ Şase', 'x' => '300', 'y' => '100', 'color' => ''],
            // 'sol_sase'          => ['key' => 'sol_sase', 'name' => 'Sol Şase', 'x' => '300', 'y' => '100', 'color' => ''],
            // vehicle chassis
            // 'sag_podye'         => ['key' => 'sag_podye', 'name' => 'Sağ Podye', 'x' => '300', 'y' => '100', 'color' => ''],
            // 'sol_podye'         => ['key' => 'sol_podye', 'name' => 'Sol Podye', 'x' => '300', 'y' => '100', 'color' => ''],
            // vehicle bottom side
            // 'taban_saci'        => ['key' => 'taban_saci', 'name' => 'Taban Sacı', 'x' => '300', 'y' => '100', 'color' => ''],
        ];

        switch ($vehicleType) {
            case 1:
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '540';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '180';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '540';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '200';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '540';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '220';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '540';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '240';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '60';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '180';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '60';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '200';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '60';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '220';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '60';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '240';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '470';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '490';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '510';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '530';


                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '490';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '510';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '530';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '80';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '100';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '120';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '140';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '80';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '100';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '140';

                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['y'] = '300';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['y'] = '320';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['y'] = '340';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['y'] = '360';

                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['x'] = '460';
                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['y'] = '300';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['x'] = '460';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['y'] = '320';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['x'] = '460';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['y'] = '340';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['x'] = '460';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['y'] = '360';

                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '205';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '225';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '245';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '265';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '205';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '225';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '245';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '265';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '30';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '50';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '70';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '90';

                $coordinates['bagaj_havuz_saci']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_havuz_saci']['coordinates']['orijinal']['y'] = '440';
                $coordinates['bagaj_havuz_saci']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_havuz_saci']['coordinates']['boyali']['y'] = '460';
                $coordinates['bagaj_havuz_saci']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_havuz_saci']['coordinates']['degisen']['y'] = '480';
                $coordinates['bagaj_havuz_saci']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_havuz_saci']['coordinates']['duz']['y'] = '500';

                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '590';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '610';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '630';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '650';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '300';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '260';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '300';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '280';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '300';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '300';
                $coordinates['tavan']['coordinates']['duz']['x'] = '300';
                $coordinates['tavan']['coordinates']['duz']['y'] = '320';
                break;
            case 3:
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '570';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '320';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '570';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '340';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '570';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '360';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '570';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '380';

                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '30';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '320';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '30';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '340';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '30';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '360';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '30';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '380';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '110';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '500';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '110';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '520';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '110';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '540';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '110';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '560';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '500';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '520';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '540';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '560';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '70';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '210';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '70';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '230';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '70';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '250';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '70';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '270';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '540';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '210';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '540';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '230';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '540';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '250';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '540';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '270';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '310';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '270';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '310';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '290';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '310';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '310';
                $coordinates['tavan']['coordinates']['duz']['x'] = '310';
                $coordinates['tavan']['coordinates']['duz']['y'] = '330';

                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '590';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '610';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '630';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '650';

                unset($coordinates['sol_marsbiyel']);
                unset($coordinates['sag_marsbiyel']);
                unset($coordinates['sol_arka_kapi']);
                unset($coordinates['sag_arka_kapi']);
                unset($coordinates['sol_on_kapi']);
                unset($coordinates['sag_on_kapi']);
                break;

            case 4:
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '570';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '570';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '570';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '570';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '140';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '520';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '140';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '540';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '140';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '560';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '140';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '580';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '520';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '540';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '560';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '580';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '90';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '110';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '130';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '150';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '130';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '150';

                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['y'] = '370';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['y'] = '390';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['y'] = '410';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['y'] = '430';

                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['y'] = '370';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['y'] = '390';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['y'] = '410';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['y'] = '430';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '310';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '320';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '310';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '340';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '310';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '360';
                $coordinates['tavan']['coordinates']['duz']['x'] = '310';
                $coordinates['tavan']['coordinates']['duz']['y'] = '380';
                unset($coordinates['bagaj_havuz_saci']);
                break;

            case 5:
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '315';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '335';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '355';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '375';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '315';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '335';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '355';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '375';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '535';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '555';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '575';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '595';

                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '570';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '590';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '610';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '630';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '535';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '555';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '575';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '595';
                unset($coordinates['bagaj_havuz_saci']);
                unset($coordinates['on_panel']);
                unset($coordinates['arka_panel']);
                break;

            case 6:
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '120';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '440';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '120';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '460';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '120';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '480';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '120';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '500';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '440';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '460';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '500';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '70';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '90';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '110';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '130';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '70';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '130';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '490';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '215';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '490';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '235';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '490';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '255';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '490';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '275';

                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '110';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '215';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '110';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '235';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '110';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '255';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '110';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '275';

                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['y'] = '320';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['y'] = '340';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['y'] = '360';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['y'] = '380';

                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['y'] = '320';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['y'] = '340';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['y'] = '360';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['y'] = '380';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '310';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '320';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '310';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '340';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '310';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '360';
                $coordinates['tavan']['coordinates']['duz']['x'] = '310';
                $coordinates['tavan']['coordinates']['duz']['y'] = '380';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '30';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '50';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '70';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '90';
                break;

            case 7:
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '555';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '555';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '555';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '555';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '50';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '230';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '50';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '250';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '50';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '270';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '50';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '290';


                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '195';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '215';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '235';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '255';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '195';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '215';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '235';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '255';

                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['y'] = '310';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['y'] = '330';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['y'] = '350';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['y'] = '370';

                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['y'] = '310';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['y'] = '330';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['y'] = '350';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['y'] = '370';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '50';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '70';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '90';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '110';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '300';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '320';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '300';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '340';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '300';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '360';
                $coordinates['tavan']['coordinates']['duz']['x'] = '300';
                $coordinates['tavan']['coordinates']['duz']['y'] = '380';

                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '590';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '610';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '630';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '650';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '475';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '80';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '475';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '100';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '475';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '120';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '475';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '140';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '80';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '100';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '140';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '440';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '460';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '480';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '500';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '440';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '460';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '500';
                break;
            case 8:
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '60';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '80';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '100';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '120';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '60';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '80';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '100';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '120';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '500';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '185';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '500';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '205';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '500';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '225';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '500';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '245';

                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '100';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '185';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '100';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '205';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '100';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '225';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '100';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '245';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '310';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '210';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '310';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '230';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '310';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '250';
                $coordinates['tavan']['coordinates']['duz']['x'] = '310';
                $coordinates['tavan']['coordinates']['duz']['y'] = '270';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '20';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '40';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '60';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '80';

                unset($coordinates['sol_arka_kapi']);
                unset($coordinates['sag_arka_kapi']);
                unset($coordinates['sol_arka_camurluk']);
                unset($coordinates['sag_arka_camurluk']);
                unset($coordinates['on_panel']);
                unset($coordinates['arka_panel']);
                break;
            case 9:
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '560';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '520';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '30';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '520';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '50';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '520';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '70';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '520';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '90';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '30';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '50';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '70';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '90';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '440';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '460';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '480';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '500';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '440';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '460';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '500';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '500';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '140';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '500';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '160';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '500';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '180';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '500';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '200';

                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '100';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '140';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '100';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '160';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '100';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '180';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '100';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '200';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '310';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '210';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '310';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '230';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '310';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '250';
                $coordinates['tavan']['coordinates']['duz']['x'] = '310';
                $coordinates['tavan']['coordinates']['duz']['y'] = '270';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '10';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '30';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '50';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '70';

                unset($coordinates['sol_arka_kapi']);
                unset($coordinates['sag_arka_kapi']);
                unset($coordinates['sol_marsbiyel']);
                unset($coordinates['sag_marsbiyel']);
                unset($coordinates['on_panel']);
                unset($coordinates['arka_panel']);
                break;

            case 10:
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '556';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '270';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '556';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '290';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '556';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '310';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '556';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '330';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '260';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '280';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '300';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '320';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '115';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '80';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '115';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '100';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '115';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '115';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '140';

                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '220';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '240';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '260';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '280';

                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['y'] = '340';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['y'] = '360';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['y'] = '380';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['y'] = '400';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '470';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '490';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '510';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '530';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '50';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '70';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '90';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '110';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '300';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '340';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '300';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '360';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '300';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '380';
                $coordinates['tavan']['coordinates']['duz']['x'] = '300';
                $coordinates['tavan']['coordinates']['duz']['y'] = '400';

                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '590';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '610';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '630';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '650';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '485';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '75';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '485';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '95';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '485';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '115';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '485';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '135';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '220';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '240';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '260';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '280';

                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['y'] = '340';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['y'] = '360';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['y'] = '380';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['y'] = '400';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '490';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '510';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '530';
                break;
            case 11:
                unset($coordinates['sol_arka_kapi']);
                unset($coordinates['sag_arka_kapi']);

                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '310';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '330';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '350';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '370';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '290';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '310';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '330';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '350';


                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '470';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '490';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '510';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '530';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '50';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '70';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '90';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '110';

                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '570';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '590';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '610';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '630';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '490';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '510';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '530';
                break;
            case 12:
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '570';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '570';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '570';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '570';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '240';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '260';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '280';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '300';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '520';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '20';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '520';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '40';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '520';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '60';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '520';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '80';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '20';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '40';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '60';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '80';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '440';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '460';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '480';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '500';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '440';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '460';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '500';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '310';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '320';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '310';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '340';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '310';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '360';
                $coordinates['tavan']['coordinates']['duz']['x'] = '310';
                $coordinates['tavan']['coordinates']['duz']['y'] = '380';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '60';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '80';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '100';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '120';

                unset($coordinates['sol_arka_kapi']);
                unset($coordinates['sag_arka_kapi']);
                unset($coordinates['on_panel']);
                unset($coordinates['arka_panel']);
                break;

            case 13:

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '30';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '50';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '70';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '90';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '30';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '50';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '70';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '90';

                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '170';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '190';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '210';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '230';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '170';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '190';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '210';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '230';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '310';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '230';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '310';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '250';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '310';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '270';
                $coordinates['tavan']['coordinates']['duz']['x'] = '310';
                $coordinates['tavan']['coordinates']['duz']['y'] = '290';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '10';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '30';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '50';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '70';

                unset($coordinates['sol_arka_kapi']);
                unset($coordinates['sag_arka_kapi']);
                unset($coordinates['sol_arka_camurluk']);
                unset($coordinates['sag_arka_camurluk']);
                unset($coordinates['sag_marsbiyel']);
                unset($coordinates['sol_marsbiyel']);
                break;
            case 14:
                unset($coordinates['sol_arka_kapi']);
                unset($coordinates['sag_arka_kapi']);

                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '556';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '270';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '556';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '290';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '556';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '310';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '556';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '330';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '270';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '290';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '310';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '40';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '330';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '80';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '100';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '140';

                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '220';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '240';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '260';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '280';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '125';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '400';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '125';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '420';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '125';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '440';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '125';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '460';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '50';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '70';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '90';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '110';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '300';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '320';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '300';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '340';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '300';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '360';
                $coordinates['tavan']['coordinates']['duz']['x'] = '300';
                $coordinates['tavan']['coordinates']['duz']['y'] = '380';

                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '590';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '610';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '630';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '650';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '480';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '80';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '480';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '100';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '480';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '120';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '480';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '140';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '220';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '240';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '260';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '280';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '475';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '400';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '475';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '420';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '475';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '440';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '475';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '460';
                break;

            case 15:
                unset($coordinates['sag_marsbiyel']);
                unset($coordinates['sol_marsbiyel']);

                unset($coordinates['sag_arka_kapi']);
                unset($coordinates['sol_arka_kapi']);

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '470';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '490';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '510';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '530';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '490';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '510';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '530';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '80';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '100';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '120';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '140';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '80';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '100';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '140';

                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '205';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '225';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '245';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '265';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '205';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '225';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '245';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '265';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '30';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '50';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '70';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '90';

                $coordinates['bagaj_havuz_saci']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_havuz_saci']['coordinates']['orijinal']['y'] = '440';
                $coordinates['bagaj_havuz_saci']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_havuz_saci']['coordinates']['boyali']['y'] = '460';
                $coordinates['bagaj_havuz_saci']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_havuz_saci']['coordinates']['degisen']['y'] = '480';
                $coordinates['bagaj_havuz_saci']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_havuz_saci']['coordinates']['duz']['y'] = '500';

                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '590';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '610';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '630';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '650';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '300';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '220';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '300';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '240';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '300';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '260';
                $coordinates['tavan']['coordinates']['duz']['x'] = '300';
                $coordinates['tavan']['coordinates']['duz']['y'] = '280';
                break;

            case 16:
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '50';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '70';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '90';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '490';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '110';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '70';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '50';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '110';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '110';

                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '565';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '270';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '565';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '290';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '565';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '310';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '565';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '330';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '270';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '290';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '310';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '35';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '330';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '310';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '320';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '310';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '340';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '310';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '360';
                $coordinates['tavan']['coordinates']['duz']['x'] = '310';
                $coordinates['tavan']['coordinates']['duz']['y'] = '380';


                unset($coordinates['sag_arka_kapi']);
                unset($coordinates['sol_arka_kapi']);
                unset($coordinates['bagaj_havuz_saci']);
                break;

            case 17:

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '30';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '50';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '70';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '90';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '30';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '50';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '70';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '90';

                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '170';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '190';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '210';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '230';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '170';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '190';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '210';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '230';

                $coordinates['tavan']['coordinates']['orijinal']['x'] = '310';
                $coordinates['tavan']['coordinates']['orijinal']['y'] = '220';
                $coordinates['tavan']['coordinates']['boyali']['x'] = '310';
                $coordinates['tavan']['coordinates']['boyali']['y'] = '240';
                $coordinates['tavan']['coordinates']['degisen']['x'] = '310';
                $coordinates['tavan']['coordinates']['degisen']['y'] = '260';
                $coordinates['tavan']['coordinates']['duz']['x'] = '310';
                $coordinates['tavan']['coordinates']['duz']['y'] = '280';

                $coordinates['motor_kaputu']['coordinates']['orijinal']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['orijinal']['y'] = '20';
                $coordinates['motor_kaputu']['coordinates']['boyali']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['boyali']['y'] = '40';
                $coordinates['motor_kaputu']['coordinates']['degisen']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['degisen']['y'] = '60';
                $coordinates['motor_kaputu']['coordinates']['duz']['x'] = '300';
                $coordinates['motor_kaputu']['coordinates']['duz']['y'] = '80';

                unset($coordinates['sol_arka_kapi']);
                unset($coordinates['sag_arka_kapi']);
                unset($coordinates['sol_arka_camurluk']);
                unset($coordinates['sag_arka_camurluk']);
                unset($coordinates['sag_marsbiyel']);
                unset($coordinates['sol_marsbiyel']);
                break;

            case 18:

                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['orijinal']['y'] = '315';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['boyali']['y'] = '335';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['degisen']['y'] = '355';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['x'] = '558';
                $coordinates['sag_marsbiyel']['coordinates']['duz']['y'] = '375';

                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['x'] = '50';
                $coordinates['sol_marsbiyel']['coordinates']['orijinal']['y'] = '315';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['x'] = '50';
                $coordinates['sol_marsbiyel']['coordinates']['boyali']['y'] = '335';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['x'] = '50';
                $coordinates['sol_marsbiyel']['coordinates']['degisen']['y'] = '355';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['x'] = '50';
                $coordinates['sol_marsbiyel']['coordinates']['duz']['y'] = '375';

                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['x'] = '480';
                $coordinates['sag_on_camurluk']['coordinates']['orijinal']['y'] = '30';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['x'] = '480';
                $coordinates['sag_on_camurluk']['coordinates']['boyali']['y'] = '50';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['x'] = '480';
                $coordinates['sag_on_camurluk']['coordinates']['degisen']['y'] = '70';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['x'] = '480';
                $coordinates['sag_on_camurluk']['coordinates']['duz']['y'] = '90';

                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['orijinal']['y'] = '30';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['boyali']['y'] = '50';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['degisen']['y'] = '70';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['x'] = '120';
                $coordinates['sol_on_camurluk']['coordinates']['duz']['y'] = '90';

                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['orijinal']['y'] = '460';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['boyali']['y'] = '480';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['degisen']['y'] = '500';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['x'] = '130';
                $coordinates['sol_arka_camurluk']['coordinates']['duz']['y'] = '520';

                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['orijinal']['y'] = '570';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['boyali']['y'] = '590';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['degisen']['y'] = '610';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['x'] = '300';
                $coordinates['bagaj_kapagi']['coordinates']['duz']['y'] = '630';

                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['orijinal']['y'] = '460';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['boyali']['y'] = '480';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['degisen']['y'] = '500';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['x'] = '470';
                $coordinates['sag_arka_camurluk']['coordinates']['duz']['y'] = '520';

                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['x'] = '120';
                $coordinates['sol_arka_kapi']['coordinates']['orijinal']['y'] = '330';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['x'] = '120';
                $coordinates['sol_arka_kapi']['coordinates']['boyali']['y'] = '350';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['x'] = '120';
                $coordinates['sol_arka_kapi']['coordinates']['degisen']['y'] = '370';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['x'] = '120';
                $coordinates['sol_arka_kapi']['coordinates']['duz']['y'] = '390';

                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['x'] = '490';
                $coordinates['sag_arka_kapi']['coordinates']['orijinal']['y'] = '330';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['x'] = '490';
                $coordinates['sag_arka_kapi']['coordinates']['boyali']['y'] = '350';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['x'] = '490';
                $coordinates['sag_arka_kapi']['coordinates']['degisen']['y'] = '370';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['x'] = '490';
                $coordinates['sag_arka_kapi']['coordinates']['duz']['y'] = '390';

                $coordinates['sol_on_kapi']['coordinates']['orijinal']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['orijinal']['y'] = '210';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['boyali']['y'] = '230';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['degisen']['y'] = '250';
                $coordinates['sol_on_kapi']['coordinates']['duz']['x'] = '90';
                $coordinates['sol_on_kapi']['coordinates']['duz']['y'] = '270';

                $coordinates['sag_on_kapi']['coordinates']['orijinal']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['orijinal']['y'] = '210';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['boyali']['y'] = '230';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['degisen']['y'] = '250';
                $coordinates['sag_on_kapi']['coordinates']['duz']['x'] = '510';
                $coordinates['sag_on_kapi']['coordinates']['duz']['y'] = '270';
                break;
            default:
                break;
        }

        return $coordinates;
    }

    /**
     * Get bodywork coordinates
     *
     * @return array $coordinates
     */
    public function getBodyworkCoordinates($expertise, $bodyworks)
    {
        $coordinates = $this->getTemplateCoordinates($expertise->getCar?->getCaseType?->id);

        foreach ($bodyworks as $key => $bodywork) {
            if (empty($coordinates[$key])) {
                continue;
            }

            foreach ($bodywork as $bodyworkKey => $value) {
                switch ($bodyworkKey) {
                    case 'orijinal':
                        if ($value == 1) {
                            $coordinates[$key]['coordinates'][$bodyworkKey]['color'] = 'lawngreen';
                            $coordinates[$key]['coordinates'][$bodyworkKey]['title'] = 'ORJ.';
                        } else {
                            unset($coordinates[$key]['coordinates'][$bodyworkKey]);
                        }

                        break;
                    case 'boyali':
                        if ($value == 1) {
                            $coordinates[$key]['coordinates'][$bodyworkKey]['color'] = 'yellow';
                            $coordinates[$key]['coordinates'][$bodyworkKey]['title'] = 'BOY.';
                        } else {
                            unset($coordinates[$key]['coordinates'][$bodyworkKey]);
                        }

                        break;
                    case 'degisen':
                        if ($value == 1) {
                            $coordinates[$key]['coordinates'][$bodyworkKey]['color'] = 'red';
                            $coordinates[$key]['coordinates'][$bodyworkKey]['title'] = 'DEĞ.';
                        } else {
                            unset($coordinates[$key]['coordinates'][$bodyworkKey]);
                        }

                        break;
                    case 'duz':
                        if ($value == 1) {
                            $coordinates[$key]['coordinates'][$bodyworkKey]['color'] = 'purple';
                            $coordinates[$key]['coordinates'][$bodyworkKey]['title'] = 'DÜz.';
                        } else {
                            unset($coordinates[$key]['coordinates'][$bodyworkKey]);
                        }

                        break;
                }
            }
        }

        return $coordinates;
    }
}
