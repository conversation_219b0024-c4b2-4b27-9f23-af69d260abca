<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->id();
            $table->char('uuid')->nullable();
            $table->integer('branch_id')->default(0)->nullable();
            $table->integer('customer_id')->nullable();
            $table->integer('related_user')->nullable();
            $table->string('to')->nullable();
            $table->integer('priority')->default(1)->nullable();
            $table->string('subject')->nullable();
            $table->integer('status')->default(2)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
