<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_cards', function (Blueprint $table) {
            $table->id();
            $table->string('system_id')->nullable();
            $table->string('no');
            $table->integer('branch_id')->default(0)->nullable();
            $table->integer('customer_id')->default(0)->nullable();
            $table->float("kredi",15,2)->nullable()->default(0);
            $table->float("puan",15,2)->nullable()->default(0);
            $table->date('valid_date')->default(now()->addYear());
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_cards');
    }
};
