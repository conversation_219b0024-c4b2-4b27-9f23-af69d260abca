<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('complaints', function (Blueprint $table) {
            $table->id();
            $table->string('talep_no',12);
            $table->string('ad_unvan');
            $table->string('soyad')->nullable();
            $table->string('vergi_dairesi')->nullable();
            $table->string('tc_vergi_no',11);
            $table->string('eposta');
            $table->string('telefon');
            $table->string('branch_id');
            $table->string('il_id');
            $table->string('ilce_id');
            $table->string('semt');
            $table->string('mahalle');
            $table->string('talep_nedeni')->nullable();
            $table->string('tur',50)->nullable();
            $table->text('musteri_ifadesi')->nullable();
            $table->text('yetkili_yanit')->nullable();
            $table->tinyInteger('status')->default(2);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('complaints');
    }
};
