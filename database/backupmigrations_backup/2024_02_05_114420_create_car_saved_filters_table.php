<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('car_saved_filters', function (Blueprint $table) {
            $table->id();
            $table->mediumInteger('user_id');
            $table->string('name');
            $table->string('plaka')->nullable();
            $table->string('sase_no')->nullable();
            $table->string('customer_name')->nullable();
            $table->string('branch_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('car_saved_filters');
    }
};
