<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->unsignedFloat('hasar_sorgu_komisyon_arabasorgula')->default(0)->nullable()->after('ruhsat_sorgu_komisyon');
            $table->unsignedFloat('kilometre_sorgu_komisyon_arabasorgula')->default(0)->nullable()->after('hasar_sorgu_komisyon_arabasorgula');
            $table->unsignedFloat('borc_sorgu_komisyon_arabasorgula')->default(0)->nullable()->after('kilometre_sorgu_komisyon_arabasorgula');
            $table->unsignedFloat('degisen_sorgu_komisyon_arabasorgula')->default(0)->nullable()->after('borc_sorgu_komisyon_arabasorgula');
            $table->unsignedFloat('detail_sorgu_komisyon_arabasorgula')->default(0)->nullable()->after('degisen_sorgu_komisyon_arabasorgula');
            $table->unsignedFloat('ruhsat_sorgu_komisyon_arabasorgula')->default(0)->nullable()->after('detail_sorgu_komisyon_arabasorgula');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            //
        });
    }
};
