<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plus_card_credi_and_puan_add', function (Blueprint $table) {
            $table->string('paynet_reference_no')->nullable();
            $table->string('paynet_confirmation_no')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plus_card_credi_and_puan_add', function (Blueprint $table) {
            //
        });
    }
};
