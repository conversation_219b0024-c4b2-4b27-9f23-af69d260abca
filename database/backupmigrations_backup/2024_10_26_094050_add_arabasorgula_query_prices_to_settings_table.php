<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            $table->float('hasar_sorgu_arabasorgula',15,2)->default(0)->nullable();
            $table->float('kilometre_sorgu_arabasorgula',15,2)->default(0)->nullable();
            $table->float('borc_sorgu_arabasorgula',15,2)->default(0)->nullable();
            $table->float('detail_sorgu_arabasorgula',15,2)->default(0)->nullable();
            $table->float('degisen_sorgu_arabasorgula',15,2)->default(0)->nullable();
            $table->float('ruhsat_sorgu_arabasorgula',15,2)->default(0)->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('settings', function (Blueprint $table) {
            //
        });
    }
};
