<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_report_downloads', function (Blueprint $table) {
            $table->id();
            $table->string('e_uuid',36)->comment('expertise.uuid');
            $table->unsignedBigInteger('u_id')->comment('user.id');

            $table->foreign('u_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('e_uuid')->references('uuid')->on('expertises')->onDelete('cascade');
            $table->timestamp('created_at')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_report_downloads');
    }
};
