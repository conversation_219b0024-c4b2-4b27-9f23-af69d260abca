<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contract_period_added_limits', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('contract_period_id');
            $table->unsignedBigInteger('user_id');
            $table->string('type',20);
            $table->unsignedFloat('amount',15,2);
            $table->timestamps();

            $table->foreign('contract_period_id')->references('id')->on('contract_periods');
            $table->foreign('user_id')->references('id')->on('users');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contract_period_added_limits');
    }
};
