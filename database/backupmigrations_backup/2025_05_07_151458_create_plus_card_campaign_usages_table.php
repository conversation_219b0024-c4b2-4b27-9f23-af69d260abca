<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_card_campaign_usages', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('campaign_id');
            $table->unsignedBigInteger('plus_card_id');
            $table->unsignedBigInteger('expertise_id')->nullable();
            $table->decimal('original_amount', 10, 2);
            $table->decimal('discounted_amount', 10, 2);
            $table->decimal('discount_value', 10, 2);
            $table->timestamp('used_at');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('campaign_id')->references('id')->on('plus_card_campaigns');
            $table->foreign('plus_card_id')->references('id')->on('plus_cards');
            $table->foreign('expertise_id')->references('id')->on('expertises');

            $table->index(['campaign_id', 'plus_card_id', 'used_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_card_campaign_usages');
    }
};
