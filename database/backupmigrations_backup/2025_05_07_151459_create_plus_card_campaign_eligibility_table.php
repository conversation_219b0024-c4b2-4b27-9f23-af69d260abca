<?php

// Create plus_card_campaign_eligibility table migration
namespace Database\Migrations;

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('plus_card_campaign_eligibility', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('campaign_id');
            $table->unsignedBigInteger('plus_card_id');
            $table->timestamps();

            $table->foreign('campaign_id')->references('id')->on('plus_card_campaigns')->onDelete('cascade');
            $table->foreign('plus_card_id')->references('id')->on('plus_cards')->onDelete('cascade');

            $table->unique(['campaign_id', 'plus_card_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('plus_card_campaign_eligibility');
    }
};
