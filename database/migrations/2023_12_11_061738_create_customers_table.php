<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->integer('branch_id')->nullable();
            $table->integer('customer_id')->nullable();
            $table->integer('tip_id')->nullable();
            $table->integer('grup_id')->nullable();
            $table->string('kod')->nullable();
            $table->string('hesap_adi')->nullable();
            $table->string('unvan');
            $table->string('ad')->nullable();
            $table->string('soyad')->nullable();
            $table->string('yetkili_adi')->nullable();
            $table->integer('customer_group_id')->default(0)->nullable();
            $table->string('cep')->nullable();
            $table->string('telefon')->nullable();
            $table->string('fax')->nullable();
            $table->string('eposta')->nullable();
            $table->string('web')->nullable();
            $table->string('mahalle')->nullable();
            $table->string('cadde')->nullable();
            $table->string('sokak')->nullable();
            $table->string('semt')->nullable();
            $table->tinyInteger('il_kodu')->default(1)->nullable();
            $table->smallInteger('ilce_kodu')->default(112)->nullable();
            $table->string('ulke')->nullable();
            $table->string('muhasebe_type',10)->default('alici')->nullable();
            $table->string('type',10)->nullable();
            $table->date('dogum_tarihi')->nullable();
            $table->date('evlilik_tarihi')->nullable();
            $table->date('vade_gunu')->nullable();
            $table->integer('campaign_id')->default(0)->nullable();
            $table->string('vergi_dairesi')->nullable();
            $table->string('vergi_dairesi_kodu')->nullable();
            $table->string('vergi_no')->nullable();
            $table->string('tc_no')->nullable();
            $table->string('plus_kart')->nullable();
            $table->integer('sitede_yayinla')->default(1)->nullable();
            $table->integer('garantek_uye')->default(0)->nullable();
            $table->integer('sms_gonder')->default(0)->nullable();
            $table->integer('kvkk_verified')->default(0)->nullable();
            $table->string('login_token')->nullable();
            $table->string('login_sms')->nullable();
            $table->integer('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
