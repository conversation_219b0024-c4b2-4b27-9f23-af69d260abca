<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
//        Schema::create('PLUSNOGEREKLI', function (Blueprint $table) {
//            $table->dateTime('TARIH')->nullable();
//            $table->string('HESAPKODU')->nullable();
//            $table->integer('SUBEKODU')->nullable();
//            $table->integer('T201ID')->nullable();
//        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('PLUSNOGEREKLI');
    }
};
