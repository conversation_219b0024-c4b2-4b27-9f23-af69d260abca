<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bodywork_coordinates', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('expertise_id')->index();
            $table->unsignedBigInteger('user_id')->index();
            $table->json('coordinates')->nullable();
            $table->string('image_url', 12);
            $table->unsignedSmallInteger('image_client_width')->default(695);
            $table->unsignedSmallInteger('image_client_height')->default(520);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bodywork_coordinates');
    }
};
