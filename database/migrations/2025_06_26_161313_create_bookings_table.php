<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('branch_id');
            $table->integer('customer_id');
            $table->string('telephone', 20)->nullable();
            $table->string('plaka', 17)->nullable();
            $table->string('sase_no', 17)->nullable();
            $table->date('date')->nullable();
            $table->time('hour')->nullable();
            $table->string('type', 20)->nullable()->default('normal');
            $table->tinyInteger('form_onay')->nullable()->default(1);
            $table->tinyInteger('sms_kabul')->nullable()->default(1);
            $table->tinyInteger('yol_testi')->nullable()->default(0);
            $table->tinyInteger('status')->nullable()->default(2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bookings');
    }
};
