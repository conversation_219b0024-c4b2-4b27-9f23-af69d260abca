<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branch_saved_filters', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->mediumInteger('user_id');
            $table->string('name');
            $table->string('unvan')->nullable();
            $table->string('kisa_ad')->nullable();
            $table->string('kod')->nullable();
            $table->string('il_kodu')->nullable();
            $table->string('ilce_kodu')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_saved_filters');
    }
};
