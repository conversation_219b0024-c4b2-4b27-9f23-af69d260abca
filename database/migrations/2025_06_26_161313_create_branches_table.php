<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branches', function (Blueprint $table) {
            $table->bigIncrements('id')->index('id');
            $table->string('kod', 10)->nullable()->index('kod');
            $table->string('belge_kod', 4)->nullable()->index('belge_kod');
            $table->unsignedBigInteger('category_id')->nullable();
            $table->string('kisa_ad')->nullable()->index('kisa_ad');
            $table->text('unvan')->nullable();
            $table->string('ad')->nullable();
            $table->string('soyad')->nullable();
            $table->string('mahalle')->nullable();
            $table->string('cadde')->nullable();
            $table->string('sokak')->nullable();
            $table->string('semt')->nullable();
            $table->integer('il_kodu')->nullable();
            $table->smallInteger('ilce_kodu')->nullable();
            $table->string('lat')->nullable();
            $table->string('lng')->nullable();
            $table->string('konum')->nullable();
            $table->string('telefon')->nullable();
            $table->string('whatsapp_telefon')->nullable();
            $table->string('fax')->nullable();
            $table->string('gsm')->nullable();
            $table->string('email')->nullable();
            $table->string('web')->nullable();
            $table->string('google_button_1')->nullable();
            $table->string('google_button_2')->nullable();
            $table->string('vergi_dairesi')->nullable();
            $table->string('vergi_no')->nullable();
            $table->string('tc_no')->nullable();
            $table->string('fatura_form_adi')->nullable();
            $table->string('makbuz_form_adi')->nullable();
            $table->string('cek_form_adi')->nullable();
            $table->string('senet_form_adi')->nullable();
            $table->string('logo')->nullable();
            $table->string('sozlesme')->nullable();
            $table->date('sozlesme_baslangic_tarihi')->nullable();
            $table->date('sozlesme_bitis_tarihi')->nullable();
            $table->integer('sozlesme_yil')->nullable();
            $table->string('sube_hesabi')->nullable();
            $table->string('plus_kart_satis')->nullable();
            $table->string('eksper_merkez')->nullable();
            $table->string('eksper_orani')->nullable();
            $table->integer('hasar_sorgulama')->nullable()->default(0);
            $table->integer('plus_kart_yukle')->nullable()->default(0);
            $table->date('son_fiyat_degisiklik_tarihi')->nullable();
            $table->integer('bedelsiz_eksper')->nullable();
            $table->double('max_indirim_tutari', 15, 2)->nullable();
            $table->double('borc_risk_tutari', 15, 2)->nullable();
            $table->float('cari_basi_max_indirim', 15)->nullable();
            $table->integer('max_indirim_orani')->nullable();
            $table->string('yetkili_ad_soyad')->nullable();
            $table->string('yetkili_gsm')->nullable();
            $table->string('yetkili_dahili')->nullable();
            $table->string('efatura_key', 50)->nullable();
            $table->string('paynet_code', 10)->nullable()->default('1697');
            $table->string('kep_mail')->nullable();
            $table->string('mersis_no')->nullable();
            $table->tinyInteger('iskonto_tip')->nullable()->default(1);
            $table->text('ip_addresses')->nullable();
            $table->smallInteger('sleep_time')->nullable()->default(0);
            $table->integer('max_cari_kod')->nullable()->default(0);
            $table->integer('status')->default(1);
            $table->timestamps();
            $table->unsignedTinyInteger('brake_values_auto')->default(0);
            $table->unsignedTinyInteger('dyno_values_auto')->default(0);
            $table->boolean('new_booking')->nullable()->default(true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branches');
    }
};
