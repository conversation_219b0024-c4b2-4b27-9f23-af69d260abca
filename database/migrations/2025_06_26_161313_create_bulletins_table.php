<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bulletins', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('no', 100)->unique();
            $table->string('slug')->unique();
            $table->string('title', 100);
            $table->unsignedInteger('publish_type')->default(0);
            $table->enum('content_type', ['text', 'html', 'file'])->default('file');
            $table->longText('content')->nullable();
            $table->string('file')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('inactive');
            $table->enum('priority_status', ['normal', 'important', 'urgent', 'critical'])->default('normal');
            $table->enum('visibility', ['public', 'private'])->default('public');
            $table->enum('is_approved', ['pending', 'approved'])->default('approved');
            $table->dateTime('publish_at')->useCurrent();
            $table->unsignedBigInteger('user_id')->nullable()->index('bulletins_user_id_foreign');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bulletins');
    }
};
