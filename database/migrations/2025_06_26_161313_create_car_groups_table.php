<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('car_groups', function (Blueprint $table) {
            $table->bigIncrements('id')->index('id');
            $table->integer('parent_id')->default(0)->index('parent_id');
            $table->string('name')->index('name');
            $table->string('code', 12)->nullable()->index('code');
            $table->integer('status')->default(1);
            $table->integer('version')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('car_groups');
    }
};
