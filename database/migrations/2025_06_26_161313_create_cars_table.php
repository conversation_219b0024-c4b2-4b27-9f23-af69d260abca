<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cars', function (Blueprint $table) {
            $table->bigIncrements('id')->index('id');
            $table->integer('branch_id')->nullable()->index('branch_id');
            $table->integer('customer_id')->nullable()->default(0)->index('customer_id');
            $table->string('plaka')->nullable()->index('plaka');
            $table->string('sase_no')->nullable()->index('sase_no');
            $table->integer('car_group_tip_id')->nullable();
            $table->integer('car_group_marka_id')->nullable();
            $table->integer('car_group_model_id')->nullable();
            $table->integer('car_case_type_id')->nullable();
            $table->integer('car_fuels_id')->nullable();
            $table->integer('car_gears_id')->nullable();
            $table->string('motor_hacmi')->nullable();
            $table->integer('model_yili')->nullable();
            $table->string('cekis')->nullable();
            $table->string('km')->nullable();
            $table->tinyInteger('km_type')->default(1);
            $table->string('il_kodu')->nullable();
            $table->string('ilce_kodu')->nullable();
            $table->date('ilk_tescil_tarihi')->nullable();
            $table->string('tescil_sira_no')->nullable();
            $table->date('tescil_tarihi')->nullable();
            $table->string('cinsi')->nullable();
            $table->string('renk')->nullable();
            $table->string('motor_no')->nullable();
            $table->string('net_agirlik')->nullable();
            $table->string('azami_agirlik')->nullable();
            $table->string('katar_agirlik')->nullable();
            $table->string('romork_agirlik')->nullable();
            $table->string('koltuk_sayisi')->nullable();
            $table->string('ayakta_yolcu_sayisi')->nullable();
            $table->string('silindir_hacmi')->nullable();
            $table->string('motor_gucu')->nullable();
            $table->string('guc_agirlik_orani')->nullable();
            $table->string('kullanim_amaci')->nullable();
            $table->string('tip_onay_no')->nullable();
            $table->integer('status')->nullable()->default(1);
            $table->timestamps();
            $table->string('UQ')->nullable()->index('uq');

            $table->fullText(['plaka'], 'plaka_ft_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cars');
    }
};
