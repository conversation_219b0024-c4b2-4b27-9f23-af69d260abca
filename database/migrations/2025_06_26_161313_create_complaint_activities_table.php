<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('complaint_activities', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('complaint_id');
            $table->unsignedInteger('user_id');
            $table->text('message');
            $table->integer('message_id')->nullable()->default(0);
            $table->integer('source_type')->nullable()->default(0)->comment('0: Normal || 1: Şikayet Var');
            $table->json('attachments')->nullable();
            $table->string('user_name')->nullable();
            $table->json('others')->nullable();
            $table->tinyInteger('status');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('complaint_activities');
    }
};
