<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('complaints', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('talep_no', 12)->index('talep_no');
            $table->unsignedInteger('customer_id')->default(0)->index('customer_id');
            $table->string('telep_kullanici', 55)->nullable();
            $table->string('ad_unvan');
            $table->string('soyad')->nullable();
            $table->string('vergi_dairesi')->nullable();
            $table->string('tc_vergi_no', 11);
            $table->string('eposta');
            $table->string('telefon');
            $table->string('expertise_uuid', 50)->nullable()->index('expertise_uuid');
            $table->string('baslik')->nullable();
            $table->string('dosya', 70)->nullable();
            $table->string('branch_id');
            $table->string('il_id');
            $table->string('ilce_id');
            $table->string('semt');
            $table->string('mahalle');
            $table->string('talep_nedeni')->nullable();
            $table->string('tur', 50)->nullable();
            $table->text('musteri_ifadesi')->nullable();
            $table->text('yetkili_yanit')->nullable();
            $table->integer('source_type')->nullable()->default(0)->comment('0: Normal || 1: Şikayet Var');
            $table->json('attachments')->nullable();
            $table->json('others')->nullable();
            $table->tinyInteger('status')->default(2);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('complaints');
    }
};
