<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('component_dyno', function (Blueprint $table) {
            $table->bigIncrements('id')->index('id');
            $table->integer('expertise_id')->index('expertise_id');
            $table->string('measured_kw')->nullable();
            $table->string('measured_hp')->nullable();
            $table->string('calculated_kw')->nullable();
            $table->string('calculated_hp')->nullable();
            $table->string('calculated_rpm')->nullable();
            $table->string('transfer_kw')->nullable();
            $table->string('transfer_hp')->nullable();
            $table->string('transfer_rpm')->nullable();
            $table->timestamps();
            $table->softDeletes()->index('deleted_at');
            $table->string('type', 20)->default('manuel');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('component_dyno');
    }
};
