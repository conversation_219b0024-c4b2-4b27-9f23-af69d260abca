<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contract_codes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('contract_id')->index('contract_codes_contract_id_foreign');
            $table->unsignedBigInteger('invoice_customer_id')->nullable()->index('contract_codes_invoice_customer_id_foreign');
            $table->string('code', 15);
            $table->string('plaka')->nullable();
            $table->string('telephone', 20)->nullable();
            $table->unsignedTinyInteger('used')->nullable()->default(0);
            $table->timestamps();
            $table->string('type')->default('tumu');
            $table->unsignedTinyInteger('must_payment')->default(1);
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contract_codes');
    }
};
