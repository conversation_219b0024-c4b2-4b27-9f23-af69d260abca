<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contract_period_added_limits', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('contract_period_id')->index('contract_period_added_limits_contract_period_id_foreign');
            $table->unsignedBigInteger('user_id')->index('contract_period_added_limits_user_id_foreign');
            $table->string('type', 20);
            $table->double('amount', 15, 2)->unsigned();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contract_period_added_limits');
    }
};
