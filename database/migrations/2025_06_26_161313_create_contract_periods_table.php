<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contract_periods', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('contract_id')->index('contract_periods_contract_id_foreign');
            $table->date('start_date');
            $table->date('end_date');
            $table->date('last_payment_date')->nullable();
            $table->string('payment_type');
            $table->double('payment_amount', 8, 2)->unsigned();
            $table->string('limit_type')->nullable();
            $table->double('limit_value', 8, 2)->unsigned()->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedTinyInteger('payment_completed')->nullable()->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contract_periods');
    }
};
