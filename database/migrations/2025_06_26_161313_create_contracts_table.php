<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('customer_id')->index('contracts_customer_id_foreign');
            $table->string('no');
            $table->date('start_date')->nullable();
            $table->unsignedTinyInteger('duration_month')->nullable()->default(1);
            $table->unsignedBigInteger('total_count')->nullable();
            $table->double('best_price', 15, 2)->unsigned()->nullable();
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedTinyInteger('status')->nullable()->default(1);
            $table->double('worst_price', 15, 2)->unsigned()->nullable();
            $table->double('seller_price', 8, 2)->unsigned()->default(0);
            $table->double('buyer_price', 8, 2)->unsigned()->default(0);
            $table->unsignedBigInteger('branch_id')->nullable()->default(1);
            $table->string('type')->nullable()->default('hedefli');

            $table->index(['customer_id'], 'customer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contracts');
    }
};
