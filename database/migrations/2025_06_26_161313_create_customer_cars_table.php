<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_cars', function (Blueprint $table) {
            $table->bigIncrements('id')->index('id');
            $table->integer('customer_id')->index('customer_id');
            $table->integer('car_id')->index('car_id');
            $table->boolean('is_old')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_cars');
    }
};
