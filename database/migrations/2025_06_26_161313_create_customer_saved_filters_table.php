<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_saved_filters', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->mediumInteger('user_id')->index('user_id');
            $table->string('name');
            $table->string('unvan')->nullable();
            $table->string('kod')->nullable();
            $table->string('telefon')->nullable();
            $table->string('eposta')->nullable();
            $table->string('web')->nullable();
            $table->string('il_kodu')->nullable();
            $table->string('ilce_kodu')->nullable();
            $table->string('plus_kart')->nullable();
            $table->string('sms_gonder')->nullable();
            $table->string('type')->nullable();
            $table->string('branch_id')->nullable()->index('branch_id');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_saved_filters');
    }
};
