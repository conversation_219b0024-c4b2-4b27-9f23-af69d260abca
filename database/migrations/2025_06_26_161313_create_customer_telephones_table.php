<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_telephones', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->mediumInteger('customer_id');
            $table->string('title', 50)->nullable();
            $table->string('telephone', 10);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_telephones');
    }
};
