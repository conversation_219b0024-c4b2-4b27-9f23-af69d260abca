<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->bigIncrements('id')->index('id');
            $table->integer('branch_id')->nullable()->index('branch_id');
            $table->integer('tip_id')->nullable();
            $table->integer('grup_id')->nullable();
            $table->string('cari_kod', 20)->nullable()->index('cari_kod');
            $table->string('eski_cari_kod')->nullable()->index('eski_cari_kod');
            $table->string('kod')->nullable()->index('kod');
            $table->string('hesap_adi')->nullable();
            $table->string('unvan')->nullable()->index('unvan');
            $table->string('ad')->nullable()->index('ad');
            $table->string('soyad')->nullable()->index('soyad');
            $table->string('yetkili_adi')->nullable();
            $table->integer('customer_group_id')->nullable()->default(0);
            $table->string('cep')->nullable()->index('cep');
            $table->string('telefon')->nullable()->index('telefon');
            $table->string('fax')->nullable();
            $table->string('eposta')->nullable();
            $table->string('web')->nullable();
            $table->string('mahalle')->nullable();
            $table->string('cadde')->nullable();
            $table->string('sokak')->nullable();
            $table->string('semt')->nullable();
            $table->string('il_kodu')->nullable()->index('il_kodu');
            $table->string('ilce')->nullable();
            $table->string('ilce_kodu')->nullable()->index('ilce_kodu');
            $table->string('ulke')->nullable();
            $table->date('dogum_tarihi')->nullable();
            $table->date('evlilik_tarihi')->nullable();
            $table->date('vade_gunu')->nullable();
            $table->integer('campaign_id')->nullable()->default(0);
            $table->string('vergi_dairesi')->nullable();
            $table->string('vergi_dairesi_kodu')->nullable();
            $table->string('vergi_no')->nullable()->index('vergi_no');
            $table->string('tc_no')->nullable()->index('tcno');
            $table->tinyInteger('turk_mu')->nullable()->default(1);
            $table->string('yabancı_belge')->nullable();
            $table->string('plus_kart')->nullable()->comment('plus kart no');
            $table->integer('sitede_yayinla')->nullable()->default(1);
            $table->integer('garantek_uye')->nullable()->default(0);
            $table->integer('sms_gonder')->nullable()->default(0);
            $table->integer('kvkk_verified')->nullable()->default(0);
            $table->string('type')->nullable()->default('bireysel');
            $table->string('muhasebe_type', 10)->nullable()->default('alici');
            $table->string('login_token')->nullable();
            $table->string('login_sms')->nullable();
            $table->integer('status')->default(1)->index('status');
            $table->timestamps();
            $table->string('plus_card')->nullable();
            $table->integer('tnt_search')->default(2);
            $table->string('mersis', 20)->nullable();
            $table->string('pasaport_no', 50)->nullable();
            $table->string('pasaport_belge')->nullable();
            $table->integer('data_check')->default(2)->index('data_check');
            $table->integer('data_not_found')->default(2);
            $table->boolean('kullanimi')->nullable();
            $table->string('esbis_no')->nullable();
            $table->string('yetki_belge_no')->nullable();
            $table->double('risk_tutari', 15, 2)->unsigned()->nullable();
            $table->string('kapi_no')->nullable();

            $table->fullText(['unvan', 'ad', 'soyad', 'telefon'], 'ad_unvan_telefon');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
