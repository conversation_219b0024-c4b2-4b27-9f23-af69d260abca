<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_bodyworks', function (Blueprint $table) {
            $table->bigIncrements('id')->index('id');
            $table->integer('expertise_id')->index('expertise_id');
            $table->string('key')->index('key');
            $table->integer('orijinal')->default(0);
            $table->integer('boyali')->default(0);
            $table->integer('degisen')->default(0);
            $table->integer('duz')->default(0);
            $table->text('note')->nullable();
            $table->timestamp('date')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_bodyworks');
    }
};
