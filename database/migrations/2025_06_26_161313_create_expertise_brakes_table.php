<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_brakes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('expertise_id')->index('expertise_id');
            $table->timestamp('date')->nullable();
            $table->string('yanal_kayma_on')->nullable();
            $table->string('yanal_kayma_arka')->nullable();
            $table->string('max_kuvvet_on_1')->nullable();
            $table->string('max_kuvvet_on_2')->nullable();
            $table->string('max_kuvvet_arka_1')->nullable();
            $table->string('max_kuvvet_arka_2')->nullable();
            $table->string('dengesizlik_orani_on')->nullable();
            $table->string('dengesizlik_orani_arka')->nullable();
            $table->string('el_freni_dengesizlik_orani')->nullable();
            $table->string('yalpa_orani_on_1')->nullable();
            $table->string('yalpa_orani_on_2')->nullable();
            $table->string('yalpa_orani_arka_1')->nullable();
            $table->string('yalpa_orani_arka_2')->nullable();
            $table->string('suspansiyon_on_1')->nullable();
            $table->string('suspansiyon_on_2')->nullable();
            $table->string('suspansiyon_arka_1')->nullable();
            $table->string('suspansiyon_arka_2')->nullable();
            $table->timestamps();
            $table->string('type')->nullable()->default('manuel');
            $table->string('on_dingil_bosta_a')->nullable();
            $table->string('on_dingil_bosta_b')->nullable();
            $table->string('arka_dingil_bosta_a')->nullable();
            $table->string('arka_dingil_bosta_b')->nullable();
            $table->string('el_freni_kuvvet_a')->nullable();
            $table->string('el_freni_kuvvet_b')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_brakes');
    }
};
