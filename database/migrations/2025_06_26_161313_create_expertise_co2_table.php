<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_co2', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('expertise_id');
            $table->string('key');
            $table->integer('status')->nullable();
            $table->text('note')->nullable();
            $table->timestamps();
            $table->softDeletes()->index('deleted_at');

            $table->index(['expertise_id', 'key'], 'expertise_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_co2');
    }
};
