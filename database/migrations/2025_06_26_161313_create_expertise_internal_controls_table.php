<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_internal_controls', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('expertise_id')->index('expertise_id');
            $table->string('key')->index('key');
            $table->integer('sorunlu_mu')->default(0);
            $table->string('answer')->nullable();
            $table->text('note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_internal_controls');
    }
};
