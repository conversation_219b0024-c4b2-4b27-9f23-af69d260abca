<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_payments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('expertise_id')->index('expertise_id');
            $table->integer('case_id')->nullable()->default(0);
            $table->float('amount', 15)->default(0);
            $table->string('type')->nullable()->index('type');
            $table->string('payment_code')->nullable();
            $table->longText('payment_detail')->nullable();
            $table->integer('plus_card_id')->nullable()->default(0)->index('plus_card_id');
            $table->integer('plus_card_odeme_id')->nullable()->default(0)->index('plus_card_odeme_id');
            $table->string('result')->nullable();
            $table->timestamps();
            $table->softDeletes()->index('deleted_at');
            $table->integer('update_data')->default(2);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_payments');
    }
};
