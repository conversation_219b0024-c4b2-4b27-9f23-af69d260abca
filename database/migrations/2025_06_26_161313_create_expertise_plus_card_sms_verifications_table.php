<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_plus_card_sms_verifications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('expertise_id')->index('expertise_plus_card_sms_verifications_expertise_id_foreign');
            $table->string('cep', 20)->nullable();
            $table->string('telefon', 20)->nullable();
            $table->string('code_telefon', 10);
            $table->string('code_cep', 10);
            $table->string('type', 10);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_plus_card_sms_verifications');
    }
};
