<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_report_downloads', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('e_uuid', 36)->index('expertise_report_downloads_e_uuid_foreign')->comment('expertise.uuid');
            $table->unsignedBigInteger('u_id')->index('expertise_report_downloads_u_id_foreign')->comment('user.id');
            $table->timestamp('created_at')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_report_downloads');
    }
};
