<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_saved_filters', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->mediumInteger('user_id')->default(0)->index('user_id');
            $table->string('name');
            $table->date('belge_tarihi_baslangic')->nullable();
            $table->date('belge_tarihi_bitis')->nullable();
            $table->string('belge_no')->nullable();
            $table->string('search')->nullable();
            $table->string('branch_id')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_saved_filters');
    }
};
