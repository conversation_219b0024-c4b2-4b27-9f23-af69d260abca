<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_stocks', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('expertise_id')->nullable()->index('expertise_id');
            $table->integer('stock_id')->nullable()->index('stock_id');
            $table->integer('campaign_id')->nullable();
            $table->integer('sorgu_hizmeti')->nullable();
            $table->integer('yol_yardimi')->nullable()->default(1);
            $table->double('iskonto_amount', 15, 2)->nullable()->default(0);
            $table->double('hizmet_tutari', 15, 2)->nullable();
            $table->double('liste_fiyati', 15, 2)->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_stocks');
    }
};
