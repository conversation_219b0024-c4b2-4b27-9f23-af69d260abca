<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_sub_control_and_engines', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('expertise_id')->index('expertise_id');
            $table->string('key')->index('key');
            $table->integer('status')->nullable();
            $table->string('answer')->nullable();
            $table->text('note')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_sub_control_and_engines');
    }
};
