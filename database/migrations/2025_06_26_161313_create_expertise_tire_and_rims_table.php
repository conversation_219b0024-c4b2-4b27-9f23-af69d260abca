<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_tire_and_rims', function (Blueprint $table) {
            $table->bigIncrements('id')->index('id');
            $table->integer('expertise_id');
            $table->string('key');
            $table->integer('sorunlu_mu')->nullable();
            $table->text('note')->nullable();
            $table->double('dis', 8, 2)->nullable();
            $table->double('basinc', 8, 2)->nullable();
            $table->timestamps();

            $table->index(['expertise_id', 'key'], 'expertise_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_tire_and_rims');
    }
};
