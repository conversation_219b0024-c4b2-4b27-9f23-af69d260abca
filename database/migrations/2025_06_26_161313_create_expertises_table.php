<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertises', function (Blueprint $table) {
            $table->bigIncrements('id')->index('id');
            $table->integer('old_id')->nullable();
            $table->char('uuid', 36)->nullable()->index('uuid');
            $table->integer('user_id')->nullable()->default(0);
            $table->integer('kayit_branch_id')->nullable()->index('kayit_branch_id');
            $table->integer('branch_id')->nullable()->index('branch_id');
            $table->integer('cari_id')->nullable()->index('cari_id');
            $table->integer('satici_id')->nullable()->index('satici_id');
            $table->integer('alici_id')->nullable()->index('alici_id');
            $table->integer('car_id')->nullable()->index('car_id');
            $table->unsignedMediumInteger('km')->nullable()->default(0);
            $table->string('sase_no', 30)->nullable();
            $table->tinyInteger('km_type')->nullable()->default(1);
            $table->string('net_agirlik')->nullable();
            $table->string('nereden_ulastiniz')->nullable();
            $table->string('sorgu_hizmeti')->nullable();
            $table->timestamp('belge_tarihi')->nullable()->useCurrent()->index('belge_tarihi');
            $table->string('belge_no')->nullable()->index('belge_no');
            $table->integer('belge_ozel_kodu')->nullable()->default(1);
            $table->integer('sigorta_teklif_ver')->nullable()->default(0);
            $table->integer('yayin_yasagi')->nullable()->default(0);
            $table->string('kayit_yeri')->nullable();
            $table->integer('status')->nullable()->default(1)->index('status')->comment('0-pasif, 1-aktif,3-onayda');
            $table->string('odeme_turu')->nullable();
            $table->string('bodywork_image')->nullable();
            $table->string('diagnostic_file')->nullable();
            $table->integer('arac_kontrol_user')->nullable()->default(0);
            $table->tinyInteger('arac_kontrol')->nullable()->default(0);
            $table->integer('fren_kontrol_user')->nullable()->default(0);
            $table->tinyInteger('fren_kontrol')->nullable()->default(0);
            $table->integer('kaporta_kontrol_user')->nullable()->default(0);
            $table->tinyInteger('kaporta_kontrol')->nullable()->default(0);
            $table->integer('diagnostic_kontrol_user')->nullable()->default(0);
            $table->tinyInteger('diagnostic_kontrol')->nullable()->default(0);
            $table->integer('ic_kontrol_user')->nullable()->default(0);
            $table->tinyInteger('ic_kontrol')->nullable()->default(0);
            $table->integer('lastik_jant_kontrol_user')->nullable()->default(0);
            $table->tinyInteger('lastik_jant_kontrol')->nullable()->default(0);
            $table->integer('alt_motor_kontrol_user')->nullable()->default(0);
            $table->tinyInteger('alt_motor_kontrol')->nullable()->default(0);
            $table->integer('komponent_kontrol_user')->nullable()->default(0);
            $table->tinyInteger('komponent_kontrol')->nullable()->default(0);
            $table->integer('co2_kontrol_user')->nullable()->default(0);
            $table->integer('co2_kontrol')->nullable()->default(0);
            $table->float('hasar_sorgu_sonuc', 15)->nullable();
            $table->float('kilometre_sorgu_sonuc', 15)->nullable();
            $table->float('borc_sorgu_sonuc', 15)->nullable();
            $table->float('ruhsat_sorgu_sonuc', 15)->nullable();
            $table->string('payment_type', 10)->nullable()->default('normal');
            $table->string('plus_card_payment_type', 10)->nullable();
            $table->integer('plus_kart_id')->nullable();
            $table->string('audio_url', 70)->nullable();
            $table->timestamp('cikis_tarihi')->nullable();
            $table->integer('manuel_save')->nullable()->default(0)->index('manuel_save');
            $table->tinyInteger('employee_downloaded')->nullable()->default(0);
            $table->tinyInteger('ftp_ok')->default(2);
            $table->timestamp('ftp_date')->nullable();
            $table->timestamp('created_at')->nullable()->index('created_at');
            $table->timestamp('updated_at')->nullable();
            $table->softDeletes()->index('deleted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertises');
    }
};
