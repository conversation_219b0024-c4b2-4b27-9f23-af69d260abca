<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hlx_plus', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->tinyInteger('karttipi')->nullable()->default(1);
            $table->string('system_id')->nullable();
            $table->string('no')->index('no');
            $table->integer('branch_id')->nullable()->default(0)->index('branch_id');
            $table->integer('customer_id')->nullable()->index('customer_id');
            $table->double('kredi', 15, 2)->default(0);
            $table->double('puan', 15, 2)->default(0);
            $table->tinyInteger('valid_date_if')->default(0);
            $table->date('valid_date')->default('2025-01-17')->index('valid_date');
            $table->timestamps();
            $table->softDeletes();
            $table->decimal('tlindtutar', 20, 8)->nullable();
            $table->integer('credits_update')->nullable()->default(2);
            $table->integer('points_update')->nullable()->default(2);
            $table->integer('T212_ID')->nullable();
            $table->tinyInteger('T404_transfered')->nullable()->default(0);

            $table->index(['no'], 'no_2');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hlx_plus');
    }
};
