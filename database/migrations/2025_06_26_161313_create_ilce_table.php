<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ilce', function (Blueprint $table) {
            $table->integer('ilce_id', true)->index('ilce_id');
            $table->string('ilce_title')->index('ilce_title');
            $table->integer('ilce_key');
            $table->integer('ilce_sehirkey')->index('ilce_sehirkey');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ilce');
    }
};
