<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_definitions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('cari_hesap_arama_tipi')->nullable();
            $table->string('cari_hesap_bakiyesi_gosterilsin')->nullable();
            $table->string('kupon_takibi_yapilacak')->nullable();
            $table->string('kasa_banka_hesabi_secilsin')->nullable();
            $table->string('barkod_girisi_yapilacak')->nullable();
            $table->string('satis_cari_hesap_grubu')->nullable();
            $table->string('satis_cari_hesap')->nullable();
            $table->string('satis_kasa_hesabi')->nullable();
            $table->string('satis_kredi_karti_hesabi')->nullable();
            $table->string('alis_cari_hesap_grubu')->nullable();
            $table->string('alis_cari_hesap')->nullable();
            $table->string('alis_kasa_hesabi')->nullable();
            $table->string('alis_kredi_karti_hesabi')->nullable();
            $table->string('belge_ozel_kodu')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_definitions');
    }
};
