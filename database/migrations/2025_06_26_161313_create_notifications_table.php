<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('bulletin_id')->index('notifications_bulletin_id_foreign');
            $table->string('notifiable_type', 191);
            $table->unsignedBigInteger('notifiable_id');
            $table->softDeletes();
            $table->timestamps();

            $table->index(['notifiable_type', 'notifiable_id'], 'notifiable_index');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
