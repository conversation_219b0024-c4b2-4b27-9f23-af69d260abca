<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->char('uuid')->nullable();
            $table->integer('customer_id');
            $table->string('description')->nullable();
            $table->string('name')->nullable();
            $table->string('surname')->nullable();
            $table->string('telephone')->nullable();
            $table->string('email')->nullable();
            $table->string('tc_no')->nullable();
            $table->text('address')->nullable();
            $table->string('ip_address')->nullable();
            $table->string('city')->nullable();
            $table->string('country')->nullable()->default('Türkiye');
            $table->integer('plus_kart_adet')->nullable()->default(0);
            $table->integer('ucretsiz_rapor_adet')->nullable()->default(0);
            $table->double('payment_price', 15, 2)->nullable()->default(0);
            $table->string('iyzico_token')->nullable();
            $table->string('iyzico_payment_id')->nullable();
            $table->string('iyzico_transaction_id')->nullable();
            $table->integer('status')->nullable()->default(2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
