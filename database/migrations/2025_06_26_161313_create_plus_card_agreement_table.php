<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_card_agreement', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('plus_card_id')->nullable()->index('plus_card_id');
            $table->integer('customer_id')->nullable()->index('customer_id');
            $table->integer('unit_quantity')->nullable();
            $table->double('payment_amount', 15, 2)->nullable();
            $table->date('valid_date')->nullable();
            $table->string('payment_type', 25)->nullable();
            $table->integer('user_id')->nullable();
            $table->string('delayed_id', 7)->nullable();
            $table->smallInteger('kvkk_approval')->default(2);
            $table->smallInteger('agreement_approval')->default(2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_card_agreement');
    }
};
