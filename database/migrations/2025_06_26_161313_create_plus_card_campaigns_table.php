<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_card_campaigns', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('title');
            $table->text('description')->nullable();
            $table->timestamp('start_date')->nullable();
            $table->timestamp('end_date')->nullable();
            $table->enum('discount_type', ['percentage', 'fixed'])->default('percentage');
            $table->decimal('discount_value', 10)->default(0);
            $table->decimal('minimum_purchase_amount', 10)->default(0);
            $table->boolean('is_single_use')->default(false);
            $table->integer('usage_limit_per_card')->default(0);
            $table->enum('status', ['active', 'inactive', 'expired'])->default('active');
            $table->unsignedBigInteger('created_by')->index('plus_card_campaigns_created_by_foreign');
            $table->unsignedBigInteger('branch_id')->nullable()->index('plus_card_campaigns_branch_id_foreign');
            $table->unsignedBigInteger('stock_id')->nullable()->index('plus_card_campaigns_stock_id_foreign');
            $table->string('usage_code')->nullable()->unique();
            $table->boolean('is_visible_to_all')->default(false);
            $table->timestamps();
            $table->softDeletes();

            $table->index(['status', 'start_date', 'end_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_card_campaigns');
    }
};
