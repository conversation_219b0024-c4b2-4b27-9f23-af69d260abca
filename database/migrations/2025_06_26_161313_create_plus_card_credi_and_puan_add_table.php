<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_card_credi_and_puan_add', function (Blueprint $table) {
            $table->bigIncrements('id')->index('id');
            $table->mediumInteger('user_id')->nullable();
            $table->mediumInteger('stock_id')->nullable()->index('stock_id');
            $table->mediumInteger('definitions_id')->nullable()->index('definitions_id');
            $table->string('balance_type')->nullable();
            $table->bigInteger('card_id')->nullable()->index('card_id');
            $table->double('unit_price', 15, 2)->nullable();
            $table->double('commission', 15, 2)->nullable();
            $table->double('credits_amount', 15, 2)->nullable();
            $table->mediumInteger('credi')->default(0);
            $table->mediumInteger('puan')->default(0);
            $table->mediumInteger('puan_branche_id')->nullable()->default(0)->index('puan_branche_id');
            $table->double('odenen_kdv_dahil_fiyat', 15, 2)->nullable();
            $table->text('payment_type')->nullable();
            $table->text('case')->nullable();
            $table->date('valid_date')->nullable();
            $table->float('devir_miktar', 15)->nullable();
            $table->date('devir_tarih')->nullable();
            $table->text('devir_aciklama')->nullable();
            $table->timestamps();
            $table->softDeletes()->index('deleted_at');
            $table->integer('code_deleted')->default(2);
            $table->string('delayed_id')->nullable();
            $table->tinyInteger('hlx_wallet_transfer')->nullable()->default(0);
            $table->string('paynet_reference_no')->nullable();
            $table->string('paynet_confirmation_no')->nullable();
            $table->unsignedSmallInteger('transfered')->nullable()->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_card_credi_and_puan_add');
    }
};
