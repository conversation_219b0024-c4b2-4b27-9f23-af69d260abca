<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_card_definition_prices', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('definition_id')->index('plus_card_definition_prices_definition_id_foreign');
            $table->unsignedMediumInteger('min_count')->nullable()->default(0);
            $table->double('price', 15, 2)->unsigned()->nullable()->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_card_definition_prices');
    }
};
