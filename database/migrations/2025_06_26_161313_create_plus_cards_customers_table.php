<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_cards_customers', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('hesapkodu')->nullable();
            $table->string('barkodu')->nullable();
            $table->string('hesapadi', 500)->nullable();
            $table->integer('subekodu')->nullable();
            $table->string('uq', 250)->nullable()->index('uq');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_cards_customers');
    }
};
