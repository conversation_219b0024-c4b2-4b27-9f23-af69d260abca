<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_cards_definitions', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->text('definition_name')->nullable();
            $table->mediumInteger('unit_quantity');
            $table->double('unit_price', 15, 2)->nullable();
            $table->mediumInteger('commission')->nullable();
            $table->text('definition_invoice_code')->nullable();
            $table->mediumInteger('stock_id')->nullable();
            $table->string('stok_uq')->nullable();
            $table->string('stock_kodu', 25)->nullable();
            $table->tinyInteger('show_in_view')->nullable()->default(1);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_cards_definitions');
    }
};
