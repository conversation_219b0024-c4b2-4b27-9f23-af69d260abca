<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_cards_puan', function (Blueprint $table) {
            $table->integer('id', true);
            $table->integer('t404id')->nullable();
            $table->mediumInteger('kalanmiktar')->nullable();
            $table->string('hesapkodu')->nullable();
            $table->string('kartid')->nullable();
            $table->string('hesapadi')->nullable();
            $table->string('telefon')->nullable();
            $table->string('cep')->nullable();
            $table->integer('kullanilanmiktar')->nullable();
            $table->string('sube')->nullable();
            $table->string('aciklama', 1500)->nullable();
            $table->string('varyok')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_cards_puan');
    }
};
