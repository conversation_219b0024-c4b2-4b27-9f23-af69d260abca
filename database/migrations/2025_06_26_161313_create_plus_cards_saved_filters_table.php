<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_cards_saved_filters', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->mediumInteger('user_id');
            $table->string('name')->nullable();
            $table->string('card_no')->nullable();
            $table->string('branch_id')->nullable();
            $table->string('customer_code')->nullable();
            $table->string('customer_name')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_cards_saved_filters');
    }
};
