<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_cards', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->tinyInteger('karttipi')->nullable()->default(1);
            $table->string('system_id')->nullable()->index('system_id');
            $table->string('no')->index('no');
            $table->integer('branch_id')->nullable()->default(0)->index('branch_id');
            $table->integer('customer_id')->nullable()->default(0)->index('customer_id');
            $table->double('kredi', 15, 2)->nullable()->default(0);
            $table->double('puan', 15, 2)->nullable()->default(0);
            $table->tinyInteger('valid_date_if')->nullable()->default(0);
            $table->date('valid_date')->default('2025-01-17')->index('valid_date');
            $table->timestamps();
            $table->softDeletes()->index('deleted_at');
            $table->decimal('tlindtutar', 20, 8)->nullable();
            $table->integer('credits_update')->default(2);
            $table->integer('points_update')->default(2);
            $table->tinyInteger('hlx_transfer')->nullable()->default(0);
            $table->tinyInteger('hlx_wallet_transfer')->nullable()->default(0);
            $table->integer('t201cariid')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_cards');
    }
};
