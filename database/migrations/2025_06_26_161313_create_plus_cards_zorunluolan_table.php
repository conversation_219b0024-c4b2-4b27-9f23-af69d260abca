<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_cards_zorunluolan', function (Blueprint $table) {
            $table->integer('cariid')->nullable();
            $table->string('kartid')->nullable();
            $table->binary('kartno')->nullable();
            $table->integer('subekodu')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_cards_zorunluolan');
    }
};
