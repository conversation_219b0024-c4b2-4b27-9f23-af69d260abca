<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('query_logs_extra', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->index('query_logs_extra_user_id_foreign');
            $table->string('expertise_uuid', 50);
            $table->string('type', 20)->comment('hasar, degisen, borc, ruhsat, kilometre, detay');
            $table->text('result')->nullable();
            $table->double('amount', 8, 2)->unsigned()->nullable();
            $table->double('commission', 8, 2)->unsigned()->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('query_logs_extra');
    }
};
