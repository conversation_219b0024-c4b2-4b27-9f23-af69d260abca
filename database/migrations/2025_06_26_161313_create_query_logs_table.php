<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('query_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('user_id')->nullable()->index('user_id');
            $table->string('uuid')->nullable()->index('uuid');
            $table->json('hasar')->nullable();
            $table->string('hasar_ucret')->nullable();
            $table->double('hasar_komisyon', 8, 2)->unsigned()->nullable()->default(0);
            $table->json('kilometre')->nullable();
            $table->string('kilometre_ucret')->nullable();
            $table->double('kilometre_komisyon', 8, 2)->unsigned()->nullable()->default(0);
            $table->json('borc')->nullable();
            $table->string('borc_ucret')->nullable();
            $table->double('borc_komisyon', 8, 2)->unsigned()->nullable()->default(0);
            $table->longText('degisen')->nullable();
            $table->string('degisen_ucret')->nullable();
            $table->double('degisen_komisyon', 8, 2)->unsigned()->nullable()->default(0);
            $table->longText('detail')->nullable();
            $table->string('detail_ucret')->nullable();
            $table->double('detail_komisyon', 8, 2)->unsigned()->nullable()->default(0);
            $table->longText('ruhsat')->nullable();
            $table->string('ruhsat_ucret')->nullable();
            $table->double('ruhsat_komisyon', 8, 2)->unsigned()->nullable()->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('query_logs');
    }
};
