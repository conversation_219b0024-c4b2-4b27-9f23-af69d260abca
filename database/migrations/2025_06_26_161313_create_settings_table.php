<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('settings', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('netgsm_active')->nullable()->default('0');
            $table->string('netgsm_usercode')->nullable();
            $table->string('netgsm_password')->nullable();
            $table->string('netgsm_msgheader')->nullable();
            $table->string('netgsm_msgheader2')->nullable();
            $table->string('netgsm_msgheader3')->nullable();
            $table->string('iyzico_active')->nullable()->default('0');
            $table->string('iyzico_url')->nullable()->default('https://api.iyzipay.com');
            $table->string('iyzico_api_key')->nullable();
            $table->string('iyzico_secret_key')->nullable();
            $table->text('passive_user_message')->nullable();
            $table->smallInteger('google_auth')->nullable();
            $table->timestamps();
            $table->text('hasar_sorgu_mesaji')->nullable();
            $table->text('kilometre_sorgu_mesaji')->nullable();
            $table->text('borc_sorgu_mesaji')->nullable();
            $table->text('arac_detay_sorgu_mesaji')->nullable();
            $table->text('degisen_sorgu_mesaji')->nullable();
            $table->text('ruhsat_sorgu_mesaji')->nullable();
            $table->float('hasar_sorgu_komisyon', 15)->nullable()->default(0);
            $table->float('kilometre_sorgu_komisyon', 15)->nullable()->default(0);
            $table->float('borc_sorgu_komisyon', 15)->nullable()->default(0);
            $table->float('detail_sorgu_komisyon', 15)->nullable()->default(0);
            $table->float('degisen_sorgu_komisyon', 15)->nullable()->default(0);
            $table->float('ruhsat_sorgu_komisyon', 15)->nullable()->default(0);
            $table->double('hasar_sorgu_komisyon_arabasorgula', 8, 2)->unsigned()->nullable()->default(0);
            $table->double('kilometre_sorgu_komisyon_arabasorgula', 8, 2)->unsigned()->nullable()->default(0);
            $table->double('borc_sorgu_komisyon_arabasorgula', 8, 2)->unsigned()->nullable()->default(0);
            $table->double('degisen_sorgu_komisyon_arabasorgula', 8, 2)->unsigned()->nullable()->default(0);
            $table->double('detail_sorgu_komisyon_arabasorgula', 8, 2)->unsigned()->nullable()->default(0);
            $table->double('ruhsat_sorgu_komisyon_arabasorgula', 8, 2)->unsigned()->nullable()->default(0);
            $table->float('hasar_sorgu_otosorgu', 15)->nullable()->default(0);
            $table->float('hasar_sorgu_sistem', 15)->nullable()->default(0);
            $table->float('kilometre_sorgu_otosorgu', 15)->nullable()->default(0);
            $table->float('kilometre_sorgu_sistem', 15)->nullable()->default(0);
            $table->float('borc_sorgu_otosorgu', 15)->nullable()->default(0);
            $table->float('borc_sorgu_sistem', 15)->nullable()->default(0);
            $table->float('detail_sorgu_otosorgu', 15)->nullable()->default(0);
            $table->float('detail_sorgu_sistem', 15)->nullable()->default(0);
            $table->float('degisen_sorgu_otosorgu', 15)->nullable()->default(0);
            $table->float('degisen_sorgu_sistem', 15)->nullable()->default(0);
            $table->float('ruhsat_sorgu_otosorgu', 15)->nullable()->default(0);
            $table->integer('ruhsat_sorgu_sistem')->nullable();
            $table->double('customer_buy_expertise_price', 15, 2)->unsigned()->nullable()->default(0);
            $table->string('hasar_sorgu_firmasi')->nullable()->default('otosorgu');
            $table->string('kilometre_sorgu_firmasi')->nullable()->default('otosorgu');
            $table->string('borc_sorgu_firmasi')->nullable()->default('otosorgu');
            $table->string('arac_detay_sorgu_firmasi')->nullable()->default('otosorgu');
            $table->string('degisen_sorgu_firmasi')->nullable()->default('otosorgu');
            $table->string('ruhsat_sorgu_firmasi')->nullable()->default('otosorgu');
            $table->double('hasar_sorgu_arabasorgula', 15, 2)->nullable()->default(0);
            $table->double('kilometre_sorgu_arabasorgula', 15, 2)->nullable()->default(0);
            $table->double('borc_sorgu_arabasorgula', 15, 2)->nullable()->default(0);
            $table->double('detail_sorgu_arabasorgula', 15, 2)->nullable()->default(0);
            $table->double('degisen_sorgu_arabasorgula', 15, 2)->nullable()->default(0);
            $table->double('ruhsat_sorgu_arabasorgula', 15, 2)->nullable()->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('settings');
    }
};
