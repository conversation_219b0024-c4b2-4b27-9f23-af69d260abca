<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_notes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('stock_id');
            $table->text('aciklama');
            $table->date('uyari_tarihi');
            $table->date('kayit_tarihi');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_notes');
    }
};
