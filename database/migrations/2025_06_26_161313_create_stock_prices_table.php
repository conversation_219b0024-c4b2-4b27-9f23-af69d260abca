<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stock_prices', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('stock_id')->index('stock_id');
            $table->integer('campaign_id')->index('campaign_id');
            $table->string('sube_kodu')->index('sube_kodu');
            $table->double('kdv_dahil_fiyat', 15, 2);
            $table->double('kdv_haric_fiyat', 15, 2);
            $table->timestamps();
            $table->softDeletes()->index('deleted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stock_prices');
    }
};
