<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stocks', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('branch_id')->nullable()->index('branch_id');
            $table->string('kod')->nullable();
            $table->string('stock_kodu', 25)->nullable();
            $table->string('ad')->nullable();
            $table->integer('stock_unit_id')->nullable();
            $table->integer('kdv')->nullable();
            $table->integer('stock_group_id')->nullable();
            $table->integer('stock_type_id')->nullable();
            $table->integer('miktar')->nullable();
            $table->integer('indirim_uygula')->nullable();
            $table->string('muhasebe_alis_fatura_kodu')->nullable();
            $table->string('muhasebe_alis_iade_kodu')->nullable();
            $table->string('muhasebe_alis_iskonto_kodu')->nullable();
            $table->string('muhasebe_alis_fark_fatura_kodu')->nullable();
            $table->string('muhasebe_alis_maliyet_kodu')->nullable();
            $table->string('muhasebe_satis_fatura_kodu')->nullable();
            $table->string('muhasebe_satis_iade_kodu')->nullable();
            $table->string('muhasebe_satis_iskonto_kodu')->nullable();
            $table->string('muhasebe_satis_fark_fatura_kodu')->nullable();
            $table->integer('car')->nullable()->default(0);
            $table->integer('brake')->nullable()->default(0);
            $table->integer('bodywork')->nullable()->default(0);
            $table->integer('internal_control')->nullable()->default(0);
            $table->integer('tire_and_rim')->nullable()->default(0);
            $table->integer('sub_control_and_engine')->nullable()->default(0);
            $table->integer('component')->nullable()->default(0);
            $table->integer('diagnostic')->nullable()->default(0);
            $table->integer('rank')->default(1);
            $table->integer('show_on_expertise')->nullable()->default(1)->index('show_on_expertise');
            $table->tinyInteger('yol_yardimi')->nullable()->default(0);
            $table->tinyInteger('sorgu_hizmeti')->nullable()->default(0);
            $table->integer('status')->default(1)->index('status');
            $table->tinyInteger('active_normal_payment')->default(1);
            $table->float('iskonto_tutari', 15)->nullable();
            $table->tinyInteger('active_plus_cart')->default(1);
            $table->integer('active_contracts')->nullable()->default(2);
            $table->timestamps();
            $table->unsignedTinyInteger('co2')->nullable()->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('stocks');
    }
};
