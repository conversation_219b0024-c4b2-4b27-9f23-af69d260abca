<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tickets', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->char('uuid')->nullable();
            $table->integer('branch_id')->nullable()->default(0);
            $table->integer('user_id')->nullable();
            $table->integer('related_user')->nullable();
            $table->string('to')->nullable();
            $table->integer('priority')->nullable()->default(1);
            $table->string('subject')->nullable();
            $table->integer('status')->nullable()->default(2);
            $table->timestamps();
            $table->softDeletes();
            $table->unsignedTinyInteger('rating')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tickets');
    }
};
