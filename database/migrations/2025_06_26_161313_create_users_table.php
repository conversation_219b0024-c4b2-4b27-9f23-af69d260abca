<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('old_id')->nullable();
            $table->smallInteger('zone_id')->nullable()->default(0)->index('zone_id');
            $table->integer('branch_id')->index('branch_id');
            $table->string('name');
            $table->string('second_name')->nullable();
            $table->string('surname')->nullable();
            $table->string('username')->nullable();
            $table->string('email')->nullable()->unique();
            $table->string('tc')->nullable();
            $table->date('dogum_tarihi')->nullable();
            $table->string('gender')->nullable()->comment('male/female');
            $table->string('password');
            $table->string('telephone')->nullable();
            $table->string('second_telephone')->nullable();
            $table->string('department')->nullable()->index('department');
            $table->string('image')->nullable();
            $table->string('licence_document')->nullable();
            $table->string('id_document')->nullable();
            $table->string('address_document')->nullable();
            $table->string('agreement_document')->nullable();
            $table->string('marriage_document')->nullable();
            $table->string('has_phone')->nullable();
            $table->string('has_computer')->nullable();
            $table->string('has_car')->nullable();
            $table->string('has_document')->nullable();
            $table->integer('yearly_work_time')->nullable()->default(0);
            $table->date('work_start_date')->nullable()->default('2023-12-19');
            $table->integer('remain_holiday_date_count')->nullable()->default(0);
            $table->text('comment')->nullable();
            $table->string('type')->default('employee')->index('type');
            $table->integer('user_role_group_id')->nullable()->default(1);
            $table->string('blood_group', 3)->nullable();
            $table->integer('kvkk_verified')->nullable()->default(0);
            $table->string('notification_token')->nullable();
            $table->integer('status')->default(1)->index('status');
            $table->timestamp('email_verified_at')->nullable();
            $table->rememberToken();
            $table->string('password_reset_token', 10)->nullable();
            $table->string('password_reset_code', 20)->nullable();
            $table->integer('session_lifetime')->nullable()->default(600);
            $table->tinyInteger('first_login')->nullable()->default(1);
            $table->string('alt_beden', 10)->nullable();
            $table->string('ust_beden', 10)->nullable();
            $table->tinyInteger('ayakkabi_numarasi')->nullable();
            $table->string('totp_secret')->nullable();
            $table->string('login_token', 32)->nullable();
            $table->string('login_sms', 10)->nullable();
            $table->tinyInteger('receive_sms')->nullable()->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
