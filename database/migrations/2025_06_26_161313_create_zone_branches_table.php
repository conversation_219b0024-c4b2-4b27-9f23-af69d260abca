<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('zone_branches', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->smallInteger('zone_id')->index('zone_id');
            $table->smallInteger('branch_id')->index('branch_id');
            $table->timestamps();
            $table->softDeletes()->index('deleted_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('zone_branches');
    }
};
