<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contract_code_stocks', function (Blueprint $table) {
            $table->foreign(['contract_code_id'])->references(['id'])->on('contract_codes')->onUpdate('restrict')->onDelete('restrict');
            $table->foreign(['stock_id'])->references(['id'])->on('stocks')->onUpdate('restrict')->onDelete('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contract_code_stocks', function (Blueprint $table) {
            $table->dropForeign('contract_code_stocks_contract_code_id_foreign');
            $table->dropForeign('contract_code_stocks_stock_id_foreign');
        });
    }
};
