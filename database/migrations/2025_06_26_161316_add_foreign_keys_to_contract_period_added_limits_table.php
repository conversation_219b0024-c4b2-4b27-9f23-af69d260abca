<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contract_period_added_limits', function (Blueprint $table) {
            $table->foreign(['contract_period_id'])->references(['id'])->on('contract_periods')->onUpdate('restrict')->onDelete('restrict');
            $table->foreign(['user_id'])->references(['id'])->on('users')->onUpdate('restrict')->onDelete('restrict');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contract_period_added_limits', function (Blueprint $table) {
            $table->dropForeign('contract_period_added_limits_contract_period_id_foreign');
            $table->dropForeign('contract_period_added_limits_user_id_foreign');
        });
    }
};
