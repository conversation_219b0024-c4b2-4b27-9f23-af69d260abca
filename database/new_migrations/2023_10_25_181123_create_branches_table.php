<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branches', function (Blueprint $table) {
            $table->id();
            $table->string('kod',5);
            $table->string('belge_kod',4);
            $table->string('kisa_ad',50);
            $table->string('unvan');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('branch_addresses', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->string('mahalle')->nullable();
            $table->string('cadde')->nullable();
            $table->string('sokak')->nullable();
            $table->string('semt')->nullable();
            $table->unsignedBigInteger('city_id')->nullable();
            $table->unsignedBigInteger('town_id')->nullable();
            $table->string('lat')->nullable();
            $table->string('lng')->nullable();
            $table->string('konum')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('city_id')->references('id')->on('cities')->nullOnDelete();
            $table->foreign('town_id')->references('id')->on('towns')->nullOnDelete();
            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();

            $table->index('branch_id');
        });

        Schema::create('branch_contacts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->string('telefon',20)->nullable();
            $table->string('whatsapp_telefon',20)->nullable();
            $table->string('fax',20)->nullable();
            $table->string('gsm',20)->nullable();
            $table->string('email',50)->nullable();
            $table->string('web',75)->nullable();

            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();
            $table->timestamps();
            $table->softDeletes();

            $table->index('branch_id');
        });

        Schema::create('branch_informations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->string('logo',70)->nullable();
            $table->string('paynet_code',10)->nullable();
            $table->string('google_button_2')->nullable();
            $table->float('plus_kart_satis',5,2)->nullable();
            $table->float('eksper_merkez',5,2)->nullable();
            $table->float('eksper_orani',5,2)->nullable();
            $table->unsignedTinyInteger('hasar_sorgulama')->default(1);
            $table->unsignedTinyInteger('plus_kart_yukle')->default(1);
            $table->date('son_fiyat_degisiklik_tarihi')->nullable();
            $table->unsignedMediumInteger('bedelsiz_eksper')->nullable();
            $table->float('max_indirim_tutari',10,2)->nullable();
            $table->float('borc_risk_tutari',15,2)->nullable();
            $table->float('cari_basi_max_indirim',15,2)->nullable();
            $table->float('max_indirim_orani',15,2)->nullable();
            $table->text('ip_addresses')->nullable();
            $table->unsignedMediumInteger('sleep_time')->default(0);
            $table->unsignedBigInteger('max_cari_kod')->default(1);
            $table->unsignedTinyInteger('brake_values_auto')->default(1);
            $table->boolean('status')->default(true);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();

            $table->index('branch_id');
        });

        Schema::create('branch_tax_informations', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->string('vergi_dairesi',30)->nullable();
            $table->unsignedBigInteger('vergi_no')->nullable();
            $table->unsignedBigInteger('tc_no')->nullable();
            $table->string('mersis_no',16)->nullable();
            $table->unsignedSmallInteger('iskonto_tip')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();

            $table->index('branch_id');
        });

        Schema::create('branch_contracts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->string('file',70)->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();

            $table->index('branch_id');
        });

        Schema::create('branch_authorized_persons', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->string('ad_soyad');
            $table->string('gsm',15);
            $table->string('dahili',15);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();

            $table->index('branch_id');
        });



        Schema::create('branch_tses', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->string('tse_kodu',20);
            $table->date('gecerlilik_tarihi');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();

            $table->index('branch_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('branch_tses', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
        });
        Schema::dropIfExists('branch_tses');

        Schema::table('branch_partners', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('branch_partners');

        Schema::table('branch_authorized_persons', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
        });
        Schema::dropIfExists('branch_authorized_persons');

        Schema::table('branch_contracts', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
        });
        Schema::dropIfExists('branch_contracts');

        Schema::table('branch_tax_informations', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
        });
        Schema::dropIfExists('branch_tax_informations');

        Schema::table('branch_informations', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
        });
        Schema::dropIfExists('branch_informations');

        Schema::table('branch_contacts', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
        });
        Schema::dropIfExists('branch_contacts');

        Schema::table('branch_addresses', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['city_id']);
            $table->dropForeign(['town_id']);
        });
        Schema::dropIfExists('branch_addresses');

        Schema::dropIfExists('branches');
    }
};
