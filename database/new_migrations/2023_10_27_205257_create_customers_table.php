<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('uq',50)->nullable();
            $table->unsignedBigInteger('branch_id')->default(1);
            $table->string('cari_kod',20);
            $table->string('eski_cari_kod',36)->nullable();
            $table->string('unvan')->nullable();
            $table->string('soyad')->nullable();
            $table->string('cep',50)->nullable();
            $table->string('telefon',50)->nullable();
            $table->unsignedBigInteger('vergi_no')->nullable();
            $table->unsignedBigInteger('tc_no')->nullable();
            $table->string('type',20)->default('bireysel');
            $table->string('login_token',30)->nullable();
            $table->string('login_sms',10)->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('set default');
        });

        Schema::create('customer_authorized_persons', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('yetkili_adi');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();

            $table->index('customer_id');
        });

        Schema::create('customer_contact_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('fax',50)->nullable();
            $table->string('eposta',50)->nullable();
            $table->string('web',50)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();

            $table->index('customer_id');
        });

        Schema::create('customer_addresses', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('mahalle')->nullable();
            $table->string('cadde')->nullable();
            $table->string('sokak')->nullable();
            $table->string('semt')->nullable();
            $table->unsignedBigInteger('city_id')->nullable();
            $table->unsignedBigInteger('town_id')->nullable();
            $table->string('ulke')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();
            $table->foreign('city_id')->references('id')->on('cities')->restrictOnDelete();
            $table->foreign('town_id')->references('id')->on('towns')->restrictOnDelete();

            $table->index('customer_id');
        });

        Schema::create('customer_other_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->date('dogum_tarihi')->nullable();
            $table->date('evlilik_tarihi')->nullable();
            $table->unsignedSmallInteger('vade_gunu')->nullable();
            $table->string('yetki_belge_no')->nullable();
            $table->string('esbis_no')->nullable();
            $table->string('pasaport_belge')->nullable();
            $table->string('pasaport_no')->nullable();
            $table->string('mersis')->nullable();
            $table->string('vergi_dairesi')->nullable();
            $table->string('yabanci_belge')->nullable();
            $table->boolean('turk_mu')->default(1);
            $table->boolean('sitede_yayinla')->default(1);
            $table->boolean('sms_gonder')->default(1);
            $table->boolean('kvkk_verified')->default(1);
//            $table->unsignedTinyInteger('kullanimi')->nullable(); ????

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();

            $table->index('customer_id');
        });

        Schema::create('customer_feedback', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('image')->nullable();
            $table->text('message')->nullable();
            $table->smallInteger('status')->default(2);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();

            $table->index('customer_id');
        });

        Schema::create('customer_queries', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('query');
            $table->timestamps();
            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();

            $table->index('customer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_queries', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
        });
        Schema::dropIfExists('customer_queries');

        Schema::table('customer_feedback', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
        });
        Schema::dropIfExists('customer_feedback');

        Schema::table('customer_other_infos', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
        });
        Schema::dropIfExists('customer_other_infos');

        Schema::table('customer_addresses', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
            $table->dropForeign(['city_id']);
            $table->dropForeign(['town_id']);
        });
        Schema::dropIfExists('customer_addresses');

        Schema::table('customer_contact_infos', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
        });
        Schema::dropIfExists('customer_contact_infos');

        Schema::table('customer_authorized_persons', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
        });
        Schema::dropIfExists('customer_authorized_persons');

        Schema::table('customers', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
        });
        Schema::dropIfExists('customers');
    }

};
