<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('stocks', function (Blueprint $table) {
            $table->id();
            $table->string('kod',50)->comment('uq')->nullable();
            $table->string('stock_kodu',10)->nullable();
            $table->string('ad',100);
            $table->float('amount')->nullable();
            $table->unsignedSmallInteger('kdv')->nullable();
            $table->unsignedFloat('kdv_haric_fiyat')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('stock_other_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('stock_id');
            $table->unsignedTinyInteger('yol_yardimi')->default(0);
            $table->unsignedTinyInteger('sorgu_hizmeti')->default(0);
            $table->unsignedTinyInteger('rank')->default(0);
            $table->tinyInteger('status')->default(1);
            $table->unsignedTinyInteger('active_normal_payment')->default(1);
            $table->unsignedTinyInteger('active_plus_card')->default(1);
            $table->unsignedTinyInteger('active_contracts')->default(1);
            $table->unsignedFloat('iskonto_amount')->nullable();
            $table->unsignedTinyInteger('indirim_uygula')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('stock_id')->references('id')->on('stocks')->cascadeOnDelete();

            $table->index('stock_id');
        });

        Schema::create('stock_expertise_tabs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('stock_id')->nullable();
            $table->unsignedTinyInteger('show_on_expertise')->default(0);
            $table->unsignedTinyInteger('car')->default(0);
            $table->unsignedTinyInteger('brake')->default(0);
            $table->unsignedTinyInteger('bodywork')->default(0);
            $table->unsignedTinyInteger('internal_control')->default(0);
            $table->unsignedTinyInteger('tire_and_rim')->default(0);
            $table->unsignedTinyInteger('sub_control_and_engine')->default(0);
            $table->unsignedTinyInteger('component')->default(0);
            $table->unsignedTinyInteger('diagnostic')->default(0);
            $table->unsignedTinyInteger('co2')->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('stock_id')->references('id')->on('stocks')->cascadeOnDelete();

            $table->index('stock_id');
        });

        Schema::create('stock_prices', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('stock_id')->nullable();
            $table->unsignedBigInteger('campaign_id')->nullable();
            $table->unsignedFloat('kdv_haric_fiyat')->nullable();
            $table->unsignedFloat('kdv_dahil_fiyat')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('stock_id')->references('id')->on('stocks')->cascadeOnDelete();
            $table->foreign('campaign_id')->references('id')->on('campaigns')->nullOnDelete();

            $table->index('stock_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('stock_prices', function (Blueprint $table) {
            $table->dropForeign(['stock_id']);
            $table->dropForeign(['campaign_id']);
        });
        Schema::dropIfExists('stock_prices');

        Schema::table('stock_expertise_tabs', function (Blueprint $table) {
            $table->dropForeign(['stock_id']);
        });
        Schema::dropIfExists('stock_expertise_tabs');

        Schema::table('stock_other_infos', function (Blueprint $table) {
            $table->dropForeign(['stock_id']);
        });
        Schema::dropIfExists('stock_other_infos');

        Schema::dropIfExists('stocks');
    }

};
