<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_card_definitions', function (Blueprint $table) {
            $table->id();
            $table->string('definition_name',50)->nullable();
            $table->mediumInteger('unit_quantity');
            $table->unsignedFloat('unit_price',15,2)->nullable();
            $table->mediumInteger('commission')->nullable();
            $table->mediumInteger('stock_id')->nullable();
            $table->string('stok_uq')->nullable();
            $table->string('stok_kodu')->nullable();
            $table->boolean('show_in_view')->default(false);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('stock_id')->references('id')->on('stocks')->restrictOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plus_card_definitions', function (Blueprint $table) {
            $table->dropForeign(['stock_id']); // Foreign key kısıtlamasını kaldırıyoruz
        });

        Schema::dropIfExists('plus_card_definitions');
    }
};
