<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_cards', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id')->nullable();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->unsignedTinyInteger('karttipi');
            $table->string('system_id',30)->nullable();
            $table->string('no',60)->nullable();
            $table->unsignedTinyInteger('valid_date_if')->default(0);
            $table->date('valid_date')->nullable();
            $table->unsignedFloat('tlindtutar',15,2)->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('branch_id')->references('id')->on('branches')->nullOnDelete();
            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();

            $table->index('customer_id');
            $table->index('no');
            $table->index('system_id');
        });

        Schema::create('plus_card_definition_stocks',function (Blueprint $table){
            $table->id();
            $table->unsignedBigInteger('definition_id');
            $table->unsignedBigInteger('stock_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('definition_id')->references('id')->on('plus_card_definitions')->cascadeOnDelete();
            $table->foreign('stock_id')->references('id')->on('stocks')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plus_card_definition_stocks', function (Blueprint $table) {
            $table->dropForeign(['definition_id']);
            $table->dropForeign(['stock_id']);
        });
        Schema::dropIfExists('plus_card_definition_stocks');

        Schema::table('plus_cards', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['customer_id']);
        });
        Schema::dropIfExists('plus_cards');
    }
};
