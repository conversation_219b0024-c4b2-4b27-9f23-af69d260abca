<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_role_groups', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->integer('status')->default(1);
            $table->timestamps();
        });

        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_role_group_id');
            $table->string('name',50);
            $table->string('second_name',50)->nullable();
            $table->string('surname',50)->nullable();
            $table->string('email',50)->nullable();
            $table->unsignedBigInteger('tc');
            $table->string('password');
            $table->string('telephone',20);
            $table->enum('type',['employee','admin'])->default('employee');
            $table->boolean('status')->default(1);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_role_group_id')->references('id')->on('user_role_groups')->restrictOnDelete();
        });

        Schema::create('user_personal_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->date('dogum_tarihi')->nullable();
            $table->char('gender',1)->default('b')->comment('e = erkek,k = kadin,b = belirtilmemis');
            $table->string('second_telephone',20)->nullable();
            $table->string('image',70)->nullable();
            $table->enum('blood_group', ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'])->nullable();
            $table->unsignedTinyInteger('alt_beden')->nullable();
            $table->enum('ust_beden', ['XS', 'S', 'M', 'L', 'XL', 'XXL', 'XXXL'])->nullable();
            $table->unsignedTinyInteger('ayakkabi_numarasi')->nullable();

            $table->timestamps();
            $table->softDeletes();
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->index('user_id');
        });

        Schema::create('user_work_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('username',30)->nullable();
            $table->string('department')->nullable();
            $table->unsignedTinyInteger('has_phone')->default(0);
            $table->unsignedTinyInteger('has_computer')->default(0);
            $table->unsignedTinyInteger('has_car')->default(0);
            $table->unsignedTinyInteger('has_document')->default(0);
            $table->unsignedMediumInteger('yearly_work_time')->nullable();
            $table->date('work_start_date')->nullable();
            $table->unsignedMediumInteger('remain_holiday_date_count')->nullable();
            $table->text('comment')->nullable();
            $table->unsignedTinyInteger('kvkk_verified')->default(0);

            $table->timestamps();
            $table->softDeletes();
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->index('user_id');
        });

        Schema::create('user_documents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('licence_document')->nullable();
            $table->string('id_document')->nullable();
            $table->string('address_document')->nullable();
            $table->string('agreement_document')->nullable();
            $table->string('marriage_document')->nullable();

            $table->timestamps();
            $table->softDeletes();
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->index('user_id');
        });

        Schema::create('user_other_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('notification_token')->nullable();
            $table->string('email_verified_at')->nullable();
            $table->string('remember_token')->nullable();
            $table->string('password_reset_token')->nullable();
            $table->string('session_lifetime')->default(1800);
            $table->string('totp_secret')->nullable();
            $table->string('login_token')->nullable();
            $table->string('login_sms')->nullable();
            $table->unsignedTinyInteger('first_login')->default(1);
            $table->unsignedTinyInteger('receive_sms')->default(0);

            $table->timestamps();
            $table->softDeletes();
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->index('user_id');
        });

        Schema::create('user_addresses', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('title',50);
            $table->text('address');
            $table->smallInteger('city_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();

            $table->index('user_id');
        });

        Schema::create('user_banks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('banka_adi')->nullable();
            $table->string('iban')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();

            $table->index('user_id');
        });

        Schema::create('user_branches', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('branch_id');
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();
            $table->softDeletes();
        });

        Schema::create('user_emergencies', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('title')->nullable();
            $table->string('name')->nullable();
            $table->string('telephone',10)->nullable();
            $table->string('address')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->index('user_id');
        });

        Schema::create('user_phones', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('title',30)->nullable();
            $table->string('telephone',10);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->index('user_id');
        });

        Schema::create('user_roles', function (Blueprint $table) {
            $table->id();
            $table->integer('group_id');
            $table->string('key');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_phones', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('user_phones');

        Schema::table('user_emergencies', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('user_emergencies');

        Schema::table('user_branches', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropForeign(['branch_id']);
        });
        Schema::dropIfExists('user_branches');

        Schema::table('user_banks', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('user_banks');

        Schema::table('user_addresses', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('user_addresses');

        Schema::table('user_other_infos', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('user_other_infos');

        Schema::table('user_documents', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('user_documents');

        Schema::table('user_work_infos', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('user_work_infos');

        Schema::table('user_personal_infos', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('user_personal_infos');

        Schema::dropIfExists('user_roles');
        Schema::dropIfExists('user_role_groups');

        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['user_role_group_id']);
        });
        Schema::dropIfExists('users');
    }

};
