<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('zone_branches', function (Blueprint $table) {
            $table->id();
            $table->smallInteger('zone_id');
            $table->smallInteger('branch_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('zone_id')->references('id')->on('zones')->cascadeOnDelete();
            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('zone_branches');

        Schema::table('zone_branches', function (Blueprint $table) {
            $table->dropForeign(['zone_id']);
            $table->dropForeign(['branch_id']);
        });
    }
};
