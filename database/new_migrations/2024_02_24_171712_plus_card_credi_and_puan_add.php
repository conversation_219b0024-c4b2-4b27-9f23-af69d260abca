<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_card_credi_and_puan_add', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->unsignedBigInteger('stock_id')->nullable();
            $table->unsignedBigInteger('definitions_id')->nullable();
            $table->unsignedBigInteger('card_id')->nullable();

            $table->enum('balance_type',['credits','points'])->nullable();

            $table->date('valid_date')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
            $table->foreign('stock_id')->references('id')->on('stocks')->restrictOnDelete();
            $table->foreign('definitions_id')->references('id')->on('plus_card_definitions')->restrictOnDelete();
            $table->foreign('card_id')->references('id')->on('plus_cards')->cascadeOnDelete();

            $table->index('card_id');
        });

        Schema::create('plus_card_credi_and_puan_add_payment_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('plus_card_credi_and_puan_add_id');
            $table->unsignedFloat('unit_price',15,2)->nullable();
            $table->unsignedFloat('commission',15,2)->nullable();
            $table->unsignedFloat('credits_amount',15,2)->nullable();
            $table->unsignedFloat('odenen_kdv_dahil_fiyat',15,2)->nullable();
            $table->string('payment_type',50)->nullable();
            $table->unsignedBigInteger('puan_branche_id')->nullable()->default(0);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('plus_card_credi_and_puan_add_id')->references('id')->on('plus_card_credi_and_puan_add')->cascadeOnDelete();
            $table->foreign('puan_branche_id')->references('id')->on('branches')->cascadeOnDelete();

            $table->index('plus_card_credi_and_puan_add_id');
        });

        Schema::create('plus_card_credi_and_puan_removes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('plus_card_credi_and_puan_add_id');
            $table->unsignedBigInteger('user_id');
            $table->unsignedMediumInteger('amount');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('plus_card_credi_and_puan_add_id')->references('id')->on('plus_card_credi_and_puan_add')->cascadeOnDelete();
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();


            $table->index('plus_card_credi_and_puan_add_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plus_card_credi_and_puan_add',function (Blueprint $table){
            $table->dropForeign(['user_id']);
            $table->dropForeign(['stock_id']);
            $table->dropForeign(['definitions_id']);
            $table->dropForeign(['card_id']);
            $table->dropForeign(['puan_branche_id']);
        });

        Schema::table('plus_card_credi_and_puan_add_payment_infos', function (Blueprint $table) {
            $table->dropForeign(['plus_card_credi_and_puan_add_id']);
        });

        Schema::table('plus_card_credi_and_puan_removes', function (Blueprint $table) {
            $table->dropForeign(['plus_card_credi_and_puan_add_id']);
            $table->dropForeign(['user_id']);
        });

    }
};
