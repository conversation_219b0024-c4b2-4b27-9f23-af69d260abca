<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_card_agreement', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('plus_card_id')->nullable();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->unsignedBigInteger('user_id')->nullable();
            $table->integer('unit_quantity')->nullable();
            $table->float('payment_amount',15,2)->nullable();
            $table->date('valid_date')->nullable();
            $table->string('delayed_id', 7)->nullable();
            $table->boolean('kvkk_approval')->default(0);
            $table->boolean('agreement_approval')->default(0);
            $table->timestamps();

            $table->foreign('plus_card_id')->references('id')->on('plus_cards')->cascadeOnDelete();
            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();

            $table->index('plus_card_id');
            $table->index('customer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plus_card_agreement', function (Blueprint $table) {
            $table->dropForeign(['plus_card_id']);
            $table->dropForeign(['customer_id']);
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('plus_card_agreement');
    }
};
