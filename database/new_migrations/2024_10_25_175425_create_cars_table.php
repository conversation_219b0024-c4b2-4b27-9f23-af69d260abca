<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('car_groups', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->string('name');
            $table->string('code')->nullable();
            $table->string('version');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('parent_id')->references('id')->on('car_groups')->cascadeOnDelete();
        });

        Schema::create('car_case_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('image',70)->nullable();
            $table->string('bodywork_open_image',70)->nullable();

            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('car_fuels', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('car_gears', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('cars', function (Blueprint $table) {
            $table->id();
            $table->string('uq',50)->nullable();
            $table->string('plaka',20);
            $table->string('sase_no',30);
            $table->string('km',15);
            $table->unsignedTinyInteger('km_type')->default(1);
            $table->timestamps();
            $table->softDeletes();

            $table->index('plaka');
            $table->index('sase_no');
        });

        Schema::create('car_technic_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('car_id');
            $table->unsignedBigInteger('car_group_tip_id')->nullable();
            $table->unsignedBigInteger('car_group_marka_id')->nullable();
            $table->unsignedBigInteger('car_group_model_id')->nullable();
            $table->unsignedBigInteger('car_case_type_id')->nullable();
            $table->unsignedBigInteger('car_fuel_id')->nullable();
            $table->unsignedBigInteger('car_gear_id')->nullable();
            $table->string('model_yili',4)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('car_id')->references('id')->on('cars')->cascadeOnDelete();
            $table->foreign('car_group_tip_id')->references('id')->on('car_groups')->restrictOnDelete();
            $table->foreign('car_group_marka_id')->references('id')->on('car_groups')->restrictOnDelete();
            $table->foreign('car_group_model_id')->references('id')->on('car_groups')->restrictOnDelete();
            $table->foreign('car_case_type_id')->references('id')->on('car_case_types')->restrictOnDelete();
            $table->foreign('car_fuel_id')->references('id')->on('car_fuels')->restrictOnDelete();
            $table->foreign('car_gear_id')->references('id')->on('car_gears')->restrictOnDelete();
            $table->index('car_id');
        });

        Schema::create('car_other_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('car_id');
            $table->string('motor_hacmi',20)->nullable();
            $table->string('cekis',4)->nullable();
            $table->unsignedBigInteger('city_id')->nullable();
            $table->unsignedBigInteger('town_id')->nullable();
            $table->date('ilk_tescil_tarihi')->nullable();
            $table->string('tescil_sira_no',50)->nullable();
            $table->date('tescil_tarihi')->nullable();
            $table->string('cinsi',50)->nullable();
            $table->string('renk',50)->nullable();
            $table->string('motor_no',50)->nullable();
            $table->string('net_agirlik',50)->nullable();
            $table->string('azami_agirlik',50)->nullable();
            $table->string('katar_agirlik',50)->nullable();
            $table->string('romork_agirlik',50)->nullable();
            $table->string('koltuk_sayisi',50)->nullable();
            $table->string('ayakta_yolcu_sayisi',50)->nullable();
            $table->string('silindir_hacmi',50)->nullable();
            $table->string('motor_gucu',50)->nullable();
            $table->string('guc_agirlik_orani',50)->nullable();
            $table->string('kullanim_amaci',50)->nullable();
            $table->string('tip_onay_no',50)->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('car_id')->references('id')->on('cars')->cascadeOnDelete();
            $table->index('car_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('car_groups', function (Blueprint $table) {
            $table->dropForeign(['parent_id']);
        });
        Schema::dropIfExists('car_groups');

        Schema::table('car_technic_infos', function (Blueprint $table) {
            $table->dropForeign(['car_id']);
            $table->dropForeign(['car_group_tip_id']);
            $table->dropForeign(['car_group_marka_id']);
            $table->dropForeign(['car_group_model_id']);
            $table->dropForeign(['car_case_type_id']);
            $table->dropForeign(['car_fuel_id']);
            $table->dropForeign(['car_gear_id']);
        });
        Schema::dropIfExists('car_technic_infos');

        Schema::table('car_other_infos', function (Blueprint $table) {
            $table->dropForeign(['car_id']);
        });
        Schema::dropIfExists('car_other_infos');

        Schema::dropIfExists('car_case_types');
        Schema::dropIfExists('car_fuels');
        Schema::dropIfExists('car_gears');
        Schema::dropIfExists('cars');
    }

};
