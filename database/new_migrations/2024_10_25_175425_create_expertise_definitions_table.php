<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertise_definitions', function (Blueprint $table) {
            $table->id();
            $table->string('key',50);
            $table->string('value',70);
            $table->string('place',20)->comment("bodywork,brake,...");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('expertise_definitions');
    }
};
