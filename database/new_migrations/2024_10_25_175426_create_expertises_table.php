<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('expertises', function (Blueprint $table) {
            $table->id();
            $table->string('uuid', 36)->unique();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('kayit_branch_id');
            $table->unsignedBigInteger('branch_id');
            $table->unsignedBigInteger('cari_id');
            $table->unsignedBigInteger('satici_id');
            $table->unsignedBigInteger('alici_id');
            $table->unsignedBigInteger('car_id');
            $table->string('km',60)->nullable();
            $table->tinyInteger('km_type')->nullable();
            $table->string('sase_no',60)->nullable();
            $table->timestamp('belge_tarihi')->default(now());
            $table->timestamp('cikis_tarihi')->nullable();
            $table->string('belge_no',50)->nullable();
            $table->unsignedTinyInteger('yayin_yasagi')->default(0);
            $table->string('payment_type',20)->nullable();
            $table->unsignedTinyInteger('manuel_save')->default(0);
            $table->tinyInteger('ftp_ok')->default(2);
            $table->timestamp('ftp_date')->nullable();
            $table->tinyInteger('status')->default(1)->comment('0-pasif, 1-aktif,3-onayda');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users')->restrictOnDelete();
            $table->foreign('kayit_branch_id')->references('id')->on('branches')->cascadeOnDelete();
            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();
            $table->foreign('cari_id')->references('id')->on('customers')->restrictOnDelete();
            $table->foreign('satici_id')->references('id')->on('customers')->restrictOnDelete();
            $table->foreign('alici_id')->references('id')->on('customers')->restrictOnDelete();
            $table->foreign('car_id')->references('id')->on('cars')->restrictOnDelete();

            $table->index('uuid');
        });

        Schema::create('expertise_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->string('bodywork_image',70)->nullable();
            $table->string('nereden_ulastiniz',40)->nullable();
            $table->string('audio_url',60)->nullable();
            $table->tinyInteger('belge_ozel_kodu')->default(1);
            $table->tinyInteger('sigorta_teklif_ver')->default(1);
            $table->tinyInteger('employee_downloaded')->default(0);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->index('expertise_id');
        });

        Schema::create('expertise_control_statuses', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->unsignedBigInteger('arac_kontrol_user')->nullable();
            $table->boolean('arac_kontrol')->default(0);
            $table->unsignedBigInteger('fren_kontrol_user')->nullable();
            $table->boolean('fren_kontrol')->default(0);
            $table->unsignedBigInteger('kaporta_kontrol_user')->nullable();
            $table->boolean('kaporta_kontrol')->default(0);
            $table->unsignedBigInteger('diagnostic_kontrol_user')->nullable();
            $table->boolean('diagnostic_kontrol')->default(0);
            $table->unsignedBigInteger('ic_kontrol_user')->nullable();
            $table->boolean('ic_kontrol')->default(0);
            $table->unsignedBigInteger('lastik_jant_kontrol_user')->nullable();
            $table->boolean('lastik_jant_kontrol')->default(0);
            $table->unsignedBigInteger('alt_motor_kontrol_user')->nullable();
            $table->boolean('alt_motor_kontrol')->default(0);
            $table->unsignedBigInteger('komponent_kontrol_user')->nullable();
            $table->boolean('komponent_kontrol')->default(0);
            $table->unsignedBigInteger('co2_kontrol_user')->nullable();
            $table->boolean('co2_kontrol')->default(0);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('arac_kontrol_user')->references('id')->on('users')->restrictOnDelete();
            $table->foreign('fren_kontrol_user')->references('id')->on('users')->restrictOnDelete();
            $table->foreign('kaporta_kontrol_user')->references('id')->on('users')->restrictOnDelete();
            $table->foreign('diagnostic_kontrol_user')->references('id')->on('users')->restrictOnDelete();
            $table->foreign('ic_kontrol_user')->references('id')->on('users')->restrictOnDelete();
            $table->foreign('lastik_jant_kontrol_user')->references('id')->on('users')->restrictOnDelete();
            $table->foreign('alt_motor_kontrol_user')->references('id')->on('users')->restrictOnDelete();
            $table->foreign('komponent_kontrol_user')->references('id')->on('users')->restrictOnDelete();
            $table->foreign('co2_kontrol_user')->references('id')->on('users')->restrictOnDelete();


            $table->index('expertise_id');
        });



        Schema::create('check_old_expertise_downloads', function (Blueprint $table) {
            $table->id();
            $table->string('download_uuid',60);
            $table->string('referral_uuid',60);
            $table->unsignedBigInteger('user_id');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('download_uuid')->references('uuid')->on('expertises')->cascadeOnDelete();
            $table->foreign('referral_uuid')->references('uuid')->on('expertises')->cascadeOnDelete();
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();
        });

        Schema::create('component_dyno', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->string('measured_kw')->nullable();
            $table->string('measured_hp')->nullable();
            $table->string('calculated_kw')->nullable();
            $table->string('calculated_hp')->nullable();
            $table->string('calculated_rpm')->nullable();
            $table->string('transfer_kw')->nullable();
            $table->string('transfer_hp')->nullable();
            $table->string('transfer_rpm')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_bodyworks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->unsignedBigInteger('expertise_definition_id');
            $table->boolean('orijinal')->default(0);
            $table->boolean('boyali')->default(0);
            $table->boolean('degisen')->default(0);
            $table->boolean('duz')->default(0);
            $table->text('note')->nullable();
            $table->timestamp('date')->nullable();
            $table->timestamps();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('expertise_definition_id')->references('id')->on('expertise_definitions')->restrictOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_bodywork_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->text('note');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_brakes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->timestamp('date')->nullable();
            $table->string('yanal_kayma_on',60)->nullable();
            $table->string('yanal_kayma_arka',60)->nullable();
            $table->string('max_kuvvet_on_1',60)->nullable();
            $table->string('max_kuvvet_on_2',60)->nullable();
            $table->string('max_kuvvet_arka_1',60)->nullable();
            $table->string('max_kuvvet_arka_2',60)->nullable();
            $table->string('dengesizlik_orani_on',60)->nullable();
            $table->string('dengesizlik_orani_arka',60)->nullable();
            $table->string('el_freni_dengesizlik_orani',60)->nullable();
            $table->string('yalpa_orani_on_1',60)->nullable();
            $table->string('yalpa_orani_on_2',60)->nullable();
            $table->string('yalpa_orani_arka_1',60)->nullable();
            $table->string('yalpa_orani_arka_2',60)->nullable();
            $table->string('suspansiyon_on_1',60)->nullable();
            $table->string('suspansiyon_on_2',60)->nullable();
            $table->string('suspansiyon_arka_1',60)->nullable();
            $table->string('suspansiyon_arka_2',60)->nullable();
            $table->string('on_dingil_bosta_a',60)->nullable();
            $table->string('on_dingil_bosta_b',60)->nullable();
            $table->string('arka_dingil_bosta_a',60)->nullable();
            $table->string('arka_dingil_bosta_b',60)->nullable();
            $table->string('el_freni_kuvvet_a',60)->nullable();
            $table->string('el_freni_kuvvet_b',60)->nullable();
            $table->enum('type',['auto','manuel'])->default('manuel')->nullable();
            $table->timestamps();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_brake_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_brake_id');
            $table->text('note');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_brake_id')->references('id')->on('expertise_brakes')->cascadeOnDelete();

            $table->index('expertise_brake_id');
        });

        Schema::create('expertise_co2', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->unsignedBigInteger('expertise_definition_id');
            $table->text('note')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('expertise_definition_id')->references('id')->on('expertise_definitions')->restrictOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_co2_note', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->text('note')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_components', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->unsignedBigInteger('expertise_definition_id');
            $table->string('answer',30)->nullable();
            $table->text('note')->nullable();
            $table->timestamps();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('expertise_definition_id')->references('id')->on('expertise_definitions')->restrictOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_component_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->text('note');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_diagnostics', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->text('note');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_diagnostic_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->text('note');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_internal_controls', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->unsignedBigInteger('expertise_definition_id');
            $table->boolean('sorunlu_mu')->default(0);
            $table->string('answer',30)->nullable();
            $table->text('note')->nullable();
            $table->timestamps();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('expertise_definition_id')->references('id')->on('expertise_definitions')->restrictOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_internal_controls_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->text('note');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_payments', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->float('amount',15,2)->default(0);
            $table->string('type',30)->nullable();
            $table->string('payment_code')->nullable();
            $table->string('payment_detail')->nullable();
            $table->unsignedBigInteger('plus_card_id')->nullable();
            $table->unsignedBigInteger('plus_card_odeme_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('plus_card_odeme_id')->references('id')->on('plus_card_credi_and_puan_add')->restrictOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_stocks', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id')->nullable();
            $table->unsignedBigInteger('stock_id')->nullable();
            $table->unsignedBigInteger('campaign_id')->nullable();
            $table->boolean('sorgu_hizmeti')->nullable();
            $table->boolean('yol_yardimi')->default(1)->nullable();
            $table->unsignedFloat('iskonto_amount',15,2)->nullable();
            $table->unsignedFloat('hizmet_tutari',15,2)->nullable();
            $table->unsignedFloat('liste_fiyati',15,2)->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('stock_id')->references('id')->on('stocks')->restrictOnDelete();
            $table->foreign('campaign_id')->references('id')->on('campaigns')->restrictOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_sub_control_and_engines', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->unsignedBigInteger('expertise_definition_id');
            $table->unsignedTinyInteger('status')->nullable();
            $table->text('note')->nullable();
            $table->timestamps();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('expertise_definition_id')->references('id')->on('expertise_definitions')->restrictOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_sub_control_and_engine_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->text('note');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_tire_and_rims', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->unsignedBigInteger('expertise_definition_id');
            $table->boolean('sorunlu_mu')->default(0);
            $table->text('note')->nullable();
            $table->float('dis',8,2)->nullable();
            $table->float('basinc',8,2)->nullable();
            $table->timestamps();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('expertise_definition_id')->references('id')->on('expertise_definitions')->restrictOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('expertise_tire_and_rim_notes', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->text('note');
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();

            $table->index('expertise_id');
        });

        Schema::create('hasarsorgu', function (Blueprint $table) {
            $table->string('sase',50);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expertise_tire_and_rim_notes', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('expertise_tire_and_rim_notes');

        Schema::table('expertise_tire_and_rims', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['expertise_definition_id']);
        });
        Schema::dropIfExists('expertise_tire_and_rims');

        Schema::table('expertise_sub_control_and_engine_notes', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('expertise_sub_control_and_engine_notes');

        Schema::table('expertise_sub_control_and_engines', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['expertise_definition_id']);
        });
        Schema::dropIfExists('expertise_sub_control_and_engines');

        Schema::table('expertise_stocks', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['stock_id']);
            $table->dropForeign(['campaign_id']);
        });
        Schema::dropIfExists('expertise_stocks');

        Schema::table('expertise_payments', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['plus_card_odeme_id']);
        });
        Schema::dropIfExists('expertise_payments');

        Schema::table('expertise_internal_controls_notes', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('expertise_internal_controls_notes');

        Schema::table('expertise_internal_controls', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['expertise_definition_id']);
        });
        Schema::dropIfExists('expertise_internal_controls');

        Schema::table('expertise_diagnostic_notes', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('expertise_diagnostic_notes');

        Schema::table('expertise_diagnostics', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('expertise_diagnostics');

        Schema::table('expertise_component_notes', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('expertise_component_notes');

        Schema::table('expertise_components', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['expertise_definition_id']);
        });
        Schema::dropIfExists('expertise_components');

        Schema::table('expertise_co2_note', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('expertise_co2_note');

        Schema::table('expertise_co2', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['expertise_definition_id']);
        });
        Schema::dropIfExists('expertise_co2');

        Schema::table('expertise_brake_notes', function (Blueprint $table) {
            $table->dropForeign(['expertise_brake_id']);
        });
        Schema::dropIfExists('expertise_brake_notes');

        Schema::table('expertise_brakes', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('expertise_brakes');

        Schema::table('expertise_bodywork_notes', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('expertise_bodywork_notes');

        Schema::table('expertise_bodyworks', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['expertise_definition_id']);
        });
        Schema::dropIfExists('expertise_bodyworks');

        Schema::table('component_dyno', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('component_dyno');

        Schema::table('check_old_expertise_downloads', function (Blueprint $table) {
            $table->dropForeign(['download_uuid']);
            $table->dropForeign(['referral_uuid']);
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('check_old_expertise_downloads');

        Schema::table('expertise_control_statuses', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['arac_kontrol_user']);
            $table->dropForeign(['fren_kontrol_user']);
            $table->dropForeign(['kaporta_kontrol_user']);
            $table->dropForeign(['diagnostic_kontrol_user']);
            $table->dropForeign(['ic_kontrol_user']);
            $table->dropForeign(['lastik_jant_kontrol_user']);
            $table->dropForeign(['alt_motor_kontrol_user']);
            $table->dropForeign(['komponent_kontrol_user']);
            $table->dropForeign(['co2_kontrol_user']);
        });
        Schema::dropIfExists('expertise_control_statuses');

        Schema::table('expertise_infos', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
        });
        Schema::dropIfExists('expertise_infos');

        Schema::table('expertises', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropForeign(['kayit_branch_id']);
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['cari_id']);
            $table->dropForeign(['satici_id']);
            $table->dropForeign(['alici_id']);
            $table->dropForeign(['car_id']);
        });
        Schema::dropIfExists('expertises');

        Schema::dropIfExists('hasarsorgu');
    }

};
