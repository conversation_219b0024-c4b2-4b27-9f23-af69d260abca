<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bodywork_coordinates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('expertise_id');
            $table->unsignedBigInteger('user_id');
            $table->json('coordinates')->nullable();
            $table->string('image_url',12);
            $table->smallInteger('image_client_width')->default(695);
            $table->smallInteger('image_client_height')->default(520);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('user_id')->references('id')->on('users')->cascadeOnDelete();

            $table->index('expertise_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bodywork_coordinates', function (Blueprint $table) {
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['user_id']);
        });
        Schema::dropIfExists('bodywork_coordinates');
    }
};
