<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bookings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('telephone',20);
            $table->string('plaka',20);
            $table->string('sase_no',17);
            $table->date('date');
            $table->time('hour');
            $table->string('type',10);
            $table->unsignedTinyInteger('form_onay')->default(1);
            $table->unsignedTinyInteger('sms_kabul')->default(1);
            $table->unsignedTinyInteger('yol_testi')->default(1);
            $table->tinyInteger('status')->default(2);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();
            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();

            $table->index('customer_id');
            $table->index('plaka');
            $table->index('sase_no');
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bookings', function (Blueprint $table) {
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['customer_id']);
        });
        Schema::dropIfExists('bookings');
    }
};
