<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('complaints', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id')->nullable();
            $table->string('talep_no',25);
            $table->string('ad_unvan')->nullable();
            $table->string('soyad')->nullable();
            $table->longText('musteri_ifadesi')->nullable();
            $table->unsignedTinyInteger('source_type')->default(0)->comment("0:normal | 1:şikayetvar");
            $table->unsignedTinyInteger('status')->default(2);
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();

            $table->index('talep_no');
        });

        Schema::create('complaint_other_infos', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('complaint_id');
            $table->unsignedBigInteger('branch_id')->nullable();
            $table->unsignedBigInteger('il_id')->nullable();
            $table->unsignedBigInteger('ilce_id')->nullable();
            $table->string('vergi_dairesi',50)->nullable();
            $table->unsignedBigInteger('tc_vergi_no')->nullable();
            $table->string('eposta',70)->nullable();
            $table->string('telefon',20)->nullable();
            $table->string('expertise_uuid',50)->nullable();
            $table->text('baslik')->nullable();
            $table->string('dosya')->nullable();
            $table->string('semt')->nullable();
            $table->string('mahalle')->nullable();
            $table->string('talep_nedeni')->nullable();
            $table->string('tur')->nullable();
            $table->string('yetkili_yanit')->nullable();
            $table->text('attachments')->nullable();
            $table->text('others')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('il_id')->references('id')->on('cities')->nullOnDelete();
            $table->foreign('ilce_id')->references('id')->on('towns')->nullOnDelete();
            $table->foreign('branch_id')->references('id')->on('branches')->cascadeOnDelete();
            $table->foreign('complaint_id')->references('id')->on('complaints')->cascadeOnDelete();
            $table->foreign('expertise_uuid')->references('uuid')->on('expertises');

            $table->index('complaint_id');
        });

        Schema::create('complaint_activities', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('complaint_id');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->text('message')->nullable();
            $table->string('message_id');
            $table->unsignedTinyInteger('source_type')->default(0)->comment("0:normal | 1:şikayetvar");
            $table->text('attachments')->nullable();
            $table->text('others')->nullable();
            $table->string('user_name')->nullable();
            $table->tinyInteger('status');

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('complaint_id')->references('id')->on('complaints')->cascadeOnDelete();

            $table->index('complaint_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('complaint_activities', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropForeign(['complaint_id']);
        });
        Schema::dropIfExists('complaint_activities');

        Schema::table('complaint_other_infos', function (Blueprint $table) {
            $table->dropForeign(['il_id']);
            $table->dropForeign(['ilce_id']);
            $table->dropForeign(['branch_id']);
            $table->dropForeign(['complaint_id']);
            $table->dropForeign(['expertise_uuid']);
        });
        Schema::dropIfExists('complaint_other_infos');

        Schema::table('complaints', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
        });
        Schema::dropIfExists('complaints');
    }

};
