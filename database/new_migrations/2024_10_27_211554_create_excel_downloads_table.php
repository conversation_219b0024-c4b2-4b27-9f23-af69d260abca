<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('excel_downloads', function (Blueprint $table) {
            $table->id();
            $table->string('type',40);
            $table->string('file_name',100);
            $table->string('code',10);
            $table->boolean('download_use')->default(0);
            $table->unsignedBigInteger('download_user_id')->nullable();
            $table->timestamps();

            $table->foreign('download_user_id')->references('id')->on('users')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('excel_downloads', function (Blueprint $table) {
            $table->dropForeign(['download_user_id']);
        });
        Schema::dropIfExists('excel_downloads');
    }

};
