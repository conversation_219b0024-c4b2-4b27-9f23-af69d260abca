<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_logs', function (Blueprint $table) {
            $table->id();
            $table->string('file_uuid')->nullable();
            $table->string('payment_id')->nullable();
            $table->unsignedFloat('payment_price');
            $table->string('payment_session_id')->nullable();
            $table->string('payment_token_id')->nullable();
            $table->longText('payment_req')->nullable();
            $table->longText('payment_res')->nullable();
            $table->unsignedTinyInteger('payment_type')->default(1)->comment('1 -) ekspertiz için 2-) plus card ödeme için');
            $table->integer('status')->default(0)->comment("0:<PERSON><PERSON><PERSON><PERSON><PERSON> || 1: Başarılı || 2:Başarısız");
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_logs');
    }
};
