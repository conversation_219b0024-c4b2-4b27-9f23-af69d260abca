<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('query_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->string('expertise_uuid',50);
            $table->string('type',20)->comment('hasar, degisen, borc, ruhsat, kilometre, detay');
            $table->text('result')->nullable();
            $table->unsignedFloat('amount')->nullable();
            $table->unsignedFloat('commission')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users');
            $table->foreign('expertise_uuid')->references('uuid')->on('expertises')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('query_logs', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropForeign(['expertise_uuid']);
        });
        Schema::dropIfExists('query_logs');
    }

};
