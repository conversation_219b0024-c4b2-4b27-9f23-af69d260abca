<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customer_buy_expertises', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->unsignedBigInteger('expertise_id');
            $table->unsignedBigInteger('payment_log_id');
            $table->boolean('status')->default(0);

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('customer_id')->references('id')->on('customers')->cascadeOnDelete();
            $table->foreign('expertise_id')->references('id')->on('expertises')->cascadeOnDelete();
            $table->foreign('payment_log_id')->references('id')->on('payment_logs')->cascadeOnDelete();

            $table->index('customer_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('customer_buy_expertises', function (Blueprint $table) {
            $table->dropForeign(['customer_id']);
            $table->dropForeign(['expertise_id']);
            $table->dropForeign(['payment_log_id']);
        });
        Schema::dropIfExists('customer_buy_expertises');
    }
};
