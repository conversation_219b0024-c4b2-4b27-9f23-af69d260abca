<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\BranchCategory;

class BranchCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        BranchCategory::truncate();

        $categories = [
            ['name' => 'A', 'status' => 1, 'sort' => 1],
            ['name' => 'B', 'status' => 1, 'sort' => 2],
            ['name' => 'C', 'status' => 1, 'sort' => 3],
            ['name' => 'D', 'status' => 1, 'sort' => 4],
            ['name' => 'E', 'status' => 1, 'sort' => 5],
        ];

        foreach ($categories as $category) {
            BranchCategory::create($category);
        }
    }
}
