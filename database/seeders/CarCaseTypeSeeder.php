<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\CarCaseType;

class CarCaseTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Define the car case types to be seeded
        $carCaseTypes = [
            [
            'name' => 'Pick-Up (Çift Kabin)',
            'image' => 'car_case_type/1.jpg',
            'bodywork_open_image' => 'car_case_type/XAG8yxlaFefxPuEqplZevnK2WeCuN2EJqq7nNt8b.png',
            'status' => 1,
            'created_at' => '2023-12-11 04:29:16',
            'updated_at' => '2024-05-14 09:29:32',
            ],
            [
            'name' => 'Pick-Up (Ç<PERSON>)',
            'image' => 'car_case_type/2.jpg',
            'bodywork_open_image' => 'car_case_type/XAG8yxlaFefxPuEqplZevnK2WeCuN2EJqq7nNt8b.png',
            'status' => -1,
            'created_at' => '2023-12-11 04:41:18',
            'updated_at' => '2024-02-16 10:01:05',
            ],
            [
            'name' => 'Otobüs',
            'image' => 'car_case_type/3.jpg',
            'bodywork_open_image' => 'car_case_type/jQJwoFBYEnTwbSgLNZT8aGUgX8kwvGyWSzYEynTj.png',
            'status' => 1,
            'created_at' => '2023-12-11 04:42:05',
            'updated_at' => '2024-03-16 18:08:13',
            ],
            [
            'name' => 'Sedan',
            'image' => 'car_case_type/4.jpg',
            'bodywork_open_image' => 'car_case_type/tjTbyGZNBGFuiNZ3z4LXvOAmAI4faT0FjlIK8QXe.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:35',
            'updated_at' => '2024-03-16 18:08:30',
            ],
            [
            'name' => 'Hatchback 5 Kapı',
            'image' => 'car_case_type/5.jpg',
            'bodywork_open_image' => 'car_case_type/eEPqMui0igqom1d2CNB53yjn2pF13REUT6oTAvj3.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:35',
            'updated_at' => '2024-03-16 18:08:39',
            ],
            [
            'name' => 'Minivan',
            'image' => 'car_case_type/6.jpg',
            'bodywork_open_image' => 'car_case_type/priw4uTvyDEk2emOXiNvv2UEAtmwulrEIguzBamm.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:35',
            'updated_at' => '2024-03-16 18:08:46',
            ],
            [
            'name' => 'Arazi/SUV (5 Kapı)',
            'image' => 'car_case_type/7.jpg',
            'bodywork_open_image' => 'car_case_type/RIBUmBf3DAEGePbDrH90OQRMoZWFyjTQYSsMwR9c.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:35',
            'updated_at' => '2024-03-16 18:09:08',
            ],
            [
            'name' => 'Kamyonet (Tek Kabin)',
            'image' => 'car_case_type/8.jpg',
            'bodywork_open_image' => 'car_case_type/tcoPfAOD1jZ46WX1qPSogZ4Cyc62wkp0hPHXBq2n.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:35',
            'updated_at' => '2024-03-16 18:09:17',
            ],
            [
            'name' => 'Panelvan / Minibüs',
            'image' => 'car_case_type/9.jpg',
            'bodywork_open_image' => 'car_case_type/FOrg0Xn5lXb3FCw9hbRMN7QlCR7fzUM3FOes4Awt.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:35',
            'updated_at' => '2024-03-16 18:09:24',
            ],
            [
            'name' => 'Station Wagon',
            'image' => 'car_case_type/10.jpg',
            'bodywork_open_image' => 'car_case_type/MMqDQdbbGU4jxDskg5skZQA25hP4244uP4vtiBLi.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:35',
            'updated_at' => '2024-03-16 18:09:31',
            ],
            [
            'name' => 'Hatchback 3 Kapı',
            'image' => 'car_case_type/11.jpg',
            'bodywork_open_image' => 'car_case_type/drbmUS5qF6iqJW2EDnyfnqPXMvleTr9sf3DFZReK.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:35',
            'updated_at' => '2024-03-16 18:09:38',
            ],
            [
            'name' => 'Coupe',
            'image' => 'car_case_type/16_2.jpg',
            'bodywork_open_image' => 'car_case_type/OdGcKU3DWChXhpuFiKyk9n3yqMlI9uvthRrk0Tm6.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:35',
            'updated_at' => '2024-03-16 18:09:46',
            ],
            [
            'name' => 'Kamyonet (Çift Kabin)',
            'image' => 'car_case_type/13.jpg',
            'bodywork_open_image' => 'car_case_type/7sX1bNNSxc5sePPZ6VN3pUkjINMHeZrNtqsWql9m.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:35',
            'updated_at' => '2024-03-16 18:09:53',
            ],
            [
            'name' => 'Arazi/Suv (3 Kapı)',
            'image' => 'car_case_type/14.jpg',
            'bodywork_open_image' => 'car_case_type/Ldr0pTVp5IvFicMF1EgMDgEdtytN5E4JC60ASDET.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:36',
            'updated_at' => '2024-03-16 18:10:00',
            ],
            [
            'name' => 'Pick-Up (Tek Kabin)',
            'image' => 'car_case_type/15.jpg',
            'bodywork_open_image' => 'car_case_type/dI7M0C8sRFQwSExcj6wk3KSp2PzH2Pakn0q7KAtg.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:08:37',
            'updated_at' => '2024-05-14 09:28:31',
            ],
            [
            'name' => 'Cabrio',
            'image' => 'car_case_type/16_2.jpg',
            'bodywork_open_image' => 'car_case_type/AMEMdXcMWa9BzU0MBCnNZhiLpUQgaS1rtLak9Slm.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:09:35',
            'updated_at' => '2024-06-12 14:17:42',
            ],
            [
            'name' => 'KAMYONET',
            'image' => 'car_case_type/17.jpg',
            'bodywork_open_image' => 'car_case_type/bK7oDO0b2AodvmPdZrAMfUw9T0zicd3gQ57Npkf0.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:10:17',
            'updated_at' => '2024-03-16 18:10:25',
            ],
            [
            'name' => '5 KAPI',
            'image' => 'car_case_type/18.jpg',
            'bodywork_open_image' => 'car_case_type/jVGFxm5lqWnxEWxhfRWu4UImMe5azOqM9pyqbpsw.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:11:32',
            'updated_at' => '2024-03-16 18:10:30',
            ],
            [
            'name' => 'Kamyon / TIR Çekici',
            'image' => 'car_case_type/19.jpg',
            'bodywork_open_image' => 'car_case_type/nNZFCRryCPZp2YOicGDmUZaZPAneQVPtQhZtO71y.png',
            'status' => 1,
            'created_at' => '2024-02-10 14:11:36',
            'updated_at' => '2024-03-16 18:10:58',
            ],
            [
            'name' => 'mını',
            'image' => 'car_case_type/63B0nIInjKArhfcR7cV74qntc3PzU3IMqXy37kQ9.jpg',
            'bodywork_open_image' => null,
            'status' => -1,
            'created_at' => '2024-02-10 14:23:26',
            'updated_at' => '2024-03-16 18:02:36',
            ],
            [
            'name' => 'kamyonet',
            'image' => 'car_case_type/ZchXUijlVbwI7AS7g8AqOL7GlBL1g6Y6NqFxgGHQ.jpg',
            'bodywork_open_image' => null,
            'status' => -1,
            'created_at' => null,
            'updated_at' => '2024-03-16 18:02:42',
            ],
            [
            'name' => 'kamyonet',
            'image' => 'car_case_type/4PTO6uOPk8J7gzBkHJh2dYD19U51ByQF5PONHc55.jpg',
            'bodywork_open_image' => null,
            'status' => -1,
            'created_at' => null,
            'updated_at' => '2024-03-16 18:03:05',
            ],
            [
            'name' => 'coupe',
            'image' => 'car_case_type/1yRxVox7K6IP6cjBVfToR2QV4STLQRGbGMNwJHNf.jpg',
            'bodywork_open_image' => null,
            'status' => -1,
            'created_at' => null,
            'updated_at' => '2024-03-16 18:04:15',
            ],
            [
            'name' => 'coupe',
            'image' => 'car_case_type/YuNzhZjkzB3PtAZKYrB81Syt50vUctbsqswEVDI6.jpg',
            'bodywork_open_image' => null,
            'status' => -1,
            'created_at' => null,
            'updated_at' => '2024-03-16 18:04:05',
            ],
            [
            'name' => 'Sedan AA',
            'image' => 'car_case_type/uqhWwvareEPIfqoDjiuiit98PYf30IsRds8A1AJy.jpg',
            'bodywork_open_image' => 'car_case_type/tjTbyGZNBGFuiNZ3z4LXvOAmAI4faT0FjlIK8QXe.png',
            'status' => -1,
            'created_at' => '2024-03-16 00:34:50',
            'updated_at' => '2024-03-27 14:13:59',
            ],
        ];
        foreach ($carCaseTypes as $carCaseType) {
            CarCaseType::create($carCaseType);
        }

    }
}
