<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\CarGear;

class CarGearSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $carGears = [
            [
                'name' => 'Manuel',
                'status' => 1,
                'created_at' => '2023-12-11 04:53:27',
                'updated_at' => '2024-03-18 08:30:27'
            ],
            [
                'name' => 'Yarı Otomatik',
                'status' => 1,
                'created_at' => '2023-12-11 04:53:40',
                'updated_at' => '2023-12-11 04:53:40'
            ],
            [
                'name' => 'Otomatik',
                'status' => 1,
                'created_at' => '2023-12-11 04:53:44',
                'updated_at' => '2023-12-11 04:53:44'
            ]
        ];

        foreach ($carGears as $carGear) {
            CarGear::create($carGear);
        }
    }
}
