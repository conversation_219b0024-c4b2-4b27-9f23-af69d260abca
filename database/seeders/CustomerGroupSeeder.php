<?php

namespace Database\Seeders;

use App\Models\CustomerGroup;
use Illuminate\Database\Seeder;

class CustomerGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $groups = [
            [
                'kod' => '11.000.000',
                'parent_id' => 0,
                'name' => 'Kurumsal',
                'status' => 1,
                'created_at' => '2023-12-13 04:07:09',
                'updated_at' => '2023-12-13 04:07:09'
            ],
            [
                'kod' => '11.000.000',
                'parent_id' => 1,
                'name' => 'Ticari',
                'status' => 1,
                'created_at' => '2023-12-13 04:07:27',
                'updated_at' => '2023-12-13 04:07:44'
            ],
            [
                'kod' => '11.000.000',
                'parent_id' => 2,
                'name' => 'Ticari',
                'status' => 1,
                'created_at' => '2023-12-13 04:09:02',
                'updated_at' => '2023-12-13 04:09:02'
            ],
        ];

        foreach ($groups as $group) {
            CustomerGroup::create($group);
        }
    }
}
