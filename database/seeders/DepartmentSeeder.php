<?php

namespace Database\Seeders;

use App\Models\Department;
use Illuminate\Database\Seeder;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Department::truncate();

        $departments = [
            ['title' => 'yonetim', 'description' => 'Yönetim', 'status' => "active", 'sort' => 1],
            ['title' => 'genel_personel', 'description' => 'Genel Yetkili Personel', 'status' => "active", 'sort' => 2],
            ['title' => 'arac_kontrol', 'description' => 'Araç Kontrol Personeli', 'status' => "active", 'sort' => 3],
            ['title' => 'kaporta_kontrol', 'description' => 'Kaporta Kontrol Personeli', 'status' => "active", 'sort' => 4],
            ['title' => 'motor_mekanik_kontrol', 'description' => 'Motor Mekanik Personeli', 'status' => "active", 'sort' => 5],
            ['title' => 'personel', 'description' => 'Personel', 'status' => "active", 'sort' => 6],
        ];

        foreach ($departments as $department) {
            Department::create($department);
        }
    }
}
