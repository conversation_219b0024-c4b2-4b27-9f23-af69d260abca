<?php

namespace Database\Seeders;

use App\Models\PlusCardCampaign;
use App\Services\PlusCardCampaignService;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class PlusCardCampaignSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        PlusCardCampaign::truncate();

        $campaigns = [
            [
                'title' => 'Yaz Kampanyası',
                'description' => '16 Mayıs\'a kadar tüm bakımlarda %10 indirim',
                'start_date' => now(),
                'end_date' => Carbon::createFromDate(2025, 5, 16),
                'discount_type' => 'percentage',
                'discount_value' => 10,
                'minimum_purchase_amount' => 500,
                'is_single_use' => true,
                'status' => 'active',
                'created_by' => 1,
                'is_visible_to_all' => true
            ],
        ];

        $campaignService = new PlusCardCampaignService();

        foreach ($campaigns as $campaign) {
            $campaignService->createCampaign($campaign);
        }
    }
}
