<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PlusCardDefinitionPrice;

class PlusCardDefinitionPriceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        PlusCardDefinitionPrice::truncate();
        
        $plusCardDefinitionPrices = [
            [
                'definition_id' => 10,
                'min_count' => 10,
                'price' => 1900.00,
                'created_at' => '2024-05-14 18:43:53',
                'updated_at' => '2024-09-30 20:57:46',
                'deleted_at' => '2024-09-30 20:57:46',
            ],
            [
                'definition_id' => 10,
                'min_count' => 20,
                'price' => 1800.00,
                'created_at' => '2024-05-14 18:43:53',
                'updated_at' => '2024-09-30 20:57:46',
                'deleted_at' => '2024-09-30 20:57:46',
            ],
            [
                'definition_id' => 10,
                'min_count' => 30,
                'price' => 1700.00,
                'created_at' => '2024-05-14 18:43:53',
                'updated_at' => '2024-09-30 20:57:46',
                'deleted_at' => '2024-09-30 20:57:46',
            ],
            [
                'definition_id' => 10,
                'min_count' => 10,
                'price' => 1900.00,
                'created_at' => '2024-09-30 20:57:46',
                'updated_at' => '2024-09-30 20:57:50',
                'deleted_at' => '2024-09-30 20:57:50',
            ],
            [
                'definition_id' => 10,
                'min_count' => 20,
                'price' => 1800.00,
                'created_at' => '2024-09-30 20:57:46',
                'updated_at' => '2024-09-30 20:57:50',
                'deleted_at' => '2024-09-30 20:57:50',
            ],
            [
                'definition_id' => 10,
                'min_count' => 30,
                'price' => 1700.00,
                'created_at' => '2024-09-30 20:57:46',
                'updated_at' => '2024-09-30 20:57:50',
                'deleted_at' => '2024-09-30 20:57:50',
            ],
            [
                'definition_id' => 10,
                'min_count' => 10,
                'price' => 1900.00,
                'created_at' => '2024-09-30 20:57:50',
                'updated_at' => '2024-09-30 20:57:50',
                'deleted_at' => null,
            ],
            [
                'definition_id' => 10,
                'min_count' => 20,
                'price' => 1800.00,
                'created_at' => '2024-09-30 20:57:50',
                'updated_at' => '2024-09-30 20:57:50',
                'deleted_at' => null,
            ],
            [
                'definition_id' => 10,
                'min_count' => 30,
                'price' => 1700.00,
                'created_at' => '2024-09-30 20:57:50',
                'updated_at' => '2024-09-30 20:57:50',
                'deleted_at' => null,
            ]
        ];
        
        foreach ($plusCardDefinitionPrices as $plusCardDefinitionPrice) {
            PlusCardDefinitionPrice::create($plusCardDefinitionPrice);
        }
    }
}
