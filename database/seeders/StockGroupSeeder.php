<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\StockGroup;

class StockGroupSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        StockGroup::truncate();
        
        $stockGroups = [
            [
                'stock_type_id' => 1,
                'kod' => '11.000.000',
                'parent_id' => 0,
                'name' => 'Personel',
                'status' => 1,
                'created_at' => '2023-12-13 05:39:31',
                'updated_at' => '2024-02-14 20:14:38',
            ],
            [
                'stock_type_id' => 2,
                'kod' => '11.100.000',
                'parent_id' => 1,
                'name' => 'Personel Ücretleri',
                'status' => 1,
                'created_at' => '2023-12-13 05:39:51',
                'updated_at' => '2024-02-15 21:28:01',
            ],
            [
                'stock_type_id' => 3,
                'kod' => '11.100.100',
                'parent_id' => 2,
                'name' => 'Maaş',
                'status' => 1,
                'created_at' => '2023-12-13 05:40:34',
                'updated_at' => '2023-12-26 12:41:12',
            ],
            [
                'stock_type_id' => 4,
                'kod' => '11.100.200',
                'parent_id' => 2,
                'name' => 'Personel Ek Ödemeler',
                'status' => 1,
                'created_at' => '2023-12-13 05:40:52',
                'updated_at' => '2023-12-26 12:41:12',
            ],
        ];

        foreach ($stockGroups as $stockGroup) {
            \App\Models\StockGroup::create($stockGroup);
        }
    }
}
