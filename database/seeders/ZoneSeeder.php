<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Zone;

class ZoneSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Zone::truncate();

        $zones = [
            [
                'name' => 'Marmara',
                'status' => -1,
                'created_at' => '2024-01-12 18:24:08',
                'updated_at' => '2024-03-13 13:33:30',
            ],
            [
                'name' => 'Türkiye',
                'status' => -1,
                'created_at' => '2024-01-12 18:55:57',
                'updated_at' => '2024-03-13 13:33:33',
            ],
            [
                'name' => '<PERSON><PERSON><PERSON><PERSON> Bölge',
                'status' => -1,
                'created_at' => '2024-02-07 11:40:02',
                'updated_at' => '2024-03-13 13:33:36',
            ],
            [
                'name' => '2. <PERSON><PERSON><PERSON><PERSON> "Korgün YILMAZ"',
                'status' => 1,
                'created_at' => '2024-03-13 13:36:56',
                'updated_at' => '2025-01-06 11:29:57',
            ],
            [
                'name' => '3. <PERSON><PERSON><PERSON><PERSON> "Ersin GEMİCİ"',
                'status' => 1,
                'created_at' => '2024-03-13 15:11:46',
                'updated_at' => '2025-01-06 11:30:30',
            ],
            [
                'name' => '4. BÖLGE "Necmi KOCAELİ"',
                'status' => 1,
                'created_at' => '2024-03-13 15:28:38',
                'updated_at' => '2025-01-06 11:30:59',
            ],
            [
                'name' => '5. BÖLGE "Mehmet ÇAKMAK"',
                'status' => 1,
                'created_at' => '2024-03-13 15:30:48',
                'updated_at' => '2025-01-06 11:31:29',
            ],
            [
                'name' => '8. BÖLGE "Ali DEMİRCİOĞLU"',
                'status' => 1,
                'created_at' => '2024-03-13 15:32:18',
                'updated_at' => '2025-01-06 11:32:01',
            ],
            [
                'name' => '6. BÖLGE "Basri DEMİR"',
                'status' => 1,
                'created_at' => '2024-03-13 15:35:31',
                'updated_at' => '2025-01-07 06:56:59',
            ],
            [
                'name' => '1. BÖLGE "Mehmet OLKUN"',
                'status' => 1,
                'created_at' => '2025-01-03 06:27:03',
                'updated_at' => '2025-01-06 11:33:13',
            ],
            [
                'name' => '7. BÖLGE "Veysi SOYLU"',
                'status' => 1,
                'created_at' => '2025-01-03 06:38:33',
                'updated_at' => '2025-01-06 10:57:48',
            ],
        ];

        foreach ($zones as $zone) {
           Zone::create($zone);
        }
    }
}
