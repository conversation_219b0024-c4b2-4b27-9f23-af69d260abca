-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1-1.el8.remi
-- https://www.phpmyadmin.net/
--
-- Host: localhost
-- Generation Time: Jun 26, 2025 at 01:10 PM
-- Server version: 10.11.8-MariaDB-log
-- PHP Version: 7.2.24

SET FOREIGN_KEY_CHECKS=0;
SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `umramdb`
--
CREATE DATABASE IF NOT EXISTS `umramdb` DEFAULT CHARACTER SET latin1 COLLATE latin1_swedish_ci;
USE `umramdb`;

-- --------------------------------------------------------

--
-- Table structure for table `bodywork_coordinates`
--

CREATE TABLE IF NOT EXISTS `bodywork_coordinates` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` bigint(20) UNSIGNED NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `coordinates` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`coordinates`)),
    `image_url` varchar(12) NOT NULL,
    `image_client_width` smallint(5) UNSIGNED NOT NULL DEFAULT 695,
    `image_client_height` smallint(5) UNSIGNED NOT NULL DEFAULT 520,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `bodywork_coordinates_expertise_id_index` (`expertise_id`),
    KEY `bodywork_coordinates_user_id_index` (`user_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE IF NOT EXISTS `bookings` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `branch_id` int(11) NOT NULL,
    `customer_id` int(11) NOT NULL,
    `telephone` varchar(20) DEFAULT NULL,
    `plaka` varchar(17) DEFAULT NULL,
    `sase_no` varchar(17) DEFAULT NULL,
    `date` date DEFAULT NULL,
    `hour` time DEFAULT NULL,
    `type` varchar(20) DEFAULT 'normal',
    `form_onay` tinyint(4) DEFAULT 1,
    `sms_kabul` tinyint(4) DEFAULT 1,
    `yol_testi` tinyint(4) DEFAULT 0,
    `status` tinyint(4) DEFAULT 2,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `branches`
--

CREATE TABLE IF NOT EXISTS `branches` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `kod` varchar(10) DEFAULT NULL,
    `belge_kod` varchar(4) DEFAULT NULL,
    `category_id` bigint(20) UNSIGNED DEFAULT NULL,
    `kisa_ad` varchar(255) DEFAULT NULL,
    `unvan` text DEFAULT NULL,
    `ad` varchar(255) DEFAULT NULL,
    `soyad` varchar(255) DEFAULT NULL,
    `mahalle` varchar(255) DEFAULT NULL,
    `cadde` varchar(255) DEFAULT NULL,
    `sokak` varchar(255) DEFAULT NULL,
    `semt` varchar(255) DEFAULT NULL,
    `il_kodu` int(11) DEFAULT NULL,
    `ilce_kodu` smallint(6) DEFAULT NULL,
    `lat` varchar(255) DEFAULT NULL,
    `lng` varchar(255) DEFAULT NULL,
    `konum` varchar(255) DEFAULT NULL,
    `telefon` varchar(255) DEFAULT NULL,
    `whatsapp_telefon` varchar(255) DEFAULT NULL,
    `fax` varchar(255) DEFAULT NULL,
    `gsm` varchar(255) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `web` varchar(255) DEFAULT NULL,
    `google_button_1` varchar(255) DEFAULT NULL,
    `google_button_2` varchar(255) DEFAULT NULL,
    `vergi_dairesi` varchar(255) DEFAULT NULL,
    `vergi_no` varchar(255) DEFAULT NULL,
    `tc_no` varchar(255) DEFAULT NULL,
    `fatura_form_adi` varchar(255) DEFAULT NULL,
    `makbuz_form_adi` varchar(255) DEFAULT NULL,
    `cek_form_adi` varchar(255) DEFAULT NULL,
    `senet_form_adi` varchar(255) DEFAULT NULL,
    `logo` varchar(255) DEFAULT NULL,
    `sozlesme` varchar(255) DEFAULT NULL,
    `sozlesme_baslangic_tarihi` date DEFAULT NULL,
    `sozlesme_bitis_tarihi` date DEFAULT NULL,
    `sozlesme_yil` int(11) DEFAULT NULL,
    `sube_hesabi` varchar(255) DEFAULT NULL,
    `plus_kart_satis` varchar(255) DEFAULT NULL,
    `eksper_merkez` varchar(255) DEFAULT NULL,
    `eksper_orani` varchar(255) DEFAULT NULL,
    `hasar_sorgulama` int(11) DEFAULT 0,
    `plus_kart_yukle` int(11) DEFAULT 0,
    `son_fiyat_degisiklik_tarihi` date DEFAULT NULL,
    `bedelsiz_eksper` int(11) DEFAULT NULL,
    `max_indirim_tutari` double(15,2) DEFAULT NULL,
    `borc_risk_tutari` double(15,2) DEFAULT NULL,
    `cari_basi_max_indirim` float(15,2) DEFAULT NULL,
    `max_indirim_orani` int(11) DEFAULT NULL,
    `yetkili_ad_soyad` varchar(255) DEFAULT NULL,
    `yetkili_gsm` varchar(255) DEFAULT NULL,
    `yetkili_dahili` varchar(255) DEFAULT NULL,
    `efatura_key` varchar(50) DEFAULT NULL,
    `paynet_code` varchar(10) DEFAULT '1697',
    `kep_mail` varchar(255) DEFAULT NULL,
    `mersis_no` varchar(255) DEFAULT NULL,
    `iskonto_tip` tinyint(4) DEFAULT 1,
    `ip_addresses` text DEFAULT NULL,
    `sleep_time` smallint(6) DEFAULT 0,
    `max_cari_kod` int(11) DEFAULT 0,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `brake_values_auto` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
    `dyno_values_auto` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
    `new_booking` tinyint(1) DEFAULT 1,
    PRIMARY KEY (`id`),
    KEY `id` (`id`),
    KEY `kod` (`kod`),
    KEY `kisa_ad` (`kisa_ad`),
    KEY `belge_kod` (`belge_kod`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `branch_categories`
--

CREATE TABLE IF NOT EXISTS `branch_categories` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `status` tinyint(1) NOT NULL DEFAULT 1,
    `sort` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `branch_category_user_limits`
--

CREATE TABLE IF NOT EXISTS `branch_category_user_limits` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `category_id` bigint(20) UNSIGNED NOT NULL,
    `group_id` bigint(20) UNSIGNED NOT NULL,
    `qty` int(11) NOT NULL DEFAULT 0,
    `status` tinyint(1) NOT NULL DEFAULT 1,
    `sort` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `branch_category_user_limits_category_id_foreign` (`category_id`),
    KEY `branch_category_user_limits_group_id_foreign` (`group_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `branch_partners`
--

CREATE TABLE IF NOT EXISTS `branch_partners` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `branch_id` int(11) NOT NULL,
    `user_id` int(11) NOT NULL,
    `percent` double(7,2) NOT NULL DEFAULT 100.00,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `branch_saved_filters`
--

CREATE TABLE IF NOT EXISTS `branch_saved_filters` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` mediumint(9) NOT NULL,
    `name` varchar(255) NOT NULL,
    `unvan` varchar(255) DEFAULT NULL,
    `kisa_ad` varchar(255) DEFAULT NULL,
    `kod` varchar(255) DEFAULT NULL,
    `il_kodu` varchar(255) DEFAULT NULL,
    `ilce_kodu` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `branch_tses`
--

CREATE TABLE IF NOT EXISTS `branch_tses` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `branch_id` int(11) NOT NULL,
    `tse_kodu` varchar(255) DEFAULT NULL,
    `gecerlilik_tarihi` date DEFAULT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bulletins`
--

CREATE TABLE IF NOT EXISTS `bulletins` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `no` varchar(100) NOT NULL,
    `slug` varchar(255) NOT NULL,
    `title` varchar(100) NOT NULL,
    `publish_type` int(10) UNSIGNED NOT NULL DEFAULT 0,
    `content_type` enum('text','html','file') NOT NULL DEFAULT 'file',
    `content` longtext DEFAULT NULL,
    `file` varchar(255) DEFAULT NULL,
    `status` enum('active','inactive') NOT NULL DEFAULT 'inactive',
    `priority_status` enum('normal','important','urgent','critical') NOT NULL DEFAULT 'normal',
    `visibility` enum('public','private') NOT NULL DEFAULT 'public',
    `is_approved` enum('pending','approved') NOT NULL DEFAULT 'approved',
    `publish_at` datetime NOT NULL DEFAULT current_timestamp(),
    `user_id` bigint(20) UNSIGNED DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `bulletins_no_unique` (`no`),
    UNIQUE KEY `bulletins_slug_unique` (`slug`) USING HASH,
    KEY `bulletins_user_id_foreign` (`user_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `bulletin_user_reads`
--

CREATE TABLE IF NOT EXISTS `bulletin_user_reads` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `bulletin_id` bigint(20) UNSIGNED NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `read_at` timestamp NOT NULL DEFAULT current_timestamp(),
    `is_read` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `bulletin_user_reads_bulletin_id_user_id_unique` (`bulletin_id`,`user_id`),
    KEY `bulletin_user_reads_user_id_foreign` (`user_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `campaigns`
--

CREATE TABLE IF NOT EXISTS `campaigns` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `branch_id` int(11) NOT NULL,
    `name` text NOT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `branch_id` (`branch_id`),
    KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cars`
--

CREATE TABLE IF NOT EXISTS `cars` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `branch_id` int(11) DEFAULT NULL,
    `customer_id` int(11) DEFAULT 0,
    `plaka` varchar(255) DEFAULT NULL,
    `sase_no` varchar(255) DEFAULT NULL,
    `car_group_tip_id` int(11) DEFAULT NULL,
    `car_group_marka_id` int(11) DEFAULT NULL,
    `car_group_model_id` int(11) DEFAULT NULL,
    `car_case_type_id` int(11) DEFAULT NULL,
    `car_fuels_id` int(11) DEFAULT NULL,
    `car_gears_id` int(11) DEFAULT NULL,
    `motor_hacmi` varchar(255) DEFAULT NULL,
    `model_yili` int(11) DEFAULT NULL,
    `cekis` varchar(255) DEFAULT NULL,
    `km` varchar(255) DEFAULT NULL,
    `km_type` tinyint(4) NOT NULL DEFAULT 1,
    `il_kodu` varchar(255) DEFAULT NULL,
    `ilce_kodu` varchar(255) DEFAULT NULL,
    `ilk_tescil_tarihi` date DEFAULT NULL,
    `tescil_sira_no` varchar(255) DEFAULT NULL,
    `tescil_tarihi` date DEFAULT NULL,
    `cinsi` varchar(255) DEFAULT NULL,
    `renk` varchar(255) DEFAULT NULL,
    `motor_no` varchar(255) DEFAULT NULL,
    `net_agirlik` varchar(255) DEFAULT NULL,
    `azami_agirlik` varchar(255) DEFAULT NULL,
    `katar_agirlik` varchar(255) DEFAULT NULL,
    `romork_agirlik` varchar(255) DEFAULT NULL,
    `koltuk_sayisi` varchar(255) DEFAULT NULL,
    `ayakta_yolcu_sayisi` varchar(255) DEFAULT NULL,
    `silindir_hacmi` varchar(255) DEFAULT NULL,
    `motor_gucu` varchar(255) DEFAULT NULL,
    `guc_agirlik_orani` varchar(255) DEFAULT NULL,
    `kullanim_amaci` varchar(255) DEFAULT NULL,
    `tip_onay_no` varchar(255) DEFAULT NULL,
    `status` int(11) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `UQ` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `branch_id` (`branch_id`),
    KEY `customer_id` (`customer_id`),
    KEY `UQ` (`UQ`),
    KEY `plaka` (`plaka`) USING BTREE,
    KEY `sase_no` (`sase_no`) USING BTREE,
    KEY `id` (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `car_case_types`
--

CREATE TABLE IF NOT EXISTS `car_case_types` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `image` varchar(255) DEFAULT NULL,
    `bodywork_open_image` varchar(70) DEFAULT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `car_fuels`
--

CREATE TABLE IF NOT EXISTS `car_fuels` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `car_gears`
--

CREATE TABLE IF NOT EXISTS `car_gears` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `car_groups`
--

CREATE TABLE IF NOT EXISTS `car_groups` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `parent_id` int(11) NOT NULL DEFAULT 0,
    `name` varchar(255) NOT NULL,
    `code` varchar(12) DEFAULT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `version` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `id` (`id`),
    KEY `parent_id` (`parent_id`),
    KEY `name` (`name`),
    KEY `code` (`code`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `car_saved_filters`
--

CREATE TABLE IF NOT EXISTS `car_saved_filters` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` mediumint(9) NOT NULL,
    `name` varchar(255) NOT NULL,
    `plaka` varchar(255) DEFAULT NULL,
    `sase_no` varchar(255) DEFAULT NULL,
    `customer_name` varchar(255) DEFAULT NULL,
    `branch_id` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `car_types`
--

CREATE TABLE IF NOT EXISTS `car_types` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `check_list_controls`
--

CREATE TABLE IF NOT EXISTS `check_list_controls` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) DEFAULT NULL,
    `note` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `check_old_expertis_dowlands`
--

CREATE TABLE IF NOT EXISTS `check_old_expertis_dowlands` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `dowland_uuid` char(255) DEFAULT NULL,
    `referral_uuid` char(255) DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cities`
--

CREATE TABLE IF NOT EXISTS `cities` (
    `id` int(11) NOT NULL,
    `title` varchar(255) NOT NULL,
    `created_at` timestamp NULL DEFAULT current_timestamp(),
    `updated_at` timestamp NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `title` (`title`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `complaints`
--

CREATE TABLE IF NOT EXISTS `complaints` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `talep_no` varchar(12) NOT NULL,
    `customer_id` int(10) UNSIGNED NOT NULL DEFAULT 0,
    `telep_kullanici` varchar(55) DEFAULT NULL,
    `ad_unvan` varchar(255) NOT NULL,
    `soyad` varchar(255) DEFAULT NULL,
    `vergi_dairesi` varchar(255) DEFAULT NULL,
    `tc_vergi_no` varchar(11) NOT NULL,
    `eposta` varchar(255) NOT NULL,
    `telefon` varchar(255) NOT NULL,
    `expertise_uuid` varchar(50) DEFAULT NULL,
    `baslik` varchar(255) DEFAULT NULL,
    `dosya` varchar(70) DEFAULT NULL,
    `branch_id` varchar(255) NOT NULL,
    `il_id` varchar(255) NOT NULL,
    `ilce_id` varchar(255) NOT NULL,
    `semt` varchar(255) NOT NULL,
    `mahalle` varchar(255) NOT NULL,
    `talep_nedeni` varchar(255) DEFAULT NULL,
    `tur` varchar(50) DEFAULT NULL,
    `musteri_ifadesi` text DEFAULT NULL,
    `yetkili_yanit` text DEFAULT NULL,
    `source_type` int(11) DEFAULT 0 COMMENT '0: Normal || 1: Şikayet Var',
    `attachments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attachments`)),
    `others` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`others`)),
    `status` tinyint(4) NOT NULL DEFAULT 2,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_uuid` (`expertise_uuid`),
    KEY `talep_no` (`talep_no`),
    KEY `customer_id` (`customer_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `complaint_activities`
--

CREATE TABLE IF NOT EXISTS `complaint_activities` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `complaint_id` int(10) UNSIGNED NOT NULL,
    `user_id` int(10) UNSIGNED NOT NULL,
    `message` text NOT NULL,
    `message_id` int(11) DEFAULT 0,
    `source_type` int(11) DEFAULT 0 COMMENT '0: Normal || 1: Şikayet Var',
    `attachments` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`attachments`)),
    `user_name` varchar(255) DEFAULT NULL,
    `others` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`others`)),
    `status` tinyint(4) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `component_dyno`
--

CREATE TABLE IF NOT EXISTS `component_dyno` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `measured_kw` varchar(255) DEFAULT NULL,
    `measured_hp` varchar(255) DEFAULT NULL,
    `calculated_kw` varchar(255) DEFAULT NULL,
    `calculated_hp` varchar(255) DEFAULT NULL,
    `calculated_rpm` varchar(255) DEFAULT NULL,
    `transfer_kw` varchar(255) DEFAULT NULL,
    `transfer_hp` varchar(255) DEFAULT NULL,
    `transfer_rpm` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `type` varchar(20) NOT NULL DEFAULT 'manuel',
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `deleted_at` (`deleted_at`),
    KEY `id` (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contracts`
--

CREATE TABLE IF NOT EXISTS `contracts` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `customer_id` bigint(20) UNSIGNED NOT NULL,
    `no` varchar(255) NOT NULL,
    `start_date` date DEFAULT NULL,
    `duration_month` tinyint(3) UNSIGNED DEFAULT 1,
    `total_count` bigint(20) UNSIGNED DEFAULT NULL,
    `best_price` double(15,2) UNSIGNED DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `status` tinyint(3) UNSIGNED DEFAULT 1,
    `worst_price` double(15,2) UNSIGNED DEFAULT NULL,
    `seller_price` double(8,2) UNSIGNED NOT NULL DEFAULT 0.00,
    `buyer_price` double(8,2) UNSIGNED NOT NULL DEFAULT 0.00,
    `branch_id` bigint(20) UNSIGNED DEFAULT 1,
    `type` varchar(255) DEFAULT 'hedefli',
    PRIMARY KEY (`id`),
    KEY `contracts_customer_id_foreign` (`customer_id`),
    KEY `customer_id` (`customer_id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contract_branches`
--

CREATE TABLE IF NOT EXISTS `contract_branches` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `contract_id` bigint(20) UNSIGNED NOT NULL,
    `branch_id` bigint(20) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `contract_branches_contract_id_foreign` (`contract_id`),
    KEY `contract_branches_branch_id_foreign` (`branch_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contract_cities`
--

CREATE TABLE IF NOT EXISTS `contract_cities` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `contract_id` bigint(20) UNSIGNED NOT NULL,
    `city_id` bigint(20) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `contract_cities_contract_id_foreign` (`contract_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contract_codes`
--

CREATE TABLE IF NOT EXISTS `contract_codes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `contract_id` bigint(20) UNSIGNED NOT NULL,
    `invoice_customer_id` bigint(20) UNSIGNED DEFAULT NULL,
    `code` varchar(15) NOT NULL,
    `plaka` varchar(255) DEFAULT NULL,
    `telephone` varchar(20) DEFAULT NULL,
    `used` tinyint(3) UNSIGNED DEFAULT 0,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `type` varchar(255) NOT NULL DEFAULT 'tumu',
    `must_payment` tinyint(3) UNSIGNED NOT NULL DEFAULT 1,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `contract_codes_contract_id_foreign` (`contract_id`),
    KEY `contract_codes_invoice_customer_id_foreign` (`invoice_customer_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contract_code_branches`
--

CREATE TABLE IF NOT EXISTS `contract_code_branches` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `contract_code_id` bigint(20) UNSIGNED NOT NULL,
    `branch_id` bigint(20) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `contract_code_branches_contract_code_id_foreign` (`contract_code_id`),
    KEY `contract_code_branches_branch_id_foreign` (`branch_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contract_code_stocks`
--

CREATE TABLE IF NOT EXISTS `contract_code_stocks` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `contract_code_id` bigint(20) UNSIGNED NOT NULL,
    `stock_id` bigint(20) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `contract_code_stocks_contract_code_id_foreign` (`contract_code_id`),
    KEY `contract_code_stocks_stock_id_foreign` (`stock_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contract_files`
--

CREATE TABLE IF NOT EXISTS `contract_files` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `contract_id` bigint(20) UNSIGNED NOT NULL,
    `name` varchar(255) DEFAULT NULL,
    `file` varchar(255) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `contract_files_contract_id_foreign` (`contract_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contract_periods`
--

CREATE TABLE IF NOT EXISTS `contract_periods` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `contract_id` bigint(20) UNSIGNED NOT NULL,
    `start_date` date NOT NULL,
    `end_date` date NOT NULL,
    `last_payment_date` date DEFAULT NULL,
    `payment_type` varchar(255) NOT NULL,
    `payment_amount` double(8,2) UNSIGNED NOT NULL,
    `limit_type` varchar(255) DEFAULT NULL,
    `limit_value` double(8,2) UNSIGNED DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `payment_completed` tinyint(3) UNSIGNED DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `contract_periods_contract_id_foreign` (`contract_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contract_period_added_limits`
--

CREATE TABLE IF NOT EXISTS `contract_period_added_limits` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `contract_period_id` bigint(20) UNSIGNED NOT NULL,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `type` varchar(20) NOT NULL,
    `amount` double(15,2) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `contract_period_added_limits_contract_period_id_foreign` (`contract_period_id`),
    KEY `contract_period_added_limits_user_id_foreign` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contract_stocks`
--

CREATE TABLE IF NOT EXISTS `contract_stocks` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `contract_id` bigint(20) UNSIGNED NOT NULL,
    `stock_id` bigint(20) UNSIGNED NOT NULL,
    `type` varchar(255) NOT NULL,
    `price` double(8,2) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `contract_stocks_contract_id_foreign` (`contract_id`),
    KEY `contract_stocks_stock_id_foreign` (`stock_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contract_targets`
--

CREATE TABLE IF NOT EXISTS `contract_targets` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `contract_id` bigint(20) UNSIGNED NOT NULL,
    `contract_period_id` int(10) UNSIGNED DEFAULT NULL,
    `date` varchar(7) DEFAULT NULL,
    `min_count` mediumint(8) UNSIGNED NOT NULL,
    `unit_price` double(15,2) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `last_payment_date` date DEFAULT NULL,
    `payment_completed` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
    `payment_type` varchar(255) DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `contract_targets_contract_id_foreign` (`contract_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE IF NOT EXISTS `customers` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `branch_id` int(11) DEFAULT NULL,
    `tip_id` int(11) DEFAULT NULL,
    `grup_id` int(11) DEFAULT NULL,
    `cari_kod` varchar(20) DEFAULT NULL,
    `eski_cari_kod` varchar(255) DEFAULT NULL,
    `kod` varchar(255) DEFAULT NULL,
    `hesap_adi` varchar(255) DEFAULT NULL,
    `unvan` varchar(255) DEFAULT NULL,
    `ad` varchar(255) DEFAULT NULL,
    `soyad` varchar(255) DEFAULT NULL,
    `yetkili_adi` varchar(255) DEFAULT NULL,
    `customer_group_id` int(11) DEFAULT 0,
    `cep` varchar(255) DEFAULT NULL,
    `telefon` varchar(255) DEFAULT NULL,
    `fax` varchar(255) DEFAULT NULL,
    `eposta` varchar(255) DEFAULT NULL,
    `web` varchar(255) DEFAULT NULL,
    `mahalle` varchar(255) DEFAULT NULL,
    `cadde` varchar(255) DEFAULT NULL,
    `sokak` varchar(255) DEFAULT NULL,
    `semt` varchar(255) DEFAULT NULL,
    `il_kodu` varchar(255) DEFAULT NULL,
    `ilce` varchar(255) DEFAULT NULL,
    `ilce_kodu` varchar(255) DEFAULT NULL,
    `ulke` varchar(255) DEFAULT NULL,
    `dogum_tarihi` date DEFAULT NULL,
    `evlilik_tarihi` date DEFAULT NULL,
    `vade_gunu` date DEFAULT NULL,
    `campaign_id` int(11) DEFAULT 0,
    `vergi_dairesi` varchar(255) DEFAULT NULL,
    `vergi_dairesi_kodu` varchar(255) DEFAULT NULL,
    `vergi_no` varchar(255) DEFAULT NULL,
    `tc_no` varchar(255) DEFAULT NULL,
    `turk_mu` tinyint(4) DEFAULT 1,
    `yabancı_belge` varchar(255) DEFAULT NULL,
    `plus_kart` varchar(255) DEFAULT NULL COMMENT 'plus kart no',
    `sitede_yayinla` int(11) DEFAULT 1,
    `garantek_uye` int(11) DEFAULT 0,
    `sms_gonder` int(11) DEFAULT 0,
    `kvkk_verified` int(11) DEFAULT 0,
    `type` varchar(255) DEFAULT 'bireysel',
    `muhasebe_type` varchar(10) DEFAULT 'alici',
    `login_token` varchar(255) DEFAULT NULL,
    `login_sms` varchar(255) DEFAULT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `plus_card` varchar(255) DEFAULT NULL,
    `tnt_search` int(11) NOT NULL DEFAULT 2,
    `mersis` varchar(20) DEFAULT NULL,
    `pasaport_no` varchar(50) DEFAULT NULL,
    `pasaport_belge` varchar(255) DEFAULT NULL,
    `data_check` int(11) NOT NULL DEFAULT 2,
    `data_not_found` int(11) NOT NULL DEFAULT 2,
    `kullanimi` bit(1) DEFAULT NULL,
    `esbis_no` varchar(255) DEFAULT NULL,
    `yetki_belge_no` varchar(255) DEFAULT NULL,
    `risk_tutari` double(15,2) UNSIGNED DEFAULT NULL,
    `kapi_no` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    KEY `branch_id` (`branch_id`) USING BTREE,
    KEY `ad` (`ad`),
    KEY `unvan` (`unvan`),
    KEY `telefon` (`telefon`),
    KEY `soyad` (`soyad`),
    KEY `status` (`status`),
    KEY `id` (`id`) USING BTREE,
    KEY `kod` (`kod`),
    KEY `cari_kod` (`cari_kod`),
    KEY `il_kodu` (`il_kodu`),
    KEY `ilce_kodu` (`ilce_kodu`),
    KEY `cep` (`cep`),
    KEY `vergi_no` (`vergi_no`),
    KEY `data_check` (`data_check`),
    KEY `eski_cari_kod` (`eski_cari_kod`),
    KEY `tcno` (`tc_no`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_buy_expertises`
--

CREATE TABLE IF NOT EXISTS `customer_buy_expertises` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `customer_id` bigint(20) UNSIGNED NOT NULL,
    `expertise_id` bigint(20) UNSIGNED NOT NULL,
    `payment_log_id` bigint(20) UNSIGNED NOT NULL,
    `status` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `customer_buy_expertises_customer_id_foreign` (`customer_id`),
    KEY `customer_buy_expertises_expertise_id_foreign` (`expertise_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_cars`
--

CREATE TABLE IF NOT EXISTS `customer_cars` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `customer_id` int(11) NOT NULL,
    `car_id` int(11) NOT NULL,
    `is_old` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `id` (`id`),
    KEY `customer_id` (`customer_id`),
    KEY `car_id` (`car_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_feedback`
--

CREATE TABLE IF NOT EXISTS `customer_feedback` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `customer_id` int(11) NOT NULL,
    `image` varchar(255) NOT NULL,
    `message` text DEFAULT NULL,
    `status` smallint(6) NOT NULL DEFAULT 2,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_groups`
--

CREATE TABLE IF NOT EXISTS `customer_groups` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `kod` varchar(255) NOT NULL,
    `parent_id` int(11) NOT NULL DEFAULT 0,
    `name` varchar(255) NOT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_queries`
--

CREATE TABLE IF NOT EXISTS `customer_queries` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `customer_id` mediumint(9) NOT NULL,
    `query` varchar(50) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `customer_queries_customer_id_index` (`customer_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_saved_filters`
--

CREATE TABLE IF NOT EXISTS `customer_saved_filters` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` mediumint(9) NOT NULL,
    `name` varchar(255) NOT NULL,
    `unvan` varchar(255) DEFAULT NULL,
    `kod` varchar(255) DEFAULT NULL,
    `telefon` varchar(255) DEFAULT NULL,
    `eposta` varchar(255) DEFAULT NULL,
    `web` varchar(255) DEFAULT NULL,
    `il_kodu` varchar(255) DEFAULT NULL,
    `ilce_kodu` varchar(255) DEFAULT NULL,
    `plus_kart` varchar(255) DEFAULT NULL,
    `sms_gonder` varchar(255) DEFAULT NULL,
    `type` varchar(255) DEFAULT NULL,
    `branch_id` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `branch_id` (`branch_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_telephones`
--

CREATE TABLE IF NOT EXISTS `customer_telephones` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `customer_id` mediumint(9) NOT NULL,
    `title` varchar(50) DEFAULT NULL,
    `telephone` varchar(10) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `customer_types`
--

CREATE TABLE IF NOT EXISTS `customer_types` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `departments`
--

CREATE TABLE IF NOT EXISTS `departments` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `title` varchar(100) DEFAULT NULL,
    `description` varchar(255) DEFAULT NULL,
    `status` enum('active','inactive') NOT NULL DEFAULT 'active',
    `sort` int(11) NOT NULL DEFAULT 0,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `excel_download`
--

CREATE TABLE IF NOT EXISTS `excel_download` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `type` varchar(255) DEFAULT NULL,
    `file_name` varchar(255) DEFAULT NULL,
    `code` varchar(10) DEFAULT NULL,
    `download_use` tinyint(4) NOT NULL DEFAULT 2,
    `download_user` int(11) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertises`
--

CREATE TABLE IF NOT EXISTS `expertises` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `old_id` int(11) DEFAULT NULL,
    `uuid` char(36) DEFAULT NULL,
    `user_id` int(11) DEFAULT 0,
    `kayit_branch_id` int(11) DEFAULT NULL,
    `branch_id` int(11) DEFAULT NULL,
    `cari_id` int(11) DEFAULT NULL,
    `satici_id` int(11) DEFAULT NULL,
    `alici_id` int(11) DEFAULT NULL,
    `car_id` int(11) DEFAULT NULL,
    `km` mediumint(9) UNSIGNED DEFAULT 0,
    `sase_no` varchar(30) DEFAULT NULL,
    `km_type` tinyint(4) DEFAULT 1,
    `net_agirlik` varchar(255) DEFAULT NULL,
    `nereden_ulastiniz` varchar(255) DEFAULT NULL,
    `sorgu_hizmeti` varchar(255) DEFAULT NULL,
    `belge_tarihi` timestamp NULL DEFAULT current_timestamp(),
    `belge_no` varchar(255) DEFAULT NULL,
    `belge_ozel_kodu` int(11) DEFAULT 1,
    `sigorta_teklif_ver` int(11) DEFAULT 0,
    `yayin_yasagi` int(11) DEFAULT 0,
    `kayit_yeri` varchar(255) DEFAULT NULL,
    `status` int(11) DEFAULT 1 COMMENT '0-pasif, 1-aktif,3-onayda',
    `odeme_turu` varchar(255) DEFAULT NULL,
    `bodywork_image` varchar(255) DEFAULT NULL,
    `diagnostic_file` varchar(255) DEFAULT NULL,
    `arac_kontrol_user` int(11) DEFAULT 0,
    `arac_kontrol` tinyint(4) DEFAULT 0,
    `fren_kontrol_user` int(11) DEFAULT 0,
    `fren_kontrol` tinyint(4) DEFAULT 0,
    `kaporta_kontrol_user` int(11) DEFAULT 0,
    `kaporta_kontrol` tinyint(4) DEFAULT 0,
    `diagnostic_kontrol_user` int(11) DEFAULT 0,
    `diagnostic_kontrol` tinyint(4) DEFAULT 0,
    `ic_kontrol_user` int(11) DEFAULT 0,
    `ic_kontrol` tinyint(4) DEFAULT 0,
    `lastik_jant_kontrol_user` int(11) DEFAULT 0,
    `lastik_jant_kontrol` tinyint(4) DEFAULT 0,
    `alt_motor_kontrol_user` int(11) DEFAULT 0,
    `alt_motor_kontrol` tinyint(4) DEFAULT 0,
    `komponent_kontrol_user` int(11) DEFAULT 0,
    `komponent_kontrol` tinyint(4) DEFAULT 0,
    `co2_kontrol_user` int(11) DEFAULT 0,
    `co2_kontrol` int(11) DEFAULT 0,
    `hasar_sorgu_sonuc` float(15,2) DEFAULT NULL,
    `kilometre_sorgu_sonuc` float(15,2) DEFAULT NULL,
    `borc_sorgu_sonuc` float(15,2) DEFAULT NULL,
    `ruhsat_sorgu_sonuc` float(15,2) DEFAULT NULL,
    `payment_type` varchar(10) DEFAULT 'normal',
    `plus_card_payment_type` varchar(10) DEFAULT NULL,
    `plus_kart_id` int(11) DEFAULT NULL,
    `audio_url` varchar(70) DEFAULT NULL,
    `cikis_tarihi` timestamp NULL DEFAULT NULL,
    `manuel_save` int(11) DEFAULT 0,
    `employee_downloaded` tinyint(4) DEFAULT 0,
    `ftp_ok` tinyint(4) NOT NULL DEFAULT 2,
    `ftp_date` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `uuid` (`uuid`),
    KEY `branch_id` (`branch_id`),
    KEY `kayit_branch_id` (`kayit_branch_id`),
    KEY `cari_id` (`cari_id`),
    KEY `satici_id` (`satici_id`),
    KEY `alici_id` (`alici_id`),
    KEY `car_id` (`car_id`),
    KEY `belge_tarihi` (`belge_tarihi`),
    KEY `deleted_at` (`deleted_at`),
    KEY `created_at` (`created_at`),
    KEY `id` (`id`),
    KEY `status` (`status`),
    KEY `manuel_save` (`manuel_save`),
    KEY `belge_no` (`belge_no`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_bodyworks`
--

CREATE TABLE IF NOT EXISTS `expertise_bodyworks` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `key` varchar(255) NOT NULL,
    `orijinal` int(11) NOT NULL DEFAULT 0,
    `boyali` int(11) NOT NULL DEFAULT 0,
    `degisen` int(11) NOT NULL DEFAULT 0,
    `duz` int(11) NOT NULL DEFAULT 0,
    `note` text DEFAULT NULL,
    `date` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `key` (`key`),
    KEY `id` (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_bodywork_notes`
--

CREATE TABLE IF NOT EXISTS `expertise_bodywork_notes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `note` text NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_brakes`
--

CREATE TABLE IF NOT EXISTS `expertise_brakes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `date` timestamp NULL DEFAULT NULL,
    `yanal_kayma_on` varchar(255) DEFAULT NULL,
    `yanal_kayma_arka` varchar(255) DEFAULT NULL,
    `max_kuvvet_on_1` varchar(255) DEFAULT NULL,
    `max_kuvvet_on_2` varchar(255) DEFAULT NULL,
    `max_kuvvet_arka_1` varchar(255) DEFAULT NULL,
    `max_kuvvet_arka_2` varchar(255) DEFAULT NULL,
    `dengesizlik_orani_on` varchar(255) DEFAULT NULL,
    `dengesizlik_orani_arka` varchar(255) DEFAULT NULL,
    `el_freni_dengesizlik_orani` varchar(255) DEFAULT NULL,
    `yalpa_orani_on_1` varchar(255) DEFAULT NULL,
    `yalpa_orani_on_2` varchar(255) DEFAULT NULL,
    `yalpa_orani_arka_1` varchar(255) DEFAULT NULL,
    `yalpa_orani_arka_2` varchar(255) DEFAULT NULL,
    `suspansiyon_on_1` varchar(255) DEFAULT NULL,
    `suspansiyon_on_2` varchar(255) DEFAULT NULL,
    `suspansiyon_arka_1` varchar(255) DEFAULT NULL,
    `suspansiyon_arka_2` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `type` varchar(255) DEFAULT 'manuel',
    `on_dingil_bosta_a` varchar(255) DEFAULT NULL,
    `on_dingil_bosta_b` varchar(255) DEFAULT NULL,
    `arka_dingil_bosta_a` varchar(255) DEFAULT NULL,
    `arka_dingil_bosta_b` varchar(255) DEFAULT NULL,
    `el_freni_kuvvet_a` varchar(255) DEFAULT NULL,
    `el_freni_kuvvet_b` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_brake_notes`
--

CREATE TABLE IF NOT EXISTS `expertise_brake_notes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_brake_id` int(11) NOT NULL,
    `note` text NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_brake_id` (`expertise_brake_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_co2`
--

CREATE TABLE IF NOT EXISTS `expertise_co2` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `key` varchar(255) NOT NULL,
    `status` int(11) DEFAULT NULL,
    `note` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`,`key`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_co2_note`
--

CREATE TABLE IF NOT EXISTS `expertise_co2_note` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) DEFAULT NULL,
    `note` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_components`
--

CREATE TABLE IF NOT EXISTS `expertise_components` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `key` varchar(255) NOT NULL,
    `status` int(11) DEFAULT NULL,
    `answer` varchar(255) DEFAULT NULL,
    `note` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `key` (`key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_component_notes`
--

CREATE TABLE IF NOT EXISTS `expertise_component_notes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `note` text NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_diagnostics`
--

CREATE TABLE IF NOT EXISTS `expertise_diagnostics` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `note` text NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_diagnostic_notes`
--

CREATE TABLE IF NOT EXISTS `expertise_diagnostic_notes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `note` text NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_internal_controls`
--

CREATE TABLE IF NOT EXISTS `expertise_internal_controls` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `key` varchar(255) NOT NULL,
    `sorunlu_mu` int(11) NOT NULL DEFAULT 0,
    `answer` varchar(255) DEFAULT NULL,
    `note` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `key` (`key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_internal_controls_notes`
--

CREATE TABLE IF NOT EXISTS `expertise_internal_controls_notes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `note` text NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_payments`
--

CREATE TABLE IF NOT EXISTS `expertise_payments` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `case_id` int(11) DEFAULT 0,
    `amount` float(15,2) NOT NULL DEFAULT 0.00,
    `type` varchar(255) DEFAULT NULL,
    `payment_code` varchar(255) DEFAULT NULL,
    `payment_detail` longtext DEFAULT NULL,
    `plus_card_id` int(11) DEFAULT 0,
    `plus_card_odeme_id` int(11) DEFAULT 0,
    `result` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `update_data` int(11) NOT NULL DEFAULT 2,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `plus_card_id` (`plus_card_id`),
    KEY `plus_card_odeme_id` (`plus_card_odeme_id`),
    KEY `type` (`type`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_payments_try`
--

CREATE TABLE IF NOT EXISTS `expertise_payments_try` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `case_id` int(11) DEFAULT 0,
    `amount` float(15,2) NOT NULL DEFAULT 0.00,
    `type` varchar(255) DEFAULT NULL,
    `payment_code` varchar(255) DEFAULT NULL,
    `payment_detail` longtext DEFAULT NULL,
    `plus_card_id` int(11) DEFAULT 0,
    `plus_card_odeme_id` int(11) DEFAULT 0,
    `result` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `update_data` int(11) NOT NULL DEFAULT 2,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_plus_card_sms_verifications`
--

CREATE TABLE IF NOT EXISTS `expertise_plus_card_sms_verifications` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` bigint(20) UNSIGNED NOT NULL,
    `cep` varchar(20) DEFAULT NULL,
    `telefon` varchar(20) DEFAULT NULL,
    `code_telefon` varchar(10) NOT NULL,
    `code_cep` varchar(10) NOT NULL,
    `type` varchar(10) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_plus_card_sms_verifications_expertise_id_foreign` (`expertise_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_report_downloads`
--

CREATE TABLE IF NOT EXISTS `expertise_report_downloads` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `e_uuid` varchar(36) NOT NULL COMMENT 'expertise.uuid',
    `u_id` bigint(20) UNSIGNED NOT NULL COMMENT 'user.id',
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `expertise_report_downloads_u_id_foreign` (`u_id`),
    KEY `expertise_report_downloads_e_uuid_foreign` (`e_uuid`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_saved_filters`
--

CREATE TABLE IF NOT EXISTS `expertise_saved_filters` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` mediumint(9) NOT NULL DEFAULT 0,
    `name` varchar(255) NOT NULL,
    `belge_tarihi_baslangic` date DEFAULT NULL,
    `belge_tarihi_bitis` date DEFAULT NULL,
    `belge_no` varchar(255) DEFAULT NULL,
    `search` varchar(255) DEFAULT NULL,
    `branch_id` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_stocks`
--

CREATE TABLE IF NOT EXISTS `expertise_stocks` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) DEFAULT NULL,
    `stock_id` int(11) DEFAULT NULL,
    `campaign_id` int(11) DEFAULT NULL,
    `sorgu_hizmeti` int(11) DEFAULT NULL,
    `yol_yardimi` int(11) DEFAULT 1,
    `iskonto_amount` double(15,2) DEFAULT 0.00,
    `hizmet_tutari` double(15,2) DEFAULT NULL,
    `liste_fiyati` double(15,2) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `stock_id` (`stock_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_sub_control_and_engines`
--

CREATE TABLE IF NOT EXISTS `expertise_sub_control_and_engines` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `key` varchar(255) NOT NULL,
    `status` int(11) DEFAULT NULL,
    `answer` varchar(255) DEFAULT NULL,
    `note` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `key` (`key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_sub_control_and_engine_notes`
--

CREATE TABLE IF NOT EXISTS `expertise_sub_control_and_engine_notes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `note` text NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_tire_and_rims`
--

CREATE TABLE IF NOT EXISTS `expertise_tire_and_rims` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `key` varchar(255) NOT NULL,
    `sorunlu_mu` int(11) DEFAULT NULL,
    `note` text DEFAULT NULL,
    `dis` double(8,2) DEFAULT NULL,
    `basinc` double(8,2) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`,`key`),
    KEY `id` (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `expertise_tire_and_rim_notes`
--

CREATE TABLE IF NOT EXISTS `expertise_tire_and_rim_notes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_id` int(11) NOT NULL,
    `note` text NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `expertise_id` (`expertise_id`),
    KEY `deleted_at` (`deleted_at`),
    KEY `id` (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE IF NOT EXISTS `failed_jobs` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `uuid` varchar(255) NOT NULL,
    `connection` text NOT NULL,
    `queue` text NOT NULL,
    `payload` longtext NOT NULL,
    `exception` longtext NOT NULL,
    `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`),
    UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `hasarsorgu`
--

CREATE TABLE IF NOT EXISTS `hasarsorgu` (
    `sase` varchar(255) DEFAULT NULL
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `hlx_plus`
--

CREATE TABLE IF NOT EXISTS `hlx_plus` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `karttipi` tinyint(4) DEFAULT 1,
    `system_id` varchar(255) DEFAULT NULL,
    `no` varchar(255) NOT NULL,
    `branch_id` int(11) DEFAULT 0,
    `customer_id` int(11) DEFAULT NULL,
    `kredi` double(15,2) NOT NULL DEFAULT 0.00,
    `puan` double(15,2) NOT NULL DEFAULT 0.00,
    `valid_date_if` tinyint(4) NOT NULL DEFAULT 0,
    `valid_date` date NOT NULL DEFAULT '2025-01-17',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `tlindtutar` decimal(20,8) DEFAULT NULL,
    `credits_update` int(11) DEFAULT 2,
    `points_update` int(11) DEFAULT 2,
    `T212_ID` int(11) DEFAULT NULL,
    `T404_transfered` tinyint(4) DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `no` (`no`(250)),
    KEY `branch_id` (`branch_id`),
    KEY `customer_id` (`customer_id`),
    KEY `valid_date` (`valid_date`),
    KEY `no_2` (`no`(250))
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `hlx_plus_try`
--

CREATE TABLE IF NOT EXISTS `hlx_plus_try` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `karttipi` tinyint(4) DEFAULT 1,
    `system_id` varchar(255) DEFAULT NULL,
    `no` varchar(255) NOT NULL,
    `branch_id` int(11) DEFAULT 0,
    `customer_id` int(11) DEFAULT NULL,
    `kredi` double(15,2) NOT NULL DEFAULT 0.00,
    `puan` double(15,2) NOT NULL DEFAULT 0.00,
    `valid_date_if` tinyint(4) NOT NULL DEFAULT 0,
    `valid_date` date NOT NULL DEFAULT '2025-01-17',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `tlindtutar` decimal(20,8) DEFAULT NULL,
    `credits_update` int(11) DEFAULT 2,
    `points_update` int(11) DEFAULT 2,
    `T212_ID` int(11) DEFAULT NULL,
    `T404_transfered` tinyint(4) DEFAULT 0,
    `T404_point_transfered` tinyint(4) DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `no` (`no`(250)),
    KEY `branch_id` (`branch_id`),
    KEY `customer_id` (`customer_id`),
    KEY `valid_date` (`valid_date`),
    KEY `no_2` (`no`(250))
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ilce`
--

CREATE TABLE IF NOT EXISTS `ilce` (
    `ilce_id` int(11) NOT NULL AUTO_INCREMENT,
    `ilce_title` varchar(255) NOT NULL,
    `ilce_key` int(11) NOT NULL,
    `ilce_sehirkey` int(11) NOT NULL,
    PRIMARY KEY (`ilce_id`),
    KEY `ilce_id` (`ilce_id`),
    KEY `ilce_title` (`ilce_title`),
    KEY `ilce_sehirkey` (`ilce_sehirkey`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COLLATE=utf8mb3_turkish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `invoice_definitions`
--

CREATE TABLE IF NOT EXISTS `invoice_definitions` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `cari_hesap_arama_tipi` varchar(255) DEFAULT NULL,
    `cari_hesap_bakiyesi_gosterilsin` varchar(255) DEFAULT NULL,
    `kupon_takibi_yapilacak` varchar(255) DEFAULT NULL,
    `kasa_banka_hesabi_secilsin` varchar(255) DEFAULT NULL,
    `barkod_girisi_yapilacak` varchar(255) DEFAULT NULL,
    `satis_cari_hesap_grubu` varchar(255) DEFAULT NULL,
    `satis_cari_hesap` varchar(255) DEFAULT NULL,
    `satis_kasa_hesabi` varchar(255) DEFAULT NULL,
    `satis_kredi_karti_hesabi` varchar(255) DEFAULT NULL,
    `alis_cari_hesap_grubu` varchar(255) DEFAULT NULL,
    `alis_cari_hesap` varchar(255) DEFAULT NULL,
    `alis_kasa_hesabi` varchar(255) DEFAULT NULL,
    `alis_kredi_karti_hesabi` varchar(255) DEFAULT NULL,
    `belge_ozel_kodu` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE IF NOT EXISTS `jobs` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `queue` varchar(255) NOT NULL,
    `payload` longtext NOT NULL,
    `attempts` tinyint(3) UNSIGNED NOT NULL,
    `reserved_at` int(10) UNSIGNED DEFAULT NULL,
    `available_at` int(10) UNSIGNED NOT NULL,
    `created_at` int(10) UNSIGNED NOT NULL,
    PRIMARY KEY (`id`),
    KEY `jobs_queue_index` (`queue`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE IF NOT EXISTS `job_batches` (
    `id` varchar(255) NOT NULL,
    `name` varchar(255) NOT NULL,
    `total_jobs` int(11) NOT NULL,
    `pending_jobs` int(11) NOT NULL,
    `failed_jobs` int(11) NOT NULL,
    `failed_job_ids` longtext NOT NULL,
    `options` mediumtext DEFAULT NULL,
    `cancelled_at` int(11) DEFAULT NULL,
    `created_at` int(11) NOT NULL,
    `finished_at` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `logs`
--

CREATE TABLE IF NOT EXISTS `logs` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `branch_id` int(11) NOT NULL,
    `content_type` varchar(255) NOT NULL,
    `content_id` varchar(255) NOT NULL DEFAULT '0',
    `description` text NOT NULL,
    `ip` varchar(255) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `content_type` (`content_type`,`content_id`),
    KEY `user_id` (`user_id`),
    KEY `branch_id` (`branch_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `menus`
--

CREATE TABLE IF NOT EXISTS `menus` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `place` varchar(255) DEFAULT NULL,
    `route_name` varchar(255) DEFAULT NULL,
    `icon` varchar(255) DEFAULT NULL,
    `key` varchar(255) NOT NULL,
    `value` varchar(255) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `key` (`key`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE IF NOT EXISTS `migrations` (
    `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `migration` varchar(255) NOT NULL,
    `batch` int(11) NOT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `new_plus_cards`
--

CREATE TABLE IF NOT EXISTS `new_plus_cards` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `karttipi` tinyint(4) DEFAULT 1,
    `system_id` varchar(255) DEFAULT NULL,
    `no` varchar(255) NOT NULL,
    `branch_id` int(11) DEFAULT 0,
    `customer_id` int(11) DEFAULT NULL,
    `kredi` double(15,2) NOT NULL DEFAULT 0.00,
    `puan` double(15,2) NOT NULL DEFAULT 0.00,
    `valid_date_if` tinyint(4) NOT NULL DEFAULT 0,
    `valid_date` date NOT NULL DEFAULT '2025-01-17',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `tlindtutar` decimal(20,8) DEFAULT NULL,
    `credits_update` int(11) DEFAULT 2,
    `points_update` int(11) DEFAULT 2,
    `T212_ID` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `no` (`no`),
    KEY `branch_id` (`branch_id`),
    KEY `customer_id` (`customer_id`),
    KEY `valid_date` (`valid_date`)
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notes`
--

CREATE TABLE IF NOT EXISTS `notes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `key` varchar(255) NOT NULL,
    `note` text NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE IF NOT EXISTS `notifications` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `bulletin_id` bigint(20) UNSIGNED NOT NULL,
    `notifiable_type` varchar(191) NOT NULL,
    `notifiable_id` bigint(20) UNSIGNED NOT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `notifications_bulletin_id_foreign` (`bulletin_id`),
    KEY `notifiable_index` (`notifiable_type`,`notifiable_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE IF NOT EXISTS `password_reset_tokens` (
    `email` varchar(255) NOT NULL,
    `token` varchar(255) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`email`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payments`
--

CREATE TABLE IF NOT EXISTS `payments` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `uuid` char(255) DEFAULT NULL,
    `customer_id` int(11) NOT NULL,
    `description` varchar(255) DEFAULT NULL,
    `name` varchar(255) DEFAULT NULL,
    `surname` varchar(255) DEFAULT NULL,
    `telephone` varchar(255) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `tc_no` varchar(255) DEFAULT NULL,
    `address` text DEFAULT NULL,
    `ip_address` varchar(255) DEFAULT NULL,
    `city` varchar(255) DEFAULT NULL,
    `country` varchar(255) DEFAULT 'Türkiye',
    `plus_kart_adet` int(11) DEFAULT 0,
    `ucretsiz_rapor_adet` int(11) DEFAULT 0,
    `payment_price` double(15,2) DEFAULT 0.00,
    `iyzico_token` varchar(255) DEFAULT NULL,
    `iyzico_payment_id` varchar(255) DEFAULT NULL,
    `iyzico_transaction_id` varchar(255) DEFAULT NULL,
    `status` int(11) DEFAULT 2,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_log`
--

CREATE TABLE IF NOT EXISTS `payment_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `file_uuid` varchar(255) DEFAULT NULL,
    `payment_id` varchar(255) DEFAULT NULL,
    `payment_price` varchar(255) DEFAULT NULL,
    `payment_session_id` varchar(255) DEFAULT NULL,
    `payment_token_id` varchar(255) DEFAULT NULL,
    `payment_req` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `payment_res` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
    `payment_type` tinyint(4) DEFAULT 1 COMMENT '1 -) ekspertiz için 2-) plus kart ödeme için',
    `status` int(11) DEFAULT 0 COMMENT '0:Bekliyor || 1: Başarılı || 2:Başarısız',
    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
    PRIMARY KEY (`id`)
    ) ;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE IF NOT EXISTS `personal_access_tokens` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `tokenable_type` varchar(255) NOT NULL,
    `tokenable_id` bigint(20) UNSIGNED NOT NULL,
    `name` varchar(255) NOT NULL,
    `token` varchar(64) NOT NULL,
    `abilities` text DEFAULT NULL,
    `last_used_at` timestamp NULL DEFAULT NULL,
    `expires_at` timestamp NULL DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
    KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `PLUSNOGEREKLI`
--

CREATE TABLE IF NOT EXISTS `PLUSNOGEREKLI` (
                                               `TARIH` datetime DEFAULT NULL,
                                               `HESAPKODU` varchar(255) DEFAULT NULL,
    `SUBEKODU` int(11) DEFAULT NULL,
    `T201ID` int(11) DEFAULT NULL
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_cards`
--

CREATE TABLE IF NOT EXISTS `plus_cards` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `karttipi` tinyint(4) DEFAULT 1,
    `system_id` varchar(255) DEFAULT NULL,
    `no` varchar(255) NOT NULL,
    `branch_id` int(11) DEFAULT 0,
    `customer_id` int(11) DEFAULT 0,
    `kredi` double(15,2) DEFAULT 0.00,
    `puan` double(15,2) DEFAULT 0.00,
    `valid_date_if` tinyint(4) DEFAULT 0,
    `valid_date` date NOT NULL DEFAULT '2025-01-17',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `tlindtutar` decimal(20,8) DEFAULT NULL,
    `credits_update` int(11) NOT NULL DEFAULT 2,
    `points_update` int(11) NOT NULL DEFAULT 2,
    `hlx_transfer` tinyint(4) DEFAULT 0,
    `hlx_wallet_transfer` tinyint(4) DEFAULT 0,
    `t201cariid` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `no` (`no`),
    KEY `customer_id` (`customer_id`),
    KEY `branch_id` (`branch_id`),
    KEY `valid_date` (`valid_date`),
    KEY `system_id` (`system_id`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_cards_customers`
--

CREATE TABLE IF NOT EXISTS `plus_cards_customers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `hesapkodu` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `barkodu` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `hesapadi` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `subekodu` int(11) DEFAULT NULL,
    `uq` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `uq` (`uq`)
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_cards_definitions`
--

CREATE TABLE IF NOT EXISTS `plus_cards_definitions` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `definition_name` text DEFAULT NULL,
    `unit_quantity` mediumint(9) NOT NULL,
    `unit_price` double(15,2) DEFAULT NULL,
    `commission` mediumint(9) DEFAULT NULL,
    `definition_invoice_code` text DEFAULT NULL,
    `stock_id` mediumint(9) DEFAULT NULL,
    `stok_uq` varchar(255) DEFAULT NULL,
    `stock_kodu` varchar(25) DEFAULT NULL,
    `show_in_view` tinyint(4) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_cards_puan`
--

CREATE TABLE IF NOT EXISTS `plus_cards_puan` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `t404id` int(11) DEFAULT NULL,
    `kalanmiktar` mediumint(9) DEFAULT NULL,
    `hesapkodu` varchar(255) DEFAULT NULL,
    `kartid` varchar(255) DEFAULT NULL,
    `hesapadi` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_turkish_ci DEFAULT NULL,
    `telefon` varchar(255) DEFAULT NULL,
    `cep` varchar(255) DEFAULT NULL,
    `kullanilanmiktar` int(11) DEFAULT NULL,
    `sube` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_turkish_ci DEFAULT NULL,
    `aciklama` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_turkish_ci DEFAULT NULL,
    `varyok` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_cards_saved_filters`
--

CREATE TABLE IF NOT EXISTS `plus_cards_saved_filters` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` mediumint(9) NOT NULL,
    `name` varchar(255) DEFAULT NULL,
    `card_no` varchar(255) DEFAULT NULL,
    `branch_id` varchar(255) DEFAULT NULL,
    `customer_code` varchar(255) DEFAULT NULL,
    `customer_name` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_cards_zorunluolan`
--

CREATE TABLE IF NOT EXISTS `plus_cards_zorunluolan` (
    `cariid` int(11) DEFAULT NULL,
    `kartid` varchar(255) DEFAULT NULL,
    `kartno` varbinary(255) DEFAULT NULL,
    `subekodu` int(11) DEFAULT NULL
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_agreement`
--

CREATE TABLE IF NOT EXISTS `plus_card_agreement` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `plus_card_id` int(11) DEFAULT NULL,
    `customer_id` int(11) DEFAULT NULL,
    `unit_quantity` int(11) DEFAULT NULL,
    `payment_amount` double(15,2) DEFAULT NULL,
    `valid_date` date DEFAULT NULL,
    `payment_type` varchar(25) DEFAULT NULL,
    `user_id` int(11) DEFAULT NULL,
    `delayed_id` varchar(7) DEFAULT NULL,
    `kvkk_approval` smallint(6) NOT NULL DEFAULT 2,
    `agreement_approval` smallint(6) NOT NULL DEFAULT 2,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `plus_card_id` (`plus_card_id`),
    KEY `customer_id` (`customer_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_campaigns`
--

CREATE TABLE IF NOT EXISTS `plus_card_campaigns` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `title` varchar(255) NOT NULL,
    `description` text DEFAULT NULL,
    `start_date` timestamp NULL DEFAULT NULL,
    `end_date` timestamp NULL DEFAULT NULL,
    `discount_type` enum('percentage','fixed') NOT NULL DEFAULT 'percentage',
    `discount_value` decimal(10,2) NOT NULL DEFAULT 0.00,
    `minimum_purchase_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
    `is_single_use` tinyint(1) NOT NULL DEFAULT 0,
    `usage_limit_per_card` int(11) NOT NULL DEFAULT 0,
    `status` enum('active','inactive','expired') NOT NULL DEFAULT 'active',
    `created_by` bigint(20) UNSIGNED NOT NULL,
    `branch_id` bigint(20) UNSIGNED DEFAULT NULL,
    `stock_id` bigint(20) UNSIGNED DEFAULT NULL,
    `usage_code` varchar(255) DEFAULT NULL,
    `is_visible_to_all` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `plus_card_campaigns_usage_code_unique` (`usage_code`) USING HASH,
    KEY `plus_card_campaigns_created_by_foreign` (`created_by`),
    KEY `plus_card_campaigns_branch_id_foreign` (`branch_id`),
    KEY `plus_card_campaigns_stock_id_foreign` (`stock_id`),
    KEY `plus_card_campaigns_status_start_date_end_date_index` (`status`,`start_date`,`end_date`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_campaign_eligibility`
--

CREATE TABLE IF NOT EXISTS `plus_card_campaign_eligibility` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `campaign_id` bigint(20) UNSIGNED NOT NULL,
    `plus_card_id` bigint(20) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `plus_card_campaign_eligibility_campaign_id_plus_card_id_unique` (`campaign_id`,`plus_card_id`),
    KEY `plus_card_campaign_eligibility_plus_card_id_foreign` (`plus_card_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_campaign_usages`
--

CREATE TABLE IF NOT EXISTS `plus_card_campaign_usages` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `campaign_id` bigint(20) UNSIGNED NOT NULL,
    `plus_card_id` bigint(20) UNSIGNED NOT NULL,
    `expertise_id` bigint(20) UNSIGNED DEFAULT NULL,
    `original_amount` decimal(10,2) NOT NULL,
    `discounted_amount` decimal(10,2) NOT NULL,
    `discount_value` decimal(10,2) NOT NULL,
    `used_at` timestamp NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `plus_card_campaign_usages_plus_card_id_foreign` (`plus_card_id`),
    KEY `plus_card_campaign_usages_expertise_id_foreign` (`expertise_id`),
    KEY `plus_card_campaign_usages_campaign_id_plus_card_id_used_at_index` (`campaign_id`,`plus_card_id`,`used_at`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_credi_and_puan_add`
--

CREATE TABLE IF NOT EXISTS `plus_card_credi_and_puan_add` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` mediumint(9) DEFAULT NULL,
    `stock_id` mediumint(9) DEFAULT NULL,
    `definitions_id` mediumint(9) DEFAULT NULL,
    `balance_type` varchar(255) DEFAULT NULL,
    `card_id` bigint(255) DEFAULT NULL,
    `unit_price` double(15,2) DEFAULT NULL,
    `commission` double(15,2) DEFAULT NULL,
    `credits_amount` double(15,2) DEFAULT NULL,
    `credi` mediumint(9) NOT NULL DEFAULT 0,
    `puan` mediumint(9) NOT NULL DEFAULT 0,
    `puan_branche_id` mediumint(9) DEFAULT 0,
    `odenen_kdv_dahil_fiyat` double(15,2) DEFAULT NULL,
    `payment_type` text DEFAULT NULL,
    `case` text DEFAULT NULL,
    `valid_date` date DEFAULT NULL,
    `devir_miktar` float(15,2) DEFAULT NULL,
    `devir_tarih` date DEFAULT NULL,
    `devir_aciklama` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `code_deleted` int(11) NOT NULL DEFAULT 2,
    `delayed_id` varchar(255) DEFAULT NULL,
    `hlx_wallet_transfer` tinyint(4) DEFAULT 0,
    `paynet_reference_no` varchar(255) DEFAULT NULL,
    `paynet_confirmation_no` varchar(255) DEFAULT NULL,
    `transfered` smallint(5) UNSIGNED DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `card_id` (`card_id`),
    KEY `definitions_id` (`definitions_id`),
    KEY `deleted_at` (`deleted_at`),
    KEY `stock_id` (`stock_id`),
    KEY `puan_branche_id` (`puan_branche_id`),
    KEY `id` (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_credi_and_puan_add_try`
--

CREATE TABLE IF NOT EXISTS `plus_card_credi_and_puan_add_try` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` mediumint(9) DEFAULT NULL,
    `stock_id` mediumint(9) DEFAULT NULL,
    `definitions_id` mediumint(9) DEFAULT NULL,
    `balance_type` varchar(255) DEFAULT NULL,
    `card_id` bigint(255) DEFAULT NULL,
    `unit_price` double(15,2) DEFAULT NULL,
    `commission` double(15,2) DEFAULT NULL,
    `credits_amount` double(15,2) DEFAULT NULL,
    `credi` mediumint(9) NOT NULL DEFAULT 0,
    `puan` mediumint(9) NOT NULL DEFAULT 0,
    `puan_branche_id` mediumint(9) DEFAULT 0,
    `odenen_kdv_dahil_fiyat` double(15,2) DEFAULT NULL,
    `payment_type` text DEFAULT NULL,
    `case` text DEFAULT NULL,
    `valid_date` date DEFAULT NULL,
    `devir_miktar` float(15,2) DEFAULT NULL,
    `devir_tarih` date DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `code_deleted` int(11) NOT NULL DEFAULT 2,
    `delayed_id` varchar(255) DEFAULT NULL,
    `T404_ID` int(11) DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `card_id` (`card_id`),
    KEY `definitions_id` (`definitions_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_credi_and_puan_removes`
--

CREATE TABLE IF NOT EXISTS `plus_card_credi_and_puan_removes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `plus_card_credi_and_puan_add_id` int(11) NOT NULL,
    `amount` int(11) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_definition_prices`
--

CREATE TABLE IF NOT EXISTS `plus_card_definition_prices` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `definition_id` bigint(20) UNSIGNED NOT NULL,
    `min_count` mediumint(8) UNSIGNED DEFAULT 0,
    `price` double(15,2) UNSIGNED DEFAULT 0.00,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `plus_card_definition_prices_definition_id_foreign` (`definition_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_definition_stocks`
--

CREATE TABLE IF NOT EXISTS `plus_card_definition_stocks` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `definition_id` bigint(20) UNSIGNED NOT NULL,
    `stock_id` bigint(20) UNSIGNED NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `plus_card_definition_stocks_definition_id_foreign` (`definition_id`),
    KEY `plus_card_definition_stocks_stock_id_foreign` (`stock_id`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_olan`
--

CREATE TABLE IF NOT EXISTS `plus_card_olan` (
    `kartid` varchar(255) DEFAULT NULL,
    `hesap` varchar(255) DEFAULT NULL,
    KEY `kartid` (`kartid`),
    KEY `hesap` (`hesap`)
    ) ENGINE=MyISAM DEFAULT CHARSET=latin1 COLLATE=latin1_swedish_ci;

-- --------------------------------------------------------

--
-- Table structure for table `plus_card_stocks`
--

CREATE TABLE IF NOT EXISTS `plus_card_stocks` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `plus_card_id` int(11) NOT NULL,
    `stock_id` int(11) NOT NULL,
    `quantity` int(11) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pool_choises`
--

CREATE TABLE IF NOT EXISTS `pool_choises` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `expertise_uuid` varchar(36) NOT NULL,
    `question` text NOT NULL,
    `answer` text NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pool_options`
--

CREATE TABLE IF NOT EXISTS `pool_options` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `pool_question_id` int(11) NOT NULL,
    `option` text NOT NULL,
    `status` int(11) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `pool_questions`
--

CREATE TABLE IF NOT EXISTS `pool_questions` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `question` varchar(255) NOT NULL,
    `type` varchar(255) DEFAULT 'text',
    `status` int(11) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `query_logs`
--

CREATE TABLE IF NOT EXISTS `query_logs` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` int(11) DEFAULT NULL,
    `uuid` varchar(255) DEFAULT NULL,
    `hasar` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`hasar`)),
    `hasar_ucret` varchar(255) DEFAULT NULL,
    `hasar_komisyon` double(8,2) UNSIGNED DEFAULT 0.00,
    `kilometre` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`kilometre`)),
    `kilometre_ucret` varchar(255) DEFAULT NULL,
    `kilometre_komisyon` double(8,2) UNSIGNED DEFAULT 0.00,
    `borc` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`borc`)),
    `borc_ucret` varchar(255) DEFAULT NULL,
    `borc_komisyon` double(8,2) UNSIGNED DEFAULT 0.00,
    `degisen` longtext DEFAULT NULL,
    `degisen_ucret` varchar(255) DEFAULT NULL,
    `degisen_komisyon` double(8,2) UNSIGNED DEFAULT 0.00,
    `detail` longtext DEFAULT NULL,
    `detail_ucret` varchar(255) DEFAULT NULL,
    `detail_komisyon` double(8,2) UNSIGNED DEFAULT 0.00,
    `ruhsat` longtext DEFAULT NULL,
    `ruhsat_ucret` varchar(255) DEFAULT NULL,
    `ruhsat_komisyon` double(8,2) UNSIGNED DEFAULT 0.00,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`),
    KEY `uuid` (`uuid`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `query_logs_extra`
--

CREATE TABLE IF NOT EXISTS `query_logs_extra` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `expertise_uuid` varchar(50) NOT NULL,
    `type` varchar(20) NOT NULL COMMENT 'hasar, degisen, borc, ruhsat, kilometre, detay',
    `result` text DEFAULT NULL,
    `amount` double(8,2) UNSIGNED DEFAULT NULL,
    `commission` double(8,2) UNSIGNED DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `query_logs_extra_user_id_foreign` (`user_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE IF NOT EXISTS `sessions` (
    `id` varchar(255) NOT NULL,
    `user_id` bigint(20) UNSIGNED DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `payload` longtext NOT NULL,
    `last_activity` int(11) NOT NULL,
    PRIMARY KEY (`id`),
    KEY `sessions_user_id_index` (`user_id`),
    KEY `sessions_last_activity_index` (`last_activity`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `settings`
--

CREATE TABLE IF NOT EXISTS `settings` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `netgsm_active` varchar(255) DEFAULT '0',
    `netgsm_usercode` varchar(255) DEFAULT NULL,
    `netgsm_password` varchar(255) DEFAULT NULL,
    `netgsm_msgheader` varchar(255) DEFAULT NULL,
    `netgsm_msgheader2` varchar(255) DEFAULT NULL,
    `netgsm_msgheader3` varchar(255) DEFAULT NULL,
    `iyzico_active` varchar(255) DEFAULT '0',
    `iyzico_url` varchar(255) DEFAULT 'https://api.iyzipay.com',
    `iyzico_api_key` varchar(255) DEFAULT NULL,
    `iyzico_secret_key` varchar(255) DEFAULT NULL,
    `passive_user_message` text DEFAULT NULL,
    `google_auth` smallint(6) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `hasar_sorgu_mesaji` text DEFAULT NULL,
    `kilometre_sorgu_mesaji` text DEFAULT NULL,
    `borc_sorgu_mesaji` text DEFAULT NULL,
    `arac_detay_sorgu_mesaji` text DEFAULT NULL,
    `degisen_sorgu_mesaji` text DEFAULT NULL,
    `ruhsat_sorgu_mesaji` text DEFAULT NULL,
    `hasar_sorgu_komisyon` float(15,2) DEFAULT 0.00,
    `kilometre_sorgu_komisyon` float(15,2) DEFAULT 0.00,
    `borc_sorgu_komisyon` float(15,2) DEFAULT 0.00,
    `detail_sorgu_komisyon` float(15,2) DEFAULT 0.00,
    `degisen_sorgu_komisyon` float(15,2) DEFAULT 0.00,
    `ruhsat_sorgu_komisyon` float(15,2) DEFAULT 0.00,
    `hasar_sorgu_komisyon_arabasorgula` double(8,2) UNSIGNED DEFAULT 0.00,
    `kilometre_sorgu_komisyon_arabasorgula` double(8,2) UNSIGNED DEFAULT 0.00,
    `borc_sorgu_komisyon_arabasorgula` double(8,2) UNSIGNED DEFAULT 0.00,
    `degisen_sorgu_komisyon_arabasorgula` double(8,2) UNSIGNED DEFAULT 0.00,
    `detail_sorgu_komisyon_arabasorgula` double(8,2) UNSIGNED DEFAULT 0.00,
    `ruhsat_sorgu_komisyon_arabasorgula` double(8,2) UNSIGNED DEFAULT 0.00,
    `hasar_sorgu_otosorgu` float(15,2) DEFAULT 0.00,
    `hasar_sorgu_sistem` float(15,2) DEFAULT 0.00,
    `kilometre_sorgu_otosorgu` float(15,2) DEFAULT 0.00,
    `kilometre_sorgu_sistem` float(15,2) DEFAULT 0.00,
    `borc_sorgu_otosorgu` float(15,2) DEFAULT 0.00,
    `borc_sorgu_sistem` float(15,2) DEFAULT 0.00,
    `detail_sorgu_otosorgu` float(15,2) DEFAULT 0.00,
    `detail_sorgu_sistem` float(15,2) DEFAULT 0.00,
    `degisen_sorgu_otosorgu` float(15,2) DEFAULT 0.00,
    `degisen_sorgu_sistem` float(15,2) DEFAULT 0.00,
    `ruhsat_sorgu_otosorgu` float(15,2) DEFAULT 0.00,
    `ruhsat_sorgu_sistem` int(11) DEFAULT NULL,
    `customer_buy_expertise_price` double(15,2) UNSIGNED DEFAULT 0.00,
    `hasar_sorgu_firmasi` varchar(255) DEFAULT 'otosorgu',
    `kilometre_sorgu_firmasi` varchar(255) DEFAULT 'otosorgu',
    `borc_sorgu_firmasi` varchar(255) DEFAULT 'otosorgu',
    `arac_detay_sorgu_firmasi` varchar(255) DEFAULT 'otosorgu',
    `degisen_sorgu_firmasi` varchar(255) DEFAULT 'otosorgu',
    `ruhsat_sorgu_firmasi` varchar(255) DEFAULT 'otosorgu',
    `hasar_sorgu_arabasorgula` double(15,2) DEFAULT 0.00,
    `kilometre_sorgu_arabasorgula` double(15,2) DEFAULT 0.00,
    `borc_sorgu_arabasorgula` double(15,2) DEFAULT 0.00,
    `detail_sorgu_arabasorgula` double(15,2) DEFAULT 0.00,
    `degisen_sorgu_arabasorgula` double(15,2) DEFAULT 0.00,
    `ruhsat_sorgu_arabasorgula` double(15,2) DEFAULT 0.00,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sliders`
--

CREATE TABLE IF NOT EXISTS `sliders` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `image` varchar(255) DEFAULT NULL,
    `place` varchar(30) DEFAULT 'panel',
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sms_verifications`
--

CREATE TABLE IF NOT EXISTS `sms_verifications` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `phone` text DEFAULT NULL,
    `page` text DEFAULT NULL,
    `code` text DEFAULT NULL,
    `use_code` tinyint(4) NOT NULL DEFAULT 2,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `stocks`
--

CREATE TABLE IF NOT EXISTS `stocks` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `branch_id` int(11) DEFAULT NULL,
    `kod` varchar(255) DEFAULT NULL,
    `stock_kodu` varchar(25) DEFAULT NULL,
    `ad` varchar(255) DEFAULT NULL,
    `stock_unit_id` int(11) DEFAULT NULL,
    `kdv` int(11) DEFAULT NULL,
    `stock_group_id` int(11) DEFAULT NULL,
    `stock_type_id` int(11) DEFAULT NULL,
    `miktar` int(11) DEFAULT NULL,
    `indirim_uygula` int(11) DEFAULT NULL,
    `muhasebe_alis_fatura_kodu` varchar(255) DEFAULT NULL,
    `muhasebe_alis_iade_kodu` varchar(255) DEFAULT NULL,
    `muhasebe_alis_iskonto_kodu` varchar(255) DEFAULT NULL,
    `muhasebe_alis_fark_fatura_kodu` varchar(255) DEFAULT NULL,
    `muhasebe_alis_maliyet_kodu` varchar(255) DEFAULT NULL,
    `muhasebe_satis_fatura_kodu` varchar(255) DEFAULT NULL,
    `muhasebe_satis_iade_kodu` varchar(255) DEFAULT NULL,
    `muhasebe_satis_iskonto_kodu` varchar(255) DEFAULT NULL,
    `muhasebe_satis_fark_fatura_kodu` varchar(255) DEFAULT NULL,
    `car` int(11) DEFAULT 0,
    `brake` int(11) DEFAULT 0,
    `bodywork` int(11) DEFAULT 0,
    `internal_control` int(11) DEFAULT 0,
    `tire_and_rim` int(11) DEFAULT 0,
    `sub_control_and_engine` int(11) DEFAULT 0,
    `component` int(11) DEFAULT 0,
    `diagnostic` int(11) DEFAULT 0,
    `rank` int(11) NOT NULL DEFAULT 1,
    `show_on_expertise` int(11) DEFAULT 1,
    `yol_yardimi` tinyint(4) DEFAULT 0,
    `sorgu_hizmeti` tinyint(4) DEFAULT 0,
    `status` int(11) NOT NULL DEFAULT 1,
    `active_normal_payment` tinyint(4) NOT NULL DEFAULT 1,
    `iskonto_tutari` float(15,2) DEFAULT NULL,
    `active_plus_cart` tinyint(4) NOT NULL DEFAULT 1,
    `active_contracts` int(11) DEFAULT 2,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `co2` tinyint(3) UNSIGNED DEFAULT 0,
    PRIMARY KEY (`id`),
    KEY `branch_id` (`branch_id`),
    KEY `status` (`status`),
    KEY `show_on_expertise` (`show_on_expertise`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `stock_groups`
--

CREATE TABLE IF NOT EXISTS `stock_groups` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `stock_type_id` int(11) DEFAULT 0,
    `kod` varchar(255) NOT NULL,
    `parent_id` int(11) NOT NULL DEFAULT 0,
    `name` varchar(255) NOT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `stock_notes`
--

CREATE TABLE IF NOT EXISTS `stock_notes` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `stock_id` int(11) NOT NULL,
    `aciklama` text NOT NULL,
    `uyari_tarihi` date NOT NULL,
    `kayit_tarihi` date NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `stock_prices`
--

CREATE TABLE IF NOT EXISTS `stock_prices` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `stock_id` int(11) NOT NULL,
    `campaign_id` int(11) NOT NULL,
    `sube_kodu` varchar(255) NOT NULL,
    `kdv_dahil_fiyat` double(15,2) NOT NULL,
    `kdv_haric_fiyat` double(15,2) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `stock_id` (`stock_id`),
    KEY `campaign_id` (`campaign_id`),
    KEY `deleted_at` (`deleted_at`),
    KEY `sube_kodu` (`sube_kodu`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `stock_types`
--

CREATE TABLE IF NOT EXISTS `stock_types` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `short_name` varchar(255) DEFAULT NULL,
    `stoklu_satis` int(11) DEFAULT 1,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `stock_units`
--

CREATE TABLE IF NOT EXISTS `stock_units` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `telescope_entries_tags`
--

CREATE TABLE IF NOT EXISTS `telescope_entries_tags` (
    `entry_uuid` char(36) NOT NULL,
    `tag` varchar(255) NOT NULL,
    KEY `telescope_entries_tags_entry_uuid_tag_index` (`entry_uuid`,`tag`),
    KEY `telescope_entries_tags_tag_index` (`tag`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `telescope_monitoring`
--

CREATE TABLE IF NOT EXISTS `telescope_monitoring` (
    `tag` varchar(255) NOT NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `tickets`
--

CREATE TABLE IF NOT EXISTS `tickets` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `uuid` char(255) DEFAULT NULL,
    `branch_id` int(11) DEFAULT 0,
    `user_id` int(11) DEFAULT NULL,
    `related_user` int(11) DEFAULT NULL,
    `to` varchar(255) DEFAULT NULL,
    `priority` int(11) DEFAULT 1,
    `subject` varchar(255) DEFAULT NULL,
    `status` int(11) DEFAULT 2,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    `rating` tinyint(3) UNSIGNED DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ticket_images`
--

CREATE TABLE IF NOT EXISTS `ticket_images` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `ticket_id` bigint(20) UNSIGNED NOT NULL,
    `image` varchar(255) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `ticket_images_ticket_id_index` (`ticket_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ticket_messages`
--

CREATE TABLE IF NOT EXISTS `ticket_messages` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `ticket_id` int(11) NOT NULL,
    `user_id` mediumint(8) UNSIGNED NOT NULL DEFAULT 0,
    `from` varchar(255) DEFAULT NULL,
    `message` text DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE IF NOT EXISTS `users` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `old_id` int(11) DEFAULT NULL,
    `zone_id` smallint(6) DEFAULT 0,
    `branch_id` int(11) NOT NULL,
    `name` varchar(255) NOT NULL,
    `second_name` varchar(255) DEFAULT NULL,
    `surname` varchar(255) DEFAULT NULL,
    `username` varchar(255) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `tc` varchar(255) DEFAULT NULL,
    `dogum_tarihi` date DEFAULT NULL,
    `gender` varchar(255) DEFAULT NULL COMMENT 'male/female',
    `password` varchar(255) NOT NULL,
    `telephone` varchar(255) DEFAULT NULL,
    `second_telephone` varchar(255) DEFAULT NULL,
    `department` varchar(255) DEFAULT NULL,
    `image` varchar(255) DEFAULT NULL,
    `licence_document` varchar(255) DEFAULT NULL,
    `id_document` varchar(255) DEFAULT NULL,
    `address_document` varchar(255) DEFAULT NULL,
    `agreement_document` varchar(255) DEFAULT NULL,
    `marriage_document` varchar(255) DEFAULT NULL,
    `has_phone` varchar(255) DEFAULT NULL,
    `has_computer` varchar(255) DEFAULT NULL,
    `has_car` varchar(255) DEFAULT NULL,
    `has_document` varchar(255) DEFAULT NULL,
    `yearly_work_time` int(11) DEFAULT 0,
    `work_start_date` date DEFAULT '2023-12-19',
    `remain_holiday_date_count` int(11) DEFAULT 0,
    `comment` text DEFAULT NULL,
    `type` varchar(255) NOT NULL DEFAULT 'employee',
    `user_role_group_id` int(11) DEFAULT 1,
    `blood_group` varchar(3) DEFAULT NULL,
    `kvkk_verified` int(11) DEFAULT 0,
    `notification_token` varchar(255) DEFAULT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `email_verified_at` timestamp NULL DEFAULT NULL,
    `remember_token` varchar(100) DEFAULT NULL,
    `password_reset_token` varchar(10) DEFAULT NULL,
    `password_reset_code` varchar(20) DEFAULT NULL,
    `session_lifetime` int(11) DEFAULT 600,
    `first_login` tinyint(4) DEFAULT 1,
    `alt_beden` varchar(10) DEFAULT NULL,
    `ust_beden` varchar(10) DEFAULT NULL,
    `ayakkabi_numarasi` tinyint(4) DEFAULT NULL,
    `totp_secret` varchar(255) DEFAULT NULL,
    `login_token` varchar(32) DEFAULT NULL,
    `login_sms` varchar(10) DEFAULT NULL,
    `receive_sms` tinyint(4) DEFAULT 0,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `users_email_unique` (`email`),
    KEY `status` (`status`),
    KEY `type` (`type`),
    KEY `zone_id` (`zone_id`),
    KEY `branch_id` (`branch_id`),
    KEY `department` (`department`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_addresses`
--

CREATE TABLE IF NOT EXISTS `user_addresses` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` mediumint(9) NOT NULL,
    `title` varchar(50) NOT NULL,
    `address` text NOT NULL,
    `city_id` smallint(6) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_banks`
--

CREATE TABLE IF NOT EXISTS `user_banks` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL,
    `banka_adi` varchar(255) DEFAULT NULL,
    `iban` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_branches2`
--

CREATE TABLE IF NOT EXISTS `user_branches2` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` bigint(20) UNSIGNED NOT NULL,
    `branch_id` bigint(20) UNSIGNED NOT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `user_branches_user_id_foreign` (`user_id`),
    KEY `user_branches_branch_id_foreign` (`branch_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_emergencies`
--

CREATE TABLE IF NOT EXISTS `user_emergencies` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` smallint(6) NOT NULL,
    `title` varchar(255) DEFAULT NULL,
    `name` varchar(255) DEFAULT NULL,
    `telephone` varchar(10) DEFAULT NULL,
    `address` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_phones`
--

CREATE TABLE IF NOT EXISTS `user_phones` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` smallint(6) NOT NULL,
    `title` varchar(50) DEFAULT NULL,
    `telephone` varchar(11) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_roles`
--

CREATE TABLE IF NOT EXISTS `user_roles` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `group_id` int(11) NOT NULL,
    `key` varchar(255) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `group_id` (`group_id`),
    KEY `key` (`key`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_role_groups`
--

CREATE TABLE IF NOT EXISTS `user_role_groups` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `status` int(11) NOT NULL DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `user_saved_filters`
--

CREATE TABLE IF NOT EXISTS `user_saved_filters` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `user_id` mediumint(9) NOT NULL,
    `name` varchar(255) NOT NULL,
    `user_name` varchar(255) DEFAULT NULL,
    `second_name` varchar(255) DEFAULT NULL,
    `surname` varchar(255) DEFAULT NULL,
    `email` varchar(255) DEFAULT NULL,
    `tc` varchar(255) DEFAULT NULL,
    `telephone` varchar(255) DEFAULT NULL,
    `branch_id` varchar(255) DEFAULT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `zones`
--

CREATE TABLE IF NOT EXISTS `zones` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL,
    `status` tinyint(4) DEFAULT 1,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `zone_branches`
--

CREATE TABLE IF NOT EXISTS `zone_branches` (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    `zone_id` smallint(6) NOT NULL,
    `branch_id` smallint(6) NOT NULL,
    `created_at` timestamp NULL DEFAULT NULL,
    `updated_at` timestamp NULL DEFAULT NULL,
    `deleted_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `zone_id` (`zone_id`),
    KEY `branch_id` (`branch_id`),
    KEY `deleted_at` (`deleted_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Indexes for dumped tables
--

--
-- Indexes for table `cars`
--
ALTER TABLE `cars` ADD FULLTEXT KEY `plaka_ft_idx` (`plaka`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers` ADD FULLTEXT KEY `ad_unvan_telefon` (`unvan`,`ad`,`soyad`,`telefon`);

--
-- Constraints for dumped tables
--

--
-- Constraints for table `contract_cities`
--
ALTER TABLE `contract_cities`
    ADD CONSTRAINT `contract_cities_contract_id_foreign` FOREIGN KEY (`contract_id`) REFERENCES `contracts` (`id`);

--
-- Constraints for table `contract_code_branches`
--
ALTER TABLE `contract_code_branches`
    ADD CONSTRAINT `contract_code_branches_branch_id_foreign` FOREIGN KEY (`branch_id`) REFERENCES `branches` (`id`),
  ADD CONSTRAINT `contract_code_branches_contract_code_id_foreign` FOREIGN KEY (`contract_code_id`) REFERENCES `contract_codes` (`id`);

--
-- Constraints for table `contract_code_stocks`
--
ALTER TABLE `contract_code_stocks`
    ADD CONSTRAINT `contract_code_stocks_contract_code_id_foreign` FOREIGN KEY (`contract_code_id`) REFERENCES `contract_codes` (`id`),
  ADD CONSTRAINT `contract_code_stocks_stock_id_foreign` FOREIGN KEY (`stock_id`) REFERENCES `stocks` (`id`);

--
-- Constraints for table `contract_files`
--
ALTER TABLE `contract_files`
    ADD CONSTRAINT `contract_files_contract_id_foreign` FOREIGN KEY (`contract_id`) REFERENCES `contracts` (`id`);

--
-- Constraints for table `contract_periods`
--
ALTER TABLE `contract_periods`
    ADD CONSTRAINT `contract_periods_contract_id_foreign` FOREIGN KEY (`contract_id`) REFERENCES `contracts` (`id`);

--
-- Constraints for table `contract_period_added_limits`
--
ALTER TABLE `contract_period_added_limits`
    ADD CONSTRAINT `contract_period_added_limits_contract_period_id_foreign` FOREIGN KEY (`contract_period_id`) REFERENCES `contract_periods` (`id`),
  ADD CONSTRAINT `contract_period_added_limits_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `contract_stocks`
--
ALTER TABLE `contract_stocks`
    ADD CONSTRAINT `contract_stocks_contract_id_foreign` FOREIGN KEY (`contract_id`) REFERENCES `contracts` (`id`),
  ADD CONSTRAINT `contract_stocks_stock_id_foreign` FOREIGN KEY (`stock_id`) REFERENCES `stocks` (`id`);

--
-- Constraints for table `query_logs_extra`
--
ALTER TABLE `query_logs_extra`
    ADD CONSTRAINT `query_logs_extra_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `telescope_entries_tags`
--
ALTER TABLE `telescope_entries_tags`
    ADD CONSTRAINT `telescope_entries_tags_entry_uuid_foreign` FOREIGN KEY (`entry_uuid`) REFERENCES `telescope_entries` (`uuid`) ON DELETE CASCADE;

--
-- Constraints for table `ticket_images`
--
ALTER TABLE `ticket_images`
    ADD CONSTRAINT `ticket_images_ticket_id_foreign` FOREIGN KEY (`ticket_id`) REFERENCES `tickets` (`id`);
SET FOREIGN_KEY_CHECKS=1;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
