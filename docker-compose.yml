name: umramonline

services:
  web:
    container_name: umramonline_web
    image: nginx:stable-alpine
    working_dir: "/var/www/umramonline"
    environment:
      - APP_ENV=local
      - APP_DEBUG=1
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - C:/xampp/htdocs/projects/umramonline/:/var/www/online.umranoto.com:delegated
      - C:/xampp/htdocs/projects/umramonline/docker/web/umramonline.conf:/etc/nginx/conf.d/umramonline.conf:consistent
      - C:/xampp/htdocs/projects/umramonline/docker/web/umranoto.conf:/etc/nginx/conf.d/umranoto.conf:consistent
      - C:/xampp/htdocs/projects/umramonline:/var/www/umramonline
    networks:
      umramonline_net:
        ipv4_address: ***********
  app:
    container_name: umramonline_app
    build: C:/xampp/htdocs/projects/umramonline/docker/app
    working_dir: "/var/www/umramonline"
    environment:
      - APP_ENV=local
      - APP_DEBUG=1
    volumes:
      - C:/xampp/htdocs/projects/umramonline/:/var/www/online.umranoto.com:delegated
      - C:/xampp/htdocs/projects/umramonline/docker/app/php.ini:/usr/local/etc/php/php.ini
      - C:/xampp/htdocs/projects/umramonline:/var/www/umramonline
    networks:
      umramonline_net:
        ipv4_address: ***********
  db:
    container_name: umramonline_db
    hostname: umramonline_db
    image: mariadb
    restart: "no"
    tty: true
    environment:
      MYSQL_USER: root
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: umramdb
    volumes:
      - C:/xampp/htdocs/projects/umramonline/docker/db/data:/var/lib/mysql
    ports:
      - 3306
      - "33007:3306"
    networks:
      umramonline_net:
        ipv4_address: ***********
  adminer:
    container_name: adminer
    hostname: adminer
    image: adminer
    restart: always
    ports:
      - 8080:8080
    networks:
      umramonline_net:
        ipv4_address: ***********
  redis:
    container_name: umramonline_redis
    image: "redis:alpine"
    ports:
      - 6379
    networks:
      umramonline_net:
        ipv4_address: ***********
  python:
    container_name: umramonline_python
    image: python:3-alpine
    tty: true
    working_dir: "/var/www/umramonline"
    volumes:
      - C:/xampp/htdocs/projects/umramonline/:/var/www/umramonline:delegated
    networks:
      umramonline_net:
        ipv4_address: ***********
  meilisearch:
    container_name: umramonline_meilisearch
    image: getmeili/meilisearch
    volumes:
      - C:/xampp/htdocs/projects/umramonline/docker/meilisearch/:/data.ms:delegated
    ports:
      - 7700:7700
    expose:
      - '7700'
    restart: unless-stopped
    networks:
      umramonline_net:
        ipv4_address: ***********

networks:
  umramonline_net:
    ipam:
      driver: default
      config:
        - subnet: **********/24
