
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,(function(t){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},e(t,n)};function n(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}var i=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},r=new function(){this.browser=new i,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(r.wxa=!0,r.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?r.worker=!0:"undefined"==typeof navigator?(r.node=!0,r.svgSupported=!0):function(t,e){var n=e.browser,i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(n.firefox=!0,n.version=i[1]);r&&(n.ie=!0,n.version=r[1]);o&&(n.edge=!0,n.version=o[1],n.newEdge=+o[1].split(".")[0]>18);a&&(n.weChat=!0);e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!n.ie&&!n.edge,e.pointerEventsSupported="onpointerdown"in window&&(n.edge||n.ie&&+n.version>=11),e.domSupported="undefined"!=typeof document;var s=document.documentElement.style;e.transform3dSupported=(n.ie&&"transition"in s||n.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||n.ie&&+n.version>=9}(navigator.userAgent,r);var o="12px sans-serif";var a,s,l=function(t){var e={};if("undefined"==typeof JSON)return e;for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),u={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(!a){var n=u.createCanvas();a=n&&n.getContext("2d")}if(a)return s!==e&&(s=a.font=e||o),a.measureText(t);t=t||"";var i=/(\d+)px/.exec(e=e||o),r=i&&+i[1]||12,h=0;if(e.indexOf("mono")>=0)h=r*t.length;else for(var c=0;c<t.length;c++){var p=l[t[c]];h+=null==p?r:p*r}return{width:h}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function h(t){for(var e in u)t[e]&&(u[e]=t[e])}var c=E(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],(function(t,e){return t["[object "+e+"]"]=!0,t}),{}),p=E(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],(function(t,e){return t["[object "+e+"Array]"]=!0,t}),{}),f=Object.prototype.toString,d=Array.prototype,g=d.forEach,y=d.filter,v=d.slice,m=d.map,_=function(){}.constructor,x=_?_.prototype:null,w="__proto__",b=2311;function S(){return b++}function M(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function T(t){if(null==t||"object"!=typeof t)return t;var e=t,n=f.call(t);if("[object Array]"===n){if(!ut(t)){e=[];for(var i=0,r=t.length;i<r;i++)e[i]=T(t[i])}}else if(p[n]){if(!ut(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!c[n]&&!ut(t)&&!j(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==w&&(e[a]=T(t[a]));return e}function C(t,e,n){if(!Y(e)||!Y(t))return n?T(e):t;for(var i in e)if(e.hasOwnProperty(i)&&i!==w){var r=t[i],o=e[i];!Y(o)||!Y(r)||H(o)||H(r)||j(o)||j(r)||q(o)||q(r)||ut(o)||ut(r)?!n&&i in t||(t[i]=T(e[i])):C(r,o,n)}return t}function D(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==w&&(t[n]=e[n]);return t}function I(t,e,n){for(var i=B(e),r=0;r<i.length;r++){var o=i[r];(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}return t}var k=u.createCanvas;function A(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function L(t,e){var n=t.prototype;function i(){}for(var r in i.prototype=e.prototype,t.prototype=new i,n)n.hasOwnProperty(r)&&(t.prototype[r]=n[r]);t.prototype.constructor=t,t.superClass=e}function P(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else I(t,e,n)}function O(t){return!!t&&("string"!=typeof t&&"number"==typeof t.length)}function R(t,e,n){if(t&&e)if(t.forEach&&t.forEach===g)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function N(t,e,n){if(!t)return[];if(!e)return it(t);if(t.map&&t.map===m)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function E(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function z(t,e,n){if(!t)return[];if(!e)return it(t);if(t.filter&&t.filter===y)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function B(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}var F=x&&W(x.bind)?x.call.bind(x.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(v.call(arguments)))}};function V(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(v.call(arguments)))}}function H(t){return Array.isArray?Array.isArray(t):"[object Array]"===f.call(t)}function W(t){return"function"==typeof t}function G(t){return"string"==typeof t}function U(t){return"[object String]"===f.call(t)}function X(t){return"number"==typeof t}function Y(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function q(t){return!!c[f.call(t)]}function Z(t){return!!p[f.call(t)]}function j(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function K(t){return null!=t.colorStops}function $(t){return null!=t.image}function Q(t){return"[object RegExp]"===f.call(t)}function J(t){return t!=t}function tt(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function et(t,e){return null!=t?t:e}function nt(t,e,n){return null!=t?t:null!=e?e:n}function it(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return v.apply(t,e)}function rt(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function ot(t,e){if(!t)throw new Error(e)}function at(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var st="__ec_primitive__";function lt(t){t[st]=!0}function ut(t){return t[st]}var ht=function(){function t(){this.data={}}return t.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},t.prototype.has=function(t){return this.data.hasOwnProperty(t)},t.prototype.get=function(t){return this.data[t]},t.prototype.set=function(t,e){return this.data[t]=e,this},t.prototype.keys=function(){return B(this.data)},t.prototype.forEach=function(t){var e=this.data;for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},t}(),ct="function"==typeof Map;var pt=function(){function t(e){var n=H(e);this.data=ct?new Map:new ht;var i=this;function r(t,e){n?i.set(t,e):i.set(e,t)}e instanceof t?e.each(r):e&&R(e,r)}return t.prototype.hasKey=function(t){return this.data.has(t)},t.prototype.get=function(t){return this.data.get(t)},t.prototype.set=function(t,e){return this.data.set(t,e),e},t.prototype.each=function(t,e){this.data.forEach((function(n,i){t.call(e,n,i)}))},t.prototype.keys=function(){var t=this.data.keys();return ct?Array.from(t):t},t.prototype.removeKey=function(t){this.data.delete(t)},t}();function ft(t){return new pt(t)}function dt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n}function gt(t,e){var n;if(Object.create)n=Object.create(t);else{var i=function(){};i.prototype=t,n=new i}return e&&D(n,e),n}function yt(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function vt(t,e){return t.hasOwnProperty(e)}function mt(){}var _t=180/Math.PI,xt=Object.freeze({__proto__:null,guid:S,logError:M,clone:T,merge:C,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=C(n,t[i],e);return n},extend:D,defaults:I,createCanvas:k,indexOf:A,inherits:L,mixin:P,isArrayLike:O,each:R,map:N,reduce:E,filter:z,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},keys:B,bind:F,curry:V,isArray:H,isFunction:W,isString:G,isStringSafe:U,isNumber:X,isObject:Y,isBuiltInObject:q,isTypedArray:Z,isDom:j,isGradientObject:K,isImagePatternObject:$,isRegExp:Q,eqNaN:J,retrieve:tt,retrieve2:et,retrieve3:nt,slice:it,normalizeCssArray:rt,assert:ot,trim:at,setAsPrimitive:lt,isPrimitive:ut,HashMap:pt,createHashMap:ft,concatArray:dt,createObject:gt,disableUserSelect:yt,hasOwn:vt,noop:mt,RADIAN_TO_DEGREE:_t});function wt(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function bt(t){return[t[0],t[1]]}function St(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Mt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Tt(t){return Math.sqrt(Dt(t))}var Ct=Tt;function Dt(t){return t[0]*t[0]+t[1]*t[1]}var It=Dt;function kt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function At(t,e){var n=Tt(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Lt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Pt=Lt;function Ot(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Rt=Ot;function Nt(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function Et(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function zt(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function Bt(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var Ft=Object.freeze({__proto__:null,create:wt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:bt,set:function(t,e,n){return t[0]=e,t[1]=n,t},add:St,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},sub:Mt,len:Tt,length:Ct,lenSquare:Dt,lengthSquare:It,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:kt,normalize:At,distance:Lt,dist:Pt,distanceSquare:Ot,distSquare:Rt,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:Nt,applyTransform:Et,min:zt,max:Bt}),Vt=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},Ht=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Vt(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,r=n-this._x,o=i-this._y;this._x=n,this._y=i,e.drift(r,o,t),this.handler.dispatchToElement(new Vt(e,t),"drag",t.event);var a=this.handler.findHover(n,i,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.handler.dispatchToElement(new Vt(s,t),"dragleave",t.event),a&&a!==s&&this.handler.dispatchToElement(new Vt(a,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Vt(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Vt(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}(),Wt=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),!n||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;var s={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},l=r[t].length-1,u=r[t][l];return u&&u.callAtLast?r[t].splice(l,0,s):r[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var n=this._$handlers;if(!n)return this;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},t.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t}(),Gt=Math.log(2);function Ut(t,e,n,i,r,o){var a=i+"-"+r,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/Gt);return t[n][l]}for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,f=0;p<s;p++){var d=1<<p;d&r||(c+=(f%2?-1:1)*t[n][p]*Ut(t,e-1,h,u,r|d,o),f++)}return o[a]=c,c}function Xt(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=Ut(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*Ut(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var Yt="___zrEVENTSAVED";function qt(t,e,n,i,o){if(e.getBoundingClientRect&&r.domSupported&&!Zt(e)){var a=e[Yt]||(e[Yt]={}),s=function(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=o%2,l=(o>>1)%2;a.style.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[s]+":0",r[l]+":0",i[1-s]+":auto",r[1-l]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return n}(e,a),l=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,f=h.top;a.push(p,f),l=l&&o&&p===o[c]&&f===o[c+1],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?Xt(s,a):Xt(a,s))}(s,a,o);if(l)return l(t,n,i),!0}return!1}function Zt(t){return"CANVAS"===t.nodeName.toUpperCase()}var jt=/([&<>"'])/g,Kt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function $t(t){return null==t?"":(t+"").replace(jt,(function(t,e){return Kt[e]}))}var Qt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Jt=[],te=r.browser.firefox&&+r.browser.version.split(".")[0]<39;function ee(t,e,n,i){return n=n||{},i?ne(t,e,n):te&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):ne(t,e,n),n}function ne(t,e,n){if(r.domSupported&&t.getBoundingClientRect){var i=e.clientX,o=e.clientY;if(Zt(t)){var a=t.getBoundingClientRect();return n.zrX=i-a.left,void(n.zrY=o-a.top)}if(qt(Jt,t,i,o))return n.zrX=Jt[0],void(n.zrY=Jt[1])}n.zrX=n.zrY=0}function ie(t){return t||window.event}function re(t,e,n){if(null!=(e=ie(e)).zrX)return e;var i=e.type;if(i&&i.indexOf("touch")>=0){var r="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];r&&ee(t,r,e,n)}else{ee(t,e,e,n);var o=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,i=t.deltaY;if(null==n||null==i)return e;return 3*(0!==i?Math.abs(i):Math.abs(n))*(i>0?-1:i<0?1:n>0?-1:1)}(e);e.zrDelta=o?o/120:-(e.detail||0)/3}var a=e.button;return null==e.which&&void 0!==a&&Qt.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}var oe=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=ee(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},t.prototype._recognize=function(t){for(var e in se)if(se.hasOwnProperty(e)){var n=se[e](this._track,t);if(n)return n}},t}();function ae(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}var se={pinch:function(t,e){var n=t.length;if(n){var i,r=(t[n-1]||{}).points,o=(t[n-2]||{}).points||r;if(o&&o.length>1&&r&&r.length>1){var a=ae(r)/ae(o);!isFinite(a)&&(a=1),e.pinchScale=a;var s=[((i=r)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}};function le(){return[1,0,0,1,0,0]}function ue(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function he(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function ce(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function pe(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function fe(t,e,n){var i=e[0],r=e[2],o=e[4],a=e[1],s=e[3],l=e[5],u=Math.sin(n),h=Math.cos(n);return t[0]=i*h+a*u,t[1]=-i*u+a*h,t[2]=r*h+s*u,t[3]=-r*u+h*s,t[4]=h*o+u*l,t[5]=h*l-u*o,t}function de(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function ge(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=n*a-o*i;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-a*r)*l,t[5]=(o*r-n*s)*l,t):null}var ye=Object.freeze({__proto__:null,create:le,identity:ue,copy:he,mul:ce,translate:pe,rotate:fe,scale:de,invert:ge,clone:function(t){var e=[1,0,0,1,0,0];return he(e,t),e}}),ve=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,n=this.y-t.y;return e*e+n*n},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,n=this.y;return this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,n){t.x=e,t.y=n},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},t.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},t.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},t.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},t.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},t}(),me=Math.min,_e=Math.max,xe=new ve,we=new ve,be=new ve,Se=new ve,Me=new ve,Te=new ve,Ce=function(){function t(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}return t.prototype.union=function(t){var e=me(t.x,this.x),n=me(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=_e(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=_e(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,n=t.width/e.width,i=t.height/e.height,r=[1,0,0,1,0,0];return pe(r,r,[-e.x,-e.y]),de(r,r,[n,i]),pe(r,r,[t.x,t.y]),r},t.prototype.intersect=function(e,n){if(!e)return!1;e instanceof t||(e=t.create(e));var i=this,r=i.x,o=i.x+i.width,a=i.y,s=i.y+i.height,l=e.x,u=e.x+e.width,h=e.y,c=e.y+e.height,p=!(o<l||u<r||s<h||c<a);if(n){var f=1/0,d=0,g=Math.abs(o-l),y=Math.abs(u-r),v=Math.abs(s-h),m=Math.abs(c-a),_=Math.min(g,y),x=Math.min(v,m);o<l||u<r?_>d&&(d=_,g<y?ve.set(Te,-g,0):ve.set(Te,y,0)):_<f&&(f=_,g<y?ve.set(Me,g,0):ve.set(Me,-y,0)),s<h||c<a?x>d&&(d=x,v<m?ve.set(Te,0,-v):ve.set(Te,0,m)):_<f&&(f=_,v<m?ve.set(Me,0,v):ve.set(Me,0,-m))}return n&&ve.copy(n,p?Me:Te),p},t.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,n,i){if(i){if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var r=i[0],o=i[3],a=i[4],s=i[5];return e.x=n.x*r+a,e.y=n.y*o+s,e.width=n.width*r,e.height=n.height*o,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}xe.x=be.x=n.x,xe.y=Se.y=n.y,we.x=Se.x=n.x+n.width,we.y=be.y=n.y+n.height,xe.transform(i),Se.transform(i),we.transform(i),be.transform(i),e.x=me(xe.x,we.x,be.x,Se.x),e.y=me(xe.y,we.y,be.y,Se.y);var l=_e(xe.x,we.x,be.x,Se.x),u=_e(xe.y,we.y,be.y,Se.y);e.width=l-e.x,e.height=u-e.y}else e!==n&&t.copy(e,n)},t}(),De="silent";function Ie(){!function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}(this.event)}var ke=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.handler=null,e}return n(e,t),e.prototype.dispose=function(){},e.prototype.setCursor=function(){},e}(Wt),Ae=function(t,e){this.x=t,this.y=e},Le=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Pe=new Ce(0,0,0,0),Oe=function(t){function e(e,n,i,r,o){var a=t.call(this)||this;return a._hovered=new Ae(0,0),a.storage=e,a.painter=n,a.painterRoot=r,a._pointerSize=o,i=i||new ke,a.proxy=null,a.setHandlerProxy(i),a._draggingMgr=new Ht(a),a}return n(e,t),e.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(R(Le,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},e.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=Ee(this,e,n),r=this._hovered,o=r.target;o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target);var a=this._hovered=i?new Ae(e,n):this.findHover(e,n),s=a.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},e.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},e.prototype.resize=function(){this._hovered=new Ae(0,0)},e.prototype.dispatch=function(t,e){var n=this[t];n&&n.call(this,e)},e.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},e.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},e.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o=function(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:Ie}}(e,t,n);i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget?i.__hostTarget:i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)})))}},e.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new Ae(t,e);if(Ne(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new Ce(t-s,e-s,a,a),u=i.length-1;u>=0;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(Pe.copy(h.getBoundingRect()),h.transform&&Pe.applyTransform(h.transform),Pe.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,f=0;f<s;f+=4)for(var d=0;d<p;d+=c){if(Ne(o,r,t+f*Math.cos(d),e+f*Math.sin(d),n),r.target)return r}}return r},e.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new oe);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r;var o=new Ae;o.target=i.target,this.dispatchToElement(o,r,i.event)}},e}(Wt);function Re(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1;i.silent&&(r=!0)}var s=i.__hostTarget;i=s||i.parent}return!r||De}return!1}function Ne(t,e,n,i,r){for(var o=t.length-1;o>=0;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=Re(a,n,i))&&(!e.topTarget&&(e.topTarget=a),s!==De)){e.target=a;break}}}function Ee(t,e,n){var i=t.painter;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}R(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){Oe.prototype[t]=function(e){var n,i,r=e.zrX,o=e.zrY,a=Ee(this,r,o);if("mouseup"===t&&a||(i=(n=this.findHover(r,o)).target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||Pt(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}));function ze(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;!function(t,e,n){n--;for(;e<n;){var i=t[e];t[e++]=t[n],t[n--]=i}}(t,e,r)}else for(;r<n&&i(t[r],t[r-1])>=0;)r++;return r-e}function Be(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=o+1;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;u>0;)t[s+u]=t[s+u-1],u--}t[s]=a}}function Fe(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])>0){for(s=i-r;l<s&&o(t,e[n+r+l])>0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])>0?a=h+1:l=h}return l}function Ve(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}else{for(s=i-r;l<s&&o(t,e[n+r+l])>=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function He(t,e){var n,i,r=7,o=0;t.length;var a=[];function s(s){var l=n[s],u=i[s],h=n[s+1],c=i[s+1];i[s]=u+c,s===o-3&&(n[s+1]=n[s+2],i[s+1]=i[s+2]),o--;var p=Ve(t[h],t,l,u,0,e);l+=p,0!==(u-=p)&&0!==(c=Fe(t[l+u-1],t,h,c,c-1,e))&&(u<=c?function(n,i,o,s){var l=0;for(l=0;l<i;l++)a[l]=t[n+l];var u=0,h=o,c=n;if(t[c++]=t[h++],0==--s){for(l=0;l<i;l++)t[c+l]=a[u+l];return}if(1===i){for(l=0;l<s;l++)t[c+l]=t[h+l];return void(t[c+s]=a[u])}var p,f,d,g=r;for(;;){p=0,f=0,d=!1;do{if(e(t[h],a[u])<0){if(t[c++]=t[h++],f++,p=0,0==--s){d=!0;break}}else if(t[c++]=a[u++],p++,f=0,1==--i){d=!0;break}}while((p|f)<g);if(d)break;do{if(0!==(p=Ve(t[h],a,u,i,0,e))){for(l=0;l<p;l++)t[c+l]=a[u+l];if(c+=p,u+=p,(i-=p)<=1){d=!0;break}}if(t[c++]=t[h++],0==--s){d=!0;break}if(0!==(f=Fe(a[u],t,h,s,0,e))){for(l=0;l<f;l++)t[c+l]=t[h+l];if(c+=f,h+=f,0===(s-=f)){d=!0;break}}if(t[c++]=a[u++],1==--i){d=!0;break}g--}while(p>=7||f>=7);if(d)break;g<0&&(g=0),g+=2}if((r=g)<1&&(r=1),1===i){for(l=0;l<s;l++)t[c+l]=t[h+l];t[c+s]=a[u]}else{if(0===i)throw new Error;for(l=0;l<i;l++)t[c+l]=a[u+l]}}(l,u,h,c):function(n,i,o,s){var l=0;for(l=0;l<s;l++)a[l]=t[o+l];var u=n+i-1,h=s-1,c=o+s-1,p=0,f=0;if(t[c--]=t[u--],0==--i){for(p=c-(s-1),l=0;l<s;l++)t[p+l]=a[l];return}if(1===s){for(f=(c-=i)+1,p=(u-=i)+1,l=i-1;l>=0;l--)t[f+l]=t[p+l];return void(t[c]=a[h])}var d=r;for(;;){var g=0,y=0,v=!1;do{if(e(a[h],t[u])<0){if(t[c--]=t[u--],g++,y=0,0==--i){v=!0;break}}else if(t[c--]=a[h--],y++,g=0,1==--s){v=!0;break}}while((g|y)<d);if(v)break;do{if(0!==(g=i-Ve(a[h],t,n,i,i-1,e))){for(i-=g,f=(c-=g)+1,p=(u-=g)+1,l=g-1;l>=0;l--)t[f+l]=t[p+l];if(0===i){v=!0;break}}if(t[c--]=a[h--],1==--s){v=!0;break}if(0!==(y=s-Fe(t[u],a,0,s,s-1,e))){for(s-=y,f=(c-=y)+1,p=(h-=y)+1,l=0;l<y;l++)t[f+l]=a[p+l];if(s<=1){v=!0;break}}if(t[c--]=t[u--],0==--i){v=!0;break}d--}while(g>=7||y>=7);if(v)break;d<0&&(d=0),d+=2}(r=d)<1&&(r=1);if(1===s){for(f=(c-=i)+1,p=(u-=i)+1,l=i-1;l>=0;l--)t[f+l]=t[p+l];t[c]=a[h]}else{if(0===s)throw new Error;for(p=c-(s-1),l=0;l<s;l++)t[p+l]=a[l]}}(l,u,h,c))}return n=[],i=[],{mergeRuns:function(){for(;o>1;){var t=o-2;if(t>=1&&i[t-1]<=i[t]+i[t+1]||t>=2&&i[t-2]<=i[t]+i[t-1])i[t-1]<i[t+1]&&t--;else if(i[t]>i[t+1])break;s(t)}},forceMergeRuns:function(){for(;o>1;){var t=o-2;t>0&&i[t-1]<i[t+1]&&t--,s(t)}},pushRun:function(t,e){n[o]=t,i[o]=e,o+=1}}}function We(t,e,n,i){n||(n=0),i||(i=t.length);var r=i-n;if(!(r<2)){var o=0;if(r<32)Be(t,n,i,n+(o=ze(t,n,i,e)),e);else{var a=He(t,e),s=function(t){for(var e=0;t>=32;)e|=1&t,t>>=1;return t+e}(r);do{if((o=ze(t,n,i,e))<s){var l=r;l>s&&(l=s),Be(t,n,n+l,n+o,e),o=l}a.pushRun(n,o),a.mergeRuns(),r-=o,n+=o}while(0!==r);a.forceMergeRuns()}}}var Ge=!1;function Ue(){Ge||(Ge=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Xe(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var Ye=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Xe}return t.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,We(n,Xe)},t.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),o=r,r=r.getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=1),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{var u=t;e&&e.length?u.__clipPaths=e:u.__clipPaths&&u.__clipPaths.length>0&&(u.__clipPaths=[]),isNaN(u.z)&&(Ue(),u.z=0),isNaN(u.z2)&&(Ue(),u.z2=0),isNaN(u.zlevel)&&(Ue(),u.zlevel=0),this._displayList[this._displayListLen++]=u}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,n);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,n);var p=t.getTextContent();p&&this._updateAndAddDisplayable(p,e,n)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=A(this._roots,t);i>=0&&this._roots.splice(i,1)}},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}(),qe=r.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},Ze={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-Ze.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*Ze.bounceIn(2*t):.5*Ze.bounceOut(2*t-1)+.5}},je=Math.pow,Ke=Math.sqrt,$e=1e-8,Qe=1e-4,Je=Ke(3),tn=1/3,en=wt(),nn=wt(),rn=wt();function on(t){return t>-1e-8&&t<$e}function an(t){return t>$e||t<-1e-8}function sn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function ln(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function un(t,e,n,i,r,o){var a=i+3*(e-n)-t,s=3*(n-2*e+t),l=3*(e-t),u=t-r,h=s*s-3*a*l,c=s*l-9*a*u,p=l*l-3*s*u,f=0;if(on(h)&&on(c)){if(on(s))o[0]=0;else(M=-l/s)>=0&&M<=1&&(o[f++]=M)}else{var d=c*c-4*h*p;if(on(d)){var g=c/h,y=-g/2;(M=-s/a+g)>=0&&M<=1&&(o[f++]=M),y>=0&&y<=1&&(o[f++]=y)}else if(d>0){var v=Ke(d),m=h*s+1.5*a*(-c+v),_=h*s+1.5*a*(-c-v);(M=(-s-((m=m<0?-je(-m,tn):je(m,tn))+(_=_<0?-je(-_,tn):je(_,tn))))/(3*a))>=0&&M<=1&&(o[f++]=M)}else{var x=(2*h*s-3*a*c)/(2*Ke(h*h*h)),w=Math.acos(x)/3,b=Ke(h),S=Math.cos(w),M=(-s-2*b*S)/(3*a),T=(y=(-s+b*(S+Je*Math.sin(w)))/(3*a),(-s+b*(S-Je*Math.sin(w)))/(3*a));M>=0&&M<=1&&(o[f++]=M),y>=0&&y<=1&&(o[f++]=y),T>=0&&T<=1&&(o[f++]=T)}}return f}function hn(t,e,n,i,r){var o=6*n-12*e+6*t,a=9*e+3*i-3*t-9*n,s=3*e-3*t,l=0;if(on(a)){if(an(o))(h=-s/o)>=0&&h<=1&&(r[l++]=h)}else{var u=o*o-4*a*s;if(on(u))r[0]=-o/(2*a);else if(u>0){var h,c=Ke(u),p=(-o-c)/(2*a);(h=(-o+c)/(2*a))>=0&&h<=1&&(r[l++]=h),p>=0&&p<=1&&(r[l++]=p)}}return l}function cn(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-a)*r+a,h=(l-s)*r+s,c=(h-u)*r+u;o[0]=t,o[1]=a,o[2]=u,o[3]=c,o[4]=c,o[5]=h,o[6]=l,o[7]=i}function pn(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,f=1;f<=l;f++){var d=f*p,g=sn(t,n,r,a,d),y=sn(e,i,o,s,d),v=g-u,m=y-h;c+=Math.sqrt(v*v+m*m),u=g,h=y}return c}function fn(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function dn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function gn(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function yn(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}function vn(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,f=fn(t,n,r,p),d=fn(e,i,o,p),g=f-s,y=d-l;u+=Math.sqrt(g*g+y*y),s=f,l=d}return u}var mn=/cubic-bezier\(([0-9,\.e ]+)\)/;function _n(t){var e=t&&mn.exec(t);if(e){var n=e[1].split(","),i=+at(n[0]),r=+at(n[1]),o=+at(n[2]),a=+at(n[3]);if(isNaN(i+r+o+a))return;var s=[];return function(t){return t<=0?0:t>=1?1:un(0,i,o,1,t,s)&&sn(0,r,a,1,s[0])}}}var xn=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||mt,this.ondestroy=t.ondestroy||mt,this.onrestart=t.onrestart||mt,t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n;r<0&&(r=0),r=Math.min(r,1);var o=this.easingFunc,a=o?o(r):r;if(this.onframe(a),1===r){if(!this.loop)return!0;var s=i%n;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=W(t)?t:Ze[t]||_n(t)},t}(),wn=function(t){this.value=t},bn=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new wn(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}(),Sn=function(){function t(t){this._list=new bn,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var n=this._list,i=this._map,r=null;if(null==i[t]){var o=n.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var s=n.head;n.remove(s),delete i[s.key],r=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new wn(e),a.key=t,n.insertEntry(a),i[t]=a}return r},t.prototype.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}(),Mn={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Tn(t){return(t=Math.round(t))<0?0:t>255?255:t}function Cn(t){return t<0?0:t>1?1:t}function Dn(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Tn(parseFloat(e)/100*255):Tn(parseInt(e,10))}function In(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Cn(parseFloat(e)/100):Cn(parseFloat(e))}function kn(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function An(t,e,n){return t+(e-t)*n}function Ln(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function Pn(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var On=new Sn(20),Rn=null;function Nn(t,e){Rn&&Pn(Rn,e),Rn=On.put(t,Rn||e.slice())}function En(t,e){if(t){e=e||[];var n=On.get(t);if(n)return Pn(e,n);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in Mn)return Pn(e,Mn[i]),Nn(t,e),e;var r,o=i.length;if("#"===i.charAt(0))return 4===o||5===o?(r=parseInt(i.slice(1,4),16))>=0&&r<=4095?(Ln(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===o?parseInt(i.slice(4),16)/15:1),Nn(t,e),e):void Ln(e,0,0,0,1):7===o||9===o?(r=parseInt(i.slice(1,7),16))>=0&&r<=16777215?(Ln(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===o?parseInt(i.slice(7),16)/255:1),Nn(t,e),e):void Ln(e,0,0,0,1):void 0;var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var l=i.substr(0,a),u=i.substr(a+1,s-(a+1)).split(","),h=1;switch(l){case"rgba":if(4!==u.length)return 3===u.length?Ln(e,+u[0],+u[1],+u[2],1):Ln(e,0,0,0,1);h=In(u.pop());case"rgb":return u.length>=3?(Ln(e,Dn(u[0]),Dn(u[1]),Dn(u[2]),3===u.length?h:In(u[3])),Nn(t,e),e):void Ln(e,0,0,0,1);case"hsla":return 4!==u.length?void Ln(e,0,0,0,1):(u[3]=In(u[3]),zn(u,e),Nn(t,e),e);case"hsl":return 3!==u.length?void Ln(e,0,0,0,1):(zn(u,e),Nn(t,e),e);default:return}}Ln(e,0,0,0,1)}}function zn(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=In(t[1]),r=In(t[2]),o=r<=.5?r*(i+1):r+i-r*i,a=2*r-o;return Ln(e=e||[],Tn(255*kn(a,o,n+1/3)),Tn(255*kn(a,o,n)),Tn(255*kn(a,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Bn(t,e){var n=En(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:n[i]<0&&(n[i]=0);return Gn(n,4===n.length?"rgba":"rgb")}}function Fn(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=e[r],s=e[o],l=i-r;return n[0]=Tn(An(a[0],s[0],l)),n[1]=Tn(An(a[1],s[1],l)),n[2]=Tn(An(a[2],s[2],l)),n[3]=Cn(An(a[3],s[3],l)),n}}var Vn=Fn;function Hn(t,e,n){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=En(e[r]),s=En(e[o]),l=i-r,u=Gn([Tn(An(a[0],s[0],l)),Tn(An(a[1],s[1],l)),Tn(An(a[2],s[2],l)),Cn(An(a[3],s[3],l))],"rgba");return n?{color:u,leftIndex:r,rightIndex:o,value:i}:u}}var Wn=Hn;function Gn(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}function Un(t,e){var n=En(t);return n?(.299*n[0]+.587*n[1]+.114*n[2])*n[3]/255+(1-n[3])*e:0}var Xn=Object.freeze({__proto__:null,parse:En,lift:Bn,toHex:function(t){var e=En(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)},fastLerp:Fn,fastMapToColor:Vn,lerp:Hn,mapToColor:Wn,modifyHSL:function(t,e,n,i){var r,o=En(t);if(t)return o=function(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),l=s-a,u=(s+a)/2;if(0===l)e=0,n=0;else{n=u<.5?l/(s+a):l/(2-s-a);var h=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,p=((s-o)/6+l/2)/l;i===s?e=p-c:r===s?e=1/3+h-p:o===s&&(e=2/3+c-h),e<0&&(e+=1),e>1&&(e-=1)}var f=[360*e,n,u];return null!=t[3]&&f.push(t[3]),f}}(o),null!=e&&(o[0]=(r=e,(r=Math.round(r))<0?0:r>360?360:r)),null!=n&&(o[1]=In(n)),null!=i&&(o[2]=In(i)),Gn(zn(o),"rgba")},modifyAlpha:function(t,e){var n=En(t);if(n&&null!=e)return n[3]=Cn(e),Gn(n,"rgba")},stringify:Gn,lum:Un,random:function(){return Gn([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")}});var Yn=Array.prototype.slice;function qn(t,e,n){return(e-t)*n+t}function Zn(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=qn(e[o],n[o],i);return t}function jn(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function Kn(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function $n(t,e){for(var n=t.length,i=e.length,r=n>i?e:t,o=Math.min(n,i),a=r[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(n,i);s++)r.push({offset:a.offset,color:a.color.slice()})}function Qn(t,e,n){var i=t,r=e;if(i.push&&r.push){var o=i.length,a=r.length;if(o!==a)if(o>a)i.length=a;else for(var s=o;s<a;s++)i.push(1===n?r[s]:Yn.call(r[s]));var l=i[0]&&i[0].length;for(s=0;s<i.length;s++)if(1===n)isNaN(i[s])&&(i[s]=r[s]);else for(var u=0;u<l;u++)isNaN(i[s][u])&&(i[s][u]=r[s][u])}}function Jn(t){if(O(t)){var e=t.length;if(O(t[0])){for(var n=[],i=0;i<e;i++)n.push(Yn.call(t[i]));return n}return Yn.call(t)}return t}function ti(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function ei(t){return 4===t||5===t}function ni(t){return 1===t||2===t}var ii=[0,0,0,0],ri=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i=this.keyframes,r=i.length,o=!1,a=6,s=e;if(O(e)){var l=function(t){return O(t&&t[0])?2:1}(e);a=l,(1===l&&!X(e[0])||2===l&&!X(e[0][0]))&&(o=!0)}else if(X(e)&&!J(e))a=0;else if(G(e))if(isNaN(+e)){var u=En(e);u&&(s=u,a=3)}else a=0;else if(K(e)){var h=D({},s);h.colorStops=N(e.colorStops,(function(t){return{offset:t.offset,color:En(t.color)}})),"linear"===e.type?a=4:function(t){return"radial"===t.type}(e)&&(a=5),s=h}0===r?this.valType=a:a===this.valType&&6!==a||(o=!0),this.discrete=this.discrete||o;var c={time:t,value:s,rawValue:e,percent:0};return n&&(c.easing=n,c.easingFunc=W(n)?n:Ze[n]||_n(n)),i.push(c),c},t.prototype.prepare=function(t,e){var n=this.keyframes;this._needsSort&&n.sort((function(t,e){return t.time-e.time}));for(var i=this.valType,r=n.length,o=n[r-1],a=this.discrete,s=ni(i),l=ei(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;h.percent=h.time/t,a||(s&&u!==r-1?Qn(c,p,i):l&&$n(c.colorStops,p.colorStops))}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;var f=n[0].value;for(u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-f:3===i?n[u].additiveValue=jn([],n[u].value,f,-1):ni(i)&&(n[u].additiveValue=1===i?jn([],n[u].value,f,-1):Kn([],n[u].value,f,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o=null!=this._additiveTrack,a=o?"additiveValue":"value",s=this.valType,l=this.keyframes,u=l.length,h=this.propName,c=3===s,p=this._lastFr,f=Math.min;if(1===u)i=r=l[0];else{if(e<0)n=0;else if(e<this._lastFrP){for(n=f(p+1,u-1);n>=0&&!(l[n].percent<=e);n--);n=f(n,u-2)}else{for(n=p;n<u&&!(l[n].percent>e);n++);n=f(n-1,u-2)}r=l[n+1],i=l[n]}if(i&&r){this._lastFr=n,this._lastFrP=e;var d=r.percent-i.percent,g=0===d?1:f((e-i.percent)/d,1);r.easingFunc&&(g=r.easingFunc(g));var y=o?this._additiveValue:c?ii:t[h];if(!ni(s)&&!c||y||(y=this._additiveValue=[]),this.discrete)t[h]=g<1?i.rawValue:r.rawValue;else if(ni(s))1===s?Zn(y,i[a],r[a],g):function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=qn(e[a][s],n[a][s],i)}}(y,i[a],r[a],g);else if(ei(s)){var v=i[a],m=r[a],_=4===s;t[h]={type:_?"linear":"radial",x:qn(v.x,m.x,g),y:qn(v.y,m.y,g),colorStops:N(v.colorStops,(function(t,e){var n=m.colorStops[e];return{offset:qn(t.offset,n.offset,g),color:ti(Zn([],t.color,n.color,g))}})),global:m.global},_?(t[h].x2=qn(v.x2,m.x2,g),t[h].y2=qn(v.y2,m.y2,g)):t[h].r=qn(v.r,m.r,g)}else if(c)Zn(y,i[a],r[a],g),o||(t[h]=ti(y));else{var x=qn(i[a],r[a],g);o?this._additiveValue=x:t[h]=x}o&&this._addToTarget(t)}}},t.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(En(t[n],ii),jn(ii,ii,i,1),t[n]=ti(ii)):1===e?jn(t[n],t[n],i,1):2===e&&Kn(t[n],t[n],i,1)},t}(),oi=function(){function t(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&i?M("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,B(e),n)},t.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o],s=r[a];if(!s){s=r[a]=new ri(a);var l=void 0,u=this._getAdditiveTrack(a);if(u){var h=u.keyframes,c=h[h.length-1];l=c&&c.value,3===u.valType&&l&&(l=ti(l))}else l=this._target[a];if(null==l)continue;t>0&&s.addKeyframe(0,Jn(l),i),this._trackKeys.push(a)}s.addKeyframe(t,Jn(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,n=[],i=this._maxTime||0,r=0;r<this._trackKeys.length;r++){var o=this._trackKeys[r],a=this._tracks[o],s=this._getAdditiveTrack(o),l=a.keyframes,u=l.length;if(a.prepare(i,s),a.needsAnimate())if(!this._allowDiscrete&&a.discrete){var h=l[u-1];h&&(e._target[a.propName]=h.rawValue),a.setFinished()}else n.push(a)}if(n.length||this._force){var c=new xn({life:i,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var i=e._additiveAnimators;if(i){for(var r=!1,o=0;o<i.length;o++)if(i[o]._clip){r=!0;break}r||(e._additiveAnimators=null)}for(o=0;o<n.length;o++)n[o].step(e._target,t);var a=e._onframeCbs;if(a)for(o=0;o<a.length;o++)a[o](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return N(this._trackKeys,(function(e){return t._tracks[e]}))},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}var a=!0;for(r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];if(o&&!o.isFinished()){var a=o.keyframes,s=a[n?0:a.length-1];s&&(t[r]=Jn(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||B(t);for(var n=0;n<e.length;n++){var i=e[n],r=this._tracks[i];if(r){var o=r.keyframes;if(o.length>1){var a=o.pop();r.addKeyframe(a.time,t[i]),r.prepare(this._maxTime,r.getAdditiveTrack())}}}},t}();function ai(){return(new Date).getTime()}var si,li,ui=function(t){function e(e){var n=t.call(this)||this;return n._running=!1,n._time=0,n._pausedTime=0,n._pauseStart=0,n._paused=!1,e=e||{},n.stage=e.stage||{},n}return n(e,t),e.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},e.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},e.prototype.removeClip=function(t){if(t.animation){var e=t.prev,n=t.next;e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},e.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},e.prototype.update=function(t){for(var e=ai()-this._pausedTime,n=e-this._time,i=this._head;i;){var r=i.next;i.step(e,n)?(i.ondestroy(),this.removeClip(i),i=r):i=r}this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},e.prototype._startLoop=function(){var t=this;this._running=!0,qe((function e(){t._running&&(qe(e),!t._paused&&t.update())}))},e.prototype.start=function(){this._running||(this._time=ai(),this._pausedTime=0,this._startLoop())},e.prototype.stop=function(){this._running=!1},e.prototype.pause=function(){this._paused||(this._pauseStart=ai(),this._paused=!0)},e.prototype.resume=function(){this._paused&&(this._pausedTime+=ai()-this._pauseStart,this._paused=!1)},e.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},e.prototype.isFinished=function(){return null==this._head},e.prototype.animate=function(t,e){e=e||{},this.start();var n=new oi(t,e.loop);return this.addAnimator(n),n},e}(Wt),hi=r.domSupported,ci=(li={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:si=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:N(si,(function(t){var e=t.replace("mouse","pointer");return li.hasOwnProperty(e)?e:t}))}),pi=["mousemove","mouseup"],fi=["pointermove","pointerup"],di=!1;function gi(t){var e=t.pointerType;return"pen"===e||"touch"===e}function yi(t){t&&(t.zrByTouch=!0)}function vi(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var mi=function(t,e){this.stopPropagation=mt,this.stopImmediatePropagation=mt,this.preventDefault=mt,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},_i={mousedown:function(t){t=re(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=re(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=re(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){vi(this,(t=re(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){di=!0,t=re(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){di||(t=re(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){yi(t=re(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),_i.mousemove.call(this,t),_i.mousedown.call(this,t)},touchmove:function(t){yi(t=re(this.dom,t)),this.handler.processGesture(t,"change"),_i.mousemove.call(this,t)},touchend:function(t){yi(t=re(this.dom,t)),this.handler.processGesture(t,"end"),_i.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&_i.click.call(this,t)},pointerdown:function(t){_i.mousedown.call(this,t)},pointermove:function(t){gi(t)||_i.mousemove.call(this,t)},pointerup:function(t){_i.mouseup.call(this,t)},pointerout:function(t){gi(t)||_i.mouseout.call(this,t)}};R(["click","dblclick","contextmenu"],(function(t){_i[t]=function(e){e=re(this.dom,e),this.trigger(t,e)}}));var xi={pointermove:function(t){gi(t)||xi.mousemove.call(this,t)},pointerup:function(t){xi.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function wi(t,e){var n=e.domHandlers;r.pointerEventsSupported?R(ci.pointer,(function(i){Si(e,i,(function(e){n[i].call(t,e)}))})):(r.touchEventsSupported&&R(ci.touch,(function(i){Si(e,i,(function(r){n[i].call(t,r),function(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}(e)}))})),R(ci.mouse,(function(i){Si(e,i,(function(r){r=ie(r),e.touching||n[i].call(t,r)}))})))}function bi(t,e){function n(n){Si(e,n,(function(i){i=ie(i),vi(t,i.target)||(i=function(t,e){return re(t.dom,new mi(t,e),!0)}(t,i),e.domHandlers[n].call(t,i))}),{capture:!0})}r.pointerEventsSupported?R(fi,n):r.touchEventsSupported||R(pi,n)}function Si(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,function(t,e,n,i){t.addEventListener(e,n,i)}(t.domTarget,e,n,i)}function Mi(t){var e,n,i,r,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,n=a,i=o[a],r=t.listenerOpts[a],e.removeEventListener(n,i,r));t.mounted={}}var Ti=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},Ci=function(t){function e(e,n){var i=t.call(this)||this;return i.__pointerCapturing=!1,i.dom=e,i.painterRoot=n,i._localHandlerScope=new Ti(e,_i),hi&&(i._globalHandlerScope=new Ti(document,xi)),wi(i,i._localHandlerScope),i}return n(e,t),e.prototype.dispose=function(){Mi(this._localHandlerScope),hi&&Mi(this._globalHandlerScope)},e.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},e.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,hi&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?bi(this,e):Mi(e)}},e}(Wt),Di=1;r.hasGlobalWindow&&(Di=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Ii=Di,ki="#333",Ai="#ccc",Li=ue,Pi=5e-5;function Oi(t){return t>Pi||t<-5e-5}var Ri=[],Ni=[],Ei=[1,0,0,1,0,0],zi=Math.abs,Bi=function(){function t(){}return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return Oi(this.rotation)||Oi(this.x)||Oi(this.y)||Oi(this.scaleX-1)||Oi(this.scaleY-1)||Oi(this.skewX)||Oi(this.skewY)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||[1,0,0,1,0,0],e?this.getLocalTransform(n):Li(n),t&&(e?ce(n,t,n):he(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(Li(n),this.invTransform=null)},t.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(Ri);var n=Ri[0]<0?-1:1,i=Ri[1]<0?-1:1,r=((Ri[0]-n)*e+n)/Ri[0]||0,o=((Ri[1]-i)*e+i)/Ri[1]||0;t[0]*=r,t[1]*=r,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||[1,0,0,1,0,0],ge(this.invTransform,t)},t.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),r=Math.PI/2+i-Math.atan2(t[3],t[2]);n=Math.sqrt(n)*Math.cos(r),e=Math.sqrt(e),this.skewX=r,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=n,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(ce(Ni,t.invTransform,e),e=Ni);var n=this.originX,i=this.originY;(n||i)&&(Ei[4]=n,Ei[5]=i,ce(Ni,e,Ei),Ni[4]-=n,Ni[5]-=i,e=Ni),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&Et(n,n,i),n},t.prototype.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&Et(n,n,i),n},t.prototype.getLineScale=function(){var t=this.transform;return t&&zi(t[0]-1)>1e-10&&zi(t[3]-1)>1e-10?Math.sqrt(zi(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){!function(t,e){for(var n=0;n<Fi.length;n++){var i=Fi[n];t[i]=e[i]}}(this,t)},t.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,p=t.skewY?Math.tan(-t.skewY):0;if(n||i||a||s){var f=n+a,d=i+s;e[4]=-f*r-c*d*o,e[5]=-d*o-p*f*r}else e[4]=e[5]=0;return e[0]=r,e[3]=o,e[1]=p*r,e[2]=c*o,l&&fe(e,e,l),e[4]+=n+u,e[5]+=i+h,e},t.initDefaultProps=function(){var e=t.prototype;e.scaleX=e.scaleY=e.globalScaleRatio=1,e.x=e.y=e.originX=e.originY=e.skewX=e.skewY=e.rotation=e.anchorX=e.anchorY=0}(),t}(),Fi=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];var Vi={};function Hi(t,e){var n=Vi[e=e||o];n||(n=Vi[e]=new Sn(500));var i=n.get(t);return null==i&&(i=u.measureText(t,e).width,n.put(t,i)),i}function Wi(t,e,n,i){var r=Hi(t,e),o=Yi(e),a=Ui(0,r,n),s=Xi(0,o,i);return new Ce(a,s,r,o)}function Gi(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return Wi(r[0],e,n,i);for(var o=new Ce(0,0,0,0),a=0;a<r.length;a++){var s=Wi(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function Ui(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function Xi(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function Yi(t){return Hi("国",t)}function qi(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function Zi(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=qi(i[0],n.width),u+=qi(i[1],n.height),h=null,c=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var ji="__zr_normal__",Ki=Fi.concat(["ignore"]),$i=E(Fi,(function(t,e){return t[e]=!0,t}),{ignore:!1}),Qi={},Ji=new Ce(0,0,0,0),tr=function(){function t(t){this.id=S(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var n=this.textConfig,i=n.local,r=e.innerTransformable,o=void 0,a=void 0,s=!1;r.parent=i?this:null;var l=!1;if(r.copyTransform(e),null!=n.position){var u=Ji;n.layoutRect?u.copy(n.layoutRect):u.copy(this.getBoundingRect()),i||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Qi,n,u):Zi(Qi,n,u),r.x=Qi.x,r.y=Qi.y,o=Qi.align,a=Qi.verticalAlign;var h=n.origin;if(h&&null!=n.rotation){var c=void 0,p=void 0;"center"===h?(c=.5*u.width,p=.5*u.height):(c=qi(h[0],u.width),p=qi(h[1],u.height)),l=!0,r.originX=-r.x+c+(i?0:u.x),r.originY=-r.y+p+(i?0:u.y)}}null!=n.rotation&&(r.rotation=n.rotation);var f=n.offset;f&&(r.x+=f[0],r.y+=f[1],l||(r.originX=-f[0],r.originY=-f[1]));var d=null==n.inside?"string"==typeof n.position&&n.position.indexOf("inside")>=0:n.inside,g=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),y=void 0,v=void 0,m=void 0;d&&this.canBeInsideText()?(y=n.insideFill,v=n.insideStroke,null!=y&&"auto"!==y||(y=this.getInsideTextFill()),null!=v&&"auto"!==v||(v=this.getInsideTextStroke(y),m=!0)):(y=n.outsideFill,v=n.outsideStroke,null!=y&&"auto"!==y||(y=this.getOutsideFill()),null!=v&&"auto"!==v||(v=this.getOutsideStroke(y),m=!0)),(y=y||"#000")===g.fill&&v===g.stroke&&m===g.autoStroke&&o===g.align&&a===g.verticalAlign||(s=!0,g.fill=y,g.stroke=v,g.autoStroke=m,g.align=o,g.verticalAlign=a,e.setDefaultTextStyle(g)),e.__dirty|=1,s&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?Ai:ki},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&En(e);n||(n=[255,255,255,1]);for(var i=n[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,Gn(n,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},D(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(Y(t))for(var n=B(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;if(!(i.getLoop()||r&&r!==ji)){var o=i.targetName,a=o?e[o]:e;i.saveTo(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Ki)},t.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState(ji,!1,t)},t.prototype.useState=function(t,e,n,i){var r=t===ji;if(this.hasState()||!r){var o=this.currentStates,a=this.stateTransition;if(!(A(o,t)>=0)||!e&&1!==o.length){var s;if(this.stateProxy&&!r&&(s=this.stateProxy(t)),s||(s=this.states&&this.states[t]),s||r){r||this.saveCurrentToNormalState(s);var l=!!(s&&s.hoverLayer||i);l&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,s,this._normalState,e,!n&&!this.__inHover&&a&&a.duration>0,a);var u=this._textContent,h=this._textGuide;return u&&u.useState(t,e,n,l),h&&h.useState(t,e,n,l),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!l&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2),s}M("State "+t+" not exists.")}}},t.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var l=t[s],u=void 0;this.stateProxy&&(u=this.stateProxy(l,t)),u||(u=this.states[l]),u&&i.push(u)}var h=i[o-1],c=!!(h&&h.hoverLayer||n);c&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(i),f=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&f&&f.duration>0,f);var d=this._textContent,g=this._textGuide;d&&d.useStates(t,e,c),g&&g.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2)}else this.clearStates()},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=A(this.currentStates,t);if(e>=0){var n=this.currentStates.slice();n.splice(e,1),this.useStates(n)}},t.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),r=A(i,t),o=A(i,e)>=0;r>=0?o?i.splice(r,1):i[r]=e:n&&!o&&i.push(e),this.useStates(i)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];D(n,r),r.textConfig&&D(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},t.prototype._applyStateObj=function(t,e,n,i,r,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=D({},i?this.textConfig:n.textConfig),D(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig);for(var s={},l=!1,u=0;u<Ki.length;u++){var h=Ki[u],c=r&&$i[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],f=p.targetName;p.getLoop()||p.__changeFinalValue(f?(e||n)[f]:e||n)}l&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if((!t.__zr||t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new Bi,this._attachComponent(t),this._textContent=t,this.markRedraw())},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),D(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=1;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,n){var i=t?this[t]:this;var r=new oi(i,e,n);return t&&(r.targetName=t),this.addAnimator(r,t),r},t.prototype.addAnimator=function(t,e){var n=this.__zr,i=this;t.during((function(){i.updateDuringAnimation(e)})).done((function(){var e=i.animators,n=A(e,t);n>=0&&e.splice(n,1)})),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},t.prototype.animateTo=function(t,e,n){er(this,t,e,n)},t.prototype.animateFrom=function(t,e,n){er(this,t,e,n,!0)},t.prototype._transitionState=function(t,e,n,i){for(var r=er(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;e.type="element",e.name="",e.ignore=e.silent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=1;function n(t,n,i,r){function o(t,e){Object.defineProperty(e,0,{get:function(){return t[i]},set:function(e){t[i]=e}}),Object.defineProperty(e,1,{get:function(){return t[r]},set:function(e){t[r]=e}})}Object.defineProperty(e,t,{get:function(){this[n]||o(this,this[n]=[]);return this[n]},set:function(t){this[i]=t[0],this[r]=t[1],this[n]=t,o(this,t)}})}Object.defineProperty&&(n("position","_legacyPos","x","y"),n("scale","_legacyScale","scaleX","scaleY"),n("origin","_legacyOrigin","originX","originY"))}(),t}();function er(t,e,n,i,r){var o=[];rr(t,"",t,e,n=n||{},i,o,r);var a=o.length,s=!1,l=n.done,u=n.aborted,h=function(){s=!0,--a<=0&&(s?l&&l():u&&u())},c=function(){--a<=0&&(s?l&&l():u&&u())};a||l&&l(),o.length>0&&n.during&&o[0].during((function(t,e){n.during(e)}));for(var p=0;p<o.length;p++){var f=o[p];h&&f.done(h),c&&f.aborted(c),n.force&&f.duration(n.duration),f.start(n.easing)}return o}function nr(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function ir(t,e,n){if(O(e[n]))if(O(t[n])||(t[n]=[]),Z(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),nr(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(O(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?nr(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else nr(o,r,a);o.length=r.length}else t[n]=e[n]}function rr(t,e,n,i,r,o,a,s){for(var l=B(i),u=r.duration,h=r.delay,c=r.additive,p=r.setToFinal,f=!Y(o),d=t.animators,g=[],y=0;y<l.length;y++){var v=l[y],m=i[v];if(null!=m&&null!=n[v]&&(f||o[v]))if(!Y(m)||O(m)||K(m))g.push(v);else{if(e){s||(n[v]=m,t.updateDuringAnimation(e));continue}rr(t,v,n[v],m,r,o&&o[v],a,s)}else s||(n[v]=m,t.updateDuringAnimation(e),g.push(v))}var _=g.length;if(!c&&_)for(var x=0;x<d.length;x++){if((b=d[x]).targetName===e)if(b.stopTracks(g)){var w=A(d,b);d.splice(w,1)}}if(r.force||(g=z(g,(function(t){return e=i[t],r=n[t],!(e===r||O(e)&&O(r)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(e,r));var e,r})),_=g.length),_>0||r.force&&!a.length){var b,S=void 0,M=void 0,T=void 0;if(s){M={},p&&(S={});for(x=0;x<_;x++){M[v=g[x]]=n[v],p?S[v]=i[v]:n[v]=i[v]}}else if(p){T={};for(x=0;x<_;x++){T[v=g[x]]=Jn(n[v]),ir(n,i,v)}}(b=new oi(n,!1,!1,c?z(d,(function(t){return t.targetName===e})):null)).targetName=e,r.scope&&(b.scope=r.scope),p&&S&&b.whenWithKeys(0,S,g),T&&b.whenWithKeys(0,T,g),b.whenWithKeys(null==u?500:u,s?M:i,g).delay(h||0),t.addAnimator(b,e),a.push(b)}}P(tr,Wt),P(tr,Bi);var or=function(t){function e(e){var n=t.call(this)||this;return n.isGroup=!0,n._children=[],n.attr(e),n}return n(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.children=function(){return this._children.slice()},e.prototype.childAt=function(t){return this._children[t]},e.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},e.prototype.childCount=function(){return this._children.length},e.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},e.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},e.prototype.replace=function(t,e){var n=A(this._children,t);return n>=0&&this.replaceAt(e,n),this},e.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];if(t&&t!==this&&t.parent!==this&&t!==i){n[e]=t,i.parent=null;var r=this.__zr;r&&i.removeSelfFromZr(r),this._doAdd(t)}return this},e.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},e.prototype.remove=function(t){var e=this.__zr,n=this._children,i=A(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},e.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},e.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},e.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++){this._children[n].addSelfToZr(e)}},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++){this._children[n].removeSelfFromZr(e)}},e.prototype.getBoundingRect=function(t){for(var e=new Ce(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a=n[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),l=a.getLocalTransform(i);l?(Ce.applyTransform(e,s,l),(r=r||e.clone()).union(e)):(r=r||s.clone()).union(s)}}return r||e},e}(tr);or.prototype.type="group";
/*!
    * ZRender, a high performance 2d drawing library.
    *
    * Copyright (c) 2013, Baidu Inc.
    * All rights reserved.
    *
    * LICENSE
    * https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
    */
var ar={},sr={};var lr=function(){function t(t,e,n){var i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t;var o=new Ye,a=n.renderer||"canvas";ar[a]||(a=B(ar)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect;var s=new ar[a](e,o,n,t),l=n.ssr||s.ssrOnly;this.storage=o,this.painter=s;var u,h=r.node||r.worker||l?null:new Ci(s.getViewportRoot(),s.root),c=n.useCoarsePointer;(null==c||"auto"===c?r.touchEventsSupported:!!c)&&(u=et(n.pointerSize,44)),this.handler=new Oe(o,s,h,s.root,u),this.animation=new ui({stage:{update:l?null:function(){return i._flush(!0)}}}),l||this.animation.start()}return t.prototype.add=function(t){t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh()},t.prototype.setBackgroundColor=function(t){this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return Un(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=Un(e[r].color,1);return(n/=i)<.4}return!1}(t)},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},t.prototype.refresh=function(){this._needsRefresh=!0,this.animation.start()},t.prototype.flush=function(){this._flush(!1)},t.prototype._flush=function(t){var e,n=ai();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=ai();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-n})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this.animation.start(),this._stillFrameAccum=0},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover()},t.prototype.resize=function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},t.prototype.clearAnimation=function(){this.animation.clear()},t.prototype.getWidth=function(){return this.painter.getWidth()},t.prototype.getHeight=function(){return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){return this.handler.findHover(t,e)},t.prototype.on=function(t,e,n){return this.handler.on(t,e,n),this},t.prototype.off=function(t,e){this.handler.off(t,e)},t.prototype.trigger=function(t,e){this.handler.trigger(t,e)},t.prototype.clear=function(){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof or&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()},t.prototype.dispose=function(){var t;this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,t=this.id,delete sr[t]},t}();function ur(t,e){var n=new lr(S(),t,e);return sr[n.id]=n,n}function hr(t,e){ar[t]=e}var cr=Object.freeze({__proto__:null,init:ur,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in sr)sr.hasOwnProperty(t)&&sr[t].dispose();sr={}},getInstance:function(t){return sr[t]},registerPainter:hr,version:"5.4.4"}),pr=1e-4;function fr(t,e,n,i){var r=e[0],o=e[1],a=n[0],s=n[1],l=o-r,u=s-a;if(0===l)return 0===u?a:(a+s)/2;if(i)if(l>0){if(t<=r)return a;if(t>=o)return s}else{if(t>=r)return a;if(t<=o)return s}else{if(t===r)return a;if(t===o)return s}return(t-r)/l*u+a}function dr(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return G(t)?(n=t,n.replace(/^\s+|\s+$/g,"")).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t;var n}function gr(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function yr(t){if(t=+t,isNaN(t))return 0;if(t>1e-14)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return vr(t)}function vr(t){var e=t.toString().toLowerCase(),n=e.indexOf("e"),i=n>0?+e.slice(n+1):0,r=n>0?n:e.length,o=e.indexOf("."),a=o<0?0:r-1-o;return Math.max(0,a-i)}function mr(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),o=Math.round(n(Math.abs(e[1]-e[0]))/i),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function _r(t,e){var n=E(t,(function(t,e){return t+(isNaN(e)?0:e)}),0);if(0===n)return[];for(var i=Math.pow(10,e),r=N(t,(function(t){return(isNaN(t)?0:t)/n*i*100})),o=100*i,a=N(r,(function(t){return Math.floor(t)})),s=E(a,(function(t,e){return t+e}),0),l=N(r,(function(t,e){return t-a[e]}));s<o;){for(var u=Number.NEGATIVE_INFINITY,h=null,c=0,p=l.length;c<p;++c)l[c]>u&&(u=l[c],h=c);++a[h],l[h]=0,++s}return N(a,(function(t){return t/i}))}function xr(t,e){var n=Math.max(yr(t),yr(e)),i=t+e;return n>20?i:gr(i,n)}function wr(t){var e=2*Math.PI;return(t%e+e)%e}function br(t){return t>-1e-4&&t<pr}var Sr=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Mr(t){if(t instanceof Date)return t;if(G(t)){var e=Sr.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0)}return null==t?new Date(NaN):new Date(Math.round(t))}function Tr(t){return Math.pow(10,Cr(t))}function Cr(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function Dr(t,e){var n=Cr(t),i=Math.pow(10,n),r=t/i;return t=(e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10)*i,n>=-20?+t.toFixed(n<0?-n:0):t}function Ir(t){var e=parseFloat(t);return e==t&&(0!==e||!G(t)||t.indexOf("x")<=0)?e:NaN}function kr(t){return!isNaN(Ir(t))}function Ar(t,e){return 0===e?t:Ar(e,t%e)}function Lr(t,e){return null==t?e:null==e?t:t*e/Ar(t,e)}"undefined"!=typeof console&&console.warn&&console.log;function Pr(t){0}function Or(t){throw new Error(t)}function Rr(t,e,n){return(e-t)*n+t}var Nr="series\0";function Er(t){return t instanceof Array?t:null==t?[]:[t]}function zr(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var Br=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function Fr(t){return!Y(t)||H(t)||t instanceof Date?t:t.value}function Vr(t){return Y(t)&&!(t instanceof Array)}function Hr(t,e,n){var i="normalMerge"===n,r="replaceMerge"===n,o="replaceAll"===n;t=t||[],e=(e||[]).slice();var a=ft();R(e,(function(t,n){Y(t)||(e[n]=null)}));var s,l,u=function(t,e,n){var i=[];if("replaceAll"===n)return i;for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||Yr(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,a,n);return(i||r)&&function(t,e,n,i){R(i,(function(r,o){if(r&&null!=r.id){var a=Gr(r.id),s=n.get(a);if(null!=s){var l=t[s];ot(!l.newOption,'Duplicated option on id "'+a+'".'),l.newOption=r,l.existing=e[s],i[o]=null}}}))}(u,t,a,e),i&&function(t,e){R(e,(function(n,i){if(n&&null!=n.name)for(var r=0;r<t.length;r++){var o=t[r].existing;if(!t[r].newOption&&o&&(null==o.id||null==n.id)&&!Yr(n)&&!Yr(o)&&Wr("name",o,n))return t[r].newOption=n,void(e[i]=null)}}))}(u,e),i||r?function(t,e,n){R(e,(function(e){if(e){for(var i,r=0;(i=t[r])&&(i.newOption||Yr(i.existing)||i.existing&&null!=e.id&&!Wr("id",e,i.existing));)r++;i?(i.newOption=e,i.brandNew=n):t.push({newOption:e,brandNew:n,existing:null,keyInfo:null}),r++}}))}(u,e,r):o&&function(t,e){R(e,(function(e){t.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})}))}(u,e),s=u,l=ft(),R(s,(function(t){var e=t.existing;e&&l.set(e.id,t)})),R(s,(function(t){var e=t.newOption;ot(!e||null==e.id||!l.get(e.id)||l.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&l.set(e.id,t),!t.keyInfo&&(t.keyInfo={})})),R(s,(function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(Y(i)){if(r.name=null!=i.name?Gr(i.name):n?n.name:Nr+e,n)r.id=Gr(n.id);else if(null!=i.id)r.id=Gr(i.id);else{var o=0;do{r.id="\0"+r.name+"\0"+o++}while(l.get(r.id))}l.set(r.id,t)}})),u}function Wr(t,e,n){var i=Ur(e[t],null),r=Ur(n[t],null);return null!=i&&null!=r&&i===r}function Gr(t){return Ur(t,"")}function Ur(t,e){return null==t?e:G(t)?t:X(t)||U(t)?t+"":e}function Xr(t){var e=t.name;return!(!e||!e.indexOf(Nr))}function Yr(t){return t&&null!=t.id&&0===Gr(t.id).indexOf("\0_ec_\0")}function qr(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?H(e.dataIndex)?N(e.dataIndex,(function(e){return t.indexOfRawIndex(e)})):t.indexOfRawIndex(e.dataIndex):null!=e.name?H(e.name)?N(e.name,(function(e){return t.indexOfName(e)})):t.indexOfName(e.name):void 0}function Zr(){var t="__ec_inner_"+jr++;return function(e){return e[t]||(e[t]={})}}var jr=Math.round(9*Math.random());function Kr(t,e,n){var i=$r(e,n),r=i.mainTypeSpecified,o=i.queryOptionMap,a=i.others,s=n?n.defaultMainType:null;return!r&&s&&o.set(s,{}),o.each((function(e,i){var r=Jr(t,i,e,{useDefault:s===i,enableAll:!n||null==n.enableAll||n.enableAll,enableNone:!n||null==n.enableNone||n.enableNone});a[i+"Models"]=r.models,a[i+"Model"]=r.models[0]})),a}function $r(t,e){var n;if(G(t)){var i={};i[t+"Index"]=0,n=i}else n=t;var r=ft(),o={},a=!1;return R(n,(function(t,n){if("dataIndex"!==n&&"dataIndexInside"!==n){var i=n.match(/^(\w+)(Index|Id|Name)$/)||[],s=i[1],l=(i[2]||"").toLowerCase();if(s&&l&&!(e&&e.includeMainTypes&&A(e.includeMainTypes,s)<0))a=a||!!s,(r.get(s)||r.set(s,{}))[l]=t}else o[n]=t})),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var Qr={useDefault:!0,enableAll:!1,enableNone:!1};function Jr(t,e,n,i){i=i||Qr;var r=n.index,o=n.id,a=n.name,s={models:null,specified:null!=r||null!=o||null!=a};if(!s.specified){var l=void 0;return s.models=i.useDefault&&(l=t.getComponent(e))?[l]:[],s}return"none"===r||!1===r?(ot(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):("all"===r&&(ot(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=a=null),s.models=t.queryComponents({mainType:e,index:r,id:o,name:a}),s)}function to(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}var eo="___EC__COMPONENT__CONTAINER___",no="___EC__EXTENDED_CLASS___";function io(t){var e={main:"",sub:""};if(t){var n=t.split(".");e.main=n[0]||"",e.sub=n[1]||""}return e}function ro(t,e){t.$constructor=t,t.extend=function(t){var e,i,r=this;return W(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?e=function(t){function e(){return t.apply(this,arguments)||this}return n(e,t),e}(r):(e=function(){(t.$constructor||r).apply(this,arguments)},L(e,this)),D(e.prototype,t),e[no]=!0,e.extend=this.extend,e.superCall=so,e.superApply=lo,e.superClass=r,e}}function oo(t,e){t.extend=e.extend}var ao=Math.round(10*Math.random());function so(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function lo(t,e,n){return this.superClass.prototype[e].apply(t,n)}function uo(t){var e={};t.registerClass=function(t){var n,i=t.type||t.prototype.type;if(i){ot(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(n=i),'componentType "'+n+'" illegal'),t.prototype.type=i;var r=io(i);if(r.sub){if(r.sub!==eo){var o=function(t){var n=e[t.main];n&&n[eo]||((n=e[t.main]={})[eo]=!0);return n}(r);o[r.sub]=t}}else e[r.main]=t}return t},t.getClass=function(t,n,i){var r=e[t];if(r&&r[eo]&&(r=n?r[n]:null),i&&!r)throw new Error(n?"Component "+t+"."+(n||"")+" is used but not imported.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){var n=io(t),i=[],r=e[n.main];return r&&r[eo]?R(r,(function(t,e){e!==eo&&i.push(t)})):i.push(r),i},t.hasClass=function(t){var n=io(t);return!!e[n.main]},t.getAllClassMainTypes=function(){var t=[];return R(e,(function(e,n){t.push(n)})),t},t.hasSubTypes=function(t){var n=io(t),i=e[n.main];return i&&i[eo]}}function ho(t,e){for(var n=0;n<t.length;n++)t[n][1]||(t[n][1]=t[n][0]);return e=e||!1,function(n,i,r){for(var o={},a=0;a<t.length;a++){var s=t[a][1];if(!(i&&A(i,s)>=0||r&&A(r,s)<0)){var l=n.getShallow(s,e);null!=l&&(o[t[a][0]]=l)}}return o}}var co=ho([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),po=function(){function t(){}return t.prototype.getAreaStyle=function(t,e){return co(this,t,e)},t}(),fo=new Sn(50);function go(t){if("string"==typeof t){var e=fo.get(t);return e&&e.image}return t}function yo(t,e,n,i,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var o=fo.get(t),a={hostEl:n,cb:i,cbPayload:r};return o?!mo(e=o.image)&&o.pending.push(a):((e=u.loadImage(t,vo,vo)).__zrImageSrc=t,fo.put(t,e.__cachedImgObj={image:e,pending:[a]})),e}return t}return e}function vo(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function mo(t){return t&&t.width&&t.height}var _o=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function xo(t,e,n,i,r){if(!e)return"";var o=(t+"").split("\n");r=wo(e,n,i,r);for(var a=0,s=o.length;a<s;a++)o[a]=bo(o[a],r);return o.join("\n")}function wo(t,e,n,i){var r=D({},i=i||{});r.font=e,n=et(n,"..."),r.maxIterations=et(i.maxIterations,2);var o=r.minChar=et(i.minChar,0);r.cnCharWidth=Hi("国",e);var a=r.ascCharWidth=Hi("a",e);r.placeholder=et(i.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;l<o&&s>=a;l++)s-=a;var u=Hi(n,e);return u>s&&(n="",u=0),s=t-u,r.ellipsis=n,r.ellipsisWidth=u,r.contentWidth=s,r.containerWidth=t,r}function bo(t,e){var n=e.containerWidth,i=e.font,r=e.contentWidth;if(!n)return"";var o=Hi(t,i);if(o<=n)return t;for(var a=0;;a++){if(o<=r||a>=e.maxIterations){t+=e.ellipsis;break}var s=0===a?So(t,r,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*r/o):0;o=Hi(t=t.substr(0,s),i)}return""===t&&(t=e.placeholder),t}function So(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}var Mo=function(){},To=function(t){this.tokens=[],t&&(this.tokens=t)},Co=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[]};function Do(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;if(i){var p=l.padding,f=p?p[1]+p[3]:0;if(null!=l.width&&"auto"!==l.width){var d=qi(l.width,i.width)+f;u.length>0&&d+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=d}else{var g=Ao(e,h,i.width,i.breakAll,i.accumWidth);i.accumWidth=g.accumWidth+f,a=g.linesWidths,o=g.lines}}else o=e.split("\n");for(var y=0;y<o.length;y++){var v=o[y],m=new Mo;if(m.styleName=r,m.text=v,m.isLineHolder=!v&&!s,"number"==typeof l.width?m.width=l.width:m.width=a?a[y]:Hi(v,h),y||c)u.push(new To([m]));else{var _=(u[u.length-1]||(u[0]=new To)).tokens,x=_.length;1===x&&_[0].isLineHolder?_[0]=m:(v||!x||s)&&_.push(m)}}}var Io=E(",&?/;] ".split(""),(function(t,e){return t[e]=!0,t}),{});function ko(t){return!function(t){var e=t.charCodeAt(0);return e>=32&&e<=591||e>=880&&e<=4351||e>=4608&&e<=5119||e>=7680&&e<=8303}(t)||!!Io[t]}function Ao(t,e,n,i,r){for(var o=[],a=[],s="",l="",u=0,h=0,c=0;c<t.length;c++){var p=t.charAt(c);if("\n"!==p){var f=Hi(p,e),d=!i&&!ko(p);(o.length?h+f>n:r+h+f>n)?h?(s||l)&&(d?(s||(s=l,l="",h=u=0),o.push(s),a.push(h-u),l+=p,s="",h=u+=f):(l&&(s+=l,l="",u=0),o.push(s),a.push(h),s=p,h=f)):d?(o.push(l),a.push(u),l=p,u=f):(o.push(p),a.push(f)):(h+=f,d?(l+=p,u+=f):(l&&(s+=l,l="",u=0),s+=p))}else l&&(s+=l,h+=u),o.push(s),a.push(h),s="",l="",u=0,h=0}return o.length||s||(s=t,l="",u=0),l&&(s+=l),s&&(o.push(s),a.push(h)),1===o.length&&(h+=r),{accumWidth:h,lines:o,linesWidths:a}}var Lo="__zr_style_"+Math.round(10*Math.random()),Po={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},Oo={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};Po[Lo]=!0;var Ro=["z","z2","invisible"],No=["invisible"],Eo=function(t){function e(e){return t.call(this,e)||this}var i;return n(e,t),e.prototype._init=function(e){for(var n=B(e),i=0;i<n.length;i++){var r=n[i];"style"===r?this.useStyle(e[r]):t.prototype.attrKV.call(this,r,e[r])}this.style||this.useStyle({})},e.prototype.beforeBrush=function(){},e.prototype.afterBrush=function(){},e.prototype.innerBeforeBrush=function(){},e.prototype.innerAfterBrush=function(){},e.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){zo.copy(t.getBoundingRect()),t.transform&&zo.applyTransform(t.transform);return Bo.width=e,Bo.height=n,!zo.intersect(Bo)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},e.prototype.contain=function(t,e){return this.rectContain(t,e)},e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.rectContain=function(t,e){var n=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(n[0],n[1])},e.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,n=this.getBoundingRect(),i=this.style,r=i.shadowBlur||0,o=i.shadowOffsetX||0,a=i.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new Ce(0,0,0,0)),e?Ce.applyTransform(t,n,e):t.copy(n),(r||o||a)&&(t.width+=2*r+Math.abs(o),t.height+=2*r+Math.abs(a),t.x=Math.min(t.x,t.x+o-r),t.y=Math.min(t.y,t.y+a-r));var s=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-s),t.y=Math.floor(t.y-s),t.width=Math.ceil(t.width+1+2*s),t.height=Math.ceil(t.height+1+2*s))}return t},e.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new Ce(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},e.prototype.getPrevPaintRect=function(){return this._prevPaintRect},e.prototype.animateStyle=function(t){return this.animate("style",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},e.prototype.attrKV=function(e,n){"style"!==e?t.prototype.attrKV.call(this,e,n):this.style?this.setStyle(n):this.useStyle(n)},e.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:D(this.style,t),this.dirtyStyle(),this},e.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},e.prototype.dirty=function(){this.dirtyStyle()},e.prototype.styleChanged=function(){return!!(2&this.__dirty)},e.prototype.styleUpdated=function(){this.__dirty&=-3},e.prototype.createStyle=function(t){return gt(Po,t)},e.prototype.useStyle=function(t){t[Lo]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},e.prototype.isStyleObject=function(t){return t[Lo]},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.style&&!n.style&&(n.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,n,Ro)},e.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,l=!(n&&r);if(n&&n.style?o?r?s=n.style:(s=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(s,n.style)):(s=this._mergeStyle(this.createStyle(),r?this.style:i.style),this._mergeStyle(s,n.style)):l&&(s=i.style),s)if(o){var u=this.style;if(this.style=this.createStyle(l?{}:u),l)for(var h=B(u),c=0;c<h.length;c++){(f=h[c])in s&&(s[f]=s[f],this.style[f]=u[f])}var p=B(s);for(c=0;c<p.length;c++){var f=p[c];this.style[f]=this.style[f]}this._transitionState(e,{style:s},a,this.getAnimationStyleProps())}else this.useStyle(s);var d=this.__inHover?No:Ro;for(c=0;c<d.length;c++){f=d[c];n&&null!=n[f]?this[f]=n[f]:l&&null!=i[f]&&(this[f]=i[f])}},e.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},e.prototype._mergeStyle=function(t,e){return D(t,e),t},e.prototype.getAnimationStyleProps=function(){return Oo},e.initDefaultProps=((i=e.prototype).type="displayable",i.invisible=!1,i.z=0,i.z2=0,i.zlevel=0,i.culling=!1,i.cursor="pointer",i.rectHover=!1,i.incremental=!1,i._rect=null,i.dirtyRectTolerance=0,void(i.__dirty=3)),e}(tr),zo=new Ce(0,0,0,0),Bo=new Ce(0,0,0,0);var Fo=Math.min,Vo=Math.max,Ho=Math.sin,Wo=Math.cos,Go=2*Math.PI,Uo=wt(),Xo=wt(),Yo=wt();function qo(t,e,n,i,r,o){r[0]=Fo(t,n),r[1]=Fo(e,i),o[0]=Vo(t,n),o[1]=Vo(e,i)}var Zo=[],jo=[];function Ko(t,e,n,i,r,o,a,s,l,u){var h=hn,c=sn,p=h(t,n,r,a,Zo);l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0;for(var f=0;f<p;f++){var d=c(t,n,r,a,Zo[f]);l[0]=Fo(d,l[0]),u[0]=Vo(d,u[0])}p=h(e,i,o,s,jo);for(f=0;f<p;f++){var g=c(e,i,o,s,jo[f]);l[1]=Fo(g,l[1]),u[1]=Vo(g,u[1])}l[0]=Fo(t,l[0]),u[0]=Vo(t,u[0]),l[0]=Fo(a,l[0]),u[0]=Vo(a,u[0]),l[1]=Fo(e,l[1]),u[1]=Vo(e,u[1]),l[1]=Fo(s,l[1]),u[1]=Vo(s,u[1])}function $o(t,e,n,i,r,o,a,s){var l=gn,u=fn,h=Vo(Fo(l(t,n,r),1),0),c=Vo(Fo(l(e,i,o),1),0),p=u(t,n,r,h),f=u(e,i,o,c);a[0]=Fo(t,r,p),a[1]=Fo(e,o,f),s[0]=Vo(t,r,p),s[1]=Vo(e,o,f)}function Qo(t,e,n,i,r,o,a,s,l){var u=zt,h=Bt,c=Math.abs(r-o);if(c%Go<1e-4&&c>1e-4)return s[0]=t-n,s[1]=e-i,l[0]=t+n,void(l[1]=e+i);if(Uo[0]=Wo(r)*n+t,Uo[1]=Ho(r)*i+e,Xo[0]=Wo(o)*n+t,Xo[1]=Ho(o)*i+e,u(s,Uo,Xo),h(l,Uo,Xo),(r%=Go)<0&&(r+=Go),(o%=Go)<0&&(o+=Go),r>o&&!a?o+=Go:r<o&&a&&(r+=Go),a){var p=o;o=r,r=p}for(var f=0;f<o;f+=Math.PI/2)f>r&&(Yo[0]=Wo(f)*n+t,Yo[1]=Ho(f)*i+e,u(s,Yo,s),h(l,Yo,l))}var Jo={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},ta=[],ea=[],na=[],ia=[],ra=[],oa=[],aa=Math.min,sa=Math.max,la=Math.cos,ua=Math.sin,ha=Math.abs,ca=Math.PI,pa=2*ca,fa="undefined"!=typeof Float32Array,da=[];function ga(t){return Math.round(t/ca*1e8)/1e8%2*ca}var ya=function(){function t(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return t.prototype.increaseVersion=function(){this._version++},t.prototype.getVersion=function(){return this._version},t.prototype.setScale=function(t,e,n){(n=n||0)>0&&(this._ux=ha(n/Ii/t)||0,this._uy=ha(n/Ii/e)||0)},t.prototype.setDPR=function(t){this.dpr=t},t.prototype.setContext=function(t){this._ctx=t},t.prototype.getContext=function(){return this._ctx},t.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},t.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},t.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(Jo.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},t.prototype.lineTo=function(t,e){var n=ha(t-this._xi),i=ha(e-this._yi),r=n>this._ux||i>this._uy;if(this.addData(Jo.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=n*n+i*i;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},t.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(Jo.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},t.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(Jo.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},t.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),da[0]=i,da[1]=r,function(t,e){var n=ga(t[0]);n<0&&(n+=pa);var i=n-t[0],r=t[1];r+=i,!e&&r-n>=pa?r=n+pa:e&&n-r>=pa?r=n-pa:!e&&n>r?r=n+(pa-ga(n-r)):e&&n<r&&(r=n-(pa-ga(r-n))),t[0]=n,t[1]=r}(da,o),i=da[0];var a=(r=da[1])-i;return this.addData(Jo.A,t,e,n,n,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=la(r)*n+t,this._yi=ua(r)*n+e,this},t.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},t.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(Jo.R,t,e,n,i),this},t.prototype.closePath=function(){this._drawPendingPt(),this.addData(Jo.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},t.prototype.fill=function(t){t&&t.fill(),this.toStatic()},t.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},t.prototype.len=function(){return this._len},t.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!fa||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},t.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();fa&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},t.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},t.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},t.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},t.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,fa&&this._len>11&&(this.data=new Float32Array(t)))}},t.prototype.getBoundingRect=function(){na[0]=na[1]=ra[0]=ra[1]=Number.MAX_VALUE,ia[0]=ia[1]=oa[0]=oa[1]=-Number.MAX_VALUE;var t,e=this.data,n=0,i=0,r=0,o=0;for(t=0;t<this._len;){var a=e[t++],s=1===t;switch(s&&(r=n=e[t],o=i=e[t+1]),a){case Jo.M:n=r=e[t++],i=o=e[t++],ra[0]=r,ra[1]=o,oa[0]=r,oa[1]=o;break;case Jo.L:qo(n,i,e[t],e[t+1],ra,oa),n=e[t++],i=e[t++];break;case Jo.C:Ko(n,i,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],ra,oa),n=e[t++],i=e[t++];break;case Jo.Q:$o(n,i,e[t++],e[t++],e[t],e[t+1],ra,oa),n=e[t++],i=e[t++];break;case Jo.A:var l=e[t++],u=e[t++],h=e[t++],c=e[t++],p=e[t++],f=e[t++]+p;t+=1;var d=!e[t++];s&&(r=la(p)*h+l,o=ua(p)*c+u),Qo(l,u,h,c,p,f,d,ra,oa),n=la(f)*h+l,i=ua(f)*c+u;break;case Jo.R:qo(r=n=e[t++],o=i=e[t++],r+e[t++],o+e[t++],ra,oa);break;case Jo.Z:n=r,i=o}zt(na,na,ra),Bt(ia,ia,oa)}return 0===t&&(na[0]=na[1]=ia[0]=ia[1]=0),new Ce(na[0],na[1],ia[0]-na[0],ia[1]-na[1])},t.prototype._calculateLength=function(){var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,u=0,h=0,c=0;c<e;){var p=t[c++],f=1===c;f&&(a=r=t[c],s=o=t[c+1]);var d=-1;switch(p){case Jo.M:r=a=t[c++],o=s=t[c++];break;case Jo.L:var g=t[c++],y=(_=t[c++])-o;(ha(k=g-r)>n||ha(y)>i||c===e-1)&&(d=Math.sqrt(k*k+y*y),r=g,o=_);break;case Jo.C:var v=t[c++],m=t[c++],_=(g=t[c++],t[c++]),x=t[c++],w=t[c++];d=pn(r,o,v,m,g,_,x,w,10),r=x,o=w;break;case Jo.Q:d=vn(r,o,v=t[c++],m=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case Jo.A:var b=t[c++],S=t[c++],M=t[c++],T=t[c++],C=t[c++],D=t[c++],I=D+C;c+=1;t[c++];f&&(a=la(C)*M+b,s=ua(C)*T+S),d=sa(M,T)*aa(pa,Math.abs(D)),r=la(I)*M+b,o=ua(I)*T+S;break;case Jo.R:a=r=t[c++],s=o=t[c++],d=2*t[c++]+2*t[c++];break;case Jo.Z:var k=a-r;y=s-o;d=Math.sqrt(k*k+y*y),r=a,o=s}d>=0&&(l[h++]=d,u+=d)}return this._pathLen=u,u},t.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p=this.data,f=this._ux,d=this._uy,g=this._len,y=e<1,v=0,m=0,_=0;if(!y||(this._pathSegLen||this._calculateLength(),l=this._pathSegLen,u=e*this._pathLen))t:for(var x=0;x<g;){var w=p[x++],b=1===x;switch(b&&(n=r=p[x],i=o=p[x+1]),w!==Jo.L&&_>0&&(t.lineTo(h,c),_=0),w){case Jo.M:n=r=p[x++],i=o=p[x++],t.moveTo(r,o);break;case Jo.L:a=p[x++],s=p[x++];var S=ha(a-r),M=ha(s-o);if(S>f||M>d){if(y){if(v+(Z=l[m++])>u){var T=(u-v)/Z;t.lineTo(r*(1-T)+a*T,o*(1-T)+s*T);break t}v+=Z}t.lineTo(a,s),r=a,o=s,_=0}else{var C=S*S+M*M;C>_&&(h=a,c=s,_=C)}break;case Jo.C:var D=p[x++],I=p[x++],k=p[x++],A=p[x++],L=p[x++],P=p[x++];if(y){if(v+(Z=l[m++])>u){cn(r,D,k,L,T=(u-v)/Z,ta),cn(o,I,A,P,T,ea),t.bezierCurveTo(ta[1],ea[1],ta[2],ea[2],ta[3],ea[3]);break t}v+=Z}t.bezierCurveTo(D,I,k,A,L,P),r=L,o=P;break;case Jo.Q:D=p[x++],I=p[x++],k=p[x++],A=p[x++];if(y){if(v+(Z=l[m++])>u){yn(r,D,k,T=(u-v)/Z,ta),yn(o,I,A,T,ea),t.quadraticCurveTo(ta[1],ea[1],ta[2],ea[2]);break t}v+=Z}t.quadraticCurveTo(D,I,k,A),r=k,o=A;break;case Jo.A:var O=p[x++],R=p[x++],N=p[x++],E=p[x++],z=p[x++],B=p[x++],F=p[x++],V=!p[x++],H=N>E?N:E,W=ha(N-E)>.001,G=z+B,U=!1;if(y)v+(Z=l[m++])>u&&(G=z+B*(u-v)/Z,U=!0),v+=Z;if(W&&t.ellipse?t.ellipse(O,R,N,E,F,z,G,V):t.arc(O,R,H,z,G,V),U)break t;b&&(n=la(z)*N+O,i=ua(z)*E+R),r=la(G)*N+O,o=ua(G)*E+R;break;case Jo.R:n=r=p[x],i=o=p[x+1],a=p[x++],s=p[x++];var X=p[x++],Y=p[x++];if(y){if(v+(Z=l[m++])>u){var q=u-v;t.moveTo(a,s),t.lineTo(a+aa(q,X),s),(q-=X)>0&&t.lineTo(a+X,s+aa(q,Y)),(q-=Y)>0&&t.lineTo(a+sa(X-q,0),s+Y),(q-=X)>0&&t.lineTo(a,s+sa(Y-q,0));break t}v+=Z}t.rect(a,s,X,Y);break;case Jo.Z:if(y){var Z;if(v+(Z=l[m++])>u){T=(u-v)/Z;t.lineTo(r*(1-T)+n*T,o*(1-T)+i*T);break t}v+=Z}t.closePath(),r=n,o=i}}},t.prototype.clone=function(){var e=new t,n=this.data;return e.data=n.slice?n.slice():Array.prototype.slice.call(n),e._len=this._len,e},t.CMD=Jo,t.initDefaultProps=function(){var e=t.prototype;e._saveData=!0,e._ux=0,e._uy=0,e._pendingPtDist=0,e._version=0}(),t}();function va(t,e,n,i,r,o,a){if(0===r)return!1;var s=r,l=0;if(a>e+s&&a>i+s||a<e-s&&a<i-s||o>t+s&&o>n+s||o<t-s&&o<n-s)return!1;if(t===n)return Math.abs(o-t)<=s/2;var u=(l=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n);return u*u/(l*l+1)<=s/2*s/2}function ma(t,e,n,i,r,o,a,s,l,u,h){if(0===l)return!1;var c=l;if(h>e+c&&h>i+c&&h>o+c&&h>s+c||h<e-c&&h<i-c&&h<o-c&&h<s-c||u>t+c&&u>n+c&&u>r+c&&u>a+c||u<t-c&&u<n-c&&u<r-c&&u<a-c)return!1;var p=function(t,e,n,i,r,o,a,s,l,u,h){var c,p,f,d,g,y=.005,v=1/0;en[0]=l,en[1]=u;for(var m=0;m<1;m+=.05)nn[0]=sn(t,n,r,a,m),nn[1]=sn(e,i,o,s,m),(d=Rt(en,nn))<v&&(c=m,v=d);v=1/0;for(var _=0;_<32&&!(y<Qe);_++)p=c-y,f=c+y,nn[0]=sn(t,n,r,a,p),nn[1]=sn(e,i,o,s,p),d=Rt(nn,en),p>=0&&d<v?(c=p,v=d):(rn[0]=sn(t,n,r,a,f),rn[1]=sn(e,i,o,s,f),g=Rt(rn,en),f<=1&&g<v?(c=f,v=g):y*=.5);return h&&(h[0]=sn(t,n,r,a,c),h[1]=sn(e,i,o,s,c)),Ke(v)}(t,e,n,i,r,o,a,s,u,h,null);return p<=c/2}function _a(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;if(l>e+u&&l>i+u&&l>o+u||l<e-u&&l<i-u&&l<o-u||s>t+u&&s>n+u&&s>r+u||s<t-u&&s<n-u&&s<r-u)return!1;var h=function(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;en[0]=a,en[1]=s;for(var p=0;p<1;p+=.05)nn[0]=fn(t,n,r,p),nn[1]=fn(e,i,o,p),(y=Rt(en,nn))<c&&(u=p,c=y);c=1/0;for(var f=0;f<32&&!(h<Qe);f++){var d=u-h,g=u+h;nn[0]=fn(t,n,r,d),nn[1]=fn(e,i,o,d);var y=Rt(nn,en);if(d>=0&&y<c)u=d,c=y;else{rn[0]=fn(t,n,r,g),rn[1]=fn(e,i,o,g);var v=Rt(rn,en);g<=1&&v<c?(u=g,c=v):h*=.5}}return l&&(l[0]=fn(t,n,r,u),l[1]=fn(e,i,o,u)),Ke(c)}(t,e,n,i,r,o,s,l,null);return h<=u/2}var xa=2*Math.PI;function wa(t){return(t%=xa)<0&&(t+=xa),t}var ba=2*Math.PI;function Sa(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;s-=t,l-=e;var h=Math.sqrt(s*s+l*l);if(h-u>n||h+u<n)return!1;if(Math.abs(i-r)%ba<1e-4)return!0;if(o){var c=i;i=wa(r),r=wa(c)}else i=wa(i),r=wa(r);i>r&&(r+=ba);var p=Math.atan2(l,s);return p<0&&(p+=ba),p>=i&&p<=r||p+ba>=i&&p+ba<=r}function Ma(t,e,n,i,r,o){if(o>e&&o>i||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!==a&&0!==a||(s=i<e?.5:-.5);var l=a*(n-t)+t;return l===r?1/0:l>r?s:0}var Ta=ya.CMD,Ca=2*Math.PI;var Da=[-1,-1,-1],Ia=[-1,-1];function ka(t,e,n,i,r,o,a,s,l,u){if(u>e&&u>i&&u>o&&u>s||u<e&&u<i&&u<o&&u<s)return 0;var h,c=un(e,i,o,s,u,Da);if(0===c)return 0;for(var p=0,f=-1,d=void 0,g=void 0,y=0;y<c;y++){var v=Da[y],m=0===v||1===v?.5:1;sn(t,n,r,a,v)<l||(f<0&&(f=hn(e,i,o,s,Ia),Ia[1]<Ia[0]&&f>1&&(h=void 0,h=Ia[0],Ia[0]=Ia[1],Ia[1]=h),d=sn(e,i,o,s,Ia[0]),f>1&&(g=sn(e,i,o,s,Ia[1]))),2===f?v<Ia[0]?p+=d<e?m:-m:v<Ia[1]?p+=g<d?m:-m:p+=s<g?m:-m:v<Ia[0]?p+=d<e?m:-m:p+=s<d?m:-m)}return p}function Aa(t,e,n,i,r,o,a,s){if(s>e&&s>i&&s>o||s<e&&s<i&&s<o)return 0;var l=function(t,e,n,i,r){var o=t-2*e+n,a=2*(e-t),s=t-i,l=0;if(on(o))an(a)&&(h=-s/a)>=0&&h<=1&&(r[l++]=h);else{var u=a*a-4*o*s;if(on(u))(h=-a/(2*o))>=0&&h<=1&&(r[l++]=h);else if(u>0){var h,c=Ke(u),p=(-a-c)/(2*o);(h=(-a+c)/(2*o))>=0&&h<=1&&(r[l++]=h),p>=0&&p<=1&&(r[l++]=p)}}return l}(e,i,o,s,Da);if(0===l)return 0;var u=gn(e,i,o);if(u>=0&&u<=1){for(var h=0,c=fn(e,i,o,u),p=0;p<l;p++){var f=0===Da[p]||1===Da[p]?.5:1;fn(t,n,r,Da[p])<a||(Da[p]<u?h+=c<e?f:-f:h+=o<c?f:-f)}return h}f=0===Da[0]||1===Da[0]?.5:1;return fn(t,n,r,Da[0])<a?0:o<e?f:-f}function La(t,e,n,i,r,o,a,s){if((s-=e)>n||s<-n)return 0;var l=Math.sqrt(n*n-s*s);Da[0]=-l,Da[1]=l;var u=Math.abs(i-r);if(u<1e-4)return 0;if(u>=Ca-1e-4){i=0,r=Ca;var h=o?1:-1;return a>=Da[0]+t&&a<=Da[1]+t?h:0}if(i>r){var c=i;i=r,r=c}i<0&&(i+=Ca,r+=Ca);for(var p=0,f=0;f<2;f++){var d=Da[f];if(d+t>a){var g=Math.atan2(s,d);h=o?1:-1;g<0&&(g=Ca+g),(g>=i&&g<=r||g+Ca>=i&&g+Ca<=r)&&(g>Math.PI/2&&g<1.5*Math.PI&&(h=-h),p+=h)}}return p}function Pa(t,e,n,i,r){for(var o,a,s,l,u=t.data,h=t.len(),c=0,p=0,f=0,d=0,g=0,y=0;y<h;){var v=u[y++],m=1===y;switch(v===Ta.M&&y>1&&(n||(c+=Ma(p,f,d,g,i,r))),m&&(d=p=u[y],g=f=u[y+1]),v){case Ta.M:p=d=u[y++],f=g=u[y++];break;case Ta.L:if(n){if(va(p,f,u[y],u[y+1],e,i,r))return!0}else c+=Ma(p,f,u[y],u[y+1],i,r)||0;p=u[y++],f=u[y++];break;case Ta.C:if(n){if(ma(p,f,u[y++],u[y++],u[y++],u[y++],u[y],u[y+1],e,i,r))return!0}else c+=ka(p,f,u[y++],u[y++],u[y++],u[y++],u[y],u[y+1],i,r)||0;p=u[y++],f=u[y++];break;case Ta.Q:if(n){if(_a(p,f,u[y++],u[y++],u[y],u[y+1],e,i,r))return!0}else c+=Aa(p,f,u[y++],u[y++],u[y],u[y+1],i,r)||0;p=u[y++],f=u[y++];break;case Ta.A:var _=u[y++],x=u[y++],w=u[y++],b=u[y++],S=u[y++],M=u[y++];y+=1;var T=!!(1-u[y++]);o=Math.cos(S)*w+_,a=Math.sin(S)*b+x,m?(d=o,g=a):c+=Ma(p,f,o,a,i,r);var C=(i-_)*b/w+_;if(n){if(Sa(_,x,b,S,S+M,T,e,C,r))return!0}else c+=La(_,x,b,S,S+M,T,C,r);p=Math.cos(S+M)*w+_,f=Math.sin(S+M)*b+x;break;case Ta.R:if(d=p=u[y++],g=f=u[y++],o=d+u[y++],a=g+u[y++],n){if(va(d,g,o,g,e,i,r)||va(o,g,o,a,e,i,r)||va(o,a,d,a,e,i,r)||va(d,a,d,g,e,i,r))return!0}else c+=Ma(o,g,o,a,i,r),c+=Ma(d,a,d,g,i,r);break;case Ta.Z:if(n){if(va(p,f,d,g,e,i,r))return!0}else c+=Ma(p,f,d,g,i,r);p=d,f=g}}return n||(s=f,l=g,Math.abs(s-l)<1e-4)||(c+=Ma(p,f,d,g,i,r)||0),0!==c}var Oa=I({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},Po),Ra={style:I({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},Oo.style)},Na=Fi.concat(["invisible","culling","z","z2","zlevel","parent"]),Ea=function(t){function e(e){return t.call(this,e)||this}var i;return n(e,t),e.prototype.update=function(){var n=this;t.prototype.update.call(this);var i=this.style;if(i.decal){var r=this._decalEl=this._decalEl||new e;r.buildPath===e.prototype.buildPath&&(r.buildPath=function(t){n.buildPath(t,n.shape)}),r.silent=!0;var o=r.style;for(var a in i)o[a]!==i[a]&&(o[a]=i[a]);o.fill=i.fill?i.decal:null,o.decal=null,o.shadowColor=null,i.strokeFirst&&(o.stroke=null);for(var s=0;s<Na.length;++s)r[Na[s]]=this[Na[s]];r.__dirty|=1}else this._decalEl&&(this._decalEl=null)},e.prototype.getDecalElement=function(){return this._decalEl},e.prototype._init=function(e){var n=B(e);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var r=0;r<n.length;r++){var o=n[r],a=e[o];"style"===o?this.style?D(this.style,a):this.useStyle(a):"shape"===o?D(this.shape,a):t.prototype.attrKV.call(this,o,a)}this.style||this.useStyle({})},e.prototype.getDefaultStyle=function(){return null},e.prototype.getDefaultShape=function(){return{}},e.prototype.canBeInsideText=function(){return this.hasFill()},e.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if(G(t)){var e=Un(t,0);return e>.5?ki:e>.2?"#eee":Ai}if(t)return Ai}return ki},e.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(G(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())===Un(t,0)<.4)return e}},e.prototype.buildPath=function(t,e,n){},e.prototype.pathUpdated=function(){this.__dirty&=-5},e.prototype.getUpdatedPathProxy=function(t){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},e.prototype.createPathProxy=function(){this.path=new ya(!1)},e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))},e.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},e.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,n=!t;if(n){var i=!1;this.path||(i=!0,this.createPathProxy());var r=this.path;(i||4&this.__dirty)&&(r.beginPath(),this.buildPath(r,this.shape,!1),this.pathUpdated()),t=r.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||n){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var l=this.strokeContainThreshold;s=Math.max(s,null==l?4:l)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),function(t,e,n,i){return Pa(t,e,!0,n,i)}(o,a/s,t,e)))return!0}if(this.hasFill())return function(t,e,n){return Pa(t,0,!1,e,n)}(o,t,e)}return!1},e.prototype.dirtyShape=function(){this.__dirty|=4,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},e.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},e.prototype.animateShape=function(t){return this.animate("shape",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},e.prototype.attrKV=function(e,n){"shape"===e?this.setShape(n):t.prototype.attrKV.call(this,e,n)},e.prototype.setShape=function(t,e){var n=this.shape;return n||(n=this.shape={}),"string"==typeof t?n[t]=e:D(n,t),this.dirtyShape(),this},e.prototype.shapeChanged=function(){return!!(4&this.__dirty)},e.prototype.createStyle=function(t){return gt(Oa,t)},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.shape&&!n.shape&&(n.shape=D({},this.shape))},e.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,l=!(n&&r);if(n&&n.shape?o?r?s=n.shape:(s=D({},i.shape),D(s,n.shape)):(s=D({},r?this.shape:i.shape),D(s,n.shape)):l&&(s=i.shape),s)if(o){this.shape=D({},this.shape);for(var u={},h=B(s),c=0;c<h.length;c++){var p=h[c];"object"==typeof s[p]?this.shape[p]=s[p]:u[p]=s[p]}this._transitionState(e,{shape:u},a)}else this.shape=s,this.dirtyShape()},e.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},e.prototype.getAnimationStyleProps=function(){return Ra},e.prototype.isZeroArea=function(){return!1},e.extend=function(t){var i=function(e){function i(n){var i=e.call(this,n)||this;return t.init&&t.init.call(i,n),i}return n(i,e),i.prototype.getDefaultStyle=function(){return T(t.style)},i.prototype.getDefaultShape=function(){return T(t.shape)},i}(e);for(var r in t)"function"==typeof t[r]&&(i.prototype[r]=t[r]);return i},e.initDefaultProps=((i=e.prototype).type="path",i.strokeContainThreshold=5,i.segmentIgnoreThreshold=0,i.subPixelOptimize=!1,i.autoBatch=!1,void(i.__dirty=7)),e}(Eo),za=I({strokeFirst:!0,font:o,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Oa),Ba=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},e.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},e.prototype.createStyle=function(t){return gt(za,t)},e.prototype.setBoundingRect=function(t){this._rect=t},e.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+="":e="";var n=Gi(e,t.font,t.textAlign,t.textBaseline);if(n.x+=t.x||0,n.y+=t.y||0,this.hasStroke()){var i=t.lineWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect},e.initDefaultProps=void(e.prototype.dirtyRectTolerance=10),e}(Eo);Ba.prototype.type="tspan";var Fa=I({x:0,y:0},Po),Va={style:I({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},Oo.style)};var Ha=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.createStyle=function(t){return gt(Fa,t)},e.prototype._getSize=function(t){var e=this.style,n=e[t];if(null!=n)return n;var i,r=(i=e.image)&&"string"!=typeof i&&i.width&&i.height?e.image:this.__image;if(!r)return 0;var o="width"===t?"height":"width",a=e[o];return null==a?r[t]:r[t]/r[o]*a},e.prototype.getWidth=function(){return this._getSize("width")},e.prototype.getHeight=function(){return this._getSize("height")},e.prototype.getAnimationStyleProps=function(){return Va},e.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new Ce(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},e}(Eo);Ha.prototype.type="image";var Wa=Math.round;function Ga(t,e,n){if(e){var i=e.x1,r=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=r,t.y1=o,t.y2=a;var s=n&&n.lineWidth;return s?(Wa(2*i)===Wa(2*r)&&(t.x1=t.x2=Ua(i,s,!0)),Wa(2*o)===Wa(2*a)&&(t.y1=t.y2=Ua(o,s,!0)),t):t}}function Ua(t,e,n){if(!e)return t;var i=Wa(2*t);return(i+Wa(e))%2==0?i/2:(i+(n?1:-1))/2}var Xa=function(){this.x=0,this.y=0,this.width=0,this.height=0},Ya={},qa=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Xa},e.prototype.buildPath=function(t,e){var n,i,r,o;if(this.subPixelOptimize){var a=function(t,e,n){if(e){var i=e.x,r=e.y,o=e.width,a=e.height;t.x=i,t.y=r,t.width=o,t.height=a;var s=n&&n.lineWidth;return s?(t.x=Ua(i,s,!0),t.y=Ua(r,s,!0),t.width=Math.max(Ua(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(Ua(r+a,s,!1)-t.y,0===a?0:1),t):t}}(Ya,e,this.style);n=a.x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a}else n=e.x,i=e.y,r=e.width,o=e.height;e.r?function(t,e){var n,i,r,o,a,s=e.x,l=e.y,u=e.width,h=e.height,c=e.r;u<0&&(s+=u,u=-u),h<0&&(l+=h,h=-h),"number"==typeof c?n=i=r=o=c:c instanceof Array?1===c.length?n=i=r=o=c[0]:2===c.length?(n=r=c[0],i=o=c[1]):3===c.length?(n=c[0],i=o=c[1],r=c[2]):(n=c[0],i=c[1],r=c[2],o=c[3]):n=i=r=o=0,n+i>u&&(n*=u/(a=n+i),i*=u/a),r+o>u&&(r*=u/(a=r+o),o*=u/a),i+r>h&&(i*=h/(a=i+r),r*=h/a),n+o>h&&(n*=h/(a=n+o),o*=h/a),t.moveTo(s+n,l),t.lineTo(s+u-i,l),0!==i&&t.arc(s+u-i,l+i,i,-Math.PI/2,0),t.lineTo(s+u,l+h-r),0!==r&&t.arc(s+u-r,l+h-r,r,0,Math.PI/2),t.lineTo(s+o,l+h),0!==o&&t.arc(s+o,l+h-o,o,Math.PI/2,Math.PI),t.lineTo(s,l+n),0!==n&&t.arc(s+n,l+n,n,Math.PI,1.5*Math.PI)}(t,e):t.rect(n,i,r,o)},e.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},e}(Ea);qa.prototype.type="rect";var Za={fill:"#000"},ja={style:I({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},Oo.style)},Ka=function(t){function e(e){var n=t.call(this)||this;return n.type="text",n._children=[],n._defaultStyle=Za,n.attr(e),n}return n(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.update=function(){t.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var n=this._children[e];n.zlevel=this.zlevel,n.z=this.z,n.z2=this.z2,n.culling=this.culling,n.cursor=this.cursor,n.invisible=this.invisible}},e.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):t.prototype.updateTransform.call(this)},e.prototype.getLocalTransform=function(e){var n=this.innerTransformable;return n?n.getLocalTransform(e):t.prototype.getLocalTransform.call(this,e)},e.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),t.prototype.getComputedTransform.call(this)},e.prototype._updateSubTexts=function(){var t;this._childCursor=0,ns(t=this.style),R(t.rich,ns),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=e},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=null},e.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new Ce(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),s=o.getLocalTransform(n);s?(t.copy(a),t.applyTransform(s),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},e.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Za},e.prototype.setTextContent=function(t){0},e.prototype._mergeStyle=function(t,e){if(!e)return t;var n=e.rich,i=t.rich||n&&{};return D(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i),t},e.prototype._mergeRich=function(t,e){for(var n=B(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},D(t[r],e[r])}},e.prototype.getAnimationStyleProps=function(){return ja},e.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},e.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||o,n=t.padding,i=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=Yi(o),l=et(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=e.width,p=(n=null==c||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?Ao(t,e.font,c,"breakAll"===i,0).lines:[]).length*l,f=et(e.height,p);if(p>f&&h){var d=Math.floor(f/l);n=n.slice(0,d)}if(t&&a&&null!=c)for(var g=wo(c,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),y=0;y<n.length;y++)n[y]=bo(n[y],g);var v=f,m=0;for(y=0;y<n.length;y++)m=Math.max(Hi(n[y],o),m);null==c&&(c=m);var _=m;return r&&(v+=r[0]+r[2],_+=r[1]+r[3],c+=r[1]+r[3]),u&&(_=c),{lines:n,height:f,outerWidth:_,outerHeight:v,lineHeight:l,calculatedLineHeight:s,contentWidth:m,contentHeight:p,width:c}}(as(t),t),r=ss(t),a=!!t.backgroundColor,s=i.outerHeight,l=i.outerWidth,u=i.contentWidth,h=i.lines,c=i.lineHeight,p=this._defaultStyle,f=t.x||0,d=t.y||0,g=t.align||p.align||"left",y=t.verticalAlign||p.verticalAlign||"top",v=f,m=Xi(d,i.contentHeight,y);if(r||n){var _=Ui(f,l,g),x=Xi(d,s,y);r&&this._renderBackground(t,t,_,x,l,s)}m+=c/2,n&&(v=os(f,g,n),"top"===y?m+=n[0]:"bottom"===y&&(m-=n[2]));for(var w=0,b=!1,S=(rs("fill"in t?t.fill:(b=!0,p.fill))),M=(is("stroke"in t?t.stroke:a||p.autoStroke&&!b?null:(w=2,p.stroke))),T=t.textShadowBlur>0,C=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),D=i.calculatedLineHeight,I=0;I<h.length;I++){var k=this._getOrCreateChild(Ba),A=k.createStyle();k.useStyle(A),A.text=h[I],A.x=v,A.y=m,g&&(A.textAlign=g),A.textBaseline="middle",A.opacity=t.opacity,A.strokeFirst=!0,T&&(A.shadowBlur=t.textShadowBlur||0,A.shadowColor=t.textShadowColor||"transparent",A.shadowOffsetX=t.textShadowOffsetX||0,A.shadowOffsetY=t.textShadowOffsetY||0),A.stroke=M,A.fill=S,M&&(A.lineWidth=t.lineWidth||w,A.lineDash=t.lineDash,A.lineDashOffset=t.lineDashOffset||0),A.font=e,es(A,t),m+=c,C&&k.setBoundingRect(new Ce(Ui(A.x,t.width,A.textAlign),Xi(A.y,D,A.textBaseline),u,D))}},e.prototype._updateRichTexts=function(){var t=this.style,e=function(t,e){var n=new Co;if(null!=t&&(t+=""),!t)return n;for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=_o.lastIndex=0;null!=(i=_o.exec(t));){var u=i.index;u>l&&Do(n,t.substring(l,u),e,s),Do(n,i[2],e,s,i[1]),l=_o.lastIndex}l<t.length&&Do(n,t.substring(l,t.length),e,s);var h=[],c=0,p=0,f=e.padding,d="truncate"===a,g="truncate"===e.lineOverflow;function y(t,e,n){t.width=e,t.lineHeight=n,c+=n,p=Math.max(p,e)}t:for(var v=0;v<n.lines.length;v++){for(var m=n.lines[v],_=0,x=0,w=0;w<m.tokens.length;w++){var b=(P=m.tokens[w]).styleName&&e.rich[P.styleName]||{},S=P.textPadding=b.padding,M=S?S[1]+S[3]:0,T=P.font=b.font||e.font;P.contentHeight=Yi(T);var C=et(b.height,P.contentHeight);if(P.innerHeight=C,S&&(C+=S[0]+S[2]),P.height=C,P.lineHeight=nt(b.lineHeight,e.lineHeight,C),P.align=b&&b.align||e.align,P.verticalAlign=b&&b.verticalAlign||"middle",g&&null!=o&&c+P.lineHeight>o){w>0?(m.tokens=m.tokens.slice(0,w),y(m,x,_),n.lines=n.lines.slice(0,v+1)):n.lines=n.lines.slice(0,v);break t}var D=b.width,I=null==D||"auto"===D;if("string"==typeof D&&"%"===D.charAt(D.length-1))P.percentWidth=D,h.push(P),P.contentWidth=Hi(P.text,T);else{if(I){var k=b.backgroundColor,A=k&&k.image;A&&mo(A=go(A))&&(P.width=Math.max(P.width,A.width*C/A.height))}var L=d&&null!=r?r-x:null;null!=L&&L<P.width?!I||L<M?(P.text="",P.width=P.contentWidth=0):(P.text=xo(P.text,L-M,T,e.ellipsis,{minChar:e.truncateMinChar}),P.width=P.contentWidth=Hi(P.text,T)):P.contentWidth=Hi(P.text,T)}P.width+=M,x+=P.width,b&&(_=Math.max(_,P.lineHeight))}y(m,x,_)}for(n.outerWidth=n.width=et(r,p),n.outerHeight=n.height=et(o,c),n.contentHeight=c,n.contentWidth=p,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]),v=0;v<h.length;v++){var P,O=(P=h[v]).percentWidth;P.width=parseInt(O,10)/100*n.width}return n}(as(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,h=t.verticalAlign||l.verticalAlign,c=Ui(a,i,u),p=Xi(s,r,h),f=c,d=p;o&&(f+=o[3],d+=o[0]);var g=f+n;ss(t)&&this._renderBackground(t,t,c,p,i,r);for(var y=!!t.backgroundColor,v=0;v<e.lines.length;v++){for(var m=e.lines[v],_=m.tokens,x=_.length,w=m.lineHeight,b=m.width,S=0,M=f,T=g,C=x-1,D=void 0;S<x&&(!(D=_[S]).align||"left"===D.align);)this._placeToken(D,t,w,d,M,"left",y),b-=D.width,M+=D.width,S++;for(;C>=0&&"right"===(D=_[C]).align;)this._placeToken(D,t,w,d,T,"right",y),b-=D.width,T-=D.width,C--;for(M+=(n-(M-f)-(g-T)-b)/2;S<=C;)D=_[S],this._placeToken(D,t,w,d,M+D.width/2,"center",y),M+=D.width,S++;d+=w}},e.prototype._placeToken=function(t,e,n,i,r,a,s){var l=e.rich[t.styleName]||{};l.text=t.text;var u=t.verticalAlign,h=i+n/2;"top"===u?h=i+t.height/2:"bottom"===u&&(h=i+n-t.height/2),!t.isLineHolder&&ss(l)&&this._renderBackground(l,e,"right"===a?r-t.width:"center"===a?r-t.width/2:r,h-t.height/2,t.width,t.height);var c=!!l.backgroundColor,p=t.textPadding;p&&(r=os(r,a,p),h-=t.height/2-p[0]-t.innerHeight/2);var f=this._getOrCreateChild(Ba),d=f.createStyle();f.useStyle(d);var g=this._defaultStyle,y=!1,v=0,m=rs("fill"in l?l.fill:"fill"in e?e.fill:(y=!0,g.fill)),_=is("stroke"in l?l.stroke:"stroke"in e?e.stroke:c||s||g.autoStroke&&!y?null:(v=2,g.stroke)),x=l.textShadowBlur>0||e.textShadowBlur>0;d.text=t.text,d.x=r,d.y=h,x&&(d.shadowBlur=l.textShadowBlur||e.textShadowBlur||0,d.shadowColor=l.textShadowColor||e.textShadowColor||"transparent",d.shadowOffsetX=l.textShadowOffsetX||e.textShadowOffsetX||0,d.shadowOffsetY=l.textShadowOffsetY||e.textShadowOffsetY||0),d.textAlign=a,d.textBaseline="middle",d.font=t.font||o,d.opacity=nt(l.opacity,e.opacity,1),es(d,l),_&&(d.lineWidth=nt(l.lineWidth,e.lineWidth,v),d.lineDash=et(l.lineDash,e.lineDash),d.lineDashOffset=e.lineDashOffset||0,d.stroke=_),m&&(d.fill=m);var w=t.contentWidth,b=t.contentHeight;f.setBoundingRect(new Ce(Ui(d.x,w,d.textAlign),Xi(d.y,b,d.textBaseline),w,b))},e.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u=t.backgroundColor,h=t.borderWidth,c=t.borderColor,p=u&&u.image,f=u&&!p,d=t.borderRadius,g=this;if(f||t.lineHeight||h&&c){(a=this._getOrCreateChild(qa)).useStyle(a.createStyle()),a.style.fill=null;var y=a.shape;y.x=n,y.y=i,y.width=r,y.height=o,y.r=d,a.dirtyShape()}if(f)(l=a.style).fill=u||null,l.fillOpacity=et(t.fillOpacity,1);else if(p){(s=this._getOrCreateChild(Ha)).onload=function(){g.dirtyStyle()};var v=s.style;v.image=u.image,v.x=n,v.y=i,v.width=r,v.height=o}h&&c&&((l=a.style).lineWidth=h,l.stroke=c,l.strokeOpacity=et(t.strokeOpacity,1),l.lineDash=t.borderDash,l.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill()&&a.hasStroke()&&(l.strokeFirst=!0,l.lineWidth*=2));var m=(a||s).style;m.shadowBlur=t.shadowBlur||0,m.shadowColor=t.shadowColor||"transparent",m.shadowOffsetX=t.shadowOffsetX||0,m.shadowOffsetY=t.shadowOffsetY||0,m.opacity=nt(t.opacity,e.opacity,1)},e.makeFont=function(t){var e="";return function(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}(t)&&(e=[t.fontStyle,t.fontWeight,ts(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&at(e)||t.textFont||t.font},e}(Eo),$a={left:!0,right:1,center:1},Qa={top:1,bottom:1,middle:1},Ja=["fontStyle","fontWeight","fontSize","fontFamily"];function ts(t){return"string"!=typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?"12px":t+"px":t}function es(t,e){for(var n=0;n<Ja.length;n++){var i=Ja[n],r=e[i];null!=r&&(t[i]=r)}}function ns(t){if(t){t.font=Ka.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||$a[e]?e:"left";var n=t.verticalAlign;"center"===n&&(n="middle"),t.verticalAlign=null==n||Qa[n]?n:"top",t.padding&&(t.padding=rt(t.padding))}}function is(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function rs(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function os(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function as(t){var e=t.text;return null!=e&&(e+=""),e}function ss(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var ls=Zr(),us=1,hs={},cs=Zr(),ps=Zr(),fs=["emphasis","blur","select"],ds=["normal","emphasis","blur","select"],gs="highlight",ys="downplay",vs="select",ms="unselect",_s="toggleSelect";function xs(t){return null!=t&&"none"!==t}var ws=new Sn(100);function bs(t){if(G(t)){var e=ws.get(t);return e||(e=Bn(t,-.1),ws.put(t,e)),e}if(K(t)){var n=D({},t);return n.colorStops=N(t.colorStops,(function(t){return{offset:t.offset,color:Bn(t.color,-.1)}})),n}return t}function Ss(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function Ms(t){Ss(t,"emphasis",2)}function Ts(t){2===t.hoverState&&Ss(t,"normal",0)}function Cs(t){Ss(t,"blur",1)}function Ds(t){1===t.hoverState&&Ss(t,"normal",0)}function Is(t){t.selected=!0}function ks(t){t.selected=!1}function As(t,e,n){e(t,n)}function Ls(t,e,n){As(t,e,n),t.isGroup&&t.traverse((function(t){As(t,e,n)}))}function Ps(t,e){switch(e){case"emphasis":t.hoverState=2;break;case"normal":t.hoverState=0;break;case"blur":t.hoverState=1;break;case"select":t.selected=!0}}function Os(t,e){var n=this.states[t];if(this.style){if("emphasis"===t)return function(t,e,n,i){var r=n&&A(n,"select")>=0,o=!1;if(t instanceof Ea){var a=cs(t),s=r&&a.selectFill||a.normalFill,l=r&&a.selectStroke||a.normalStroke;if(xs(s)||xs(l)){var u=(i=i||{}).style||{};"inherit"===u.fill?(o=!0,i=D({},i),(u=D({},u)).fill=s):!xs(u.fill)&&xs(s)?(o=!0,i=D({},i),(u=D({},u)).fill=bs(s)):!xs(u.stroke)&&xs(l)&&(o||(i=D({},i),u=D({},u)),u.stroke=bs(l)),i.style=u}}if(i&&null==i.z2){o||(i=D({},i));var h=t.z2EmphasisLift;i.z2=t.z2+(null!=h?h:10)}return i}(this,0,e,n);if("blur"===t)return function(t,e,n){var i=A(t.currentStates,e)>=0,r=t.style.opacity,o=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),a=(n=n||{}).style||{};return null==a.opacity&&(n=D({},n),a=D({opacity:i?r:.1*o.opacity},a),n.style=a),n}(this,t,n);if("select"===t)return function(t,e,n){if(n&&null==n.z2){n=D({},n);var i=t.z2SelectLift;n.z2=t.z2+(null!=i?i:9)}return n}(this,0,n)}return n}function Rs(t){t.stateProxy=Os;var e=t.getTextContent(),n=t.getTextGuideLine();e&&(e.stateProxy=Os),n&&(n.stateProxy=Os)}function Ns(t,e){!Ws(t,e)&&!t.__highByOuter&&Ls(t,Ms)}function Es(t,e){!Ws(t,e)&&!t.__highByOuter&&Ls(t,Ts)}function zs(t,e){t.__highByOuter|=1<<(e||0),Ls(t,Ms)}function Bs(t,e){!(t.__highByOuter&=~(1<<(e||0)))&&Ls(t,Ts)}function Fs(t){Ls(t,Ds)}function Vs(t){Ls(t,Is)}function Hs(t){Ls(t,ks)}function Ws(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Gs(t){var e=t.getModel(),n=[],i=[];e.eachComponent((function(e,r){var o=ps(r),a="series"===e,s=a?t.getViewOfSeriesModel(r):t.getViewOfComponentModel(r);!a&&i.push(s),o.isBlured&&(s.group.traverse((function(t){Ds(t)})),a&&n.push(r)),o.isBlured=!1})),R(i,(function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(n,!1,e)}))}function Us(t,e,n,i){var r=i.getModel();function o(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&Fs(i)}}if(n=n||"coordinateSystem",null!=t&&e&&"none"!==e){var a=r.getSeriesByIndex(t),s=a.coordinateSystem;s&&s.master&&(s=s.master);var l=[];r.eachSeries((function(t){var r=a===t,u=t.coordinateSystem;if(u&&u.master&&(u=u.master),!("series"===n&&!r||"coordinateSystem"===n&&!(u&&s?u===s:r)||"series"===e&&r)){if(i.getViewOfSeriesModel(t).group.traverse((function(t){t.__highByOuter&&r&&"self"===e||Cs(t)})),O(e))o(t.getData(),e);else if(Y(e))for(var h=B(e),c=0;c<h.length;c++)o(t.getData(h[c]),e[h[c]]);l.push(t),ps(t).isBlured=!0}})),r.eachComponent((function(t,e){if("series"!==t){var n=i.getViewOfComponentModel(e);n&&n.toggleBlurSeries&&n.toggleBlurSeries(l,!0,r)}}))}}function Xs(t,e,n){if(null!=t&&null!=e){var i=n.getModel().getComponent(t,e);if(i){ps(i).isBlured=!0;var r=n.getViewOfComponentModel(i);r&&r.focusBlurEnabled&&r.group.traverse((function(t){Cs(t)}))}}}function Ys(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;var o=i.getModel().getComponent(t,e);if(!o)return r;var a=i.getViewOfComponentModel(o);if(!a||!a.findHighDownDispatchers)return r;for(var s,l=a.findHighDownDispatchers(n),u=0;u<l.length;u++)if("self"===ls(l[u]).focus){s=!0;break}return{focusSelf:s,dispatchers:l}}function qs(t){R(t.getAllData(),(function(e){var n=e.data,i=e.type;n.eachItemGraphicEl((function(e,n){t.isSelected(n,i)?Vs(e):Hs(e)}))}))}function Zs(t){var e=[];return t.eachSeries((function(t){R(t.getAllData(),(function(n){n.data;var i=n.type,r=t.getSelectedDataIndices();if(r.length>0){var o={dataIndex:r,seriesIndex:t.seriesIndex};null!=i&&(o.dataType=i),e.push(o)}}))})),e}function js(t,e,n){tl(t,!0),Ls(t,Rs),function(t,e,n){var i=ls(t);null!=e?(i.focus=e,i.blurScope=n):i.focus&&(i.focus=null)}(t,e,n)}function Ks(t,e,n,i){i?function(t){tl(t,!1)}(t):js(t,e,n)}var $s=["emphasis","blur","select"],Qs={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function Js(t,e,n,i){n=n||"itemStyle";for(var r=0;r<$s.length;r++){var o=$s[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[Qs[n]]()}}function tl(t,e){var n=!1===e,i=t;t.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=t.highDownSilentOnTouch),n&&!i.__highDownDispatcher||(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!n)}function el(t){return!(!t||!t.__highDownDispatcher)}function nl(t){var e=t.type;return e===vs||e===ms||e===_s}function il(t){var e=t.type;return e===gs||e===ys}var rl=ya.CMD,ol=[[],[],[]],al=Math.sqrt,sl=Math.atan2;var ll=Math.sqrt,ul=Math.sin,hl=Math.cos,cl=Math.PI;function pl(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function fl(t,e){return(t[0]*e[0]+t[1]*e[1])/(pl(t)*pl(e))}function dl(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(fl(t,e))}function gl(t,e,n,i,r,o,a,s,l,u,h){var c=l*(cl/180),p=hl(c)*(t-n)/2+ul(c)*(e-i)/2,f=-1*ul(c)*(t-n)/2+hl(c)*(e-i)/2,d=p*p/(a*a)+f*f/(s*s);d>1&&(a*=ll(d),s*=ll(d));var g=(r===o?-1:1)*ll((a*a*(s*s)-a*a*(f*f)-s*s*(p*p))/(a*a*(f*f)+s*s*(p*p)))||0,y=g*a*f/s,v=g*-s*p/a,m=(t+n)/2+hl(c)*y-ul(c)*v,_=(e+i)/2+ul(c)*y+hl(c)*v,x=dl([1,0],[(p-y)/a,(f-v)/s]),w=[(p-y)/a,(f-v)/s],b=[(-1*p-y)/a,(-1*f-v)/s],S=dl(w,b);if(fl(w,b)<=-1&&(S=cl),fl(w,b)>=1&&(S=0),S<0){var M=Math.round(S/cl*1e6)/1e6;S=2*cl+M%2*cl}h.addData(u,m,_,a,s,x,S,c,o)}var yl=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,vl=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;var ml=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.applyTransform=function(t){},e}(Ea);function _l(t){return null!=t.setData}function xl(t,e){var n=function(t){var e=new ya;if(!t)return e;var n,i=0,r=0,o=i,a=r,s=ya.CMD,l=t.match(yl);if(!l)return e;for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,f=h.match(vl)||[],d=f.length,g=0;g<d;g++)f[g]=parseFloat(f[g]);for(var y=0;y<d;){var v=void 0,m=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=i,T=r,C=void 0,D=void 0;switch(c){case"l":i+=f[y++],r+=f[y++],p=s.L,e.addData(p,i,r);break;case"L":i=f[y++],r=f[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=f[y++],r+=f[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=f[y++],r=f[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=f[y++],p=s.L,e.addData(p,i,r);break;case"H":i=f[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=f[y++],p=s.L,e.addData(p,i,r);break;case"V":r=f[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,f[y++],f[y++],f[y++],f[y++],f[y++],f[y++]),i=f[y-2],r=f[y-1];break;case"c":p=s.C,e.addData(p,f[y++]+i,f[y++]+r,f[y++]+i,f[y++]+r,f[y++]+i,f[y++]+r),i+=f[y-2],r+=f[y-1];break;case"S":v=i,m=r,C=e.len(),D=e.data,n===s.C&&(v+=i-D[C-4],m+=r-D[C-3]),p=s.C,M=f[y++],T=f[y++],i=f[y++],r=f[y++],e.addData(p,v,m,M,T,i,r);break;case"s":v=i,m=r,C=e.len(),D=e.data,n===s.C&&(v+=i-D[C-4],m+=r-D[C-3]),p=s.C,M=i+f[y++],T=r+f[y++],i+=f[y++],r+=f[y++],e.addData(p,v,m,M,T,i,r);break;case"Q":M=f[y++],T=f[y++],i=f[y++],r=f[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=f[y++]+i,T=f[y++]+r,i+=f[y++],r+=f[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":v=i,m=r,C=e.len(),D=e.data,n===s.Q&&(v+=i-D[C-4],m+=r-D[C-3]),i=f[y++],r=f[y++],p=s.Q,e.addData(p,v,m,i,r);break;case"t":v=i,m=r,C=e.len(),D=e.data,n===s.Q&&(v+=i-D[C-4],m+=r-D[C-3]),i+=f[y++],r+=f[y++],p=s.Q,e.addData(p,v,m,i,r);break;case"A":_=f[y++],x=f[y++],w=f[y++],b=f[y++],S=f[y++],gl(M=i,T=r,i=f[y++],r=f[y++],b,S,_,x,w,p=s.A,e);break;case"a":_=f[y++],x=f[y++],w=f[y++],b=f[y++],S=f[y++],gl(M=i,T=r,i+=f[y++],r+=f[y++],b,S,_,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}return e.toStatic(),e}(t),i=D({},e);return i.buildPath=function(t){if(_l(t)){t.setData(n.data),(e=t.getContext())&&t.rebuildPath(e,1)}else{var e=t;n.rebuildPath(e,1)}},i.applyTransform=function(t){!function(t,e){if(e){var n,i,r,o,a,s,l=t.data,u=t.len(),h=rl.M,c=rl.C,p=rl.L,f=rl.R,d=rl.A,g=rl.Q;for(r=0,o=0;r<u;){switch(n=l[r++],o=r,i=0,n){case h:case p:i=1;break;case c:i=3;break;case g:i=2;break;case d:var y=e[4],v=e[5],m=al(e[0]*e[0]+e[1]*e[1]),_=al(e[2]*e[2]+e[3]*e[3]),x=sl(-e[1]/_,e[0]/m);l[r]*=m,l[r++]+=y,l[r]*=_,l[r++]+=v,l[r++]*=m,l[r++]*=_,l[r++]+=x,l[r++]+=x,o=r+=2;break;case f:s[0]=l[r++],s[1]=l[r++],Et(s,s,e),l[o++]=s[0],l[o++]=s[1],s[0]+=l[r++],s[1]+=l[r++],Et(s,s,e),l[o++]=s[0],l[o++]=s[1]}for(a=0;a<i;a++){var w=ol[a];w[0]=l[r++],w[1]=l[r++],Et(w,w,e),l[o++]=w[0],l[o++]=w[1]}}t.increaseVersion()}}(n,t),this.dirtyShape()},i}var wl=function(){this.cx=0,this.cy=0,this.r=0},bl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new wl},e.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},e}(Ea);bl.prototype.type="circle";var Sl=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},Ml=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Sl},e.prototype.buildPath=function(t,e){var n=.5522848,i=e.cx,r=e.cy,o=e.rx,a=e.ry,s=o*n,l=a*n;t.moveTo(i-o,r),t.bezierCurveTo(i-o,r-l,i-s,r-a,i,r-a),t.bezierCurveTo(i+s,r-a,i+o,r-l,i+o,r),t.bezierCurveTo(i+o,r+l,i+s,r+a,i,r+a),t.bezierCurveTo(i-s,r+a,i-o,r+l,i-o,r),t.closePath()},e}(Ea);Ml.prototype.type="ellipse";var Tl=Math.PI,Cl=2*Tl,Dl=Math.sin,Il=Math.cos,kl=Math.acos,Al=Math.atan2,Ll=Math.abs,Pl=Math.sqrt,Ol=Math.max,Rl=Math.min,Nl=1e-4;function El(t,e,n,i,r,o,a){var s=t-n,l=e-i,u=(a?o:-o)/Pl(s*s+l*l),h=u*l,c=-u*s,p=t+h,f=e+c,d=n+h,g=i+c,y=(p+d)/2,v=(f+g)/2,m=d-p,_=g-f,x=m*m+_*_,w=r-o,b=p*g-d*f,S=(_<0?-1:1)*Pl(Ol(0,w*w*x-b*b)),M=(b*_-m*S)/x,T=(-b*m-_*S)/x,C=(b*_+m*S)/x,D=(-b*m+_*S)/x,I=M-y,k=T-v,A=C-y,L=D-v;return I*I+k*k>A*A+L*L&&(M=C,T=D),{cx:M,cy:T,x0:-h,y0:-c,x1:M*(r/w-1),y1:T*(r/w-1)}}function zl(t,e){var n,i=Ol(e.r,0),r=Ol(e.r0||0,0),o=i>0;if(o||r>0){if(o||(i=r,r=0),r>i){var a=i;i=r,r=a}var s=e.startAngle,l=e.endAngle;if(!isNaN(s)&&!isNaN(l)){var u=e.cx,h=e.cy,c=!!e.clockwise,p=Ll(l-s),f=p>Cl&&p%Cl;if(f>Nl&&(p=f),i>Nl)if(p>Cl-Nl)t.moveTo(u+i*Il(s),h+i*Dl(s)),t.arc(u,h,i,s,l,!c),r>Nl&&(t.moveTo(u+r*Il(l),h+r*Dl(l)),t.arc(u,h,r,l,s,c));else{var d=void 0,g=void 0,y=void 0,v=void 0,m=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=void 0,T=void 0,C=void 0,D=void 0,I=void 0,k=void 0,A=i*Il(s),L=i*Dl(s),P=r*Il(l),O=r*Dl(l),R=p>Nl;if(R){var N=e.cornerRadius;N&&(n=function(t){var e;if(H(t)){var n=t.length;if(!n)return t;e=1===n?[t[0],t[0],0,0]:2===n?[t[0],t[0],t[1],t[1]]:3===n?t.concat(t[2]):t}else e=[t,t,t,t];return e}(N),d=n[0],g=n[1],y=n[2],v=n[3]);var E=Ll(i-r)/2;if(m=Rl(E,y),_=Rl(E,v),x=Rl(E,d),w=Rl(E,g),M=b=Ol(m,_),T=S=Ol(x,w),(b>Nl||S>Nl)&&(C=i*Il(l),D=i*Dl(l),I=r*Il(s),k=r*Dl(s),p<Tl)){var z=function(t,e,n,i,r,o,a,s){var l=n-t,u=i-e,h=a-r,c=s-o,p=c*l-h*u;if(!(p*p<Nl))return[t+(p=(h*(e-o)-c*(t-r))/p)*l,e+p*u]}(A,L,I,k,C,D,P,O);if(z){var B=A-z[0],F=L-z[1],V=C-z[0],W=D-z[1],G=1/Dl(kl((B*V+F*W)/(Pl(B*B+F*F)*Pl(V*V+W*W)))/2),U=Pl(z[0]*z[0]+z[1]*z[1]);M=Rl(b,(i-U)/(G+1)),T=Rl(S,(r-U)/(G-1))}}}if(R)if(M>Nl){var X=Rl(y,M),Y=Rl(v,M),q=El(I,k,A,L,i,X,c),Z=El(C,D,P,O,i,Y,c);t.moveTo(u+q.cx+q.x0,h+q.cy+q.y0),M<b&&X===Y?t.arc(u+q.cx,h+q.cy,M,Al(q.y0,q.x0),Al(Z.y0,Z.x0),!c):(X>0&&t.arc(u+q.cx,h+q.cy,X,Al(q.y0,q.x0),Al(q.y1,q.x1),!c),t.arc(u,h,i,Al(q.cy+q.y1,q.cx+q.x1),Al(Z.cy+Z.y1,Z.cx+Z.x1),!c),Y>0&&t.arc(u+Z.cx,h+Z.cy,Y,Al(Z.y1,Z.x1),Al(Z.y0,Z.x0),!c))}else t.moveTo(u+A,h+L),t.arc(u,h,i,s,l,!c);else t.moveTo(u+A,h+L);if(r>Nl&&R)if(T>Nl){X=Rl(d,T),q=El(P,O,C,D,r,-(Y=Rl(g,T)),c),Z=El(A,L,I,k,r,-X,c);t.lineTo(u+q.cx+q.x0,h+q.cy+q.y0),T<S&&X===Y?t.arc(u+q.cx,h+q.cy,T,Al(q.y0,q.x0),Al(Z.y0,Z.x0),!c):(Y>0&&t.arc(u+q.cx,h+q.cy,Y,Al(q.y0,q.x0),Al(q.y1,q.x1),!c),t.arc(u,h,r,Al(q.cy+q.y1,q.cx+q.x1),Al(Z.cy+Z.y1,Z.cx+Z.x1),c),X>0&&t.arc(u+Z.cx,h+Z.cy,X,Al(Z.y1,Z.x1),Al(Z.y0,Z.x0),!c))}else t.lineTo(u+P,h+O),t.arc(u,h,r,l,s,c);else t.lineTo(u+P,h+O)}else t.moveTo(u,h);t.closePath()}}}var Bl=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},Fl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Bl},e.prototype.buildPath=function(t,e){zl(t,e)},e.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},e}(Ea);Fl.prototype.type="sector";var Vl=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Hl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Vl},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},e}(Ea);function Wl(t,e,n){var i=e.smooth,r=e.points;if(r&&r.length>=2){if(i){var o=function(t,e,n,i){var r,o,a,s,l=[],u=[],h=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var p=0,f=t.length;p<f;p++)zt(a,a,t[p]),Bt(s,s,t[p]);zt(a,a,i[0]),Bt(s,s,i[1])}for(p=0,f=t.length;p<f;p++){var d=t[p];if(n)r=t[p?p-1:f-1],o=t[(p+1)%f];else{if(0===p||p===f-1){l.push(bt(t[p]));continue}r=t[p-1],o=t[p+1]}Mt(u,o,r),kt(u,u,e);var g=Lt(d,r),y=Lt(d,o),v=g+y;0!==v&&(g/=v,y/=v),kt(h,u,-g),kt(c,u,y);var m=St([],d,h),_=St([],d,c);i&&(Bt(m,m,a),zt(m,m,s),Bt(_,_,a),zt(_,_,s)),l.push(m),l.push(_)}return n&&l.push(l.shift()),l}(r,i,n,e.smoothConstraint);t.moveTo(r[0][0],r[0][1]);for(var a=r.length,s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}}else{t.moveTo(r[0][0],r[0][1]);s=1;for(var c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Hl.prototype.type="ring";var Gl=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},Ul=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Gl},e.prototype.buildPath=function(t,e){Wl(t,e,!0)},e}(Ea);Ul.prototype.type="polygon";var Xl=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},Yl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Xl},e.prototype.buildPath=function(t,e){Wl(t,e,!1)},e}(Ea);Yl.prototype.type="polyline";var ql={},Zl=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},jl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Zl},e.prototype.buildPath=function(t,e){var n,i,r,o;if(this.subPixelOptimize){var a=Ga(ql,e,this.style);n=a.x1,i=a.y1,r=a.x2,o=a.y2}else n=e.x1,i=e.y1,r=e.x2,o=e.y2;var s=e.percent;0!==s&&(t.moveTo(n,i),s<1&&(r=n*(1-s)+r*s,o=i*(1-s)+o*s),t.lineTo(r,o))},e.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},e}(Ea);jl.prototype.type="line";var Kl=[],$l=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function Ql(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?ln:sn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?ln:sn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?dn:fn)(t.x1,t.cpx1,t.x2,e),(n?dn:fn)(t.y1,t.cpy1,t.y2,e)]}var Jl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new $l},e.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,h=e.percent;0!==h&&(t.moveTo(n,i),null==l||null==u?(h<1&&(yn(n,a,r,h,Kl),a=Kl[1],r=Kl[2],yn(i,s,o,h,Kl),s=Kl[1],o=Kl[2]),t.quadraticCurveTo(a,s,r,o)):(h<1&&(cn(n,a,l,r,h,Kl),a=Kl[1],l=Kl[2],r=Kl[3],cn(i,s,u,o,h,Kl),s=Kl[1],u=Kl[2],o=Kl[3]),t.bezierCurveTo(a,s,l,u,r,o)))},e.prototype.pointAt=function(t){return Ql(this.shape,t,!1)},e.prototype.tangentAt=function(t){var e=Ql(this.shape,t,!0);return At(e,e)},e}(Ea);Jl.prototype.type="bezier-curve";var tu=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},eu=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new tu},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,o,a,!s)},e}(Ea);eu.prototype.type="arc";var nu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="compound",e}return n(e,t),e.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},e.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},e.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},e.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},e.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),Ea.prototype.getBoundingRect.call(this)},e}(Ea),iu=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}(),ru=function(t){function e(e,n,i,r,o,a){var s=t.call(this,o)||this;return s.x=null==e?0:e,s.y=null==n?0:n,s.x2=null==i?1:i,s.y2=null==r?0:r,s.type="linear",s.global=a||!1,s}return n(e,t),e}(iu),ou=function(t){function e(e,n,i,r,o){var a=t.call(this,r)||this;return a.x=null==e?.5:e,a.y=null==n?.5:n,a.r=null==i?.5:i,a.type="radial",a.global=o||!1,a}return n(e,t),e}(iu),au=[0,0],su=[0,0],lu=new ve,uu=new ve,hu=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new ve;for(n=0;n<2;n++)this._axes[n]=new ve;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,s=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,s),n[3].set(r,s),e)for(var l=0;l<4;l++)n[l].transform(e);ve.sub(i[0],n[1],n[0]),ve.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(l=0;l<2;l++)this._origin[l]=i[l].dot(n[0])},t.prototype.intersect=function(t,e){var n=!0,i=!e;return lu.set(1/0,1/0),uu.set(0,0),!this._intersectCheckOneSide(this,t,lu,uu,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,lu,uu,i,-1)&&(n=!1,i)||i||ve.copy(e,n?lu:uu),n},t.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,au),this._getProjMinMaxOnAxis(s,e._corners,su),au[1]<su[0]||au[0]>su[1]){if(a=!1,r)return a;var u=Math.abs(su[0]-au[1]),h=Math.abs(au[0]-su[1]);Math.min(u,h)>i.len()&&(u<h?ve.scale(i,l,-u*o):ve.scale(i,l,h*o))}else if(n){u=Math.abs(su[0]-au[1]),h=Math.abs(au[0]-su[1]);Math.min(u,h)<n.len()&&(u<h?ve.scale(n,l,u*o):ve.scale(n,l,-h*o))}}return a},t.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++){var u=e[l].dot(i)+r[t];a=Math.min(u,a),s=Math.max(u,s)}n[0]=a,n[1]=s},t}(),cu=[],pu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return n(e,t),e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.useStyle=function(){this.style={}},e.prototype.getCursor=function(){return this._cursor},e.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},e.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},e.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},e.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},e.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},e.prototype.getDisplayables=function(){return this._displayables},e.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},e.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},e.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){var e;(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},e.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Ce(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(cu)),t.union(i)}this._rect=t}return this._rect},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++){if(this._displayables[i].contain(t,e))return!0}return!1},e}(Eo),fu=Zr();function du(t,e,n,i,r,o,a){var s,l=!1;W(r)?(a=o,o=r,r=null):Y(r)&&(o=r.cb,a=r.during,l=r.isFrom,s=r.removeOpt,r=r.dataIndex);var u="leave"===t;u||e.stopAnimation("leave");var h=function(t,e,n,i,r){var o;if(e&&e.ecModel){var a=e.ecModel.getUpdatePayload();o=a&&a.animation}var s="update"===t;if(e&&e.isAnimationEnabled()){var l=void 0,u=void 0,h=void 0;return i?(l=et(i.duration,200),u=et(i.easing,"cubicOut"),h=0):(l=e.getShallow(s?"animationDurationUpdate":"animationDuration"),u=e.getShallow(s?"animationEasingUpdate":"animationEasing"),h=e.getShallow(s?"animationDelayUpdate":"animationDelay")),o&&(null!=o.duration&&(l=o.duration),null!=o.easing&&(u=o.easing),null!=o.delay&&(h=o.delay)),W(h)&&(h=h(n,r)),W(l)&&(l=l(n)),{duration:l||0,delay:h,easing:u}}return null}(t,i,r,u?s||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null);if(h&&h.duration>0){var c={duration:h.duration,delay:h.delay||0,easing:h.easing,done:o,force:!!o||!!a,setToFinal:!u,scope:t,during:a};l?e.animateFrom(n,c):e.animateTo(n,c)}else e.stopAnimation(),!l&&e.attr(n),a&&a(1),o&&o()}function gu(t,e,n,i,r,o){du("update",t,e,n,i,r,o)}function yu(t,e,n,i,r,o){du("enter",t,e,n,i,r,o)}function vu(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++){if("leave"===t.animators[e].scope)return!0}return!1}function mu(t,e,n,i,r,o){vu(t)||du("leave",t,e,n,i,r,o)}function _u(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),mu(t,{style:{opacity:0}},e,n,i)}function xu(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse((function(t){t.isGroup||_u(t,e,n,i)})):_u(t,e,n,i)}function wu(t){fu(t).oldStyle=t.style}var bu=Math.max,Su=Math.min,Mu={};var Tu=function(t,e){var i=xl(t,e);return function(t){function e(e){var n=t.call(this,e)||this;return n.applyTransform=i.applyTransform,n.buildPath=i.buildPath,n}return n(e,t),e}(ml)};function Cu(t,e){Mu[t]=e}function Du(t,e,n,i){var r=function(t,e){return new ml(xl(t,e))}(t,e);return n&&("center"===i&&(n=ku(n,r.getBoundingRect())),Lu(r,n)),r}function Iu(t,e,n){var i=new Ha({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(ku(e,r))}}});return i}function ku(t,e){var n,i=e.width/e.height,r=t.height*i;return n=r<=t.width?t.height:(r=t.width)/i,{x:t.x+t.width/2-r/2,y:t.y+t.height/2-n/2,width:r,height:n}}var Au=function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}var a=new Ea(e);return a.createPathProxy(),a.buildPath=function(t){if(_l(t)){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e,1)}},a};function Lu(t,e){if(t.applyTransform){var n=t.getBoundingRect().calculateTransform(e);t.applyTransform(n)}}function Pu(t,e){return Ga(t,t,{lineWidth:e}),t}function Ou(t){return!t.isGroup}function Ru(t,e){var n;t.isGroup&&(n=e(t)),n||t.traverse(e)}function Nu(t,e){if(t)if(H(t))for(var n=0;n<t.length;n++)Ru(t[n],e);else Ru(t,e)}Cu("circle",bl),Cu("ellipse",Ml),Cu("sector",Fl),Cu("ring",Hl),Cu("polygon",Ul),Cu("polyline",Yl),Cu("rect",qa),Cu("line",jl),Cu("bezierCurve",Jl),Cu("arc",eu);var Eu={};function zu(t,e,n){var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal;r&&(i=r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null)),null==i&&(i=W(t.defaultText)?t.defaultText(o,t,n):t.defaultText);for(var l={normal:i},u=0;u<fs.length;u++){var h=fs[u],c=e[h];l[h]=et(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function Bu(t,e,n,i){n=n||Eu;for(var r=t instanceof Ka,o=!1,a=0;a<ds.length;a++){if((p=e[ds[a]])&&p.getShallow("show")){o=!0;break}}var s=r?t:t.getTextContent();if(o){r||(s||(s=new Ka,t.setTextContent(s)),t.stateProxy&&(s.stateProxy=t.stateProxy));var l=zu(n,e),u=e.normal,h=!!u.getShallow("show"),c=Vu(u,i&&i.normal,n,!1,!r);c.text=l.normal,r||t.setTextConfig(Hu(u,n,!1));for(a=0;a<fs.length;a++){var p,f=fs[a];if(p=e[f]){var d=s.ensureState(f),g=!!et(p.getShallow("show"),h);if(g!==h&&(d.ignore=!g),d.style=Vu(p,i&&i[f],n,!0,!r),d.style.text=l[f],!r)t.ensureState(f).textConfig=Hu(p,n,!0)}}s.silent=!!u.getShallow("silent"),null!=s.style.x&&(c.x=s.style.x),null!=s.style.y&&(c.y=s.style.y),s.ignore=!h,s.useStyle(c),s.dirty(),n.enableTextSetter&&(Yu(s).setLabelText=function(t){var i=zu(n,e,t);!function(t,e){for(var n=0;n<fs.length;n++){var i=fs[n],r=e[i],o=t.ensureState(i);o.style=o.style||{},o.style.text=r}var a=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(a,!0)}(s,i)})}else s&&(s.ignore=!0);t.dirty()}function Fu(t,e){e=e||"label";for(var n={normal:t.getModel(e)},i=0;i<fs.length;i++){var r=fs[i];n[r]=t.getModel([r,e])}return n}function Vu(t,e,n,i,r){var o={};return function(t,e,n,i,r){n=n||Eu;var o,a=e.ecModel,s=a&&a.option.textStyle,l=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||Eu).rich;if(n){e=e||{};for(var i=B(n),r=0;r<i.length;r++){e[i[r]]=1}}t=t.parentModel}return e}(e);if(l)for(var u in o={},l)if(l.hasOwnProperty(u)){var h=e.getModel(["rich",u]);Xu(o[u]={},h,s,n,i,r,!1,!0)}o&&(t.rich=o);var c=e.get("overflow");c&&(t.overflow=c);var p=e.get("minMargin");null!=p&&(t.margin=p);Xu(t,e,s,n,i,r,!0,!1)}(o,t,n,i,r),e&&D(o,e),o}function Hu(t,e,n){e=e||{};var i,r={},o=t.getShallow("rotate"),a=et(t.getShallow("distance"),n?null:5),s=t.getShallow("offset");return"outside"===(i=t.getShallow("position")||(n?null:"inside"))&&(i=e.defaultOutsidePosition||"top"),null!=i&&(r.position=i),null!=s&&(r.offset=s),null!=o&&(o*=Math.PI/180,r.rotation=o),null!=a&&(r.distance=a),r.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",r}var Wu=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],Gu=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],Uu=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function Xu(t,e,n,i,r,o,a,s){n=!r&&n||Eu;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=et(e.getShallow("opacity"),n.opacity);"inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h);var p=et(e.getShallow("textBorderWidth"),n.textBorderWidth);null!=p&&(t.lineWidth=p);var f=et(e.getShallow("textBorderType"),n.textBorderType);null!=f&&(t.lineDash=f);var d=et(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset);null!=d&&(t.lineDashOffset=d),r||null!=c||s||(c=i&&i.defaultOpacity),null!=c&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var g=0;g<Wu.length;g++){var y=Wu[g];null!=(m=et(e.getShallow(y),n[y]))&&(t[y]=m)}for(g=0;g<Gu.length;g++){y=Gu[g];null!=(m=e.getShallow(y))&&(t[y]=m)}if(null==t.verticalAlign){var v=e.getShallow("baseline");null!=v&&(t.verticalAlign=v)}if(!a||!i.disableBox){for(g=0;g<Uu.length;g++){var m;y=Uu[g];null!=(m=e.getShallow(y))&&(t[y]=m)}var _=e.getShallow("borderType");null!=_&&(t.borderDash=_),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var Yu=Zr();var qu,Zu,ju=["textStyle","color"],Ku=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],$u=new Ka,Qu=function(){function t(){}return t.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(ju):null)},t.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=this.ecModel,n=e&&e.getModel("textStyle"),at([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e,n},t.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<Ku.length;n++)e[Ku[n]]=this.getShallow(Ku[n]);return $u.useStyle(e),$u.update(),$u.getBoundingRect()},t}(),Ju=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],th=ho(Ju),eh=function(){function t(){}return t.prototype.getLineStyle=function(t){return th(this,t)},t}(),nh=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],ih=ho(nh),rh=function(){function t(){}return t.prototype.getItemStyle=function(t,e){return ih(this,t,e)},t}(),oh=function(){function t(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}return t.prototype.init=function(t,e,n){for(var i=[],r=3;r<arguments.length;r++)i[r-3]=arguments[r]},t.prototype.mergeOption=function(t,e){C(this.option,t,!0)},t.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},t.prototype.getShallow=function(t,e){var n=this.option,i=null==n?n:n[t];if(null==i&&!e){var r=this.parentModel;r&&(i=r.getShallow(t))}return i},t.prototype.getModel=function(e,n){var i=null!=e,r=i?this.parsePath(e):null;return new t(i?this._doGet(r):this.option,n=n||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(r)),this.ecModel)},t.prototype.isEmpty=function(){return null==this.option},t.prototype.restoreData=function(){},t.prototype.clone=function(){return new(0,this.constructor)(T(this.option))},t.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},t.prototype.resolveParentPath=function(t){return t},t.prototype.isAnimationEnabled=function(){if(!r.node&&this.option){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},t.prototype._doGet=function(t,e){var n=this.option;if(!t)return n;for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);return null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel)),n},t}();ro(oh),qu=oh,Zu=["__\0is_clz",ao++].join("_"),qu.prototype[Zu]=!0,qu.isInstance=function(t){return!(!t||!t[Zu])},P(oh,eh),P(oh,rh),P(oh,po),P(oh,Qu);var ah=Math.round(10*Math.random());function sh(t){return[t||"",ah++].join("_")}var lh="ZH",uh="EN",hh=uh,ch={},ph={},fh=r.domSupported&&(document.documentElement.lang||navigator.language||navigator.browserLanguage).toUpperCase().indexOf(lh)>-1?lh:hh;function dh(t,e){t=t.toUpperCase(),ph[t]=new oh(e),ch[t]=e}dh(uh,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),dh(lh,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var gh=1e3,yh=6e4,vh=36e5,mh=864e5,_h=31536e6,xh={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},wh="{yyyy}-{MM}-{dd}",bh={year:"{yyyy}",month:"{yyyy}-{MM}",day:wh,hour:wh+" "+xh.hour,minute:wh+" "+xh.minute,second:wh+" "+xh.second,millisecond:xh.none},Sh=["year","month","day","hour","minute","second","millisecond"],Mh=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function Th(t,e){return"0000".substr(0,e-(t+="").length)+t}function Ch(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function Dh(t){return t===Ch(t)}function Ih(t,e,n,i){var r=Mr(t),o=r[Lh(n)](),a=r[Ph(n)]()+1,s=Math.floor((a-1)/3)+1,l=r[Oh(n)](),u=r["get"+(n?"UTC":"")+"Day"](),h=r[Rh(n)](),c=(h-1)%12+1,p=r[Nh(n)](),f=r[Eh(n)](),d=r[zh(n)](),g=i instanceof oh?i:function(t){return ph[t]}(i||fh)||ph[hh],y=g.getModel("time"),v=y.get("month"),m=y.get("monthAbbr"),_=y.get("dayOfWeek"),x=y.get("dayOfWeekAbbr");return(e||"").replace(/{yyyy}/g,o+"").replace(/{yy}/g,Th(o%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,v[a-1]).replace(/{MMM}/g,m[a-1]).replace(/{MM}/g,Th(a,2)).replace(/{M}/g,a+"").replace(/{dd}/g,Th(l,2)).replace(/{d}/g,l+"").replace(/{eeee}/g,_[u]).replace(/{ee}/g,x[u]).replace(/{e}/g,u+"").replace(/{HH}/g,Th(h,2)).replace(/{H}/g,h+"").replace(/{hh}/g,Th(c+"",2)).replace(/{h}/g,c+"").replace(/{mm}/g,Th(p,2)).replace(/{m}/g,p+"").replace(/{ss}/g,Th(f,2)).replace(/{s}/g,f+"").replace(/{SSS}/g,Th(d,3)).replace(/{S}/g,d+"")}function kh(t,e){var n=Mr(t),i=n[Ph(e)]()+1,r=n[Oh(e)](),o=n[Rh(e)](),a=n[Nh(e)](),s=n[Eh(e)](),l=0===n[zh(e)](),u=l&&0===s,h=u&&0===a,c=h&&0===o,p=c&&1===r;return p&&1===i?"year":p?"month":c?"day":h?"hour":u?"minute":l?"second":"millisecond"}function Ah(t,e,n){var i=X(t)?Mr(t):t;switch(e=e||kh(t,n)){case"year":return i[Lh(n)]();case"half-year":return i[Ph(n)]()>=6?1:0;case"quarter":return Math.floor((i[Ph(n)]()+1)/4);case"month":return i[Ph(n)]();case"day":return i[Oh(n)]();case"half-day":return i[Rh(n)]()/24;case"hour":return i[Rh(n)]();case"minute":return i[Nh(n)]();case"second":return i[Eh(n)]();case"millisecond":return i[zh(n)]()}}function Lh(t){return t?"getUTCFullYear":"getFullYear"}function Ph(t){return t?"getUTCMonth":"getMonth"}function Oh(t){return t?"getUTCDate":"getDate"}function Rh(t){return t?"getUTCHours":"getHours"}function Nh(t){return t?"getUTCMinutes":"getMinutes"}function Eh(t){return t?"getUTCSeconds":"getSeconds"}function zh(t){return t?"getUTCMilliseconds":"getMilliseconds"}function Bh(t){return t?"setUTCFullYear":"setFullYear"}function Fh(t){return t?"setUTCMonth":"setMonth"}function Vh(t){return t?"setUTCDate":"setDate"}function Hh(t){return t?"setUTCHours":"setHours"}function Wh(t){return t?"setUTCMinutes":"setMinutes"}function Gh(t){return t?"setUTCSeconds":"setSeconds"}function Uh(t){return t?"setUTCMilliseconds":"setMilliseconds"}function Xh(t){if(!kr(t))return G(t)?t:"-";var e=(t+"").split(".");return e[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(e.length>1?"."+e[1]:"")}var Yh=rt,qh=["a","b","c","d","e","f","g"],Zh=function(t,e){return"{"+t+(null==e?"":e)+"}"};function jh(t,e,n){H(e)||(e=[e]);var i=e.length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=qh[o];t=t.replace(Zh(a),Zh(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(Zh(qh[l],s),n?$t(u):u)}return t}function Kh(t,e){return e=e||"transparent",G(t)?t:Y(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}var $h=R,Qh=["left","right","top","bottom","width","height"],Jh=[["width","left","right"],["height","top","bottom"]];function tc(t,e,n,i,r){var o=0,a=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild((function(l,u){var h,c,p=l.getBoundingRect(),f=e.childAt(u+1),d=f&&f.getBoundingRect();if("horizontal"===t){var g=p.width+(d?-d.x+p.x:0);(h=o+g)>i||l.newline?(o=0,h=g,a+=s+n,s=p.height):s=Math.max(s,p.height)}else{var y=p.height+(d?-d.y+p.y:0);(c=a+y)>r||l.newline?(o+=s+n,a=0,c=y,s=p.width):s=Math.max(s,p.width)}l.newline||(l.x=o,l.y=a,l.markRedraw(),"horizontal"===t?o=h+n:a=c+n)}))}V(tc,"vertical"),V(tc,"horizontal");function ec(t,e,n){n=Yh(n||0);var i=e.width,r=e.height,o=dr(t.left,i),a=dr(t.top,r),s=dr(t.right,i),l=dr(t.bottom,r),u=dr(t.width,i),h=dr(t.height,r),c=n[2]+n[0],p=n[1]+n[3],f=t.aspect;switch(isNaN(u)&&(u=i-s-p-o),isNaN(h)&&(h=r-l-c-a),null!=f&&(isNaN(u)&&isNaN(h)&&(f>i/r?u=.8*i:h=.8*r),isNaN(u)&&(u=f*h),isNaN(h)&&(h=u/f)),isNaN(o)&&(o=i-s-u-p),isNaN(a)&&(a=r-l-h-c),t.left||t.right){case"center":o=i/2-u/2-n[3];break;case"right":o=i-u-p}switch(t.top||t.bottom){case"middle":case"center":a=r/2-h/2-n[0];break;case"bottom":a=r-h-c}o=o||0,a=a||0,isNaN(u)&&(u=i-p-o-(s||0)),isNaN(h)&&(h=r-c-a-(l||0));var d=new Ce(o+n[3],a+n[0],u,h);return d.margin=n,d}function nc(t){var e=t.layoutMode||t.constructor.layoutMode;return Y(e)?e:e?{type:e}:null}function ic(t,e,n){var i=n&&n.ignoreSize;!H(i)&&(i=[i,i]);var r=a(Jh[0],0),o=a(Jh[1],1);function a(n,r){var o={},a=0,u={},h=0;if($h(n,(function(e){u[e]=t[e]})),$h(n,(function(t){s(e,t)&&(o[t]=u[t]=e[t]),l(o,t)&&a++,l(u,t)&&h++})),i[r])return l(e,n[1])?u[n[2]]=null:l(e,n[2])&&(u[n[1]]=null),u;if(2!==h&&a){if(a>=2)return o;for(var c=0;c<n.length;c++){var p=n[c];if(!s(o,p)&&s(t,p)){o[p]=t[p];break}}return o}return u}function s(t,e){return t.hasOwnProperty(e)}function l(t,e){return null!=t[e]&&"auto"!==t[e]}function u(t,e,n){$h(t,(function(t){e[t]=n[t]}))}u(Jh[0],t,r),u(Jh[1],t,o)}function rc(t){return function(t,e){return e&&t&&$h(Qh,(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t}({},t)}var oc=Zr(),ac=function(t){function e(e,n,i){var r=t.call(this,e,n,i)||this;return r.uid=sh("ec_cpt_model"),r}return n(e,t),e.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},e.prototype.mergeDefaultAndTheme=function(t,e){var n=nc(this),i=n?rc(t):{};C(t,e.getTheme().get(this.mainType)),C(t,this.getDefaultOption()),n&&ic(t,i,n)},e.prototype.mergeOption=function(t,e){C(this.option,t,!0);var n=nc(this);n&&ic(this.option,t,n)},e.prototype.optionUpdated=function(t,e){},e.prototype.getDefaultOption=function(){var t=this.constructor;if(!function(t){return!(!t||!t[no])}(t))return t.defaultOption;var e=oc(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;a>=0;a--)o=C(o,n[a],!0);e.defaultOption=o}return e.defaultOption},e.prototype.getReferringComponents=function(t,e){var n=t+"Index",i=t+"Id";return Jr(this.ecModel,t,{index:this.get(n,!0),id:this.get(i,!0)},e)},e.prototype.getBoxLayoutParams=function(){var t=this;return{left:t.get("left"),top:t.get("top"),right:t.get("right"),bottom:t.get("bottom"),width:t.get("width"),height:t.get("height")}},e.prototype.getZLevelKey=function(){return""},e.prototype.setZLevel=function(t){this.option.zlevel=t},e.protoInitialize=function(){var t=e.prototype;t.type="component",t.id="",t.name="",t.mainType="",t.subType="",t.componentIndex=0}(),e}(oh);oo(ac,oh),uo(ac),function(t){var e={};t.registerSubTypeDefaulter=function(t,n){var i=io(t);e[i.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var o=io(n).main;t.hasSubTypes(n)&&e[o]&&(r=e[o](i))}return r}}(ac),function(t,e){function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}t.topologicalTravel=function(t,i,r,o){if(t.length){var a=function(t){var i={},r=[];return R(t,(function(o){var a=n(i,o),s=function(t,e){var n=[];return R(t,(function(t){A(e,t)>=0&&n.push(t)})),n}(a.originalDeps=e(o),t);a.entryCount=s.length,0===a.entryCount&&r.push(o),R(s,(function(t){A(a.predecessor,t)<0&&a.predecessor.push(t);var e=n(i,t);A(e.successor,t)<0&&e.successor.push(o)}))})),{graph:i,noEntryList:r}}(i),s=a.graph,l=a.noEntryList,u={};for(R(t,(function(t){u[t]=!0}));l.length;){var h=l.pop(),c=s[h],p=!!u[h];p&&(r.call(o,h,c.originalDeps.slice()),delete u[h]),R(c.successor,p?d:f)}R(u,(function(){var t="";throw new Error(t)}))}function f(t){s[t].entryCount--,0===s[t].entryCount&&l.push(t)}function d(t){u[t]=!0,f(t)}}}(ac,(function(t){var e=[];R(ac.getClassesByMainType(t),(function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])})),e=N(e,(function(t){return io(t).main})),"dataset"!==t&&A(e,"dataset")<=0&&e.unshift("dataset");return e}));var sc="";"undefined"!=typeof navigator&&(sc=navigator.platform||"");var lc="rgba(0, 0, 0, 0.2)",uc={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:lc,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:lc,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:lc,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:lc,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:lc,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:lc,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:sc.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},hc=ft(["tooltip","label","itemName","itemId","itemGroupId","seriesName"]),cc="original",pc="arrayRows",fc="objectRows",dc="keyedColumns",gc="typedArray",yc="unknown",vc="column",mc="row",_c=1,xc=2,wc=3,bc=Zr();function Sc(t,e,n){var i={},r=Tc(e);if(!r||!t)return i;var o,a,s=[],l=[],u=e.ecModel,h=bc(u).datasetMap,c=r.uid+"_"+n.seriesLayoutBy;R(t=t.slice(),(function(e,n){var r=Y(e)?e:t[n]={name:e};"ordinal"===r.type&&null==o&&(o=n,a=d(r)),i[r.name]=[]}));var p=h.get(c)||h.set(c,{categoryWayDim:a,valueWayDim:0});function f(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function d(t){var e=t.dimsDef;return e?e.length:1}return R(t,(function(t,e){var n=t.name,r=d(t);if(null==o){var a=p.valueWayDim;f(i[n],a,r),f(l,a,r),p.valueWayDim+=r}else if(o===e)f(i[n],0,r),f(s,0,r);else{a=p.categoryWayDim;f(i[n],a,r),f(l,a,r),p.categoryWayDim+=r}})),s.length&&(i.itemName=s),l.length&&(i.seriesName=l),i}function Mc(t,e,n){var i={};if(!Tc(t))return i;var r,o=e.sourceFormat,a=e.dimensionsDefine;o!==fc&&o!==dc||R(a,(function(t,e){"name"===(Y(t)?t.name:t)&&(r=e)}));var s=function(){for(var t={},i={},s=[],l=0,u=Math.min(5,n);l<u;l++){var h=Dc(e.data,o,e.seriesLayoutBy,a,e.startIndex,l);s.push(h);var c=h===wc;if(c&&null==t.v&&l!==r&&(t.v=l),(null==t.n||t.n===t.v||!c&&s[t.n]===wc)&&(t.n=l),p(t)&&s[t.n]!==wc)return t;c||(h===xc&&null==i.v&&l!==r&&(i.v=l),null!=i.n&&i.n!==i.v||(i.n=l))}function p(t){return null!=t.v&&null!=t.n}return p(t)?t:p(i)?i:null}();if(s){i.value=[s.v];var l=null!=r?r:s.n;i.itemName=[l],i.seriesName=[l]}return i}function Tc(t){if(!t.get("data",!0))return Jr(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},Qr).models[0]}function Cc(t,e){return Dc(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function Dc(t,e,n,i,r,o){var a,s,l;if(Z(t))return wc;if(i){var u=i[o];Y(u)?(s=u.name,l=u.type):G(u)&&(s=u)}if(null!=l)return"ordinal"===l?_c:wc;if(e===pc){var h=t;if(n===mc){for(var c=h[o],p=0;p<(c||[]).length&&p<5;p++)if(null!=(a=m(c[r+p])))return a}else for(p=0;p<h.length&&p<5;p++){var f=h[r+p];if(f&&null!=(a=m(f[o])))return a}}else if(e===fc){var d=t;if(!s)return wc;for(p=0;p<d.length&&p<5;p++){if((y=d[p])&&null!=(a=m(y[s])))return a}}else if(e===dc){if(!s)return wc;if(!(c=t[s])||Z(c))return wc;for(p=0;p<c.length&&p<5;p++)if(null!=(a=m(c[p])))return a}else if(e===cc){var g=t;for(p=0;p<g.length&&p<5;p++){var y,v=Fr(y=g[p]);if(!H(v))return wc;if(null!=(a=m(v[o])))return a}}function m(t){var e=G(t);return null!=t&&isFinite(t)&&""!==t?e?xc:wc:e&&"-"!==t?_c:void 0}return wc}var Ic=ft();var kc,Ac,Lc,Pc=Zr(),Oc=Zr(),Rc=function(){function t(){}return t.prototype.getColorFromPalette=function(t,e,n){var i=Er(this.get("color",!0)),r=this.get("colorLayer",!0);return Ec(this,Pc,i,r,t,e,n)},t.prototype.clearColorPalette=function(){!function(t,e){e(t).paletteIdx=0,e(t).paletteNameMap={}}(this,Pc)},t}();function Nc(t,e,n,i){var r=Er(t.get(["aria","decal","decals"]));return Ec(t,Oc,r,null,e,n,i)}function Ec(t,e,n,i,r,o,a){var s=e(o=o||t),l=s.paletteIdx||0,u=s.paletteNameMap=s.paletteNameMap||{};if(u.hasOwnProperty(r))return u[r];var h=null!=a&&i?function(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}(i,a):n;if((h=h||n)&&h.length){var c=h[l];return r&&(u[r]=c),s.paletteIdx=(l+1)%h.length,c}}var zc="\0_ec_inner";var Bc=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new oh(i),this._locale=new oh(r),this._optionManager=o},e.prototype.setOption=function(t,e,n){var i=Hc(e);this._optionManager.setOption(t,n,i),this._resetOption(null,i)},e.prototype.resetOption=function(t,e){return this._resetOption(t,Hc(e))},e.prototype._resetOption=function(t,e){var n=!1,i=this._optionManager;if(!t||"recreate"===t){var r=i.mountOption("recreate"===t);0,this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(r,e)):Lc(this,r),n=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var o=i.getTimelineOption(this);o&&(n=!0,this._mergeOption(o,e))}if(!t||"recreate"===t||"media"===t){var a=i.getMediaOption(this);a.length&&R(a,(function(t){n=!0,this._mergeOption(t,e)}),this)}return n},e.prototype.mergeOption=function(t){this._mergeOption(t,null)},e.prototype._mergeOption=function(t,e){var n=this.option,i=this._componentsMap,r=this._componentsCount,o=[],a=ft(),s=e&&e.replaceMergeMainTypeMap;bc(this).datasetMap=ft(),R(t,(function(t,e){null!=t&&(ac.hasClass(e)?e&&(o.push(e),a.set(e,!0)):n[e]=null==n[e]?T(t):C(n[e],t,!0))})),s&&s.each((function(t,e){ac.hasClass(e)&&!a.get(e)&&(o.push(e),a.set(e,!0))})),ac.topologicalTravel(o,ac.getAllClassMainTypes(),(function(e){var o=function(t,e,n){var i=Ic.get(e);if(!i)return n;var r=i(t);return r?n.concat(r):n}(this,e,Er(t[e])),a=i.get(e),l=a?s&&s.get(e)?"replaceMerge":"normalMerge":"replaceAll",u=Hr(a,o,l);(function(t,e,n){R(t,(function(t){var i=t.newOption;Y(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=function(t,e,n,i){return e.type?e.type:n?n.subType:i.determineSubType(t,e)}(e,i,t.existing,n))}))})(u,e,ac),n[e]=null,i.set(e,null),r.set(e,0);var h,c=[],p=[],f=0;R(u,(function(t,n){var i=t.existing,r=t.newOption;if(r){var o="series"===e,a=ac.getClass(e,t.keyInfo.subType,!o);if(!a)return;if("tooltip"===e){if(h)return void 0;h=!0}if(i&&i.constructor===a)i.name=t.keyInfo.name,i.mergeOption(r,this),i.optionUpdated(r,!1);else{var s=D({componentIndex:n},t.keyInfo);D(i=new a(r,this,this,s),s),t.brandNew&&(i.__requireNewView=!0),i.init(r,this,this),i.optionUpdated(null,!0)}}else i&&(i.mergeOption({},this),i.optionUpdated({},!1));i?(c.push(i.option),p.push(i),f++):(c.push(void 0),p.push(void 0))}),this),n[e]=c,i.set(e,p),r.set(e,f),"series"===e&&kc(this)}),this),this._seriesIndices||kc(this)},e.prototype.getOption=function(){var t=T(this.option);return R(t,(function(e,n){if(ac.hasClass(n)){for(var i=Er(e),r=i.length,o=!1,a=r-1;a>=0;a--)i[a]&&!Yr(i[a])?o=!0:(i[a]=null,!o&&r--);i.length=r,t[n]=i}})),delete t[zc],t},e.prototype.getTheme=function(){return this._theme},e.prototype.getLocaleModel=function(){return this._locale},e.prototype.setUpdatePayload=function(t){this._payload=t},e.prototype.getUpdatePayload=function(){return this._payload},e.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){var i=n[e||0];if(i)return i;if(null==e)for(var r=0;r<n.length;r++)if(n[r])return n[r]}},e.prototype.queryComponents=function(t){var e=t.mainType;if(!e)return[];var n,i=t.index,r=t.id,o=t.name,a=this._componentsMap.get(e);return a&&a.length?(null!=i?(n=[],R(Er(i),(function(t){a[t]&&n.push(a[t])}))):n=null!=r?Fc("id",r,a):null!=o?Fc("name",o,a):z(a,(function(t){return!!t})),Vc(n,t)):[]},e.prototype.findComponents=function(t){var e,n,i,r,o,a=t.query,s=t.mainType,l=(n=s+"Index",i=s+"Id",r=s+"Name",!(e=a)||null==e[n]&&null==e[i]&&null==e[r]?null:{mainType:s,index:e[n],id:e[i],name:e[r]}),u=l?this.queryComponents(l):z(this._componentsMap.get(s),(function(t){return!!t}));return o=Vc(u,t),t.filter?z(o,t.filter):o},e.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(W(t)){var r=e,o=t;i.each((function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}}))}else for(var a=G(t)?i.get(t):Y(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},e.prototype.getSeriesByName=function(t){var e=Ur(t,null);return z(this._componentsMap.get("series"),(function(t){return!!t&&null!=e&&t.name===e}))},e.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},e.prototype.getSeriesByType=function(t){return z(this._componentsMap.get("series"),(function(e){return!!e&&e.subType===t}))},e.prototype.getSeries=function(){return z(this._componentsMap.get("series"),(function(t){return!!t}))},e.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},e.prototype.eachSeries=function(t,e){Ac(this),R(this._seriesIndices,(function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)}),this)},e.prototype.eachRawSeries=function(t,e){R(this._componentsMap.get("series"),(function(n){n&&t.call(e,n,n.componentIndex)}))},e.prototype.eachSeriesByType=function(t,e,n){Ac(this),R(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)}),this)},e.prototype.eachRawSeriesByType=function(t,e,n){return R(this.getSeriesByType(t),e,n)},e.prototype.isSeriesFiltered=function(t){return Ac(this),null==this._seriesIndicesMap.get(t.componentIndex)},e.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},e.prototype.filterSeries=function(t,e){Ac(this);var n=[];R(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];t.call(e,r,i)&&n.push(i)}),this),this._seriesIndices=n,this._seriesIndicesMap=ft(n)},e.prototype.restoreData=function(t){kc(this);var e=this._componentsMap,n=[];e.each((function(t,e){ac.hasClass(e)&&n.push(e)})),ac.topologicalTravel(n,ac.getAllClassMainTypes(),(function(n){R(e.get(n),(function(e){!e||"series"===n&&function(t,e){if(e){var n=e.seriesIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}(e,t)||e.restoreData()}))}))},e.internalField=(kc=function(t){var e=t._seriesIndices=[];R(t._componentsMap.get("series"),(function(t){t&&e.push(t.componentIndex)})),t._seriesIndicesMap=ft(e)},Ac=function(t){},void(Lc=function(t,e){t.option={},t.option[zc]=1,t._componentsMap=ft({series:[]}),t._componentsCount=ft();var n=e.aria;Y(n)&&null==n.enabled&&(n.enabled=!0),function(t,e){var n=t.color&&!t.colorLayer;R(e,(function(e,i){"colorLayer"===i&&n||ac.hasClass(i)||("object"==typeof e?t[i]=t[i]?C(t[i],e,!1):T(e):null==t[i]&&(t[i]=e))}))}(e,t._theme.option),C(e,uc,!1),t._mergeOption(e,null)})),e}(oh);function Fc(t,e,n){if(H(e)){var i=ft();return R(e,(function(t){null!=t&&(null!=Ur(t,null)&&i.set(t,!0))})),z(n,(function(e){return e&&i.get(e[t])}))}var r=Ur(e,null);return z(n,(function(e){return e&&null!=r&&e[t]===r}))}function Vc(t,e){return e.hasOwnProperty("subType")?z(t,(function(t){return t&&t.subType===e.subType})):t}function Hc(t){var e=ft();return t&&R(Er(t.replaceMerge),(function(t){e.set(t,!0)})),{replaceMergeMainTypeMap:e}}P(Bc,Rc);var Wc=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],Gc=function(t){R(Wc,(function(e){this[e]=F(t[e],t)}),this)},Uc={},Xc=function(){function t(){this._coordinateSystems=[]}return t.prototype.create=function(t,e){var n=[];R(Uc,(function(i,r){var o=i.create(t,e);n=n.concat(o||[])})),this._coordinateSystems=n},t.prototype.update=function(t,e){R(this._coordinateSystems,(function(n){n.update&&n.update(t,e)}))},t.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},t.register=function(t,e){Uc[t]=e},t.get=function(t){return Uc[t]},t}(),Yc=/^(min|max)?(.+)$/,qc=function(){function t(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return t.prototype.setOption=function(t,e,n){t&&(R(Er(t.series),(function(t){t&&t.data&&Z(t.data)&&lt(t.data)})),R(Er(t.dataset),(function(t){t&&t.source&&Z(t.source)&&lt(t.source)}))),t=T(t);var i=this._optionBackup,r=function(t,e,n){var i,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&H(u)&&R(u,(function(t){t&&t.option&&(t.query?o.push(t):i||(i=t))}));function p(t){R(e,(function(e){e(t,n)}))}return p(r),R(l,(function(t){return p(t)})),R(o,(function(t){return p(t.option)})),{baseOption:r,timelineOptions:l||[],mediaDefault:i,mediaList:o}}(t,e,!i);this._newBaseOption=r.baseOption,i?(r.timelineOptions.length&&(i.timelineOptions=r.timelineOptions),r.mediaList.length&&(i.mediaList=r.mediaList),r.mediaDefault&&(i.mediaDefault=r.mediaDefault)):this._optionBackup=r},t.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],T(t?e.baseOption:this._newBaseOption)},t.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=T(n[i.getCurrentIndex()]))}return e},t.prototype.getMediaOption=function(t){var e,n,i=this._api.getWidth(),r=this._api.getHeight(),o=this._mediaList,a=this._mediaDefault,s=[],l=[];if(!o.length&&!a)return l;for(var u=0,h=o.length;u<h;u++)Zc(o[u].query,i,r)&&s.push(u);return!s.length&&a&&(s=[-1]),s.length&&(e=s,n=this._currentMediaIndices,e.join(",")!==n.join(","))&&(l=N(s,(function(t){return T(-1===t?a.option:o[t].option)}))),this._currentMediaIndices=s,l},t}();function Zc(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return R(t,(function(t,e){var n=e.match(Yc);if(n&&n[1]&&n[2]){var o=n[1],a=n[2].toLowerCase();(function(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e})(i[a],t,o)||(r=!1)}})),r}var jc=R,Kc=Y,$c=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function Qc(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=$c.length;n<i;n++){var r=$c[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?C(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?C(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function Jc(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,r=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,I(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r,r.focus&&(t.emphasis.focus=r.focus),r.blurScope&&(t.emphasis.blurScope=r.blurScope))}}function tp(t){Jc(t,"itemStyle"),Jc(t,"lineStyle"),Jc(t,"areaStyle"),Jc(t,"label"),Jc(t,"labelLine"),Jc(t,"upperLabel"),Jc(t,"edgeLabel")}function ep(t,e){var n=Kc(t)&&t[e],i=Kc(n)&&n.textStyle;if(i){0;for(var r=0,o=Br.length;r<o;r++){var a=Br[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}}function np(t){t&&(tp(t),ep(t,"label"),t.emphasis&&ep(t.emphasis,"label"))}function ip(t){return H(t)?t:t?[t]:[]}function rp(t){return(H(t)?t[0]:t)||{}}function op(t,e){jc(ip(t.series),(function(t){Kc(t)&&function(t){if(Kc(t)){Qc(t),tp(t),ep(t,"label"),ep(t,"upperLabel"),ep(t,"edgeLabel"),t.emphasis&&(ep(t.emphasis,"label"),ep(t.emphasis,"upperLabel"),ep(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(Qc(e),np(e));var n=t.markLine;n&&(Qc(n),np(n));var i=t.markArea;i&&np(i);var r=t.data;if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!Z(o))for(var a=0;a<o.length;a++)np(o[a]);R(t.categories,(function(t){tp(t)}))}if(r&&!Z(r))for(a=0;a<r.length;a++)np(r[a]);if((e=t.markPoint)&&e.data){var s=e.data;for(a=0;a<s.length;a++)np(s[a])}if((n=t.markLine)&&n.data){var l=n.data;for(a=0;a<l.length;a++)H(l[a])?(np(l[a][0]),np(l[a][1])):np(l[a])}"gauge"===t.type?(ep(t,"axisLabel"),ep(t,"title"),ep(t,"detail")):"treemap"===t.type?(Jc(t.breadcrumb,"itemStyle"),R(t.levels,(function(t){tp(t)}))):"tree"===t.type&&tp(t.leaves)}}(t)}));var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),jc(n,(function(e){jc(ip(t[e]),(function(t){t&&(ep(t,"axisLabel"),ep(t.axisPointer,"label"))}))})),jc(ip(t.parallel),(function(t){var e=t&&t.parallelAxisDefault;ep(e,"axisLabel"),ep(e&&e.axisPointer,"label")})),jc(ip(t.calendar),(function(t){Jc(t,"itemStyle"),ep(t,"dayLabel"),ep(t,"monthLabel"),ep(t,"yearLabel")})),jc(ip(t.radar),(function(t){ep(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)})),jc(ip(t.geo),(function(t){Kc(t)&&(np(t),jc(ip(t.regions),(function(t){np(t)})))})),jc(ip(t.timeline),(function(t){np(t),Jc(t,"label"),Jc(t,"itemStyle"),Jc(t,"controlStyle",!0);var e=t.data;H(e)&&R(e,(function(t){Y(t)&&(Jc(t,"label"),Jc(t,"itemStyle"))}))})),jc(ip(t.toolbox),(function(t){Jc(t,"iconStyle"),jc(t.feature,(function(t){Jc(t,"iconStyle")}))})),ep(rp(t.axisPointer),"label"),ep(rp(t.tooltip).axisPointer,"label")}function ap(t){t&&R(sp,(function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])}))}var sp=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],lp=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],up=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function hp(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<up.length;n++){var i=up[n][1],r=up[n][0];null!=e[i]&&(e[r]=e[i])}}function cp(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function pp(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function fp(t,e){if(t)for(var n=0;n<t.length;n++)e(t[n]),t[n]&&fp(t[n].children,e)}function dp(t,e){op(t,e),t.series=Er(t.series),R(t.series,(function(t){if(Y(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e){if(null!=t.clockWise&&(t.clockwise=t.clockWise),cp(t.label),(r=t.data)&&!Z(r))for(var n=0;n<r.length;n++)cp(r[n]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},(t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset))}else if("gauge"===e){var i=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");null!=i&&function(t,e,n,i){for(var r,o=e.split(","),a=t,s=0;s<o.length-1;s++)null==a[r=o[s]]&&(a[r]={}),a=a[r];(i||null==a[o[s]])&&(a[o[s]]=n)}(t,"itemStyle.color",i)}else if("bar"===e){var r;if(hp(t),hp(t.backgroundStyle),hp(t.emphasis),(r=t.data)&&!Z(r))for(n=0;n<r.length;n++)"object"==typeof r[n]&&(hp(r[n]),hp(r[n]&&r[n].emphasis))}else if("sunburst"===e){var o=t.highlightPolicy;o&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=o)),pp(t),fp(t.data,pp)}else"graph"===e||"sankey"===e?function(t){t&&null!=t.focusNodeAdjacency&&(t.emphasis=t.emphasis||{},null==t.emphasis.focus&&(t.emphasis.focus="adjacency"))}(t):"map"===e&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation&&I(t,t.mapLocation));null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation)),ap(t)}})),t.dataRange&&(t.visualMap=t.dataRange),R(lp,(function(e){var n=t[e];n&&(H(n)||(n=[n]),R(n,(function(t){ap(t)})))}))}function gp(t){R(t,(function(e,n){var i=[],r=[NaN,NaN],o=[e.stackResultDimension,e.stackedOverDimension],a=e.data,s=e.isStackedByIndex,l=e.seriesModel.get("stackStrategy")||"samesign";a.modify(o,(function(o,u,h){var c,p,f=a.get(e.stackedDimension,h);if(isNaN(f))return r;s?p=a.getRawIndex(h):c=a.get(e.stackedByDimension,h);for(var d=NaN,g=n-1;g>=0;g--){var y=t[g];if(s||(p=y.data.rawIndexOf(y.stackedByDimension,c)),p>=0){var v=y.data.getByRawIndex(y.stackResultDimension,p);if("all"===l||"positive"===l&&v>0||"negative"===l&&v<0||"samesign"===l&&f>=0&&v>0||"samesign"===l&&f<=0&&v<0){f=xr(f,v),d=v;break}}}return i[0]=f,i[1]=d,i}))}))}var yp,vp,mp,_p,xp,wp=function(t){this.data=t.data||(t.sourceFormat===dc?{}:[]),this.sourceFormat=t.sourceFormat||yc,this.seriesLayoutBy=t.seriesLayoutBy||vc,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&Cc(this,n)===_c&&(i.type="ordinal")}};function bp(t){return t instanceof wp}function Sp(t,e,n){n=n||Tp(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:Cp(r),startIndex:a,dimensionsDetectedCount:o};if(e===pc){var s=t;"auto"===i||null==i?Dp((function(t){null!=t&&"-"!==t&&(G(t)?null==a&&(a=1):a=0)}),n,s,10):a=X(i)?i:i?1:0,r||1!==a||(r=[],Dp((function(t,e){r[e]=null!=t?t+"":""}),n,s,1/0)),o=r?r.length:n===mc?s.length:s[0]?s[0].length:null}else if(e===fc)r||(r=function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e)return B(e)}(t));else if(e===dc)r||(r=[],R(t,(function(t,e){r.push(e)})));else if(e===cc){var l=Fr(t[0]);o=H(l)&&l.length||1}return{startIndex:a,dimensionsDefine:Cp(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new wp({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:T(e)})}function Mp(t){return new wp({data:t,sourceFormat:Z(t)?gc:cc})}function Tp(t){var e=yc;if(Z(t))e=gc;else if(H(t)){0===t.length&&(e=pc);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(H(r)){e=pc;break}if(Y(r)){e=fc;break}}}}else if(Y(t))for(var o in t)if(vt(t,o)&&O(t[o])){e=dc;break}return e}function Cp(t){if(t){var e=ft();return N(t,(function(t,n){var i={name:(t=Y(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};if(null==i.name)return i;i.name+="",null==i.displayName&&(i.displayName=i.name);var r=e.get(i.name);return r?i.name+="-"+r.count++:e.set(i.name,{count:1}),i}))}}function Dp(t,e,n,i){if(e===mc)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else{var o=n[0]||[];for(r=0;r<o.length&&r<i;r++)t(o[r],r)}}function Ip(t){var e=t.sourceFormat;return e===fc||e===dc}var kp=function(){function t(t,e){var n=bp(t)?t:Mp(t);this._source=n;var i=this._data=n.data;n.sourceFormat===gc&&(this._offset=0,this._dimSize=e,this._data=i),xp(this,i,n)}return t.prototype.getSource=function(){return this._source},t.prototype.count=function(){return 0},t.prototype.getItem=function(t,e){},t.prototype.appendData=function(t){},t.prototype.clean=function(){},t.protoInitialize=function(){var e=t.prototype;e.pure=!1,e.persistent=!0}(),t.internalField=function(){var t;xp=function(t,r,o){var a=o.sourceFormat,s=o.seriesLayoutBy,l=o.startIndex,u=o.dimensionsDefine,h=_p[Fp(a,s)];if(D(t,h),a===gc)t.getItem=e,t.count=i,t.fillStorage=n;else{var c=Pp(a,s);t.getItem=F(c,null,r,l,u);var p=Np(a,s);t.count=F(p,null,r,l,u)}};var e=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},n=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var f=r[p*o+a];c[t+p]=f,f<l&&(l=f),f>u&&(u=f)}s[0]=l,s[1]=u}},i=function(){return this._data?this._data.length/this._dimSize:0};function r(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}(t={})[pc+"_"+vc]={pure:!0,appendData:r},t[pc+"_"+mc]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[fc]={pure:!0,appendData:r},t[dc]={pure:!0,appendData:function(t){var e=this._data;R(t,(function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])}))}},t[cc]={appendData:r},t[gc]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},_p=t}(),t}(),Ap=function(t,e,n,i){return t[i]},Lp=((yp={})[pc+"_"+vc]=function(t,e,n,i){return t[i+e]},yp[pc+"_"+mc]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},yp[fc]=Ap,yp[dc]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=n[a].name;0;var l=t[s];o[a]=l?l[i]:null}return o},yp[cc]=Ap,yp);function Pp(t,e){var n=Lp[Fp(t,e)];return n}var Op=function(t,e,n){return t.length},Rp=((vp={})[pc+"_"+vc]=function(t,e,n){return Math.max(0,t.length-e)},vp[pc+"_"+mc]=function(t,e,n){var i=t[0];return i?Math.max(0,i.length-e):0},vp[fc]=Op,vp[dc]=function(t,e,n){var i=n[0].name;var r=t[i];return r?r.length:0},vp[cc]=Op,vp);function Np(t,e){var n=Rp[Fp(t,e)];return n}var Ep=function(t,e,n){return t[e]},zp=((mp={})[pc]=Ep,mp[fc]=function(t,e,n){return t[n]},mp[dc]=Ep,mp[cc]=function(t,e,n){var i=Fr(t);return i instanceof Array?i[e]:i},mp[gc]=Ep,mp);function Bp(t){var e=zp[t];return e}function Fp(t,e){return t===pc?t+"_"+e:t}function Vp(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r=t.getStore(),o=r.getSource().sourceFormat;if(null!=n){var a=t.getDimensionIndex(n),s=r.getDimensionProperty(a);return Bp(o)(i,a,s)}var l=i;return o===cc&&(l=Fr(i)),l}}}var Hp=/\{@(.+?)\}/g,Wp=function(){function t(){}return t.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),l=s&&s[n.getItemVisual(t,"drawType")||"fill"],u=s&&s.stroke,h=this.mainType,c="series"===h,p=n.userOutput&&n.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:l,borderColor:u,dimensionNames:p?p.fullDimensions:null,encode:p?p.encode:null,$vars:["seriesName","name","value"]}},t.prototype.getFormattedLabel=function(t,e,n,i,r,o){e=e||"normal";var a=this.getData(n),s=this.getDataParams(t,n);(o&&(s.value=o.interpolatedValue),null!=i&&H(s.value)&&(s.value=s.value[i]),r)||(r=a.getItemModel(t).get("normal"===e?["label","formatter"]:[e,"label","formatter"]));return W(r)?(s.status=e,s.dimensionIndex=i,r(s)):G(r)?jh(r,s).replace(Hp,(function(e,n){var i=n.length,r=n;"["===r.charAt(0)&&"]"===r.charAt(i-1)&&(r=+r.slice(1,i-1));var s=Vp(a,t,r);if(o&&H(o.interpolatedValue)){var l=a.getDimensionIndex(r);l>=0&&(s=o.interpolatedValue[l])}return null!=s?s+"":""})):void 0},t.prototype.getRawValue=function(t,e){return Vp(this.getData(e),t)},t.prototype.formatTooltip=function(t,e,n){},t}();function Gp(t){return new Up(t)}var Up=function(){function t(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return t.prototype.perform=function(t){var e,n=this._upstream,i=t&&t.skip;if(this._dirty&&n){var r=this.context;r.data=r.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!i&&(e=this._plan(this.context));var o,a=h(this._modBy),s=this._modDataCount||0,l=h(t&&t.modBy),u=t&&t.modDataCount||0;function h(t){return!(t>=1)&&(t=1),t}a===l&&s===u||(e="reset"),(this._dirty||"reset"===e)&&(this._dirty=!1,o=this._doReset(i)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(this._dueEnd=n?n._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var p=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(o||p<f)){var d=this._progress;if(H(d))for(var g=0;g<d.length;g++)this._doProgress(d[g],p,f,l,u);else this._doProgress(d,p,f,l,u)}this._dueIndex=f;var y=null!=this._settedOutputEnd?this._settedOutputEnd:f;0,this._outputDueEnd=y}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},t.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},t.prototype._doProgress=function(t,e,n,i,r){Xp.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:Xp.next},this.context)},t.prototype._doReset=function(t){var e,n;this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),H(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var i=this._downstream;return i&&i.dirty(),n},t.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},t.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},t.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},t.prototype.getUpstream=function(){return this._upstream},t.prototype.getDownstream=function(){return this._downstream},t.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},t}(),Xp=function(){var t,e,n,i,r,o={reset:function(l,u,h,c){e=l,t=u,n=h,i=c,r=Math.ceil(i/n),o.next=n>1&&i>0?s:a}};return o;function a(){return e<t?e++:null}function s(){var o=e%r*n+Math.ceil(e/r),a=e>=t?null:o<i?o:e;return e++,a}}();function Yp(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"!==n||X(t)||null==t||"-"===t||(t=+Mr(t)),null==t||""===t?NaN:+t)}ft({number:function(t){return parseFloat(t)},time:function(t){return+Mr(t)},trim:function(t){return G(t)?at(t):t}});var qp=function(){function t(){}return t.prototype.getRawData=function(){throw new Error("not supported")},t.prototype.getRawDataItem=function(t){throw new Error("not supported")},t.prototype.cloneRawData=function(){},t.prototype.getDimensionInfo=function(t){},t.prototype.cloneAllDimensionInfo=function(){},t.prototype.count=function(){},t.prototype.retrieveValue=function(t,e){},t.prototype.retrieveValueFromItem=function(t,e){},t.prototype.convertValue=function(t,e){return Yp(t,e)},t}();function Zp(t){var e=t.sourceFormat;if(!tf(e)){var n="";0,Or(n)}return t.data}function jp(t){var e=t.sourceFormat,n=t.data;if(!tf(e)){var i="";0,Or(i)}if(e===pc){for(var r=[],o=0,a=n.length;o<a;o++)r.push(n[o].slice());return r}if(e===fc){for(r=[],o=0,a=n.length;o<a;o++)r.push(D({},n[o]));return r}}function Kp(t,e,n){if(null!=n)return X(n)||!isNaN(n)&&!vt(e,n)?t[n]:vt(e,n)?e[n]:void 0}function $p(t){return T(t)}var Qp=ft();function Jp(t,e,n,i){var r="";e.length||Or(r),Y(t)||Or(r);var o=t.type,a=Qp.get(o);a||Or(r);var s=N(e,(function(t){return function(t,e){var n=new qp,i=t.data,r=n.sourceFormat=t.sourceFormat,o=t.startIndex,a="";t.seriesLayoutBy!==vc&&Or(a);var s=[],l={},u=t.dimensionsDefine;if(u)R(u,(function(t,e){var n=t.name,i={index:e,name:n,displayName:t.displayName};if(s.push(i),null!=n){var r="";vt(l,n)&&Or(r),l[n]=i}}));else for(var h=0;h<t.dimensionsDetectedCount;h++)s.push({index:h});var c=Pp(r,vc);e.__isBuiltIn&&(n.getRawDataItem=function(t){return c(i,o,s,t)},n.getRawData=F(Zp,null,t)),n.cloneRawData=F(jp,null,t);var p=Np(r,vc);n.count=F(p,null,i,o,s);var f=Bp(r);n.retrieveValue=function(t,e){var n=c(i,o,s,t);return d(n,e)};var d=n.retrieveValueFromItem=function(t,e){if(null!=t){var n=s[e];return n?f(t,e,n.name):void 0}};return n.getDimensionInfo=F(Kp,null,s,l),n.cloneAllDimensionInfo=F($p,null,s),n}(t,a)})),l=Er(a.transform({upstream:s[0],upstreamList:s,config:T(t.config)}));return N(l,(function(t,n){var i,r="";Y(t)||Or(r),t.data||Or(r),tf(Tp(t.data))||Or(r);var o=e[0];if(o&&0===n&&!t.dimensions){var a=o.startIndex;a&&(t.data=o.data.slice(0,a).concat(t.data)),i={seriesLayoutBy:vc,sourceHeader:a,dimensions:o.metaRawOption.dimensions}}else i={seriesLayoutBy:vc,sourceHeader:0,dimensions:t.dimensions};return Sp(t.data,i,null)}))}function tf(t){return t===pc||t===fc}var ef,nf="undefined",rf=typeof Uint32Array===nf?Array:Uint32Array,of=typeof Uint16Array===nf?Array:Uint16Array,af=typeof Int32Array===nf?Array:Int32Array,sf=typeof Float64Array===nf?Array:Float64Array,lf={float:sf,int:af,ordinal:Array,number:Array,time:sf};function uf(t){return t>65535?rf:of}function hf(t,e,n,i,r){var o=lf[n||"float"];if(r){var a=t[e],s=a&&a.length;if(s!==i){for(var l=new o(i),u=0;u<s;u++)l[u]=a[u];t[e]=l}}else t[e]=new o(i)}var cf=function(){function t(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=ft()}return t.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=ef[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[];Ip(i);this._dimensions=N(e,(function(t){return{type:t.type,property:t.property}})),this._initDataFromProvider(0,t.count())},t.prototype.getProvider=function(){return this._provider},t.prototype.getSource=function(){return this._provider.getSource()},t.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new lf[e||"float"](this._rawCount),this._rawExtent[r]=[1/0,-1/0],r},t.prototype.collectOrdinalMeta=function(t,e){var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length;0===o&&(r[t]=[1/0,-1/0]);for(var s=r[t],l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},t.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},t.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},t.prototype.appendData=function(t){var e=this._provider,n=this.count();e.appendData(t);var i=e.count();return e.persistent||(i+=n),n<i&&this._initDataFromProvider(n,i,!0),[n,i]},t.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++){hf(n,l,(f=i[l]).type,s,!0)}for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var f=i[p],d=ef.arrayRows.call(this,t[c]||u,f.property,c,p);n[p][h]=d;var g=o[p];d<g[0]&&(g[0]=d),d>g[1]&&(g[1]=d)}return this._rawCount=this._count=s,{start:a,end:s}},t.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=N(o,(function(t){return t.property})),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=[1/0,-1/0]),hf(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++){c=i.getItem(p,c);for(var f=0;f<a;f++){var d=r[f],g=this._dimValueGetter(c,l[f],p,f);d[p]=g;var y=s[f];g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},t.prototype.count=function(){return this._count},t.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._chunks[t];return n?n[this.getRawIndex(e)]:NaN},t.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;r=0;for(var o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},t.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._chunks[t];return n?n[e]:NaN},t.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},t.prototype.getMedian=function(t){var e=[];this.each([t],(function(t){isNaN(t)||e.push(t)}));var n=e.sort((function(t,e){return t-e})),i=this.count();return 0===i?0:i%2==1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},t.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=o+1;else{if(!(e[o]>t))return o;r=o-1}}return-1},t.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(!i)return r;null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&h>=0&&a<0)&&(o=c,a=h,s=0),h===a&&(r[s++]=l))}return r.length=s,r},t.prototype.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,i=this._count;if(n===Array){t=new n(i);for(var r=0;r<i;r++)t[r]=e[r]}else t=new n(e.buffer,0,i)}else{t=new(n=uf(this._rawCount))(this.count());for(r=0;r<t.length;r++)t[r]=r}return t},t.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(uf(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a){c=e(u[l][p],h)}else{for(var f=0;f<a;f++)o[f]=u[t[f]][p];o[f]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},t.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=B(t),r=i.length;if(!r)return this;var o=e.count(),a=new(uf(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,p=!1;if(!e._indices){var f=0;if(1===r){for(var d=c[i[0]],g=0;g<n;g++){((_=d[g])>=u&&_<=h||isNaN(_))&&(a[s++]=f),f++}p=!0}else if(2===r){d=c[i[0]];var y=c[i[1]],v=t[i[1]][0],m=t[i[1]][1];for(g=0;g<n;g++){var _=d[g],x=y[g];(_>=u&&_<=h||isNaN(_))&&(x>=v&&x<=m||isNaN(x))&&(a[s++]=f),f++}p=!0}}if(!p)if(1===r)for(g=0;g<o;g++){var w=e.getRawIndex(g);((_=c[i[0]][w])>=u&&_<=h||isNaN(_))&&(a[s++]=w)}else for(g=0;g<o;g++){for(var b=!0,S=(w=e.getRawIndex(g),0);S<r;S++){var M=i[S];((_=c[M][w])<t[M][0]||_>t[M][1])&&(b=!1)}b&&(a[s++]=e.getRawIndex(g))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},t.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},t.prototype.modify=function(t,e){this._updateDims(this,t,e)},t.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=[1/0,-1/0];for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var f=n&&n.apply(null,s);if(null!=f){"object"!=typeof f&&(r[0]=f,f=r);for(u=0;u<f.length;u++){var d=e[u],g=f[u],y=l[d],v=i[d];v&&(v[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},t.prototype.lttbDownSample=function(t,e){var n,i,r,o=this.clone([t],!0),a=o._chunks[t],s=this.count(),l=0,u=Math.floor(1/e),h=this.getRawIndex(0),c=new(uf(this._rawCount))(Math.min(2*(Math.ceil(s/u)+2),s));c[l++]=h;for(var p=1;p<s-1;p+=u){for(var f=Math.min(p+u,s-1),d=Math.min(p+2*u,s),g=(d+f)/2,y=0,v=f;v<d;v++){var m=a[T=this.getRawIndex(v)];isNaN(m)||(y+=m)}y/=d-f;var _=p,x=Math.min(p+u,s),w=p-1,b=a[h];n=-1,r=_;var S=-1,M=0;for(v=_;v<x;v++){var T;m=a[T=this.getRawIndex(v)];isNaN(m)?(M++,S<0&&(S=T)):(i=Math.abs((w-g)*(m-b)-(w-v)*(y-b)))>n&&(n=i,r=T)}M>0&&M<x-_&&(c[l++]=Math.min(S,r),r=Math.max(S,r)),c[l++]=r,h=r}return c[l++]=this.getRawIndex(s-1),o._count=l,o._indices=c,o.getRawIndex=this._getRawIdx,o},t.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=[1/0,-1/0],c=new(uf(this._rawCount))(Math.ceil(u/s)),p=0,f=0;f<u;f+=s){s>u-f&&(s=u-f,a.length=s);for(var d=0;d<s;d++){var g=this.getRawIndex(f+d);a[d]=l[g]}var y=n(a),v=this.getRawIndex(Math.min(f+i(a,y)||0,u-1));l[v]=y,y<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=v}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},t.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},t.prototype.getDataExtent=function(t){var e=this._chunks[t],n=[1/0,-1/0];if(!e)return n;var i,r=this.count();if(!this._indices)return this._rawExtent[t].slice();if(i=this._extent[t])return i.slice();for(var o=(i=n)[0],a=i[1],s=0;s<r;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),l>a&&(a=l)}return i=[o,a],this._extent[t]=i,i},t.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},t.prototype.clone=function(e,n){var i,r,o=new t,a=this._chunks,s=e&&E(e,(function(t,e){return t[e]=!0,t}),{});if(s)for(var l=0;l<a.length;l++)o._chunks[l]=s[l]?(i=a[l],r=void 0,(r=i.constructor)===Array?i.slice():new r(i)):a[l];else o._chunks=a;return this._copyCommonProps(o),n||(o._indices=this._cloneIndices()),o._updateGetRawIdx(),o},t.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=T(this._extent),t._rawExtent=T(this._rawExtent)},t.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var n=this._indices.length;e=new t(n);for(var i=0;i<n;i++)e[i]=this._indices[i]}else e=new t(this._indices);return e}return null},t.prototype._getRawIdxIdentity=function(t){return t},t.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},t.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},t.internalField=function(){function t(t,e,n,i){return Yp(t[i],this._dimensions[i])}ef={arrayRows:t,objectRows:function(t,e,n,i){return Yp(t[e],this._dimensions[i])},keyedColumns:t,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return Yp(r instanceof Array?r[i]:r,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}}}(),t}(),pf=function(){function t(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return t.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},t.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},t.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},t.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},t.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n=this._sourceHost,i=this._getUpstreamSourceManagers(),r=!!i.length;if(df(n)){var o=n,a=void 0,s=void 0,l=void 0;if(r){var u=i[0];u.prepareSource(),a=(l=u.getSource()).data,s=l.sourceFormat,e=[u._getVersionSign()]}else s=Z(a=o.get("data",!0))?gc:cc,e=[];var h=this._getSourceMetaRawOption()||{},c=l&&l.metaRawOption||{},p=et(h.seriesLayoutBy,c.seriesLayoutBy)||null,f=et(h.sourceHeader,c.sourceHeader),d=et(h.dimensions,c.dimensions);t=p!==c.seriesLayoutBy||!!f!=!!c.sourceHeader||d?[Sp(a,{seriesLayoutBy:p,sourceHeader:f,dimensions:d},s)]:[]}else{var g=n;if(r){var y=this._applyTransform(i);t=y.sourceList,e=y.upstreamSignList}else{t=[Sp(g.get("source",!0),this._getSourceMetaRawOption(),null)],e=[]}}this._setLocalSource(t,e)},t.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0);if(null!=r){var o="";1!==t.length&&gf(o)}var a,s=[],l=[];return R(t,(function(t){t.prepareSource();var e=t.getSource(r||0),n="";null==r||e||gf(n),s.push(e),l.push(t._getVersionSign())})),i?e=function(t,e,n){var i=Er(t),r=i.length,o="";r||Or(o);for(var a=0,s=r;a<s;a++)e=Jp(i[a],e),a!==s-1&&(e.length=Math.max(e.length,1));return e}(i,s,n.componentIndex):null!=r&&(e=[(a=s[0],new wp({data:a.data,sourceFormat:a.sourceFormat,seriesLayoutBy:a.seriesLayoutBy,dimensionsDefine:T(a.dimensionsDefine),startIndex:a.startIndex,dimensionsDetectedCount:a.dimensionsDetectedCount}))]),{sourceList:e,upstreamSignList:l}},t.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},t.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return e},t.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},t.prototype._innerGetDataStore=function(t,e,n){var i=this._storeList,r=i[0];r||(r=i[0]={});var o=r[n];if(!o){var a=this._getUpstreamSourceManagers()[0];df(this._sourceHost)&&a?o=a._innerGetDataStore(t,e,n):(o=new cf).initData(new kp(e,t.length),t),r[n]=o}return o},t.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(df(t)){var e=Tc(t);return e?[e.getSourceManager()]:[]}return N(function(t){return t.get("transform",!0)||t.get("fromTransformResult",!0)?Jr(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},Qr).models:[]}(t),(function(t){return t.getSourceManager()}))},t.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;if(df(i))t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var r=i;t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0)}return{seriesLayoutBy:t,sourceHeader:e,dimensions:n}},t}();function ff(t){t.option.transform&&lt(t.option.transform)}function df(t){return"series"===t.mainType}function gf(t){throw new Error(t)}function yf(t,e){return e.type=t,e}function vf(t){var e,n,i,r,o=t.series,a=t.dataIndex,s=t.multipleSeries,l=o.getData(),u=l.mapDimensionsAll("defaultedTooltip"),h=u.length,c=o.getRawValue(a),p=H(c),f=function(t,e){return Kh(t.getData().getItemVisual(e,"style")[t.visualDrawType])}(o,a);if(h>1||p&&!h){var d=function(t,e,n,i,r){var o=e.getData(),a=E(t,(function(t,e,n){var i=o.getDimensionInfo(n);return t||i&&!1!==i.tooltip&&null!=i.displayName}),!1),s=[],l=[],u=[];function h(t,e){var n=o.getDimensionInfo(e);n&&!1!==n.otherDims.tooltip&&(a?u.push(yf("nameValue",{markerType:"subItem",markerColor:r,name:n.displayName,value:t,valueType:n.type})):(s.push(t),l.push(n.type)))}return i.length?R(i,(function(t){h(Vp(o,n,t),t)})):R(t,h),{inlineValues:s,inlineValueTypes:l,blocks:u}}(c,o,a,u,f);e=d.inlineValues,n=d.inlineValueTypes,i=d.blocks,r=d.inlineValues[0]}else if(h){var g=l.getDimensionInfo(u[0]);r=e=Vp(l,a,u[0]),n=g.type}else r=e=p?c[0]:c;var y=Xr(o),v=y&&o.name||"",m=l.getName(a),_=s?v:m;return yf("section",{header:v,noHeader:s||!y,sortParam:r,blocks:[yf("nameValue",{markerType:"item",markerColor:f,name:_,noName:!at(_),value:e,valueType:n})].concat(i||[])})}var mf=Zr();function _f(t,e){return t.getName(e)||t.getId(e)}var xf=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return n(e,t),e.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=Gp({count:bf,reset:Sf}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),(mf(this).sourceManager=new pf(this)).prepareSource();var i=this.getInitialData(t,n);Tf(i,this),this.dataTask.context.data=i,mf(this).dataBeforeProcessed=i,wf(this),this._initSelectedMapFromData(i)},e.prototype.mergeDefaultAndTheme=function(t,e){var n=nc(this),i=n?rc(t):{},r=this.subType;ac.hasClass(r)&&(r+="Series"),C(t,e.getTheme().get(this.subType)),C(t,this.getDefaultOption()),zr(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&ic(t,i,n)},e.prototype.mergeOption=function(t,e){t=C(this.option,t,!0),this.fillDataTextStyle(t.data);var n=nc(this);n&&ic(this.option,t,n);var i=mf(this).sourceManager;i.dirty(),i.prepareSource();var r=this.getInitialData(t,e);Tf(r,this),this.dataTask.dirty(),this.dataTask.context.data=r,mf(this).dataBeforeProcessed=r,wf(this),this._initSelectedMapFromData(r)},e.prototype.fillDataTextStyle=function(t){if(t&&!Z(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&zr(t[n],"label",e)},e.prototype.getInitialData=function(t,e){},e.prototype.appendData=function(t){this.getRawData().appendData(t.data)},e.prototype.getData=function(t){var e=Df(this);if(e){var n=e.context.data;return null==t?n:n.getLinkedData(t)}return mf(this).data},e.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},e.prototype.setData=function(t){var e=Df(this);if(e){var n=e.context;n.outputData=t,e!==this.dataTask&&(n.data=t)}mf(this).data=t},e.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return ft(t)},e.prototype.getSourceManager=function(){return mf(this).sourceManager},e.prototype.getSource=function(){return this.getSourceManager().getSource()},e.prototype.getRawData=function(){return mf(this).dataBeforeProcessed},e.prototype.getColorBy=function(){return this.get("colorBy")||"series"},e.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},e.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},e.prototype.formatTooltip=function(t,e,n){return vf({series:this,dataIndex:t,multipleSeries:e})},e.prototype.isAnimationEnabled=function(){var t=this.ecModel;if(r.node&&(!t||!t.ssr))return!1;var e=this.getShallow("animation");return e&&this.getData().count()>this.getShallow("animationThreshold")&&(e=!1),!!e},e.prototype.restoreData=function(){this.dataTask.dirty()},e.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel,r=Rc.prototype.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},e.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},e.prototype.getProgressive=function(){return this.get("progressive")},e.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},e.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},e.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)return this.option.selectedMap={},void(this._selectedDataIndicesMap={});for(var o=0;o<t.length;o++){var a=_f(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},e.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},e.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=B(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];r>=0&&n.push(r)}return n},e.prototype.isSelected=function(t,e){var n=this.option.selectedMap;if(!n)return!1;var i=this.getData(e);return("all"===n||n[_f(i,t)])&&!i.getItemModel(t).get(["select","disabled"])},e.prototype.isUniversalTransitionEnabled=function(){if(this.__universalTransitionEnabled)return!0;var t=this.option.universalTransition;return!!t&&(!0===t||t&&t.enabled)},e.prototype._innerSelect=function(t,e){var n,i,r=this.option,o=r.selectedMode,a=e.length;if(o&&a)if("series"===o)r.selectedMap="all";else if("multiple"===o){Y(r.selectedMap)||(r.selectedMap={});for(var s=r.selectedMap,l=0;l<a;l++){var u=e[l];s[c=_f(t,u)]=!0,this._selectedDataIndicesMap[c]=t.getRawIndex(u)}}else if("single"===o||!0===o){var h=e[a-1],c=_f(t,h);r.selectedMap=((n={})[c]=!0,n),this._selectedDataIndicesMap=((i={})[c]=t.getRawIndex(h),i)}},e.prototype._initSelectedMapFromData=function(t){if(!this.option.selectedMap){var e=[];t.hasItemOption&&t.each((function(n){var i=t.getRawDataItem(n);i&&i.selected&&e.push(n)})),e.length>0&&this._innerSelect(t,e)}},e.registerClass=function(t){return ac.registerClass(t)},e.protoInitialize=function(){var t=e.prototype;t.type="series.__base__",t.seriesIndex=0,t.ignoreStyleOnData=!1,t.hasSymbolVisual=!1,t.defaultSymbol="circle",t.visualStyleAccessPath="itemStyle",t.visualDrawType="fill"}(),e}(ac);function wf(t){var e=t.name;Xr(t)||(t.name=function(t){var e=t.getRawData(),n=e.mapDimensionsAll("seriesName"),i=[];return R(n,(function(t){var n=e.getDimensionInfo(t);n.displayName&&i.push(n.displayName)})),i.join(" ")}(t)||e)}function bf(t){return t.model.getRawData().count()}function Sf(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),Mf}function Mf(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function Tf(t,e){R(dt(t.CHANGABLE_METHODS,t.DOWNSAMPLE_METHODS),(function(n){t.wrapMethod(n,V(Cf,e))}))}function Cf(t,e){var n=Df(t);return n&&n.setOutputEnd((e||this).count()),e}function Df(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}P(xf,Wp),P(xf,Rc),oo(xf,ac);var If=function(){function t(){this.group=new or,this.uid=sh("viewComponent")}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){},t.prototype.updateLayout=function(t,e,n,i){},t.prototype.updateVisual=function(t,e,n,i){},t.prototype.toggleBlurSeries=function(t,e,n){},t.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},t}();function kf(){var t=Zr();return function(e){var n=t(e),i=e.pipelineContext,r=!!n.large,o=!!n.progressiveRender,a=n.large=!(!i||!i.large),s=n.progressiveRender=!(!i||!i.progressiveRender);return!(r===a&&o===s)&&"reset"}}ro(If),uo(If);var Af=Zr(),Lf=kf(),Pf=function(){function t(){this.group=new or,this.uid=sh("viewChart"),this.renderTask=Gp({plan:Nf,reset:Ef}),this.renderTask.context={view:this}}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){0},t.prototype.highlight=function(t,e,n,i){var r=t.getData(i&&i.dataType);r&&Rf(r,i,"emphasis")},t.prototype.downplay=function(t,e,n,i){var r=t.getData(i&&i.dataType);r&&Rf(r,i,"normal")},t.prototype.remove=function(t,e){this.group.removeAll()},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.eachRendered=function(t){Nu(this.group,t)},t.markUpdateMethod=function(t,e){Af(t).updateMethod=e},t.protoInitialize=void(t.prototype.type="chart"),t}();function Of(t,e,n){t&&el(t)&&("emphasis"===e?zs:Bs)(t,n)}function Rf(t,e,n){var i=qr(t,e),r=e&&null!=e.highlightKey?function(t){var e=hs[t];return null==e&&us<=32&&(e=hs[t]=us++),e}(e.highlightKey):null;null!=i?R(Er(i),(function(e){Of(t.getItemGraphicEl(e),n,r)})):t.eachItemGraphicEl((function(t){Of(t,n,r)}))}function Nf(t){return Lf(t.model)}function Ef(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=r&&Af(r).updateMethod,l=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==l&&a[l](e,n,i,r),zf[l]}ro(Pf),uo(Pf);var zf={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};function Bf(t,e,n){var i,r,o,a,s,l=0,u=0,h=null;function c(){u=(new Date).getTime(),h=null,t.apply(o,a||[])}e=e||0;var p=function(){for(var t=[],p=0;p<arguments.length;p++)t[p]=arguments[p];i=(new Date).getTime(),o=this,a=t;var f=s||e,d=s||n;s=null,r=i-(d?l:u)-f,clearTimeout(h),d?h=setTimeout(c,f):r>=0?c():h=setTimeout(c,-r),l=i};return p.clear=function(){h&&(clearTimeout(h),h=null)},p.debounceNextCall=function(t){s=t},p}var Ff=Zr(),Vf={itemStyle:ho(nh,!0),lineStyle:ho(Ju,!0)},Hf={lineStyle:"stroke",itemStyle:"fill"};function Wf(t,e){var n=t.visualStyleMapper||Vf[e];return n||(console.warn("Unknown style type '"+e+"'."),Vf.itemStyle)}function Gf(t,e){var n=t.visualDrawType||Hf[e];return n||(console.warn("Unknown style type '"+e+"'."),"fill")}var Uf={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=t.getModel(i),o=Wf(t,i)(r),a=r.getShallow("decal");a&&(n.setVisual("decal",a),a.dirty=!0);var s=Gf(t,i),l=o[s],u=W(l)?l:null,h="auto"===o.fill||"auto"===o.stroke;if(!o[s]||u||h){var c=t.getColorFromPalette(t.name,null,e.getSeriesCount());o[s]||(o[s]=c,n.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||W(o.fill)?c:o.fill,o.stroke="auto"===o.stroke||W(o.stroke)?c:o.stroke}if(n.setVisual("style",o),n.setVisual("drawType",s),!e.isSeriesFiltered(t)&&u)return n.setVisual("colorFromPalette",!1),{dataEach:function(e,n){var i=t.getDataParams(n),r=D({},o);r[s]=u(i),e.setItemVisual(n,"style",r)}}}},Xf=new oh,Yf={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t)){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=Wf(t,i),o=n.getVisual("drawType");return{dataEach:n.hasItemOption?function(t,e){var n=t.getRawDataItem(e);if(n&&n[i]){Xf.option=n[i];var a=r(Xf);D(t.ensureUniqueItemVisual(e,"style"),a),Xf.option.decal&&(t.setItemVisual(e,"decal",Xf.option.decal),Xf.option.decal.dirty=!0),o in a&&t.setItemVisual(e,"colorFromPalette",!1)}}:null}}}},qf={performRawSeries:!0,overallReset:function(t){var e=ft();t.eachSeries((function(t){var n=t.getColorBy();if(!t.isColorBySeries()){var i=t.type+"-"+n,r=e.get(i);r||(r={},e.set(i,r)),Ff(t).scope=r}})),t.eachSeries((function(e){if(!e.isColorBySeries()&&!t.isSeriesFiltered(e)){var n=e.getRawData(),i={},r=e.getData(),o=Ff(e).scope,a=e.visualStyleAccessPath||"itemStyle",s=Gf(e,a);r.each((function(t){var e=r.getRawIndex(t);i[e]=t})),n.each((function(t){var a=i[t];if(r.getItemVisual(a,"colorFromPalette")){var l=r.ensureUniqueItemVisual(a,"style"),u=n.getName(t)||t+"",h=n.count();l[s]=e.getColorFromPalette(u,o,h)}}))}}))}},Zf=Math.PI;var jf=function(){function t(t,e,n,i){this._stageTaskMap=ft(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}return t.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each((function(t){var e=t.overallTask;e&&e.dirty()}))},t.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,o=i&&i.modDataCount;return{step:r,modBy:null!=o?Math.ceil(o/r):null,modDataCount:o}}},t.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},t.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),r=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,o=t.get("large")&&i>=t.get("largeThreshold"),a="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:r,modDataCount:a,large:o}},t.prototype.restorePipelines=function(t){var e=this,n=e._pipelineMap=ft();t.eachSeries((function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),e._pipe(t,t.dataTask)}))},t.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),n=this.api;R(this._allHandlers,(function(i){var r=t.get(i.uid)||t.set(i.uid,{}),o="";ot(!(i.reset&&i.overallReset),o),i.reset&&this._createSeriesStageTask(i,r,e,n),i.overallReset&&this._createOverallStageTask(i,r,e,n)}),this)},t.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},t.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},t.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},t.prototype._performStageTasks=function(t,e,n,i){i=i||{};var r=!1,o=this;function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}R(t,(function(t,s){if(!i.visualType||i.visualType===t.visualType){var l=o._stageTaskMap.get(t.uid),u=l.seriesTaskMap,h=l.overallTask;if(h){var c,p=h.agentStubMap;p.each((function(t){a(i,t)&&(t.dirty(),c=!0)})),c&&h.dirty(),o.updatePayload(h,n);var f=o.getPerformArgs(h,i.block);p.each((function(t){t.perform(f)})),h.perform(f)&&(r=!0)}else u&&u.each((function(s,l){a(i,s)&&s.dirty();var u=o.getPerformArgs(s,i.block);u.skip=!t.performRawSeries&&e.isSeriesFiltered(s.context.model),o.updatePayload(s,n),s.perform(u)&&(r=!0)}))}})),this.unfinished=r||this.unfinished},t.prototype.performSeriesTasks=function(t){var e;t.eachSeries((function(t){e=t.dataTask.perform()||e})),this.unfinished=e||this.unfinished},t.prototype.plan=function(){this._pipelineMap.each((function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)}))},t.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},t.prototype._createSeriesStageTask=function(t,e,n,i){var r=this,o=e.seriesTaskMap,a=e.seriesTaskMap=ft(),s=t.seriesType,l=t.getTargetSeries;function u(e){var s=e.uid,l=a.set(s,o&&o.get(s)||Gp({plan:td,reset:ed,count:rd}));l.context={model:e,ecModel:n,api:i,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:r},r._pipe(e,l)}t.createOnAllSeries?n.eachRawSeries(u):s?n.eachRawSeriesByType(s,u):l&&l(n,i).each(u)},t.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||Gp({reset:Kf});o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r};var a=o.agentStubMap,s=o.agentStubMap=ft(),l=t.seriesType,u=t.getTargetSeries,h=!0,c=!1,p="";function f(t){var e=t.uid,n=s.set(e,a&&a.get(e)||(c=!0,Gp({reset:$f,onDirty:Jf})));n.context={model:t,overallProgress:h},n.agent=o,n.__block=h,r._pipe(t,n)}ot(!t.createOnAllSeries,p),l?n.eachRawSeriesByType(l,f):u?u(n,i).each(f):(h=!1,R(n.getSeries(),f)),c&&o.dirty()},t.prototype._pipe=function(t,e){var n=t.uid,i=this._pipelineMap.get(n);!i.head&&(i.head=e),i.tail&&i.tail.pipe(e),i.tail=e,e.__idxInPipeline=i.count++,e.__pipeline=i},t.wrapStageHandler=function(t,e){return W(t)&&(t={overallReset:t,seriesType:od(t)}),t.uid=sh("stageHandler"),e&&(t.visualType=e),t},t}();function Kf(t){t.overallReset(t.ecModel,t.api,t.payload)}function $f(t){return t.overallProgress&&Qf}function Qf(){this.agent.dirty(),this.getDownstream().dirty()}function Jf(){this.agent&&this.agent.dirty()}function td(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function ed(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=Er(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?N(e,(function(t,e){return id(e)})):nd}var nd=id(0);function id(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var o=e.start;o<e.end;o++)r.dataEach(i,o);else r&&r.progress&&r.progress(e,i)}}function rd(t){return t.data.count()}function od(t){ad=null;try{t(sd,ld)}catch(t){}return ad}var ad,sd={},ld={};function ud(t,e){for(var n in e.prototype)t[n]=mt}ud(sd,Bc),ud(ld,Gc),sd.eachSeriesByType=sd.eachRawSeriesByType=function(t){ad=t},sd.eachComponent=function(t){"series"===t.mainType&&t.subType&&(ad=t.subType)};var hd=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],cd={color:hd,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],hd]},pd="#B9B8CE",fd="#100C2A",dd=function(){return{axisLine:{lineStyle:{color:pd}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},gd=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],yd={darkMode:!0,color:gd,backgroundColor:fd,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:pd}},textStyle:{color:pd},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:pd}},dataZoom:{borderColor:"#71708A",textStyle:{color:pd},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:pd}},timeline:{lineStyle:{color:pd},label:{color:pd},controlStyle:{color:pd,borderColor:pd}},calendar:{itemStyle:{color:fd},dayLabel:{color:pd},monthLabel:{color:pd},yearLabel:{color:pd}},timeAxis:dd(),logAxis:dd(),valueAxis:dd(),categoryAxis:dd(),line:{symbol:"circle"},graph:{color:gd},gauge:{title:{color:pd},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:pd},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};yd.categoryAxis.splitLine.show=!1;var vd=function(){function t(){}return t.prototype.normalizeQuery=function(t){var e={},n={},i={};if(G(t)){var r=io(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var o=["Index","Name","Id"],a={name:1,dataIndex:1,dataType:1};R(t,(function(t,r){for(var s=!1,l=0;l<o.length;l++){var u=o[l],h=r.lastIndexOf(u);if(h>0&&h===r.length-u.length){var c=r.slice(0,h);"data"!==c&&(e.mainType=c,e[u.toLowerCase()]=t,s=!0)}}a.hasOwnProperty(r)&&(n[r]=t,s=!0),s||(i[r]=t)}))}return{cptQuery:e,dataQuery:n,otherQuery:i}},t.prototype.filter=function(t,e){var n=this.eventInfo;if(!n)return!0;var i=n.targetEl,r=n.packedEvent,o=n.model,a=n.view;if(!o||!a)return!0;var s=e.cptQuery,l=e.dataQuery;return u(s,o,"mainType")&&u(s,o,"subType")&&u(s,o,"index","componentIndex")&&u(s,o,"name")&&u(s,o,"id")&&u(l,r,"name")&&u(l,r,"dataIndex")&&u(l,r,"dataType")&&(!a.filterForExposedEvent||a.filterForExposedEvent(t,e.otherQuery,i,r));function u(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},t.prototype.afterTrigger=function(){this.eventInfo=null},t}(),md=["symbol","symbolSize","symbolRotate","symbolOffset"],_d=md.concat(["symbolKeepAspect"]),xd={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData();if(t.legendIcon&&n.setVisual("legendIcon",t.legendIcon),t.hasSymbolVisual){for(var i={},r={},o=!1,a=0;a<md.length;a++){var s=md[a],l=t.get(s);W(l)?(o=!0,r[s]=l):i[s]=l}if(i.symbol=i.symbol||t.defaultSymbol,n.setVisual(D({legendIcon:t.legendIcon||i.symbol,symbolKeepAspect:t.get("symbolKeepAspect")},i)),!e.isSeriesFiltered(t)){var u=B(r);return{dataEach:o?function(e,n){for(var i=t.getRawValue(n),o=t.getDataParams(n),a=0;a<u.length;a++){var s=u[a];e.setItemVisual(n,s,r[s](i,o))}}:null}}}}},wd={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<_d.length;i++){var r=_d[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function bd(t,e,n,i,r){var o=t+e;n.isSilent(o)||i.eachComponent({mainType:"series",subType:"pie"},(function(t){for(var e=t.seriesIndex,i=t.option.selectedMap,a=r.selected,s=0;s<a.length;s++)if(a[s].seriesIndex===e){var l=t.getData(),u=qr(l,r.fromActionPayload);n.trigger(o,{type:o,seriesId:t.id,name:H(u)?l.getName(u[0]):l.getName(u),selected:G(i)?i:D({},i)})}}))}function Sd(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var Md=Math.round(9*Math.random()),Td="function"==typeof Object.defineProperty,Cd=function(){function t(){this._id="__ec_inner_"+Md++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var n=this._guard(t);return Td?Object.defineProperty(n,this._id,{value:e,enumerable:!1,configurable:!0}):n[this._id]=e,this},t.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}(),Dd=Ea.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),Id=Ea.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),kd=Ea.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=i-o+a+s,u=Math.asin(s/a),h=Math.cos(u)*a,c=Math.sin(u),p=Math.cos(u),f=.6*a,d=.7*a;t.moveTo(n-h,l+s),t.arc(n,l,a,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(n+h-c*f,l+s+p*f,n,i-d,n,i),t.bezierCurveTo(n,i-d,n-h+c*f,l+s+p*f,n-h,l+s),t.closePath()}}),Ad=Ea.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),Ld={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},Pd={};R({line:jl,rect:qa,roundRect:qa,square:qa,circle:bl,diamond:Id,pin:kd,arrow:Ad,triangle:Dd},(function(t,e){Pd[e]=new t}));var Od=Ea.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=Zi(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.position&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i=e.symbolType;if("none"!==i){var r=Pd[i];r||(r=Pd[i="rect"]),Ld[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n)}}});function Rd(t,e){if("image"!==this.type){var n=this.style;this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw()}}function Nd(t,e,n,i,r,o,a){var s,l=0===t.indexOf("empty");return l&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),(s=0===t.indexOf("image://")?Iu(t.slice(8),new Ce(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Du(t.slice(7),{},new Ce(e,n,i,r),a?"center":"cover"):new Od({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=l,s.setColor=Rd,o&&s.setColor(o),s}function Ed(t,e){if(null!=t)return H(t)||(t=[t,t]),[dr(t[0],e[0])||0,dr(et(t[1],t[0]),e[1])||0]}function zd(t){return isFinite(t)}function Bd(t,e,n){for(var i="radial"===e.type?function(t,e,n){var i=n.width,r=n.height,o=Math.min(i,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(a=a*i+n.x,s=s*r+n.y,l*=o),a=zd(a)?a:.5,s=zd(s)?s:.5,l=l>=0&&zd(l)?l:.5,t.createRadialGradient(a,s,0,a,s,l)}(t,e,n):function(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*n.width+n.x,r=r*n.width+n.x,o=o*n.height+n.y,a=a*n.height+n.y),i=zd(i)?i:0,r=zd(r)?r:1,o=zd(o)?o:0,a=zd(a)?a:0,t.createLinearGradient(i,o,r,a)}(t,e,n),r=e.colorStops,o=0;o<r.length;o++)i.addColorStop(r[o].offset,r[o].color);return i}function Fd(t){return parseInt(t,10)}function Vd(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],a=["paddingRight","paddingBottom"][e];if(null!=n[i]&&"auto"!==n[i])return parseFloat(n[i]);var s=document.defaultView.getComputedStyle(t);return(t[r]||Fd(s[i])||Fd(t.style[i]))-(Fd(s[o])||0)-(Fd(s[a])||0)|0}function Hd(t){var e,n,i=t.style,r=i.lineDash&&i.lineWidth>0&&(e=i.lineDash,n=i.lineWidth,e&&"solid"!==e&&n>0?"dashed"===e?[4*n,2*n]:"dotted"===e?[n]:X(e)?[e]:H(e)?e:null:null),o=i.lineDashOffset;if(r){var a=i.strokeNoScale&&t.getLineScale?t.getLineScale():1;a&&1!==a&&(r=N(r,(function(t){return t/a})),o/=a)}return[r,o]}var Wd=new ya(!0);function Gd(t){var e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))}function Ud(t){return"string"==typeof t&&"none"!==t}function Xd(t){var e=t.fill;return null!=e&&"none"!==e}function Yd(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var n=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n}else t.fill()}function qd(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var n=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n}else t.stroke()}function Zd(t,e,n){var i=yo(e.image,e.__image,n);if(mo(i)){var r=t.createPattern(i,e.repeat||"repeat");if("function"==typeof DOMMatrix&&r&&r.setTransform){var o=new DOMMatrix;o.translateSelf(e.x||0,e.y||0),o.rotateSelf(0,0,(e.rotation||0)*_t),o.scaleSelf(e.scaleX||1,e.scaleY||1),r.setTransform(o)}return r}}var jd=["shadowBlur","shadowOffsetX","shadowOffsetY"],Kd=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function $d(t,e,n,i,r){var o=!1;if(!i&&e===(n=n||{}))return!1;if(i||e.opacity!==n.opacity){tg(t,r),o=!0;var a=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(a)?Po.opacity:a}(i||e.blend!==n.blend)&&(o||(tg(t,r),o=!0),t.globalCompositeOperation=e.blend||Po.blend);for(var s=0;s<jd.length;s++){var l=jd[s];(i||e[l]!==n[l])&&(o||(tg(t,r),o=!0),t[l]=t.dpr*(e[l]||0))}return(i||e.shadowColor!==n.shadowColor)&&(o||(tg(t,r),o=!0),t.shadowColor=e.shadowColor||Po.shadowColor),o}function Qd(t,e,n,i,r){var o=eg(e,r.inHover),a=i?null:n&&eg(n,r.inHover)||{};if(o===a)return!1;var s=$d(t,o,a,i,r);if((i||o.fill!==a.fill)&&(s||(tg(t,r),s=!0),Ud(o.fill)&&(t.fillStyle=o.fill)),(i||o.stroke!==a.stroke)&&(s||(tg(t,r),s=!0),Ud(o.stroke)&&(t.strokeStyle=o.stroke)),(i||o.opacity!==a.opacity)&&(s||(tg(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var l=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==l&&(s||(tg(t,r),s=!0),t.lineWidth=l)}for(var u=0;u<Kd.length;u++){var h=Kd[u],c=h[0];(i||o[c]!==a[c])&&(s||(tg(t,r),s=!0),t[c]=o[c]||h[1])}return s}function Jd(t,e){var n=e.transform,i=t.dpr||1;n?t.setTransform(i*n[0],i*n[1],i*n[2],i*n[3],i*n[4],i*n[5]):t.setTransform(i,0,0,i,0,0)}function tg(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function eg(t,e){return e&&t.__hoverStyle||t.style}function ng(t,e){ig(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function ig(t,e,n,i){var r=e.transform;if(!e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1))return e.__dirty&=-2,void(e.__isRendered=!1);var a=e.__clipPaths,s=n.prevElClipPaths,l=!1,u=!1;if(s&&!function(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}(a,s)||(s&&s.length&&(tg(t,n),t.restore(),u=l=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),a&&a.length&&(tg(t,n),t.save(),function(t,e,n){for(var i=!1,r=0;r<t.length;r++){var o=t[r];i=i||o.isZeroArea(),Jd(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}n.allClipped=i}(a,t,n),l=!0),n.prevElClipPaths=a),n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var h=n.prevEl;h||(u=l=!0);var c,p,f=e instanceof Ea&&e.autoBatch&&function(t){var e=Xd(t),n=Gd(t);return!(t.lineDash||!(+e^+n)||e&&"string"!=typeof t.fill||n&&"string"!=typeof t.stroke||t.strokePercent<1||t.strokeOpacity<1||t.fillOpacity<1)}(e.style);l||(c=r,p=h.transform,c&&p?c[0]!==p[0]||c[1]!==p[1]||c[2]!==p[2]||c[3]!==p[3]||c[4]!==p[4]||c[5]!==p[5]:c||p)?(tg(t,n),Jd(t,e)):f||tg(t,n);var d=eg(e,n.inHover);e instanceof Ea?(1!==n.lastDrawType&&(u=!0,n.lastDrawType=1),Qd(t,e,h,u,n),f&&(n.batchFill||n.batchStroke)||t.beginPath(),function(t,e,n,i){var r,o=Gd(n),a=Xd(n),s=n.strokePercent,l=s<1,u=!e.path;e.silent&&!l||!u||e.createPathProxy();var h=e.path||Wd,c=e.__dirty;if(!i){var p=n.fill,f=n.stroke,d=a&&!!p.colorStops,g=o&&!!f.colorStops,y=a&&!!p.image,v=o&&!!f.image,m=void 0,_=void 0,x=void 0,w=void 0,b=void 0;(d||g)&&(b=e.getBoundingRect()),d&&(m=c?Bd(t,p,b):e.__canvasFillGradient,e.__canvasFillGradient=m),g&&(_=c?Bd(t,f,b):e.__canvasStrokeGradient,e.__canvasStrokeGradient=_),y&&(x=c||!e.__canvasFillPattern?Zd(t,p,e):e.__canvasFillPattern,e.__canvasFillPattern=x),v&&(w=c||!e.__canvasStrokePattern?Zd(t,f,e):e.__canvasStrokePattern,e.__canvasStrokePattern=x),d?t.fillStyle=m:y&&(x?t.fillStyle=x:a=!1),g?t.strokeStyle=_:v&&(w?t.strokeStyle=w:o=!1)}var S,M,T=e.getGlobalScale();h.setScale(T[0],T[1],e.segmentIgnoreThreshold),t.setLineDash&&n.lineDash&&(S=(r=Hd(e))[0],M=r[1]);var C=!0;(u||4&c)&&(h.setDPR(t.dpr),l?h.setContext(null):(h.setContext(t),C=!1),h.reset(),e.buildPath(h,e.shape,i),h.toStatic(),e.pathUpdated()),C&&h.rebuildPath(t,l?s:1),S&&(t.setLineDash(S),t.lineDashOffset=M),i||(n.strokeFirst?(o&&qd(t,n),a&&Yd(t,n)):(a&&Yd(t,n),o&&qd(t,n))),S&&t.setLineDash([])}(t,e,d,f),f&&(n.batchFill=d.fill||"",n.batchStroke=d.stroke||"")):e instanceof Ba?(3!==n.lastDrawType&&(u=!0,n.lastDrawType=3),Qd(t,e,h,u,n),function(t,e,n){var i,r=n.text;if(null!=r&&(r+=""),r){t.font=n.font||o,t.textAlign=n.textAlign,t.textBaseline=n.textBaseline;var a=void 0,s=void 0;t.setLineDash&&n.lineDash&&(a=(i=Hd(e))[0],s=i[1]),a&&(t.setLineDash(a),t.lineDashOffset=s),n.strokeFirst?(Gd(n)&&t.strokeText(r,n.x,n.y),Xd(n)&&t.fillText(r,n.x,n.y)):(Xd(n)&&t.fillText(r,n.x,n.y),Gd(n)&&t.strokeText(r,n.x,n.y)),a&&t.setLineDash([])}}(t,e,d)):e instanceof Ha?(2!==n.lastDrawType&&(u=!0,n.lastDrawType=2),function(t,e,n,i,r){$d(t,eg(e,r.inHover),n&&eg(n,r.inHover),i,r)}(t,e,h,u,n),function(t,e,n){var i=e.__image=yo(n.image,e.__image,e,e.onload);if(i&&mo(i)){var r=n.x||0,o=n.y||0,a=e.getWidth(),s=e.getHeight(),l=i.width/i.height;if(null==a&&null!=s?a=s*l:null==s&&null!=a?s=a/l:null==a&&null==s&&(a=i.width,s=i.height),n.sWidth&&n.sHeight){var u=n.sx||0,h=n.sy||0;t.drawImage(i,u,h,n.sWidth,n.sHeight,r,o,a,s)}else if(n.sx&&n.sy){var c=a-(u=n.sx),p=s-(h=n.sy);t.drawImage(i,u,h,c,p,r,o,a,s)}else t.drawImage(i,r,o,a,s)}}(t,e,d)):e.getTemporalDisplayables&&(4!==n.lastDrawType&&(u=!0,n.lastDrawType=4),function(t,e,n){var i=e.getDisplayables(),r=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:n.viewWidth,viewHeight:n.viewHeight,inHover:n.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){(h=i[o]).beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),ig(t,h,s,o===a-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}for(var l=0,u=r.length;l<u;l++){var h;(h=r[l]).beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),ig(t,h,s,l===u-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,n)),f&&i&&tg(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),n.prevEl=e,e.__dirty=0,e.__isRendered=!0}}var rg=new Cd,og=new Sn(100),ag=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function sg(t,e){if("none"===t)return null;var n=e.getDevicePixelRatio(),i=e.getZr(),r="svg"===i.painter.type;t.dirty&&rg.delete(t);var o=rg.get(t);if(o)return o;var a=I(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});"none"===a.backgroundColor&&(a.backgroundColor=null);var s={repeat:"repeat"};return function(t){for(var e,o=[n],s=!0,l=0;l<ag.length;++l){var h=a[ag[l]];if(null!=h&&!H(h)&&!G(h)&&!X(h)&&"boolean"!=typeof h){s=!1;break}o.push(h)}if(s){e=o.join(",")+(r?"-svg":"");var c=og.get(e);c&&(r?t.svgElement=c:t.image=c)}var p,f=ug(a.dashArrayX),d=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(X(t)){var e=Math.ceil(t);return[e,e]}var n=N(t,(function(t){return Math.ceil(t)}));return t.length%2?n.concat(n):n}(a.dashArrayY),g=lg(a.symbol),y=(w=f,N(w,(function(t){return hg(t)}))),v=hg(d),m=!r&&u.createCanvas(),_=r&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=Lr(t,y[e]);var i=1;for(e=0,n=g.length;e<n;++e)i=Lr(i,g[e].length);t*=i;var r=v*y.length*g.length;return{width:Math.max(1,Math.min(t,a.maxTileWidth)),height:Math.max(1,Math.min(r,a.maxTileHeight))}}();var w;m&&(m.width=x.width*n,m.height=x.height*n,p=m.getContext("2d"));(function(){p&&(p.clearRect(0,0,m.width,m.height),a.backgroundColor&&(p.fillStyle=a.backgroundColor,p.fillRect(0,0,m.width,m.height)));for(var t=0,e=0;e<d.length;++e)t+=d[e];if(t<=0)return;var o=-v,s=0,l=0,u=0;for(;o<x.height;){if(s%2==0){for(var h=l/2%g.length,c=0,y=0,w=0;c<2*x.width;){var b=0;for(e=0;e<f[u].length;++e)b+=f[u][e];if(b<=0)break;if(y%2==0){var S=.5*(1-a.symbolSize),M=c+f[u][y]*S,T=o+d[s]*S,C=f[u][y]*a.symbolSize,D=d[s]*a.symbolSize,I=w/2%g[h].length;k(M,T,C,D,g[h][I])}c+=f[u][y],++w,++y===f[u].length&&(y=0)}++u===f.length&&(u=0)}o+=d[s],++l,++s===d.length&&(s=0)}function k(t,e,o,s,l){var u=r?1:n,h=Nd(l,t*u,e*u,o*u,s*u,a.color,a.symbolKeepAspect);if(r){var c=i.painter.renderOneToVNode(h);c&&_.children.push(c)}else ng(p,h)}})(),s&&og.put(e,m||_);t.image=m,t.svgElement=_,t.svgWidth=x.width,t.svgHeight=x.height}(s),s.rotation=a.rotation,s.scaleX=s.scaleY=r?1:1/n,rg.set(t,s),t.dirty=!1,s}function lg(t){if(!t||0===t.length)return[["rect"]];if(G(t))return[[t]];for(var e=!0,n=0;n<t.length;++n)if(!G(t[n])){e=!1;break}if(e)return lg([t]);var i=[];for(n=0;n<t.length;++n)G(t[n])?i.push([t[n]]):i.push(t[n]);return i}function ug(t){if(!t||0===t.length)return[[0,0]];if(X(t))return[[r=Math.ceil(t),r]];for(var e=!0,n=0;n<t.length;++n)if(!X(t[n])){e=!1;break}if(e)return ug([t]);var i=[];for(n=0;n<t.length;++n)if(X(t[n])){var r=Math.ceil(t[n]);i.push([r,r])}else{(r=N(t[n],(function(t){return Math.ceil(t)}))).length%2==1?i.push(r.concat(r)):i.push(r)}return i}function hg(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var cg=new Wt,pg={};function fg(t){return pg[t]}var dg=2e3,gg=4500,yg={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:dg,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:gg,ARIA:6e3,DECAL:7e3}},vg="__flagInMainProcess",mg="__pendingUpdate",_g="__needsUpdateStatus",xg=/^[a-zA-Z0-9_]+$/,wg="__connectUpdateStatus";function bg(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(!this.isDisposed())return Mg(this,t,e);Zg(this.id)}}function Sg(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return Mg(this,t,e)}}function Mg(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),Wt.prototype[e].apply(t,n)}var Tg,Cg,Dg,Ig,kg,Ag,Lg,Pg,Og,Rg,Ng,Eg,zg,Bg,Fg,Vg,Hg,Wg,Gg=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(Wt),Ug=Gg.prototype;Ug.on=Sg("on"),Ug.off=Sg("off");var Xg=function(t){function e(e,n,i){var r=t.call(this,new vd)||this;r._chartsViews=[],r._chartsMap={},r._componentsViews=[],r._componentsMap={},r._pendingActions=[],i=i||{},G(n)&&(n=ty[n]),r._dom=e;var o="canvas",a="auto",s=!1,l=r._zr=ur(e,{renderer:i.renderer||o,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height,ssr:i.ssr,useDirtyRect:et(i.useDirtyRect,s),useCoarsePointer:et(i.useCoarsePointer,a),pointerSize:i.pointerSize});r._ssr=i.ssr,r._throttledZrFlush=Bf(F(l.flush,l),17),(n=T(n))&&dp(n,!0),r._theme=n,r._locale=function(t){if(G(t)){var e=ch[t.toUpperCase()]||{};return t===lh||t===uh?T(e):C(T(e),T(ch[hh]),!1)}return C(T(t),T(ch[hh]),!1)}(i.locale||fh),r._coordSysMgr=new Xc;var u=r._api=Fg(r);function h(t,e){return t.__prio-e.__prio}return We(Jg,h),We($g,h),r._scheduler=new jf(r,u,$g,Jg),r._messageCenter=new Gg,r._initEvents(),r.resize=F(r.resize,r),l.animation.on("frame",r._onframe,r),Rg(l,r),Ng(l,r),lt(r),r}return n(e,t),e.prototype._onframe=function(){if(!this._disposed){Wg(this);var t=this._scheduler;if(this[mg]){var e=this[mg].silent;this[vg]=!0;try{Tg(this),Ig.update.call(this,null,this[mg].updateParams)}catch(t){throw this[vg]=!1,this[mg]=null,t}this._zr.flush(),this[vg]=!1,this[mg]=null,Pg.call(this,e),Og.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),Ag(this,i),t.performVisualTasks(i),Bg(this,this._model,r,"remain",{}),n-=+new Date-o}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},e.prototype.getDom=function(){return this._dom},e.prototype.getId=function(){return this.id},e.prototype.getZr=function(){return this._zr},e.prototype.isSSR=function(){return this._ssr},e.prototype.setOption=function(t,e,n){if(!this[vg])if(this._disposed)Zg(this.id);else{var i,r,o;if(Y(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[vg]=!0,!this._model||e){var a=new qc(this._api),s=this._theme,l=this._model=new Bc;l.scheduler=this._scheduler,l.ssr=this._ssr,l.init(null,null,null,s,this._locale,a)}this._model.setOption(t,{replaceMerge:r},Qg);var u={seriesTransition:o,optionChanged:!0};if(n)this[mg]={silent:i,updateParams:u},this[vg]=!1,this.getZr().wakeUp();else{try{Tg(this),Ig.update.call(this,null,u)}catch(t){throw this[mg]=null,this[vg]=!1,t}this._ssr||this._zr.flush(),this[mg]=null,this[vg]=!1,Pg.call(this,i),Og.call(this,i)}}},e.prototype.setTheme=function(){Pr()},e.prototype.getModel=function(){return this._model},e.prototype.getOption=function(){return this._model&&this._model.getOption()},e.prototype.getWidth=function(){return this._zr.getWidth()},e.prototype.getHeight=function(){return this._zr.getHeight()},e.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||r.hasGlobalWindow&&window.devicePixelRatio||1},e.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},e.prototype.renderToCanvas=function(t){t=t||{};var e=this._zr.painter;return e.getRenderedCanvas({backgroundColor:t.backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},e.prototype.renderToSVGString=function(t){t=t||{};var e=this._zr.painter;return e.renderToString({useViewBox:t.useViewBox})},e.prototype.getSvgDataURL=function(){if(r.svgSupported){var t=this._zr;return R(t.storage.getDisplayList(),(function(t){t.stopAnimation(null,!0)})),t.painter.toDataURL()}},e.prototype.getDataURL=function(t){if(!this._disposed){var e=(t=t||{}).excludeComponents,n=this._model,i=[],r=this;R(e,(function(t){n.eachComponent({mainType:t},(function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)}))}));var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return R(i,(function(t){t.group.ignore=!1})),o}Zg(this.id)},e.prototype.getConnectedDataURL=function(t){if(!this._disposed){var e="svg"===t.type,n=this.group,i=Math.min,r=Math.max,o=1/0;if(iy[n]){var a=o,s=o,l=-1/0,h=-1/0,c=[],p=t&&t.pixelRatio||this.getDevicePixelRatio();R(ny,(function(o,u){if(o.group===n){var p=e?o.getZr().painter.getSvgDom().innerHTML:o.renderToCanvas(T(t)),f=o.getDom().getBoundingClientRect();a=i(f.left,a),s=i(f.top,s),l=r(f.right,l),h=r(f.bottom,h),c.push({dom:p,left:f.left,top:f.top})}}));var f=(l*=p)-(a*=p),d=(h*=p)-(s*=p),g=u.createCanvas(),y=ur(g,{renderer:e?"svg":"canvas"});if(y.resize({width:f,height:d}),e){var v="";return R(c,(function(t){var e=t.left-a,n=t.top-s;v+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"})),y.painter.getSvgRoot().innerHTML=v,t.connectedBackgroundColor&&y.painter.setBackgroundColor(t.connectedBackgroundColor),y.refreshImmediately(),y.painter.toDataURL()}return t.connectedBackgroundColor&&y.add(new qa({shape:{x:0,y:0,width:f,height:d},style:{fill:t.connectedBackgroundColor}})),R(c,(function(t){var e=new Ha({style:{x:t.left*p-a,y:t.top*p-s,image:t.dom}});y.add(e)})),y.refreshImmediately(),g.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}Zg(this.id)},e.prototype.convertToPixel=function(t,e){return kg(this,"convertToPixel",t,e)},e.prototype.convertFromPixel=function(t,e){return kg(this,"convertFromPixel",t,e)},e.prototype.containPixel=function(t,e){var n;if(!this._disposed)return R(Kr(this._model,t),(function(t,i){i.indexOf("Models")>=0&&R(t,(function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n=n||!!r.containPoint(e);else if("seriesModels"===i){var o=this._chartsMap[t.__viewId];o&&o.containPoint&&(n=n||o.containPoint(e,t))}else 0}),this)}),this),!!n;Zg(this.id)},e.prototype.getVisual=function(t,e){var n=Kr(this._model,t,{defaultMainType:"series"}),i=n.seriesModel;var r=i.getData(),o=n.hasOwnProperty("dataIndexInside")?n.dataIndexInside:n.hasOwnProperty("dataIndex")?r.indexOfRawIndex(n.dataIndex):null;return null!=o?function(t,e,n){switch(n){case"color":return t.getItemVisual(e,"style")[t.getVisual("drawType")];case"opacity":return t.getItemVisual(e,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getItemVisual(e,n)}}(r,o,e):function(t,e){switch(e){case"color":return t.getVisual("style")[t.getVisual("drawType")];case"opacity":return t.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getVisual(e)}}(r,e)},e.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},e.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},e.prototype._initEvents=function(){var t,e,n,i=this;R(qg,(function(t){var e=function(e){var n,r=i.getModel(),o=e.target,a="globalout"===t;if(a?n={}:o&&Sd(o,(function(t){var e=ls(t);if(e&&null!=e.dataIndex){var i=e.dataModel||r.getSeriesByIndex(e.seriesIndex);return n=i&&i.getDataParams(e.dataIndex,e.dataType,o)||{},!0}if(e.eventData)return n=D({},e.eventData),!0}),!0),n){var s=n.componentType,l=n.componentIndex;"markLine"!==s&&"markPoint"!==s&&"markArea"!==s||(s="series",l=n.seriesIndex);var u=s&&null!=l&&r.getComponent(s,l),h=u&&i["series"===u.mainType?"_chartsMap":"_componentsMap"][u.__viewId];0,n.event=e,n.type=t,i._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:u,view:h},i.trigger(t,n)}};e.zrEventfulCallAtLast=!0,i._zr.on(t,e,i)})),R(Kg,(function(t,e){i._messageCenter.on(e,(function(t){this.trigger(e,t)}),i)})),R(["selectchanged"],(function(t){i._messageCenter.on(t,(function(e){this.trigger(t,e)}),i)})),t=this._messageCenter,e=this,n=this._api,t.on("selectchanged",(function(t){var i=n.getModel();t.isFromClick?(bd("map","selectchanged",e,i,t),bd("pie","selectchanged",e,i,t)):"select"===t.fromAction?(bd("map","selected",e,i,t),bd("pie","selected",e,i,t)):"unselect"===t.fromAction&&(bd("map","unselected",e,i,t),bd("pie","unselected",e,i,t))}))},e.prototype.isDisposed=function(){return this._disposed},e.prototype.clear=function(){this._disposed?Zg(this.id):this.setOption({series:[]},!0)},e.prototype.dispose=function(){if(this._disposed)Zg(this.id);else{this._disposed=!0,this.getDom()&&to(this.getDom(),ay,"");var t=this,e=t._api,n=t._model;R(t._componentsViews,(function(t){t.dispose(n,e)})),R(t._chartsViews,(function(t){t.dispose(n,e)})),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete ny[t.id]}},e.prototype.resize=function(t){if(!this[vg])if(this._disposed)Zg(this.id);else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[mg]&&(null==i&&(i=this[mg].silent),n=!0,this[mg]=null),this[vg]=!0;try{n&&Tg(this),Ig.update.call(this,{type:"resize",animation:D({duration:0},t&&t.animation)})}catch(t){throw this[vg]=!1,t}this[vg]=!1,Pg.call(this,i),Og.call(this,i)}}},e.prototype.showLoading=function(t,e){if(this._disposed)Zg(this.id);else if(Y(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),ey[t]){var n=ey[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},e.prototype.hideLoading=function(){this._disposed?Zg(this.id):(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},e.prototype.makeActionFromEvent=function(t){var e=D({},t);return e.type=Kg[t.type],e},e.prototype.dispatchAction=function(t,e){if(this._disposed)Zg(this.id);else if(Y(e)||(e={silent:!!e}),jg[t.type]&&this._model)if(this[vg])this._pendingActions.push(t);else{var n=e.silent;Lg.call(this,t,n);var i=e.flush;i?this._zr.flush():!1!==i&&r.browser.weChat&&this._throttledZrFlush(),Pg.call(this,n),Og.call(this,n)}},e.prototype.updateLabelLayout=function(){cg.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},e.prototype.appendData=function(t){if(this._disposed)Zg(this.id);else{var e=t.seriesIndex,n=this.getModel().getSeriesByIndex(e);0,n.appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp()}},e.internalField=function(){function t(t){t.clearColorPalette(),t.eachSeries((function(t){t.clearColorPalette()}))}function e(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),2===t.hoverState&&t.states.emphasis?e.push("emphasis"):1===t.hoverState&&t.states.blur&&e.push("blur"),t.useStates(e)}function i(t,e){if(!t.preventAutoZ){var n=t.get("z")||0,i=t.get("zlevel")||0;e.eachRendered((function(t){return o(t,n,i,-1/0),!0}))}}function o(t,e,n,i){var r=t.getTextContent(),a=t.getTextGuideLine();if(t.isGroup)for(var s=t.childrenRef(),l=0;l<s.length;l++)i=Math.max(o(s[l],e,n,i),i);else t.z=e,t.zlevel=n,i=Math.max(t.z2,i);if(r&&(r.z=e,r.zlevel=n,isFinite(i)&&(r.z2=i+2)),a){var u=t.textGuideLineConfig;a.z=e,a.zlevel=n,isFinite(i)&&(a.z2=i+(u&&u.showAbove?1:-1))}return i}function a(t,e){e.eachRendered((function(t){if(!vu(t)){var e=t.getTextContent(),n=t.getTextGuideLine();t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null)}}))}function s(t,n){var i=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),o=i.get("duration"),a=o>0?{duration:o,delay:i.get("delay"),easing:i.get("easing")}:null;n.eachRendered((function(t){if(t.states&&t.states.emphasis){if(vu(t))return;if(t instanceof Ea&&function(t){var e=cs(t);e.normalFill=t.style.fill,e.normalStroke=t.style.stroke;var n=t.states.select||{};e.selectFill=n.style&&n.style.fill||null,e.selectStroke=n.style&&n.style.stroke||null}(t),t.__dirty){var n=t.prevStates;n&&t.useStates(n)}if(r){t.stateTransition=a;var i=t.getTextContent(),o=t.getTextGuideLine();i&&(i.stateTransition=a),o&&(o.stateTransition=a)}t.__dirty&&e(t)}}))}Tg=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),Cg(t,!0),Cg(t,!1),e.plan()},Cg=function(t,e){for(var n=t._model,i=t._scheduler,r=e?t._componentsViews:t._chartsViews,o=e?t._componentsMap:t._chartsMap,a=t._zr,s=t._api,l=0;l<r.length;l++)r[l].__alive=!1;function u(t){var l=t.__requireNewView;t.__requireNewView=!1;var u="_ec_"+t.id+"_"+t.type,h=!l&&o[u];if(!h){var c=io(t.type),p=e?If.getClass(c.main,c.sub):Pf.getClass(c.sub);0,(h=new p).init(n,s),o[u]=h,r.push(h),a.add(h.group)}t.__viewId=h.__id=u,h.__alive=!0,h.__model=t,h.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!e&&i.prepareView(h,t,n,s)}e?n.eachComponent((function(t,e){"series"!==t&&u(e)})):n.eachSeries(u);for(l=0;l<r.length;){var h=r[l];h.__alive?l++:(!e&&h.renderTask.dispose(),a.remove(h.group),h.dispose(n,s),r.splice(l,1),o[h.__id]===h&&delete o[h.__id],h.__id=h.group.__ecComponentInfo=null)}},Dg=function(t,e,n,i,r){var o=t._model;if(o.setUpdatePayload(n),i){var a={};a[i+"Id"]=n[i+"Id"],a[i+"Index"]=n[i+"Index"],a[i+"Name"]=n[i+"Name"];var s={mainType:i,query:a};r&&(s.subType=r);var l,u=n.excludeSeriesId;null!=u&&(l=ft(),R(Er(u),(function(t){var e=Ur(t,null);null!=e&&l.set(e,!0)}))),o&&o.eachComponent(s,(function(e){if(!(l&&null!=l.get(e.id)))if(il(n))if(e instanceof xf)n.type!==gs||n.notBlur||e.get(["emphasis","disabled"])||function(t,e,n){var i=t.seriesIndex,r=t.getData(e.dataType);if(r){var o=qr(r,e);o=(H(o)?o[0]:o)||0;var a=r.getItemGraphicEl(o);if(!a)for(var s=r.count(),l=0;!a&&l<s;)a=r.getItemGraphicEl(l++);if(a){var u=ls(a);Us(i,u.focus,u.blurScope,n)}else{var h=t.get(["emphasis","focus"]),c=t.get(["emphasis","blurScope"]);null!=h&&Us(i,h,c,n)}}}(e,n,t._api);else{var i=Ys(e.mainType,e.componentIndex,n.name,t._api),r=i.focusSelf,o=i.dispatchers;n.type===gs&&r&&!n.notBlur&&Xs(e.mainType,e.componentIndex,t._api),o&&R(o,(function(t){n.type===gs?zs(t):Bs(t)}))}else nl(n)&&e instanceof xf&&(!function(t,e,n){if(nl(e)){var i=e.dataType,r=qr(t.getData(i),e);H(r)||(r=[r]),t[e.type===_s?"toggleSelect":e.type===vs?"select":"unselect"](r,i)}}(e,n,t._api),qs(e),Hg(t))}),t),o&&o.eachComponent(s,(function(e){l&&null!=l.get(e.id)||h(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])}),t)}else R([].concat(t._componentsViews).concat(t._chartsViews),h);function h(i){i&&i.__alive&&i[e]&&i[e](i.__model,o,t._api,n)}},Ig={prepareAndUpdate:function(t){Tg(this),Ig.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(e,n){var i=this._model,r=this._api,o=this._zr,a=this._coordSysMgr,s=this._scheduler;if(i){i.setUpdatePayload(e),s.restoreData(i,e),s.performSeriesTasks(i),a.create(i,r),s.performDataProcessorTasks(i,e),Ag(this,i),a.update(i,r),t(i),s.performVisualTasks(i,e),Eg(this,i,r,e,n);var l=i.get("backgroundColor")||"transparent",u=i.get("darkMode");o.setBackgroundColor(l),null!=u&&"auto"!==u&&o.setDarkMode(u),cg.trigger("afterupdate",i,r)}},updateTransform:function(e){var n=this,i=this._model,r=this._api;if(i){i.setUpdatePayload(e);var o=[];i.eachComponent((function(t,a){if("series"!==t){var s=n.getViewOfComponentModel(a);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(a,i,r,e);l&&l.update&&o.push(s)}else o.push(s)}}));var a=ft();i.eachSeries((function(t){var o=n._chartsMap[t.__viewId];if(o.updateTransform){var s=o.updateTransform(t,i,r,e);s&&s.update&&a.set(t.uid,1)}else a.set(t.uid,1)})),t(i),this._scheduler.performVisualTasks(i,e,{setDirty:!0,dirtyMap:a}),Bg(this,i,r,e,{},a),cg.trigger("afterupdate",i,r)}},updateView:function(e){var n=this._model;n&&(n.setUpdatePayload(e),Pf.markUpdateMethod(e,"updateView"),t(n),this._scheduler.performVisualTasks(n,e,{setDirty:!0}),Eg(this,n,this._api,e,{}),cg.trigger("afterupdate",n,this._api))},updateVisual:function(e){var n=this,i=this._model;i&&(i.setUpdatePayload(e),i.eachSeries((function(t){t.getData().clearAllVisual()})),Pf.markUpdateMethod(e,"updateVisual"),t(i),this._scheduler.performVisualTasks(i,e,{visualType:"visual",setDirty:!0}),i.eachComponent((function(t,r){if("series"!==t){var o=n.getViewOfComponentModel(r);o&&o.__alive&&o.updateVisual(r,i,n._api,e)}})),i.eachSeries((function(t){n._chartsMap[t.__viewId].updateVisual(t,i,n._api,e)})),cg.trigger("afterupdate",i,this._api))},updateLayout:function(t){Ig.update.call(this,t)}},kg=function(t,e,n,i){if(t._disposed)Zg(t.id);else{for(var r,o=t._model,a=t._coordSysMgr.getCoordinateSystems(),s=Kr(o,n),l=0;l<a.length;l++){var u=a[l];if(u[e]&&null!=(r=u[e](o,s,i)))return r}0}},Ag=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries((function(t){i.updateStreamModes(t,n[t.__viewId])}))},Lg=function(t,e){var n=this,i=this.getModel(),r=t.type,o=t.escapeConnect,a=jg[r],s=a.actionInfo,l=(s.update||"update").split(":"),u=l.pop(),h=null!=l[0]&&io(l[0]);this[vg]=!0;var c=[t],p=!1;t.batch&&(p=!0,c=N(t.batch,(function(e){return(e=I(D({},e),t)).batch=null,e})));var f,d=[],g=nl(t),y=il(t);if(y&&Gs(this._api),R(c,(function(e){if((f=(f=a.action(e,n._model,n._api))||D({},e)).type=s.event||f.type,d.push(f),y){var i=$r(t),r=i.queryOptionMap,o=i.mainTypeSpecified?r.keys()[0]:"series";Dg(n,u,e,o),Hg(n)}else g?(Dg(n,u,e,"series"),Hg(n)):h&&Dg(n,u,e,h.main,h.sub)})),"none"!==u&&!y&&!g&&!h)try{this[mg]?(Tg(this),Ig.update.call(this,t),this[mg]=null):Ig[u].call(this,t)}catch(t){throw this[vg]=!1,t}if(f=p?{type:s.event||r,escapeConnect:o,batch:d}:d[0],this[vg]=!1,!e){var v=this._messageCenter;if(v.trigger(f.type,f),g){var m={type:"selectchanged",escapeConnect:o,selected:Zs(i),isFromClick:t.isFromClick||!1,fromAction:t.type,fromActionPayload:t};v.trigger(m.type,m)}}},Pg=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();Lg.call(this,n,t)}},Og=function(t){!t&&this.trigger("updated")},Rg=function(t,e){t.on("rendered",(function(n){e.trigger("rendered",n),!t.animation.isFinished()||e[mg]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")}))},Ng=function(t,e){t.on("mouseover",(function(t){var n=Sd(t.target,el);n&&(!function(t,e,n){var i=ls(t),r=Ys(i.componentMainType,i.componentIndex,i.componentHighDownName,n),o=r.dispatchers,a=r.focusSelf;o?(a&&Xs(i.componentMainType,i.componentIndex,n),R(o,(function(t){return Ns(t,e)}))):(Us(i.seriesIndex,i.focus,i.blurScope,n),"self"===i.focus&&Xs(i.componentMainType,i.componentIndex,n),Ns(t,e))}(n,t,e._api),Hg(e))})).on("mouseout",(function(t){var n=Sd(t.target,el);n&&(!function(t,e,n){Gs(n);var i=ls(t),r=Ys(i.componentMainType,i.componentIndex,i.componentHighDownName,n).dispatchers;r?R(r,(function(t){return Es(t,e)})):Es(t,e)}(n,t,e._api),Hg(e))})).on("click",(function(t){var n=Sd(t.target,(function(t){return null!=ls(t).dataIndex}),!0);if(n){var i=n.selected?"unselect":"select",r=ls(n);e._api.dispatchAction({type:i,dataType:r.dataType,dataIndexInside:r.dataIndex,seriesIndex:r.seriesIndex,isFromClick:!0})}}))},Eg=function(t,e,n,i,r){!function(t){var e=[],n=[],i=!1;if(t.eachComponent((function(t,r){var o=r.get("zlevel")||0,a=r.get("z")||0,s=r.getZLevelKey();i=i||!!s,("series"===t?n:e).push({zlevel:o,z:a,idx:r.componentIndex,type:t,key:s})})),i){var r,o,a=e.concat(n);We(a,(function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel})),R(a,(function(e){var n=t.getComponent(e.type,e.idx),i=e.zlevel,a=e.key;null!=r&&(i=Math.max(r,i)),a?(i===r&&a!==o&&i++,o=a):o&&(i===r&&i++,o=""),r=i,n.setZLevel(i)}))}}(e),zg(t,e,n,i,r),R(t._chartsViews,(function(t){t.__alive=!1})),Bg(t,e,n,i,r),R(t._chartsViews,(function(t){t.__alive||t.remove(e,n)}))},zg=function(t,e,n,r,o,l){R(l||t._componentsViews,(function(t){var o=t.__model;a(o,t),t.render(o,e,n,r),i(o,t),s(o,t)}))},Bg=function(t,e,n,o,l,u){var h=t._scheduler;l=D(l||{},{updatedSeries:e.getSeries()}),cg.trigger("series:beforeupdate",e,n,l);var c=!1;e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var i=n.renderTask;h.updatePayload(i,o),a(e,n),u&&u.get(e.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!e.get("silent"),function(t,e){var n=t.get("blendMode")||null;e.eachRendered((function(t){t.isGroup||(t.style.blend=n)}))}(e,n),qs(e)})),h.unfinished=c||h.unfinished,cg.trigger("series:layoutlabels",e,n,l),cg.trigger("series:transition",e,n,l),e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];i(e,n),s(e,n)})),function(t,e){var n=t._zr,i=n.storage,o=0;i.traverse((function(t){t.isGroup||o++})),o>e.get("hoverLayerThreshold")&&!r.node&&!r.worker&&e.eachSeries((function(e){if(!e.preventUsingHoverLayer){var n=t._chartsMap[e.__viewId];n.__alive&&n.eachRendered((function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)}))}}))}(t,e),cg.trigger("series:afterupdate",e,n,l)},Hg=function(t){t[_g]=!0,t.getZr().wakeUp()},Wg=function(t){t[_g]&&(t.getZr().storage.traverse((function(t){vu(t)||e(t)})),t[_g]=!1)},Fg=function(t){return new(function(e){function i(){return null!==e&&e.apply(this,arguments)||this}return n(i,e),i.prototype.getCoordinateSystems=function(){return t._coordSysMgr.getCoordinateSystems()},i.prototype.getComponentByElement=function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}},i.prototype.enterEmphasis=function(e,n){zs(e,n),Hg(t)},i.prototype.leaveEmphasis=function(e,n){Bs(e,n),Hg(t)},i.prototype.enterBlur=function(e){!function(t){Ls(t,Cs)}(e),Hg(t)},i.prototype.leaveBlur=function(e){Fs(e),Hg(t)},i.prototype.enterSelect=function(e){Vs(e),Hg(t)},i.prototype.leaveSelect=function(e){Hs(e),Hg(t)},i.prototype.getModel=function(){return t.getModel()},i.prototype.getViewOfComponentModel=function(e){return t.getViewOfComponentModel(e)},i.prototype.getViewOfSeriesModel=function(e){return t.getViewOfSeriesModel(e)},i}(Gc))(t)},Vg=function(t){function e(t,e){for(var n=0;n<t.length;n++){t[n][wg]=e}}R(Kg,(function(n,i){t._messageCenter.on(i,(function(n){if(iy[t.group]&&0!==t[wg]){if(n&&n.escapeConnect)return;var i=t.makeActionFromEvent(n),r=[];R(ny,(function(e){e!==t&&e.group===t.group&&r.push(e)})),e(r,0),R(r,(function(t){1!==t[wg]&&t.dispatchAction(i)})),e(r,2)}}))}))}}(),e}(Wt),Yg=Xg.prototype;Yg.on=bg("on"),Yg.off=bg("off"),Yg.one=function(t,e,n){var i=this;Pr(),this.on.call(this,t,(function n(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e&&e.apply&&e.apply(this,r),i.off(t,n)}),n)};var qg=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function Zg(t){0}var jg={},Kg={},$g=[],Qg=[],Jg=[],ty={},ey={},ny={},iy={},ry=+new Date-0,oy=+new Date-0,ay="_echarts_instance_";function sy(t){iy[t]=!1}var ly=sy;function uy(t){return ny[function(t,e){return t.getAttribute?t.getAttribute(e):t[e]}(t,ay)]}function hy(t,e){ty[t]=e}function cy(t){A(Qg,t)<0&&Qg.push(t)}function py(t,e){wy($g,t,e,2e3)}function fy(t){gy("afterinit",t)}function dy(t){gy("afterupdate",t)}function gy(t,e){cg.on(t,e)}function yy(t,e,n){W(e)&&(n=e,e="");var i=Y(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,Kg[e]||(ot(xg.test(i)&&xg.test(e)),jg[i]||(jg[i]={action:n,actionInfo:t}),Kg[e]=i)}function vy(t,e){Xc.register(t,e)}function my(t,e){wy(Jg,t,e,1e3,"layout")}function _y(t,e){wy(Jg,t,e,3e3,"visual")}var xy=[];function wy(t,e,n,i,r){if((W(e)||Y(e))&&(n=e,e=i),!(A(xy,n)>=0)){xy.push(n);var o=jf.wrapStageHandler(n,r);o.__prio=e,o.__raw=n,t.push(o)}}function by(t,e){ey[t]=e}function Sy(t,e,n){var i=fg("registerMap");i&&i(t,e,n)}var My=function(t){var e=(t=T(t)).type,n="";e||Or(n);var i=e.split(":");2!==i.length&&Or(n);var r=!1;"echarts"===i[0]&&(e=i[1],r=!0),t.__isBuiltIn=r,Qp.set(e,t)};_y(dg,Uf),_y(gg,Yf),_y(gg,qf),_y(dg,xd),_y(gg,wd),_y(7e3,(function(t,e){t.eachRawSeries((function(n){if(!t.isSeriesFiltered(n)){var i=n.getData();i.hasItemVisual()&&i.each((function(t){var n=i.getItemVisual(t,"decal");n&&(i.ensureUniqueItemVisual(t,"style").decal=sg(n,e))}));var r=i.getVisual("decal");if(r)i.getVisual("style").decal=sg(r,e)}}))})),cy(dp),py(900,(function(t){var e=ft();t.eachSeries((function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),o={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!o.stackedDimension||!o.isStackedByIndex&&!o.stackedByDimension)return;i.length&&r.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(o)}})),e.each(gp)})),by("default",(function(t,e){I(e=e||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var n=new or,i=new qa({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});n.add(i);var r,o=new Ka({style:{text:e.text,fill:e.textColor,fontSize:e.fontSize,fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:e.fontFamily},zlevel:e.zlevel,z:10001}),a=new qa({style:{fill:"none"},textContent:o,textConfig:{position:"right",distance:10},zlevel:e.zlevel,z:10001});return n.add(a),e.showSpinner&&((r=new eu({shape:{startAngle:-Zf/2,endAngle:-Zf/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*Zf/2}).start("circularInOut"),r.animateShape(!0).when(1e3,{startAngle:3*Zf/2}).delay(300).start("circularInOut"),n.add(r)),n.resize=function(){var n=o.getBoundingRect().width,s=e.showSpinner?e.spinnerRadius:0,l=(t.getWidth()-2*s-(e.showSpinner&&n?10:0)-n)/2-(e.showSpinner&&n?0:5+n/2)+(e.showSpinner?0:n/2)+(n?0:s),u=t.getHeight()/2;e.showSpinner&&r.setShape({cx:l,cy:u}),a.setShape({x:l-s,y:u-s,width:2*s,height:2*s}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},n.resize(),n})),yy({type:gs,event:gs,update:gs},mt),yy({type:ys,event:ys,update:ys},mt),yy({type:vs,event:vs,update:vs},mt),yy({type:ms,event:ms,update:ms},mt),yy({type:_s,event:_s,update:_s},mt),hy("light",cd),hy("dark",yd);var Ty=[],Cy={registerPreprocessor:cy,registerProcessor:py,registerPostInit:fy,registerPostUpdate:dy,registerUpdateLifecycle:gy,registerAction:yy,registerCoordinateSystem:vy,registerLayout:my,registerVisual:_y,registerTransform:My,registerLoading:by,registerMap:Sy,registerImpl:function(t,e){pg[t]=e},PRIORITY:yg,ComponentModel:ac,ComponentView:If,SeriesModel:xf,ChartView:Pf,registerComponentModel:function(t){ac.registerClass(t)},registerComponentView:function(t){If.registerClass(t)},registerSeriesModel:function(t){xf.registerClass(t)},registerChartView:function(t){Pf.registerClass(t)},registerSubTypeDefaulter:function(t,e){ac.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){hr(t,e)}};function Dy(t){H(t)?R(t,(function(t){Dy(t)})):A(Ty,t)>=0||(Ty.push(t),W(t)&&(t={install:t}),t.install(Cy))}function Iy(t){return null==t?0:t.length||1}function ky(t){return t}var Ay=function(){function t(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||ky,this._newKeyGetter=i||ky,this.context=r,this._diffModeMultiple="multiple"===o}return t.prototype.add=function(t){return this._add=t,this},t.prototype.update=function(t){return this._update=t,this},t.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},t.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},t.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},t.prototype.remove=function(t){return this._remove=t,this},t.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},t.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a=i[o],s=n[a],l=Iy(s);if(l>1){var u=s.shift();1===s.length&&(n[a]=s[0]),this._update&&this._update(u,o)}else 1===l?(n[a]=null,this._update&&this._update(s,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},t.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=Iy(l),c=Iy(u);if(h>1&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&c>1)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(h>1)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},t.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=Iy(r);if(o>1)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},t.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a="_ec_"+this[i](t[o],o);if(r||(n[o]=a),e){var s=e[a],l=Iy(s);0===l?(e[a]=o,r&&n.push(a)):1===l?e[a]=[s,o]:s.push(o)}}},t}(),Ly=function(){function t(t,e){this._encode=t,this._schema=e}return t.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},t.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},t}();function Py(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var Oy=function(t){this.otherDims={},null!=t&&D(this,t)},Ry=Zr(),Ny={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},Ey=function(){function t(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return t.prototype.isDimensionOmitted=function(){return this._dimOmitted},t.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=Fy(this.source)))},t.prototype.getSourceDimensionIndex=function(t){return et(this._dimNameMap.get(t),-1)},t.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},t.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Ip(this.source),n=!Vy(t),i="",r=[],o=0,a=0;o<t;o++){var s=void 0,l=void 0,u=void 0,h=this.dimensions[a];if(h&&h.storeDimIndex===o)s=e?h.name:null,l=h.type,u=h.ordinalMeta,a++;else{var c=this.getSourceDimension(o);c&&(s=e?c.name:null,l=c.type)}r.push({property:s,type:l,ordinalMeta:u}),!e||null==s||h&&h.isCalculationCoord||(i+=n?s.replace(/\`/g,"`1").replace(/\$/g,"`2"):s),i+="$",i+=Ny[l]||"f",u&&(i+=u.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},t.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];if(r&&r.storeDimIndex===e)r.isCalculationCoord||(i=r.name),n++;else{var o=this.getSourceDimension(e);o&&(i=o.name)}t.push(i)}return t},t.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},t}();function zy(t){return t instanceof Ey}function By(t){for(var e=ft(),n=0;n<(t||[]).length;n++){var i=t[n],r=Y(i)?i.name:i;null!=r&&null==e.get(r)&&e.set(r,n)}return e}function Fy(t){var e=Ry(t);return e.dimNameMap||(e.dimNameMap=By(t.dimensionsDefine))}function Vy(t){return t>30}var Hy,Wy,Gy,Uy,Xy,Yy,qy,Zy=Y,jy=N,Ky="undefined"==typeof Int32Array?Array:Int32Array,$y=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],Qy=["_approximateExtent"],Jy=function(){function t(t,e){var n;this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","lttbDownSample"];var i=!1;zy(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(i=!0,n=t),n=n||["x","y"];for(var r={},o=[],a={},s=!1,l={},u=0;u<n.length;u++){var h=n[u],c=G(h)?new Oy({name:h}):h instanceof Oy?h:new Oy(h),p=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0);var f=c.otherDims=c.otherDims||{};o.push(p),r[p]=c,null!=l[p]&&(s=!0),c.createInvertedIndices&&(a[p]=[]),0===f.itemName&&(this._nameDimIdx=u),0===f.itemId&&(this._idDimIdx=u),i&&(c.storeDimIndex=u)}if(this.dimensions=o,this._dimInfos=r,this._initGetDimensionInfo(s),this.hostModel=e,this._invertedIndicesMap=a,this._dimOmitted){var d=this._dimIdxToName=ft();R(o,(function(t){d.set(r[t].storeDimIndex,t)}))}}return t.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(null==e)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var n=this._dimIdxToName.get(e);if(null!=n)return n;var i=this._schema.getSourceDimension(e);return i?i.name:void 0},t.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(null!=e)return e;if(null==t)return-1;var n=this._getDimInfo(t);return n?n.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},t.prototype._recognizeDimIndex=function(t){if(X(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},t.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},t.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},t.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},t.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},t.prototype.mapDimension=function(t,e){var n=this._dimSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return i?i[e]:null},t.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},t.prototype.getStore=function(){return this._store},t.prototype.initData=function(t,e,n){var i,r=this;if(t instanceof cf&&(i=t),!i){var o=this.dimensions,a=bp(t)||O(t)?new kp(t,o.length):t;i=new cf;var s=jy(o,(function(t){return{type:r._dimInfos[t].type,property:t}}));i.initData(a,s,n)}this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=function(t,e){var n={},i=n.encode={},r=ft(),o=[],a=[],s={};R(t.dimensions,(function(e){var n,l=t.getDimensionInfo(e),u=l.coordDim;if(u){var h=l.coordDimIndex;Py(i,u)[h]=e,l.isExtraCoord||(r.set(u,1),"ordinal"!==(n=l.type)&&"time"!==n&&(o[0]=e),Py(s,u)[h]=t.getDimensionIndex(l.name)),l.defaultTooltip&&a.push(e)}hc.each((function(t,e){var n=Py(i,e),r=l.otherDims[e];null!=r&&!1!==r&&(n[r]=l.name)}))}));var l=[],u={};r.each((function(t,e){var n=i[e];u[e]=n[0],l=l.concat(n)})),n.dataDimsOnCoord=l,n.dataDimIndicesOnCoord=N(l,(function(e){return t.getDimensionInfo(e).storeDimIndex})),n.encodeFirstDimNotExtra=u;var h=i.label;h&&h.length&&(o=h.slice());var c=i.tooltip;return c&&c.length?a=c.slice():a.length||(a=o.slice()),i.defaultedLabel=o,i.defaultedTooltip=a,n.userOutput=new Ly(s,e),n}(this,this._schema),this.userOutput=this._dimSummary.userOutput},t.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},t.prototype.appendValues=function(t,e){var n=this._store.appendValues(t,e.length),i=n.start,r=n.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var a=i;a<r;a++){var s=a-i;this._nameList[a]=e[s],o&&qy(this,a)}},t.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},t.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==gc&&!t.fillStorage},t.prototype._doInit=function(t,e){if(!(t>=e)){var n=this._store.getProvider();this._updateOrdinalMeta();var i=this._nameList,r=this._idList;if(n.getSource().sourceFormat===cc&&!n.pure)for(var o=[],a=t;a<e;a++){var s=n.getItem(a,o);if(!this.hasItemOption&&Vr(s)&&(this.hasItemOption=!0),s){var l=s.name;null==i[a]&&null!=l&&(i[a]=Ur(l,null));var u=s.id;null==r[a]&&null!=u&&(r[a]=Ur(u,null))}}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)qy(this,a);Hy(this)}},t.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},t.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},t.prototype.setCalculationInfo=function(t,e){Zy(t)?D(this._calculationInfo,t):this._calculationInfo[t]=e},t.prototype.getName=function(t){var e=this.getRawIndex(t),n=this._nameList[e];return null==n&&null!=this._nameDimIdx&&(n=Gy(this,this._nameDimIdx,e)),null==n&&(n=""),n},t.prototype._getCategory=function(t,e){var n=this._store.get(t,e),i=this._store.getOrdinalMeta(t);return i?i.categories[n]:n},t.prototype.getId=function(t){return Wy(this,this.getRawIndex(t))},t.prototype.count=function(){return this._store.count()},t.prototype.get=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.get(i.storeDimIndex,e)},t.prototype.getByRawIndex=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.getByRawIndex(i.storeDimIndex,e)},t.prototype.getIndices=function(){return this._store.getIndices()},t.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},t.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},t.prototype.getValues=function(t,e){var n=this,i=this._store;return H(t)?i.getValues(jy(t,(function(t){return n._getStoreDimIndex(t)})),e):i.getValues(t)},t.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},t.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},t.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},t.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},t.prototype.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t];var i=n[e];return null==i||isNaN(i)?-1:i},t.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},t.prototype.each=function(t,e,n){W(t)&&(n=e,e=t,t=[]);var i=n||this,r=jy(Uy(t),this._getStoreDimIndex,this);this._store.each(r,i?F(e,i):e)},t.prototype.filterSelf=function(t,e,n){W(t)&&(n=e,e=t,t=[]);var i=n||this,r=jy(Uy(t),this._getStoreDimIndex,this);return this._store=this._store.filter(r,i?F(e,i):e),this},t.prototype.selectRange=function(t){var e=this,n={};return R(B(t),(function(i){var r=e._getStoreDimIndex(i);n[r]=t[i]})),this._store=this._store.selectRange(n),this},t.prototype.mapArray=function(t,e,n){W(t)&&(n=e,e=t,t=[]),n=n||this;var i=[];return this.each(t,(function(){i.push(e&&e.apply(this,arguments))}),n),i},t.prototype.map=function(t,e,n,i){var r=n||i||this,o=jy(Uy(t),this._getStoreDimIndex,this),a=Yy(this);return a._store=this._store.map(o,r?F(e,r):e),a},t.prototype.modify=function(t,e,n,i){var r=n||i||this;var o=jy(Uy(t),this._getStoreDimIndex,this);this._store.modify(o,r?F(e,r):e)},t.prototype.downSample=function(t,e,n,i){var r=Yy(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},t.prototype.lttbDownSample=function(t,e){var n=Yy(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},t.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},t.prototype.getItemModel=function(t){var e=this.hostModel,n=this.getRawDataItem(t);return new oh(n,e,e&&e.ecModel)},t.prototype.diff=function(t){var e=this;return new Ay(t?t.getStore().getIndices():[],this.getStore().getIndices(),(function(e){return Wy(t,e)}),(function(t){return Wy(e,t)}))},t.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},t.prototype.setVisual=function(t,e){this._visual=this._visual||{},Zy(t)?D(this._visual,t):this._visual[t]=e},t.prototype.getItemVisual=function(t,e){var n=this._itemVisuals[t],i=n&&n[e];return null==i?this.getVisual(e):i},t.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},t.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t];i||(i=n[t]={});var r=i[e];return null==r&&(H(r=this.getVisual(e))?r=r.slice():Zy(r)&&(r=D({},r)),i[e]=r),r},t.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,Zy(e)?D(i,e):i[e]=n},t.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},t.prototype.setLayout=function(t,e){Zy(t)?D(this._layout,t):this._layout[t]=e},t.prototype.getLayout=function(t){return this._layout[t]},t.prototype.getItemLayout=function(t){return this._itemLayouts[t]},t.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?D(this._itemLayouts[t]||{},e):e},t.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},t.prototype.setItemGraphicEl=function(t,e){!function(t,e,n,i){if(i){var r=ls(i);r.dataIndex=n,r.dataType=e,r.seriesIndex=t,"group"===i.type&&i.traverse((function(i){var r=ls(i);r.seriesIndex=t,r.dataIndex=n,r.dataType=e}))}}(this.hostModel&&this.hostModel.seriesIndex,this.dataType,t,e),this._graphicEls[t]=e},t.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},t.prototype.eachItemGraphicEl=function(t,e){R(this._graphicEls,(function(n,i){n&&t&&t.call(e,n,i)}))},t.prototype.cloneShallow=function(e){return e||(e=new t(this._schema?this._schema:jy(this.dimensions,this._getDimInfo,this),this.hostModel)),Xy(e,this),e._store=this._store,e},t.prototype.wrapMethod=function(t,e){var n=this[t];W(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(it(arguments)))})},t.internalField=(Hy=function(t){var e=t._invertedIndicesMap;R(e,(function(n,i){var r=t._dimInfos[i],o=r.ordinalMeta,a=t._store;if(o){n=e[i]=new Ky(o.categories.length);for(var s=0;s<n.length;s++)n[s]=-1;for(s=0;s<a.count();s++)n[a.get(r.storeDimIndex,s)]=s}}))},Gy=function(t,e,n){return Ur(t._getCategory(e,n),null)},Wy=function(t,e){var n=t._idList[e];return null==n&&null!=t._idDimIdx&&(n=Gy(t,t._idDimIdx,e)),null==n&&(n="e\0\0"+e),n},Uy=function(t){return H(t)||(t=null!=t?[t]:[]),t},Yy=function(e){var n=new t(e._schema?e._schema:jy(e.dimensions,e._getDimInfo,e),e.hostModel);return Xy(n,e),n},Xy=function(t,e){R($y.concat(e.__wrappedMethods||[]),(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t.__wrappedMethods=e.__wrappedMethods,R(Qy,(function(n){t[n]=T(e[n])})),t._calculationInfo=D({},e._calculationInfo)},void(qy=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];if(null==a&&null!=r&&(n[e]=a=Gy(t,r,e)),null==s&&null!=o&&(i[e]=s=Gy(t,o,e)),null==s&&null!=a){var l=t._nameRepeatCount,u=l[a]=(l[a]||0)+1;s=a,u>1&&(s+="__ec__"+u),i[e]=s}})),t}();function tv(t,e){bp(t)||(t=Mp(t));var n=(e=e||{}).coordDimensions||[],i=e.dimensionsDefine||t.dimensionsDefine||[],r=ft(),o=[],a=function(t,e,n,i){var r=Math.max(t.dimensionsDetectedCount||1,e.length,n.length,i||0);return R(e,(function(t){var e;Y(t)&&(e=t.dimsDef)&&(r=Math.max(r,e.length))})),r}(t,n,i,e.dimensionsCount),s=e.canOmitUnusedDimensions&&Vy(a),l=i===t.dimensionsDefine,u=l?Fy(t):By(i),h=e.encodeDefine;!h&&e.encodeDefaulter&&(h=e.encodeDefaulter(t,a));for(var c=ft(h),p=new af(a),f=0;f<p.length;f++)p[f]=-1;function d(t){var e=p[t];if(e<0){var n=i[t],r=Y(n)?n:{name:n},a=new Oy,s=r.name;null!=s&&null!=u.get(s)&&(a.name=a.displayName=s),null!=r.type&&(a.type=r.type),null!=r.displayName&&(a.displayName=r.displayName);var l=o.length;return p[t]=l,a.storeDimIndex=t,o.push(a),a}return o[e]}if(!s)for(f=0;f<a;f++)d(f);c.each((function(t,e){var n=Er(t).slice();if(1===n.length&&!G(n[0])&&n[0]<0)c.set(e,!1);else{var i=c.set(e,[]);R(n,(function(t,n){var r=G(t)?u.get(t):t;null!=r&&r<a&&(i[n]=r,y(d(r),e,n))}))}}));var g=0;function y(t,e,n){null!=hc.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,r.set(e,!0))}R(n,(function(t){var e,n,i,r;if(G(t))e=t,r={};else{e=(r=t).name;var o=r.ordinalMeta;r.ordinalMeta=null,(r=D({},r)).ordinalMeta=o,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null}var s=c.get(e);if(!1!==s){if(!(s=Er(s)).length)for(var u=0;u<(n&&n.length||1);u++){for(;g<a&&null!=d(g).coordDim;)g++;g<a&&s.push(g++)}R(s,(function(t,o){var a=d(t);if(l&&null!=r.type&&(a.type=r.type),y(I(a,r),e,o),null==a.name&&n){var s=n[o];!Y(s)&&(s={name:s}),a.name=a.displayName=s.name,a.defaultTooltip=s.defaultTooltip}i&&I(a.otherDims,i)}))}}));var v=e.generateCoord,m=e.generateCoordCount,_=null!=m;m=v?m||1:0;var x=v||"value";function w(t){null==t.name&&(t.name=t.coordDim)}if(s)R(o,(function(t){w(t)})),o.sort((function(t,e){return t.storeDimIndex-e.storeDimIndex}));else for(var b=0;b<a;b++){var S=d(b);null==S.coordDim&&(S.coordDim=ev(x,r,_),S.coordDimIndex=0,(!v||m<=0)&&(S.isExtraCoord=!0),m--),w(S),null!=S.type||Cc(t,b)!==_c&&(!S.isExtraCoord||null==S.otherDims.itemName&&null==S.otherDims.seriesName)||(S.type="ordinal")}return function(t){for(var e=ft(),n=0;n<t.length;n++){var i=t[n],r=i.name,o=e.get(r)||0;o>0&&(i.name=r+(o-1)),o++,e.set(r,o)}}(o),new Ey({source:t,dimensions:o,fullDimensionCount:a,dimensionOmitted:s})}function ev(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}var nv=function(t){this.coordSysDims=[],this.axisMap=ft(),this.categoryAxisMap=ft(),this.coordSysName=t};var iv={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",Qr).models[0],o=t.getReferringComponents("yAxis",Qr).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",o),rv(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),rv(o)&&(i.set("y",o),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis",Qr).models[0];e.coordSysDims=["single"],n.set("single",r),rv(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar",Qr).models[0],o=r.findAxisModel("radiusAxis"),a=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",a),rv(o)&&(i.set("radius",o),e.firstCategoryDimIndex=0),rv(a)&&(i.set("angle",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,o=r.getComponent("parallel",t.get("parallelIndex")),a=e.coordSysDims=o.dimensions.slice();R(o.parallelAxisIndex,(function(t,o){var s=r.getComponent("parallelAxis",t),l=a[o];n.set(l,s),rv(s)&&(i.set(l,s),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=o))}))}};function rv(t){return"category"===t.get("type")}function ov(t,e,n){var i,r,o,a=(n=n||{}).byIndex,s=n.stackedCoordDimension;!function(t){return!zy(t.schema)}(e)?(r=e.schema,i=r.dimensions,o=e.store):i=e;var l,u,h,c,p=!(!t||!t.get("stack"));if(R(i,(function(t,e){G(t)&&(i[e]=t={name:t}),p&&!t.isExtraCoord&&(a||l||!t.ordinalMeta||(l=t),u||"ordinal"===t.type||"time"===t.type||s&&s!==t.coordDim||(u=t))})),!u||a||l||(a=!0),u){h="__\0ecstackresult_"+t.id,c="__\0ecstackedover_"+t.id,l&&(l.createInvertedIndices=!0);var f=u.coordDim,d=u.type,g=0;R(i,(function(t){t.coordDim===f&&g++}));var y={name:h,coordDim:f,coordDimIndex:g,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},v={name:c,coordDim:c,coordDimIndex:g+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1};r?(o&&(y.storeDimIndex=o.ensureCalculationDimension(c,d),v.storeDimIndex=o.ensureCalculationDimension(h,d)),r.appendCalculationDimension(y),r.appendCalculationDimension(v)):(i.push(y),i.push(v))}return{stackedDimension:u&&u.name,stackedByDimension:l&&l.name,isStackedByIndex:a,stackedOverDimension:c,stackResultDimension:h}}function av(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function sv(t,e){return av(t,e)?t.getCalculationInfo("stackResultDimension"):e}function lv(t,e,n){n=n||{};var i,r=e.getSourceManager(),o=!1;t?(o=!0,i=Mp(t)):o=(i=r.getSource()).sourceFormat===cc;var a=function(t){var e=t.get("coordinateSystem"),n=new nv(e),i=iv[e];if(i)return i(t,n,n.axisMap,n.categoryAxisMap),n}(e),s=function(t,e){var n,i=t.get("coordinateSystem"),r=Xc.get(i);return e&&e.coordSysDims&&(n=N(e.coordSysDims,(function(t){var n={name:t},i=e.axisMap.get(t);if(i){var r=i.get("type");n.type=function(t){return"category"===t?"ordinal":"time"===t?"time":"float"}(r)}return n}))),n||(n=r&&(r.getDimensionsInfo?r.getDimensionsInfo():r.dimensions.slice())||["x","y"]),n}(e,a),l=n.useEncodeDefaulter,u=W(l)?l:l?V(Sc,s,e):null,h=tv(i,{coordDimensions:s,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!o}),c=function(t,e,n){var i,r;return n&&R(t,(function(t,o){var a=t.coordDim,s=n.categoryAxisMap.get(a);s&&(null==i&&(i=o),t.ordinalMeta=s.getOrdinalMeta(),e&&(t.createInvertedIndices=!0)),null!=t.otherDims.itemName&&(r=!0)})),r||null==i||(t[i].otherDims.itemName=0),i}(h.dimensions,n.createInvertedIndices,a),p=o?null:r.getSharedDataStore(h),f=ov(e,{schema:h,store:p}),d=new Jy(h,e);d.setCalculationInfo(f);var g=null!=c&&function(t){if(t.sourceFormat===cc){return!H(Fr(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[])))}}(i)?function(t,e,n,i){return i===c?n:this.defaultDimValueGetter(t,e,n,i)}:null;return d.hasItemOption=!1,d.initData(o?i:p,null,g),d}var uv=function(){function t(t){this._setting=t||{},this._extent=[1/0,-1/0]}return t.prototype.getSetting=function(t){return this._setting[t]},t.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},t.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},t.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},t.prototype.isBlank=function(){return this._isBlank},t.prototype.setBlank=function(t){this._isBlank=t},t}();uo(uv);var hv=0,cv=function(){function t(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++hv}return t.createByAxisModel=function(e){var n=e.option,i=n.data,r=i&&N(i,pv);return new t({categories:r,needCollect:!r,deduplication:!1!==n.dedplication})},t.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},t.prototype.parseAndCollect=function(t){var e,n=this._needCollect;if(!G(t)&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=this._getOrCreateMap();return null==(e=i.get(t))&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=NaN),e},t.prototype._getOrCreateMap=function(){return this._map||(this._map=ft(this.categories))},t}();function pv(t){return Y(t)&&null!=t.value?t.value:t+""}function fv(t){return"interval"===t.type||"log"===t.type}function dv(t,e,n,i){var r={},o=t[1]-t[0],a=r.interval=Dr(o/e,!0);null!=n&&a<n&&(a=r.interval=n),null!=i&&a>i&&(a=r.interval=i);var s=r.intervalPrecision=yv(a);return function(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),vv(t,0,e),vv(t,1,e),t[0]>t[1]&&(t[0]=t[1])}(r.niceTickExtent=[gr(Math.ceil(t[0]/a)*a,s),gr(Math.floor(t[1]/a)*a,s)],t),r}function gv(t){var e=Math.pow(10,Cr(t)),n=t/e;return n?2===n?n=3:3===n?n=5:n*=2:n=1,gr(n*e)}function yv(t){return yr(t)+2}function vv(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function mv(t,e){return t>=e[0]&&t<=e[1]}function _v(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function xv(t,e){return t*(e[1]-e[0])+e[0]}var wv=function(t){function e(e){var n=t.call(this,e)||this;n.type="ordinal";var i=n.getSetting("ordinalMeta");return i||(i=new cv({})),H(i)&&(i=new cv({categories:N(i,(function(t){return Y(t)?t.value:t}))})),n._ordinalMeta=i,n._extent=n.getSetting("extent")||[0,i.categories.length-1],n}return n(e,t),e.prototype.parse=function(t){return null==t?NaN:G(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},e.prototype.contain=function(t){return mv(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},e.prototype.normalize=function(t){return _v(t=this._getTickNumber(this.parse(t)),this._extent)},e.prototype.scale=function(t){return t=Math.round(xv(t,this._extent)),this.getRawOrdinalNumber(t)},e.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},e.prototype.getMinorTicks=function(t){},e.prototype.setSortInfo=function(t){if(null!=t){for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];n[r]=s,i[s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}else this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null},e.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getLabel=function(t){if(!this.isBlank()){var e=this.getRawOrdinalNumber(t.value),n=this._ordinalMeta.categories[e];return null==n?"":n+""}},e.prototype.count=function(){return this._extent[1]-this._extent[0]+1},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},e.prototype.getOrdinalMeta=function(){return this._ordinalMeta},e.prototype.calcNiceTicks=function(){},e.prototype.calcNiceExtent=function(){},e.type="ordinal",e}(uv);uv.registerClass(wv);var bv=gr,Sv=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return n(e,t),e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return mv(t,this._extent)},e.prototype.normalize=function(t){return _v(t,this._extent)},e.prototype.scale=function(t){return xv(t,this._extent)},e.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},e.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},e.prototype.getInterval=function(){return this._interval},e.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=yv(t)},e.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(!e)return o;n[0]<i[0]&&(t?o.push({value:bv(i[0]-e,r)}):o.push({value:n[0]}));for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=bv(a+e,r))!==o[o.length-1].value);)if(o.length>1e4)return[];var s=o.length?o[o.length-1].value:i[1];return n[1]>s&&(t?o.push({value:bv(s+e,r)}):o.push({value:n[1]})),o},e.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=bv(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},e.prototype.getLabel=function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=yr(t.value)||0:"auto"===n&&(n=this._intervalPrecision),Xh(bv(t.value,n,!0))},e.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var o=dv(i,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},e.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=Math.abs(e[0]);t.fixMax||(e[1]+=n/2),e[0]-=n/2}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=bv(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=bv(Math.ceil(e[1]/r)*r))},e.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},e.type="interval",e}(uv);uv.registerClass(Sv);var Mv="undefined"!=typeof Float32Array,Tv=Mv?Float32Array:Array;function Cv(t){return H(t)?Mv?new Float32Array(t):t:new Tv(t)}function Dv(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function Iv(t){return t.dim+t.index}function kv(t,e){var n=[];return e.eachSeriesByType(t,(function(t){Pv(t)&&n.push(t)})),n}function Av(t){var e=function(t){var e={};R(t,(function(t){var n=t.coordinateSystem.getBaseAxis();if("time"===n.type||"value"===n.type)for(var i=t.getData(),r=n.dim+"_"+n.index,o=i.getDimensionIndex(i.mapDimension(n.dim)),a=i.getStore(),s=0,l=a.count();s<l;++s){var u=a.get(o,s);e[r]?e[r].push(u):e[r]=[u]}}));var n={};for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(r){r.sort((function(t,e){return t-e}));for(var o=null,a=1;a<r.length;++a){var s=r[a]-r[a-1];s>0&&(o=null===o?s:Math.min(o,s))}n[i]=o}}return n}(t),n=[];return R(t,(function(t){var i,r=t.coordinateSystem.getBaseAxis(),o=r.getExtent();if("category"===r.type)i=r.getBandWidth();else if("value"===r.type||"time"===r.type){var a=r.dim+"_"+r.index,s=e[a],l=Math.abs(o[1]-o[0]),u=r.scale.getExtent(),h=Math.abs(u[1]-u[0]);i=s?l/h*s:l}else{var c=t.getData();i=Math.abs(o[1]-o[0])/c.count()}var p=dr(t.get("barWidth"),i),f=dr(t.get("barMaxWidth"),i),d=dr(t.get("barMinWidth")||(Ov(t)?.5:1),i),g=t.get("barGap"),y=t.get("barCategoryGap");n.push({bandWidth:i,barWidth:p,barMaxWidth:f,barMinWidth:d,barGap:g,barCategoryGap:y,axisKey:Iv(r),stackId:Dv(t)})})),function(t){var e={};R(t,(function(t,n){var i=t.axisKey,r=t.bandWidth,o=e[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},a=o.stacks;e[i]=o;var s=t.stackId;a[s]||o.autoWidthCount++,a[s]=a[s]||{width:0,maxWidth:0};var l=t.barWidth;l&&!a[s].width&&(a[s].width=l,l=Math.min(o.remainedWidth,l),o.remainedWidth-=l);var u=t.barMaxWidth;u&&(a[s].maxWidth=u);var h=t.barMinWidth;h&&(a[s].minWidth=h);var c=t.barGap;null!=c&&(o.gap=c);var p=t.barCategoryGap;null!=p&&(o.categoryGap=p)}));var n={};return R(e,(function(t,e){n[e]={};var i=t.stacks,r=t.bandWidth,o=t.categoryGap;if(null==o){var a=B(i).length;o=Math.max(35-4*a,15)+"%"}var s=dr(o,r),l=dr(t.gap,1),u=t.remainedWidth,h=t.autoWidthCount,c=(u-s)/(h+(h-1)*l);c=Math.max(c,0),R(i,(function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){i=t.width;e&&(i=Math.min(i,e)),n&&(i=Math.max(i,n)),t.width=i,u-=i+l*i,h--}else{var i=c;e&&e<i&&(i=Math.min(e,u)),n&&n>i&&(i=n),i!==c&&(t.width=i,u-=i+l*i,h--)}})),c=(u-s)/(h+(h-1)*l),c=Math.max(c,0);var p,f=0;R(i,(function(t,e){t.width||(t.width=c),p=t,f+=t.width*(1+l)})),p&&(f-=p.width*l);var d=-f/2;R(i,(function(t,i){n[e][i]=n[e][i]||{bandWidth:r,offset:d,width:t.width},d+=t.width*(1+l)}))})),n}(n)}function Lv(t,e){var n=kv(t,e),i=Av(n);R(n,(function(t){var e=t.getData(),n=t.coordinateSystem.getBaseAxis(),r=Dv(t),o=i[Iv(n)][r],a=o.offset,s=o.width;e.setLayout({bandWidth:o.bandWidth,offset:a,size:s})}))}function Pv(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function Ov(t){return t.pipelineContext&&t.pipelineContext.large}var Rv=function(t){function e(e){var n=t.call(this,e)||this;return n.type="time",n}return n(e,t),e.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return Ih(t.value,bh[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(Ch(this._minLevelUnit))]||bh.second,e,this.getSetting("locale"))},e.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC");return function(t,e,n,i,r){var o=null;if(G(n))o=n;else if(W(n))o=n(t.value,e,{level:t.level});else{var a=D({},xh);if(t.level>0)for(var s=0;s<Sh.length;++s)a[Sh[s]]="{primary|"+a[Sh[s]]+"}";var l=n?!1===n.inherit?n:I(n,a):a,u=kh(t.value,r);if(l[u])o=l[u];else if(l.inherit){for(s=Mh.indexOf(u)-1;s>=0;--s)if(l[u]){o=l[u];break}o=o||a.none}if(H(o)){var h=null==t.level?0:t.level>=0?t.level:o.length+t.level;o=o[h=Math.min(h,o.length-1)]}}return Ih(new Date(t.value),o,r,i)}(t,e,n,this.getSetting("locale"),i)},e.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];if(!t)return n;n.push({value:e[0],level:0});var i=this.getSetting("useUTC"),r=function(t,e,n,i){var r=1e4,o=Mh,a=0;function s(t,e,n,r,o,a,s){for(var l=new Date(e),u=e,h=l[r]();u<n&&u<=i[1];)s.push({value:u}),h+=t,l[o](h),u=l.getTime();s.push({value:u,notAdd:!0})}function l(t,r,o){var a=[],l=!r.length;if(!function(t,e,n,i){var r=Mr(e),o=Mr(n),a=function(t){return Ah(r,t,i)===Ah(o,t,i)},s=function(){return a("year")},l=function(){return s()&&a("month")},u=function(){return l()&&a("day")},h=function(){return u()&&a("hour")},c=function(){return h()&&a("minute")},p=function(){return c()&&a("second")},f=function(){return p()&&a("millisecond")};switch(t){case"year":return s();case"month":return l();case"day":return u();case"hour":return h();case"minute":return c();case"second":return p();case"millisecond":return f()}}(Ch(t),i[0],i[1],n)){l&&(r=[{value:Hv(new Date(i[0]),t,n)},{value:i[1]}]);for(var u=0;u<r.length-1;u++){var h=r[u].value,c=r[u+1].value;if(h!==c){var p=void 0,f=void 0,d=void 0,g=!1;switch(t){case"year":p=Math.max(1,Math.round(e/mh/365)),f=Lh(n),d=Bh(n);break;case"half-year":case"quarter":case"month":p=zv(e),f=Ph(n),d=Fh(n);break;case"week":case"half-week":case"day":p=Ev(e),f=Oh(n),d=Vh(n),g=!0;break;case"half-day":case"quarter-day":case"hour":p=Bv(e),f=Rh(n),d=Hh(n);break;case"minute":p=Fv(e,!0),f=Nh(n),d=Wh(n);break;case"second":p=Fv(e,!1),f=Eh(n),d=Gh(n);break;case"millisecond":p=Vv(e),f=zh(n),d=Uh(n)}s(p,h,c,f,d,g,a),"year"===t&&o.length>1&&0===u&&o.unshift({value:o[0].value-p})}}for(u=0;u<a.length;u++)o.push(a[u]);return a}}for(var u=[],h=[],c=0,p=0,f=0;f<o.length&&a++<r;++f){var d=Ch(o[f]);if(Dh(o[f]))if(l(o[f],u[u.length-1]||[],h),d!==(o[f+1]?Ch(o[f+1]):null)){if(h.length){p=c,h.sort((function(t,e){return t.value-e.value}));for(var g=[],y=0;y<h.length;++y){var v=h[y].value;0!==y&&h[y-1].value===v||(g.push(h[y]),v>=i[0]&&v<=i[1]&&c++)}var m=(i[1]-i[0])/e;if(c>1.5*m&&p>m/1.5)break;if(u.push(g),c>m||t===o[f])break}h=[]}}0;var _=z(N(u,(function(t){return z(t,(function(t){return t.value>=i[0]&&t.value<=i[1]&&!t.notAdd}))})),(function(t){return t.length>0})),x=[],w=_.length-1;for(f=0;f<_.length;++f)for(var b=_[f],S=0;S<b.length;++S)x.push({value:b[S].value,level:w-f});x.sort((function(t,e){return t.value-e.value}));var M=[];for(f=0;f<x.length;++f)0!==f&&x[f].value===x[f-1].value||M.push(x[f]);return M}(this._minLevelUnit,this._approxInterval,i,e);return(n=n.concat(r)).push({value:e[1],level:0}),n},e.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=mh,e[1]+=mh),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-mh}this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},e.prototype.calcNiceTicks=function(t,e,n){t=t||10;var i=this._extent,r=i[1]-i[0];this._approxInterval=r/t,null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n);var o=Nv.length,a=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n}(Nv,this._approxInterval,0,o),o-1);this._interval=Nv[a][1],this._minLevelUnit=Nv[Math.max(a-1,0)][0]},e.prototype.parse=function(t){return X(t)?t:+Mr(t)},e.prototype.contain=function(t){return mv(this.parse(t),this._extent)},e.prototype.normalize=function(t){return _v(this.parse(t),this._extent)},e.prototype.scale=function(t){return xv(t,this._extent)},e.type="time",e}(Sv),Nv=[["second",gh],["minute",yh],["hour",vh],["quarter-day",216e5],["half-day",432e5],["day",10368e4],["half-week",3024e5],["week",6048e5],["month",26784e5],["quarter",8208e6],["half-year",_h/2],["year",_h]];function Ev(t,e){return(t/=mh)>16?16:t>7.5?7:t>3.5?4:t>1.5?2:1}function zv(t){return(t/=2592e6)>6?6:t>3?3:t>2?2:1}function Bv(t){return(t/=vh)>12?12:t>6?6:t>3.5?4:t>2?2:1}function Fv(t,e){return(t/=e?yh:gh)>30?30:t>20?20:t>15?15:t>10?10:t>5?5:t>2?2:1}function Vv(t){return Dr(t,!0)}function Hv(t,e,n){var i=new Date(t);switch(Ch(e)){case"year":case"month":i[Fh(n)](0);case"day":i[Vh(n)](1);case"hour":i[Hh(n)](0);case"minute":i[Wh(n)](0);case"second":i[Gh(n)](0),i[Uh(n)](0)}return i.getTime()}uv.registerClass(Rv);var Wv=uv.prototype,Gv=Sv.prototype,Uv=gr,Xv=Math.floor,Yv=Math.ceil,qv=Math.pow,Zv=Math.log,jv=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new Sv,e._interval=0,e}return n(e,t),e.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return N(Gv.getTicks.call(this,t),(function(t){var e=t.value,r=gr(qv(this.base,e));return r=e===n[0]&&this._fixMin?$v(r,i[0]):r,{value:r=e===n[1]&&this._fixMax?$v(r,i[1]):r}}),this)},e.prototype.setExtent=function(t,e){var n=Zv(this.base);t=Zv(Math.max(0,t))/n,e=Zv(Math.max(0,e))/n,Gv.setExtent.call(this,t,e)},e.prototype.getExtent=function(){var t=this.base,e=Wv.getExtent.call(this);e[0]=qv(t,e[0]),e[1]=qv(t,e[1]);var n=this._originalScale.getExtent();return this._fixMin&&(e[0]=$v(e[0],n[0])),this._fixMax&&(e[1]=$v(e[1],n[1])),e},e.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=Zv(t[0])/Zv(e),t[1]=Zv(t[1])/Zv(e),Wv.unionExtent.call(this,t)},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n===1/0||n<=0)){var i=Tr(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0;)i*=10;var r=[gr(Yv(e[0]/i)*i),gr(Xv(e[1]/i)*i)];this._interval=i,this._niceExtent=r}},e.prototype.calcNiceExtent=function(t){Gv.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return mv(t=Zv(t)/Zv(this.base),this._extent)},e.prototype.normalize=function(t){return _v(t=Zv(t)/Zv(this.base),this._extent)},e.prototype.scale=function(t){return t=xv(t,this._extent),qv(this.base,t)},e.type="log",e}(uv),Kv=jv.prototype;function $v(t,e){return Uv(t,yr(e))}Kv.getMinorTicks=Gv.getMinorTicks,Kv.getLabel=Gv.getLabel,uv.registerClass(jv);var Qv=function(){function t(t,e,n){this._prepareParams(t,e,n)}return t.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type;this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero();var r=this._modelMinRaw=e.get("min",!0);W(r)?this._modelMinNum=em(t,r({min:n[0],max:n[1]})):"dataMin"!==r&&(this._modelMinNum=em(t,r));var o=this._modelMaxRaw=e.get("max",!0);if(W(o)?this._modelMaxNum=em(t,o({min:n[0],max:n[1]})):"dataMax"!==o&&(this._modelMaxNum=em(t,o)),i)this._axisDataLen=e.getCategories().length;else{var a=e.get("boundaryGap"),s=H(a)?a:[a||0,a||0];"boolean"==typeof s[0]||"boolean"==typeof s[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[qi(s[0],1),qi(s[1],1)]}},t.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s;null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),(null==a||!isFinite(a))&&(a=NaN),(null==s||!isFinite(s))&&(s=NaN);var h=J(a)||J(s)||t&&!i;this._needCrossZero&&(a>0&&s>0&&!l&&(a=0),a<0&&s<0&&!u&&(s=0));var c=this._determinedMin,p=this._determinedMax;return null!=c&&(a=c,l=!0),null!=p&&(s=p,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:h}},t.prototype.modifyDataMinMax=function(t,e){this[tm[t]]=e},t.prototype.setDeterminedMinMax=function(t,e){var n=Jv[t];this[n]=e},t.prototype.freeze=function(){this.frozen=!0},t}(),Jv={min:"_determinedMin",max:"_determinedMax"},tm={min:"_dataMin",max:"_dataMax"};function em(t,e){return null==e?null:J(e)?NaN:t.parse(e)}function nm(t,e){var n=t.type,i=function(t,e,n){var i=t.rawExtentInfo;return i||(i=new Qv(t,e,n),t.rawExtentInfo=i,i)}(t,e,t.getExtent()).calculate();t.setBlank(i.isBlank);var r=i.min,o=i.max,a=e.ecModel;if(a&&"time"===n){var s=kv("bar",a),l=!1;if(R(s,(function(t){l=l||t.getBaseAxis()===e.axis})),l){var u=Av(s),h=function(t,e,n,i){var r=n.axis.getExtent(),o=r[1]-r[0],a=function(t,e,n){if(t&&e){var i=t[Iv(e)];return null!=i&&null!=n?i[Dv(n)]:i}}(i,n.axis);if(void 0===a)return{min:t,max:e};var s=1/0;R(a,(function(t){s=Math.min(t.offset,s)}));var l=-1/0;R(a,(function(t){l=Math.max(t.offset+t.width,l)})),s=Math.abs(s),l=Math.abs(l);var u=s+l,h=e-t,c=h/(1-(s+l)/o)-h;return e+=c*(l/u),t-=c*(s/u),{min:t,max:e}}(r,o,e,u);r=h.min,o=h.max}}return{extent:[r,o],fixMin:i.minFixed,fixMax:i.maxFixed}}function im(t,e){var n=e,i=nm(t,n),r=i.extent,o=n.get("splitNumber");t instanceof jv&&(t.base=n.get("logBase"));var a=t.type,s=n.get("interval"),l="interval"===a||"time"===a;t.setExtent(r[0],r[1]),t.calcNiceExtent({splitNumber:o,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:l?n.get("minInterval"):null,maxInterval:l?n.get("maxInterval"):null}),null!=s&&t.setInterval&&t.setInterval(s)}function rm(t,e){if(e=e||t.get("type"))switch(e){case"category":return new wv({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new Rv({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(uv.getClass(e)||Sv)}}function om(t){var e,n,i=t.getLabelModel().get("formatter"),r="category"===t.type?t.scale.getExtent()[0]:null;return"time"===t.scale.type?(n=i,function(e,i){return t.scale.getFormattedLabel(e,i,n)}):G(i)?function(e){return function(n){var i=t.scale.getLabel(n);return e.replace("{value}",null!=i?i:"")}}(i):W(i)?(e=i,function(n,i){return null!=r&&(i=n.value-r),e(function(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}(t,n),i,null!=n.level?{level:n.level}:null)}):function(e){return t.scale.getLabel(e)}}function am(t,e){var n=e*Math.PI/180,i=t.width,r=t.height,o=i*Math.abs(Math.cos(n))+Math.abs(r*Math.sin(n)),a=i*Math.abs(Math.sin(n))+Math.abs(r*Math.cos(n));return new Ce(t.x,t.y,o,a)}function sm(t){var e=t.get("interval");return null==e?"auto":e}function lm(t){return"category"===t.type&&0===sm(t.getLabelModel())}var um=function(){function t(){}return t.prototype.getNeedCrossZero=function(){return!this.option.scale},t.prototype.getCoordSysModel=function(){},t}();var hm={isDimensionStacked:av,enableDataStack:ov,getStackedDimension:sv};var cm=Object.freeze({__proto__:null,createList:function(t){return lv(null,t)},getLayoutRect:ec,dataStack:hm,createScale:function(t,e){var n=e;e instanceof oh||(n=new oh(e));var i=rm(n);return i.setExtent(t[0],t[1]),im(i,n),i},mixinAxisModelCommonMethods:function(t){P(t,um)},getECData:ls,createTextStyle:function(t,e){return Vu(t,null,null,"normal"!==(e=e||{}).state)},createDimensions:function(t,e){return tv(t,e).dimensions},createSymbol:Nd,enableHoverEmphasis:js});function pm(t,e){return Math.abs(t-e)<1e-8}function fm(t,e,n){var i=0,r=t[0];if(!r)return!1;for(var o=1;o<t.length;o++){var a=t[o];i+=Ma(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return pm(r[0],s[0])&&pm(r[1],s[1])||(i+=Ma(r[0],r[1],s[0],s[1],e,n)),0!==i}var dm=[];function gm(t,e){for(var n=0;n<t.length;n++)Et(t[n],t[n],e)}function ym(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];i&&(o=i.project(o)),o&&isFinite(o[0])&&isFinite(o[1])&&(zt(e,e,o),Bt(n,n,o))}}var vm=function(){function t(t){this.name=t}return t.prototype.setCenter=function(t){this._center=t},t.prototype.getCenter=function(){var t=this._center;return t||(t=this._center=this.calcCenter()),t},t}(),mm=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},_m=function(t){this.type="linestring",this.points=t},xm=function(t){function e(e,n,i){var r=t.call(this,e)||this;return r.type="geoJSON",r.geometries=n,r._center=i&&[i[0],i[1]],r}return n(e,t),e.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior,a=o&&o.length;a>n&&(t=r,n=a)}if(t)return function(t){for(var e=0,n=0,i=0,r=t.length,o=t[r-1][0],a=t[r-1][1],s=0;s<r;s++){var l=t[s][0],u=t[s][1],h=o*u-l*a;e+=h,n+=(o+l)*h,i+=(a+u)*h,o=l,a=u}return e?[n/e/3,i/e/3,e]:[t[0][0]||0,t[0][1]||0]}(t.exterior);var s=this.getBoundingRect();return[s.x+s.width/2,s.y+s.height/2]},e.prototype.getBoundingRect=function(t){var e=this._rect;if(e&&!t)return e;var n=[1/0,1/0],i=[-1/0,-1/0];return R(this.geometries,(function(e){"polygon"===e.type?ym(e.exterior,n,i,t):R(e.points,(function(e){ym(e,n,i,t)}))})),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),e=new Ce(n[0],n[1],i[0]-n[0],i[1]-n[1]),t||(this._rect=e),e},e.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(fm(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(fm(s[l],t[0],t[1]))continue t;return!0}}}return!1},e.prototype.transformTo=function(t,e,n,i){var r=this.getBoundingRect(),o=r.width/r.height;n?i||(i=n/o):n=o*i;for(var a=new Ce(t,e,n,i),s=r.calculateTransform(a),l=this.geometries,u=0;u<l.length;u++){var h=l[u];"polygon"===h.type?(gm(h.exterior,s),R(h.interiors,(function(t){gm(t,s)}))):R(h.points,(function(t){gm(t,s)}))}(r=this._rect).copy(a),this._center=[r.x+r.width/2,r.y+r.height/2]},e.prototype.cloneShallow=function(t){null==t&&(t=this.name);var n=new e(t,this.geometries,this._center);return n._rect=this._rect,n.transformTo=null,n},e}(vm);!function(t){function e(e,n){var i=t.call(this,e)||this;return i.type="geoSVG",i._elOnlyForCalculate=n,i}n(e,t),e.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),n=[e.x+e.width/2,e.y+e.height/2],i=ue(dm),r=t;r&&!r.isGeoSVGGraphicRoot;)ce(i,r.getLocalTransform(),i),r=r.parent;return ge(i,i),Et(n,n,i),n}}(vm);function wm(t,e,n){for(var i=0;i<t.length;i++)t[i]=bm(t[i],e[i],n)}function bm(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=t.charCodeAt(a)-64,l=t.charCodeAt(a+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),r=s+=r,o=l+=o,i.push([s/n,l/n])}return i}function Sm(t,e){return N(z((t=function(t){if(!t.UTF8Encoding)return t;var e=t,n=e.UTF8Scale;return null==n&&(n=1024),R(e.features,(function(t){var e=t.geometry,i=e.encodeOffsets,r=e.coordinates;if(i)switch(e.type){case"LineString":e.coordinates=bm(r,i,n);break;case"Polygon":case"MultiLineString":wm(r,i,n);break;case"MultiPolygon":R(r,(function(t,e){return wm(t,i[e],n)}))}})),e.UTF8Encoding=!1,e}(t)).features,(function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0})),(function(t){var n=t.properties,i=t.geometry,r=[];switch(i.type){case"Polygon":var o=i.coordinates;r.push(new mm(o[0],o.slice(1)));break;case"MultiPolygon":R(i.coordinates,(function(t){t[0]&&r.push(new mm(t[0],t.slice(1)))}));break;case"LineString":r.push(new _m([i.coordinates]));break;case"MultiLineString":r.push(new _m(i.coordinates))}var a=new xm(n[e||"name"],r,n.cp);return a.properties=n,a}))}var Mm=Object.freeze({__proto__:null,linearMap:fr,round:gr,asc:function(t){return t.sort((function(t,e){return t-e})),t},getPrecision:yr,getPrecisionSafe:vr,getPixelPrecision:mr,getPercentWithPrecision:function(t,e,n){return t[e]&&_r(t,n)[e]||0},MAX_SAFE_INTEGER:9007199254740991,remRadian:wr,isRadianAroundZero:br,parseDate:Mr,quantity:Tr,quantityExponent:Cr,nice:Dr,quantile:function(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],o=n-i;return o?r+o*(t[i]-r):r},reformIntervals:function(t){t.sort((function(t,e){return s(t,e,0)?-1:1}));for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t;function s(t,e,n){return t.interval[n]<e.interval[n]||t.interval[n]===e.interval[n]&&(t.close[n]-e.close[n]==(n?-1:1)||!n&&s(t,e,1))}},isNumeric:kr,numericToNumber:Ir}),Tm=Object.freeze({__proto__:null,parse:Mr,format:Ih}),Cm=Object.freeze({__proto__:null,extendShape:function(t){return Ea.extend(t)},extendPath:function(t,e){return Tu(t,e)},makePath:Du,makeImage:Iu,mergePath:Au,resizePath:Lu,createIcon:function(t,e,n){var i=D({rectHover:!0},e),r=i.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(r.image=t.slice(8),I(r,n),new Ha(i)):Du(t.replace("path://",""),i,n,"center")},updateProps:gu,initProps:yu,getTransform:function(t,e){for(var n=ue([]);t&&t!==e;)ce(n,t.getLocalTransform(),n),t=t.parent;return n},clipPointsByRect:function(t,e){return N(t,(function(t){var n=t[0];n=bu(n,e.x),n=Su(n,e.x+e.width);var i=t[1];return i=bu(i,e.y),[n,i=Su(i,e.y+e.height)]}))},clipRectByRect:function(t,e){var n=bu(t.x,e.x),i=Su(t.x+t.width,e.x+e.width),r=bu(t.y,e.y),o=Su(t.y+t.height,e.y+e.height);if(i>=n&&o>=r)return{x:n,y:r,width:i-n,height:o-r}},registerShape:Cu,getShapeClass:function(t){if(Mu.hasOwnProperty(t))return Mu[t]},Group:or,Image:Ha,Text:Ka,Circle:bl,Ellipse:Ml,Sector:Fl,Ring:Hl,Polygon:Ul,Polyline:Yl,Rect:qa,Line:jl,BezierCurve:Jl,Arc:eu,IncrementalDisplayable:pu,CompoundPath:nu,LinearGradient:ru,RadialGradient:ou,BoundingRect:Ce}),Dm=Object.freeze({__proto__:null,addCommas:Xh,toCamelCase:function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,(function(t,e){return e.toUpperCase()})),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t},normalizeCssArray:Yh,encodeHTML:$t,formatTpl:jh,getTooltipMarker:function(t,e){var n=G(t)?{color:t,extraCssText:e}:t||{},i=n.color,r=n.type;e=n.extraCssText;var o=n.renderMode||"html";return i?"html"===o?"subItem"===r?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+$t(i)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+$t(i)+";"+(e||"")+'"></span>':{renderMode:o,content:"{"+(n.markerId||"markerX")+"|}  ",style:"subItem"===r?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}:""},formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=Mr(e),r=n?"getUTC":"get",o=i[r+"FullYear"](),a=i[r+"Month"]()+1,s=i[r+"Date"](),l=i[r+"Hours"](),u=i[r+"Minutes"](),h=i[r+"Seconds"](),c=i[r+"Milliseconds"]();return t=t.replace("MM",Th(a,2)).replace("M",a).replace("yyyy",o).replace("yy",Th(o%100+"",2)).replace("dd",Th(s,2)).replace("d",s).replace("hh",Th(l,2)).replace("h",l).replace("mm",Th(u,2)).replace("m",u).replace("ss",Th(h,2)).replace("s",h).replace("SSS",Th(c,3))},capitalFirst:function(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t},truncateText:xo,getTextRect:function(t,e,n,i,r,o,a,s){return new Ka({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()}}),Im=Object.freeze({__proto__:null,map:N,each:R,indexOf:A,inherits:L,reduce:E,filter:z,bind:F,curry:V,isArray:H,isString:G,isObject:Y,isFunction:W,extend:D,defaults:I,clone:T,merge:C}),km=Zr();function Am(t){return"category"===t.type?function(t){var e=t.getLabelModel(),n=Pm(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:n.labelCategoryInterval}:n}(t):function(t){var e=t.scale.getTicks(),n=om(t);return{labels:N(e,(function(e,i){return{level:e.level,formattedLabel:n(e,i),rawLabel:t.scale.getLabel(e),tickValue:e.value}}))}}(t)}function Lm(t,e){return"category"===t.type?function(t,e){var n,i,r=Om(t,"ticks"),o=sm(e),a=Rm(r,o);if(a)return a;e.get("show")&&!t.scale.isBlank()||(n=[]);if(W(o))n=zm(t,o,!0);else if("auto"===o){var s=Pm(t,t.getLabelModel());i=s.labelCategoryInterval,n=N(s.labels,(function(t){return t.tickValue}))}else n=Em(t,i=o,!0);return Nm(r,o,{ticks:n,tickCategoryInterval:i})}(t,e):{ticks:N(t.scale.getTicks(),(function(t){return t.value}))}}function Pm(t,e){var n,i,r=Om(t,"labels"),o=sm(e),a=Rm(r,o);return a||(W(o)?n=zm(t,o):(i="auto"===o?function(t){var e=km(t).autoInterval;return null!=e?e:km(t).autoInterval=t.calculateCategoryInterval()}(t):o,n=Em(t,i)),Nm(r,o,{labels:n,labelCategoryInterval:i}))}function Om(t,e){return km(t)[e]||(km(t)[e]=[])}function Rm(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function Nm(t,e,n){return t.push({key:e,value:n}),n}function Em(t,e,n){var i=om(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=o[0],h=r.count();0!==u&&l>1&&h/l>2&&(u=Math.round(Math.ceil(u/l)*l));var c=lm(t),p=a.get("showMinLabel")||c,f=a.get("showMaxLabel")||c;p&&u!==o[0]&&g(o[0]);for(var d=u;d<=o[1];d+=l)g(d);function g(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return f&&d-l!==o[1]&&g(o[1]),s}function zm(t,e,n){var i=t.scale,r=om(t),o=[];return R(i.getTicks(),(function(t){var a=i.getLabel(t),s=t.value;e(t.value,a)&&o.push(n?s:{formattedLabel:r(t),rawLabel:a,tickValue:s})})),o}var Bm=[0,1],Fm=function(){function t(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}return t.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},t.prototype.containData=function(t){return this.scale.contain(t)},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.getPixelPrecision=function(t){return mr(t||this.scale.getExtent(),this._extent)},t.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},t.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&Vm(n=n.slice(),i.count()),fr(t,Bm,n,e)},t.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&Vm(n=n.slice(),i.count());var r=fr(t,n,Bm,e);return this.scale.scale(r)},t.prototype.pointToData=function(t,e){},t.prototype.getTicksCoords=function(t){var e=(t=t||{}).tickModel||this.getTickModel(),n=N(Lm(this,e).ticks,(function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}}),this);return function(t,e,n,i){var r=e.length;if(!t.onBand||n||!r)return;var o,a,s=t.getExtent();if(1===r)e[0].coord=s[0],o=e[1]={coord:s[1]};else{var l=e[r-1].tickValue-e[0].tickValue,u=(e[r-1].coord-e[0].coord)/l;R(e,(function(t){t.coord-=u/2})),a=1+t.scale.getExtent()[1]-e[r-1].tickValue,o={coord:e[r-1].coord+u*a},e.push(o)}var h=s[0]>s[1];c(e[0].coord,s[0])&&(i?e[0].coord=s[0]:e.shift());i&&c(s[0],e[0].coord)&&e.unshift({coord:s[0]});c(s[1],o.coord)&&(i?o.coord=s[1]:e.pop());i&&c(o.coord,s[1])&&e.push({coord:s[1]});function c(t,e){return t=gr(t),e=gr(e),h?t>e:t<e}}(this,n,e.get("alignWithLabel"),t.clamp),n},t.prototype.getMinorTicksCoords=function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick").get("splitNumber");return t>0&&t<100||(t=5),N(this.scale.getMinorTicks(t),(function(t){return N(t,(function(t){return{coord:this.dataToCoord(t),tickValue:t}}),this)}),this)},t.prototype.getViewLabels=function(){return Am(this).labels},t.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},t.prototype.getTickModel=function(){return this.model.getModel("axisTick")},t.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},t.prototype.calculateCategoryInterval=function(){return function(t){var e=function(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}(t),n=om(t),i=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,o=r.getExtent(),a=r.count();if(o[1]-o[0]<1)return 0;var s=1;a>40&&(s=Math.max(1,Math.floor(a/40)));for(var l=o[0],u=t.dataToCoord(l+1)-t.dataToCoord(l),h=Math.abs(u*Math.cos(i)),c=Math.abs(u*Math.sin(i)),p=0,f=0;l<=o[1];l+=s){var d,g,y=Gi(n({value:l}),e.font,"center","top");d=1.3*y.width,g=1.3*y.height,p=Math.max(p,d,7),f=Math.max(f,g,7)}var v=p/h,m=f/c;isNaN(v)&&(v=1/0),isNaN(m)&&(m=1/0);var _=Math.max(0,Math.floor(Math.min(v,m))),x=km(t.model),w=t.getExtent(),b=x.lastAutoInterval,S=x.lastTickCount;return null!=b&&null!=S&&Math.abs(b-_)<=1&&Math.abs(S-a)<=1&&b>_&&x.axisExtent0===w[0]&&x.axisExtent1===w[1]?_=b:(x.lastTickCount=a,x.lastAutoInterval=_,x.axisExtent0=w[0],x.axisExtent1=w[1]),_}(this)},t}();function Vm(t,e){var n=(t[1]-t[0])/e/2;t[0]+=n,t[1]-=n}function Hm(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,h=n-t,c=i-e,p=Math.sqrt(h*h+c*c),f=(l*(h/=p)+u*(c/=p))/p;s&&(f=Math.min(Math.max(f,0),1)),f*=p;var d=a[0]=t+f*h,g=a[1]=e+f*c;return Math.sqrt((d-r)*(d-r)+(g-o)*(g-o))}var Wm=new ve,Gm=new ve,Um=new ve,Xm=new ve,Ym=new ve,qm=[],Zm=new ve;function jm(t,e){if(e<=180&&e>0){e=e/180*Math.PI,Wm.fromArray(t[0]),Gm.fromArray(t[1]),Um.fromArray(t[2]),ve.sub(Xm,Wm,Gm),ve.sub(Ym,Um,Gm);var n=Xm.len(),i=Ym.len();if(!(n<.001||i<.001)){Xm.scale(1/n),Ym.scale(1/i);var r=Xm.dot(Ym);if(Math.cos(e)<r){var o=Hm(Gm.x,Gm.y,Um.x,Um.y,Wm.x,Wm.y,qm,!1);Zm.fromArray(qm),Zm.scaleAndAdd(Ym,o/Math.tan(Math.PI-e));var a=Um.x!==Gm.x?(Zm.x-Gm.x)/(Um.x-Gm.x):(Zm.y-Gm.y)/(Um.y-Gm.y);if(isNaN(a))return;a<0?ve.copy(Zm,Gm):a>1&&ve.copy(Zm,Um),Zm.toArray(t[1])}}}}function Km(t,e,n){if(n<=180&&n>0){n=n/180*Math.PI,Wm.fromArray(t[0]),Gm.fromArray(t[1]),Um.fromArray(t[2]),ve.sub(Xm,Gm,Wm),ve.sub(Ym,Um,Gm);var i=Xm.len(),r=Ym.len();if(!(i<.001||r<.001))if(Xm.scale(1/i),Ym.scale(1/r),Xm.dot(e)<Math.cos(n)){var o=Hm(Gm.x,Gm.y,Um.x,Um.y,Wm.x,Wm.y,qm,!1);Zm.fromArray(qm);var a=Math.PI/2,s=a+Math.acos(Ym.dot(e))-n;if(s>=a)ve.copy(Zm,Um);else{Zm.scaleAndAdd(Ym,o/Math.tan(Math.PI/2-s));var l=Um.x!==Gm.x?(Zm.x-Gm.x)/(Um.x-Gm.x):(Zm.y-Gm.y)/(Um.y-Gm.y);if(isNaN(l))return;l<0?ve.copy(Zm,Gm):l>1&&ve.copy(Zm,Um)}Zm.toArray(t[1])}}}function $m(t,e,n,i){var r="normal"===n,o=r?t:t.ensureState(n);o.ignore=e;var a=i.get("smooth");a&&!0===a&&(a=.3),o.shape=o.shape||{},a>0&&(o.shape.smooth=a);var s=i.getModel("lineStyle").getLineStyle();r?t.useStyle(s):o.style=s}function Qm(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),n>0&&i.length>=3){var r=Pt(i[0],i[1]),o=Pt(i[1],i[2]);if(!r||!o)return t.lineTo(i[1][0],i[1][1]),void t.lineTo(i[2][0],i[2][1]);var a=Math.min(r,o)*n,s=Nt([],i[1],i[0],a/r),l=Nt([],i[1],i[2],a/o),u=Nt([],s,l,.5);t.bezierCurveTo(s[0],s[1],s[0],s[1],u[0],u[1]),t.bezierCurveTo(l[0],l[1],l[0],l[1],i[2][0],i[2][1])}else for(var h=1;h<i.length;h++)t.lineTo(i[h][0],i[h][1])}function Jm(t,e,n,i){return function(t,e,n,i,r,o){var a=t.length;if(!(a<2)){t.sort((function(t,n){return t.rect[e]-n.rect[e]}));for(var s,l=0,u=!1,h=0,c=0;c<a;c++){var p=t[c],f=p.rect;(s=f[e]-l)<0&&(f[e]-=s,p.label[e]-=s,u=!0),h+=Math.max(-s,0),l=f[e]+f[n]}h>0&&o&&x(-h/a,0,a);var d,g,y=t[0],v=t[a-1];return m(),d<0&&w(-d,.8),g<0&&w(g,.8),m(),_(d,g,1),_(g,d,-1),m(),d<0&&b(-d),g<0&&b(g),u}function m(){d=y.rect[e]-i,g=r-v.rect[e]-v.rect[n]}function _(t,e,n){if(t<0){var i=Math.min(e,-t);if(i>0){x(i*n,0,a);var r=i+t;r<0&&w(-r*n,1)}else w(-t*n,1)}}function x(n,i,r){0!==n&&(u=!0);for(var o=i;o<r;o++){var a=t[o];a.rect[e]+=n,a.label[e]+=n}}function w(i,r){for(var o=[],s=0,l=1;l<a;l++){var u=t[l-1].rect,h=Math.max(t[l].rect[e]-u[e]-u[n],0);o.push(h),s+=h}if(s){var c=Math.min(Math.abs(i)/s,r);if(i>0)for(l=0;l<a-1;l++)x(o[l]*c,0,l+1);else for(l=a-1;l>0;l--)x(-o[l-1]*c,l,a)}}function b(t){var e=t<0?-1:1;t=Math.abs(t);for(var n=Math.ceil(t/(a-1)),i=0;i<a-1;i++)if(e>0?x(n,0,i+1):x(-n,a-i-1,a),(t-=n)<=0)return}}(t,"y","height",e,n,i)}function t_(t,e,n){var i=u.createCanvas(),r=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=r+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=o*n,i}var e_=function(t){function e(e,n,i){var r,o=t.call(this)||this;o.motionBlur=!1,o.lastFrameAlpha=.7,o.dpr=1,o.virtual=!1,o.config={},o.incremental=!1,o.zlevel=0,o.maxRepaintRectCount=5,o.__dirty=!0,o.__firstTimePaint=!0,o.__used=!1,o.__drawIndex=0,o.__startIndex=0,o.__endIndex=0,o.__prevStartIndex=null,o.__prevEndIndex=null,i=i||Ii,"string"==typeof e?r=t_(e,n,i):Y(e)&&(e=(r=e).id),o.id=e,o.dom=r;var a=r.style;return a&&(yt(r),r.onselectstart=function(){return!1},a.padding="0",a.margin="0",a.borderWidth="0"),o.painter=n,o.dpr=i,o}return n(e,t),e.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},e.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},e.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},e.prototype.setUnpainted=function(){this.__firstTimePaint=!0},e.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=t_("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},e.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var r,o=[],a=this.maxRepaintRectCount,s=!1,l=new Ce(0,0,0,0);function u(t){if(t.isFinite()&&!t.isZero())if(0===o.length){(e=new Ce(0,0,0,0)).copy(t),o.push(e)}else{for(var e,n=!1,i=1/0,r=0,u=0;u<o.length;++u){var h=o[u];if(h.intersect(t)){var c=new Ce(0,0,0,0);c.copy(h),c.union(t),o[u]=c,n=!0;break}if(s){l.copy(t),l.union(h);var p=t.width*t.height,f=h.width*h.height,d=l.width*l.height-p-f;d<i&&(i=d,r=u)}}if(s&&(o[r].union(t),n=!0),!n)(e=new Ce(0,0,0,0)).copy(t),o.push(e);s||(s=o.length>=a)}}for(var h=this.__startIndex;h<this.__endIndex;++h){if(f=t[h]){var c=f.shouldBePainted(n,i,!0,!0);(d=f.__isRendered&&(1&f.__dirty||!c)?f.getPrevPaintRect():null)&&u(d);var p=c&&(1&f.__dirty||!f.__isRendered)?f.getPaintRect():null;p&&u(p)}}for(h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var f,d;c=(f=e[h]).shouldBePainted(n,i,!0,!0);if(f&&(!c||!f.__zr)&&f.__isRendered)(d=f.getPrevPaintRect())&&u(d)}do{r=!1;for(h=0;h<o.length;)if(o[h].isZero())o.splice(h,1);else{for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(r=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(r);return this._paintRects=o,o},e.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},e.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n&&this.ctxBack.scale(n,n))},e.prototype.clear=function(t,e,n){var i=this.dom,r=this.ctx,o=i.width,a=i.height;e=e||this.clearColor;var s=this.motionBlur&&!t,l=this.lastFrameAlpha,u=this.dpr,h=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,o/u,a/u));var c=this.domBack;function p(t,n,i,o){if(r.clearRect(t,n,i,o),e&&"transparent"!==e){var a=void 0;if(K(e))a=(e.global||e.__width===i&&e.__height===o)&&e.__canvasGradient||Bd(r,e,{x:0,y:0,width:i,height:o}),e.__canvasGradient=a,e.__width=i,e.__height=o;else $(e)&&(e.scaleX=e.scaleX||u,e.scaleY=e.scaleY||u,a=Zd(r,e,{dirty:function(){h.setUnpainted(),h.__painter.refresh()}}));r.save(),r.fillStyle=a||e,r.fillRect(t,n,i,o),r.restore()}s&&(r.save(),r.globalAlpha=l,r.drawImage(c,t,n,i,o),r.restore())}!n||s?p(0,0,o,a):n.length&&R(n,(function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)}))},e}(Wt),n_=1e5,i_=314159,r_=.01;var o_=function(){function t(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=D({},n||{}),this.dpr=n.devicePixelRatio||Ii,this._singleCanvas=r,this.root=t,t.style&&(yt(t),t.innerHTML=""),this.storage=e;var o=this._zlevelList;this._prevDisplayList=[];var a=this._layers;if(r){var s=t,l=s.width,u=s.height;null!=n.width&&(l=n.width),null!=n.height&&(u=n.height),this.dpr=n.devicePixelRatio||1,s.width=l*this.dpr,s.height=u*this.dpr,this._width=l,this._height=u;var h=new e_(s,this,this.dpr);h.__builtin__=!0,h.initContext(),a[314159]=h,h.zlevel=i_,o.push(i_),this._domRoot=t}else{this._width=Vd(t,0,n),this._height=Vd(t,1,n);var c=this._domRoot=function(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}(this._width,this._height);t.appendChild(c)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o=i[r],a=this._layers[o];if(!a.__builtin__&&a.refresh){var s=0===r?this._backgroundColor:null;a.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n||(n=this._hoverlayer=this.getLayer(n_)),i||(i=n.ctx).save(),ig(i,a,r,o===e-1))}i&&i.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(n_)},t.prototype.paintOne=function(t,e){ng(t,e)},t.prototype._paintList=function(t,e,n,i){if(this._redrawId===i){n=n||!1,this._updateLayerStatus(t);var r=this._doPaintList(t,e,n),o=r.finished,a=r.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;qe((function(){s._paintList(t,e,n,i)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(i_).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)}))},t.prototype._doPaintList=function(t,e,n){for(var i=this,o=[],a=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var l=this._zlevelList[s],u=this._layers[l];u.__builtin__&&u!==this._hoverlayer&&(u.__dirty||n)&&o.push(u)}for(var h=!0,c=!1,p=function(r){var s,l=o[r],u=l.ctx,p=a&&l.createRepaintRects(t,e,f._width,f._height),d=n?l.__startIndex:l.__drawIndex,g=!n&&l.incremental&&Date.now,y=g&&Date.now(),v=l.zlevel===f._zlevelList[0]?f._backgroundColor:null;if(l.__startIndex===l.__endIndex)l.clear(!1,v,p);else if(d===l.__startIndex){var m=t[d];m.incremental&&m.notClear&&!n||l.clear(!1,v,p)}-1===d&&(console.error("For some unknown reason. drawIndex is -1"),d=l.__startIndex);var _=function(e){var n={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(s=d;s<l.__endIndex;s++){var r=t[s];if(r.__inHover&&(c=!0),i._doPaintEl(r,l,a,e,n,s===l.__endIndex-1),g)if(Date.now()-y>15)break}n.prevElClipPaths&&u.restore()};if(p)if(0===p.length)s=l.__endIndex;else for(var x=f.dpr,w=0;w<p.length;++w){var b=p[w];u.save(),u.beginPath(),u.rect(b.x*x,b.y*x,b.width*x,b.height*x),u.clip(),_(b),u.restore()}else u.save(),_(),u.restore();l.__drawIndex=s,l.__drawIndex<l.__endIndex&&(h=!1)},f=this,d=0;d<o.length;d++)p(d);return r.wxa&&R(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:h,needsRefreshHover:c}},t.prototype._doPaintEl=function(t,e,n,i,r,o){var a=e.ctx;if(n){var s=t.getPaintRect();(!i||s&&s.intersect(i))&&(ig(a,t,r,o),t.setPrevPaintRect(s))}else ig(a,t,r,o)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=i_);var n=this._layers[t];return n||((n=new e_("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?C(n,this._layerConfig[t],!0):this._layerConfig[t-r_]&&C(n,this._layerConfig[t-r_],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},t.prototype.insertLayer=function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,o=this._domRoot,a=null,s=-1;if(!n[t]&&function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(r>0&&t>i[0]){for(s=0;s<r-1&&!(i[s]<t&&i[s+1]>t);s++);a=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?o.insertBefore(e.dom,l.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.__painter=this}},t.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},t.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},t.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){o&&(o.__endIndex!==t&&(o.__dirty=!0),o.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var n=1;n<t.length;n++){if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}}var i,r,o=null,a=0;for(r=0;r<t.length;r++){var s,l=(s=t[r]).zlevel,u=void 0;i!==l&&(i=l,a=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,a=1):u=this.getLayer(l+(a>0?r_:0),this._needsManuallyCompositing),u.__builtin__||M("ZLevel "+l+" has been used by unkown layer "+u.id),u!==o&&(u.__used=!0,u.__startIndex!==r&&(u.__dirty=!0),u.__startIndex=r,u.incremental?u.__drawIndex=-1:u.__drawIndex=r,e(r),o=u),1&s.__dirty&&!s.__inHover&&(u.__dirty=!0,u.incremental&&u.__drawIndex<0&&(u.__drawIndex=r))}e(r),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,R(this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?C(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];if(r===t||r===t+r_)C(this._layers[r],n[t],!0)}}},t.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(A(n,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts,r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=Vd(r,0,i),e=Vd(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(i_).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[314159].dom;var e=new e_("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var n=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer((function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())}))}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];ig(n,u,o,s===l-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}();var a_=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.hasSymbolVisual=!0,n}return n(e,t),e.prototype.getInitialData=function(t){return lv(null,this,{useEncodeDefaulter:!0})},e.prototype.getLegendIcon=function(t){var e=new or,n=Nd("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);e.add(n),n.setStyle(t.lineStyle);var i=this.getData().getVisual("symbol"),r=this.getData().getVisual("symbolRotate"),o="none"===i?"circle":i,a=.8*t.itemHeight,s=Nd(o,(t.itemWidth-a)/2,(t.itemHeight-a)/2,a,a,t.itemStyle.fill);e.add(s),s.setStyle(t.itemStyle);var l="inherit"===t.iconRotate?r:t.iconRotate||0;return s.rotation=l*Math.PI/180,s.setOrigin([t.itemWidth/2,t.itemHeight/2]),o.indexOf("empty")>-1&&(s.style.stroke=s.style.fill,s.style.fill="#fff",s.style.lineWidth=2),e},e.type="series.line",e.dependencies=["grid","polar"],e.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},e}(xf);function s_(t,e){var n=t.mapDimensionsAll("defaultedLabel"),i=n.length;if(1===i){var r=Vp(t,e,n[0]);return null!=r?r+"":null}if(i){for(var o=[],a=0;a<n.length;a++)o.push(Vp(t,e,n[a]));return o.join(" ")}}function l_(t,e){var n=t.mapDimensionsAll("defaultedLabel");if(!H(e))return e+"";for(var i=[],r=0;r<n.length;r++){var o=t.getDimensionIndex(n[r]);o>=0&&i.push(e[o])}return i.join(" ")}var u_=function(t){function e(e,n,i,r){var o=t.call(this)||this;return o.updateData(e,n,i,r),o}return n(e,t),e.prototype._createSymbol=function(t,e,n,i,r){this.removeAll();var o=Nd(t,-1,-1,2,2,null,r);o.attr({z2:100,culling:!0,scaleX:i[0]/2,scaleY:i[1]/2}),o.drift=h_,this._symbolType=t,this.add(o)},e.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},e.prototype.getSymbolType=function(){return this._symbolType},e.prototype.getSymbolPath=function(){return this.childAt(0)},e.prototype.highlight=function(){zs(this.childAt(0))},e.prototype.downplay=function(){Bs(this.childAt(0))},e.prototype.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},e.prototype.setDraggable=function(t,e){var n=this.childAt(0);n.draggable=t,n.cursor=!e&&t?"move":n.cursor},e.prototype.updateData=function(t,n,i,r){this.silent=!1;var o=t.getItemVisual(n,"symbol")||"circle",a=t.hostModel,s=e.getSymbolSize(t,n),l=o!==this._symbolType,u=r&&r.disableAnimation;if(l){var h=t.getItemVisual(n,"symbolKeepAspect");this._createSymbol(o,t,n,s,h)}else{(p=this.childAt(0)).silent=!1;var c={scaleX:s[0]/2,scaleY:s[1]/2};u?p.attr(c):gu(p,c,a,n),wu(p)}if(this._updateCommon(t,n,s,i,r),l){var p=this.childAt(0);if(!u){c={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:p.style.opacity}};p.scaleX=p.scaleY=0,p.style.opacity=0,yu(p,c,a,n)}}u&&this.childAt(0).stopAnimation("leave")},e.prototype._updateCommon=function(t,e,n,i,r){var o,a,s,l,u,h,c,p,f,d=this.childAt(0),g=t.hostModel;if(i&&(o=i.emphasisItemStyle,a=i.blurItemStyle,s=i.selectItemStyle,l=i.focus,u=i.blurScope,c=i.labelStatesModels,p=i.hoverScale,f=i.cursorStyle,h=i.emphasisDisabled),!i||t.hasItemOption){var y=i&&i.itemModel?i.itemModel:t.getItemModel(e),v=y.getModel("emphasis");o=v.getModel("itemStyle").getItemStyle(),s=y.getModel(["select","itemStyle"]).getItemStyle(),a=y.getModel(["blur","itemStyle"]).getItemStyle(),l=v.get("focus"),u=v.get("blurScope"),h=v.get("disabled"),c=Fu(y),p=v.getShallow("scale"),f=y.getShallow("cursor")}var m=t.getItemVisual(e,"symbolRotate");d.attr("rotation",(m||0)*Math.PI/180||0);var _=Ed(t.getItemVisual(e,"symbolOffset"),n);_&&(d.x=_[0],d.y=_[1]),f&&d.attr("cursor",f);var x=t.getItemVisual(e,"style"),w=x.fill;if(d instanceof Ha){var b=d.style;d.useStyle(D({image:b.image,x:b.x,y:b.y,width:b.width,height:b.height},x))}else d.__isEmptyBrush?d.useStyle(D({},x)):d.useStyle(x),d.style.decal=null,d.setColor(w,r&&r.symbolInnerColor),d.style.strokeNoScale=!0;var S=t.getItemVisual(e,"liftZ"),M=this._z2;null!=S?null==M&&(this._z2=d.z2,d.z2+=S):null!=M&&(d.z2=M,this._z2=null);var T=r&&r.useNameLabel;Bu(d,c,{labelFetcher:g,labelDataIndex:e,defaultText:function(e){return T?t.getName(e):s_(t,e)},inheritColor:w,defaultOpacity:x.opacity}),this._sizeX=n[0]/2,this._sizeY=n[1]/2;var C=d.ensureState("emphasis");C.style=o,d.ensureState("select").style=s,d.ensureState("blur").style=a;var I=null==p||!0===p?Math.max(1.1,3/this._sizeY):isFinite(p)&&p>0?+p:1;C.scaleX=this._sizeX*I,C.scaleY=this._sizeY*I,this.setSymbolScale(1),Ks(this,l,u,h)},e.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},e.prototype.fadeOut=function(t,e,n){var i=this.childAt(0),r=ls(this).dataIndex,o=n&&n.animation;if(this.silent=i.silent=!0,n&&n.fadeLabel){var a=i.getTextContent();a&&mu(a,{style:{opacity:0}},e,{dataIndex:r,removeOpt:o,cb:function(){i.removeTextContent()}})}else i.removeTextContent();mu(i,{style:{opacity:0},scaleX:0,scaleY:0},e,{dataIndex:r,cb:t,removeOpt:o})},e.getSymbolSize=function(t,e){return H(n=t.getItemVisual(e,"symbolSize"))||(n=[+n,+n]),[n[0]||0,n[1]||0];var n},e}(or);function h_(t,e){this.parent.drift(t,e)}function c_(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(i.isIgnore&&i.isIgnore(n))&&!(i.clipShape&&!i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function p_(t){return null==t||Y(t)||(t={isIgnore:t}),t||{}}function f_(t){var e=t.hostModel,n=e.getModel("emphasis");return{emphasisItemStyle:n.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:n.get("focus"),blurScope:n.get("blurScope"),emphasisDisabled:n.get("disabled"),hoverScale:n.get("scale"),labelStatesModels:Fu(e),cursorStyle:e.get("cursor")}}var d_=function(){function t(t){this.group=new or,this._SymbolCtor=t||u_}return t.prototype.updateData=function(t,e){this._progressiveEls=null,e=p_(e);var n=this.group,i=t.hostModel,r=this._data,o=this._SymbolCtor,a=e.disableAnimation,s=f_(t),l={disableAnimation:a},u=e.getSymbolPoint||function(e){return t.getItemLayout(e)};r||n.removeAll(),t.diff(r).add((function(i){var r=u(i);if(c_(t,r,i,e)){var a=new o(t,i,s,l);a.setPosition(r),t.setItemGraphicEl(i,a),n.add(a)}})).update((function(h,c){var p=r.getItemGraphicEl(c),f=u(h);if(c_(t,f,h,e)){var d=t.getItemVisual(h,"symbol")||"circle",g=p&&p.getSymbolType&&p.getSymbolType();if(!p||g&&g!==d)n.remove(p),(p=new o(t,h,s,l)).setPosition(f);else{p.updateData(t,h,s,l);var y={x:f[0],y:f[1]};a?p.attr(y):gu(p,y,i)}n.add(p),t.setItemGraphicEl(h,p)}else n.remove(p)})).remove((function(t){var e=r.getItemGraphicEl(t);e&&e.fadeOut((function(){n.remove(e)}),i)})).execute(),this._getSymbolPoint=u,this._data=t},t.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl((function(e,n){var i=t._getSymbolPoint(n);e.setPosition(i),e.markRedraw()}))},t.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=f_(t),this._data=null,this.group.removeAll()},t.prototype.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[],n=p_(n);for(var r=t.start;r<t.end;r++){var o=e.getItemLayout(r);if(c_(e,o,r,n)){var a=new this._SymbolCtor(e,r,this._seriesScope);a.traverse(i),a.setPosition(o),this.group.add(a),e.setItemGraphicEl(r,a),this._progressiveEls.push(a)}}},t.prototype.eachRendered=function(t){Nu(this._progressiveEls||this.group,t)},t.prototype.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl((function(t){t.fadeOut((function(){e.remove(t)}),n.hostModel)})):e.removeAll()},t}();function g_(t,e,n){var i=t.getBaseAxis(),r=t.getOtherAxis(i),o=function(t,e){var n=0,i=t.scale.getExtent();"start"===e?n=i[0]:"end"===e?n=i[1]:X(e)&&!isNaN(e)?n=e:i[0]>0?n=i[0]:i[1]<0&&(n=i[1]);return n}(r,n),a=i.dim,s=r.dim,l=e.mapDimension(s),u=e.mapDimension(a),h="x"===s||"radius"===s?1:0,c=N(t.dimensions,(function(t){return e.mapDimension(t)})),p=!1,f=e.getCalculationInfo("stackResultDimension");return av(e,c[0])&&(p=!0,c[0]=f),av(e,c[1])&&(p=!0,c[1]=f),{dataDimsForPoint:c,valueStart:o,valueAxisDim:s,baseAxisDim:a,stacked:!!p,valueDim:l,baseDim:u,baseDataOffset:h,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function y_(t,e,n,i){var r=NaN;t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart);var o=t.baseDataOffset,a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}var v_=Math.min,m_=Math.max;function __(t,e){return isNaN(t)||isNaN(e)}function x_(t,e,n,i,r,o,a,s,l){for(var u,h,c,p,f,d,g=n,y=0;y<i;y++){var v=e[2*g],m=e[2*g+1];if(g>=r||g<0)break;if(__(v,m)){if(l){g+=o;continue}break}if(g===n)t[o>0?"moveTo":"lineTo"](v,m),c=v,p=m;else{var _=v-u,x=m-h;if(_*_+x*x<.5){g+=o;continue}if(a>0){for(var w=g+o,b=e[2*w],S=e[2*w+1];b===v&&S===m&&y<i;)y++,g+=o,b=e[2*(w+=o)],S=e[2*w+1],_=(v=e[2*g])-u,x=(m=e[2*g+1])-h;var M=y+1;if(l)for(;__(b,S)&&M<i;)M++,b=e[2*(w+=o)],S=e[2*w+1];var T=.5,C=0,D=0,I=void 0,k=void 0;if(M>=i||__(b,S))f=v,d=m;else{C=b-u,D=S-h;var A=v-u,L=b-v,P=m-h,O=S-m,R=void 0,N=void 0;if("x"===s){var E=C>0?1:-1;f=v-E*(R=Math.abs(A))*a,d=m,I=v+E*(N=Math.abs(L))*a,k=m}else if("y"===s){var z=D>0?1:-1;f=v,d=m-z*(R=Math.abs(P))*a,I=v,k=m+z*(N=Math.abs(O))*a}else R=Math.sqrt(A*A+P*P),f=v-C*a*(1-(T=(N=Math.sqrt(L*L+O*O))/(N+R))),d=m-D*a*(1-T),k=m+D*a*T,I=v_(I=v+C*a*T,m_(b,v)),k=v_(k,m_(S,m)),I=m_(I,v_(b,v)),d=m-(D=(k=m_(k,v_(S,m)))-m)*R/N,f=v_(f=v-(C=I-v)*R/N,m_(u,v)),d=v_(d,m_(h,m)),I=v+(C=v-(f=m_(f,v_(u,v))))*N/R,k=m+(D=m-(d=m_(d,v_(h,m))))*N/R}t.bezierCurveTo(c,p,f,d,v,m),c=I,p=k}else t.lineTo(v,m)}u=v,h=m,g+=o}return y}var w_=function(){this.smooth=0,this.smoothConstraint=!0},b_=function(t){function e(e){var n=t.call(this,e)||this;return n.type="ec-polyline",n}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new w_},e.prototype.buildPath=function(t,e){var n=e.points,i=0,r=n.length/2;if(e.connectNulls){for(;r>0&&__(n[2*r-2],n[2*r-1]);r--);for(;i<r&&__(n[2*i],n[2*i+1]);i++);}for(;i<r;)i+=x_(t,n,i,r,r,1,e.smooth,e.smoothMonotone,e.connectNulls)+1},e.prototype.getPointOn=function(t,e){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n,i,r=this.path.data,o=ya.CMD,a="x"===e,s=[],l=0;l<r.length;){var u=void 0,h=void 0,c=void 0,p=void 0,f=void 0,d=void 0,g=void 0;switch(r[l++]){case o.M:n=r[l++],i=r[l++];break;case o.L:if(u=r[l++],h=r[l++],(g=a?(t-n)/(u-n):(t-i)/(h-i))<=1&&g>=0){var y=a?(h-i)*g+i:(u-n)*g+n;return a?[t,y]:[y,t]}n=u,i=h;break;case o.C:u=r[l++],h=r[l++],c=r[l++],p=r[l++],f=r[l++],d=r[l++];var v=a?un(n,u,c,f,t,s):un(i,h,p,d,t,s);if(v>0)for(var m=0;m<v;m++){var _=s[m];if(_<=1&&_>=0){y=a?sn(i,h,p,d,_):sn(n,u,c,f,_);return a?[t,y]:[y,t]}}n=f,i=d}}},e}(Ea),S_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(w_),M_=function(t){function e(e){var n=t.call(this,e)||this;return n.type="ec-polygon",n}return n(e,t),e.prototype.getDefaultShape=function(){return new S_},e.prototype.buildPath=function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length/2,a=e.smoothMonotone;if(e.connectNulls){for(;o>0&&__(n[2*o-2],n[2*o-1]);o--);for(;r<o&&__(n[2*r],n[2*r+1]);r++);}for(;r<o;){var s=x_(t,n,r,o,o,1,e.smooth,a,e.connectNulls);x_(t,i,r+s-1,s,o,-1,e.stackedOnSmooth,a,e.connectNulls),r+=s+1,t.closePath()}},e}(Ea);function T_(t,e,n,i,r){var o=t.getArea(),a=o.x,s=o.y,l=o.width,u=o.height,h=n.get(["lineStyle","width"])||2;a-=h/2,s-=h/2,l+=h,u+=h,a=Math.floor(a),l=Math.round(l);var c=new qa({shape:{x:a,y:s,width:l,height:u}});if(e){var p=t.getBaseAxis(),f=p.isHorizontal(),d=p.inverse;f?(d&&(c.shape.x+=l),c.shape.width=0):(d||(c.shape.y+=u),c.shape.height=0);var g=W(r)?function(t){r(t,c)}:null;yu(c,{shape:{width:l,height:u,x:a,y:s}},n,null,i,g)}return c}function C_(t,e,n){var i=t.getArea(),r=gr(i.r0,1),o=gr(i.r,1),a=new Fl({shape:{cx:gr(t.cx,1),cy:gr(t.cy,1),r0:r,r:o,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});e&&("angle"===t.getBaseAxis().dim?a.shape.endAngle=i.startAngle:a.shape.r=r,yu(a,{shape:{endAngle:i.endAngle,r:o}},n));return a}function D_(t,e){return t.type===e}function I_(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++)if(t[n]!==e[n])return;return!0}}function k_(t){for(var e=1/0,n=1/0,i=-1/0,r=-1/0,o=0;o<t.length;){var a=t[o++],s=t[o++];isNaN(a)||(e=Math.min(a,e),i=Math.max(a,i)),isNaN(s)||(n=Math.min(s,n),r=Math.max(s,r))}return[[e,n],[i,r]]}function A_(t,e){var n=k_(t),i=n[0],r=n[1],o=k_(e),a=o[0],s=o[1];return Math.max(Math.abs(i[0]-a[0]),Math.abs(i[1]-a[1]),Math.abs(r[0]-s[0]),Math.abs(r[1]-s[1]))}function L_(t){return X(t)?t:t?.5:0}function P_(t,e,n,i){var r=e.getBaseAxis(),o="x"===r.dim||"radius"===r.dim?0:1,a=[],s=0,l=[],u=[],h=[],c=[];if(i){for(s=0;s<t.length;s+=2)isNaN(t[s])||isNaN(t[s+1])||c.push(t[s],t[s+1]);t=c}for(s=0;s<t.length-2;s+=2)switch(h[0]=t[s+2],h[1]=t[s+3],u[0]=t[s],u[1]=t[s+1],a.push(u[0],u[1]),n){case"end":l[o]=h[o],l[1-o]=u[1-o],a.push(l[0],l[1]);break;case"middle":var p=(u[o]+h[o])/2,f=[];l[o]=f[o]=p,l[1-o]=u[1-o],f[1-o]=h[1-o],a.push(l[0],l[1]),a.push(f[0],f[1]);break;default:l[o]=u[o],l[1-o]=h[1-o],a.push(l[0],l[1])}return a.push(t[s++],t[s++]),a}function O_(t,e,n){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var r,o,a=i.length-1;a>=0;a--){var s=t.getDimensionInfo(i[a].dimension);if("x"===(r=s&&s.coordDim)||"y"===r){o=i[a];break}}if(o){var l=e.getAxis(r),u=N(o.stops,(function(t){return{coord:l.toGlobalCoord(l.dataToCoord(t.value)),color:t.color}})),h=u.length,c=o.outerColors.slice();h&&u[0].coord>u[h-1].coord&&(u.reverse(),c.reverse());var p=function(t,e){var n,i,r=[],o=t.length;function a(t,e,n){var i=t.coord;return{coord:n,color:Hn((n-i)/(e.coord-i),[t.color,e.color])}}for(var s=0;s<o;s++){var l=t[s],u=l.coord;if(u<0)n=l;else{if(u>e){i?r.push(a(i,l,e)):n&&r.push(a(n,l,0),a(n,l,e));break}n&&(r.push(a(n,l,0)),n=null),r.push(l),i=l}}return r}(u,"x"===r?n.getWidth():n.getHeight()),f=p.length;if(!f&&h)return u[0].coord<0?c[1]?c[1]:u[h-1].color:c[0]?c[0]:u[0].color;var d=p[0].coord-10,g=p[f-1].coord+10,y=g-d;if(y<.001)return"transparent";R(p,(function(t){t.offset=(t.coord-d)/y})),p.push({offset:f?p[f-1].offset:.5,color:c[1]||"transparent"}),p.unshift({offset:f?p[0].offset:.5,color:c[0]||"transparent"});var v=new ru(0,0,0,0,p,!0);return v[r]=d,v[r+"2"]=g,v}}}function R_(t,e,n){var i=t.get("showAllSymbol"),r="auto"===i;if(!i||r){var o=n.getAxesByScale("ordinal")[0];if(o&&(!r||!function(t,e){var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count();isNaN(i)&&(i=0);for(var r=e.count(),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*u_.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return!1;return!0}(o,e))){var a=e.mapDimension(o.dim),s={};return R(o.getViewLabels(),(function(t){var e=o.scale.getRawOrdinalNumber(t.tickValue);s[e]=1})),function(t){return!s.hasOwnProperty(e.get(a,t))}}}}function N_(t,e){return[t[2*e],t[2*e+1]]}function E_(t){if(t.get(["endLabel","show"]))return!0;for(var e=0;e<fs.length;e++)if(t.get([fs[e],"endLabel","show"]))return!0;return!1}function z_(t,e,n,i){if(D_(e,"cartesian2d")){var r=i.getModel("endLabel"),o=r.get("valueAnimation"),a=i.getData(),s={lastFrameIndex:0},l=E_(i)?function(n,i){t._endLabelOnDuring(n,i,a,s,o,r,e)}:null,u=e.getBaseAxis().isHorizontal(),h=T_(e,n,i,(function(){var e=t._endLabel;e&&n&&null!=s.originalX&&e.attr({x:s.originalX,y:s.originalY})}),l);if(!i.get("clip",!0)){var c=h.shape,p=Math.max(c.width,c.height);u?(c.y-=p,c.height+=2*p):(c.x-=p,c.width+=2*p)}return l&&l(1,h),h}return C_(e,n,i)}var B_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(){var t=new or,e=new d_;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},e.prototype.render=function(t,e,n){var i=this,r=t.coordinateSystem,o=this.group,a=t.getData(),s=t.getModel("lineStyle"),l=t.getModel("areaStyle"),u=a.getLayout("points")||[],h="polar"===r.type,c=this._coordSys,p=this._symbolDraw,f=this._polyline,d=this._polygon,g=this._lineGroup,y=!e.ssr&&t.isAnimationEnabled(),v=!l.isEmpty(),m=l.get("origin"),_=g_(r,a,m),x=v&&function(t,e,n){if(!n.valueDim)return[];for(var i=e.count(),r=Cv(2*i),o=0;o<i;o++){var a=y_(n,t,e,o);r[2*o]=a[0],r[2*o+1]=a[1]}return r}(r,a,_),w=t.get("showSymbol"),b=t.get("connectNulls"),S=w&&!h&&R_(t,a,r),M=this._data;M&&M.eachItemGraphicEl((function(t,e){t.__temp&&(o.remove(t),M.setItemGraphicEl(e,null))})),w||p.remove(),o.add(g);var T,C=!h&&t.get("step");r&&r.getArea&&t.get("clip",!0)&&(null!=(T=r.getArea()).width?(T.x-=.1,T.y-=.1,T.width+=.2,T.height+=.2):T.r0&&(T.r0-=.5,T.r+=.5)),this._clipShapeForSymbol=T;var D=O_(a,r,n)||a.getVisual("style")[a.getVisual("drawType")];if(f&&c.type===r.type&&C===this._step){v&&!d?d=this._newPolygon(u,x):d&&!v&&(g.remove(d),d=this._polygon=null),h||this._initOrUpdateEndLabel(t,r,Kh(D));var k=g.getClipPath();if(k)yu(k,{shape:z_(this,r,!1,t).shape},t);else g.setClipPath(z_(this,r,!0,t));w&&p.updateData(a,{isIgnore:S,clipShape:T,disableAnimation:!0,getSymbolPoint:function(t){return[u[2*t],u[2*t+1]]}}),I_(this._stackedOnPoints,x)&&I_(this._points,u)||(y?this._doUpdateAnimation(a,x,r,n,C,m,b):(C&&(u=P_(u,r,C,b),x&&(x=P_(x,r,C,b))),f.setShape({points:u}),d&&d.setShape({points:u,stackedOnPoints:x})))}else w&&p.updateData(a,{isIgnore:S,clipShape:T,disableAnimation:!0,getSymbolPoint:function(t){return[u[2*t],u[2*t+1]]}}),y&&this._initSymbolLabelAnimation(a,r,T),C&&(u=P_(u,r,C,b),x&&(x=P_(x,r,C,b))),f=this._newPolyline(u),v?d=this._newPolygon(u,x):d&&(g.remove(d),d=this._polygon=null),h||this._initOrUpdateEndLabel(t,r,Kh(D)),g.setClipPath(z_(this,r,!0,t));var A=t.getModel("emphasis"),L=A.get("focus"),P=A.get("blurScope"),O=A.get("disabled");(f.useStyle(I(s.getLineStyle(),{fill:"none",stroke:D,lineJoin:"bevel"})),Js(f,t,"lineStyle"),f.style.lineWidth>0&&"bolder"===t.get(["emphasis","lineStyle","width"]))&&(f.getState("emphasis").style.lineWidth=+f.style.lineWidth+1);ls(f).seriesIndex=t.seriesIndex,Ks(f,L,P,O);var R=L_(t.get("smooth")),N=t.get("smoothMonotone");if(f.setShape({smooth:R,smoothMonotone:N,connectNulls:b}),d){var E=a.getCalculationInfo("stackedOnSeries"),z=0;d.useStyle(I(l.getAreaStyle(),{fill:D,opacity:.7,lineJoin:"bevel",decal:a.getVisual("style").decal})),E&&(z=L_(E.get("smooth"))),d.setShape({smooth:R,stackedOnSmooth:z,smoothMonotone:N,connectNulls:b}),Js(d,t,"areaStyle"),ls(d).seriesIndex=t.seriesIndex,Ks(d,L,P,O)}var B=function(t){i._changePolyState(t)};a.eachItemGraphicEl((function(t){t&&(t.onHoverStateChange=B)})),this._polyline.onHoverStateChange=B,this._data=a,this._coordSys=r,this._stackedOnPoints=x,this._points=u,this._step=C,this._valueOrigin=m,t.get("triggerLineEvent")&&(this.packEventData(t,f),d&&this.packEventData(t,d))},e.prototype.packEventData=function(t,e){ls(e).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},e.prototype.highlight=function(t,e,n,i){var r=t.getData(),o=qr(r,i);if(this._changePolyState("emphasis"),!(o instanceof Array)&&null!=o&&o>=0){var a=r.getLayout("points"),s=r.getItemGraphicEl(o);if(!s){var l=a[2*o],u=a[2*o+1];if(isNaN(l)||isNaN(u))return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(l,u))return;var h=t.get("zlevel")||0,c=t.get("z")||0;(s=new u_(r,o)).x=l,s.y=u,s.setZ(h,c);var p=s.getSymbolPath().getTextContent();p&&(p.zlevel=h,p.z=c,p.z2=this._polyline.z2+1),s.__temp=!0,r.setItemGraphicEl(o,s),s.stopSymbolAnimation(!0),this.group.add(s)}s.highlight()}else Pf.prototype.highlight.call(this,t,e,n,i)},e.prototype.downplay=function(t,e,n,i){var r=t.getData(),o=qr(r,i);if(this._changePolyState("normal"),null!=o&&o>=0){var a=r.getItemGraphicEl(o);a&&(a.__temp?(r.setItemGraphicEl(o,null),this.group.remove(a)):a.downplay())}else Pf.prototype.downplay.call(this,t,e,n,i)},e.prototype._changePolyState=function(t){var e=this._polygon;Ps(this._polyline,t),e&&Ps(e,t)},e.prototype._newPolyline=function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new b_({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(e),this._polyline=e,e},e.prototype._newPolygon=function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new M_({shape:{points:t,stackedOnPoints:e},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n,n},e.prototype._initSymbolLabelAnimation=function(t,e,n){var i,r,o=e.getBaseAxis(),a=o.inverse;"cartesian2d"===e.type?(i=o.isHorizontal(),r=!1):"polar"===e.type&&(i="angle"===o.dim,r=!0);var s=t.hostModel,l=s.get("animationDuration");W(l)&&(l=l(null));var u=s.get("animationDelay")||0,h=W(u)?u(null):u;t.eachItemGraphicEl((function(t,o){var s=t;if(s){var c=[t.x,t.y],p=void 0,f=void 0,d=void 0;if(n)if(r){var g=n,y=e.pointToCoord(c);i?(p=g.startAngle,f=g.endAngle,d=-y[1]/180*Math.PI):(p=g.r0,f=g.r,d=y[0])}else{var v=n;i?(p=v.x,f=v.x+v.width,d=t.x):(p=v.y+v.height,f=v.y,d=t.y)}var m=f===p?0:(d-p)/(f-p);a&&(m=1-m);var _=W(u)?u(o):l*m+h,x=s.getSymbolPath(),w=x.getTextContent();s.attr({scaleX:0,scaleY:0}),s.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:_}),w&&w.animateFrom({style:{opacity:0}},{duration:300,delay:_}),x.disableLabelAnimation=!0}}))},e.prototype._initOrUpdateEndLabel=function(t,e,n){var i=t.getModel("endLabel");if(E_(t)){var r=t.getData(),o=this._polyline,a=r.getLayout("points");if(!a)return o.removeTextContent(),void(this._endLabel=null);var s=this._endLabel;s||((s=this._endLabel=new Ka({z2:200})).ignoreClip=!0,o.setTextContent(this._endLabel),o.disableLabelAnimation=!0);var l=function(t){for(var e,n,i=t.length/2;i>0&&(e=t[2*i-2],n=t[2*i-1],isNaN(e)||isNaN(n));i--);return i-1}(a);l>=0&&(Bu(o,Fu(t,"endLabel"),{inheritColor:n,labelFetcher:t,labelDataIndex:l,defaultText:function(t,e,n){return null!=n?l_(r,n):s_(r,t)},enableTextSetter:!0},function(t,e){var n=e.getBaseAxis(),i=n.isHorizontal(),r=n.inverse,o=i?r?"right":"left":"center",a=i?"middle":r?"top":"bottom";return{normal:{align:t.get("align")||o,verticalAlign:t.get("verticalAlign")||a}}}(i,e)),o.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},e.prototype._endLabelOnDuring=function(t,e,n,i,r,o,a){var s=this._endLabel,l=this._polyline;if(s){t<1&&null==i.originalX&&(i.originalX=s.x,i.originalY=s.y);var u=n.getLayout("points"),h=n.hostModel,c=h.get("connectNulls"),p=o.get("precision"),f=o.get("distance")||0,d=a.getBaseAxis(),g=d.isHorizontal(),y=d.inverse,v=e.shape,m=y?g?v.x:v.y+v.height:g?v.x+v.width:v.y,_=(g?f:0)*(y?-1:1),x=(g?0:-f)*(y?-1:1),w=g?"x":"y",b=function(t,e,n){for(var i,r,o=t.length/2,a="x"===n?0:1,s=0,l=-1,u=0;u<o;u++)if(r=t[2*u+a],!isNaN(r)&&!isNaN(t[2*u+1-a]))if(0!==u){if(i<=e&&r>=e||i>=e&&r<=e){l=u;break}s=u,i=r}else i=r;return{range:[s,l],t:(e-i)/(r-i)}}(u,m,w),S=b.range,M=S[1]-S[0],T=void 0;if(M>=1){if(M>1&&!c){var C=N_(u,S[0]);s.attr({x:C[0]+_,y:C[1]+x}),r&&(T=h.getRawValue(S[0]))}else{(C=l.getPointOn(m,w))&&s.attr({x:C[0]+_,y:C[1]+x});var D=h.getRawValue(S[0]),I=h.getRawValue(S[1]);r&&(T=function(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(X(i))return gr(d=Rr(n||0,i,r),o?Math.max(yr(n||0),yr(i)):e);if(G(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c=t.getDimensionInfo(h);if(c&&"ordinal"===c.type)a[h]=(r<1&&s?s:l)[h];else{var p=s&&s[h]?s[h]:0,f=l[h],d=Rr(p,f,r);a[h]=gr(d,o?Math.max(yr(p),yr(f)):e)}}return a}(n,p,D,I,b.t))}i.lastFrameIndex=S[0]}else{var k=1===t||i.lastFrameIndex>0?S[0]:0;C=N_(u,k);r&&(T=h.getRawValue(k)),s.attr({x:C[0]+_,y:C[1]+x})}if(r){var A=Yu(s);"function"==typeof A.setLabelText&&A.setLabelText(T)}}},e.prototype._doUpdateAnimation=function(t,e,n,i,r,o,a){var s=this._polyline,l=this._polygon,u=t.hostModel,h=function(t,e,n,i,r,o,a,s){for(var l=function(t,e){var n=[];return e.diff(t).add((function(t){n.push({cmd:"+",idx:t})})).update((function(t,e){n.push({cmd:"=",idx:e,idx1:t})})).remove((function(t){n.push({cmd:"-",idx:t})})).execute(),n}(t,e),u=[],h=[],c=[],p=[],f=[],d=[],g=[],y=g_(r,e,a),v=t.getLayout("points")||[],m=e.getLayout("points")||[],_=0;_<l.length;_++){var x=l[_],w=!0,b=void 0,S=void 0;switch(x.cmd){case"=":b=2*x.idx,S=2*x.idx1;var M=v[b],T=v[b+1],C=m[S],D=m[S+1];(isNaN(M)||isNaN(T))&&(M=C,T=D),u.push(M,T),h.push(C,D),c.push(n[b],n[b+1]),p.push(i[S],i[S+1]),g.push(e.getRawIndex(x.idx1));break;case"+":var I=x.idx,k=y.dataDimsForPoint,A=r.dataToPoint([e.get(k[0],I),e.get(k[1],I)]);S=2*I,u.push(A[0],A[1]),h.push(m[S],m[S+1]);var L=y_(y,r,e,I);c.push(L[0],L[1]),p.push(i[S],i[S+1]),g.push(e.getRawIndex(I));break;case"-":w=!1}w&&(f.push(x),d.push(d.length))}d.sort((function(t,e){return g[t]-g[e]}));var P=u.length,O=Cv(P),R=Cv(P),N=Cv(P),E=Cv(P),z=[];for(_=0;_<d.length;_++){var B=d[_],F=2*_,V=2*B;O[F]=u[V],O[F+1]=u[V+1],R[F]=h[V],R[F+1]=h[V+1],N[F]=c[V],N[F+1]=c[V+1],E[F]=p[V],E[F+1]=p[V+1],z[_]=f[B]}return{current:O,next:R,stackedOnCurrent:N,stackedOnNext:E,status:z}}(this._data,t,this._stackedOnPoints,e,this._coordSys,0,this._valueOrigin),c=h.current,p=h.stackedOnCurrent,f=h.next,d=h.stackedOnNext;if(r&&(c=P_(h.current,n,r,a),p=P_(h.stackedOnCurrent,n,r,a),f=P_(h.next,n,r,a),d=P_(h.stackedOnNext,n,r,a)),A_(c,f)>3e3||l&&A_(p,d)>3e3)return s.stopAnimation(),s.setShape({points:f}),void(l&&(l.stopAnimation(),l.setShape({points:f,stackedOnPoints:d})));s.shape.__points=h.current,s.shape.points=c;var g={shape:{points:f}};h.current!==c&&(g.shape.__points=h.next),s.stopAnimation(),gu(s,g,u),l&&(l.setShape({points:c,stackedOnPoints:p}),l.stopAnimation(),gu(l,{shape:{stackedOnPoints:d}},u),s.shape.points!==l.shape.points&&(l.shape.points=s.shape.points));for(var y=[],v=h.status,m=0;m<v.length;m++){if("="===v[m].cmd){var _=t.getItemGraphicEl(v[m].idx1);_&&y.push({el:_,ptIdx:m})}}s.animators&&s.animators.length&&s.animators[0].during((function(){l&&l.dirtyShape();for(var t=s.shape.__points,e=0;e<y.length;e++){var n=y[e].el,i=2*y[e].ptIdx;n.x=t[i],n.y=t[i+1],n.markRedraw()}}))},e.prototype.remove=function(t){var e=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl((function(t,i){t.__temp&&(e.remove(t),n.setItemGraphicEl(i,null))})),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},e.type="line",e}(Pf);var F_={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}},V_=function(t){return Math.round(t.length/2)};function H_(t){return{seriesType:t,reset:function(t,e,n){var i=t.getData(),r=t.get("sampling"),o=t.coordinateSystem,a=i.count();if(a>10&&"cartesian2d"===o.type&&r){var s=o.getBaseAxis(),l=o.getOtherAxis(s),u=s.getExtent(),h=n.getDevicePixelRatio(),c=Math.abs(u[1]-u[0])*(h||1),p=Math.round(a/c);if(isFinite(p)&&p>1){"lttb"===r&&t.setData(i.lttbDownSample(i.mapDimension(l.dim),1/p));var f=void 0;G(r)?f=F_[r]:W(r)&&(f=r),f&&t.setData(i.downSample(i.mapDimension(l.dim),1/p,f,V_))}}}}}var W_=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.prototype.getInitialData=function(t,e){return lv(null,this,{useEncodeDefaulter:!0})},e.prototype.getMarkerPosition=function(t,e,n){var i=this.coordinateSystem;if(i&&i.clampData){var r=i.clampData(t),o=i.dataToPoint(r);if(n)R(i.getAxes(),(function(t,n){if("category"===t.type&&null!=e){var i=t.getTicksCoords(),a=r[n],s="x1"===e[n]||"y1"===e[n];if(s&&(a+=1),i.length<2)return;if(2===i.length)return void(o[n]=t.toGlobalCoord(t.getExtent()[s?1:0]));for(var l=void 0,u=void 0,h=1,c=0;c<i.length;c++){var p=i[c].coord,f=c===i.length-1?i[c-1].tickValue+h:i[c].tickValue;if(f===a){u=p;break}if(f<a)l=p;else if(null!=l&&f>a){u=(p+l)/2;break}1===c&&(h=f-i[0].tickValue)}null==u&&(l?l&&(u=i[i.length-1].coord):u=i[0].coord),o[n]=t.toGlobalCoord(u)}}));else{var a=this.getData(),s=a.getLayout("offset"),l=a.getLayout("size"),u=i.getBaseAxis().isHorizontal()?0:1;o[u]+=s+l/2}return o}return[NaN,NaN]},e.type="series.__base_bar__",e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},e}(xf);xf.registerClass(W_);var G_=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}var i,r;return n(e,t),e.prototype.getInitialData=function(){return lv(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},e.prototype.getProgressive=function(){return!!this.get("large")&&this.get("progressive")},e.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},e.prototype.brushSelector=function(t,e,n){return n.rect(e.getItemLayout(t))},e.type="series.bar",e.dependencies=["grid","polar"],e.defaultOption=(i=W_.defaultOption,r={clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1},C(C({},i,!0),r,!0)),e}(W_),U_=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},X_=function(t){function e(e){var n=t.call(this,e)||this;return n.type="sausage",n}return n(e,t),e.prototype.getDefaultShape=function(){return new U_},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,u=e.endAngle,h=e.clockwise,c=2*Math.PI,p=h?u-l<c:l-u<c;p||(l=u-(h?c:-c));var f=Math.cos(l),d=Math.sin(l),g=Math.cos(u),y=Math.sin(u);p?(t.moveTo(f*r+n,d*r+i),t.arc(f*s+n,d*s+i,a,-Math.PI+l,l,!h)):t.moveTo(f*o+n,d*o+i),t.arc(n,i,o,l,u,!h),t.arc(g*s+n,y*s+i,a,u-2*Math.PI,u-Math.PI,!h),0!==r&&t.arc(n,i,r,u,l,h)},e}(Ea);function Y_(t,e,n){return e*Math.sin(t)*(n?-1:1)}function q_(t,e,n){return e*Math.cos(t)*(n?1:-1)}function Z_(t,e,n){var i=t.get("borderRadius");if(null==i)return n?{cornerRadius:0}:null;H(i)||(i=[i,i,i,i]);var r=Math.abs(e.r||0-e.r0||0);return{cornerRadius:N(i,(function(t){return qi(t,r)}))}}var j_=Math.max,K_=Math.min;var $_=function(t){function e(){var n=t.call(this)||this;return n.type=e.type,n._isFirstFrame=!0,n}return n(e,t),e.prototype.render=function(t,e,n,i){this._model=t,this._removeOnRenderedListener(n),this._updateDrawMode(t);var r=t.get("coordinateSystem");("cartesian2d"===r||"polar"===r)&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n,i))},e.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},e.prototype.incrementalRender=function(t,e){this._progressiveEls=[],this._incrementalRenderLarge(t,e)},e.prototype.eachRendered=function(t){Nu(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var e=t.pipelineContext.large;null!=this._isLargeDraw&&e===this._isLargeDraw||(this._isLargeDraw=e,this._clear())},e.prototype._renderNormal=function(t,e,n,i){var r,o=this.group,a=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis();"cartesian2d"===l.type?r=u.isHorizontal():"polar"===l.type&&(r="angle"===u.dim);var h=t.isAnimationEnabled()?t:null,c=function(t,e){var n=t.get("realtimeSort",!0),i=e.getBaseAxis();0;if(n&&"category"===i.type&&"cartesian2d"===e.type)return{baseAxis:i,otherAxis:e.getOtherAxis(i)}}(t,l);c&&this._enableRealtimeSort(c,a,n);var p=t.get("clip",!0)||c,f=function(t,e){var n=t.getArea&&t.getArea();if(D_(t,"cartesian2d")){var i=t.getBaseAxis();if("category"!==i.type||!i.onBand){var r=e.getLayout("bandWidth");i.isHorizontal()?(n.x-=r,n.width+=2*r):(n.y-=r,n.height+=2*r)}}return n}(l,a);o.removeClipPath();var d=t.get("roundCap",!0),g=t.get("showBackground",!0),y=t.getModel("backgroundStyle"),v=y.get("borderRadius")||0,m=[],_=this._backgroundEls,x=i&&i.isInitSort,w=i&&"changeAxisOrder"===i.type;function b(t){var e=ox[l.type](a,t),n=function(t,e,n){var i="polar"===t.type?Fl:qa;return new i({shape:px(e,n,t),silent:!0,z2:0})}(l,r,e);return n.useStyle(y.getItemStyle()),"cartesian2d"===l.type?n.setShape("r",v):n.setShape("cornerRadius",v),m[t]=n,n}a.diff(s).add((function(e){var n=a.getItemModel(e),i=ox[l.type](a,e,n);if(g&&b(e),a.hasValue(e)&&rx[l.type](i)){var s=!1;p&&(s=Q_[l.type](f,i));var y=J_[l.type](t,a,e,i,r,h,u.model,!1,d);c&&(y.forceLabelAnimation=!0),sx(y,a,e,n,i,t,r,"polar"===l.type),x?y.attr({shape:i}):c?tx(c,h,y,i,e,r,!1,!1):yu(y,{shape:i},t,e),a.setItemGraphicEl(e,y),o.add(y),y.ignore=s}})).update((function(e,n){var i=a.getItemModel(e),S=ox[l.type](a,e,i);if(g){var M=void 0;0===_.length?M=b(n):((M=_[n]).useStyle(y.getItemStyle()),"cartesian2d"===l.type?M.setShape("r",v):M.setShape("cornerRadius",v),m[e]=M);var T=ox[l.type](a,e);gu(M,{shape:px(r,T,l)},h,e)}var C=s.getItemGraphicEl(n);if(a.hasValue(e)&&rx[l.type](S)){var D=!1;if(p&&(D=Q_[l.type](f,S))&&o.remove(C),C?wu(C):C=J_[l.type](t,a,e,S,r,h,u.model,!!C,d),c&&(C.forceLabelAnimation=!0),w){var I=C.getTextContent();if(I){var k=Yu(I);null!=k.prevValue&&(k.prevValue=k.value)}}else sx(C,a,e,i,S,t,r,"polar"===l.type);x?C.attr({shape:S}):c?tx(c,h,C,S,e,r,!0,w):gu(C,{shape:S},t,e,null),a.setItemGraphicEl(e,C),C.ignore=D,o.add(C)}else o.remove(C)})).remove((function(e){var n=s.getItemGraphicEl(e);n&&xu(n,t,e)})).execute();var S=this._backgroundGroup||(this._backgroundGroup=new or);S.removeAll();for(var M=0;M<m.length;++M)S.add(m[M]);o.add(S),this._backgroundEls=m,this._data=a},e.prototype._renderLarge=function(t,e,n){this._clear(),hx(t,this.group),this._updateLargeClip(t)},e.prototype._incrementalRenderLarge=function(t,e){this._removeBackground(),hx(e,this.group,this._progressiveEls,!0)},e.prototype._updateLargeClip=function(t){var e=t.get("clip",!0)&&function(t,e,n,i,r){return t?"polar"===t.type?C_(t,e,n):"cartesian2d"===t.type?T_(t,e,n,i,r):null:null}(t.coordinateSystem,!1,t),n=this.group;e?n.setClipPath(e):n.removeClipPath()},e.prototype._enableRealtimeSort=function(t,e,n){var i=this;if(e.count()){var r=t.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(e,t,n),this._isFirstFrame=!1;else{var o=function(t){var n=e.getItemGraphicEl(t),i=n&&n.shape;return i&&Math.abs(r.isHorizontal()?i.height:i.width)||0};this._onRendered=function(){i._updateSortWithinSameData(e,o,r,n)},n.getZr().on("rendered",this._onRendered)}}},e.prototype._dataSort=function(t,e,n){var i=[];return t.each(t.mapDimension(e.dim),(function(t,e){var r=n(e);r=null==r?NaN:r,i.push({dataIndex:e,mappedValue:r,ordinalNumber:t})})),i.sort((function(t,e){return e.mappedValue-t.mappedValue})),{ordinalNumbers:N(i,(function(t){return t.ordinalNumber}))}},e.prototype._isOrderChangedWithinSameData=function(t,e,n){for(var i=n.scale,r=t.mapDimension(n.dim),o=Number.MAX_VALUE,a=0,s=i.getOrdinalMeta().categories.length;a<s;++a){var l=t.rawIndexOf(r,i.getRawOrdinalNumber(a)),u=l<0?Number.MIN_VALUE:e(t.indexOfRawIndex(l));if(u>o)return!0;o=u}return!1},e.prototype._isOrderDifferentInView=function(t,e){for(var n=e.scale,i=n.getExtent(),r=Math.max(0,i[0]),o=Math.min(i[1],n.getOrdinalMeta().categories.length-1);r<=o;++r)if(t.ordinalNumbers[r]!==n.getRawOrdinalNumber(r))return!0},e.prototype._updateSortWithinSameData=function(t,e,n,i){if(this._isOrderChangedWithinSameData(t,e,n)){var r=this._dataSort(t,n,e);this._isOrderDifferentInView(r,n)&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",axisId:n.index,sortInfo:r}))}},e.prototype._dispatchInitSort=function(t,e,n){var i=e.baseAxis,r=this._dataSort(t,i,(function(n){return t.get(t.mapDimension(e.otherAxis.dim),n)}));n.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:r})},e.prototype.remove=function(t,e){this._clear(this._model),this._removeOnRenderedListener(e)},e.prototype.dispose=function(t,e){this._removeOnRenderedListener(e)},e.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},e.prototype._clear=function(t){var e=this.group,n=this._data;t&&t.isAnimationEnabled()&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl((function(e){xu(e,t,ls(e).dataIndex)}))):e.removeAll(),this._data=null,this._isFirstFrame=!0},e.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},e.type="bar",e}(Pf),Q_={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1;n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height);var r=t.x+t.width,o=t.y+t.height,a=j_(e.x,t.x),s=K_(e.x+e.width,r),l=j_(e.y,t.y),u=K_(e.y+e.height,o),h=s<a,c=u<l;return e.x=h&&a>r?s:a,e.y=c&&l>o?u:l,e.width=h?0:s-a,e.height=c?0:u-l,n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),h||c},polar:function(t,e){var n=e.r0<=e.r?1:-1;if(n<0){var i=e.r;e.r=e.r0,e.r0=i}var r=K_(e.r,t.r),o=j_(e.r0,t.r0);e.r=r,e.r0=o;var a=r-o<0;if(n<0){i=e.r;e.r=e.r0,e.r0=i}return a}},J_={cartesian2d:function(t,e,n,i,r,o,a,s,l){var u=new qa({shape:D({},i),z2:1});(u.__dataIndex=n,u.name="item",o)&&(u.shape[r?"height":"width"]=0);return u},polar:function(t,e,n,i,r,o,a,s,l){var u=!r&&l?X_:Fl,h=new u({shape:i,z2:1});h.name="item";var c,p,f=ax(r);if(h.calculateTextPosition=(c=f,p=({isRoundCap:u===X_}||{}).isRoundCap,function(t,e,n){var i=e.position;if(!i||i instanceof Array)return Zi(t,e,n);var r=c(i),o=null!=e.distance?e.distance:5,a=this.shape,s=a.cx,l=a.cy,u=a.r,h=a.r0,f=(u+h)/2,d=a.startAngle,g=a.endAngle,y=(d+g)/2,v=p?Math.abs(u-h)/2:0,m=Math.cos,_=Math.sin,x=s+u*m(d),w=l+u*_(d),b="left",S="top";switch(r){case"startArc":x=s+(h-o)*m(y),w=l+(h-o)*_(y),b="center",S="top";break;case"insideStartArc":x=s+(h+o)*m(y),w=l+(h+o)*_(y),b="center",S="bottom";break;case"startAngle":x=s+f*m(d)+Y_(d,o+v,!1),w=l+f*_(d)+q_(d,o+v,!1),b="right",S="middle";break;case"insideStartAngle":x=s+f*m(d)+Y_(d,-o+v,!1),w=l+f*_(d)+q_(d,-o+v,!1),b="left",S="middle";break;case"middle":x=s+f*m(y),w=l+f*_(y),b="center",S="middle";break;case"endArc":x=s+(u+o)*m(y),w=l+(u+o)*_(y),b="center",S="bottom";break;case"insideEndArc":x=s+(u-o)*m(y),w=l+(u-o)*_(y),b="center",S="top";break;case"endAngle":x=s+f*m(g)+Y_(g,o+v,!0),w=l+f*_(g)+q_(g,o+v,!0),b="left",S="middle";break;case"insideEndAngle":x=s+f*m(g)+Y_(g,-o+v,!0),w=l+f*_(g)+q_(g,-o+v,!0),b="right",S="middle";break;default:return Zi(t,e,n)}return(t=t||{}).x=x,t.y=w,t.align=b,t.verticalAlign=S,t}),o){var d=r?"r":"endAngle",g={};h.shape[d]=r?i.r0:i.startAngle,g[d]=i[d],(s?gu:yu)(h,{shape:g},o)}return h}};function tx(t,e,n,i,r,o,a,s){var l,u;o?(u={x:i.x,width:i.width},l={y:i.y,height:i.height}):(u={y:i.y,height:i.height},l={x:i.x,width:i.width}),s||(a?gu:yu)(n,{shape:l},e,r,null),(a?gu:yu)(n,{shape:u},e?t.baseAxis.model:null,r)}function ex(t,e){for(var n=0;n<e.length;n++)if(!isFinite(t[e[n]]))return!0;return!1}var nx=["x","y","width","height"],ix=["cx","cy","r","startAngle","endAngle"],rx={cartesian2d:function(t){return!ex(t,nx)},polar:function(t){return!ex(t,ix)}},ox={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=n?function(t,e){var n=t.get(["itemStyle","borderColor"]);if(!n||"none"===n)return 0;var i=t.get(["itemStyle","borderWidth"])||0,r=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),o=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(i,r,o)}(n,i):0,o=i.width>0?1:-1,a=i.height>0?1:-1;return{x:i.x+o*r/2,y:i.y+a*r/2,width:i.width-o*r,height:i.height-a*r}},polar:function(t,e,n){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}}};function ax(t){return function(t){var e=t?"Arc":"Angle";return function(t){switch(t){case"start":case"insideStart":case"end":case"insideEnd":return t+e;default:return t}}}(t)}function sx(t,e,n,i,r,o,a,s){var l=e.getItemVisual(n,"style");if(s){if(!o.get("roundCap")){var u=t.shape;D(u,Z_(i.getModel("itemStyle"),u,!0)),t.setShape(u)}}else{var h=i.get(["itemStyle","borderRadius"])||0;t.setShape("r",h)}t.useStyle(l);var c=i.getShallow("cursor");c&&t.attr("cursor",c);var p=s?a?r.r>=r.r0?"endArc":"startArc":r.endAngle>=r.startAngle?"endAngle":"startAngle":a?r.height>=0?"bottom":"top":r.width>=0?"right":"left",f=Fu(i);Bu(t,f,{labelFetcher:o,labelDataIndex:n,defaultText:s_(o.getData(),n),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:p});var d=t.getTextContent();if(s&&d){var g=i.get(["label","position"]);t.textConfig.inside="middle"===g||null,function(t,e,n,i){if(X(i))t.setTextConfig({rotation:i});else if(H(e))t.setTextConfig({rotation:0});else{var r,o=t.shape,a=o.clockwise?o.startAngle:o.endAngle,s=o.clockwise?o.endAngle:o.startAngle,l=(a+s)/2,u=n(e);switch(u){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":r=l;break;case"startAngle":case"insideStartAngle":r=a;break;case"endAngle":case"insideEndAngle":r=s;break;default:return void t.setTextConfig({rotation:0})}var h=1.5*Math.PI-r;"middle"===u&&h>Math.PI/2&&h<1.5*Math.PI&&(h-=Math.PI),t.setTextConfig({rotation:h})}}(t,"outside"===g?p:g,ax(a),i.get(["label","rotate"]))}!function(t,e,n,i){if(t){var r=Yu(t);r.prevValue=r.value,r.value=n;var o=e.normal;r.valueAnimation=o.get("valueAnimation"),r.valueAnimation&&(r.precision=o.get("precision"),r.defaultInterpolatedText=i,r.statesModels=e)}}(d,f,o.getRawValue(n),(function(t){return l_(e,t)}));var y=i.getModel(["emphasis"]);Ks(t,y.get("focus"),y.get("blurScope"),y.get("disabled")),Js(t,i),function(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}(r)&&(t.style.fill="none",t.style.stroke="none",R(t.states,(function(t){t.style&&(t.style.fill=t.style.stroke="none")})))}var lx=function(){},ux=function(t){function e(e){var n=t.call(this,e)||this;return n.type="largeBar",n}return n(e,t),e.prototype.getDefaultShape=function(){return new lx},e.prototype.buildPath=function(t,e){for(var n=e.points,i=this.baseDimIdx,r=1-this.baseDimIdx,o=[],a=[],s=this.barWidth,l=0;l<n.length;l+=3)a[i]=s,a[r]=n[l+2],o[i]=n[l+i],o[r]=n[l+r],t.rect(o[0],o[1],a[0],a[1])},e}(Ea);function hx(t,e,n,i){var r=t.getData(),o=r.getLayout("valueAxisHorizontal")?1:0,a=r.getLayout("largeDataIndices"),s=r.getLayout("size"),l=t.getModel("backgroundStyle"),u=r.getLayout("largeBackgroundPoints");if(u){var h=new ux({shape:{points:u},incremental:!!i,silent:!0,z2:0});h.baseDimIdx=o,h.largeDataIndices=a,h.barWidth=s,h.useStyle(l.getItemStyle()),e.add(h),n&&n.push(h)}var c=new ux({shape:{points:r.getLayout("largePoints")},incremental:!!i,ignoreCoarsePointer:!0,z2:1});c.baseDimIdx=o,c.largeDataIndices=a,c.barWidth=s,e.add(c),c.useStyle(r.getVisual("style")),ls(c).seriesIndex=t.seriesIndex,t.get("silent")||(c.on("mousedown",cx),c.on("mousemove",cx)),n&&n.push(c)}var cx=Bf((function(t){var e=function(t,e,n){for(var i=t.baseDimIdx,r=1-i,o=t.shape.points,a=t.largeDataIndices,s=[],l=[],u=t.barWidth,h=0,c=o.length/3;h<c;h++){var p=3*h;if(l[i]=u,l[r]=o[p+2],s[i]=o[p+i],s[r]=o[p+r],l[r]<0&&(s[r]+=l[r],l[r]=-l[r]),e>=s[0]&&e<=s[0]+l[0]&&n>=s[1]&&n<=s[1]+l[1])return a[h]}return-1}(this,t.offsetX,t.offsetY);ls(this).dataIndex=e>=0?e:null}),30,!1);function px(t,e,n){if(D_(n,"cartesian2d")){var i=e,r=n.getArea();return{x:t?i.x:r.x,y:t?r.y:i.y,width:t?i.width:r.width,height:t?r.height:i.height}}var o=e;return{cx:(r=n.getArea()).cx,cy:r.cy,r0:t?r.r0:o.r0,r:t?r.r:o.r,startAngle:t?o.startAngle:0,endAngle:t?o.endAngle:2*Math.PI}}var fx=2*Math.PI,dx=Math.PI/180;function gx(t,e){return ec(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function yx(t,e){var n=gx(t,e),i=t.get("center"),r=t.get("radius");H(r)||(r=[0,r]);var o,a,s=dr(n.width,e.getWidth()),l=dr(n.height,e.getHeight()),u=Math.min(s,l),h=dr(r[0],u/2),c=dr(r[1],u/2),p=t.coordinateSystem;if(p){var f=p.dataToPoint(i);o=f[0]||0,a=f[1]||0}else H(i)||(i=[i,i]),o=dr(i[0],s)+n.x,a=dr(i[1],l)+n.y;return{cx:o,cy:a,r0:h,r:c}}function vx(t,e,n){e.eachSeriesByType(t,(function(t){var e=t.getData(),i=e.mapDimension("value"),r=gx(t,n),o=yx(t,n),a=o.cx,s=o.cy,l=o.r,u=o.r0,h=-t.get("startAngle")*dx,c=t.get("minAngle")*dx,p=0;e.each(i,(function(t){!isNaN(t)&&p++}));var f=e.getSum(i),d=Math.PI/(f||p)*2,g=t.get("clockwise"),y=t.get("roseType"),v=t.get("stillShowZeroSum"),m=e.getDataExtent(i);m[0]=0;var _=fx,x=0,w=h,b=g?1:-1;if(e.setLayout({viewRect:r,r:l}),e.each(i,(function(t,n){var i;if(isNaN(t))e.setItemLayout(n,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:g,cx:a,cy:s,r0:u,r:y?NaN:l});else{(i="area"!==y?0===f&&v?d:t*d:fx/p)<c?(i=c,_-=c):x+=t;var r=w+b*i;e.setItemLayout(n,{angle:i,startAngle:w,endAngle:r,clockwise:g,cx:a,cy:s,r0:u,r:y?fr(t,m,[u,l]):l}),w=r}})),_<fx&&p)if(_<=.001){var S=fx/p;e.each(i,(function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n);i.angle=S,i.startAngle=h+b*n*S,i.endAngle=h+b*(n+1)*S}}))}else d=_/x,w=h,e.each(i,(function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n),r=i.angle===c?c:t*d;i.startAngle=w,i.endAngle=w+b*r,w+=b*r}}))}))}var mx=Math.PI/180;function _x(t,e,n,i,r,o,a,s,l,u){if(!(t.length<2)){for(var h=t.length,c=0;c<h;c++)if("outer"===t[c].position&&"labelLine"===t[c].labelAlignTo){var p=t[c].label.x-u;t[c].linePoints[1][0]+=p,t[c].label.x=u}Jm(t,l,l+a)&&function(t){for(var o={list:[],maxY:0},a={list:[],maxY:0},s=0;s<t.length;s++)if("none"===t[s].labelAlignTo){var l=t[s],u=l.label.y>n?a:o,h=Math.abs(l.label.y-n);if(h>=u.maxY){var c=l.label.x-e-l.len2*r,p=i+l.len,d=Math.abs(c)<p?Math.sqrt(h*h/(1-c*c/p/p)):p;u.rB=d,u.maxY=h}u.list.push(l)}f(o),f(a)}(t)}function f(t){for(var o=t.rB,a=o*o,s=0;s<t.list.length;s++){var l=t.list[s],u=Math.abs(l.label.y-n),h=i+l.len,c=h*h,p=Math.sqrt((1-Math.abs(u*u/a))*c),f=e+(p+l.len2)*r,d=f-l.label.x;xx(l,l.targetTextWidth-d*r,!0),l.label.x=f}}}function xx(t,e,n){if(void 0===n&&(n=!1),null==t.labelStyleWidth){var i=t.label,r=i.style,o=t.rect,a=r.backgroundColor,s=r.padding,l=s?s[1]+s[3]:0,u=r.overflow,h=o.width+(a?0:l);if(e<h||n){var c=o.height;if(u&&u.match("break")){i.setStyle("backgroundColor",null),i.setStyle("width",e-l);var p=i.getBoundingRect();i.setStyle("width",Math.ceil(p.width)),i.setStyle("backgroundColor",a)}else{var f=e-l,d=e<h?f:n?f>t.unconstrainedWidth?null:f:null;i.setStyle("width",d)}var g=i.getBoundingRect();o.width=g.width;var y=(i.style.margin||0)+2.1;o.height=g.height+y,o.y-=(o.height-c)/2}}}function bx(t){return"center"===t.position}function Sx(t){var e,n,i=t.getData(),r=[],o=!1,a=(t.get("minShowLabelAngle")||0)*mx,s=i.getLayout("viewRect"),l=i.getLayout("r"),u=s.width,h=s.x,c=s.y,p=s.height;function f(t){t.ignore=!0}i.each((function(t){var s=i.getItemGraphicEl(t),c=s.shape,p=s.getTextContent(),d=s.getTextGuideLine(),g=i.getItemModel(t),y=g.getModel("label"),v=y.get("position")||g.get(["emphasis","label","position"]),m=y.get("distanceToLabelLine"),_=y.get("alignTo"),x=dr(y.get("edgeDistance"),u),w=y.get("bleedMargin"),b=g.getModel("labelLine"),S=b.get("length");S=dr(S,u);var M=b.get("length2");if(M=dr(M,u),Math.abs(c.endAngle-c.startAngle)<a)return R(p.states,f),p.ignore=!0,void(d&&(R(d.states,f),d.ignore=!0));if(function(t){if(!t.ignore)return!0;for(var e in t.states)if(!1===t.states[e].ignore)return!0;return!1}(p)){var T,C,D,I,k=(c.startAngle+c.endAngle)/2,A=Math.cos(k),L=Math.sin(k);e=c.cx,n=c.cy;var P="inside"===v||"inner"===v;if("center"===v)T=c.cx,C=c.cy,I="center";else{var O=(P?(c.r+c.r0)/2*A:c.r*A)+e,N=(P?(c.r+c.r0)/2*L:c.r*L)+n;if(T=O+3*A,C=N+3*L,!P){var E=O+A*(S+l-c.r),z=N+L*(S+l-c.r),B=E+(A<0?-1:1)*M;T="edge"===_?A<0?h+x:h+u-x:B+(A<0?-m:m),C=z,D=[[O,N],[E,z],[B,z]]}I=P?"center":"edge"===_?A>0?"right":"left":A>0?"left":"right"}var F=Math.PI,V=0,H=y.get("rotate");if(X(H))V=H*(F/180);else if("center"===v)V=0;else if("radial"===H||!0===H){V=A<0?-k+F:-k}else if("tangential"===H&&"outside"!==v&&"outer"!==v){var W=Math.atan2(A,L);W<0&&(W=2*F+W),L>0&&(W=F+W),V=W-F}if(o=!!V,p.x=T,p.y=C,p.rotation=V,p.setStyle({verticalAlign:"middle"}),P){p.setStyle({align:I});var G=p.states.select;G&&(G.x+=p.x,G.y+=p.y)}else{var U=p.getBoundingRect().clone();U.applyTransform(p.getComputedTransform());var Y=(p.style.margin||0)+2.1;U.y-=Y/2,U.height+=Y,r.push({label:p,labelLine:d,position:v,len:S,len2:M,minTurnAngle:b.get("minTurnAngle"),maxSurfaceAngle:b.get("maxSurfaceAngle"),surfaceNormal:new ve(A,L),linePoints:D,textAlign:I,labelDistance:m,labelAlignTo:_,edgeDistance:x,bleedMargin:w,rect:U,unconstrainedWidth:U.width,labelStyleWidth:p.style.width})}s.setTextConfig({inside:P})}})),!o&&t.get("avoidLabelOverlap")&&function(t,e,n,i,r,o,a,s){for(var l=[],u=[],h=Number.MAX_VALUE,c=-Number.MAX_VALUE,p=0;p<t.length;p++){var f=t[p].label;bx(t[p])||(f.x<e?(h=Math.min(h,f.x),l.push(t[p])):(c=Math.max(c,f.x),u.push(t[p])))}for(p=0;p<t.length;p++)if(!bx(y=t[p])&&y.linePoints){if(null!=y.labelStyleWidth)continue;f=y.label;var d=y.linePoints,g=void 0;g="edge"===y.labelAlignTo?f.x<e?d[2][0]-y.labelDistance-a-y.edgeDistance:a+r-y.edgeDistance-d[2][0]-y.labelDistance:"labelLine"===y.labelAlignTo?f.x<e?h-a-y.bleedMargin:a+r-c-y.bleedMargin:f.x<e?f.x-a-y.bleedMargin:a+r-f.x-y.bleedMargin,y.targetTextWidth=g,xx(y,g)}for(_x(u,e,n,i,1,0,o,0,s,c),_x(l,e,n,i,-1,0,o,0,s,h),p=0;p<t.length;p++){var y;if(!bx(y=t[p])&&y.linePoints){f=y.label,d=y.linePoints;var v="edge"===y.labelAlignTo,m=f.style.padding,_=m?m[1]+m[3]:0,x=f.style.backgroundColor?0:_,w=y.rect.width+x,b=d[1][0]-d[2][0];v?f.x<e?d[2][0]=a+y.edgeDistance+w+y.labelDistance:d[2][0]=a+r-y.edgeDistance-w-y.labelDistance:(f.x<e?d[2][0]=f.x+y.labelDistance:d[2][0]=f.x-y.labelDistance,d[1][0]=d[2][0]+b),d[1][1]=d[2][1]=f.y}}}(r,e,n,l,u,p,h,c);for(var d=0;d<r.length;d++){var g=r[d],y=g.label,v=g.labelLine,m=isNaN(y.x)||isNaN(y.y);if(y){y.setStyle({align:g.textAlign}),m&&(R(y.states,f),y.ignore=!0);var _=y.states.select;_&&(_.x+=y.x,_.y+=y.y)}if(v){var x=g.linePoints;m||!x?(R(v.states,f),v.ignore=!0):(jm(x,g.minTurnAngle),Km(x,g.surfaceNormal,g.maxSurfaceAngle),v.setShape({points:x}),y.__hostTarget.textGuideLineConfig={anchor:new ve(x[0][0],x[0][1])})}}}var Mx=function(t){function e(e,n,i){var r=t.call(this)||this;r.z2=2;var o=new Ka;return r.setTextContent(o),r.updateData(e,n,i,!0),r}return n(e,t),e.prototype.updateData=function(t,e,n,i){var r=this,o=t.hostModel,a=t.getItemModel(e),s=a.getModel("emphasis"),l=t.getItemLayout(e),u=D(Z_(a.getModel("itemStyle"),l,!0),l);if(isNaN(u.startAngle))r.setShape(u);else{if(i){r.setShape(u);var h=o.getShallow("animationType");o.ecModel.ssr?(yu(r,{scaleX:0,scaleY:0},o,{dataIndex:e,isFrom:!0}),r.originX=u.cx,r.originY=u.cy):"scale"===h?(r.shape.r=l.r0,yu(r,{shape:{r:l.r}},o,e)):null!=n?(r.setShape({startAngle:n,endAngle:n}),yu(r,{shape:{startAngle:l.startAngle,endAngle:l.endAngle}},o,e)):(r.shape.endAngle=l.startAngle,gu(r,{shape:{endAngle:l.endAngle}},o,e))}else wu(r),gu(r,{shape:u},o,e);r.useStyle(t.getItemVisual(e,"style")),Js(r,a);var c=(l.startAngle+l.endAngle)/2,p=o.get("selectedOffset"),f=Math.cos(c)*p,d=Math.sin(c)*p,g=a.getShallow("cursor");g&&r.attr("cursor",g),this._updateLabel(o,t,e),r.ensureState("emphasis").shape=D({r:l.r+(s.get("scale")&&s.get("scaleSize")||0)},Z_(s.getModel("itemStyle"),l)),D(r.ensureState("select"),{x:f,y:d,shape:Z_(a.getModel(["select","itemStyle"]),l)}),D(r.ensureState("blur"),{shape:Z_(a.getModel(["blur","itemStyle"]),l)});var y=r.getTextGuideLine(),v=r.getTextContent();y&&D(y.ensureState("select"),{x:f,y:d}),D(v.ensureState("select"),{x:f,y:d}),Ks(this,s.get("focus"),s.get("blurScope"),s.get("disabled"))}},e.prototype._updateLabel=function(t,e,n){var i=this,r=e.getItemModel(n),o=r.getModel("labelLine"),a=e.getItemVisual(n,"style"),s=a&&a.fill,l=a&&a.opacity;Bu(i,Fu(r),{labelFetcher:e.hostModel,labelDataIndex:n,inheritColor:s,defaultOpacity:l,defaultText:t.getFormattedLabel(n,"normal")||e.getName(n)});var u=i.getTextContent();i.setTextConfig({position:null,rotation:null}),u.attr({z2:10});var h=t.get(["label","position"]);if("outside"!==h&&"outer"!==h)i.removeTextGuideLine();else{var c=this.getTextGuideLine();c||(c=new Yl,this.setTextGuideLine(c)),function(t,e,n){var i=t.getTextGuideLine(),r=t.getTextContent();if(r){for(var o=e.normal,a=o.get("show"),s=r.ignore,l=0;l<ds.length;l++){var u=ds[l],h=e[u],c="normal"===u;if(h){var p=h.get("show");if((c?s:et(r.states[u]&&r.states[u].ignore,s))||!et(p,a)){var f=c?i:i&&i.states[u];f&&(f.ignore=!0);continue}i||(i=new Yl,t.setTextGuideLine(i),c||!s&&a||$m(i,!0,"normal",e.normal),t.stateProxy&&(i.stateProxy=t.stateProxy)),$m(i,!1,u,h)}}if(i){I(i.style,n),i.style.fill=null;var d=o.get("showAbove");(t.textGuideLineConfig=t.textGuideLineConfig||{}).showAbove=d||!1,i.buildPath=Qm}}else i&&t.removeTextGuideLine()}(this,function(t,e){e=e||"labelLine";for(var n={normal:t.getModel(e)},i=0;i<fs.length;i++){var r=fs[i];n[r]=t.getModel([r,e])}return n}(r),{stroke:s,opacity:nt(o.get(["lineStyle","opacity"]),l,1)})}},e}(Fl),Tx=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.ignoreLabelLineUpdate=!0,e}return n(e,t),e.prototype.render=function(t,e,n,i){var r,o=t.getData(),a=this._data,s=this.group;if(!a&&o.count()>0){for(var l=o.getItemLayout(0),u=1;isNaN(l&&l.startAngle)&&u<o.count();++u)l=o.getItemLayout(u);l&&(r=l.startAngle)}if(this._emptyCircleSector&&s.remove(this._emptyCircleSector),0===o.count()&&t.get("showEmptyCircle")){var h=new Fl({shape:yx(t,n)});h.useStyle(t.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=h,s.add(h)}o.diff(a).add((function(t){var e=new Mx(o,t,r);o.setItemGraphicEl(t,e),s.add(e)})).update((function(t,e){var n=a.getItemGraphicEl(e);n.updateData(o,t,r),n.off("click"),s.add(n),o.setItemGraphicEl(t,n)})).remove((function(e){xu(a.getItemGraphicEl(e),t,e)})).execute(),Sx(t),"expansion"!==t.get("animationTypeUpdate")&&(this._data=o)},e.prototype.dispose=function(){},e.prototype.containPoint=function(t,e){var n=e.getData().getItemLayout(0);if(n){var i=t[0]-n.cx,r=t[1]-n.cy,o=Math.sqrt(i*i+r*r);return o<=n.r&&o>=n.r0}},e.type="pie",e}(Pf);var Cx=function(){function t(t,e){this._getDataWithEncodedVisual=t,this._getRawData=e}return t.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},t.prototype.containName=function(t){return this._getRawData().indexOfName(t)>=0},t.prototype.indexOfName=function(t){return this._getDataWithEncodedVisual().indexOfName(t)},t.prototype.getItemVisual=function(t,e){return this._getDataWithEncodedVisual().getItemVisual(t,e)},t}(),Dx=Zr(),Ix=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new Cx(F(this.getData,this),F(this.getRawData,this)),this._defaultLabelLine(e)},e.prototype.mergeOption=function(){t.prototype.mergeOption.apply(this,arguments)},e.prototype.getInitialData=function(){return function(t,e,n){e=H(e)&&{coordDimensions:e}||D({encodeDefine:t.getEncode()},e);var i=t.getSource(),r=tv(i,e).dimensions,o=new Jy(r,t);return o.initData(i,n),o}(this,{coordDimensions:["value"],encodeDefaulter:V(Mc,this)})},e.prototype.getDataParams=function(e){var n=this.getData(),i=Dx(n),r=i.seats;if(!r){var o=[];n.each(n.mapDimension("value"),(function(t){o.push(t)})),r=i.seats=_r(o,n.hostModel.get("percentPrecision"))}var a=t.prototype.getDataParams.call(this,e);return a.percent=r[e]||0,a.$vars.push("percent"),a},e.prototype._defaultLabelLine=function(t){zr(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},e.type="series.pie",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},e}(xf);var kx=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.type="grid",e.dependencies=["xAxis","yAxis"],e.layoutMode="box",e.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},e}(ac),Ax=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",Qr).models[0]},e.type="cartesian2dAxis",e}(ac);P(Ax,um);var Lx={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},Px=C({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Lx),Ox=C({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Lx),Rx={category:Px,value:Ox,time:C({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Ox),log:I({logBase:10},Ox)},Nx={value:1,category:1,time:1,log:1};function Ex(t,e,i,r){R(Nx,(function(o,a){var s=C(C({},Rx[a],!0),r,!0),l=function(t){function i(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e+"Axis."+a,n}return n(i,t),i.prototype.mergeDefaultAndTheme=function(t,e){var n=nc(this),i=n?rc(t):{};C(t,e.getTheme().get(a+"Axis")),C(t,this.getDefaultOption()),t.type=zx(t),n&&ic(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=cv.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.type=e+"Axis."+a,i.defaultOption=s,i}(i);t.registerComponentModel(l)})),t.registerSubTypeDefaulter(e+"Axis",zx)}function zx(t){return t.type||(t.data?"category":"value")}var Bx=function(){function t(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return t.prototype.getAxis=function(t){return this._axes[t]},t.prototype.getAxes=function(){return N(this._dimList,(function(t){return this._axes[t]}),this)},t.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),z(this.getAxes(),(function(e){return e.scale.type===t}))},t.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},t}(),Fx=["x","y"];function Vx(t){return"interval"===t.type||"time"===t.type}var Hx=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=Fx,e}return n(e,t),e.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t=this.getAxis("x").scale,e=this.getAxis("y").scale;if(Vx(t)&&Vx(e)){var n=t.getExtent(),i=e.getExtent(),r=this.dataToPoint([n[0],i[0]]),o=this.dataToPoint([n[1],i[1]]),a=n[1]-n[0],s=i[1]-i[0];if(a&&s){var l=(o[0]-r[0])/a,u=(o[1]-r[1])/s,h=r[0]-n[0]*l,c=r[1]-i[0]*u,p=this._transform=[l,0,0,u,h,c];this._invTransform=ge([],p)}}},e.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},e.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},e.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},e.prototype.containZone=function(t,e){var n=this.dataToPoint(t),i=this.dataToPoint(e),r=this.getArea(),o=new Ce(n[0],n[1],i[0]-n[0],i[1]-n[1]);return r.intersect(o)},e.prototype.dataToPoint=function(t,e,n){n=n||[];var i=t[0],r=t[1];if(this._transform&&null!=i&&isFinite(i)&&null!=r&&isFinite(r))return Et(n,t,this._transform);var o=this.getAxis("x"),a=this.getAxis("y");return n[0]=o.toGlobalCoord(o.dataToCoord(i,e)),n[1]=a.toGlobalCoord(a.dataToCoord(r,e)),n},e.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),a=n.parse(t[0]),s=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),a),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),s),Math.max(o[0],o[1])),e},e.prototype.pointToData=function(t,e){var n=[];if(this._invTransform)return Et(n,t,this._invTransform);var i=this.getAxis("x"),r=this.getAxis("y");return n[0]=i.coordToData(i.toLocalCoord(t[0]),e),n[1]=r.coordToData(r.toLocalCoord(t[1]),e),n},e.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},e.prototype.getArea=function(){var t=this.getAxis("x").getGlobalExtent(),e=this.getAxis("y").getGlobalExtent(),n=Math.min(t[0],t[1]),i=Math.min(e[0],e[1]),r=Math.max(t[0],t[1])-n,o=Math.max(e[0],e[1])-i;return new Ce(n,i,r,o)},e}(Bx),Wx=function(t){function e(e,n,i,r,o){var a=t.call(this,e,n,i)||this;return a.index=0,a.type=r||"value",a.position=o||"bottom",a}return n(e,t),e.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},e.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},e.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},e.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)},e}(Fm);function Gx(t){return"cartesian2d"===t.get("coordinateSystem")}function Ux(t){var e={xAxisModel:null,yAxisModel:null};return R(e,(function(n,i){var r=i.replace(/Model$/,""),o=t.getReferringComponents(r,Qr).models[0];e[i]=o})),e}var Xx=Math.log;function Yx(t,e,n){var i=Sv.prototype,r=i.getTicks.call(n),o=i.getTicks.call(n,!0),a=r.length-1,s=i.getInterval.call(n),l=nm(t,e),u=l.extent,h=l.fixMin,c=l.fixMax;if("log"===t.type){var p=Xx(t.base);u=[Xx(u[0])/p,Xx(u[1])/p]}t.setExtent(u[0],u[1]),t.calcNiceExtent({splitNumber:a,fixMin:h,fixMax:c});var f=i.getExtent.call(t);h&&(u[0]=f[0]),c&&(u[1]=f[1]);var d=i.getInterval.call(t),g=u[0],y=u[1];if(h&&c)d=(y-g)/a;else if(h)for(y=u[0]+d*a;y<u[1]&&isFinite(y)&&isFinite(u[1]);)d=gv(d),y=u[0]+d*a;else if(c)for(g=u[1]-d*a;g>u[0]&&isFinite(g)&&isFinite(u[0]);)d=gv(d),g=u[1]-d*a;else{t.getTicks().length-1>a&&(d=gv(d));var v=d*a;(g=gr((y=Math.ceil(u[1]/d)*d)-v))<0&&u[0]>=0?(g=0,y=gr(v)):y>0&&u[1]<=0&&(y=0,g=-gr(v))}var m=(r[0].value-o[0].value)/s,_=(r[a].value-o[a].value)/s;i.setExtent.call(t,g+d*m,y+d*_),i.setInterval.call(t,d),(m||_)&&i.setNiceExtent.call(t,g+d,y-d)}var qx=function(){function t(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=Fx,this._initCartesian(t,e,n),this.model=t}return t.prototype.getRect=function(){return this._rect},t.prototype.update=function(t,e){var n=this._axesMap;function i(t){var e,n=B(t),i=n.length;if(i){for(var r=[],o=i-1;o>=0;o--){var a=t[+n[o]],s=a.model,l=a.scale;fv(l)&&s.get("alignTicks")&&null==s.get("interval")?r.push(a):(im(l,s),fv(l)&&(e=a))}r.length&&(e||im((e=r.pop()).scale,e.model),R(r,(function(t){Yx(t.scale,t.model,e.scale)})))}}this._updateScale(t,this.model),i(n.x),i(n.y);var r={};R(n.x,(function(t){jx(n,"y",t,r)})),R(n.y,(function(t){jx(n,"x",t,r)})),this.resize(this.model,e)},t.prototype.resize=function(t,e,n){var i=t.getBoxLayoutParams(),r=!n&&t.get("containLabel"),o=ec(i,{width:e.getWidth(),height:e.getHeight()});this._rect=o;var a=this._axesList;function s(){R(a,(function(t){var e=t.isHorizontal(),n=e?[0,o.width]:[0,o.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),function(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}(t,e?o.x:o.y)}))}s(),r&&(R(a,(function(t){if(!t.model.get(["axisLabel","inside"])){var e=function(t){var e=t.model,n=t.scale;if(e.get(["axisLabel","show"])&&!n.isBlank()){var i,r,o=n.getExtent();r=n instanceof wv?n.count():(i=n.getTicks()).length;var a,s=t.getLabelModel(),l=om(t),u=1;r>40&&(u=Math.ceil(r/40));for(var h=0;h<r;h+=u){var c=l(i?i[h]:{value:o[0]+h},h),p=am(s.getTextRect(c),s.get("rotate")||0);a?a.union(p):a=p}return a}}(t);if(e){var n=t.isHorizontal()?"height":"width",i=t.model.get(["axisLabel","margin"]);o[n]-=e[n]+i,"top"===t.position?o.y+=e.height+i:"left"===t.position&&(o.x+=e.width+i)}}})),s()),R(this._coordsList,(function(t){t.calcAffineTransform()}))},t.prototype.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n)return n[e||0]},t.prototype.getAxes=function(){return this._axesList.slice()},t.prototype.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}Y(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},t.prototype.getCartesians=function(){return this._coordsList.slice()},t.prototype.convertToPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},t.prototype.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},t.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,r=t.xAxisModel||i&&i.getReferringComponents("xAxis",Qr).models[0],o=t.yAxisModel||i&&i.getReferringComponents("yAxis",Qr).models[0],a=t.gridModel,s=this._coordsList;if(i)A(s,e=i.coordinateSystem)<0&&(e=null);else if(r&&o)e=this.getCartesian(r.componentIndex,o.componentIndex);else if(r)n=this.getAxis("x",r.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(a){a.coordinateSystem===this&&(e=this._coordsList[0])}return{cartesian:e,axis:n}},t.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},t.prototype._initCartesian=function(t,e,n){var i=this,r=this,o={left:!1,right:!1,top:!1,bottom:!1},a={x:{},y:{}},s={x:0,y:0};if(e.eachComponent("xAxis",l("x"),this),e.eachComponent("yAxis",l("y"),this),!s.x||!s.y)return this._axesMap={},void(this._axesList=[]);function l(e){return function(n,i){if(Zx(n,t)){var l=n.get("position");"x"===e?"top"!==l&&"bottom"!==l&&(l=o.bottom?"top":"bottom"):"left"!==l&&"right"!==l&&(l=o.left?"right":"left"),o[l]=!0;var u=new Wx(e,rm(n),[0,0],n.get("type"),l),h="category"===u.type;u.onBand=h&&n.get("boundaryGap"),u.inverse=n.get("inverse"),n.axis=u,u.model=n,u.grid=r,u.index=i,r._axesList.push(u),a[e][i]=u,s[e]++}}}this._axesMap=a,R(a.x,(function(e,n){R(a.y,(function(r,o){var a="x"+n+"y"+o,s=new Hx(a);s.master=i,s.model=t,i._coordsMap[a]=s,i._coordsList.push(s),s.addAxis(e),s.addAxis(r)}))}))},t.prototype._updateScale=function(t,e){function n(t,e){R(function(t,e){var n={};return R(t.mapDimensionsAll(e),(function(e){n[sv(t,e)]=!0})),B(n)}(t,e.dim),(function(n){e.scale.unionExtentFromData(t,n)}))}R(this._axesList,(function(t){if(t.scale.setExtent(1/0,-1/0),"category"===t.type){var e=t.model.get("categorySortInfo");t.scale.setSortInfo(e)}})),t.eachSeries((function(t){if(Gx(t)){var i=Ux(t),r=i.xAxisModel,o=i.yAxisModel;if(!Zx(r,e)||!Zx(o,e))return;var a=this.getCartesian(r.componentIndex,o.componentIndex),s=t.getData(),l=a.getAxis("x"),u=a.getAxis("y");n(s,l),n(s,u)}}),this)},t.prototype.getTooltipAxes=function(t){var e=[],n=[];return R(this.getCartesians(),(function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),o=i.getOtherAxis(r);A(e,r)<0&&e.push(r),A(n,o)<0&&n.push(o)})),{baseAxes:e,otherAxes:n}},t.create=function(e,n){var i=[];return e.eachComponent("grid",(function(r,o){var a=new t(r,e,n);a.name="grid_"+o,a.resize(r,n,!0),r.coordinateSystem=a,i.push(a)})),e.eachSeries((function(t){if(Gx(t)){var e=Ux(t),n=e.xAxisModel,i=e.yAxisModel,r=n.getCoordSysModel();0;var o=r.coordinateSystem;t.coordinateSystem=o.getCartesian(n.componentIndex,i.componentIndex)}})),i},t.dimensions=Fx,t}();function Zx(t,e){return t.getCoordSysModel()===e}function jx(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],a=n.model,s=a.get(["axisLine","onZero"]),l=a.get(["axisLine","onZeroAxisIndex"]);if(s){if(null!=l)Kx(o[l])&&(r=o[l]);else for(var u in o)if(o.hasOwnProperty(u)&&Kx(o[u])&&!i[h(o[u])]){r=o[u];break}r&&(i[h(r)]=!0)}function h(t){return t.dim+"_"+t.index}}function Kx(t){return t&&"category"!==t.type&&"time"!==t.type&&function(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)}(t)}var $x=Math.PI,Qx=function(){function t(t,e){this.group=new or,this.opt=e,this.axisModel=t,I(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var n=new or({x:e.position[0],y:e.position[1],rotation:e.rotation});n.updateTransform(),this._transformGroup=n}return t.prototype.hasBuilder=function(t){return!!Jx[t]},t.prototype.add=function(t){Jx[t](this.opt,this.axisModel,this.group,this._transformGroup)},t.prototype.getGroup=function(){return this.group},t.innerTextLayout=function(t,e,n){var i,r,o=wr(e-t);return br(o)?(r=n>0?"top":"bottom",i="center"):br(o-$x)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=o>0&&o<$x?n>0?"right":"left":n>0?"left":"right"),{rotation:o,textAlign:i,textVerticalAlign:r}},t.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},t.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},t}(),Jx={axisLine:function(t,e,n,i){var r=e.get(["axisLine","show"]);if("auto"===r&&t.handleAutoShown&&(r=t.handleAutoShown("axisLine")),r){var o=e.axis.getExtent(),a=i.transform,s=[o[0],0],l=[o[1],0],u=s[0]>l[0];a&&(Et(s,s,a),Et(l,l,a));var h=D({lineCap:"round"},e.getModel(["axisLine","lineStyle"]).getLineStyle()),c=new jl({shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:h,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1});Pu(c.shape,c.style.lineWidth),c.anid="line",n.add(c);var p=e.get(["axisLine","symbol"]);if(null!=p){var f=e.get(["axisLine","symbolSize"]);G(p)&&(p=[p,p]),(G(f)||X(f))&&(f=[f,f]);var d=Ed(e.get(["axisLine","symbolOffset"])||0,f),g=f[0],y=f[1];R([{rotate:t.rotation+Math.PI/2,offset:d[0],r:0},{rotate:t.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-l[0])*(s[0]-l[0])+(s[1]-l[1])*(s[1]-l[1]))}],(function(e,i){if("none"!==p[i]&&null!=p[i]){var r=Nd(p[i],-g/2,-y/2,g,y,h.stroke,!0),o=e.r+e.offset,a=u?l:s;r.attr({rotation:e.rotate,x:a[0]+o*Math.cos(t.rotation),y:a[1]-o*Math.sin(t.rotation),silent:!0,z2:11}),n.add(r)}}))}}},axisTickLabel:function(t,e,n,i){var r=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(!a||r.scale.isBlank())return;for(var s=o.getModel("lineStyle"),l=i.tickDirection*o.get("length"),u=iw(r.getTicksCoords(),e.transform,l,I(s.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<u.length;h++)t.add(u[h]);return u}(n,i,e,t),o=function(t,e,n,i){var r=n.axis,o=tt(i.axisLabelShow,n.get(["axisLabel","show"]));if(!o||r.scale.isBlank())return;var a=n.getModel("axisLabel"),s=a.get("margin"),l=r.getViewLabels(),u=(tt(i.labelRotate,a.get("rotate"))||0)*$x/180,h=Qx.innerTextLayout(i.rotation,u,i.labelDirection),c=n.getCategories&&n.getCategories(!0),p=[],f=Qx.isLabelSilent(n),d=n.get("triggerEvent");return R(l,(function(o,l){var u="ordinal"===r.scale.type?r.scale.getRawOrdinalNumber(o.tickValue):o.tickValue,g=o.formattedLabel,y=o.rawLabel,v=a;if(c&&c[u]){var m=c[u];Y(m)&&m.textStyle&&(v=new oh(m.textStyle,a,n.ecModel))}var _=v.getTextColor()||n.get(["axisLine","lineStyle","color"]),x=r.dataToCoord(u),w=new Ka({x:x,y:i.labelOffset+i.labelDirection*s,rotation:h.rotation,silent:f,z2:10+(o.level||0),style:Vu(v,{text:g,align:v.getShallow("align",!0)||h.textAlign,verticalAlign:v.getShallow("verticalAlign",!0)||v.getShallow("baseline",!0)||h.textVerticalAlign,fill:W(_)?_("category"===r.type?y:"value"===r.type?u+"":u,l):_})});if(w.anid="label_"+u,d){var b=Qx.makeAxisEventDataBase(n);b.targetType="axisLabel",b.value=y,b.tickIndex=l,"category"===r.type&&(b.dataIndex=u),ls(w).eventData=b}e.add(w),w.updateTransform(),p.push(w),t.add(w),w.decomposeTransform()})),p}(n,i,e,t);(function(t,e,n){if(lm(t.axis))return;var i=t.get(["axisLabel","showMinLabel"]),r=t.get(["axisLabel","showMaxLabel"]);e=e||[],n=n||[];var o=e[0],a=e[1],s=e[e.length-1],l=e[e.length-2],u=n[0],h=n[1],c=n[n.length-1],p=n[n.length-2];!1===i?(tw(o),tw(u)):ew(o,a)&&(i?(tw(a),tw(h)):(tw(o),tw(u)));!1===r?(tw(s),tw(c)):ew(l,s)&&(r?(tw(l),tw(p)):(tw(s),tw(c)))}(e,o,r),function(t,e,n,i){var r=n.axis,o=n.getModel("minorTick");if(!o.get("show")||r.scale.isBlank())return;var a=r.getMinorTicksCoords();if(!a.length)return;for(var s=o.getModel("lineStyle"),l=i*o.get("length"),u=I(s.getLineStyle(),I(n.getModel("axisTick").getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])})),h=0;h<a.length;h++)for(var c=iw(a[h],e.transform,l,u,"minorticks_"+h),p=0;p<c.length;p++)t.add(c[p])}(n,i,e,t.tickDirection),e.get(["axisLabel","hideOverlap"]))&&function(t){var e=[];t.sort((function(t,e){return e.priority-t.priority}));var n=new Ce(0,0,0,0);function i(t){if(!t.ignore){var e=t.ensureState("emphasis");null==e.ignore&&(e.ignore=!1)}t.ignore=!0}for(var r=0;r<t.length;r++){var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine;n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05;for(var c=o.obb,p=!1,f=0;f<e.length;f++){var d=e[f];if(n.intersect(d.rect)){if(a&&d.axisAligned){p=!0;break}if(d.obb||(d.obb=new hu(d.localRect,d.transform)),c||(c=new hu(s,l)),c.intersect(d.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}(function(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];if(!i.defaultAttr.ignore){var r=i.label,o=r.getComputedTransform(),a=r.getBoundingRect(),s=!o||o[1]<1e-5&&o[2]<1e-5,l=r.style.margin||0,u=a.clone();u.applyTransform(o),u.x-=l/2,u.y-=l/2,u.width+=l,u.height+=l;var h=s?new hu(a,o):null;e.push({label:r,labelLine:i.labelLine,rect:u,localRect:a,obb:h,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:o})}}return e}(N(o,(function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}}))))},axisName:function(t,e,n,i){var r=tt(t.axisName,e.get("name"));if(r){var o,a,s=e.get("nameLocation"),l=t.nameDirection,u=e.getModel("nameTextStyle"),h=e.get("nameGap")||0,c=e.axis.getExtent(),p=c[0]>c[1]?-1:1,f=["start"===s?c[0]-p*h:"end"===s?c[1]+p*h:(c[0]+c[1])/2,nw(s)?t.labelOffset+l*h:0],d=e.get("nameRotate");null!=d&&(d=d*$x/180),nw(s)?o=Qx.innerTextLayout(t.rotation,null!=d?d:t.rotation,l):(o=function(t,e,n,i){var r,o,a=wr(n-t),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;br(a-$x/2)?(o=l?"bottom":"top",r="center"):br(a-1.5*$x)?(o=l?"top":"bottom",r="center"):(o="middle",r=a<1.5*$x&&a>$x/2?l?"left":"right":l?"right":"left");return{rotation:a,textAlign:r,textVerticalAlign:o}}(t.rotation,s,d||0,c),null!=(a=t.axisNameAvailableWidth)&&(a=Math.abs(a/Math.sin(o.rotation)),!isFinite(a)&&(a=null)));var g=u.getFont(),y=e.get("nameTruncate",!0)||{},v=y.ellipsis,m=tt(t.nameTruncateMaxWidth,y.maxWidth,a),_=new Ka({x:f[0],y:f[1],rotation:o.rotation,silent:Qx.isLabelSilent(e),style:Vu(u,{text:r,font:g,overflow:"truncate",width:m,ellipsis:v,fill:u.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:u.get("align")||o.textAlign,verticalAlign:u.get("verticalAlign")||o.textVerticalAlign}),z2:1});if(function(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,r=G(e)?{formatter:e}:e,o=n.mainType,a=n.componentIndex,s={componentType:o,name:i,$vars:["name"]};s[o+"Index"]=a;var l=t.formatterParamsExtra;l&&R(B(l),(function(t){vt(s,t)||(s[t]=l[t],s.$vars.push(t))}));var u=ls(t.el);u.componentMainType=o,u.componentIndex=a,u.tooltipConfig={name:i,option:I({content:i,formatterParams:s},r)}}({el:_,componentModel:e,itemName:r}),_.__fullText=r,_.anid="name",e.get("triggerEvent")){var x=Qx.makeAxisEventDataBase(e);x.targetType="axisName",x.name=r,ls(_).eventData=x}i.add(_),_.updateTransform(),n.add(_),_.decomposeTransform()}}};function tw(t){t&&(t.ignore=!0)}function ew(t,e){var n=t&&t.getBoundingRect().clone(),i=e&&e.getBoundingRect().clone();if(n&&i){var r=ue([]);return fe(r,r,-t.rotation),n.applyTransform(ce([],r,t.getLocalTransform())),i.applyTransform(ce([],r,e.getLocalTransform())),n.intersect(i)}}function nw(t){return"middle"===t||"center"===t}function iw(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord;a[0]=u,a[1]=0,s[0]=u,s[1]=n,e&&(Et(a,a,e),Et(s,s,e));var h=new jl({shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});Pu(h.shape,h.style.lineWidth),h.anid=r+"_"+t[l].tickValue,o.push(h)}return o}function rw(t){var e=ow(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=i.parse(a));var s=function(t){return!!t.get(["handle","show"])}(n);null==o&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function ow(t){var e,n=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return n&&n.axesInfo[(e=t,e.type+"||"+e.id)]}var aw={},sw=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.prototype.render=function(e,n,i,r){this.axisPointerClass&&rw(e),t.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,i,!0)},e.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},e.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},e.prototype.dispose=function(e,n){this._disposeAxisPointer(n),t.prototype.dispose.apply(this,arguments)},e.prototype._doUpdateAxisPointerClass=function(t,n,i){var r=e.getAxisPointerClass(this.axisPointerClass);if(r){var o=function(t){var e=ow(t);return e&&e.axisPointerModel}(t);o?(this._axisPointer||(this._axisPointer=new r)).render(t,o,n,i):this._disposeAxisPointer(n)}},e.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},e.registerAxisPointerClass=function(t,e){aw[t]=e},e.getAxisPointerClass=function(t){return t&&aw[t]},e.type="axis",e}(If),lw=Zr();var uw=["axisLine","axisTickLabel","axisName"],hw=["splitArea","splitLine","minorSplitLine"],cw=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.axisPointerClass="CartesianAxisPointer",n}return n(e,t),e.prototype.render=function(e,n,i,r){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new or,this.group.add(this._axisGroup),e.get("show")){var a=e.getCoordSysModel(),s=function(t,e,n){n=n||{};var i=t.coordinateSystem,r=e.axis,o={},a=r.getAxesOnZeroOf()[0],s=r.position,l=a?"onZero":s,u=r.dim,h=i.getRect(),c=[h.x,h.x+h.width,h.y,h.y+h.height],p={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,d="x"===u?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(a){var g=a.toGlobalCoord(a.dataToCoord(0));d[p.onZero]=Math.max(Math.min(g,d[1]),d[0])}o.position=["y"===u?d[p[l]]:c[0],"x"===u?d[p[l]]:c[3]],o.rotation=Math.PI/2*("x"===u?0:1),o.labelDirection=o.tickDirection=o.nameDirection={top:-1,bottom:1,left:-1,right:1}[s],o.labelOffset=a?d[p[s]]-d[p.onZero]:0,e.get(["axisTick","inside"])&&(o.tickDirection=-o.tickDirection),tt(n.labelInside,e.get(["axisLabel","inside"]))&&(o.labelDirection=-o.labelDirection);var y=e.get(["axisLabel","rotate"]);return o.labelRotate="top"===l?-y:y,o.z2=1,o}(a,e),l=new Qx(e,D({handleAutoShown:function(t){for(var n=a.coordinateSystem.getCartesians(),i=0;i<n.length;i++)if(fv(n[i].getOtherAxis(e.axis).scale))return!0;return!1}},s));R(uw,l.add,l),this._axisGroup.add(l.getGroup()),R(hw,(function(t){e.get([t,"show"])&&pw[t](this,this._axisGroup,e,a)}),this),r&&"changeAxisOrder"===r.type&&r.isInitSort||function(t,e,n){if(t&&e){var i,r=(i={},t.traverse((function(t){Ou(t)&&t.anid&&(i[t.anid]=t)})),i);e.traverse((function(t){if(Ou(t)&&t.anid){var e=r[t.anid];if(e){var i=o(t);t.attr(o(e)),gu(t,i,n,ls(t).dataIndex)}}}))}function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return function(t){return null!=t.shape}(t)&&(e.shape=D({},t.shape)),e}}(o,this._axisGroup,e),t.prototype.render.call(this,e,n,i,r)}},e.prototype.remove=function(){lw(this).splitAreaColors=null},e.type="cartesianAxis",e}(sw),pw={splitLine:function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitLine"),a=o.getModel("lineStyle"),s=a.get("color");s=H(s)?s:[s];for(var l=i.coordinateSystem.getRect(),u=r.isHorizontal(),h=0,c=r.getTicksCoords({tickModel:o}),p=[],f=[],d=a.getLineStyle(),g=0;g<c.length;g++){var y=r.toGlobalCoord(c[g].coord);u?(p[0]=y,p[1]=l.y,f[0]=y,f[1]=l.y+l.height):(p[0]=l.x,p[1]=y,f[0]=l.x+l.width,f[1]=y);var v=h++%s.length,m=c[g].tickValue,_=new jl({anid:null!=m?"line_"+c[g].tickValue:null,autoBatch:!0,shape:{x1:p[0],y1:p[1],x2:f[0],y2:f[1]},style:I({stroke:s[v]},d),silent:!0});Pu(_.shape,d.lineWidth),e.add(_)}}},minorSplitLine:function(t,e,n,i){var r=n.axis,o=n.getModel("minorSplitLine").getModel("lineStyle"),a=i.coordinateSystem.getRect(),s=r.isHorizontal(),l=r.getMinorTicksCoords();if(l.length)for(var u=[],h=[],c=o.getLineStyle(),p=0;p<l.length;p++)for(var f=0;f<l[p].length;f++){var d=r.toGlobalCoord(l[p][f].coord);s?(u[0]=d,u[1]=a.y,h[0]=d,h[1]=a.y+a.height):(u[0]=a.x,u[1]=d,h[0]=a.x+a.width,h[1]=d);var g=new jl({anid:"minor_line_"+l[p][f].tickValue,autoBatch:!0,shape:{x1:u[0],y1:u[1],x2:h[0],y2:h[1]},style:c,silent:!0});Pu(g.shape,c.lineWidth),e.add(g)}},splitArea:function(t,e,n,i){!function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitArea"),a=o.getModel("areaStyle"),s=a.get("color"),l=i.coordinateSystem.getRect(),u=r.getTicksCoords({tickModel:o,clamp:!0});if(u.length){var h=s.length,c=lw(t).splitAreaColors,p=ft(),f=0;if(c)for(var d=0;d<u.length;d++){var g=c.get(u[d].tickValue);if(null!=g){f=(g+(h-1)*d)%h;break}}var y=r.toGlobalCoord(u[0].coord),v=a.getAreaStyle();for(s=H(s)?s:[s],d=1;d<u.length;d++){var m=r.toGlobalCoord(u[d].coord),_=void 0,x=void 0,w=void 0,b=void 0;r.isHorizontal()?(_=y,x=l.y,w=m-_,b=l.height,y=_+w):(_=l.x,x=y,w=l.width,y=x+(b=m-x));var S=u[d-1].tickValue;null!=S&&p.set(S,f),e.add(new qa({anid:null!=S?"area_"+S:null,shape:{x:_,y:x,width:w,height:b},style:I({fill:s[f]},v),autoBatch:!0,silent:!0})),f=(f+1)%h}lw(t).splitAreaColors=p}}}(t,e,n,i)}},fw=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.type="xAxis",e}(cw),dw=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=fw.type,e}return n(e,t),e.type="yAxis",e}(cw),gw=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="grid",e}return n(e,t),e.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new qa({shape:t.coordinateSystem.getRect(),style:I({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},e.type="grid",e}(If),yw={offset:0};var vw={label:{enabled:!0},decal:{show:!1}},mw=Zr(),_w={};function xw(t,e){var n=t.getModel("aria");if(n.get("enabled")){var i=T(vw);C(i.label,t.getLocaleModel().get("aria"),!1),C(n.option,i,!1),function(){if(n.getModel("decal").get("show")){var e=ft();t.eachSeries((function(t){if(!t.isColorBySeries()){var n=e.get(t.type);n||(n={},e.set(t.type,n)),mw(t).scope=n}})),t.eachRawSeries((function(e){if(!t.isSeriesFiltered(e))if(W(e.enableAriaDecal))e.enableAriaDecal();else{var n=e.getData();if(e.isColorBySeries()){var i=Nc(e.ecModel,e.name,_w,t.getSeriesCount()),r=n.getVisual("decal");n.setVisual("decal",u(r,i))}else{var o=e.getRawData(),a={},s=mw(e).scope;n.each((function(t){var e=n.getRawIndex(t);a[e]=t}));var l=o.count();o.each((function(t){var i=a[t],r=o.getName(t)||t+"",h=Nc(e.ecModel,r,s,l),c=n.getItemVisual(i,"decal");n.setItemVisual(i,"decal",u(c,h))}))}}function u(t,e){var n=t?D(D({},e),t):e;return n.dirty=!0,n}}))}}(),function(){var i=t.getLocaleModel().get("aria"),o=n.getModel("label");if(o.option=I(o.option,i),!o.get("enabled"))return;var a=e.getZr().dom;if(o.get("description"))return void a.setAttribute("aria-label",o.get("description"));var s,l=t.getSeriesCount(),u=o.get(["data","maxCount"])||10,h=o.get(["series","maxCount"])||10,c=Math.min(l,h);if(l<1)return;var p=function(){var e=t.get("title");e&&e.length&&(e=e[0]);return e&&e.text}();s=p?r(o.get(["general","withTitle"]),{title:p}):o.get(["general","withoutTitle"]);var f=[];s+=r(l>1?o.get(["series","multiple","prefix"]):o.get(["series","single","prefix"]),{seriesCount:l}),t.eachSeries((function(e,n){if(n<c){var i=void 0,a=e.get("name")?"withName":"withoutName";i=r(i=l>1?o.get(["series","multiple",a]):o.get(["series","single",a]),{seriesId:e.seriesIndex,seriesName:e.get("name"),seriesType:(_=e.subType,t.getLocaleModel().get(["series","typeNames"])[_]||"自定义图")});var s=e.getData();if(s.count()>u)i+=r(o.get(["data","partialData"]),{displayCnt:u});else i+=o.get(["data","allData"]);for(var h=o.get(["data","separator","middle"]),p=o.get(["data","separator","end"]),d=[],g=0;g<s.count();g++)if(g<u){var y=s.getName(g),v=s.getValues(g),m=o.get(["data",y?"withName":"withoutName"]);d.push(r(m,{name:y,value:v.join(h)}))}i+=d.join(h)+p,f.push(i)}var _}));var d=o.getModel(["series","multiple","separator"]),g=d.get("middle"),y=d.get("end");s+=f.join(g)+y,a.setAttribute("aria-label",s)}()}function r(t,e){if(!G(t))return t;var n=t;return R(e,(function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)})),n}}function ww(t){if(t&&t.aria){var e=t.aria;null!=e.show&&(e.enabled=e.show),e.label=e.label||{},R(["description","general","series","data"],(function(t){null!=e[t]&&(e.label[t]=e[t])}))}}var bw=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return n(e,t),e.prototype.init=function(e,n,i){t.prototype.init.call(this,e,n,i),this._sourceManager=new pf(this),ff(this)},e.prototype.mergeOption=function(e,n){t.prototype.mergeOption.call(this,e,n),ff(this)},e.prototype.optionUpdated=function(){this._sourceManager.dirty()},e.prototype.getSourceManager=function(){return this._sourceManager},e.type="dataset",e.defaultOption={seriesLayoutBy:vc},e}(ac),Sw=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return n(e,t),e.type="dataset",e}(If);Dy([function(t){t.registerPainter("canvas",o_)}]),Dy([function(t){t.registerChartView(B_),t.registerSeriesModel(a_),t.registerLayout(function(t,e){return{seriesType:t,plan:kf(),reset:function(t){var n=t.getData(),i=t.coordinateSystem,r=t.pipelineContext,o=e||r.large;if(i){var a=N(i.dimensions,(function(t){return n.mapDimension(t)})).slice(0,2),s=a.length,l=n.getCalculationInfo("stackResultDimension");av(n,a[0])&&(a[0]=l),av(n,a[1])&&(a[1]=l);var u=n.getStore(),h=n.getDimensionIndex(a[0]),c=n.getDimensionIndex(a[1]);return s&&{progress:function(t,e){for(var n=t.end-t.start,r=o&&Cv(n*s),a=[],l=[],p=t.start,f=0;p<t.end;p++){var d=void 0;if(1===s){var g=u.get(h,p);d=i.dataToPoint(g,null,l)}else a[0]=u.get(h,p),a[1]=u.get(c,p),d=i.dataToPoint(a,null,l);o?(r[f++]=d[0],r[f++]=d[1]):e.setItemLayout(p,d.slice())}o&&e.setLayout("points",r)}}}}}}("line",!0)),t.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),n=t.getModel("lineStyle").getLineStyle();n&&!n.stroke&&(n.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",n)}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,H_("line"))},function(t){t.registerChartView($_),t.registerSeriesModel(G_),t.registerLayout(t.PRIORITY.VISUAL.LAYOUT,V(Lv,"bar")),t.registerLayout(t.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,function(t){return{seriesType:t,plan:kf(),reset:function(t){if(Pv(t)){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),r=n.getOtherAxis(i),o=e.getDimensionIndex(e.mapDimension(r.dim)),a=e.getDimensionIndex(e.mapDimension(i.dim)),s=t.get("showBackground",!0),l=e.mapDimension(r.dim),u=e.getCalculationInfo("stackResultDimension"),h=av(e,l)&&!!e.getCalculationInfo("stackedOnSeries"),c=r.isHorizontal(),p=function(t,e){return e.toGlobalCoord(e.dataToCoord("log"===e.type?1:0))}(0,r),f=Ov(t),d=t.get("barMinHeight")||0,g=u&&e.getDimensionIndex(u),y=e.getLayout("size"),v=e.getLayout("offset");return{progress:function(t,e){for(var i,r=t.count,l=f&&Cv(3*r),u=f&&s&&Cv(3*r),m=f&&Cv(r),_=n.master.getRect(),x=c?_.width:_.height,w=e.getStore(),b=0;null!=(i=t.next());){var S=w.get(h?g:o,i),M=w.get(a,i),T=p,C=void 0;h&&(C=+S-w.get(o,i));var D=void 0,I=void 0,k=void 0,A=void 0;if(c){var L=n.dataToPoint([S,M]);h&&(T=n.dataToPoint([C,M])[0]),D=T,I=L[1]+v,k=L[0]-T,A=y,Math.abs(k)<d&&(k=(k<0?-1:1)*d)}else L=n.dataToPoint([M,S]),h&&(T=n.dataToPoint([M,C])[1]),D=L[0]+v,I=T,k=y,A=L[1]-T,Math.abs(A)<d&&(A=(A<=0?-1:1)*d);f?(l[b]=D,l[b+1]=I,l[b+2]=c?k:A,u&&(u[b]=c?_.x:D,u[b+1]=c?I:_.y,u[b+2]=x),m[i]=i):e.setItemLayout(i,{x:D,y:I,width:k,height:A}),b+=3}f&&e.setLayout({largePoints:l,largeDataIndices:m,largeBackgroundPoints:u,valueAxisHorizontal:c})}}}}}}("bar")),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,H_("bar")),t.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},(function(t,e){var n=t.componentType||"series";e.eachComponent({mainType:n,query:t},(function(e){t.sortInfo&&e.axis.setCategorySortInfo(t.sortInfo)}))}))},function(t){t.registerChartView(Tx),t.registerSeriesModel(Ix),function(t,e){function n(e,n){var i=[];return e.eachComponent({mainType:"series",subType:t,query:n},(function(t){i.push(t.seriesIndex)})),i}R([[t+"ToggleSelect","toggleSelect"],[t+"Select","select"],[t+"UnSelect","unselect"]],(function(t){e(t[0],(function(e,i,r){e=D({},e),r.dispatchAction(D(e,{type:t[1],seriesIndex:n(i,e)}))}))}))}("pie",t.registerAction),t.registerLayout(V(vx,"pie")),t.registerProcessor(function(t){return{seriesType:t,reset:function(t,e){var n=e.findComponents({mainType:"legend"});if(n&&n.length){var i=t.getData();i.filterSelf((function(t){for(var e=i.getName(t),r=0;r<n.length;r++)if(!n[r].isSelected(e))return!1;return!0}))}}}}("pie")),t.registerProcessor(function(t){return{seriesType:t,reset:function(t,e){var n=t.getData();n.filterSelf((function(t){var e=n.mapDimension("value"),i=n.get(e,t);return!(X(i)&&!isNaN(i)&&i<0)}))}}}("pie"))}]),Dy([function(t){t.registerComponentView(gw),t.registerComponentModel(kx),t.registerCoordinateSystem("cartesian2d",qx),Ex(t,"x",Ax,yw),Ex(t,"y",Ax,yw),t.registerComponentView(fw),t.registerComponentView(dw),t.registerPreprocessor((function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}))},function(t){t.registerPreprocessor(ww),t.registerVisual(t.PRIORITY.VISUAL.ARIA,xw)},function(t){t.registerComponentModel(bw),t.registerComponentView(Sw)}]),t.Axis=Fm,t.ChartView=Pf,t.ComponentModel=ac,t.ComponentView=If,t.List=Jy,t.Model=oh,t.PRIORITY=yg,t.SeriesModel=xf,t.color=Xn,t.connect=function(t){if(H(t)){var e=t;t=null,R(e,(function(e){null!=e.group&&(t=e.group)})),t=t||"g_"+oy++,R(e,(function(e){e.group=t}))}return iy[t]=!0,t},t.dataTool={},t.dependencies={zrender:"5.4.4"},t.disConnect=ly,t.disconnect=sy,t.dispose=function(t){G(t)?t=ny[t]:t instanceof Xg||(t=uy(t)),t instanceof Xg&&!t.isDisposed()&&t.dispose()},t.env=r,t.extendChartView=function(t){var e=Pf.extend(t);return Pf.registerClass(e),e},t.extendComponentModel=function(t){var e=ac.extend(t);return ac.registerClass(e),e},t.extendComponentView=function(t){var e=If.extend(t);return If.registerClass(e),e},t.extendSeriesModel=function(t){var e=xf.extend(t);return xf.registerClass(e),e},t.format=Dm,t.getCoordinateSystemDimensions=function(t){var e=Xc.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()},t.getInstanceByDom=uy,t.getInstanceById=function(t){return ny[t]},t.getMap=function(t){var e=fg("getMap");return e&&e(t)},t.graphic=Cm,t.helper=cm,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){0;var r=uy(t);if(r)return r;0}var o=new Xg(t,e,n);return o.id="ec_"+ry++,ny[o.id]=o,i&&to(t,ay,o.id),Vg(o),cg.trigger("afterinit",o),o},t.innerDrawElementOnCanvas=ng,t.matrix=ye,t.number=Mm,t.parseGeoJSON=Sm,t.parseGeoJson=Sm,t.registerAction=yy,t.registerCoordinateSystem=vy,t.registerLayout=my,t.registerLoading=by,t.registerLocale=dh,t.registerMap=Sy,t.registerPostInit=fy,t.registerPostUpdate=dy,t.registerPreprocessor=cy,t.registerProcessor=py,t.registerTheme=hy,t.registerTransform=My,t.registerUpdateLifecycle=gy,t.registerVisual=_y,t.setCanvasCreator=function(t){h({createCanvas:t})},t.setPlatformAPI=h,t.throttle=Bf,t.time=Tm,t.use=Dy,t.util=Im,t.vector=Ft,t.version="5.4.3",t.zrUtil=xt,t.zrender=cr,Object.defineProperty(t,"__esModule",{value:!0})}));
