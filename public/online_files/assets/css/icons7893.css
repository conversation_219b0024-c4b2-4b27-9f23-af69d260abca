@charset "UTF-8";
/*
 Template Name: <PERSON>or - Responsive Bootstrap 4 Admin Dashboard
 Author: Themesdesign
 Website: www.themesdesign.in
 File: Icons
 */
/*!
 * Font Awesome Free 5.0.13 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
.fa,
.fas,
.far,
.fal,
.fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

.fa-lg {
  font-size: 1.33333em;
  line-height: 0.75em;
  vertical-align: -.0667em;
}

.fa-xs {
  font-size: .75em;
}

.fa-sm {
  font-size: .875em;
}

.fa-1x {
  font-size: 1em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-6x {
  font-size: 6em;
}

.fa-7x {
  font-size: 7em;
}

.fa-8x {
  font-size: 8em;
}

.fa-9x {
  font-size: 9em;
}

.fa-10x {
  font-size: 10em;
}

.fa-fw {
  text-align: center;
  width: 1.25em;
}

.fa-ul {
  list-style-type: none;
  margin-left: 2.5em;
  padding-left: 0;
}

.fa-ul > li {
  position: relative;
}

.fa-li {
  left: -2em;
  position: absolute;
  text-align: center;
  width: 2em;
  line-height: inherit;
}

.fa-border {
  border: solid 0.08em #eee;
  border-radius: .1em;
  padding: .2em .25em .15em;
}

.fa-pull-left {
  float: left;
}

.fa-pull-right {
  float: right;
}

.fa.fa-pull-left,
.fas.fa-pull-left,
.far.fa-pull-left,
.fal.fa-pull-left,
.fab.fa-pull-left {
  margin-right: .3em;
}

.fa.fa-pull-right,
.fas.fa-pull-right,
.far.fa-pull-right,
.fal.fa-pull-right,
.fab.fa-pull-right {
  margin-left: .3em;
}

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
          animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
          animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

.fa-rotate-90 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg);
}

.fa-rotate-180 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg);
}

.fa-rotate-270 {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg);
}

.fa-flip-horizontal {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";
  -webkit-transform: scale(-1, 1);
          transform: scale(-1, 1);
}

.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(1, -1);
          transform: scale(1, -1);
}

.fa-flip-horizontal.fa-flip-vertical {
  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";
  -webkit-transform: scale(-1, -1);
          transform: scale(-1, -1);
}

:root .fa-rotate-90,
:root .fa-rotate-180,
:root .fa-rotate-270,
:root .fa-flip-horizontal,
:root .fa-flip-vertical {
  -webkit-filter: none;
          filter: none;
}

.fa-stack {
  display: inline-block;
  height: 2em;
  line-height: 2em;
  position: relative;
  vertical-align: middle;
  width: 2em;
}

.fa-stack-1x,
.fa-stack-2x {
  left: 0;
  position: absolute;
  text-align: center;
  width: 100%;
}

.fa-stack-1x {
  line-height: inherit;
}

.fa-stack-2x {
  font-size: 2em;
}

.fa-inverse {
  color: #fff;
}

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
readers do not read off random characters that represent icons */
.fa-500px:before {
  content: "\f26e";
}

.fa-accessible-icon:before {
  content: "\f368";
}

.fa-accusoft:before {
  content: "\f369";
}

.fa-address-book:before {
  content: "\f2b9";
}

.fa-address-card:before {
  content: "\f2bb";
}

.fa-adjust:before {
  content: "\f042";
}

.fa-adn:before {
  content: "\f170";
}

.fa-adversal:before {
  content: "\f36a";
}

.fa-affiliatetheme:before {
  content: "\f36b";
}

.fa-algolia:before {
  content: "\f36c";
}

.fa-align-center:before {
  content: "\f037";
}

.fa-align-justify:before {
  content: "\f039";
}

.fa-align-left:before {
  content: "\f036";
}

.fa-align-right:before {
  content: "\f038";
}

.fa-allergies:before {
  content: "\f461";
}

.fa-amazon:before {
  content: "\f270";
}

.fa-amazon-pay:before {
  content: "\f42c";
}

.fa-ambulance:before {
  content: "\f0f9";
}

.fa-american-sign-language-interpreting:before {
  content: "\f2a3";
}

.fa-amilia:before {
  content: "\f36d";
}

.fa-anchor:before {
  content: "\f13d";
}

.fa-android:before {
  content: "\f17b";
}

.fa-angellist:before {
  content: "\f209";
}

.fa-angle-double-down:before {
  content: "\f103";
}

.fa-angle-double-left:before {
  content: "\f100";
}

.fa-angle-double-right:before {
  content: "\f101";
}

.fa-angle-double-up:before {
  content: "\f102";
}

.fa-angle-down:before {
  content: "\f107";
}

.fa-angle-left:before {
  content: "\f104";
}

.fa-angle-right:before {
  content: "\f105";
}

.fa-angle-up:before {
  content: "\f106";
}

.fa-angrycreative:before {
  content: "\f36e";
}

.fa-angular:before {
  content: "\f420";
}

.fa-app-store:before {
  content: "\f36f";
}

.fa-app-store-ios:before {
  content: "\f370";
}

.fa-apper:before {
  content: "\f371";
}

.fa-apple:before {
  content: "\f179";
}

.fa-apple-pay:before {
  content: "\f415";
}

.fa-archive:before {
  content: "\f187";
}

.fa-arrow-alt-circle-down:before {
  content: "\f358";
}

.fa-arrow-alt-circle-left:before {
  content: "\f359";
}

.fa-arrow-alt-circle-right:before {
  content: "\f35a";
}

.fa-arrow-alt-circle-up:before {
  content: "\f35b";
}

.fa-arrow-circle-down:before {
  content: "\f0ab";
}

.fa-arrow-circle-left:before {
  content: "\f0a8";
}

.fa-arrow-circle-right:before {
  content: "\f0a9";
}

.fa-arrow-circle-up:before {
  content: "\f0aa";
}

.fa-arrow-down:before {
  content: "\f063";
}

.fa-arrow-left:before {
  content: "\f060";
}

.fa-arrow-right:before {
  content: "\f061";
}

.fa-arrow-up:before {
  content: "\f062";
}

.fa-arrows-alt:before {
  content: "\f0b2";
}

.fa-arrows-alt-h:before {
  content: "\f337";
}

.fa-arrows-alt-v:before {
  content: "\f338";
}

.fa-assistive-listening-systems:before {
  content: "\f2a2";
}

.fa-asterisk:before {
  content: "\f069";
}

.fa-asymmetrik:before {
  content: "\f372";
}

.fa-at:before {
  content: "\f1fa";
}

.fa-audible:before {
  content: "\f373";
}

.fa-audio-description:before {
  content: "\f29e";
}

.fa-autoprefixer:before {
  content: "\f41c";
}

.fa-avianex:before {
  content: "\f374";
}

.fa-aviato:before {
  content: "\f421";
}

.fa-aws:before {
  content: "\f375";
}

.fa-backward:before {
  content: "\f04a";
}

.fa-balance-scale:before {
  content: "\f24e";
}

.fa-ban:before {
  content: "\f05e";
}

.fa-band-aid:before {
  content: "\f462";
}

.fa-bandcamp:before {
  content: "\f2d5";
}

.fa-barcode:before {
  content: "\f02a";
}

.fa-bars:before {
  content: "\f0c9";
}

.fa-baseball-ball:before {
  content: "\f433";
}

.fa-basketball-ball:before {
  content: "\f434";
}

.fa-bath:before {
  content: "\f2cd";
}

.fa-battery-empty:before {
  content: "\f244";
}

.fa-battery-full:before {
  content: "\f240";
}

.fa-battery-half:before {
  content: "\f242";
}

.fa-battery-quarter:before {
  content: "\f243";
}

.fa-battery-three-quarters:before {
  content: "\f241";
}

.fa-bed:before {
  content: "\f236";
}

.fa-beer:before {
  content: "\f0fc";
}

.fa-behance:before {
  content: "\f1b4";
}

.fa-behance-square:before {
  content: "\f1b5";
}

.fa-bell:before {
  content: "\f0f3";
}

.fa-bell-slash:before {
  content: "\f1f6";
}

.fa-bicycle:before {
  content: "\f206";
}

.fa-bimobject:before {
  content: "\f378";
}

.fa-binoculars:before {
  content: "\f1e5";
}

.fa-birthday-cake:before {
  content: "\f1fd";
}

.fa-bitbucket:before {
  content: "\f171";
}

.fa-bitcoin:before {
  content: "\f379";
}

.fa-bity:before {
  content: "\f37a";
}

.fa-black-tie:before {
  content: "\f27e";
}

.fa-blackberry:before {
  content: "\f37b";
}

.fa-blender:before {
  content: "\f517";
}

.fa-blind:before {
  content: "\f29d";
}

.fa-blogger:before {
  content: "\f37c";
}

.fa-blogger-b:before {
  content: "\f37d";
}

.fa-bluetooth:before {
  content: "\f293";
}

.fa-bluetooth-b:before {
  content: "\f294";
}

.fa-bold:before {
  content: "\f032";
}

.fa-bolt:before {
  content: "\f0e7";
}

.fa-bomb:before {
  content: "\f1e2";
}

.fa-book:before {
  content: "\f02d";
}

.fa-book-open:before {
  content: "\f518";
}

.fa-bookmark:before {
  content: "\f02e";
}

.fa-bowling-ball:before {
  content: "\f436";
}

.fa-box:before {
  content: "\f466";
}

.fa-box-open:before {
  content: "\f49e";
}

.fa-boxes:before {
  content: "\f468";
}

.fa-braille:before {
  content: "\f2a1";
}

.fa-briefcase:before {
  content: "\f0b1";
}

.fa-briefcase-medical:before {
  content: "\f469";
}

.fa-broadcast-tower:before {
  content: "\f519";
}

.fa-broom:before {
  content: "\f51a";
}

.fa-btc:before {
  content: "\f15a";
}

.fa-bug:before {
  content: "\f188";
}

.fa-building:before {
  content: "\f1ad";
}

.fa-bullhorn:before {
  content: "\f0a1";
}

.fa-bullseye:before {
  content: "\f140";
}

.fa-burn:before {
  content: "\f46a";
}

.fa-buromobelexperte:before {
  content: "\f37f";
}

.fa-bus:before {
  content: "\f207";
}

.fa-buysellads:before {
  content: "\f20d";
}

.fa-calculator:before {
  content: "\f1ec";
}

.fa-calendar:before {
  content: "\f133";
}

.fa-calendar-alt:before {
  content: "\f073";
}

.fa-calendar-check:before {
  content: "\f274";
}

.fa-calendar-minus:before {
  content: "\f272";
}

.fa-calendar-plus:before {
  content: "\f271";
}

.fa-calendar-times:before {
  content: "\f273";
}

.fa-camera:before {
  content: "\f030";
}

.fa-camera-retro:before {
  content: "\f083";
}

.fa-capsules:before {
  content: "\f46b";
}

.fa-car:before {
  content: "\f1b9";
}

.fa-caret-down:before {
  content: "\f0d7";
}

.fa-caret-left:before {
  content: "\f0d9";
}

.fa-caret-right:before {
  content: "\f0da";
}

.fa-caret-square-down:before {
  content: "\f150";
}

.fa-caret-square-left:before {
  content: "\f191";
}

.fa-caret-square-right:before {
  content: "\f152";
}

.fa-caret-square-up:before {
  content: "\f151";
}

.fa-caret-up:before {
  content: "\f0d8";
}

.fa-cart-arrow-down:before {
  content: "\f218";
}

.fa-cart-plus:before {
  content: "\f217";
}

.fa-cc-amazon-pay:before {
  content: "\f42d";
}

.fa-cc-amex:before {
  content: "\f1f3";
}

.fa-cc-apple-pay:before {
  content: "\f416";
}

.fa-cc-diners-club:before {
  content: "\f24c";
}

.fa-cc-discover:before {
  content: "\f1f2";
}

.fa-cc-jcb:before {
  content: "\f24b";
}

.fa-cc-mastercard:before {
  content: "\f1f1";
}

.fa-cc-paypal:before {
  content: "\f1f4";
}

.fa-cc-stripe:before {
  content: "\f1f5";
}

.fa-cc-visa:before {
  content: "\f1f0";
}

.fa-centercode:before {
  content: "\f380";
}

.fa-certificate:before {
  content: "\f0a3";
}

.fa-chalkboard:before {
  content: "\f51b";
}

.fa-chalkboard-teacher:before {
  content: "\f51c";
}

.fa-chart-area:before {
  content: "\f1fe";
}

.fa-chart-bar:before {
  content: "\f080";
}

.fa-chart-line:before {
  content: "\f201";
}

.fa-chart-pie:before {
  content: "\f200";
}

.fa-check:before {
  content: "\f00c";
}

.fa-check-circle:before {
  content: "\f058";
}

.fa-check-square:before {
  content: "\f14a";
}

.fa-chess:before {
  content: "\f439";
}

.fa-chess-bishop:before {
  content: "\f43a";
}

.fa-chess-board:before {
  content: "\f43c";
}

.fa-chess-king:before {
  content: "\f43f";
}

.fa-chess-knight:before {
  content: "\f441";
}

.fa-chess-pawn:before {
  content: "\f443";
}

.fa-chess-queen:before {
  content: "\f445";
}

.fa-chess-rook:before {
  content: "\f447";
}

.fa-chevron-circle-down:before {
  content: "\f13a";
}

.fa-chevron-circle-left:before {
  content: "\f137";
}

.fa-chevron-circle-right:before {
  content: "\f138";
}

.fa-chevron-circle-up:before {
  content: "\f139";
}

.fa-chevron-down:before {
  content: "\f078";
}

.fa-chevron-left:before {
  content: "\f053";
}

.fa-chevron-right:before {
  content: "\f054";
}

.fa-chevron-up:before {
  content: "\f077";
}

.fa-child:before {
  content: "\f1ae";
}

.fa-chrome:before {
  content: "\f268";
}

.fa-church:before {
  content: "\f51d";
}

.fa-circle:before {
  content: "\f111";
}

.fa-circle-notch:before {
  content: "\f1ce";
}

.fa-clipboard:before {
  content: "\f328";
}

.fa-clipboard-check:before {
  content: "\f46c";
}

.fa-clipboard-list:before {
  content: "\f46d";
}

.fa-clock:before {
  content: "\f017";
}

.fa-clone:before {
  content: "\f24d";
}

.fa-closed-captioning:before {
  content: "\f20a";
}

.fa-cloud:before {
  content: "\f0c2";
}

.fa-cloud-download-alt:before {
  content: "\f381";
}

.fa-cloud-upload-alt:before {
  content: "\f382";
}

.fa-cloudscale:before {
  content: "\f383";
}

.fa-cloudsmith:before {
  content: "\f384";
}

.fa-cloudversify:before {
  content: "\f385";
}

.fa-code:before {
  content: "\f121";
}

.fa-code-branch:before {
  content: "\f126";
}

.fa-codepen:before {
  content: "\f1cb";
}

.fa-codiepie:before {
  content: "\f284";
}

.fa-coffee:before {
  content: "\f0f4";
}

.fa-cog:before {
  content: "\f013";
}

.fa-cogs:before {
  content: "\f085";
}

.fa-coins:before {
  content: "\f51e";
}

.fa-columns:before {
  content: "\f0db";
}

.fa-comment:before {
  content: "\f075";
}

.fa-comment-alt:before {
  content: "\f27a";
}

.fa-comment-dots:before {
  content: "\f4ad";
}

.fa-comment-slash:before {
  content: "\f4b3";
}

.fa-comments:before {
  content: "\f086";
}

.fa-compact-disc:before {
  content: "\f51f";
}

.fa-compass:before {
  content: "\f14e";
}

.fa-compress:before {
  content: "\f066";
}

.fa-connectdevelop:before {
  content: "\f20e";
}

.fa-contao:before {
  content: "\f26d";
}

.fa-copy:before {
  content: "\f0c5";
}

.fa-copyright:before {
  content: "\f1f9";
}

.fa-couch:before {
  content: "\f4b8";
}

.fa-cpanel:before {
  content: "\f388";
}

.fa-creative-commons:before {
  content: "\f25e";
}

.fa-creative-commons-by:before {
  content: "\f4e7";
}

.fa-creative-commons-nc:before {
  content: "\f4e8";
}

.fa-creative-commons-nc-eu:before {
  content: "\f4e9";
}

.fa-creative-commons-nc-jp:before {
  content: "\f4ea";
}

.fa-creative-commons-nd:before {
  content: "\f4eb";
}

.fa-creative-commons-pd:before {
  content: "\f4ec";
}

.fa-creative-commons-pd-alt:before {
  content: "\f4ed";
}

.fa-creative-commons-remix:before {
  content: "\f4ee";
}

.fa-creative-commons-sa:before {
  content: "\f4ef";
}

.fa-creative-commons-sampling:before {
  content: "\f4f0";
}

.fa-creative-commons-sampling-plus:before {
  content: "\f4f1";
}

.fa-creative-commons-share:before {
  content: "\f4f2";
}

.fa-credit-card:before {
  content: "\f09d";
}

.fa-crop:before {
  content: "\f125";
}

.fa-crosshairs:before {
  content: "\f05b";
}

.fa-crow:before {
  content: "\f520";
}

.fa-crown:before {
  content: "\f521";
}

.fa-css3:before {
  content: "\f13c";
}

.fa-css3-alt:before {
  content: "\f38b";
}

.fa-cube:before {
  content: "\f1b2";
}

.fa-cubes:before {
  content: "\f1b3";
}

.fa-cut:before {
  content: "\f0c4";
}

.fa-cuttlefish:before {
  content: "\f38c";
}

.fa-d-and-d:before {
  content: "\f38d";
}

.fa-dashcube:before {
  content: "\f210";
}

.fa-database:before {
  content: "\f1c0";
}

.fa-deaf:before {
  content: "\f2a4";
}

.fa-delicious:before {
  content: "\f1a5";
}

.fa-deploydog:before {
  content: "\f38e";
}

.fa-deskpro:before {
  content: "\f38f";
}

.fa-desktop:before {
  content: "\f108";
}

.fa-deviantart:before {
  content: "\f1bd";
}

.fa-diagnoses:before {
  content: "\f470";
}

.fa-dice:before {
  content: "\f522";
}

.fa-dice-five:before {
  content: "\f523";
}

.fa-dice-four:before {
  content: "\f524";
}

.fa-dice-one:before {
  content: "\f525";
}

.fa-dice-six:before {
  content: "\f526";
}

.fa-dice-three:before {
  content: "\f527";
}

.fa-dice-two:before {
  content: "\f528";
}

.fa-digg:before {
  content: "\f1a6";
}

.fa-digital-ocean:before {
  content: "\f391";
}

.fa-discord:before {
  content: "\f392";
}

.fa-discourse:before {
  content: "\f393";
}

.fa-divide:before {
  content: "\f529";
}

.fa-dna:before {
  content: "\f471";
}

.fa-dochub:before {
  content: "\f394";
}

.fa-docker:before {
  content: "\f395";
}

.fa-dollar-sign:before {
  content: "\f155";
}

.fa-dolly:before {
  content: "\f472";
}

.fa-dolly-flatbed:before {
  content: "\f474";
}

.fa-donate:before {
  content: "\f4b9";
}

.fa-door-closed:before {
  content: "\f52a";
}

.fa-door-open:before {
  content: "\f52b";
}

.fa-dot-circle:before {
  content: "\f192";
}

.fa-dove:before {
  content: "\f4ba";
}

.fa-download:before {
  content: "\f019";
}

.fa-draft2digital:before {
  content: "\f396";
}

.fa-dribbble:before {
  content: "\f17d";
}

.fa-dribbble-square:before {
  content: "\f397";
}

.fa-dropbox:before {
  content: "\f16b";
}

.fa-drupal:before {
  content: "\f1a9";
}

.fa-dumbbell:before {
  content: "\f44b";
}

.fa-dyalog:before {
  content: "\f399";
}

.fa-earlybirds:before {
  content: "\f39a";
}

.fa-ebay:before {
  content: "\f4f4";
}

.fa-edge:before {
  content: "\f282";
}

.fa-edit:before {
  content: "\f044";
}

.fa-eject:before {
  content: "\f052";
}

.fa-elementor:before {
  content: "\f430";
}

.fa-ellipsis-h:before {
  content: "\f141";
}

.fa-ellipsis-v:before {
  content: "\f142";
}

.fa-ember:before {
  content: "\f423";
}

.fa-empire:before {
  content: "\f1d1";
}

.fa-envelope:before {
  content: "\f0e0";
}

.fa-envelope-open:before {
  content: "\f2b6";
}

.fa-envelope-square:before {
  content: "\f199";
}

.fa-envira:before {
  content: "\f299";
}

.fa-equals:before {
  content: "\f52c";
}

.fa-eraser:before {
  content: "\f12d";
}

.fa-erlang:before {
  content: "\f39d";
}

.fa-ethereum:before {
  content: "\f42e";
}

.fa-etsy:before {
  content: "\f2d7";
}

.fa-euro-sign:before {
  content: "\f153";
}

.fa-exchange-alt:before {
  content: "\f362";
}

.fa-exclamation:before {
  content: "\f12a";
}

.fa-exclamation-circle:before {
  content: "\f06a";
}

.fa-exclamation-triangle:before {
  content: "\f071";
}

.fa-expand:before {
  content: "\f065";
}

.fa-expand-arrows-alt:before {
  content: "\f31e";
}

.fa-expeditedssl:before {
  content: "\f23e";
}

.fa-external-link-alt:before {
  content: "\f35d";
}

.fa-external-link-square-alt:before {
  content: "\f360";
}

.fa-eye:before {
  content: "\f06e";
}

.fa-eye-dropper:before {
  content: "\f1fb";
}

.fa-eye-slash:before {
  content: "\f070";
}

.fa-facebook:before {
  content: "\f09a";
}

.fa-facebook-f:before {
  content: "\f39e";
}

.fa-facebook-messenger:before {
  content: "\f39f";
}

.fa-facebook-square:before {
  content: "\f082";
}

.fa-fast-backward:before {
  content: "\f049";
}

.fa-fast-forward:before {
  content: "\f050";
}

.fa-fax:before {
  content: "\f1ac";
}

.fa-feather:before {
  content: "\f52d";
}

.fa-female:before {
  content: "\f182";
}

.fa-fighter-jet:before {
  content: "\f0fb";
}

.fa-file:before {
  content: "\f15b";
}

.fa-file-alt:before {
  content: "\f15c";
}

.fa-file-archive:before {
  content: "\f1c6";
}

.fa-file-audio:before {
  content: "\f1c7";
}

.fa-file-code:before {
  content: "\f1c9";
}

.fa-file-excel:before {
  content: "\f1c3";
}

.fa-file-image:before {
  content: "\f1c5";
}

.fa-file-medical:before {
  content: "\f477";
}

.fa-file-medical-alt:before {
  content: "\f478";
}

.fa-file-pdf:before {
  content: "\f1c1";
}

.fa-file-powerpoint:before {
  content: "\f1c4";
}

.fa-file-video:before {
  content: "\f1c8";
}

.fa-file-word:before {
  content: "\f1c2";
}

.fa-film:before {
  content: "\f008";
}

.fa-filter:before {
  content: "\f0b0";
}

.fa-fire:before {
  content: "\f06d";
}

.fa-fire-extinguisher:before {
  content: "\f134";
}

.fa-firefox:before {
  content: "\f269";
}

.fa-first-aid:before {
  content: "\f479";
}

.fa-first-order:before {
  content: "\f2b0";
}

.fa-first-order-alt:before {
  content: "\f50a";
}

.fa-firstdraft:before {
  content: "\f3a1";
}

.fa-flag:before {
  content: "\f024";
}

.fa-flag-checkered:before {
  content: "\f11e";
}

.fa-flask:before {
  content: "\f0c3";
}

.fa-flickr:before {
  content: "\f16e";
}

.fa-flipboard:before {
  content: "\f44d";
}

.fa-fly:before {
  content: "\f417";
}

.fa-folder:before {
  content: "\f07b";
}

.fa-folder-open:before {
  content: "\f07c";
}

.fa-font:before {
  content: "\f031";
}

.fa-font-awesome:before {
  content: "\f2b4";
}

.fa-font-awesome-alt:before {
  content: "\f35c";
}

.fa-font-awesome-flag:before {
  content: "\f425";
}

.fa-font-awesome-logo-full:before {
  content: "\f4e6";
}

.fa-fonticons:before {
  content: "\f280";
}

.fa-fonticons-fi:before {
  content: "\f3a2";
}

.fa-football-ball:before {
  content: "\f44e";
}

.fa-fort-awesome:before {
  content: "\f286";
}

.fa-fort-awesome-alt:before {
  content: "\f3a3";
}

.fa-forumbee:before {
  content: "\f211";
}

.fa-forward:before {
  content: "\f04e";
}

.fa-foursquare:before {
  content: "\f180";
}

.fa-free-code-camp:before {
  content: "\f2c5";
}

.fa-freebsd:before {
  content: "\f3a4";
}

.fa-frog:before {
  content: "\f52e";
}

.fa-frown:before {
  content: "\f119";
}

.fa-fulcrum:before {
  content: "\f50b";
}

.fa-futbol:before {
  content: "\f1e3";
}

.fa-galactic-republic:before {
  content: "\f50c";
}

.fa-galactic-senate:before {
  content: "\f50d";
}

.fa-gamepad:before {
  content: "\f11b";
}

.fa-gas-pump:before {
  content: "\f52f";
}

.fa-gavel:before {
  content: "\f0e3";
}

.fa-gem:before {
  content: "\f3a5";
}

.fa-genderless:before {
  content: "\f22d";
}

.fa-get-pocket:before {
  content: "\f265";
}

.fa-gg:before {
  content: "\f260";
}

.fa-gg-circle:before {
  content: "\f261";
}

.fa-gift:before {
  content: "\f06b";
}

.fa-git:before {
  content: "\f1d3";
}

.fa-git-square:before {
  content: "\f1d2";
}

.fa-github:before {
  content: "\f09b";
}

.fa-github-alt:before {
  content: "\f113";
}

.fa-github-square:before {
  content: "\f092";
}

.fa-gitkraken:before {
  content: "\f3a6";
}

.fa-gitlab:before {
  content: "\f296";
}

.fa-gitter:before {
  content: "\f426";
}

.fa-glass-martini:before {
  content: "\f000";
}

.fa-glasses:before {
  content: "\f530";
}

.fa-glide:before {
  content: "\f2a5";
}

.fa-glide-g:before {
  content: "\f2a6";
}

.fa-globe:before {
  content: "\f0ac";
}

.fa-gofore:before {
  content: "\f3a7";
}

.fa-golf-ball:before {
  content: "\f450";
}

.fa-goodreads:before {
  content: "\f3a8";
}

.fa-goodreads-g:before {
  content: "\f3a9";
}

.fa-google:before {
  content: "\f1a0";
}

.fa-google-drive:before {
  content: "\f3aa";
}

.fa-google-play:before {
  content: "\f3ab";
}

.fa-google-plus:before {
  content: "\f2b3";
}

.fa-google-plus-g:before {
  content: "\f0d5";
}

.fa-google-plus-square:before {
  content: "\f0d4";
}

.fa-google-wallet:before {
  content: "\f1ee";
}

.fa-graduation-cap:before {
  content: "\f19d";
}

.fa-gratipay:before {
  content: "\f184";
}

.fa-grav:before {
  content: "\f2d6";
}

.fa-greater-than:before {
  content: "\f531";
}

.fa-greater-than-equal:before {
  content: "\f532";
}

.fa-gripfire:before {
  content: "\f3ac";
}

.fa-grunt:before {
  content: "\f3ad";
}

.fa-gulp:before {
  content: "\f3ae";
}

.fa-h-square:before {
  content: "\f0fd";
}

.fa-hacker-news:before {
  content: "\f1d4";
}

.fa-hacker-news-square:before {
  content: "\f3af";
}

.fa-hand-holding:before {
  content: "\f4bd";
}

.fa-hand-holding-heart:before {
  content: "\f4be";
}

.fa-hand-holding-usd:before {
  content: "\f4c0";
}

.fa-hand-lizard:before {
  content: "\f258";
}

.fa-hand-paper:before {
  content: "\f256";
}

.fa-hand-peace:before {
  content: "\f25b";
}

.fa-hand-point-down:before {
  content: "\f0a7";
}

.fa-hand-point-left:before {
  content: "\f0a5";
}

.fa-hand-point-right:before {
  content: "\f0a4";
}

.fa-hand-point-up:before {
  content: "\f0a6";
}

.fa-hand-pointer:before {
  content: "\f25a";
}

.fa-hand-rock:before {
  content: "\f255";
}

.fa-hand-scissors:before {
  content: "\f257";
}

.fa-hand-spock:before {
  content: "\f259";
}

.fa-hands:before {
  content: "\f4c2";
}

.fa-hands-helping:before {
  content: "\f4c4";
}

.fa-handshake:before {
  content: "\f2b5";
}

.fa-hashtag:before {
  content: "\f292";
}

.fa-hdd:before {
  content: "\f0a0";
}

.fa-heading:before {
  content: "\f1dc";
}

.fa-headphones:before {
  content: "\f025";
}

.fa-heart:before {
  content: "\f004";
}

.fa-heartbeat:before {
  content: "\f21e";
}

.fa-helicopter:before {
  content: "\f533";
}

.fa-hips:before {
  content: "\f452";
}

.fa-hire-a-helper:before {
  content: "\f3b0";
}

.fa-history:before {
  content: "\f1da";
}

.fa-hockey-puck:before {
  content: "\f453";
}

.fa-home:before {
  content: "\f015";
}

.fa-hooli:before {
  content: "\f427";
}

.fa-hospital:before {
  content: "\f0f8";
}

.fa-hospital-alt:before {
  content: "\f47d";
}

.fa-hospital-symbol:before {
  content: "\f47e";
}

.fa-hotjar:before {
  content: "\f3b1";
}

.fa-hourglass:before {
  content: "\f254";
}

.fa-hourglass-end:before {
  content: "\f253";
}

.fa-hourglass-half:before {
  content: "\f252";
}

.fa-hourglass-start:before {
  content: "\f251";
}

.fa-houzz:before {
  content: "\f27c";
}

.fa-html5:before {
  content: "\f13b";
}

.fa-hubspot:before {
  content: "\f3b2";
}

.fa-i-cursor:before {
  content: "\f246";
}

.fa-id-badge:before {
  content: "\f2c1";
}

.fa-id-card:before {
  content: "\f2c2";
}

.fa-id-card-alt:before {
  content: "\f47f";
}

.fa-image:before {
  content: "\f03e";
}

.fa-images:before {
  content: "\f302";
}

.fa-imdb:before {
  content: "\f2d8";
}

.fa-inbox:before {
  content: "\f01c";
}

.fa-indent:before {
  content: "\f03c";
}

.fa-industry:before {
  content: "\f275";
}

.fa-infinity:before {
  content: "\f534";
}

.fa-info:before {
  content: "\f129";
}

.fa-info-circle:before {
  content: "\f05a";
}

.fa-instagram:before {
  content: "\f16d";
}

.fa-internet-explorer:before {
  content: "\f26b";
}

.fa-ioxhost:before {
  content: "\f208";
}

.fa-italic:before {
  content: "\f033";
}

.fa-itunes:before {
  content: "\f3b4";
}

.fa-itunes-note:before {
  content: "\f3b5";
}

.fa-java:before {
  content: "\f4e4";
}

.fa-jedi-order:before {
  content: "\f50e";
}

.fa-jenkins:before {
  content: "\f3b6";
}

.fa-joget:before {
  content: "\f3b7";
}

.fa-joomla:before {
  content: "\f1aa";
}

.fa-js:before {
  content: "\f3b8";
}

.fa-js-square:before {
  content: "\f3b9";
}

.fa-jsfiddle:before {
  content: "\f1cc";
}

.fa-key:before {
  content: "\f084";
}

.fa-keybase:before {
  content: "\f4f5";
}

.fa-keyboard:before {
  content: "\f11c";
}

.fa-keycdn:before {
  content: "\f3ba";
}

.fa-kickstarter:before {
  content: "\f3bb";
}

.fa-kickstarter-k:before {
  content: "\f3bc";
}

.fa-kiwi-bird:before {
  content: "\f535";
}

.fa-korvue:before {
  content: "\f42f";
}

.fa-language:before {
  content: "\f1ab";
}

.fa-laptop:before {
  content: "\f109";
}

.fa-laravel:before {
  content: "\f3bd";
}

.fa-lastfm:before {
  content: "\f202";
}

.fa-lastfm-square:before {
  content: "\f203";
}

.fa-leaf:before {
  content: "\f06c";
}

.fa-leanpub:before {
  content: "\f212";
}

.fa-lemon:before {
  content: "\f094";
}

.fa-less:before {
  content: "\f41d";
}

.fa-less-than:before {
  content: "\f536";
}

.fa-less-than-equal:before {
  content: "\f537";
}

.fa-level-down-alt:before {
  content: "\f3be";
}

.fa-level-up-alt:before {
  content: "\f3bf";
}

.fa-life-ring:before {
  content: "\f1cd";
}

.fa-lightbulb:before {
  content: "\f0eb";
}

.fa-line:before {
  content: "\f3c0";
}

.fa-link:before {
  content: "\f0c1";
}

.fa-linkedin:before {
  content: "\f08c";
}

.fa-linkedin-in:before {
  content: "\f0e1";
}

.fa-linode:before {
  content: "\f2b8";
}

.fa-linux:before {
  content: "\f17c";
}

.fa-lira-sign:before {
  content: "\f195";
}

.fa-list:before {
  content: "\f03a";
}

.fa-list-alt:before {
  content: "\f022";
}

.fa-list-ol:before {
  content: "\f0cb";
}

.fa-list-ul:before {
  content: "\f0ca";
}

.fa-location-arrow:before {
  content: "\f124";
}

.fa-lock:before {
  content: "\f023";
}

.fa-lock-open:before {
  content: "\f3c1";
}

.fa-long-arrow-alt-down:before {
  content: "\f309";
}

.fa-long-arrow-alt-left:before {
  content: "\f30a";
}

.fa-long-arrow-alt-right:before {
  content: "\f30b";
}

.fa-long-arrow-alt-up:before {
  content: "\f30c";
}

.fa-low-vision:before {
  content: "\f2a8";
}

.fa-lyft:before {
  content: "\f3c3";
}

.fa-magento:before {
  content: "\f3c4";
}

.fa-magic:before {
  content: "\f0d0";
}

.fa-magnet:before {
  content: "\f076";
}

.fa-male:before {
  content: "\f183";
}

.fa-mandalorian:before {
  content: "\f50f";
}

.fa-map:before {
  content: "\f279";
}

.fa-map-marker:before {
  content: "\f041";
}

.fa-map-marker-alt:before {
  content: "\f3c5";
}

.fa-map-pin:before {
  content: "\f276";
}

.fa-map-signs:before {
  content: "\f277";
}

.fa-mars:before {
  content: "\f222";
}

.fa-mars-double:before {
  content: "\f227";
}

.fa-mars-stroke:before {
  content: "\f229";
}

.fa-mars-stroke-h:before {
  content: "\f22b";
}

.fa-mars-stroke-v:before {
  content: "\f22a";
}

.fa-mastodon:before {
  content: "\f4f6";
}

.fa-maxcdn:before {
  content: "\f136";
}

.fa-medapps:before {
  content: "\f3c6";
}

.fa-medium:before {
  content: "\f23a";
}

.fa-medium-m:before {
  content: "\f3c7";
}

.fa-medkit:before {
  content: "\f0fa";
}

.fa-medrt:before {
  content: "\f3c8";
}

.fa-meetup:before {
  content: "\f2e0";
}

.fa-meh:before {
  content: "\f11a";
}

.fa-memory:before {
  content: "\f538";
}

.fa-mercury:before {
  content: "\f223";
}

.fa-microchip:before {
  content: "\f2db";
}

.fa-microphone:before {
  content: "\f130";
}

.fa-microphone-alt:before {
  content: "\f3c9";
}

.fa-microphone-alt-slash:before {
  content: "\f539";
}

.fa-microphone-slash:before {
  content: "\f131";
}

.fa-microsoft:before {
  content: "\f3ca";
}

.fa-minus:before {
  content: "\f068";
}

.fa-minus-circle:before {
  content: "\f056";
}

.fa-minus-square:before {
  content: "\f146";
}

.fa-mix:before {
  content: "\f3cb";
}

.fa-mixcloud:before {
  content: "\f289";
}

.fa-mizuni:before {
  content: "\f3cc";
}

.fa-mobile:before {
  content: "\f10b";
}

.fa-mobile-alt:before {
  content: "\f3cd";
}

.fa-modx:before {
  content: "\f285";
}

.fa-monero:before {
  content: "\f3d0";
}

.fa-money-bill:before {
  content: "\f0d6";
}

.fa-money-bill-alt:before {
  content: "\f3d1";
}

.fa-money-bill-wave:before {
  content: "\f53a";
}

.fa-money-bill-wave-alt:before {
  content: "\f53b";
}

.fa-money-check:before {
  content: "\f53c";
}

.fa-money-check-alt:before {
  content: "\f53d";
}

.fa-moon:before {
  content: "\f186";
}

.fa-motorcycle:before {
  content: "\f21c";
}

.fa-mouse-pointer:before {
  content: "\f245";
}

.fa-music:before {
  content: "\f001";
}

.fa-napster:before {
  content: "\f3d2";
}

.fa-neuter:before {
  content: "\f22c";
}

.fa-newspaper:before {
  content: "\f1ea";
}

.fa-nintendo-switch:before {
  content: "\f418";
}

.fa-node:before {
  content: "\f419";
}

.fa-node-js:before {
  content: "\f3d3";
}

.fa-not-equal:before {
  content: "\f53e";
}

.fa-notes-medical:before {
  content: "\f481";
}

.fa-npm:before {
  content: "\f3d4";
}

.fa-ns8:before {
  content: "\f3d5";
}

.fa-nutritionix:before {
  content: "\f3d6";
}

.fa-object-group:before {
  content: "\f247";
}

.fa-object-ungroup:before {
  content: "\f248";
}

.fa-odnoklassniki:before {
  content: "\f263";
}

.fa-odnoklassniki-square:before {
  content: "\f264";
}

.fa-old-republic:before {
  content: "\f510";
}

.fa-opencart:before {
  content: "\f23d";
}

.fa-openid:before {
  content: "\f19b";
}

.fa-opera:before {
  content: "\f26a";
}

.fa-optin-monster:before {
  content: "\f23c";
}

.fa-osi:before {
  content: "\f41a";
}

.fa-outdent:before {
  content: "\f03b";
}

.fa-page4:before {
  content: "\f3d7";
}

.fa-pagelines:before {
  content: "\f18c";
}

.fa-paint-brush:before {
  content: "\f1fc";
}

.fa-palette:before {
  content: "\f53f";
}

.fa-palfed:before {
  content: "\f3d8";
}

.fa-pallet:before {
  content: "\f482";
}

.fa-paper-plane:before {
  content: "\f1d8";
}

.fa-paperclip:before {
  content: "\f0c6";
}

.fa-parachute-box:before {
  content: "\f4cd";
}

.fa-paragraph:before {
  content: "\f1dd";
}

.fa-parking:before {
  content: "\f540";
}

.fa-paste:before {
  content: "\f0ea";
}

.fa-patreon:before {
  content: "\f3d9";
}

.fa-pause:before {
  content: "\f04c";
}

.fa-pause-circle:before {
  content: "\f28b";
}

.fa-paw:before {
  content: "\f1b0";
}

.fa-paypal:before {
  content: "\f1ed";
}

.fa-pen-square:before {
  content: "\f14b";
}

.fa-pencil-alt:before {
  content: "\f303";
}

.fa-people-carry:before {
  content: "\f4ce";
}

.fa-percent:before {
  content: "\f295";
}

.fa-percentage:before {
  content: "\f541";
}

.fa-periscope:before {
  content: "\f3da";
}

.fa-phabricator:before {
  content: "\f3db";
}

.fa-phoenix-framework:before {
  content: "\f3dc";
}

.fa-phoenix-squadron:before {
  content: "\f511";
}

.fa-phone:before {
  content: "\f095";
}

.fa-phone-slash:before {
  content: "\f3dd";
}

.fa-phone-square:before {
  content: "\f098";
}

.fa-phone-volume:before {
  content: "\f2a0";
}

.fa-php:before {
  content: "\f457";
}

.fa-pied-piper:before {
  content: "\f2ae";
}

.fa-pied-piper-alt:before {
  content: "\f1a8";
}

.fa-pied-piper-hat:before {
  content: "\f4e5";
}

.fa-pied-piper-pp:before {
  content: "\f1a7";
}

.fa-piggy-bank:before {
  content: "\f4d3";
}

.fa-pills:before {
  content: "\f484";
}

.fa-pinterest:before {
  content: "\f0d2";
}

.fa-pinterest-p:before {
  content: "\f231";
}

.fa-pinterest-square:before {
  content: "\f0d3";
}

.fa-plane:before {
  content: "\f072";
}

.fa-play:before {
  content: "\f04b";
}

.fa-play-circle:before {
  content: "\f144";
}

.fa-playstation:before {
  content: "\f3df";
}

.fa-plug:before {
  content: "\f1e6";
}

.fa-plus:before {
  content: "\f067";
}

.fa-plus-circle:before {
  content: "\f055";
}

.fa-plus-square:before {
  content: "\f0fe";
}

.fa-podcast:before {
  content: "\f2ce";
}

.fa-poo:before {
  content: "\f2fe";
}

.fa-portrait:before {
  content: "\f3e0";
}

.fa-pound-sign:before {
  content: "\f154";
}

.fa-power-off:before {
  content: "\f011";
}

.fa-prescription-bottle:before {
  content: "\f485";
}

.fa-prescription-bottle-alt:before {
  content: "\f486";
}

.fa-print:before {
  content: "\f02f";
}

.fa-procedures:before {
  content: "\f487";
}

.fa-product-hunt:before {
  content: "\f288";
}

.fa-project-diagram:before {
  content: "\f542";
}

.fa-pushed:before {
  content: "\f3e1";
}

.fa-puzzle-piece:before {
  content: "\f12e";
}

.fa-python:before {
  content: "\f3e2";
}

.fa-qq:before {
  content: "\f1d6";
}

.fa-qrcode:before {
  content: "\f029";
}

.fa-question:before {
  content: "\f128";
}

.fa-question-circle:before {
  content: "\f059";
}

.fa-quidditch:before {
  content: "\f458";
}

.fa-quinscape:before {
  content: "\f459";
}

.fa-quora:before {
  content: "\f2c4";
}

.fa-quote-left:before {
  content: "\f10d";
}

.fa-quote-right:before {
  content: "\f10e";
}

.fa-r-project:before {
  content: "\f4f7";
}

.fa-random:before {
  content: "\f074";
}

.fa-ravelry:before {
  content: "\f2d9";
}

.fa-react:before {
  content: "\f41b";
}

.fa-readme:before {
  content: "\f4d5";
}

.fa-rebel:before {
  content: "\f1d0";
}

.fa-receipt:before {
  content: "\f543";
}

.fa-recycle:before {
  content: "\f1b8";
}

.fa-red-river:before {
  content: "\f3e3";
}

.fa-reddit:before {
  content: "\f1a1";
}

.fa-reddit-alien:before {
  content: "\f281";
}

.fa-reddit-square:before {
  content: "\f1a2";
}

.fa-redo:before {
  content: "\f01e";
}

.fa-redo-alt:before {
  content: "\f2f9";
}

.fa-registered:before {
  content: "\f25d";
}

.fa-rendact:before {
  content: "\f3e4";
}

.fa-renren:before {
  content: "\f18b";
}

.fa-reply:before {
  content: "\f3e5";
}

.fa-reply-all:before {
  content: "\f122";
}

.fa-replyd:before {
  content: "\f3e6";
}

.fa-researchgate:before {
  content: "\f4f8";
}

.fa-resolving:before {
  content: "\f3e7";
}

.fa-retweet:before {
  content: "\f079";
}

.fa-ribbon:before {
  content: "\f4d6";
}

.fa-road:before {
  content: "\f018";
}

.fa-robot:before {
  content: "\f544";
}

.fa-rocket:before {
  content: "\f135";
}

.fa-rocketchat:before {
  content: "\f3e8";
}

.fa-rockrms:before {
  content: "\f3e9";
}

.fa-rss:before {
  content: "\f09e";
}

.fa-rss-square:before {
  content: "\f143";
}

.fa-ruble-sign:before {
  content: "\f158";
}

.fa-ruler:before {
  content: "\f545";
}

.fa-ruler-combined:before {
  content: "\f546";
}

.fa-ruler-horizontal:before {
  content: "\f547";
}

.fa-ruler-vertical:before {
  content: "\f548";
}

.fa-rupee-sign:before {
  content: "\f156";
}

.fa-safari:before {
  content: "\f267";
}

.fa-sass:before {
  content: "\f41e";
}

.fa-save:before {
  content: "\f0c7";
}

.fa-schlix:before {
  content: "\f3ea";
}

.fa-school:before {
  content: "\f549";
}

.fa-screwdriver:before {
  content: "\f54a";
}

.fa-scribd:before {
  content: "\f28a";
}

.fa-search:before {
  content: "\f002";
}

.fa-search-minus:before {
  content: "\f010";
}

.fa-search-plus:before {
  content: "\f00e";
}

.fa-searchengin:before {
  content: "\f3eb";
}

.fa-seedling:before {
  content: "\f4d8";
}

.fa-sellcast:before {
  content: "\f2da";
}

.fa-sellsy:before {
  content: "\f213";
}

.fa-server:before {
  content: "\f233";
}

.fa-servicestack:before {
  content: "\f3ec";
}

.fa-share:before {
  content: "\f064";
}

.fa-share-alt:before {
  content: "\f1e0";
}

.fa-share-alt-square:before {
  content: "\f1e1";
}

.fa-share-square:before {
  content: "\f14d";
}

.fa-shekel-sign:before {
  content: "\f20b";
}

.fa-shield-alt:before {
  content: "\f3ed";
}

.fa-ship:before {
  content: "\f21a";
}

.fa-shipping-fast:before {
  content: "\f48b";
}

.fa-shirtsinbulk:before {
  content: "\f214";
}

.fa-shoe-prints:before {
  content: "\f54b";
}

.fa-shopping-bag:before {
  content: "\f290";
}

.fa-shopping-basket:before {
  content: "\f291";
}

.fa-shopping-cart:before {
  content: "\f07a";
}

.fa-shower:before {
  content: "\f2cc";
}

.fa-sign:before {
  content: "\f4d9";
}

.fa-sign-in-alt:before {
  content: "\f2f6";
}

.fa-sign-language:before {
  content: "\f2a7";
}

.fa-sign-out-alt:before {
  content: "\f2f5";
}

.fa-signal:before {
  content: "\f012";
}

.fa-simplybuilt:before {
  content: "\f215";
}

.fa-sistrix:before {
  content: "\f3ee";
}

.fa-sitemap:before {
  content: "\f0e8";
}

.fa-sith:before {
  content: "\f512";
}

.fa-skull:before {
  content: "\f54c";
}

.fa-skyatlas:before {
  content: "\f216";
}

.fa-skype:before {
  content: "\f17e";
}

.fa-slack:before {
  content: "\f198";
}

.fa-slack-hash:before {
  content: "\f3ef";
}

.fa-sliders-h:before {
  content: "\f1de";
}

.fa-slideshare:before {
  content: "\f1e7";
}

.fa-smile:before {
  content: "\f118";
}

.fa-smoking:before {
  content: "\f48d";
}

.fa-smoking-ban:before {
  content: "\f54d";
}

.fa-snapchat:before {
  content: "\f2ab";
}

.fa-snapchat-ghost:before {
  content: "\f2ac";
}

.fa-snapchat-square:before {
  content: "\f2ad";
}

.fa-snowflake:before {
  content: "\f2dc";
}

.fa-sort:before {
  content: "\f0dc";
}

.fa-sort-alpha-down:before {
  content: "\f15d";
}

.fa-sort-alpha-up:before {
  content: "\f15e";
}

.fa-sort-amount-down:before {
  content: "\f160";
}

.fa-sort-amount-up:before {
  content: "\f161";
}

.fa-sort-down:before {
  content: "\f0dd";
}

.fa-sort-numeric-down:before {
  content: "\f162";
}

.fa-sort-numeric-up:before {
  content: "\f163";
}

.fa-sort-up:before {
  content: "\f0de";
}

.fa-soundcloud:before {
  content: "\f1be";
}

.fa-space-shuttle:before {
  content: "\f197";
}

.fa-speakap:before {
  content: "\f3f3";
}

.fa-spinner:before {
  content: "\f110";
}

.fa-spotify:before {
  content: "\f1bc";
}

.fa-square:before {
  content: "\f0c8";
}

.fa-square-full:before {
  content: "\f45c";
}

.fa-stack-exchange:before {
  content: "\f18d";
}

.fa-stack-overflow:before {
  content: "\f16c";
}

.fa-star:before {
  content: "\f005";
}

.fa-star-half:before {
  content: "\f089";
}

.fa-staylinked:before {
  content: "\f3f5";
}

.fa-steam:before {
  content: "\f1b6";
}

.fa-steam-square:before {
  content: "\f1b7";
}

.fa-steam-symbol:before {
  content: "\f3f6";
}

.fa-step-backward:before {
  content: "\f048";
}

.fa-step-forward:before {
  content: "\f051";
}

.fa-stethoscope:before {
  content: "\f0f1";
}

.fa-sticker-mule:before {
  content: "\f3f7";
}

.fa-sticky-note:before {
  content: "\f249";
}

.fa-stop:before {
  content: "\f04d";
}

.fa-stop-circle:before {
  content: "\f28d";
}

.fa-stopwatch:before {
  content: "\f2f2";
}

.fa-store:before {
  content: "\f54e";
}

.fa-store-alt:before {
  content: "\f54f";
}

.fa-strava:before {
  content: "\f428";
}

.fa-stream:before {
  content: "\f550";
}

.fa-street-view:before {
  content: "\f21d";
}

.fa-strikethrough:before {
  content: "\f0cc";
}

.fa-stripe:before {
  content: "\f429";
}

.fa-stripe-s:before {
  content: "\f42a";
}

.fa-stroopwafel:before {
  content: "\f551";
}

.fa-studiovinari:before {
  content: "\f3f8";
}

.fa-stumbleupon:before {
  content: "\f1a4";
}

.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

.fa-subscript:before {
  content: "\f12c";
}

.fa-subway:before {
  content: "\f239";
}

.fa-suitcase:before {
  content: "\f0f2";
}

.fa-sun:before {
  content: "\f185";
}

.fa-superpowers:before {
  content: "\f2dd";
}

.fa-superscript:before {
  content: "\f12b";
}

.fa-supple:before {
  content: "\f3f9";
}

.fa-sync:before {
  content: "\f021";
}

.fa-sync-alt:before {
  content: "\f2f1";
}

.fa-syringe:before {
  content: "\f48e";
}

.fa-table:before {
  content: "\f0ce";
}

.fa-table-tennis:before {
  content: "\f45d";
}

.fa-tablet:before {
  content: "\f10a";
}

.fa-tablet-alt:before {
  content: "\f3fa";
}

.fa-tablets:before {
  content: "\f490";
}

.fa-tachometer-alt:before {
  content: "\f3fd";
}

.fa-tag:before {
  content: "\f02b";
}

.fa-tags:before {
  content: "\f02c";
}

.fa-tape:before {
  content: "\f4db";
}

.fa-tasks:before {
  content: "\f0ae";
}

.fa-taxi:before {
  content: "\f1ba";
}

.fa-teamspeak:before {
  content: "\f4f9";
}

.fa-telegram:before {
  content: "\f2c6";
}

.fa-telegram-plane:before {
  content: "\f3fe";
}

.fa-tencent-weibo:before {
  content: "\f1d5";
}

.fa-terminal:before {
  content: "\f120";
}

.fa-text-height:before {
  content: "\f034";
}

.fa-text-width:before {
  content: "\f035";
}

.fa-th:before {
  content: "\f00a";
}

.fa-th-large:before {
  content: "\f009";
}

.fa-th-list:before {
  content: "\f00b";
}

.fa-themeisle:before {
  content: "\f2b2";
}

.fa-thermometer:before {
  content: "\f491";
}

.fa-thermometer-empty:before {
  content: "\f2cb";
}

.fa-thermometer-full:before {
  content: "\f2c7";
}

.fa-thermometer-half:before {
  content: "\f2c9";
}

.fa-thermometer-quarter:before {
  content: "\f2ca";
}

.fa-thermometer-three-quarters:before {
  content: "\f2c8";
}

.fa-thumbs-down:before {
  content: "\f165";
}

.fa-thumbs-up:before {
  content: "\f164";
}

.fa-thumbtack:before {
  content: "\f08d";
}

.fa-ticket-alt:before {
  content: "\f3ff";
}

.fa-times:before {
  content: "\f00d";
}

.fa-times-circle:before {
  content: "\f057";
}

.fa-tint:before {
  content: "\f043";
}

.fa-toggle-off:before {
  content: "\f204";
}

.fa-toggle-on:before {
  content: "\f205";
}

.fa-toolbox:before {
  content: "\f552";
}

.fa-trade-federation:before {
  content: "\f513";
}

.fa-trademark:before {
  content: "\f25c";
}

.fa-train:before {
  content: "\f238";
}

.fa-transgender:before {
  content: "\f224";
}

.fa-transgender-alt:before {
  content: "\f225";
}

.fa-trash:before {
  content: "\f1f8";
}

.fa-trash-alt:before {
  content: "\f2ed";
}

.fa-tree:before {
  content: "\f1bb";
}

.fa-trello:before {
  content: "\f181";
}

.fa-tripadvisor:before {
  content: "\f262";
}

.fa-trophy:before {
  content: "\f091";
}

.fa-truck:before {
  content: "\f0d1";
}

.fa-truck-loading:before {
  content: "\f4de";
}

.fa-truck-moving:before {
  content: "\f4df";
}

.fa-tshirt:before {
  content: "\f553";
}

.fa-tty:before {
  content: "\f1e4";
}

.fa-tumblr:before {
  content: "\f173";
}

.fa-tumblr-square:before {
  content: "\f174";
}

.fa-tv:before {
  content: "\f26c";
}

.fa-twitch:before {
  content: "\f1e8";
}

.fa-twitter:before {
  content: "\f099";
}

.fa-twitter-square:before {
  content: "\f081";
}

.fa-typo3:before {
  content: "\f42b";
}

.fa-uber:before {
  content: "\f402";
}

.fa-uikit:before {
  content: "\f403";
}

.fa-umbrella:before {
  content: "\f0e9";
}

.fa-underline:before {
  content: "\f0cd";
}

.fa-undo:before {
  content: "\f0e2";
}

.fa-undo-alt:before {
  content: "\f2ea";
}

.fa-uniregistry:before {
  content: "\f404";
}

.fa-universal-access:before {
  content: "\f29a";
}

.fa-university:before {
  content: "\f19c";
}

.fa-unlink:before {
  content: "\f127";
}

.fa-unlock:before {
  content: "\f09c";
}

.fa-unlock-alt:before {
  content: "\f13e";
}

.fa-untappd:before {
  content: "\f405";
}

.fa-upload:before {
  content: "\f093";
}

.fa-usb:before {
  content: "\f287";
}

.fa-user:before {
  content: "\f007";
}

.fa-user-alt:before {
  content: "\f406";
}

.fa-user-alt-slash:before {
  content: "\f4fa";
}

.fa-user-astronaut:before {
  content: "\f4fb";
}

.fa-user-check:before {
  content: "\f4fc";
}

.fa-user-circle:before {
  content: "\f2bd";
}

.fa-user-clock:before {
  content: "\f4fd";
}

.fa-user-cog:before {
  content: "\f4fe";
}

.fa-user-edit:before {
  content: "\f4ff";
}

.fa-user-friends:before {
  content: "\f500";
}

.fa-user-graduate:before {
  content: "\f501";
}

.fa-user-lock:before {
  content: "\f502";
}

.fa-user-md:before {
  content: "\f0f0";
}

.fa-user-minus:before {
  content: "\f503";
}

.fa-user-ninja:before {
  content: "\f504";
}

.fa-user-plus:before {
  content: "\f234";
}

.fa-user-secret:before {
  content: "\f21b";
}

.fa-user-shield:before {
  content: "\f505";
}

.fa-user-slash:before {
  content: "\f506";
}

.fa-user-tag:before {
  content: "\f507";
}

.fa-user-tie:before {
  content: "\f508";
}

.fa-user-times:before {
  content: "\f235";
}

.fa-users:before {
  content: "\f0c0";
}

.fa-users-cog:before {
  content: "\f509";
}

.fa-ussunnah:before {
  content: "\f407";
}

.fa-utensil-spoon:before {
  content: "\f2e5";
}

.fa-utensils:before {
  content: "\f2e7";
}

.fa-vaadin:before {
  content: "\f408";
}

.fa-venus:before {
  content: "\f221";
}

.fa-venus-double:before {
  content: "\f226";
}

.fa-venus-mars:before {
  content: "\f228";
}

.fa-viacoin:before {
  content: "\f237";
}

.fa-viadeo:before {
  content: "\f2a9";
}

.fa-viadeo-square:before {
  content: "\f2aa";
}

.fa-vial:before {
  content: "\f492";
}

.fa-vials:before {
  content: "\f493";
}

.fa-viber:before {
  content: "\f409";
}

.fa-video:before {
  content: "\f03d";
}

.fa-video-slash:before {
  content: "\f4e2";
}

.fa-vimeo:before {
  content: "\f40a";
}

.fa-vimeo-square:before {
  content: "\f194";
}

.fa-vimeo-v:before {
  content: "\f27d";
}

.fa-vine:before {
  content: "\f1ca";
}

.fa-vk:before {
  content: "\f189";
}

.fa-vnv:before {
  content: "\f40b";
}

.fa-volleyball-ball:before {
  content: "\f45f";
}

.fa-volume-down:before {
  content: "\f027";
}

.fa-volume-off:before {
  content: "\f026";
}

.fa-volume-up:before {
  content: "\f028";
}

.fa-vuejs:before {
  content: "\f41f";
}

.fa-walking:before {
  content: "\f554";
}

.fa-wallet:before {
  content: "\f555";
}

.fa-warehouse:before {
  content: "\f494";
}

.fa-weibo:before {
  content: "\f18a";
}

.fa-weight:before {
  content: "\f496";
}

.fa-weixin:before {
  content: "\f1d7";
}

.fa-whatsapp:before {
  content: "\f232";
}

.fa-whatsapp-square:before {
  content: "\f40c";
}

.fa-wheelchair:before {
  content: "\f193";
}

.fa-whmcs:before {
  content: "\f40d";
}

.fa-wifi:before {
  content: "\f1eb";
}

.fa-wikipedia-w:before {
  content: "\f266";
}

.fa-window-close:before {
  content: "\f410";
}

.fa-window-maximize:before {
  content: "\f2d0";
}

.fa-window-minimize:before {
  content: "\f2d1";
}

.fa-window-restore:before {
  content: "\f2d2";
}

.fa-windows:before {
  content: "\f17a";
}

.fa-wine-glass:before {
  content: "\f4e3";
}

.fa-wolf-pack-battalion:before {
  content: "\f514";
}

.fa-won-sign:before {
  content: "\f159";
}

.fa-wordpress:before {
  content: "\f19a";
}

.fa-wordpress-simple:before {
  content: "\f411";
}

.fa-wpbeginner:before {
  content: "\f297";
}

.fa-wpexplorer:before {
  content: "\f2de";
}

.fa-wpforms:before {
  content: "\f298";
}

.fa-wrench:before {
  content: "\f0ad";
}

.fa-x-ray:before {
  content: "\f497";
}

.fa-xbox:before {
  content: "\f412";
}

.fa-xing:before {
  content: "\f168";
}

.fa-xing-square:before {
  content: "\f169";
}

.fa-y-combinator:before {
  content: "\f23b";
}

.fa-yahoo:before {
  content: "\f19e";
}

.fa-yandex:before {
  content: "\f413";
}

.fa-yandex-international:before {
  content: "\f414";
}

.fa-yelp:before {
  content: "\f1e9";
}

.fa-yen-sign:before {
  content: "\f157";
}

.fa-yoast:before {
  content: "\f2b1";
}

.fa-youtube:before {
  content: "\f167";
}

.fa-youtube-square:before {
  content: "\f431";
}

.sr-only {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

/*!
 * Font Awesome Free 5.0.13 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 900;
  src: url("../fonts/fa-solid-900.eot");
  src: url("../fonts/fa-solid-900d41d.eot?#iefix") format("embedded-opentype"), url("../fonts/fa-solid-900.woff2") format("woff2"), url("../fonts/fa-solid-900.woff") format("woff"), url("../fonts/fa-solid-900.ttf") format("truetype"), url("../fonts/fa-solid-900.svg#fontawesome") format("svg");
}

.fa,
.fas {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

/*!
 * Font Awesome Free 5.0.13 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@font-face {
  font-family: 'Font Awesome 5 Brands';
  font-style: normal;
  font-weight: normal;
  src: url("../fonts/fa-brands-400.eot");
  src: url("../fonts/fa-brands-400d41d.eot?#iefix") format("embedded-opentype"), url("../fonts/fa-brands-400.woff2") format("woff2"), url("../fonts/fa-brands-400.woff") format("woff"), url("../fonts/fa-brands-400.ttf") format("truetype"), url("../fonts/fa-brands-400.svg#fontawesome") format("svg");
}

.fab {
  font-family: 'Font Awesome 5 Brands';
}

/*!
 * Font Awesome Free 5.0.13 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 400;
  src: url("../fonts/fa-regular-400.eot");
  src: url("../fonts/fa-regular-400d41d.eot?#iefix") format("embedded-opentype"), url("../fonts/fa-regular-400.woff2") format("woff2"), url("../fonts/fa-regular-400.woff") format("woff"), url("../fonts/fa-regular-400.ttf") format("truetype"), url("../fonts/fa-regular-400.svg#fontawesome") format("svg");
}

.far {
  font-family: 'Font Awesome 5 Free';
  font-weight: 400;
}

/* MaterialDesignIcons.com */
@font-face {
  font-family: "Material Design Icons";
  src: url("../fonts/materialdesignicons-webfont6c9c.eot?v=1.6.50");
  src: url("../fonts/materialdesignicons-webfontd41d.eot?#iefix&v=1.6.50") format("embedded-opentype"), url("../fonts/materialdesignicons-webfont6c9c.woff2?v=1.6.50") format("woff2"), url("../fonts/materialdesignicons-webfont6c9c.woff?v=1.6.50") format("woff"), url("../fonts/materialdesignicons-webfont6c9c.ttf?v=1.6.50") format("truetype"), url("../fonts/materialdesignicons-webfont6c9c.svg?v=1.6.50#materialdesigniconsregular") format("svg");
  font-weight: normal;
  font-style: normal;
}

.mdi {
  display: inline-block;
  font: normal normal normal 24px/1 "Material Design Icons";
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-transform: translate(0, 0);
          transform: translate(0, 0);
}

.mdi-access-point:before {
  content: "\F002";
}

.mdi-access-point-network:before {
  content: "\F003";
}

.mdi-account:before {
  content: "\F004";
}

.mdi-account-alert:before {
  content: "\F005";
}

.mdi-account-box:before {
  content: "\F006";
}

.mdi-account-box-outline:before {
  content: "\F007";
}

.mdi-account-card-details:before {
  content: "\F5D2";
}

.mdi-account-check:before {
  content: "\F008";
}

.mdi-account-circle:before {
  content: "\F009";
}

.mdi-account-convert:before {
  content: "\F00A";
}

.mdi-account-key:before {
  content: "\F00B";
}

.mdi-account-location:before {
  content: "\F00C";
}

.mdi-account-minus:before {
  content: "\F00D";
}

.mdi-account-multiple:before {
  content: "\F00E";
}

.mdi-account-multiple-minus:before {
  content: "\F5D3";
}

.mdi-account-multiple-outline:before {
  content: "\F00F";
}

.mdi-account-multiple-plus:before {
  content: "\F010";
}

.mdi-account-network:before {
  content: "\F011";
}

.mdi-account-off:before {
  content: "\F012";
}

.mdi-account-outline:before {
  content: "\F013";
}

.mdi-account-plus:before {
  content: "\F014";
}

.mdi-account-remove:before {
  content: "\F015";
}

.mdi-account-search:before {
  content: "\F016";
}

.mdi-account-settings:before {
  content: "\F630";
}

.mdi-account-settings-variant:before {
  content: "\F631";
}

.mdi-account-star:before {
  content: "\F017";
}

.mdi-account-star-variant:before {
  content: "\F018";
}

.mdi-account-switch:before {
  content: "\F019";
}

.mdi-adjust:before {
  content: "\F01A";
}

.mdi-air-conditioner:before {
  content: "\F01B";
}

.mdi-airballoon:before {
  content: "\F01C";
}

.mdi-airplane:before {
  content: "\F01D";
}

.mdi-airplane-landing:before {
  content: "\F5D4";
}

.mdi-airplane-off:before {
  content: "\F01E";
}

.mdi-airplane-takeoff:before {
  content: "\F5D5";
}

.mdi-airplay:before {
  content: "\F01F";
}

.mdi-alarm:before {
  content: "\F020";
}

.mdi-alarm-check:before {
  content: "\F021";
}

.mdi-alarm-multiple:before {
  content: "\F022";
}

.mdi-alarm-off:before {
  content: "\F023";
}

.mdi-alarm-plus:before {
  content: "\F024";
}

.mdi-album:before {
  content: "\F025";
}

.mdi-alert:before {
  content: "\F026";
}

.mdi-alert-box:before {
  content: "\F027";
}

.mdi-alert-circle:before {
  content: "\F028";
}

.mdi-alert-circle-outline:before {
  content: "\F5D6";
}

.mdi-alert-octagon:before {
  content: "\F029";
}

.mdi-alert-outline:before {
  content: "\F02A";
}

.mdi-alpha:before {
  content: "\F02B";
}

.mdi-alphabetical:before {
  content: "\F02C";
}

.mdi-altimeter:before {
  content: "\F5D7";
}

.mdi-amazon:before {
  content: "\F02D";
}

.mdi-amazon-clouddrive:before {
  content: "\F02E";
}

.mdi-ambulance:before {
  content: "\F02F";
}

.mdi-amplifier:before {
  content: "\F030";
}

.mdi-anchor:before {
  content: "\F031";
}

.mdi-android:before {
  content: "\F032";
}

.mdi-android-debug-bridge:before {
  content: "\F033";
}

.mdi-android-studio:before {
  content: "\F034";
}

.mdi-animation:before {
  content: "\F5D8";
}

.mdi-apple:before {
  content: "\F035";
}

.mdi-apple-finder:before {
  content: "\F036";
}

.mdi-apple-ios:before {
  content: "\F037";
}

.mdi-apple-keyboard-caps:before {
  content: "\F632";
}

.mdi-apple-keyboard-command:before {
  content: "\F633";
}

.mdi-apple-keyboard-control:before {
  content: "\F634";
}

.mdi-apple-keyboard-option:before {
  content: "\F635";
}

.mdi-apple-keyboard-shift:before {
  content: "\F636";
}

.mdi-apple-mobileme:before {
  content: "\F038";
}

.mdi-apple-safari:before {
  content: "\F039";
}

.mdi-application:before {
  content: "\F614";
}

.mdi-appnet:before {
  content: "\F03A";
}

.mdi-apps:before {
  content: "\F03B";
}

.mdi-archive:before {
  content: "\F03C";
}

.mdi-arrange-bring-forward:before {
  content: "\F03D";
}

.mdi-arrange-bring-to-front:before {
  content: "\F03E";
}

.mdi-arrange-send-backward:before {
  content: "\F03F";
}

.mdi-arrange-send-to-back:before {
  content: "\F040";
}

.mdi-arrow-all:before {
  content: "\F041";
}

.mdi-arrow-bottom-left:before {
  content: "\F042";
}

.mdi-arrow-bottom-right:before {
  content: "\F043";
}

.mdi-arrow-compress:before {
  content: "\F615";
}

.mdi-arrow-compress-all:before {
  content: "\F044";
}

.mdi-arrow-down:before {
  content: "\F045";
}

.mdi-arrow-down-bold:before {
  content: "\F046";
}

.mdi-arrow-down-bold-circle:before {
  content: "\F047";
}

.mdi-arrow-down-bold-circle-outline:before {
  content: "\F048";
}

.mdi-arrow-down-bold-hexagon-outline:before {
  content: "\F049";
}

.mdi-arrow-down-drop-circle:before {
  content: "\F04A";
}

.mdi-arrow-down-drop-circle-outline:before {
  content: "\F04B";
}

.mdi-arrow-expand:before {
  content: "\F616";
}

.mdi-arrow-expand-all:before {
  content: "\F04C";
}

.mdi-arrow-left:before {
  content: "\F04D";
}

.mdi-arrow-left-bold:before {
  content: "\F04E";
}

.mdi-arrow-left-bold-circle:before {
  content: "\F04F";
}

.mdi-arrow-left-bold-circle-outline:before {
  content: "\F050";
}

.mdi-arrow-left-bold-hexagon-outline:before {
  content: "\F051";
}

.mdi-arrow-left-drop-circle:before {
  content: "\F052";
}

.mdi-arrow-left-drop-circle-outline:before {
  content: "\F053";
}

.mdi-arrow-right:before {
  content: "\F054";
}

.mdi-arrow-right-bold:before {
  content: "\F055";
}

.mdi-arrow-right-bold-circle:before {
  content: "\F056";
}

.mdi-arrow-right-bold-circle-outline:before {
  content: "\F057";
}

.mdi-arrow-right-bold-hexagon-outline:before {
  content: "\F058";
}

.mdi-arrow-right-drop-circle:before {
  content: "\F059";
}

.mdi-arrow-right-drop-circle-outline:before {
  content: "\F05A";
}

.mdi-arrow-top-left:before {
  content: "\F05B";
}

.mdi-arrow-top-right:before {
  content: "\F05C";
}

.mdi-arrow-up:before {
  content: "\F05D";
}

.mdi-arrow-up-bold:before {
  content: "\F05E";
}

.mdi-arrow-up-bold-circle:before {
  content: "\F05F";
}

.mdi-arrow-up-bold-circle-outline:before {
  content: "\F060";
}

.mdi-arrow-up-bold-hexagon-outline:before {
  content: "\F061";
}

.mdi-arrow-up-drop-circle:before {
  content: "\F062";
}

.mdi-arrow-up-drop-circle-outline:before {
  content: "\F063";
}

.mdi-assistant:before {
  content: "\F064";
}

.mdi-at:before {
  content: "\F065";
}

.mdi-attachment:before {
  content: "\F066";
}

.mdi-audiobook:before {
  content: "\F067";
}

.mdi-auto-fix:before {
  content: "\F068";
}

.mdi-auto-upload:before {
  content: "\F069";
}

.mdi-autorenew:before {
  content: "\F06A";
}

.mdi-av-timer:before {
  content: "\F06B";
}

.mdi-baby:before {
  content: "\F06C";
}

.mdi-backburger:before {
  content: "\F06D";
}

.mdi-backspace:before {
  content: "\F06E";
}

.mdi-backup-restore:before {
  content: "\F06F";
}

.mdi-bank:before {
  content: "\F070";
}

.mdi-barcode:before {
  content: "\F071";
}

.mdi-barcode-scan:before {
  content: "\F072";
}

.mdi-barley:before {
  content: "\F073";
}

.mdi-barrel:before {
  content: "\F074";
}

.mdi-basecamp:before {
  content: "\F075";
}

.mdi-basket:before {
  content: "\F076";
}

.mdi-basket-fill:before {
  content: "\F077";
}

.mdi-basket-unfill:before {
  content: "\F078";
}

.mdi-battery:before {
  content: "\F079";
}

.mdi-battery-10:before {
  content: "\F07A";
}

.mdi-battery-20:before {
  content: "\F07B";
}

.mdi-battery-30:before {
  content: "\F07C";
}

.mdi-battery-40:before {
  content: "\F07D";
}

.mdi-battery-50:before {
  content: "\F07E";
}

.mdi-battery-60:before {
  content: "\F07F";
}

.mdi-battery-70:before {
  content: "\F080";
}

.mdi-battery-80:before {
  content: "\F081";
}

.mdi-battery-90:before {
  content: "\F082";
}

.mdi-battery-alert:before {
  content: "\F083";
}

.mdi-battery-charging:before {
  content: "\F084";
}

.mdi-battery-charging-100:before {
  content: "\F085";
}

.mdi-battery-charging-20:before {
  content: "\F086";
}

.mdi-battery-charging-30:before {
  content: "\F087";
}

.mdi-battery-charging-40:before {
  content: "\F088";
}

.mdi-battery-charging-60:before {
  content: "\F089";
}

.mdi-battery-charging-80:before {
  content: "\F08A";
}

.mdi-battery-charging-90:before {
  content: "\F08B";
}

.mdi-battery-minus:before {
  content: "\F08C";
}

.mdi-battery-negative:before {
  content: "\F08D";
}

.mdi-battery-outline:before {
  content: "\F08E";
}

.mdi-battery-plus:before {
  content: "\F08F";
}

.mdi-battery-positive:before {
  content: "\F090";
}

.mdi-battery-unknown:before {
  content: "\F091";
}

.mdi-beach:before {
  content: "\F092";
}

.mdi-beats:before {
  content: "\F097";
}

.mdi-beer:before {
  content: "\F098";
}

.mdi-behance:before {
  content: "\F099";
}

.mdi-bell:before {
  content: "\F09A";
}

.mdi-bell-off:before {
  content: "\F09B";
}

.mdi-bell-outline:before {
  content: "\F09C";
}

.mdi-bell-plus:before {
  content: "\F09D";
}

.mdi-bell-ring:before {
  content: "\F09E";
}

.mdi-bell-ring-outline:before {
  content: "\F09F";
}

.mdi-bell-sleep:before {
  content: "\F0A0";
}

.mdi-beta:before {
  content: "\F0A1";
}

.mdi-bible:before {
  content: "\F0A2";
}

.mdi-bike:before {
  content: "\F0A3";
}

.mdi-bing:before {
  content: "\F0A4";
}

.mdi-binoculars:before {
  content: "\F0A5";
}

.mdi-bio:before {
  content: "\F0A6";
}

.mdi-biohazard:before {
  content: "\F0A7";
}

.mdi-bitbucket:before {
  content: "\F0A8";
}

.mdi-black-mesa:before {
  content: "\F0A9";
}

.mdi-blackberry:before {
  content: "\F0AA";
}

.mdi-blender:before {
  content: "\F0AB";
}

.mdi-blinds:before {
  content: "\F0AC";
}

.mdi-block-helper:before {
  content: "\F0AD";
}

.mdi-blogger:before {
  content: "\F0AE";
}

.mdi-bluetooth:before {
  content: "\F0AF";
}

.mdi-bluetooth-audio:before {
  content: "\F0B0";
}

.mdi-bluetooth-connect:before {
  content: "\F0B1";
}

.mdi-bluetooth-off:before {
  content: "\F0B2";
}

.mdi-bluetooth-settings:before {
  content: "\F0B3";
}

.mdi-bluetooth-transfer:before {
  content: "\F0B4";
}

.mdi-blur:before {
  content: "\F0B5";
}

.mdi-blur-linear:before {
  content: "\F0B6";
}

.mdi-blur-off:before {
  content: "\F0B7";
}

.mdi-blur-radial:before {
  content: "\F0B8";
}

.mdi-bone:before {
  content: "\F0B9";
}

.mdi-book:before {
  content: "\F0BA";
}

.mdi-book-minus:before {
  content: "\F5D9";
}

.mdi-book-multiple:before {
  content: "\F0BB";
}

.mdi-book-multiple-variant:before {
  content: "\F0BC";
}

.mdi-book-open:before {
  content: "\F0BD";
}

.mdi-book-open-page-variant:before {
  content: "\F5DA";
}

.mdi-book-open-variant:before {
  content: "\F0BE";
}

.mdi-book-plus:before {
  content: "\F5DB";
}

.mdi-book-variant:before {
  content: "\F0BF";
}

.mdi-bookmark:before {
  content: "\F0C0";
}

.mdi-bookmark-check:before {
  content: "\F0C1";
}

.mdi-bookmark-music:before {
  content: "\F0C2";
}

.mdi-bookmark-outline:before {
  content: "\F0C3";
}

.mdi-bookmark-plus:before {
  content: "\F0C5";
}

.mdi-bookmark-plus-outline:before {
  content: "\F0C4";
}

.mdi-bookmark-remove:before {
  content: "\F0C6";
}

.mdi-boombox:before {
  content: "\F5DC";
}

.mdi-border-all:before {
  content: "\F0C7";
}

.mdi-border-bottom:before {
  content: "\F0C8";
}

.mdi-border-color:before {
  content: "\F0C9";
}

.mdi-border-horizontal:before {
  content: "\F0CA";
}

.mdi-border-inside:before {
  content: "\F0CB";
}

.mdi-border-left:before {
  content: "\F0CC";
}

.mdi-border-none:before {
  content: "\F0CD";
}

.mdi-border-outside:before {
  content: "\F0CE";
}

.mdi-border-right:before {
  content: "\F0CF";
}

.mdi-border-style:before {
  content: "\F0D0";
}

.mdi-border-top:before {
  content: "\F0D1";
}

.mdi-border-vertical:before {
  content: "\F0D2";
}

.mdi-bowl:before {
  content: "\F617";
}

.mdi-bowling:before {
  content: "\F0D3";
}

.mdi-box:before {
  content: "\F0D4";
}

.mdi-box-cutter:before {
  content: "\F0D5";
}

.mdi-box-shadow:before {
  content: "\F637";
}

.mdi-bridge:before {
  content: "\F618";
}

.mdi-briefcase:before {
  content: "\F0D6";
}

.mdi-briefcase-check:before {
  content: "\F0D7";
}

.mdi-briefcase-download:before {
  content: "\F0D8";
}

.mdi-briefcase-upload:before {
  content: "\F0D9";
}

.mdi-brightness-1:before {
  content: "\F0DA";
}

.mdi-brightness-2:before {
  content: "\F0DB";
}

.mdi-brightness-3:before {
  content: "\F0DC";
}

.mdi-brightness-4:before {
  content: "\F0DD";
}

.mdi-brightness-5:before {
  content: "\F0DE";
}

.mdi-brightness-6:before {
  content: "\F0DF";
}

.mdi-brightness-7:before {
  content: "\F0E0";
}

.mdi-brightness-auto:before {
  content: "\F0E1";
}

.mdi-broom:before {
  content: "\F0E2";
}

.mdi-brush:before {
  content: "\F0E3";
}

.mdi-buffer:before {
  content: "\F619";
}

.mdi-bug:before {
  content: "\F0E4";
}

.mdi-bulletin-board:before {
  content: "\F0E5";
}

.mdi-bullhorn:before {
  content: "\F0E6";
}

.mdi-bullseye:before {
  content: "\F5DD";
}

.mdi-burst-mode:before {
  content: "\F5DE";
}

.mdi-bus:before {
  content: "\F0E7";
}

.mdi-cached:before {
  content: "\F0E8";
}

.mdi-cake:before {
  content: "\F0E9";
}

.mdi-cake-layered:before {
  content: "\F0EA";
}

.mdi-cake-variant:before {
  content: "\F0EB";
}

.mdi-calculator:before {
  content: "\F0EC";
}

.mdi-calendar:before {
  content: "\F0ED";
}

.mdi-calendar-blank:before {
  content: "\F0EE";
}

.mdi-calendar-check:before {
  content: "\F0EF";
}

.mdi-calendar-clock:before {
  content: "\F0F0";
}

.mdi-calendar-multiple:before {
  content: "\F0F1";
}

.mdi-calendar-multiple-check:before {
  content: "\F0F2";
}

.mdi-calendar-plus:before {
  content: "\F0F3";
}

.mdi-calendar-remove:before {
  content: "\F0F4";
}

.mdi-calendar-text:before {
  content: "\F0F5";
}

.mdi-calendar-today:before {
  content: "\F0F6";
}

.mdi-call-made:before {
  content: "\F0F7";
}

.mdi-call-merge:before {
  content: "\F0F8";
}

.mdi-call-missed:before {
  content: "\F0F9";
}

.mdi-call-received:before {
  content: "\F0FA";
}

.mdi-call-split:before {
  content: "\F0FB";
}

.mdi-camcorder:before {
  content: "\F0FC";
}

.mdi-camcorder-box:before {
  content: "\F0FD";
}

.mdi-camcorder-box-off:before {
  content: "\F0FE";
}

.mdi-camcorder-off:before {
  content: "\F0FF";
}

.mdi-camera:before {
  content: "\F100";
}

.mdi-camera-enhance:before {
  content: "\F101";
}

.mdi-camera-front:before {
  content: "\F102";
}

.mdi-camera-front-variant:before {
  content: "\F103";
}

.mdi-camera-iris:before {
  content: "\F104";
}

.mdi-camera-off:before {
  content: "\F5DF";
}

.mdi-camera-party-mode:before {
  content: "\F105";
}

.mdi-camera-rear:before {
  content: "\F106";
}

.mdi-camera-rear-variant:before {
  content: "\F107";
}

.mdi-camera-switch:before {
  content: "\F108";
}

.mdi-camera-timer:before {
  content: "\F109";
}

.mdi-candle:before {
  content: "\F5E2";
}

.mdi-candycane:before {
  content: "\F10A";
}

.mdi-car:before {
  content: "\F10B";
}

.mdi-car-battery:before {
  content: "\F10C";
}

.mdi-car-connected:before {
  content: "\F10D";
}

.mdi-car-wash:before {
  content: "\F10E";
}

.mdi-cards:before {
  content: "\F638";
}

.mdi-cards-outline:before {
  content: "\F639";
}

.mdi-cards-playing-outline:before {
  content: "\F63A";
}

.mdi-carrot:before {
  content: "\F10F";
}

.mdi-cart:before {
  content: "\F110";
}

.mdi-cart-off:before {
  content: "\F66B";
}

.mdi-cart-outline:before {
  content: "\F111";
}

.mdi-cart-plus:before {
  content: "\F112";
}

.mdi-case-sensitive-alt:before {
  content: "\F113";
}

.mdi-cash:before {
  content: "\F114";
}

.mdi-cash-100:before {
  content: "\F115";
}

.mdi-cash-multiple:before {
  content: "\F116";
}

.mdi-cash-usd:before {
  content: "\F117";
}

.mdi-cast:before {
  content: "\F118";
}

.mdi-cast-connected:before {
  content: "\F119";
}

.mdi-castle:before {
  content: "\F11A";
}

.mdi-cat:before {
  content: "\F11B";
}

.mdi-cellphone:before {
  content: "\F11C";
}

.mdi-cellphone-android:before {
  content: "\F11D";
}

.mdi-cellphone-basic:before {
  content: "\F11E";
}

.mdi-cellphone-dock:before {
  content: "\F11F";
}

.mdi-cellphone-iphone:before {
  content: "\F120";
}

.mdi-cellphone-link:before {
  content: "\F121";
}

.mdi-cellphone-link-off:before {
  content: "\F122";
}

.mdi-cellphone-settings:before {
  content: "\F123";
}

.mdi-certificate:before {
  content: "\F124";
}

.mdi-chair-school:before {
  content: "\F125";
}

.mdi-chart-arc:before {
  content: "\F126";
}

.mdi-chart-areaspline:before {
  content: "\F127";
}

.mdi-chart-bar:before {
  content: "\F128";
}

.mdi-chart-bubble:before {
  content: "\F5E3";
}

.mdi-chart-gantt:before {
  content: "\F66C";
}

.mdi-chart-histogram:before {
  content: "\F129";
}

.mdi-chart-line:before {
  content: "\F12A";
}

.mdi-chart-pie:before {
  content: "\F12B";
}

.mdi-chart-scatterplot-hexbin:before {
  content: "\F66D";
}

.mdi-chart-timeline:before {
  content: "\F66E";
}

.mdi-check:before {
  content: "\F12C";
}

.mdi-check-all:before {
  content: "\F12D";
}

.mdi-check-circle:before {
  content: "\F5E0";
}

.mdi-check-circle-outline:before {
  content: "\F5E1";
}

.mdi-checkbox-blank:before {
  content: "\F12E";
}

.mdi-checkbox-blank-circle:before {
  content: "\F12F";
}

.mdi-checkbox-blank-circle-outline:before {
  content: "\F130";
}

.mdi-checkbox-blank-outline:before {
  content: "\F131";
}

.mdi-checkbox-marked:before {
  content: "\F132";
}

.mdi-checkbox-marked-circle:before {
  content: "\F133";
}

.mdi-checkbox-marked-circle-outline:before {
  content: "\F134";
}

.mdi-checkbox-marked-outline:before {
  content: "\F135";
}

.mdi-checkbox-multiple-blank:before {
  content: "\F136";
}

.mdi-checkbox-multiple-blank-circle:before {
  content: "\F63B";
}

.mdi-checkbox-multiple-blank-circle-outline:before {
  content: "\F63C";
}

.mdi-checkbox-multiple-blank-outline:before {
  content: "\F137";
}

.mdi-checkbox-multiple-marked:before {
  content: "\F138";
}

.mdi-checkbox-multiple-marked-circle:before {
  content: "\F63D";
}

.mdi-checkbox-multiple-marked-circle-outline:before {
  content: "\F63E";
}

.mdi-checkbox-multiple-marked-outline:before {
  content: "\F139";
}

.mdi-checkerboard:before {
  content: "\F13A";
}

.mdi-chemical-weapon:before {
  content: "\F13B";
}

.mdi-chevron-double-down:before {
  content: "\F13C";
}

.mdi-chevron-double-left:before {
  content: "\F13D";
}

.mdi-chevron-double-right:before {
  content: "\F13E";
}

.mdi-chevron-double-up:before {
  content: "\F13F";
}

.mdi-chevron-down:before {
  content: "\F140";
}

.mdi-chevron-left:before {
  content: "\F141";
}

.mdi-chevron-right:before {
  content: "\F142";
}

.mdi-chevron-up:before {
  content: "\F143";
}

.mdi-chip:before {
  content: "\F61A";
}

.mdi-church:before {
  content: "\F144";
}

.mdi-cisco-webex:before {
  content: "\F145";
}

.mdi-city:before {
  content: "\F146";
}

.mdi-clipboard:before {
  content: "\F147";
}

.mdi-clipboard-account:before {
  content: "\F148";
}

.mdi-clipboard-alert:before {
  content: "\F149";
}

.mdi-clipboard-arrow-down:before {
  content: "\F14A";
}

.mdi-clipboard-arrow-left:before {
  content: "\F14B";
}

.mdi-clipboard-check:before {
  content: "\F14C";
}

.mdi-clipboard-outline:before {
  content: "\F14D";
}

.mdi-clipboard-text:before {
  content: "\F14E";
}

.mdi-clippy:before {
  content: "\F14F";
}

.mdi-clock:before {
  content: "\F150";
}

.mdi-clock-alert:before {
  content: "\F5CE";
}

.mdi-clock-end:before {
  content: "\F151";
}

.mdi-clock-fast:before {
  content: "\F152";
}

.mdi-clock-in:before {
  content: "\F153";
}

.mdi-clock-out:before {
  content: "\F154";
}

.mdi-clock-start:before {
  content: "\F155";
}

.mdi-close:before {
  content: "\F156";
}

.mdi-close-box:before {
  content: "\F157";
}

.mdi-close-box-outline:before {
  content: "\F158";
}

.mdi-close-circle:before {
  content: "\F159";
}

.mdi-close-circle-outline:before {
  content: "\F15A";
}

.mdi-close-network:before {
  content: "\F15B";
}

.mdi-close-octagon:before {
  content: "\F15C";
}

.mdi-close-octagon-outline:before {
  content: "\F15D";
}

.mdi-closed-caption:before {
  content: "\F15E";
}

.mdi-cloud:before {
  content: "\F15F";
}

.mdi-cloud-check:before {
  content: "\F160";
}

.mdi-cloud-circle:before {
  content: "\F161";
}

.mdi-cloud-download:before {
  content: "\F162";
}

.mdi-cloud-outline:before {
  content: "\F163";
}

.mdi-cloud-outline-off:before {
  content: "\F164";
}

.mdi-cloud-print:before {
  content: "\F165";
}

.mdi-cloud-print-outline:before {
  content: "\F166";
}

.mdi-cloud-sync:before {
  content: "\F63F";
}

.mdi-cloud-upload:before {
  content: "\F167";
}

.mdi-code-array:before {
  content: "\F168";
}

.mdi-code-braces:before {
  content: "\F169";
}

.mdi-code-brackets:before {
  content: "\F16A";
}

.mdi-code-equal:before {
  content: "\F16B";
}

.mdi-code-greater-than:before {
  content: "\F16C";
}

.mdi-code-greater-than-or-equal:before {
  content: "\F16D";
}

.mdi-code-less-than:before {
  content: "\F16E";
}

.mdi-code-less-than-or-equal:before {
  content: "\F16F";
}

.mdi-code-not-equal:before {
  content: "\F170";
}

.mdi-code-not-equal-variant:before {
  content: "\F171";
}

.mdi-code-parentheses:before {
  content: "\F172";
}

.mdi-code-string:before {
  content: "\F173";
}

.mdi-code-tags:before {
  content: "\F174";
}

.mdi-codepen:before {
  content: "\F175";
}

.mdi-coffee:before {
  content: "\F176";
}

.mdi-coffee-to-go:before {
  content: "\F177";
}

.mdi-coin:before {
  content: "\F178";
}

.mdi-collage:before {
  content: "\F640";
}

.mdi-color-helper:before {
  content: "\F179";
}

.mdi-comment:before {
  content: "\F17A";
}

.mdi-comment-account:before {
  content: "\F17B";
}

.mdi-comment-account-outline:before {
  content: "\F17C";
}

.mdi-comment-alert:before {
  content: "\F17D";
}

.mdi-comment-alert-outline:before {
  content: "\F17E";
}

.mdi-comment-check:before {
  content: "\F17F";
}

.mdi-comment-check-outline:before {
  content: "\F180";
}

.mdi-comment-multiple-outline:before {
  content: "\F181";
}

.mdi-comment-outline:before {
  content: "\F182";
}

.mdi-comment-plus-outline:before {
  content: "\F183";
}

.mdi-comment-processing:before {
  content: "\F184";
}

.mdi-comment-processing-outline:before {
  content: "\F185";
}

.mdi-comment-question-outline:before {
  content: "\F186";
}

.mdi-comment-remove-outline:before {
  content: "\F187";
}

.mdi-comment-text:before {
  content: "\F188";
}

.mdi-comment-text-outline:before {
  content: "\F189";
}

.mdi-compare:before {
  content: "\F18A";
}

.mdi-compass:before {
  content: "\F18B";
}

.mdi-compass-outline:before {
  content: "\F18C";
}

.mdi-console:before {
  content: "\F18D";
}

.mdi-contact-mail:before {
  content: "\F18E";
}

.mdi-content-copy:before {
  content: "\F18F";
}

.mdi-content-cut:before {
  content: "\F190";
}

.mdi-content-duplicate:before {
  content: "\F191";
}

.mdi-content-paste:before {
  content: "\F192";
}

.mdi-content-save:before {
  content: "\F193";
}

.mdi-content-save-all:before {
  content: "\F194";
}

.mdi-content-save-settings:before {
  content: "\F61B";
}

.mdi-contrast:before {
  content: "\F195";
}

.mdi-contrast-box:before {
  content: "\F196";
}

.mdi-contrast-circle:before {
  content: "\F197";
}

.mdi-cookie:before {
  content: "\F198";
}

.mdi-copyright:before {
  content: "\F5E6";
}

.mdi-counter:before {
  content: "\F199";
}

.mdi-cow:before {
  content: "\F19A";
}

.mdi-credit-card:before {
  content: "\F19B";
}

.mdi-credit-card-multiple:before {
  content: "\F19C";
}

.mdi-credit-card-off:before {
  content: "\F5E4";
}

.mdi-credit-card-scan:before {
  content: "\F19D";
}

.mdi-crop:before {
  content: "\F19E";
}

.mdi-crop-free:before {
  content: "\F19F";
}

.mdi-crop-landscape:before {
  content: "\F1A0";
}

.mdi-crop-portrait:before {
  content: "\F1A1";
}

.mdi-crop-square:before {
  content: "\F1A2";
}

.mdi-crosshairs:before {
  content: "\F1A3";
}

.mdi-crosshairs-gps:before {
  content: "\F1A4";
}

.mdi-crown:before {
  content: "\F1A5";
}

.mdi-cube:before {
  content: "\F1A6";
}

.mdi-cube-outline:before {
  content: "\F1A7";
}

.mdi-cube-send:before {
  content: "\F1A8";
}

.mdi-cube-unfolded:before {
  content: "\F1A9";
}

.mdi-cup:before {
  content: "\F1AA";
}

.mdi-cup-off:before {
  content: "\F5E5";
}

.mdi-cup-water:before {
  content: "\F1AB";
}

.mdi-currency-btc:before {
  content: "\F1AC";
}

.mdi-currency-eur:before {
  content: "\F1AD";
}

.mdi-currency-gbp:before {
  content: "\F1AE";
}

.mdi-currency-inr:before {
  content: "\F1AF";
}

.mdi-currency-ngn:before {
  content: "\F1B0";
}

.mdi-currency-rub:before {
  content: "\F1B1";
}

.mdi-currency-try:before {
  content: "\F1B2";
}

.mdi-currency-usd:before {
  content: "\F1B3";
}

.mdi-cursor-default:before {
  content: "\F1B4";
}

.mdi-cursor-default-outline:before {
  content: "\F1B5";
}

.mdi-cursor-move:before {
  content: "\F1B6";
}

.mdi-cursor-pointer:before {
  content: "\F1B7";
}

.mdi-cursor-text:before {
  content: "\F5E7";
}

.mdi-database:before {
  content: "\F1B8";
}

.mdi-database-minus:before {
  content: "\F1B9";
}

.mdi-database-plus:before {
  content: "\F1BA";
}

.mdi-debug-step-into:before {
  content: "\F1BB";
}

.mdi-debug-step-out:before {
  content: "\F1BC";
}

.mdi-debug-step-over:before {
  content: "\F1BD";
}

.mdi-decimal-decrease:before {
  content: "\F1BE";
}

.mdi-decimal-increase:before {
  content: "\F1BF";
}

.mdi-delete:before {
  content: "\F1C0";
}

.mdi-delete-forever:before {
  content: "\F5E8";
}

.mdi-delete-sweep:before {
  content: "\F5E9";
}

.mdi-delete-variant:before {
  content: "\F1C1";
}

.mdi-delta:before {
  content: "\F1C2";
}

.mdi-deskphone:before {
  content: "\F1C3";
}

.mdi-desktop-mac:before {
  content: "\F1C4";
}

.mdi-desktop-tower:before {
  content: "\F1C5";
}

.mdi-details:before {
  content: "\F1C6";
}

.mdi-deviantart:before {
  content: "\F1C7";
}

.mdi-dialpad:before {
  content: "\F61C";
}

.mdi-diamond:before {
  content: "\F1C8";
}

.mdi-dice-1:before {
  content: "\F1CA";
}

.mdi-dice-2:before {
  content: "\F1CB";
}

.mdi-dice-3:before {
  content: "\F1CC";
}

.mdi-dice-4:before {
  content: "\F1CD";
}

.mdi-dice-5:before {
  content: "\F1CE";
}

.mdi-dice-6:before {
  content: "\F1CF";
}

.mdi-dice-d20:before {
  content: "\F5EA";
}

.mdi-dice-d4:before {
  content: "\F5EB";
}

.mdi-dice-d6:before {
  content: "\F5EC";
}

.mdi-dice-d8:before {
  content: "\F5ED";
}

.mdi-dictionary:before {
  content: "\F61D";
}

.mdi-directions:before {
  content: "\F1D0";
}

.mdi-directions-fork:before {
  content: "\F641";
}

.mdi-discord:before {
  content: "\F66F";
}

.mdi-disk:before {
  content: "\F5EE";
}

.mdi-disk-alert:before {
  content: "\F1D1";
}

.mdi-disqus:before {
  content: "\F1D2";
}

.mdi-disqus-outline:before {
  content: "\F1D3";
}

.mdi-division:before {
  content: "\F1D4";
}

.mdi-division-box:before {
  content: "\F1D5";
}

.mdi-dns:before {
  content: "\F1D6";
}

.mdi-domain:before {
  content: "\F1D7";
}

.mdi-dots-horizontal:before {
  content: "\F1D8";
}

.mdi-dots-vertical:before {
  content: "\F1D9";
}

.mdi-download:before {
  content: "\F1DA";
}

.mdi-drag:before {
  content: "\F1DB";
}

.mdi-drag-horizontal:before {
  content: "\F1DC";
}

.mdi-drag-vertical:before {
  content: "\F1DD";
}

.mdi-drawing:before {
  content: "\F1DE";
}

.mdi-drawing-box:before {
  content: "\F1DF";
}

.mdi-dribbble:before {
  content: "\F1E0";
}

.mdi-dribbble-box:before {
  content: "\F1E1";
}

.mdi-drone:before {
  content: "\F1E2";
}

.mdi-dropbox:before {
  content: "\F1E3";
}

.mdi-drupal:before {
  content: "\F1E4";
}

.mdi-duck:before {
  content: "\F1E5";
}

.mdi-dumbbell:before {
  content: "\F1E6";
}

.mdi-earth:before {
  content: "\F1E7";
}

.mdi-earth-off:before {
  content: "\F1E8";
}

.mdi-edge:before {
  content: "\F1E9";
}

.mdi-eject:before {
  content: "\F1EA";
}

.mdi-elevation-decline:before {
  content: "\F1EB";
}

.mdi-elevation-rise:before {
  content: "\F1EC";
}

.mdi-elevator:before {
  content: "\F1ED";
}

.mdi-email:before {
  content: "\F1EE";
}

.mdi-email-open:before {
  content: "\F1EF";
}

.mdi-email-open-outline:before {
  content: "\F5EF";
}

.mdi-email-outline:before {
  content: "\F1F0";
}

.mdi-email-secure:before {
  content: "\F1F1";
}

.mdi-email-variant:before {
  content: "\F5F0";
}

.mdi-emoticon:before {
  content: "\F1F2";
}

.mdi-emoticon-cool:before {
  content: "\F1F3";
}

.mdi-emoticon-devil:before {
  content: "\F1F4";
}

.mdi-emoticon-happy:before {
  content: "\F1F5";
}

.mdi-emoticon-neutral:before {
  content: "\F1F6";
}

.mdi-emoticon-poop:before {
  content: "\F1F7";
}

.mdi-emoticon-sad:before {
  content: "\F1F8";
}

.mdi-emoticon-tongue:before {
  content: "\F1F9";
}

.mdi-engine:before {
  content: "\F1FA";
}

.mdi-engine-outline:before {
  content: "\F1FB";
}

.mdi-equal:before {
  content: "\F1FC";
}

.mdi-equal-box:before {
  content: "\F1FD";
}

.mdi-eraser:before {
  content: "\F1FE";
}

.mdi-eraser-variant:before {
  content: "\F642";
}

.mdi-escalator:before {
  content: "\F1FF";
}

.mdi-ethernet:before {
  content: "\F200";
}

.mdi-ethernet-cable:before {
  content: "\F201";
}

.mdi-ethernet-cable-off:before {
  content: "\F202";
}

.mdi-etsy:before {
  content: "\F203";
}

.mdi-ev-station:before {
  content: "\F5F1";
}

.mdi-evernote:before {
  content: "\F204";
}

.mdi-exclamation:before {
  content: "\F205";
}

.mdi-exit-to-app:before {
  content: "\F206";
}

.mdi-export:before {
  content: "\F207";
}

.mdi-eye:before {
  content: "\F208";
}

.mdi-eye-off:before {
  content: "\F209";
}

.mdi-eyedropper:before {
  content: "\F20A";
}

.mdi-eyedropper-variant:before {
  content: "\F20B";
}

.mdi-face:before {
  content: "\F643";
}

.mdi-face-profile:before {
  content: "\F644";
}

.mdi-facebook:before {
  content: "\F20C";
}

.mdi-facebook-box:before {
  content: "\F20D";
}

.mdi-facebook-messenger:before {
  content: "\F20E";
}

.mdi-factory:before {
  content: "\F20F";
}

.mdi-fan:before {
  content: "\F210";
}

.mdi-fast-forward:before {
  content: "\F211";
}

.mdi-fax:before {
  content: "\F212";
}

.mdi-ferry:before {
  content: "\F213";
}

.mdi-file:before {
  content: "\F214";
}

.mdi-file-chart:before {
  content: "\F215";
}

.mdi-file-check:before {
  content: "\F216";
}

.mdi-file-cloud:before {
  content: "\F217";
}

.mdi-file-delimited:before {
  content: "\F218";
}

.mdi-file-document:before {
  content: "\F219";
}

.mdi-file-document-box:before {
  content: "\F21A";
}

.mdi-file-excel:before {
  content: "\F21B";
}

.mdi-file-excel-box:before {
  content: "\F21C";
}

.mdi-file-export:before {
  content: "\F21D";
}

.mdi-file-find:before {
  content: "\F21E";
}

.mdi-file-hidden:before {
  content: "\F613";
}

.mdi-file-image:before {
  content: "\F21F";
}

.mdi-file-import:before {
  content: "\F220";
}

.mdi-file-lock:before {
  content: "\F221";
}

.mdi-file-multiple:before {
  content: "\F222";
}

.mdi-file-music:before {
  content: "\F223";
}

.mdi-file-outline:before {
  content: "\F224";
}

.mdi-file-pdf:before {
  content: "\F225";
}

.mdi-file-pdf-box:before {
  content: "\F226";
}

.mdi-file-powerpoint:before {
  content: "\F227";
}

.mdi-file-powerpoint-box:before {
  content: "\F228";
}

.mdi-file-presentation-box:before {
  content: "\F229";
}

.mdi-file-restore:before {
  content: "\F670";
}

.mdi-file-send:before {
  content: "\F22A";
}

.mdi-file-tree:before {
  content: "\F645";
}

.mdi-file-video:before {
  content: "\F22B";
}

.mdi-file-word:before {
  content: "\F22C";
}

.mdi-file-word-box:before {
  content: "\F22D";
}

.mdi-file-xml:before {
  content: "\F22E";
}

.mdi-film:before {
  content: "\F22F";
}

.mdi-filmstrip:before {
  content: "\F230";
}

.mdi-filmstrip-off:before {
  content: "\F231";
}

.mdi-filter:before {
  content: "\F232";
}

.mdi-filter-outline:before {
  content: "\F233";
}

.mdi-filter-remove:before {
  content: "\F234";
}

.mdi-filter-remove-outline:before {
  content: "\F235";
}

.mdi-filter-variant:before {
  content: "\F236";
}

.mdi-fingerprint:before {
  content: "\F237";
}

.mdi-fire:before {
  content: "\F238";
}

.mdi-firefox:before {
  content: "\F239";
}

.mdi-fish:before {
  content: "\F23A";
}

.mdi-flag:before {
  content: "\F23B";
}

.mdi-flag-checkered:before {
  content: "\F23C";
}

.mdi-flag-outline:before {
  content: "\F23D";
}

.mdi-flag-outline-variant:before {
  content: "\F23E";
}

.mdi-flag-triangle:before {
  content: "\F23F";
}

.mdi-flag-variant:before {
  content: "\F240";
}

.mdi-flash:before {
  content: "\F241";
}

.mdi-flash-auto:before {
  content: "\F242";
}

.mdi-flash-off:before {
  content: "\F243";
}

.mdi-flashlight:before {
  content: "\F244";
}

.mdi-flashlight-off:before {
  content: "\F245";
}

.mdi-flask:before {
  content: "\F093";
}

.mdi-flask-empty:before {
  content: "\F094";
}

.mdi-flask-empty-outline:before {
  content: "\F095";
}

.mdi-flask-outline:before {
  content: "\F096";
}

.mdi-flattr:before {
  content: "\F246";
}

.mdi-flip-to-back:before {
  content: "\F247";
}

.mdi-flip-to-front:before {
  content: "\F248";
}

.mdi-floppy:before {
  content: "\F249";
}

.mdi-flower:before {
  content: "\F24A";
}

.mdi-folder:before {
  content: "\F24B";
}

.mdi-folder-account:before {
  content: "\F24C";
}

.mdi-folder-download:before {
  content: "\F24D";
}

.mdi-folder-google-drive:before {
  content: "\F24E";
}

.mdi-folder-image:before {
  content: "\F24F";
}

.mdi-folder-lock:before {
  content: "\F250";
}

.mdi-folder-lock-open:before {
  content: "\F251";
}

.mdi-folder-move:before {
  content: "\F252";
}

.mdi-folder-multiple:before {
  content: "\F253";
}

.mdi-folder-multiple-image:before {
  content: "\F254";
}

.mdi-folder-multiple-outline:before {
  content: "\F255";
}

.mdi-folder-outline:before {
  content: "\F256";
}

.mdi-folder-plus:before {
  content: "\F257";
}

.mdi-folder-remove:before {
  content: "\F258";
}

.mdi-folder-upload:before {
  content: "\F259";
}

.mdi-food:before {
  content: "\F25A";
}

.mdi-food-apple:before {
  content: "\F25B";
}

.mdi-food-fork-drink:before {
  content: "\F5F2";
}

.mdi-food-off:before {
  content: "\F5F3";
}

.mdi-food-variant:before {
  content: "\F25C";
}

.mdi-football:before {
  content: "\F25D";
}

.mdi-football-australian:before {
  content: "\F25E";
}

.mdi-football-helmet:before {
  content: "\F25F";
}

.mdi-format-align-center:before {
  content: "\F260";
}

.mdi-format-align-justify:before {
  content: "\F261";
}

.mdi-format-align-left:before {
  content: "\F262";
}

.mdi-format-align-right:before {
  content: "\F263";
}

.mdi-format-annotation-plus:before {
  content: "\F646";
}

.mdi-format-bold:before {
  content: "\F264";
}

.mdi-format-clear:before {
  content: "\F265";
}

.mdi-format-color-fill:before {
  content: "\F266";
}

.mdi-format-float-center:before {
  content: "\F267";
}

.mdi-format-float-left:before {
  content: "\F268";
}

.mdi-format-float-none:before {
  content: "\F269";
}

.mdi-format-float-right:before {
  content: "\F26A";
}

.mdi-format-header-1:before {
  content: "\F26B";
}

.mdi-format-header-2:before {
  content: "\F26C";
}

.mdi-format-header-3:before {
  content: "\F26D";
}

.mdi-format-header-4:before {
  content: "\F26E";
}

.mdi-format-header-5:before {
  content: "\F26F";
}

.mdi-format-header-6:before {
  content: "\F270";
}

.mdi-format-header-decrease:before {
  content: "\F271";
}

.mdi-format-header-equal:before {
  content: "\F272";
}

.mdi-format-header-increase:before {
  content: "\F273";
}

.mdi-format-header-pound:before {
  content: "\F274";
}

.mdi-format-horizontal-align-center:before {
  content: "\F61E";
}

.mdi-format-horizontal-align-left:before {
  content: "\F61F";
}

.mdi-format-horizontal-align-right:before {
  content: "\F620";
}

.mdi-format-indent-decrease:before {
  content: "\F275";
}

.mdi-format-indent-increase:before {
  content: "\F276";
}

.mdi-format-italic:before {
  content: "\F277";
}

.mdi-format-line-spacing:before {
  content: "\F278";
}

.mdi-format-line-style:before {
  content: "\F5C8";
}

.mdi-format-line-weight:before {
  content: "\F5C9";
}

.mdi-format-list-bulleted:before {
  content: "\F279";
}

.mdi-format-list-bulleted-type:before {
  content: "\F27A";
}

.mdi-format-list-numbers:before {
  content: "\F27B";
}

.mdi-format-paint:before {
  content: "\F27C";
}

.mdi-format-paragraph:before {
  content: "\F27D";
}

.mdi-format-quote:before {
  content: "\F27E";
}

.mdi-format-size:before {
  content: "\F27F";
}

.mdi-format-strikethrough:before {
  content: "\F280";
}

.mdi-format-strikethrough-variant:before {
  content: "\F281";
}

.mdi-format-subscript:before {
  content: "\F282";
}

.mdi-format-superscript:before {
  content: "\F283";
}

.mdi-format-text:before {
  content: "\F284";
}

.mdi-format-textdirection-l-to-r:before {
  content: "\F285";
}

.mdi-format-textdirection-r-to-l:before {
  content: "\F286";
}

.mdi-format-title:before {
  content: "\F5F4";
}

.mdi-format-underline:before {
  content: "\F287";
}

.mdi-format-vertical-align-bottom:before {
  content: "\F621";
}

.mdi-format-vertical-align-center:before {
  content: "\F622";
}

.mdi-format-vertical-align-top:before {
  content: "\F623";
}

.mdi-format-wrap-inline:before {
  content: "\F288";
}

.mdi-format-wrap-square:before {
  content: "\F289";
}

.mdi-format-wrap-tight:before {
  content: "\F28A";
}

.mdi-format-wrap-top-bottom:before {
  content: "\F28B";
}

.mdi-forum:before {
  content: "\F28C";
}

.mdi-forward:before {
  content: "\F28D";
}

.mdi-foursquare:before {
  content: "\F28E";
}

.mdi-fridge:before {
  content: "\F28F";
}

.mdi-fridge-filled:before {
  content: "\F290";
}

.mdi-fridge-filled-bottom:before {
  content: "\F291";
}

.mdi-fridge-filled-top:before {
  content: "\F292";
}

.mdi-fullscreen:before {
  content: "\F293";
}

.mdi-fullscreen-exit:before {
  content: "\F294";
}

.mdi-function:before {
  content: "\F295";
}

.mdi-gamepad:before {
  content: "\F296";
}

.mdi-gamepad-variant:before {
  content: "\F297";
}

.mdi-gas-cylinder:before {
  content: "\F647";
}

.mdi-gas-station:before {
  content: "\F298";
}

.mdi-gate:before {
  content: "\F299";
}

.mdi-gauge:before {
  content: "\F29A";
}

.mdi-gavel:before {
  content: "\F29B";
}

.mdi-gender-female:before {
  content: "\F29C";
}

.mdi-gender-male:before {
  content: "\F29D";
}

.mdi-gender-male-female:before {
  content: "\F29E";
}

.mdi-gender-transgender:before {
  content: "\F29F";
}

.mdi-ghost:before {
  content: "\F2A0";
}

.mdi-gift:before {
  content: "\F2A1";
}

.mdi-git:before {
  content: "\F2A2";
}

.mdi-github-box:before {
  content: "\F2A3";
}

.mdi-github-circle:before {
  content: "\F2A4";
}

.mdi-glass-flute:before {
  content: "\F2A5";
}

.mdi-glass-mug:before {
  content: "\F2A6";
}

.mdi-glass-stange:before {
  content: "\F2A7";
}

.mdi-glass-tulip:before {
  content: "\F2A8";
}

.mdi-glassdoor:before {
  content: "\F2A9";
}

.mdi-glasses:before {
  content: "\F2AA";
}

.mdi-gmail:before {
  content: "\F2AB";
}

.mdi-gnome:before {
  content: "\F2AC";
}

.mdi-google:before {
  content: "\F2AD";
}

.mdi-google-cardboard:before {
  content: "\F2AE";
}

.mdi-google-chrome:before {
  content: "\F2AF";
}

.mdi-google-circles:before {
  content: "\F2B0";
}

.mdi-google-circles-communities:before {
  content: "\F2B1";
}

.mdi-google-circles-extended:before {
  content: "\F2B2";
}

.mdi-google-circles-group:before {
  content: "\F2B3";
}

.mdi-google-controller:before {
  content: "\F2B4";
}

.mdi-google-controller-off:before {
  content: "\F2B5";
}

.mdi-google-drive:before {
  content: "\F2B6";
}

.mdi-google-earth:before {
  content: "\F2B7";
}

.mdi-google-glass:before {
  content: "\F2B8";
}

.mdi-google-maps:before {
  content: "\F5F5";
}

.mdi-google-nearby:before {
  content: "\F2B9";
}

.mdi-google-pages:before {
  content: "\F2BA";
}

.mdi-google-physical-web:before {
  content: "\F2BB";
}

.mdi-google-play:before {
  content: "\F2BC";
}

.mdi-google-plus:before {
  content: "\F2BD";
}

.mdi-google-plus-box:before {
  content: "\F2BE";
}

.mdi-google-translate:before {
  content: "\F2BF";
}

.mdi-google-wallet:before {
  content: "\F2C0";
}

.mdi-grease-pencil:before {
  content: "\F648";
}

.mdi-grid:before {
  content: "\F2C1";
}

.mdi-grid-off:before {
  content: "\F2C2";
}

.mdi-group:before {
  content: "\F2C3";
}

.mdi-guitar-electric:before {
  content: "\F2C4";
}

.mdi-guitar-pick:before {
  content: "\F2C5";
}

.mdi-guitar-pick-outline:before {
  content: "\F2C6";
}

.mdi-hackernews:before {
  content: "\F624";
}

.mdi-hand-pointing-right:before {
  content: "\F2C7";
}

.mdi-hanger:before {
  content: "\F2C8";
}

.mdi-hangouts:before {
  content: "\F2C9";
}

.mdi-harddisk:before {
  content: "\F2CA";
}

.mdi-headphones:before {
  content: "\F2CB";
}

.mdi-headphones-box:before {
  content: "\F2CC";
}

.mdi-headphones-settings:before {
  content: "\F2CD";
}

.mdi-headset:before {
  content: "\F2CE";
}

.mdi-headset-dock:before {
  content: "\F2CF";
}

.mdi-headset-off:before {
  content: "\F2D0";
}

.mdi-heart:before {
  content: "\F2D1";
}

.mdi-heart-box:before {
  content: "\F2D2";
}

.mdi-heart-box-outline:before {
  content: "\F2D3";
}

.mdi-heart-broken:before {
  content: "\F2D4";
}

.mdi-heart-outline:before {
  content: "\F2D5";
}

.mdi-heart-pulse:before {
  content: "\F5F6";
}

.mdi-help:before {
  content: "\F2D6";
}

.mdi-help-circle:before {
  content: "\F2D7";
}

.mdi-help-circle-outline:before {
  content: "\F625";
}

.mdi-hexagon:before {
  content: "\F2D8";
}

.mdi-hexagon-outline:before {
  content: "\F2D9";
}

.mdi-highway:before {
  content: "\F5F7";
}

.mdi-history:before {
  content: "\F2DA";
}

.mdi-hololens:before {
  content: "\F2DB";
}

.mdi-home:before {
  content: "\F2DC";
}

.mdi-home-map-marker:before {
  content: "\F5F8";
}

.mdi-home-modern:before {
  content: "\F2DD";
}

.mdi-home-variant:before {
  content: "\F2DE";
}

.mdi-hops:before {
  content: "\F2DF";
}

.mdi-hospital:before {
  content: "\F2E0";
}

.mdi-hospital-building:before {
  content: "\F2E1";
}

.mdi-hospital-marker:before {
  content: "\F2E2";
}

.mdi-hotel:before {
  content: "\F2E3";
}

.mdi-houzz:before {
  content: "\F2E4";
}

.mdi-houzz-box:before {
  content: "\F2E5";
}

.mdi-human:before {
  content: "\F2E6";
}

.mdi-human-child:before {
  content: "\F2E7";
}

.mdi-human-female:before {
  content: "\F649";
}

.mdi-human-greeting:before {
  content: "\F64A";
}

.mdi-human-handsdown:before {
  content: "\F64B";
}

.mdi-human-handsup:before {
  content: "\F64C";
}

.mdi-human-male:before {
  content: "\F64D";
}

.mdi-human-male-female:before {
  content: "\F2E8";
}

.mdi-human-pregnant:before {
  content: "\F5CF";
}

.mdi-image:before {
  content: "\F2E9";
}

.mdi-image-album:before {
  content: "\F2EA";
}

.mdi-image-area:before {
  content: "\F2EB";
}

.mdi-image-area-close:before {
  content: "\F2EC";
}

.mdi-image-broken:before {
  content: "\F2ED";
}

.mdi-image-broken-variant:before {
  content: "\F2EE";
}

.mdi-image-filter:before {
  content: "\F2EF";
}

.mdi-image-filter-black-white:before {
  content: "\F2F0";
}

.mdi-image-filter-center-focus:before {
  content: "\F2F1";
}

.mdi-image-filter-center-focus-weak:before {
  content: "\F2F2";
}

.mdi-image-filter-drama:before {
  content: "\F2F3";
}

.mdi-image-filter-frames:before {
  content: "\F2F4";
}

.mdi-image-filter-hdr:before {
  content: "\F2F5";
}

.mdi-image-filter-none:before {
  content: "\F2F6";
}

.mdi-image-filter-tilt-shift:before {
  content: "\F2F7";
}

.mdi-image-filter-vintage:before {
  content: "\F2F8";
}

.mdi-image-multiple:before {
  content: "\F2F9";
}

.mdi-import:before {
  content: "\F2FA";
}

.mdi-inbox:before {
  content: "\F2FB";
}

.mdi-incognito:before {
  content: "\F5F9";
}

.mdi-information:before {
  content: "\F2FC";
}

.mdi-information-outline:before {
  content: "\F2FD";
}

.mdi-information-variant:before {
  content: "\F64E";
}

.mdi-instagram:before {
  content: "\F2FE";
}

.mdi-instapaper:before {
  content: "\F2FF";
}

.mdi-internet-explorer:before {
  content: "\F300";
}

.mdi-invert-colors:before {
  content: "\F301";
}

.mdi-jeepney:before {
  content: "\F302";
}

.mdi-jira:before {
  content: "\F303";
}

.mdi-jsfiddle:before {
  content: "\F304";
}

.mdi-json:before {
  content: "\F626";
}

.mdi-keg:before {
  content: "\F305";
}

.mdi-kettle:before {
  content: "\F5FA";
}

.mdi-key:before {
  content: "\F306";
}

.mdi-key-change:before {
  content: "\F307";
}

.mdi-key-minus:before {
  content: "\F308";
}

.mdi-key-plus:before {
  content: "\F309";
}

.mdi-key-remove:before {
  content: "\F30A";
}

.mdi-key-variant:before {
  content: "\F30B";
}

.mdi-keyboard:before {
  content: "\F30C";
}

.mdi-keyboard-backspace:before {
  content: "\F30D";
}

.mdi-keyboard-caps:before {
  content: "\F30E";
}

.mdi-keyboard-close:before {
  content: "\F30F";
}

.mdi-keyboard-off:before {
  content: "\F310";
}

.mdi-keyboard-return:before {
  content: "\F311";
}

.mdi-keyboard-tab:before {
  content: "\F312";
}

.mdi-keyboard-variant:before {
  content: "\F313";
}

.mdi-kodi:before {
  content: "\F314";
}

.mdi-label:before {
  content: "\F315";
}

.mdi-label-outline:before {
  content: "\F316";
}

.mdi-lambda:before {
  content: "\F627";
}

.mdi-lan:before {
  content: "\F317";
}

.mdi-lan-connect:before {
  content: "\F318";
}

.mdi-lan-disconnect:before {
  content: "\F319";
}

.mdi-lan-pending:before {
  content: "\F31A";
}

.mdi-language-c:before {
  content: "\F671";
}

.mdi-language-cpp:before {
  content: "\F672";
}

.mdi-language-csharp:before {
  content: "\F31B";
}

.mdi-language-css3:before {
  content: "\F31C";
}

.mdi-language-html5:before {
  content: "\F31D";
}

.mdi-language-javascript:before {
  content: "\F31E";
}

.mdi-language-php:before {
  content: "\F31F";
}

.mdi-language-python:before {
  content: "\F320";
}

.mdi-language-python-text:before {
  content: "\F321";
}

.mdi-laptop:before {
  content: "\F322";
}

.mdi-laptop-chromebook:before {
  content: "\F323";
}

.mdi-laptop-mac:before {
  content: "\F324";
}

.mdi-laptop-windows:before {
  content: "\F325";
}

.mdi-lastfm:before {
  content: "\F326";
}

.mdi-launch:before {
  content: "\F327";
}

.mdi-layers:before {
  content: "\F328";
}

.mdi-layers-off:before {
  content: "\F329";
}

.mdi-lead-pencil:before {
  content: "\F64F";
}

.mdi-leaf:before {
  content: "\F32A";
}

.mdi-led-off:before {
  content: "\F32B";
}

.mdi-led-on:before {
  content: "\F32C";
}

.mdi-led-outline:before {
  content: "\F32D";
}

.mdi-led-variant-off:before {
  content: "\F32E";
}

.mdi-led-variant-on:before {
  content: "\F32F";
}

.mdi-led-variant-outline:before {
  content: "\F330";
}

.mdi-library:before {
  content: "\F331";
}

.mdi-library-books:before {
  content: "\F332";
}

.mdi-library-music:before {
  content: "\F333";
}

.mdi-library-plus:before {
  content: "\F334";
}

.mdi-lightbulb:before {
  content: "\F335";
}

.mdi-lightbulb-outline:before {
  content: "\F336";
}

.mdi-link:before {
  content: "\F337";
}

.mdi-link-off:before {
  content: "\F338";
}

.mdi-link-variant:before {
  content: "\F339";
}

.mdi-link-variant-off:before {
  content: "\F33A";
}

.mdi-linkedin:before {
  content: "\F33B";
}

.mdi-linkedin-box:before {
  content: "\F33C";
}

.mdi-linux:before {
  content: "\F33D";
}

.mdi-lock:before {
  content: "\F33E";
}

.mdi-lock-open:before {
  content: "\F33F";
}

.mdi-lock-open-outline:before {
  content: "\F340";
}

.mdi-lock-outline:before {
  content: "\F341";
}

.mdi-lock-plus:before {
  content: "\F5FB";
}

.mdi-login:before {
  content: "\F342";
}

.mdi-login-variant:before {
  content: "\F5FC";
}

.mdi-logout:before {
  content: "\F343";
}

.mdi-logout-variant:before {
  content: "\F5FD";
}

.mdi-looks:before {
  content: "\F344";
}

.mdi-loupe:before {
  content: "\F345";
}

.mdi-lumx:before {
  content: "\F346";
}

.mdi-magnet:before {
  content: "\F347";
}

.mdi-magnet-on:before {
  content: "\F348";
}

.mdi-magnify:before {
  content: "\F349";
}

.mdi-magnify-minus:before {
  content: "\F34A";
}

.mdi-magnify-plus:before {
  content: "\F34B";
}

.mdi-mail-ru:before {
  content: "\F34C";
}

.mdi-map:before {
  content: "\F34D";
}

.mdi-map-marker:before {
  content: "\F34E";
}

.mdi-map-marker-circle:before {
  content: "\F34F";
}

.mdi-map-marker-minus:before {
  content: "\F650";
}

.mdi-map-marker-multiple:before {
  content: "\F350";
}

.mdi-map-marker-off:before {
  content: "\F351";
}

.mdi-map-marker-plus:before {
  content: "\F651";
}

.mdi-map-marker-radius:before {
  content: "\F352";
}

.mdi-margin:before {
  content: "\F353";
}

.mdi-markdown:before {
  content: "\F354";
}

.mdi-marker:before {
  content: "\F652";
}

.mdi-marker-check:before {
  content: "\F355";
}

.mdi-martini:before {
  content: "\F356";
}

.mdi-material-ui:before {
  content: "\F357";
}

.mdi-math-compass:before {
  content: "\F358";
}

.mdi-matrix:before {
  content: "\F628";
}

.mdi-maxcdn:before {
  content: "\F359";
}

.mdi-medium:before {
  content: "\F35A";
}

.mdi-memory:before {
  content: "\F35B";
}

.mdi-menu:before {
  content: "\F35C";
}

.mdi-menu-down:before {
  content: "\F35D";
}

.mdi-menu-left:before {
  content: "\F35E";
}

.mdi-menu-right:before {
  content: "\F35F";
}

.mdi-menu-up:before {
  content: "\F360";
}

.mdi-message:before {
  content: "\F361";
}

.mdi-message-alert:before {
  content: "\F362";
}

.mdi-message-draw:before {
  content: "\F363";
}

.mdi-message-image:before {
  content: "\F364";
}

.mdi-message-outline:before {
  content: "\F365";
}

.mdi-message-plus:before {
  content: "\F653";
}

.mdi-message-processing:before {
  content: "\F366";
}

.mdi-message-reply:before {
  content: "\F367";
}

.mdi-message-reply-text:before {
  content: "\F368";
}

.mdi-message-text:before {
  content: "\F369";
}

.mdi-message-text-outline:before {
  content: "\F36A";
}

.mdi-message-video:before {
  content: "\F36B";
}

.mdi-meteor:before {
  content: "\F629";
}

.mdi-microphone:before {
  content: "\F36C";
}

.mdi-microphone-off:before {
  content: "\F36D";
}

.mdi-microphone-outline:before {
  content: "\F36E";
}

.mdi-microphone-settings:before {
  content: "\F36F";
}

.mdi-microphone-variant:before {
  content: "\F370";
}

.mdi-microphone-variant-off:before {
  content: "\F371";
}

.mdi-microscope:before {
  content: "\F654";
}

.mdi-microsoft:before {
  content: "\F372";
}

.mdi-minecraft:before {
  content: "\F373";
}

.mdi-minus:before {
  content: "\F374";
}

.mdi-minus-box:before {
  content: "\F375";
}

.mdi-minus-circle:before {
  content: "\F376";
}

.mdi-minus-circle-outline:before {
  content: "\F377";
}

.mdi-minus-network:before {
  content: "\F378";
}

.mdi-mixcloud:before {
  content: "\F62A";
}

.mdi-monitor:before {
  content: "\F379";
}

.mdi-monitor-multiple:before {
  content: "\F37A";
}

.mdi-more:before {
  content: "\F37B";
}

.mdi-motorbike:before {
  content: "\F37C";
}

.mdi-mouse:before {
  content: "\F37D";
}

.mdi-mouse-off:before {
  content: "\F37E";
}

.mdi-mouse-variant:before {
  content: "\F37F";
}

.mdi-mouse-variant-off:before {
  content: "\F380";
}

.mdi-move-resize:before {
  content: "\F655";
}

.mdi-move-resize-variant:before {
  content: "\F656";
}

.mdi-movie:before {
  content: "\F381";
}

.mdi-multiplication:before {
  content: "\F382";
}

.mdi-multiplication-box:before {
  content: "\F383";
}

.mdi-music-box:before {
  content: "\F384";
}

.mdi-music-box-outline:before {
  content: "\F385";
}

.mdi-music-circle:before {
  content: "\F386";
}

.mdi-music-note:before {
  content: "\F387";
}

.mdi-music-note-bluetooth:before {
  content: "\F5FE";
}

.mdi-music-note-bluetooth-off:before {
  content: "\F5FF";
}

.mdi-music-note-eighth:before {
  content: "\F388";
}

.mdi-music-note-half:before {
  content: "\F389";
}

.mdi-music-note-off:before {
  content: "\F38A";
}

.mdi-music-note-quarter:before {
  content: "\F38B";
}

.mdi-music-note-sixteenth:before {
  content: "\F38C";
}

.mdi-music-note-whole:before {
  content: "\F38D";
}

.mdi-nature:before {
  content: "\F38E";
}

.mdi-nature-people:before {
  content: "\F38F";
}

.mdi-navigation:before {
  content: "\F390";
}

.mdi-near-me:before {
  content: "\F5CD";
}

.mdi-needle:before {
  content: "\F391";
}

.mdi-nest-protect:before {
  content: "\F392";
}

.mdi-nest-thermostat:before {
  content: "\F393";
}

.mdi-new-box:before {
  content: "\F394";
}

.mdi-newspaper:before {
  content: "\F395";
}

.mdi-nfc:before {
  content: "\F396";
}

.mdi-nfc-tap:before {
  content: "\F397";
}

.mdi-nfc-variant:before {
  content: "\F398";
}

.mdi-nodejs:before {
  content: "\F399";
}

.mdi-note:before {
  content: "\F39A";
}

.mdi-note-outline:before {
  content: "\F39B";
}

.mdi-note-plus:before {
  content: "\F39C";
}

.mdi-note-plus-outline:before {
  content: "\F39D";
}

.mdi-note-text:before {
  content: "\F39E";
}

.mdi-notification-clear-all:before {
  content: "\F39F";
}

.mdi-numeric:before {
  content: "\F3A0";
}

.mdi-numeric-0-box:before {
  content: "\F3A1";
}

.mdi-numeric-0-box-multiple-outline:before {
  content: "\F3A2";
}

.mdi-numeric-0-box-outline:before {
  content: "\F3A3";
}

.mdi-numeric-1-box:before {
  content: "\F3A4";
}

.mdi-numeric-1-box-multiple-outline:before {
  content: "\F3A5";
}

.mdi-numeric-1-box-outline:before {
  content: "\F3A6";
}

.mdi-numeric-2-box:before {
  content: "\F3A7";
}

.mdi-numeric-2-box-multiple-outline:before {
  content: "\F3A8";
}

.mdi-numeric-2-box-outline:before {
  content: "\F3A9";
}

.mdi-numeric-3-box:before {
  content: "\F3AA";
}

.mdi-numeric-3-box-multiple-outline:before {
  content: "\F3AB";
}

.mdi-numeric-3-box-outline:before {
  content: "\F3AC";
}

.mdi-numeric-4-box:before {
  content: "\F3AD";
}

.mdi-numeric-4-box-multiple-outline:before {
  content: "\F3AE";
}

.mdi-numeric-4-box-outline:before {
  content: "\F3AF";
}

.mdi-numeric-5-box:before {
  content: "\F3B0";
}

.mdi-numeric-5-box-multiple-outline:before {
  content: "\F3B1";
}

.mdi-numeric-5-box-outline:before {
  content: "\F3B2";
}

.mdi-numeric-6-box:before {
  content: "\F3B3";
}

.mdi-numeric-6-box-multiple-outline:before {
  content: "\F3B4";
}

.mdi-numeric-6-box-outline:before {
  content: "\F3B5";
}

.mdi-numeric-7-box:before {
  content: "\F3B6";
}

.mdi-numeric-7-box-multiple-outline:before {
  content: "\F3B7";
}

.mdi-numeric-7-box-outline:before {
  content: "\F3B8";
}

.mdi-numeric-8-box:before {
  content: "\F3B9";
}

.mdi-numeric-8-box-multiple-outline:before {
  content: "\F3BA";
}

.mdi-numeric-8-box-outline:before {
  content: "\F3BB";
}

.mdi-numeric-9-box:before {
  content: "\F3BC";
}

.mdi-numeric-9-box-multiple-outline:before {
  content: "\F3BD";
}

.mdi-numeric-9-box-outline:before {
  content: "\F3BE";
}

.mdi-numeric-9-plus-box:before {
  content: "\F3BF";
}

.mdi-numeric-9-plus-box-multiple-outline:before {
  content: "\F3C0";
}

.mdi-numeric-9-plus-box-outline:before {
  content: "\F3C1";
}

.mdi-nutrition:before {
  content: "\F3C2";
}

.mdi-octagon:before {
  content: "\F3C3";
}

.mdi-octagon-outline:before {
  content: "\F3C4";
}

.mdi-odnoklassniki:before {
  content: "\F3C5";
}

.mdi-office:before {
  content: "\F3C6";
}

.mdi-oil:before {
  content: "\F3C7";
}

.mdi-oil-temperature:before {
  content: "\F3C8";
}

.mdi-omega:before {
  content: "\F3C9";
}

.mdi-onedrive:before {
  content: "\F3CA";
}

.mdi-opacity:before {
  content: "\F5CC";
}

.mdi-open-in-app:before {
  content: "\F3CB";
}

.mdi-open-in-new:before {
  content: "\F3CC";
}

.mdi-openid:before {
  content: "\F3CD";
}

.mdi-opera:before {
  content: "\F3CE";
}

.mdi-ornament:before {
  content: "\F3CF";
}

.mdi-ornament-variant:before {
  content: "\F3D0";
}

.mdi-outbox:before {
  content: "\F3D1";
}

.mdi-owl:before {
  content: "\F3D2";
}

.mdi-package:before {
  content: "\F3D3";
}

.mdi-package-down:before {
  content: "\F3D4";
}

.mdi-package-up:before {
  content: "\F3D5";
}

.mdi-package-variant:before {
  content: "\F3D6";
}

.mdi-package-variant-closed:before {
  content: "\F3D7";
}

.mdi-page-first:before {
  content: "\F600";
}

.mdi-page-last:before {
  content: "\F601";
}

.mdi-palette:before {
  content: "\F3D8";
}

.mdi-palette-advanced:before {
  content: "\F3D9";
}

.mdi-panda:before {
  content: "\F3DA";
}

.mdi-pandora:before {
  content: "\F3DB";
}

.mdi-panorama:before {
  content: "\F3DC";
}

.mdi-panorama-fisheye:before {
  content: "\F3DD";
}

.mdi-panorama-horizontal:before {
  content: "\F3DE";
}

.mdi-panorama-vertical:before {
  content: "\F3DF";
}

.mdi-panorama-wide-angle:before {
  content: "\F3E0";
}

.mdi-paper-cut-vertical:before {
  content: "\F3E1";
}

.mdi-paperclip:before {
  content: "\F3E2";
}

.mdi-parking:before {
  content: "\F3E3";
}

.mdi-pause:before {
  content: "\F3E4";
}

.mdi-pause-circle:before {
  content: "\F3E5";
}

.mdi-pause-circle-outline:before {
  content: "\F3E6";
}

.mdi-pause-octagon:before {
  content: "\F3E7";
}

.mdi-pause-octagon-outline:before {
  content: "\F3E8";
}

.mdi-paw:before {
  content: "\F3E9";
}

.mdi-paw-off:before {
  content: "\F657";
}

.mdi-pen:before {
  content: "\F3EA";
}

.mdi-pencil:before {
  content: "\F3EB";
}

.mdi-pencil-box:before {
  content: "\F3EC";
}

.mdi-pencil-box-outline:before {
  content: "\F3ED";
}

.mdi-pencil-lock:before {
  content: "\F3EE";
}

.mdi-pencil-off:before {
  content: "\F3EF";
}

.mdi-percent:before {
  content: "\F3F0";
}

.mdi-pharmacy:before {
  content: "\F3F1";
}

.mdi-phone:before {
  content: "\F3F2";
}

.mdi-phone-bluetooth:before {
  content: "\F3F3";
}

.mdi-phone-classic:before {
  content: "\F602";
}

.mdi-phone-forward:before {
  content: "\F3F4";
}

.mdi-phone-hangup:before {
  content: "\F3F5";
}

.mdi-phone-in-talk:before {
  content: "\F3F6";
}

.mdi-phone-incoming:before {
  content: "\F3F7";
}

.mdi-phone-locked:before {
  content: "\F3F8";
}

.mdi-phone-log:before {
  content: "\F3F9";
}

.mdi-phone-minus:before {
  content: "\F658";
}

.mdi-phone-missed:before {
  content: "\F3FA";
}

.mdi-phone-outgoing:before {
  content: "\F3FB";
}

.mdi-phone-paused:before {
  content: "\F3FC";
}

.mdi-phone-plus:before {
  content: "\F659";
}

.mdi-phone-settings:before {
  content: "\F3FD";
}

.mdi-phone-voip:before {
  content: "\F3FE";
}

.mdi-pi:before {
  content: "\F3FF";
}

.mdi-pi-box:before {
  content: "\F400";
}

.mdi-pig:before {
  content: "\F401";
}

.mdi-pill:before {
  content: "\F402";
}

.mdi-pin:before {
  content: "\F403";
}

.mdi-pin-off:before {
  content: "\F404";
}

.mdi-pine-tree:before {
  content: "\F405";
}

.mdi-pine-tree-box:before {
  content: "\F406";
}

.mdi-pinterest:before {
  content: "\F407";
}

.mdi-pinterest-box:before {
  content: "\F408";
}

.mdi-pizza:before {
  content: "\F409";
}

.mdi-play:before {
  content: "\F40A";
}

.mdi-play-box-outline:before {
  content: "\F40B";
}

.mdi-play-circle:before {
  content: "\F40C";
}

.mdi-play-circle-outline:before {
  content: "\F40D";
}

.mdi-play-pause:before {
  content: "\F40E";
}

.mdi-play-protected-content:before {
  content: "\F40F";
}

.mdi-playlist-check:before {
  content: "\F5C7";
}

.mdi-playlist-minus:before {
  content: "\F410";
}

.mdi-playlist-play:before {
  content: "\F411";
}

.mdi-playlist-plus:before {
  content: "\F412";
}

.mdi-playlist-remove:before {
  content: "\F413";
}

.mdi-playstation:before {
  content: "\F414";
}

.mdi-plus:before {
  content: "\F415";
}

.mdi-plus-box:before {
  content: "\F416";
}

.mdi-plus-circle:before {
  content: "\F417";
}

.mdi-plus-circle-multiple-outline:before {
  content: "\F418";
}

.mdi-plus-circle-outline:before {
  content: "\F419";
}

.mdi-plus-network:before {
  content: "\F41A";
}

.mdi-plus-one:before {
  content: "\F41B";
}

.mdi-pocket:before {
  content: "\F41C";
}

.mdi-pokeball:before {
  content: "\F41D";
}

.mdi-polaroid:before {
  content: "\F41E";
}

.mdi-poll:before {
  content: "\F41F";
}

.mdi-poll-box:before {
  content: "\F420";
}

.mdi-polymer:before {
  content: "\F421";
}

.mdi-pool:before {
  content: "\F606";
}

.mdi-popcorn:before {
  content: "\F422";
}

.mdi-pot:before {
  content: "\F65A";
}

.mdi-pot-mix:before {
  content: "\F65B";
}

.mdi-pound:before {
  content: "\F423";
}

.mdi-pound-box:before {
  content: "\F424";
}

.mdi-power:before {
  content: "\F425";
}

.mdi-power-settings:before {
  content: "\F426";
}

.mdi-power-socket:before {
  content: "\F427";
}

.mdi-presentation:before {
  content: "\F428";
}

.mdi-presentation-play:before {
  content: "\F429";
}

.mdi-printer:before {
  content: "\F42A";
}

.mdi-printer-3d:before {
  content: "\F42B";
}

.mdi-printer-alert:before {
  content: "\F42C";
}

.mdi-priority-high:before {
  content: "\F603";
}

.mdi-priority-low:before {
  content: "\F604";
}

.mdi-professional-hexagon:before {
  content: "\F42D";
}

.mdi-projector:before {
  content: "\F42E";
}

.mdi-projector-screen:before {
  content: "\F42F";
}

.mdi-pulse:before {
  content: "\F430";
}

.mdi-puzzle:before {
  content: "\F431";
}

.mdi-qqchat:before {
  content: "\F605";
}

.mdi-qrcode:before {
  content: "\F432";
}

.mdi-qrcode-scan:before {
  content: "\F433";
}

.mdi-quadcopter:before {
  content: "\F434";
}

.mdi-quality-high:before {
  content: "\F435";
}

.mdi-quicktime:before {
  content: "\F436";
}

.mdi-radar:before {
  content: "\F437";
}

.mdi-radiator:before {
  content: "\F438";
}

.mdi-radio:before {
  content: "\F439";
}

.mdi-radio-handheld:before {
  content: "\F43A";
}

.mdi-radio-tower:before {
  content: "\F43B";
}

.mdi-radioactive:before {
  content: "\F43C";
}

.mdi-radiobox-blank:before {
  content: "\F43D";
}

.mdi-radiobox-marked:before {
  content: "\F43E";
}

.mdi-raspberrypi:before {
  content: "\F43F";
}

.mdi-ray-end:before {
  content: "\F440";
}

.mdi-ray-end-arrow:before {
  content: "\F441";
}

.mdi-ray-start:before {
  content: "\F442";
}

.mdi-ray-start-arrow:before {
  content: "\F443";
}

.mdi-ray-start-end:before {
  content: "\F444";
}

.mdi-ray-vertex:before {
  content: "\F445";
}

.mdi-rdio:before {
  content: "\F446";
}

.mdi-read:before {
  content: "\F447";
}

.mdi-readability:before {
  content: "\F448";
}

.mdi-receipt:before {
  content: "\F449";
}

.mdi-record:before {
  content: "\F44A";
}

.mdi-record-rec:before {
  content: "\F44B";
}

.mdi-recycle:before {
  content: "\F44C";
}

.mdi-reddit:before {
  content: "\F44D";
}

.mdi-redo:before {
  content: "\F44E";
}

.mdi-redo-variant:before {
  content: "\F44F";
}

.mdi-refresh:before {
  content: "\F450";
}

.mdi-regex:before {
  content: "\F451";
}

.mdi-relative-scale:before {
  content: "\F452";
}

.mdi-reload:before {
  content: "\F453";
}

.mdi-remote:before {
  content: "\F454";
}

.mdi-rename-box:before {
  content: "\F455";
}

.mdi-repeat:before {
  content: "\F456";
}

.mdi-repeat-off:before {
  content: "\F457";
}

.mdi-repeat-once:before {
  content: "\F458";
}

.mdi-replay:before {
  content: "\F459";
}

.mdi-reply:before {
  content: "\F45A";
}

.mdi-reply-all:before {
  content: "\F45B";
}

.mdi-reproduction:before {
  content: "\F45C";
}

.mdi-resize-bottom-right:before {
  content: "\F45D";
}

.mdi-responsive:before {
  content: "\F45E";
}

.mdi-rewind:before {
  content: "\F45F";
}

.mdi-ribbon:before {
  content: "\F460";
}

.mdi-road:before {
  content: "\F461";
}

.mdi-road-variant:before {
  content: "\F462";
}

.mdi-rocket:before {
  content: "\F463";
}

.mdi-rotate-3d:before {
  content: "\F464";
}

.mdi-rotate-left:before {
  content: "\F465";
}

.mdi-rotate-left-variant:before {
  content: "\F466";
}

.mdi-rotate-right:before {
  content: "\F467";
}

.mdi-rotate-right-variant:before {
  content: "\F468";
}

.mdi-rounded-corner:before {
  content: "\F607";
}

.mdi-router-wireless:before {
  content: "\F469";
}

.mdi-routes:before {
  content: "\F46A";
}

.mdi-rowing:before {
  content: "\F608";
}

.mdi-rss:before {
  content: "\F46B";
}

.mdi-rss-box:before {
  content: "\F46C";
}

.mdi-ruler:before {
  content: "\F46D";
}

.mdi-run:before {
  content: "\F46E";
}

.mdi-sale:before {
  content: "\F46F";
}

.mdi-satellite:before {
  content: "\F470";
}

.mdi-satellite-variant:before {
  content: "\F471";
}

.mdi-saxophone:before {
  content: "\F609";
}

.mdi-scale:before {
  content: "\F472";
}

.mdi-scale-balance:before {
  content: "\F5D1";
}

.mdi-scale-bathroom:before {
  content: "\F473";
}

.mdi-school:before {
  content: "\F474";
}

.mdi-screen-rotation:before {
  content: "\F475";
}

.mdi-screen-rotation-lock:before {
  content: "\F476";
}

.mdi-screwdriver:before {
  content: "\F477";
}

.mdi-script:before {
  content: "\F478";
}

.mdi-sd:before {
  content: "\F479";
}

.mdi-seal:before {
  content: "\F47A";
}

.mdi-seat-flat:before {
  content: "\F47B";
}

.mdi-seat-flat-angled:before {
  content: "\F47C";
}

.mdi-seat-individual-suite:before {
  content: "\F47D";
}

.mdi-seat-legroom-extra:before {
  content: "\F47E";
}

.mdi-seat-legroom-normal:before {
  content: "\F47F";
}

.mdi-seat-legroom-reduced:before {
  content: "\F480";
}

.mdi-seat-recline-extra:before {
  content: "\F481";
}

.mdi-seat-recline-normal:before {
  content: "\F482";
}

.mdi-security:before {
  content: "\F483";
}

.mdi-security-network:before {
  content: "\F484";
}

.mdi-select:before {
  content: "\F485";
}

.mdi-select-all:before {
  content: "\F486";
}

.mdi-select-inverse:before {
  content: "\F487";
}

.mdi-select-off:before {
  content: "\F488";
}

.mdi-selection:before {
  content: "\F489";
}

.mdi-send:before {
  content: "\F48A";
}

.mdi-serial-port:before {
  content: "\F65C";
}

.mdi-server:before {
  content: "\F48B";
}

.mdi-server-minus:before {
  content: "\F48C";
}

.mdi-server-network:before {
  content: "\F48D";
}

.mdi-server-network-off:before {
  content: "\F48E";
}

.mdi-server-off:before {
  content: "\F48F";
}

.mdi-server-plus:before {
  content: "\F490";
}

.mdi-server-remove:before {
  content: "\F491";
}

.mdi-server-security:before {
  content: "\F492";
}

.mdi-settings:before {
  content: "\F493";
}

.mdi-settings-box:before {
  content: "\F494";
}

.mdi-shape-circle-plus:before {
  content: "\F65D";
}

.mdi-shape-plus:before {
  content: "\F495";
}

.mdi-shape-polygon-plus:before {
  content: "\F65E";
}

.mdi-shape-rectangle-plus:before {
  content: "\F65F";
}

.mdi-shape-square-plus:before {
  content: "\F660";
}

.mdi-share:before {
  content: "\F496";
}

.mdi-share-variant:before {
  content: "\F497";
}

.mdi-shield:before {
  content: "\F498";
}

.mdi-shield-outline:before {
  content: "\F499";
}

.mdi-shopping:before {
  content: "\F49A";
}

.mdi-shopping-music:before {
  content: "\F49B";
}

.mdi-shredder:before {
  content: "\F49C";
}

.mdi-shuffle:before {
  content: "\F49D";
}

.mdi-shuffle-disabled:before {
  content: "\F49E";
}

.mdi-shuffle-variant:before {
  content: "\F49F";
}

.mdi-sigma:before {
  content: "\F4A0";
}

.mdi-sigma-lower:before {
  content: "\F62B";
}

.mdi-sign-caution:before {
  content: "\F4A1";
}

.mdi-signal:before {
  content: "\F4A2";
}

.mdi-signal-variant:before {
  content: "\F60A";
}

.mdi-silverware:before {
  content: "\F4A3";
}

.mdi-silverware-fork:before {
  content: "\F4A4";
}

.mdi-silverware-spoon:before {
  content: "\F4A5";
}

.mdi-silverware-variant:before {
  content: "\F4A6";
}

.mdi-sim:before {
  content: "\F4A7";
}

.mdi-sim-alert:before {
  content: "\F4A8";
}

.mdi-sim-off:before {
  content: "\F4A9";
}

.mdi-sitemap:before {
  content: "\F4AA";
}

.mdi-skip-backward:before {
  content: "\F4AB";
}

.mdi-skip-forward:before {
  content: "\F4AC";
}

.mdi-skip-next:before {
  content: "\F4AD";
}

.mdi-skip-next-circle:before {
  content: "\F661";
}

.mdi-skip-next-circle-outline:before {
  content: "\F662";
}

.mdi-skip-previous:before {
  content: "\F4AE";
}

.mdi-skip-previous-circle:before {
  content: "\F663";
}

.mdi-skip-previous-circle-outline:before {
  content: "\F664";
}

.mdi-skype:before {
  content: "\F4AF";
}

.mdi-skype-business:before {
  content: "\F4B0";
}

.mdi-slack:before {
  content: "\F4B1";
}

.mdi-sleep:before {
  content: "\F4B2";
}

.mdi-sleep-off:before {
  content: "\F4B3";
}

.mdi-smoking:before {
  content: "\F4B4";
}

.mdi-smoking-off:before {
  content: "\F4B5";
}

.mdi-snapchat:before {
  content: "\F4B6";
}

.mdi-snowman:before {
  content: "\F4B7";
}

.mdi-soccer:before {
  content: "\F4B8";
}

.mdi-sofa:before {
  content: "\F4B9";
}

.mdi-sort:before {
  content: "\F4BA";
}

.mdi-sort-alphabetical:before {
  content: "\F4BB";
}

.mdi-sort-ascending:before {
  content: "\F4BC";
}

.mdi-sort-descending:before {
  content: "\F4BD";
}

.mdi-sort-numeric:before {
  content: "\F4BE";
}

.mdi-sort-variant:before {
  content: "\F4BF";
}

.mdi-soundcloud:before {
  content: "\F4C0";
}

.mdi-source-branch:before {
  content: "\F62C";
}

.mdi-source-fork:before {
  content: "\F4C1";
}

.mdi-source-merge:before {
  content: "\F62D";
}

.mdi-source-pull:before {
  content: "\F4C2";
}

.mdi-speaker:before {
  content: "\F4C3";
}

.mdi-speaker-off:before {
  content: "\F4C4";
}

.mdi-speedometer:before {
  content: "\F4C5";
}

.mdi-spellcheck:before {
  content: "\F4C6";
}

.mdi-spotify:before {
  content: "\F4C7";
}

.mdi-spotlight:before {
  content: "\F4C8";
}

.mdi-spotlight-beam:before {
  content: "\F4C9";
}

.mdi-spray:before {
  content: "\F665";
}

.mdi-square-inc:before {
  content: "\F4CA";
}

.mdi-square-inc-cash:before {
  content: "\F4CB";
}

.mdi-stackexchange:before {
  content: "\F60B";
}

.mdi-stackoverflow:before {
  content: "\F4CC";
}

.mdi-stairs:before {
  content: "\F4CD";
}

.mdi-star:before {
  content: "\F4CE";
}

.mdi-star-circle:before {
  content: "\F4CF";
}

.mdi-star-half:before {
  content: "\F4D0";
}

.mdi-star-off:before {
  content: "\F4D1";
}

.mdi-star-outline:before {
  content: "\F4D2";
}

.mdi-steam:before {
  content: "\F4D3";
}

.mdi-steering:before {
  content: "\F4D4";
}

.mdi-step-backward:before {
  content: "\F4D5";
}

.mdi-step-backward-2:before {
  content: "\F4D6";
}

.mdi-step-forward:before {
  content: "\F4D7";
}

.mdi-step-forward-2:before {
  content: "\F4D8";
}

.mdi-stethoscope:before {
  content: "\F4D9";
}

.mdi-sticker:before {
  content: "\F5D0";
}

.mdi-stocking:before {
  content: "\F4DA";
}

.mdi-stop:before {
  content: "\F4DB";
}

.mdi-stop-circle:before {
  content: "\F666";
}

.mdi-stop-circle-outline:before {
  content: "\F667";
}

.mdi-store:before {
  content: "\F4DC";
}

.mdi-store-24-hour:before {
  content: "\F4DD";
}

.mdi-stove:before {
  content: "\F4DE";
}

.mdi-subdirectory-arrow-left:before {
  content: "\F60C";
}

.mdi-subdirectory-arrow-right:before {
  content: "\F60D";
}

.mdi-subway:before {
  content: "\F4DF";
}

.mdi-sunglasses:before {
  content: "\F4E0";
}

.mdi-surround-sound:before {
  content: "\F5C5";
}

.mdi-swap-horizontal:before {
  content: "\F4E1";
}

.mdi-swap-vertical:before {
  content: "\F4E2";
}

.mdi-swim:before {
  content: "\F4E3";
}

.mdi-switch:before {
  content: "\F4E4";
}

.mdi-sword:before {
  content: "\F4E5";
}

.mdi-sync:before {
  content: "\F4E6";
}

.mdi-sync-alert:before {
  content: "\F4E7";
}

.mdi-sync-off:before {
  content: "\F4E8";
}

.mdi-tab:before {
  content: "\F4E9";
}

.mdi-tab-unselected:before {
  content: "\F4EA";
}

.mdi-table:before {
  content: "\F4EB";
}

.mdi-table-column-plus-after:before {
  content: "\F4EC";
}

.mdi-table-column-plus-before:before {
  content: "\F4ED";
}

.mdi-table-column-remove:before {
  content: "\F4EE";
}

.mdi-table-column-width:before {
  content: "\F4EF";
}

.mdi-table-edit:before {
  content: "\F4F0";
}

.mdi-table-large:before {
  content: "\F4F1";
}

.mdi-table-row-height:before {
  content: "\F4F2";
}

.mdi-table-row-plus-after:before {
  content: "\F4F3";
}

.mdi-table-row-plus-before:before {
  content: "\F4F4";
}

.mdi-table-row-remove:before {
  content: "\F4F5";
}

.mdi-tablet:before {
  content: "\F4F6";
}

.mdi-tablet-android:before {
  content: "\F4F7";
}

.mdi-tablet-ipad:before {
  content: "\F4F8";
}

.mdi-tag:before {
  content: "\F4F9";
}

.mdi-tag-faces:before {
  content: "\F4FA";
}

.mdi-tag-multiple:before {
  content: "\F4FB";
}

.mdi-tag-outline:before {
  content: "\F4FC";
}

.mdi-tag-text-outline:before {
  content: "\F4FD";
}

.mdi-target:before {
  content: "\F4FE";
}

.mdi-taxi:before {
  content: "\F4FF";
}

.mdi-teamviewer:before {
  content: "\F500";
}

.mdi-telegram:before {
  content: "\F501";
}

.mdi-television:before {
  content: "\F502";
}

.mdi-television-guide:before {
  content: "\F503";
}

.mdi-temperature-celsius:before {
  content: "\F504";
}

.mdi-temperature-fahrenheit:before {
  content: "\F505";
}

.mdi-temperature-kelvin:before {
  content: "\F506";
}

.mdi-tennis:before {
  content: "\F507";
}

.mdi-tent:before {
  content: "\F508";
}

.mdi-terrain:before {
  content: "\F509";
}

.mdi-test-tube:before {
  content: "\F668";
}

.mdi-text-shadow:before {
  content: "\F669";
}

.mdi-text-to-speech:before {
  content: "\F50A";
}

.mdi-text-to-speech-off:before {
  content: "\F50B";
}

.mdi-textbox:before {
  content: "\F60E";
}

.mdi-texture:before {
  content: "\F50C";
}

.mdi-theater:before {
  content: "\F50D";
}

.mdi-theme-light-dark:before {
  content: "\F50E";
}

.mdi-thermometer:before {
  content: "\F50F";
}

.mdi-thermometer-lines:before {
  content: "\F510";
}

.mdi-thumb-down:before {
  content: "\F511";
}

.mdi-thumb-down-outline:before {
  content: "\F512";
}

.mdi-thumb-up:before {
  content: "\F513";
}

.mdi-thumb-up-outline:before {
  content: "\F514";
}

.mdi-thumbs-up-down:before {
  content: "\F515";
}

.mdi-ticket:before {
  content: "\F516";
}

.mdi-ticket-account:before {
  content: "\F517";
}

.mdi-ticket-confirmation:before {
  content: "\F518";
}

.mdi-tie:before {
  content: "\F519";
}

.mdi-timelapse:before {
  content: "\F51A";
}

.mdi-timer:before {
  content: "\F51B";
}

.mdi-timer-10:before {
  content: "\F51C";
}

.mdi-timer-3:before {
  content: "\F51D";
}

.mdi-timer-off:before {
  content: "\F51E";
}

.mdi-timer-sand:before {
  content: "\F51F";
}

.mdi-timetable:before {
  content: "\F520";
}

.mdi-toggle-switch:before {
  content: "\F521";
}

.mdi-toggle-switch-off:before {
  content: "\F522";
}

.mdi-tooltip:before {
  content: "\F523";
}

.mdi-tooltip-edit:before {
  content: "\F524";
}

.mdi-tooltip-image:before {
  content: "\F525";
}

.mdi-tooltip-outline:before {
  content: "\F526";
}

.mdi-tooltip-outline-plus:before {
  content: "\F527";
}

.mdi-tooltip-text:before {
  content: "\F528";
}

.mdi-tooth:before {
  content: "\F529";
}

.mdi-tor:before {
  content: "\F52A";
}

.mdi-traffic-light:before {
  content: "\F52B";
}

.mdi-train:before {
  content: "\F52C";
}

.mdi-tram:before {
  content: "\F52D";
}

.mdi-transcribe:before {
  content: "\F52E";
}

.mdi-transcribe-close:before {
  content: "\F52F";
}

.mdi-transfer:before {
  content: "\F530";
}

.mdi-translate:before {
  content: "\F5CA";
}

.mdi-tree:before {
  content: "\F531";
}

.mdi-trello:before {
  content: "\F532";
}

.mdi-trending-down:before {
  content: "\F533";
}

.mdi-trending-neutral:before {
  content: "\F534";
}

.mdi-trending-up:before {
  content: "\F535";
}

.mdi-triangle:before {
  content: "\F536";
}

.mdi-triangle-outline:before {
  content: "\F537";
}

.mdi-trophy:before {
  content: "\F538";
}

.mdi-trophy-award:before {
  content: "\F539";
}

.mdi-trophy-outline:before {
  content: "\F53A";
}

.mdi-trophy-variant:before {
  content: "\F53B";
}

.mdi-trophy-variant-outline:before {
  content: "\F53C";
}

.mdi-truck:before {
  content: "\F53D";
}

.mdi-truck-delivery:before {
  content: "\F53E";
}

.mdi-tshirt-crew:before {
  content: "\F53F";
}

.mdi-tshirt-v:before {
  content: "\F540";
}

.mdi-tumblr:before {
  content: "\F541";
}

.mdi-tumblr-reblog:before {
  content: "\F542";
}

.mdi-tune:before {
  content: "\F62E";
}

.mdi-tune-vertical:before {
  content: "\F66A";
}

.mdi-twitch:before {
  content: "\F543";
}

.mdi-twitter:before {
  content: "\F544";
}

.mdi-twitter-box:before {
  content: "\F545";
}

.mdi-twitter-circle:before {
  content: "\F546";
}

.mdi-twitter-retweet:before {
  content: "\F547";
}

.mdi-ubuntu:before {
  content: "\F548";
}

.mdi-umbraco:before {
  content: "\F549";
}

.mdi-umbrella:before {
  content: "\F54A";
}

.mdi-umbrella-outline:before {
  content: "\F54B";
}

.mdi-undo:before {
  content: "\F54C";
}

.mdi-undo-variant:before {
  content: "\F54D";
}

.mdi-unfold-less:before {
  content: "\F54E";
}

.mdi-unfold-more:before {
  content: "\F54F";
}

.mdi-ungroup:before {
  content: "\F550";
}

.mdi-untappd:before {
  content: "\F551";
}

.mdi-upload:before {
  content: "\F552";
}

.mdi-usb:before {
  content: "\F553";
}

.mdi-vector-arrange-above:before {
  content: "\F554";
}

.mdi-vector-arrange-below:before {
  content: "\F555";
}

.mdi-vector-circle:before {
  content: "\F556";
}

.mdi-vector-circle-variant:before {
  content: "\F557";
}

.mdi-vector-combine:before {
  content: "\F558";
}

.mdi-vector-curve:before {
  content: "\F559";
}

.mdi-vector-difference:before {
  content: "\F55A";
}

.mdi-vector-difference-ab:before {
  content: "\F55B";
}

.mdi-vector-difference-ba:before {
  content: "\F55C";
}

.mdi-vector-intersection:before {
  content: "\F55D";
}

.mdi-vector-line:before {
  content: "\F55E";
}

.mdi-vector-point:before {
  content: "\F55F";
}

.mdi-vector-polygon:before {
  content: "\F560";
}

.mdi-vector-polyline:before {
  content: "\F561";
}

.mdi-vector-rectangle:before {
  content: "\F5C6";
}

.mdi-vector-selection:before {
  content: "\F562";
}

.mdi-vector-square:before {
  content: "\F001";
}

.mdi-vector-triangle:before {
  content: "\F563";
}

.mdi-vector-union:before {
  content: "\F564";
}

.mdi-verified:before {
  content: "\F565";
}

.mdi-vibrate:before {
  content: "\F566";
}

.mdi-video:before {
  content: "\F567";
}

.mdi-video-off:before {
  content: "\F568";
}

.mdi-video-switch:before {
  content: "\F569";
}

.mdi-view-agenda:before {
  content: "\F56A";
}

.mdi-view-array:before {
  content: "\F56B";
}

.mdi-view-carousel:before {
  content: "\F56C";
}

.mdi-view-column:before {
  content: "\F56D";
}

.mdi-view-dashboard:before {
  content: "\F56E";
}

.mdi-view-day:before {
  content: "\F56F";
}

.mdi-view-grid:before {
  content: "\F570";
}

.mdi-view-headline:before {
  content: "\F571";
}

.mdi-view-list:before {
  content: "\F572";
}

.mdi-view-module:before {
  content: "\F573";
}

.mdi-view-quilt:before {
  content: "\F574";
}

.mdi-view-stream:before {
  content: "\F575";
}

.mdi-view-week:before {
  content: "\F576";
}

.mdi-vimeo:before {
  content: "\F577";
}

.mdi-vine:before {
  content: "\F578";
}

.mdi-violin:before {
  content: "\F60F";
}

.mdi-visualstudio:before {
  content: "\F610";
}

.mdi-vk:before {
  content: "\F579";
}

.mdi-vk-box:before {
  content: "\F57A";
}

.mdi-vk-circle:before {
  content: "\F57B";
}

.mdi-vlc:before {
  content: "\F57C";
}

.mdi-voice:before {
  content: "\F5CB";
}

.mdi-voicemail:before {
  content: "\F57D";
}

.mdi-volume-high:before {
  content: "\F57E";
}

.mdi-volume-low:before {
  content: "\F57F";
}

.mdi-volume-medium:before {
  content: "\F580";
}

.mdi-volume-off:before {
  content: "\F581";
}

.mdi-vpn:before {
  content: "\F582";
}

.mdi-walk:before {
  content: "\F583";
}

.mdi-wallet:before {
  content: "\F584";
}

.mdi-wallet-giftcard:before {
  content: "\F585";
}

.mdi-wallet-membership:before {
  content: "\F586";
}

.mdi-wallet-travel:before {
  content: "\F587";
}

.mdi-wan:before {
  content: "\F588";
}

.mdi-watch:before {
  content: "\F589";
}

.mdi-watch-export:before {
  content: "\F58A";
}

.mdi-watch-import:before {
  content: "\F58B";
}

.mdi-water:before {
  content: "\F58C";
}

.mdi-water-off:before {
  content: "\F58D";
}

.mdi-water-percent:before {
  content: "\F58E";
}

.mdi-water-pump:before {
  content: "\F58F";
}

.mdi-watermark:before {
  content: "\F612";
}

.mdi-weather-cloudy:before {
  content: "\F590";
}

.mdi-weather-fog:before {
  content: "\F591";
}

.mdi-weather-hail:before {
  content: "\F592";
}

.mdi-weather-lightning:before {
  content: "\F593";
}

.mdi-weather-night:before {
  content: "\F594";
}

.mdi-weather-partlycloudy:before {
  content: "\F595";
}

.mdi-weather-pouring:before {
  content: "\F596";
}

.mdi-weather-rainy:before {
  content: "\F597";
}

.mdi-weather-snowy:before {
  content: "\F598";
}

.mdi-weather-sunny:before {
  content: "\F599";
}

.mdi-weather-sunset:before {
  content: "\F59A";
}

.mdi-weather-sunset-down:before {
  content: "\F59B";
}

.mdi-weather-sunset-up:before {
  content: "\F59C";
}

.mdi-weather-windy:before {
  content: "\F59D";
}

.mdi-weather-windy-variant:before {
  content: "\F59E";
}

.mdi-web:before {
  content: "\F59F";
}

.mdi-webcam:before {
  content: "\F5A0";
}

.mdi-webhook:before {
  content: "\F62F";
}

.mdi-wechat:before {
  content: "\F611";
}

.mdi-weight:before {
  content: "\F5A1";
}

.mdi-weight-kilogram:before {
  content: "\F5A2";
}

.mdi-whatsapp:before {
  content: "\F5A3";
}

.mdi-wheelchair-accessibility:before {
  content: "\F5A4";
}

.mdi-white-balance-auto:before {
  content: "\F5A5";
}

.mdi-white-balance-incandescent:before {
  content: "\F5A6";
}

.mdi-white-balance-iridescent:before {
  content: "\F5A7";
}

.mdi-white-balance-sunny:before {
  content: "\F5A8";
}

.mdi-wifi:before {
  content: "\F5A9";
}

.mdi-wifi-off:before {
  content: "\F5AA";
}

.mdi-wii:before {
  content: "\F5AB";
}

.mdi-wikipedia:before {
  content: "\F5AC";
}

.mdi-window-close:before {
  content: "\F5AD";
}

.mdi-window-closed:before {
  content: "\F5AE";
}

.mdi-window-maximize:before {
  content: "\F5AF";
}

.mdi-window-minimize:before {
  content: "\F5B0";
}

.mdi-window-open:before {
  content: "\F5B1";
}

.mdi-window-restore:before {
  content: "\F5B2";
}

.mdi-windows:before {
  content: "\F5B3";
}

.mdi-wordpress:before {
  content: "\F5B4";
}

.mdi-worker:before {
  content: "\F5B5";
}

.mdi-wrap:before {
  content: "\F5B6";
}

.mdi-wrench:before {
  content: "\F5B7";
}

.mdi-wunderlist:before {
  content: "\F5B8";
}

.mdi-xaml:before {
  content: "\F673";
}

.mdi-xbox:before {
  content: "\F5B9";
}

.mdi-xbox-controller:before {
  content: "\F5BA";
}

.mdi-xbox-controller-off:before {
  content: "\F5BB";
}

.mdi-xda:before {
  content: "\F5BC";
}

.mdi-xing:before {
  content: "\F5BD";
}

.mdi-xing-box:before {
  content: "\F5BE";
}

.mdi-xing-circle:before {
  content: "\F5BF";
}

.mdi-xml:before {
  content: "\F5C0";
}

.mdi-yeast:before {
  content: "\F5C1";
}

.mdi-yelp:before {
  content: "\F5C2";
}

.mdi-youtube-play:before {
  content: "\F5C3";
}

.mdi-zip-box:before {
  content: "\F5C4";
}

.mdi-18px {
  font-size: 18px;
}

.mdi-24px {
  font-size: 24px;
}

.mdi-36px {
  font-size: 36px;
}

.mdi-48px {
  font-size: 48px;
}

.mdi-dark {
  color: rgba(0, 0, 0, 0.54);
}

.mdi-dark.mdi-inactive {
  color: rgba(0, 0, 0, 0.26);
}

.mdi-light {
  color: white;
}

.mdi-light.mdi-inactive {
  color: rgba(255, 255, 255, 0.3);
}

.mdi-rotate-45 {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.mdi-rotate-90 {
  -webkit-transform: rotate(90deg);
  transform: rotate(90deg);
}

.mdi-rotate-135 {
  -webkit-transform: rotate(135deg);
  transform: rotate(135deg);
}

.mdi-rotate-180 {
  -webkit-transform: rotate(180deg);
  transform: rotate(180deg);
}

.mdi-rotate-225 {
  -webkit-transform: rotate(225deg);
  transform: rotate(225deg);
}

.mdi-rotate-270 {
  -webkit-transform: rotate(270deg);
  transform: rotate(270deg);
}

.mdi-rotate-315 {
  -webkit-transform: rotate(315deg);
  transform: rotate(315deg);
}

.mdi-flip-horizontal {
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
  -webkit-filter: FlipH;
          filter: FlipH;
  -ms-filter: "FlipH";
}

.mdi-flip-vertical {
  -webkit-transform: scaleY(-1);
  transform: scaleY(-1);
  -webkit-filter: FlipV;
          filter: FlipV;
  -ms-filter: "FlipV";
}
/*!
  Ionicons, v1.5.0
  Created by Ben Sperry for the Ionic Framework, http://ionicons.com/
  https://twitter.com/benjsperry  https://twitter.com/ionicframework
  MIT License: https://github.com/driftyco/ionicons
*/
@font-face {
  font-family: "Ionicons";
  src: url("../fonts/ioniconsaa26.eot?v=1.5.0");
  src: url("../fonts/ioniconsaa26.eot?v=1.5.0#iefix") format("embedded-opentype"), url("../fonts/ioniconsaa26.ttf?v=1.5.0") format("truetype"), url("../fonts/ioniconsaa26.woff?v=1.5.0") format("woff"), url("../fonts/ioniconsaa26.svg?v=1.5.0#Ionicons") format("svg");
  font-weight: normal;
  font-style: normal;
}

.ion, .ion-loading-a,
.ion-loading-b,
.ion-loading-c,
.ion-loading-d,
.ion-looping,
.ion-refreshing,
.ion-ios7-reloading, .ionicons,
.ion-alert,
.ion-alert-circled,
.ion-android-add,
.ion-android-add-contact,
.ion-android-alarm,
.ion-android-archive,
.ion-android-arrow-back,
.ion-android-arrow-down-left,
.ion-android-arrow-down-right,
.ion-android-arrow-forward,
.ion-android-arrow-up-left,
.ion-android-arrow-up-right,
.ion-android-battery,
.ion-android-book,
.ion-android-calendar,
.ion-android-call,
.ion-android-camera,
.ion-android-chat,
.ion-android-checkmark,
.ion-android-clock,
.ion-android-close,
.ion-android-contact,
.ion-android-contacts,
.ion-android-data,
.ion-android-developer,
.ion-android-display,
.ion-android-download,
.ion-android-drawer,
.ion-android-dropdown,
.ion-android-earth,
.ion-android-folder,
.ion-android-forums,
.ion-android-friends,
.ion-android-hand,
.ion-android-image,
.ion-android-inbox,
.ion-android-information,
.ion-android-keypad,
.ion-android-lightbulb,
.ion-android-locate,
.ion-android-location,
.ion-android-mail,
.ion-android-microphone,
.ion-android-mixer,
.ion-android-more,
.ion-android-note,
.ion-android-playstore,
.ion-android-printer,
.ion-android-promotion,
.ion-android-reminder,
.ion-android-remove,
.ion-android-search,
.ion-android-send,
.ion-android-settings,
.ion-android-share,
.ion-android-social,
.ion-android-social-user,
.ion-android-sort,
.ion-android-stair-drawer,
.ion-android-star,
.ion-android-stopwatch,
.ion-android-storage,
.ion-android-system-back,
.ion-android-system-home,
.ion-android-system-windows,
.ion-android-timer,
.ion-android-trash,
.ion-android-user-menu,
.ion-android-volume,
.ion-android-wifi,
.ion-aperture,
.ion-archive,
.ion-arrow-down-a,
.ion-arrow-down-b,
.ion-arrow-down-c,
.ion-arrow-expand,
.ion-arrow-graph-down-left,
.ion-arrow-graph-down-right,
.ion-arrow-graph-up-left,
.ion-arrow-graph-up-right,
.ion-arrow-left-a,
.ion-arrow-left-b,
.ion-arrow-left-c,
.ion-arrow-move,
.ion-arrow-resize,
.ion-arrow-return-left,
.ion-arrow-return-right,
.ion-arrow-right-a,
.ion-arrow-right-b,
.ion-arrow-right-c,
.ion-arrow-shrink,
.ion-arrow-swap,
.ion-arrow-up-a,
.ion-arrow-up-b,
.ion-arrow-up-c,
.ion-asterisk,
.ion-at,
.ion-bag,
.ion-battery-charging,
.ion-battery-empty,
.ion-battery-full,
.ion-battery-half,
.ion-battery-low,
.ion-beaker,
.ion-beer,
.ion-bluetooth,
.ion-bonfire,
.ion-bookmark,
.ion-briefcase,
.ion-bug,
.ion-calculator,
.ion-calendar,
.ion-camera,
.ion-card,
.ion-cash,
.ion-chatbox,
.ion-chatbox-working,
.ion-chatboxes,
.ion-chatbubble,
.ion-chatbubble-working,
.ion-chatbubbles,
.ion-checkmark,
.ion-checkmark-circled,
.ion-checkmark-round,
.ion-chevron-down,
.ion-chevron-left,
.ion-chevron-right,
.ion-chevron-up,
.ion-clipboard,
.ion-clock,
.ion-close,
.ion-close-circled,
.ion-close-round,
.ion-closed-captioning,
.ion-cloud,
.ion-code,
.ion-code-download,
.ion-code-working,
.ion-coffee,
.ion-compass,
.ion-compose,
.ion-connection-bars,
.ion-contrast,
.ion-cube,
.ion-disc,
.ion-document,
.ion-document-text,
.ion-drag,
.ion-earth,
.ion-edit,
.ion-egg,
.ion-eject,
.ion-email,
.ion-eye,
.ion-eye-disabled,
.ion-female,
.ion-filing,
.ion-film-marker,
.ion-fireball,
.ion-flag,
.ion-flame,
.ion-flash,
.ion-flash-off,
.ion-flask,
.ion-folder,
.ion-fork,
.ion-fork-repo,
.ion-forward,
.ion-funnel,
.ion-game-controller-a,
.ion-game-controller-b,
.ion-gear-a,
.ion-gear-b,
.ion-grid,
.ion-hammer,
.ion-happy,
.ion-headphone,
.ion-heart,
.ion-heart-broken,
.ion-help,
.ion-help-buoy,
.ion-help-circled,
.ion-home,
.ion-icecream,
.ion-icon-social-google-plus,
.ion-icon-social-google-plus-outline,
.ion-image,
.ion-images,
.ion-information,
.ion-information-circled,
.ion-ionic,
.ion-ios7-alarm,
.ion-ios7-alarm-outline,
.ion-ios7-albums,
.ion-ios7-albums-outline,
.ion-ios7-americanfootball,
.ion-ios7-americanfootball-outline,
.ion-ios7-analytics,
.ion-ios7-analytics-outline,
.ion-ios7-arrow-back,
.ion-ios7-arrow-down,
.ion-ios7-arrow-forward,
.ion-ios7-arrow-left,
.ion-ios7-arrow-right,
.ion-ios7-arrow-thin-down,
.ion-ios7-arrow-thin-left,
.ion-ios7-arrow-thin-right,
.ion-ios7-arrow-thin-up,
.ion-ios7-arrow-up,
.ion-ios7-at,
.ion-ios7-at-outline,
.ion-ios7-barcode,
.ion-ios7-barcode-outline,
.ion-ios7-baseball,
.ion-ios7-baseball-outline,
.ion-ios7-basketball,
.ion-ios7-basketball-outline,
.ion-ios7-bell,
.ion-ios7-bell-outline,
.ion-ios7-bolt,
.ion-ios7-bolt-outline,
.ion-ios7-bookmarks,
.ion-ios7-bookmarks-outline,
.ion-ios7-box,
.ion-ios7-box-outline,
.ion-ios7-briefcase,
.ion-ios7-briefcase-outline,
.ion-ios7-browsers,
.ion-ios7-browsers-outline,
.ion-ios7-calculator,
.ion-ios7-calculator-outline,
.ion-ios7-calendar,
.ion-ios7-calendar-outline,
.ion-ios7-camera,
.ion-ios7-camera-outline,
.ion-ios7-cart,
.ion-ios7-cart-outline,
.ion-ios7-chatboxes,
.ion-ios7-chatboxes-outline,
.ion-ios7-chatbubble,
.ion-ios7-chatbubble-outline,
.ion-ios7-checkmark,
.ion-ios7-checkmark-empty,
.ion-ios7-checkmark-outline,
.ion-ios7-circle-filled,
.ion-ios7-circle-outline,
.ion-ios7-clock,
.ion-ios7-clock-outline,
.ion-ios7-close,
.ion-ios7-close-empty,
.ion-ios7-close-outline,
.ion-ios7-cloud,
.ion-ios7-cloud-download,
.ion-ios7-cloud-download-outline,
.ion-ios7-cloud-outline,
.ion-ios7-cloud-upload,
.ion-ios7-cloud-upload-outline,
.ion-ios7-cloudy,
.ion-ios7-cloudy-night,
.ion-ios7-cloudy-night-outline,
.ion-ios7-cloudy-outline,
.ion-ios7-cog,
.ion-ios7-cog-outline,
.ion-ios7-compose,
.ion-ios7-compose-outline,
.ion-ios7-contact,
.ion-ios7-contact-outline,
.ion-ios7-copy,
.ion-ios7-copy-outline,
.ion-ios7-download,
.ion-ios7-download-outline,
.ion-ios7-drag,
.ion-ios7-email,
.ion-ios7-email-outline,
.ion-ios7-expand,
.ion-ios7-eye,
.ion-ios7-eye-outline,
.ion-ios7-fastforward,
.ion-ios7-fastforward-outline,
.ion-ios7-filing,
.ion-ios7-filing-outline,
.ion-ios7-film,
.ion-ios7-film-outline,
.ion-ios7-flag,
.ion-ios7-flag-outline,
.ion-ios7-folder,
.ion-ios7-folder-outline,
.ion-ios7-football,
.ion-ios7-football-outline,
.ion-ios7-gear,
.ion-ios7-gear-outline,
.ion-ios7-glasses,
.ion-ios7-glasses-outline,
.ion-ios7-heart,
.ion-ios7-heart-outline,
.ion-ios7-help,
.ion-ios7-help-empty,
.ion-ios7-help-outline,
.ion-ios7-home,
.ion-ios7-home-outline,
.ion-ios7-infinite,
.ion-ios7-infinite-outline,
.ion-ios7-information,
.ion-ios7-information-empty,
.ion-ios7-information-outline,
.ion-ios7-ionic-outline,
.ion-ios7-keypad,
.ion-ios7-keypad-outline,
.ion-ios7-lightbulb,
.ion-ios7-lightbulb-outline,
.ion-ios7-location,
.ion-ios7-location-outline,
.ion-ios7-locked,
.ion-ios7-locked-outline,
.ion-ios7-loop,
.ion-ios7-loop-strong,
.ion-ios7-medkit,
.ion-ios7-medkit-outline,
.ion-ios7-mic,
.ion-ios7-mic-off,
.ion-ios7-mic-outline,
.ion-ios7-minus,
.ion-ios7-minus-empty,
.ion-ios7-minus-outline,
.ion-ios7-monitor,
.ion-ios7-monitor-outline,
.ion-ios7-moon,
.ion-ios7-moon-outline,
.ion-ios7-more,
.ion-ios7-more-outline,
.ion-ios7-musical-note,
.ion-ios7-musical-notes,
.ion-ios7-navigate,
.ion-ios7-navigate-outline,
.ion-ios7-paper,
.ion-ios7-paper-outline,
.ion-ios7-paperplane,
.ion-ios7-paperplane-outline,
.ion-ios7-partlysunny,
.ion-ios7-partlysunny-outline,
.ion-ios7-pause,
.ion-ios7-pause-outline,
.ion-ios7-paw,
.ion-ios7-paw-outline,
.ion-ios7-people,
.ion-ios7-people-outline,
.ion-ios7-person,
.ion-ios7-person-outline,
.ion-ios7-personadd,
.ion-ios7-personadd-outline,
.ion-ios7-photos,
.ion-ios7-photos-outline,
.ion-ios7-pie,
.ion-ios7-pie-outline,
.ion-ios7-play,
.ion-ios7-play-outline,
.ion-ios7-plus,
.ion-ios7-plus-empty,
.ion-ios7-plus-outline,
.ion-ios7-pricetag,
.ion-ios7-pricetag-outline,
.ion-ios7-pricetags,
.ion-ios7-pricetags-outline,
.ion-ios7-printer,
.ion-ios7-printer-outline,
.ion-ios7-pulse,
.ion-ios7-pulse-strong,
.ion-ios7-rainy,
.ion-ios7-rainy-outline,
.ion-ios7-recording,
.ion-ios7-recording-outline,
.ion-ios7-redo,
.ion-ios7-redo-outline,
.ion-ios7-refresh,
.ion-ios7-refresh-empty,
.ion-ios7-refresh-outline,
.ion-ios7-reload,
.ion-ios7-reverse-camera,
.ion-ios7-reverse-camera-outline,
.ion-ios7-rewind,
.ion-ios7-rewind-outline,
.ion-ios7-search,
.ion-ios7-search-strong,
.ion-ios7-settings,
.ion-ios7-settings-strong,
.ion-ios7-shrink,
.ion-ios7-skipbackward,
.ion-ios7-skipbackward-outline,
.ion-ios7-skipforward,
.ion-ios7-skipforward-outline,
.ion-ios7-snowy,
.ion-ios7-speedometer,
.ion-ios7-speedometer-outline,
.ion-ios7-star,
.ion-ios7-star-half,
.ion-ios7-star-outline,
.ion-ios7-stopwatch,
.ion-ios7-stopwatch-outline,
.ion-ios7-sunny,
.ion-ios7-sunny-outline,
.ion-ios7-telephone,
.ion-ios7-telephone-outline,
.ion-ios7-tennisball,
.ion-ios7-tennisball-outline,
.ion-ios7-thunderstorm,
.ion-ios7-thunderstorm-outline,
.ion-ios7-time,
.ion-ios7-time-outline,
.ion-ios7-timer,
.ion-ios7-timer-outline,
.ion-ios7-toggle,
.ion-ios7-toggle-outline,
.ion-ios7-trash,
.ion-ios7-trash-outline,
.ion-ios7-undo,
.ion-ios7-undo-outline,
.ion-ios7-unlocked,
.ion-ios7-unlocked-outline,
.ion-ios7-upload,
.ion-ios7-upload-outline,
.ion-ios7-videocam,
.ion-ios7-videocam-outline,
.ion-ios7-volume-high,
.ion-ios7-volume-low,
.ion-ios7-wineglass,
.ion-ios7-wineglass-outline,
.ion-ios7-world,
.ion-ios7-world-outline,
.ion-ipad,
.ion-iphone,
.ion-ipod,
.ion-jet,
.ion-key,
.ion-knife,
.ion-laptop,
.ion-leaf,
.ion-levels,
.ion-lightbulb,
.ion-link,
.ion-load-a,
.ion-load-b,
.ion-load-c,
.ion-load-d,
.ion-location,
.ion-locked,
.ion-log-in,
.ion-log-out,
.ion-loop,
.ion-magnet,
.ion-male,
.ion-man,
.ion-map,
.ion-medkit,
.ion-merge,
.ion-mic-a,
.ion-mic-b,
.ion-mic-c,
.ion-minus,
.ion-minus-circled,
.ion-minus-round,
.ion-model-s,
.ion-monitor,
.ion-more,
.ion-mouse,
.ion-music-note,
.ion-navicon,
.ion-navicon-round,
.ion-navigate,
.ion-network,
.ion-no-smoking,
.ion-nuclear,
.ion-outlet,
.ion-paper-airplane,
.ion-paperclip,
.ion-pause,
.ion-person,
.ion-person-add,
.ion-person-stalker,
.ion-pie-graph,
.ion-pin,
.ion-pinpoint,
.ion-pizza,
.ion-plane,
.ion-planet,
.ion-play,
.ion-playstation,
.ion-plus,
.ion-plus-circled,
.ion-plus-round,
.ion-podium,
.ion-pound,
.ion-power,
.ion-pricetag,
.ion-pricetags,
.ion-printer,
.ion-pull-request,
.ion-qr-scanner,
.ion-quote,
.ion-radio-waves,
.ion-record,
.ion-refresh,
.ion-reply,
.ion-reply-all,
.ion-ribbon-a,
.ion-ribbon-b,
.ion-sad,
.ion-scissors,
.ion-search,
.ion-settings,
.ion-share,
.ion-shuffle,
.ion-skip-backward,
.ion-skip-forward,
.ion-social-android,
.ion-social-android-outline,
.ion-social-apple,
.ion-social-apple-outline,
.ion-social-bitcoin,
.ion-social-bitcoin-outline,
.ion-social-buffer,
.ion-social-buffer-outline,
.ion-social-designernews,
.ion-social-designernews-outline,
.ion-social-dribbble,
.ion-social-dribbble-outline,
.ion-social-dropbox,
.ion-social-dropbox-outline,
.ion-social-facebook,
.ion-social-facebook-outline,
.ion-social-foursquare,
.ion-social-foursquare-outline,
.ion-social-freebsd-devil,
.ion-social-github,
.ion-social-github-outline,
.ion-social-google,
.ion-social-google-outline,
.ion-social-googleplus,
.ion-social-googleplus-outline,
.ion-social-hackernews,
.ion-social-hackernews-outline,
.ion-social-instagram,
.ion-social-instagram-outline,
.ion-social-linkedin,
.ion-social-linkedin-outline,
.ion-social-pinterest,
.ion-social-pinterest-outline,
.ion-social-reddit,
.ion-social-reddit-outline,
.ion-social-rss,
.ion-social-rss-outline,
.ion-social-skype,
.ion-social-skype-outline,
.ion-social-tumblr,
.ion-social-tumblr-outline,
.ion-social-tux,
.ion-social-twitter,
.ion-social-twitter-outline,
.ion-social-usd,
.ion-social-usd-outline,
.ion-social-vimeo,
.ion-social-vimeo-outline,
.ion-social-windows,
.ion-social-windows-outline,
.ion-social-wordpress,
.ion-social-wordpress-outline,
.ion-social-yahoo,
.ion-social-yahoo-outline,
.ion-social-youtube,
.ion-social-youtube-outline,
.ion-speakerphone,
.ion-speedometer,
.ion-spoon,
.ion-star,
.ion-stats-bars,
.ion-steam,
.ion-stop,
.ion-thermometer,
.ion-thumbsdown,
.ion-thumbsup,
.ion-toggle,
.ion-toggle-filled,
.ion-trash-a,
.ion-trash-b,
.ion-trophy,
.ion-umbrella,
.ion-university,
.ion-unlocked,
.ion-upload,
.ion-usb,
.ion-videocamera,
.ion-volume-high,
.ion-volume-low,
.ion-volume-medium,
.ion-volume-mute,
.ion-wand,
.ion-waterdrop,
.ion-wifi,
.ion-wineglass,
.ion-woman,
.ion-wrench,
.ion-xbox {
  display: inline-block;
  font-family: "Ionicons";
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  text-rendering: auto;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ion-spin, .ion-loading-a,
.ion-loading-b,
.ion-loading-c,
.ion-loading-d,
.ion-looping,
.ion-refreshing,
.ion-ios7-reloading {
  -webkit-animation: spin 1s infinite linear;
  animation: spin 1s infinite linear;
}

@-webkit-keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
            transform: rotate(359deg);
  }
}

.ion-loading-a {
  -webkit-animation-timing-function: steps(8, start);
  animation-timing-function: steps(8, start);
}

.ion-alert:before {
  content: "";
}

.ion-alert-circled:before {
  content: "";
}

.ion-android-add:before {
  content: "";
}

.ion-android-add-contact:before {
  content: "";
}

.ion-android-alarm:before {
  content: "";
}

.ion-android-archive:before {
  content: "";
}

.ion-android-arrow-back:before {
  content: "";
}

.ion-android-arrow-down-left:before {
  content: "";
}

.ion-android-arrow-down-right:before {
  content: "";
}

.ion-android-arrow-forward:before {
  content: "";
}

.ion-android-arrow-up-left:before {
  content: "";
}

.ion-android-arrow-up-right:before {
  content: "";
}

.ion-android-battery:before {
  content: "";
}

.ion-android-book:before {
  content: "";
}

.ion-android-calendar:before {
  content: "";
}

.ion-android-call:before {
  content: "";
}

.ion-android-camera:before {
  content: "";
}

.ion-android-chat:before {
  content: "";
}

.ion-android-checkmark:before {
  content: "";
}

.ion-android-clock:before {
  content: "";
}

.ion-android-close:before {
  content: "";
}

.ion-android-contact:before {
  content: "";
}

.ion-android-contacts:before {
  content: "";
}

.ion-android-data:before {
  content: "";
}

.ion-android-developer:before {
  content: "";
}

.ion-android-display:before {
  content: "";
}

.ion-android-download:before {
  content: "";
}

.ion-android-drawer:before {
  content: "";
}

.ion-android-dropdown:before {
  content: "";
}

.ion-android-earth:before {
  content: "";
}

.ion-android-folder:before {
  content: "";
}

.ion-android-forums:before {
  content: "";
}

.ion-android-friends:before {
  content: "";
}

.ion-android-hand:before {
  content: "";
}

.ion-android-image:before {
  content: "";
}

.ion-android-inbox:before {
  content: "";
}

.ion-android-information:before {
  content: "";
}

.ion-android-keypad:before {
  content: "";
}

.ion-android-lightbulb:before {
  content: "";
}

.ion-android-locate:before {
  content: "";
}

.ion-android-location:before {
  content: "";
}

.ion-android-mail:before {
  content: "";
}

.ion-android-microphone:before {
  content: "";
}

.ion-android-mixer:before {
  content: "";
}

.ion-android-more:before {
  content: "";
}

.ion-android-note:before {
  content: "";
}

.ion-android-playstore:before {
  content: "";
}

.ion-android-printer:before {
  content: "";
}

.ion-android-promotion:before {
  content: "";
}

.ion-android-reminder:before {
  content: "";
}

.ion-android-remove:before {
  content: "";
}

.ion-android-search:before {
  content: "";
}

.ion-android-send:before {
  content: "";
}

.ion-android-settings:before {
  content: "";
}

.ion-android-share:before {
  content: "";
}

.ion-android-social:before {
  content: "";
}

.ion-android-social-user:before {
  content: "";
}

.ion-android-sort:before {
  content: "";
}

.ion-android-stair-drawer:before {
  content: "";
}

.ion-android-star:before {
  content: "";
}

.ion-android-stopwatch:before {
  content: "";
}

.ion-android-storage:before {
  content: "";
}

.ion-android-system-back:before {
  content: "";
}

.ion-android-system-home:before {
  content: "";
}

.ion-android-system-windows:before {
  content: "";
}

.ion-android-timer:before {
  content: "";
}

.ion-android-trash:before {
  content: "";
}

.ion-android-user-menu:before {
  content: "";
}

.ion-android-volume:before {
  content: "";
}

.ion-android-wifi:before {
  content: "";
}

.ion-aperture:before {
  content: "";
}

.ion-archive:before {
  content: "";
}

.ion-arrow-down-a:before {
  content: "";
}

.ion-arrow-down-b:before {
  content: "";
}

.ion-arrow-down-c:before {
  content: "";
}

.ion-arrow-expand:before {
  content: "";
}

.ion-arrow-graph-down-left:before {
  content: "";
}

.ion-arrow-graph-down-right:before {
  content: "";
}

.ion-arrow-graph-up-left:before {
  content: "";
}

.ion-arrow-graph-up-right:before {
  content: "";
}

.ion-arrow-left-a:before {
  content: "";
}

.ion-arrow-left-b:before {
  content: "";
}

.ion-arrow-left-c:before {
  content: "";
}

.ion-arrow-move:before {
  content: "";
}

.ion-arrow-resize:before {
  content: "";
}

.ion-arrow-return-left:before {
  content: "";
}

.ion-arrow-return-right:before {
  content: "";
}

.ion-arrow-right-a:before {
  content: "";
}

.ion-arrow-right-b:before {
  content: "";
}

.ion-arrow-right-c:before {
  content: "";
}

.ion-arrow-shrink:before {
  content: "";
}

.ion-arrow-swap:before {
  content: "";
}

.ion-arrow-up-a:before {
  content: "";
}

.ion-arrow-up-b:before {
  content: "";
}

.ion-arrow-up-c:before {
  content: "";
}

.ion-asterisk:before {
  content: "";
}

.ion-at:before {
  content: "";
}

.ion-bag:before {
  content: "";
}

.ion-battery-charging:before {
  content: "";
}

.ion-battery-empty:before {
  content: "";
}

.ion-battery-full:before {
  content: "";
}

.ion-battery-half:before {
  content: "";
}

.ion-battery-low:before {
  content: "";
}

.ion-beaker:before {
  content: "";
}

.ion-beer:before {
  content: "";
}

.ion-bluetooth:before {
  content: "";
}

.ion-bonfire:before {
  content: "";
}

.ion-bookmark:before {
  content: "";
}

.ion-briefcase:before {
  content: "";
}

.ion-bug:before {
  content: "";
}

.ion-calculator:before {
  content: "";
}

.ion-calendar:before {
  content: "";
}

.ion-camera:before {
  content: "";
}

.ion-card:before {
  content: "";
}

.ion-cash:before {
  content: "";
}

.ion-chatbox:before {
  content: "";
}

.ion-chatbox-working:before {
  content: "";
}

.ion-chatboxes:before {
  content: "";
}

.ion-chatbubble:before {
  content: "";
}

.ion-chatbubble-working:before {
  content: "";
}

.ion-chatbubbles:before {
  content: "";
}

.ion-checkmark:before {
  content: "";
}

.ion-checkmark-circled:before {
  content: "";
}

.ion-checkmark-round:before {
  content: "";
}

.ion-chevron-down:before {
  content: "";
}

.ion-chevron-left:before {
  content: "";
}

.ion-chevron-right:before {
  content: "";
}

.ion-chevron-up:before {
  content: "";
}

.ion-clipboard:before {
  content: "";
}

.ion-clock:before {
  content: "";
}

.ion-close:before {
  content: "";
}

.ion-close-circled:before {
  content: "";
}

.ion-close-round:before {
  content: "";
}

.ion-closed-captioning:before {
  content: "";
}

.ion-cloud:before {
  content: "";
}

.ion-code:before {
  content: "";
}

.ion-code-download:before {
  content: "";
}

.ion-code-working:before {
  content: "";
}

.ion-coffee:before {
  content: "";
}

.ion-compass:before {
  content: "";
}

.ion-compose:before {
  content: "";
}

.ion-connection-bars:before {
  content: "";
}

.ion-contrast:before {
  content: "";
}

.ion-cube:before {
  content: "";
}

.ion-disc:before {
  content: "";
}

.ion-document:before {
  content: "";
}

.ion-document-text:before {
  content: "";
}

.ion-drag:before {
  content: "";
}

.ion-earth:before {
  content: "";
}

.ion-edit:before {
  content: "";
}

.ion-egg:before {
  content: "";
}

.ion-eject:before {
  content: "";
}

.ion-email:before {
  content: "";
}

.ion-eye:before {
  content: "";
}

.ion-eye-disabled:before {
  content: "";
}

.ion-female:before {
  content: "";
}

.ion-filing:before {
  content: "";
}

.ion-film-marker:before {
  content: "";
}

.ion-fireball:before {
  content: "";
}

.ion-flag:before {
  content: "";
}

.ion-flame:before {
  content: "";
}

.ion-flash:before {
  content: "";
}

.ion-flash-off:before {
  content: "";
}

.ion-flask:before {
  content: "";
}

.ion-folder:before {
  content: "";
}

.ion-fork:before {
  content: "";
}

.ion-fork-repo:before {
  content: "";
}

.ion-forward:before {
  content: "";
}

.ion-funnel:before {
  content: "";
}

.ion-game-controller-a:before {
  content: "";
}

.ion-game-controller-b:before {
  content: "";
}

.ion-gear-a:before {
  content: "";
}

.ion-gear-b:before {
  content: "";
}

.ion-grid:before {
  content: "";
}

.ion-hammer:before {
  content: "";
}

.ion-happy:before {
  content: "";
}

.ion-headphone:before {
  content: "";
}

.ion-heart:before {
  content: "";
}

.ion-heart-broken:before {
  content: "";
}

.ion-help:before {
  content: "";
}

.ion-help-buoy:before {
  content: "";
}

.ion-help-circled:before {
  content: "";
}

.ion-home:before {
  content: "";
}

.ion-icecream:before {
  content: "";
}

.ion-icon-social-google-plus:before {
  content: "";
}

.ion-icon-social-google-plus-outline:before {
  content: "";
}

.ion-image:before {
  content: "";
}

.ion-images:before {
  content: "";
}

.ion-information:before {
  content: "";
}

.ion-information-circled:before {
  content: "";
}

.ion-ionic:before {
  content: "";
}

.ion-ios7-alarm:before {
  content: "";
}

.ion-ios7-alarm-outline:before {
  content: "";
}

.ion-ios7-albums:before {
  content: "";
}

.ion-ios7-albums-outline:before {
  content: "";
}

.ion-ios7-americanfootball:before {
  content: "";
}

.ion-ios7-americanfootball-outline:before {
  content: "";
}

.ion-ios7-analytics:before {
  content: "";
}

.ion-ios7-analytics-outline:before {
  content: "";
}

.ion-ios7-arrow-back:before {
  content: "";
}

.ion-ios7-arrow-down:before {
  content: "";
}

.ion-ios7-arrow-forward:before {
  content: "";
}

.ion-ios7-arrow-left:before {
  content: "";
}

.ion-ios7-arrow-right:before {
  content: "";
}

.ion-ios7-arrow-thin-down:before {
  content: "";
}

.ion-ios7-arrow-thin-left:before {
  content: "";
}

.ion-ios7-arrow-thin-right:before {
  content: "";
}

.ion-ios7-arrow-thin-up:before {
  content: "";
}

.ion-ios7-arrow-up:before {
  content: "";
}

.ion-ios7-at:before {
  content: "";
}

.ion-ios7-at-outline:before {
  content: "";
}

.ion-ios7-barcode:before {
  content: "";
}

.ion-ios7-barcode-outline:before {
  content: "";
}

.ion-ios7-baseball:before {
  content: "";
}

.ion-ios7-baseball-outline:before {
  content: "";
}

.ion-ios7-basketball:before {
  content: "";
}

.ion-ios7-basketball-outline:before {
  content: "";
}

.ion-ios7-bell:before {
  content: "";
}

.ion-ios7-bell-outline:before {
  content: "";
}

.ion-ios7-bolt:before {
  content: "";
}

.ion-ios7-bolt-outline:before {
  content: "";
}

.ion-ios7-bookmarks:before {
  content: "";
}

.ion-ios7-bookmarks-outline:before {
  content: "";
}

.ion-ios7-box:before {
  content: "";
}

.ion-ios7-box-outline:before {
  content: "";
}

.ion-ios7-briefcase:before {
  content: "";
}

.ion-ios7-briefcase-outline:before {
  content: "";
}

.ion-ios7-browsers:before {
  content: "";
}

.ion-ios7-browsers-outline:before {
  content: "";
}

.ion-ios7-calculator:before {
  content: "";
}

.ion-ios7-calculator-outline:before {
  content: "";
}

.ion-ios7-calendar:before {
  content: "";
}

.ion-ios7-calendar-outline:before {
  content: "";
}

.ion-ios7-camera:before {
  content: "";
}

.ion-ios7-camera-outline:before {
  content: "";
}

.ion-ios7-cart:before {
  content: "";
}

.ion-ios7-cart-outline:before {
  content: "";
}

.ion-ios7-chatboxes:before {
  content: "";
}

.ion-ios7-chatboxes-outline:before {
  content: "";
}

.ion-ios7-chatbubble:before {
  content: "";
}

.ion-ios7-chatbubble-outline:before {
  content: "";
}

.ion-ios7-checkmark:before {
  content: "";
}

.ion-ios7-checkmark-empty:before {
  content: "";
}

.ion-ios7-checkmark-outline:before {
  content: "";
}

.ion-ios7-circle-filled:before {
  content: "";
}

.ion-ios7-circle-outline:before {
  content: "";
}

.ion-ios7-clock:before {
  content: "";
}

.ion-ios7-clock-outline:before {
  content: "";
}

.ion-ios7-close:before {
  content: "";
}

.ion-ios7-close-empty:before {
  content: "";
}

.ion-ios7-close-outline:before {
  content: "";
}

.ion-ios7-cloud:before {
  content: "";
}

.ion-ios7-cloud-download:before {
  content: "";
}

.ion-ios7-cloud-download-outline:before {
  content: "";
}

.ion-ios7-cloud-outline:before {
  content: "";
}

.ion-ios7-cloud-upload:before {
  content: "";
}

.ion-ios7-cloud-upload-outline:before {
  content: "";
}

.ion-ios7-cloudy:before {
  content: "";
}

.ion-ios7-cloudy-night:before {
  content: "";
}

.ion-ios7-cloudy-night-outline:before {
  content: "";
}

.ion-ios7-cloudy-outline:before {
  content: "";
}

.ion-ios7-cog:before {
  content: "";
}

.ion-ios7-cog-outline:before {
  content: "";
}

.ion-ios7-compose:before {
  content: "";
}

.ion-ios7-compose-outline:before {
  content: "";
}

.ion-ios7-contact:before {
  content: "";
}

.ion-ios7-contact-outline:before {
  content: "";
}

.ion-ios7-copy:before {
  content: "";
}

.ion-ios7-copy-outline:before {
  content: "";
}

.ion-ios7-download:before {
  content: "";
}

.ion-ios7-download-outline:before {
  content: "";
}

.ion-ios7-drag:before {
  content: "";
}

.ion-ios7-email:before {
  content: "";
}

.ion-ios7-email-outline:before {
  content: "";
}

.ion-ios7-expand:before {
  content: "";
}

.ion-ios7-eye:before {
  content: "";
}

.ion-ios7-eye-outline:before {
  content: "";
}

.ion-ios7-fastforward:before {
  content: "";
}

.ion-ios7-fastforward-outline:before {
  content: "";
}

.ion-ios7-filing:before {
  content: "";
}

.ion-ios7-filing-outline:before {
  content: "";
}

.ion-ios7-film:before {
  content: "";
}

.ion-ios7-film-outline:before {
  content: "";
}

.ion-ios7-flag:before {
  content: "";
}

.ion-ios7-flag-outline:before {
  content: "";
}

.ion-ios7-folder:before {
  content: "";
}

.ion-ios7-folder-outline:before {
  content: "";
}

.ion-ios7-football:before {
  content: "";
}

.ion-ios7-football-outline:before {
  content: "";
}

.ion-ios7-gear:before {
  content: "";
}

.ion-ios7-gear-outline:before {
  content: "";
}

.ion-ios7-glasses:before {
  content: "";
}

.ion-ios7-glasses-outline:before {
  content: "";
}

.ion-ios7-heart:before {
  content: "";
}

.ion-ios7-heart-outline:before {
  content: "";
}

.ion-ios7-help:before {
  content: "";
}

.ion-ios7-help-empty:before {
  content: "";
}

.ion-ios7-help-outline:before {
  content: "";
}

.ion-ios7-home:before {
  content: "";
}

.ion-ios7-home-outline:before {
  content: "";
}

.ion-ios7-infinite:before {
  content: "";
}

.ion-ios7-infinite-outline:before {
  content: "";
}

.ion-ios7-information:before {
  content: "";
}

.ion-ios7-information-empty:before {
  content: "";
}

.ion-ios7-information-outline:before {
  content: "";
}

.ion-ios7-ionic-outline:before {
  content: "";
}

.ion-ios7-keypad:before {
  content: "";
}

.ion-ios7-keypad-outline:before {
  content: "";
}

.ion-ios7-lightbulb:before {
  content: "";
}

.ion-ios7-lightbulb-outline:before {
  content: "";
}

.ion-ios7-location:before {
  content: "";
}

.ion-ios7-location-outline:before {
  content: "";
}

.ion-ios7-locked:before {
  content: "";
}

.ion-ios7-locked-outline:before {
  content: "";
}

.ion-ios7-loop:before {
  content: "";
}

.ion-ios7-loop-strong:before {
  content: "";
}

.ion-ios7-medkit:before {
  content: "";
}

.ion-ios7-medkit-outline:before {
  content: "";
}

.ion-ios7-mic:before {
  content: "";
}

.ion-ios7-mic-off:before {
  content: "";
}

.ion-ios7-mic-outline:before {
  content: "";
}

.ion-ios7-minus:before {
  content: "";
}

.ion-ios7-minus-empty:before {
  content: "";
}

.ion-ios7-minus-outline:before {
  content: "";
}

.ion-ios7-monitor:before {
  content: "";
}

.ion-ios7-monitor-outline:before {
  content: "";
}

.ion-ios7-moon:before {
  content: "";
}

.ion-ios7-moon-outline:before {
  content: "";
}

.ion-ios7-more:before {
  content: "";
}

.ion-ios7-more-outline:before {
  content: "";
}

.ion-ios7-musical-note:before {
  content: "";
}

.ion-ios7-musical-notes:before {
  content: "";
}

.ion-ios7-navigate:before {
  content: "";
}

.ion-ios7-navigate-outline:before {
  content: "";
}

.ion-ios7-paper:before {
  content: "";
}

.ion-ios7-paper-outline:before {
  content: "";
}

.ion-ios7-paperplane:before {
  content: "";
}

.ion-ios7-paperplane-outline:before {
  content: "";
}

.ion-ios7-partlysunny:before {
  content: "";
}

.ion-ios7-partlysunny-outline:before {
  content: "";
}

.ion-ios7-pause:before {
  content: "";
}

.ion-ios7-pause-outline:before {
  content: "";
}

.ion-ios7-paw:before {
  content: "";
}

.ion-ios7-paw-outline:before {
  content: "";
}

.ion-ios7-people:before {
  content: "";
}

.ion-ios7-people-outline:before {
  content: "";
}

.ion-ios7-person:before {
  content: "";
}

.ion-ios7-person-outline:before {
  content: "";
}

.ion-ios7-personadd:before {
  content: "";
}

.ion-ios7-personadd-outline:before {
  content: "";
}

.ion-ios7-photos:before {
  content: "";
}

.ion-ios7-photos-outline:before {
  content: "";
}

.ion-ios7-pie:before {
  content: "";
}

.ion-ios7-pie-outline:before {
  content: "";
}

.ion-ios7-play:before {
  content: "";
}

.ion-ios7-play-outline:before {
  content: "";
}

.ion-ios7-plus:before {
  content: "";
}

.ion-ios7-plus-empty:before {
  content: "";
}

.ion-ios7-plus-outline:before {
  content: "";
}

.ion-ios7-pricetag:before {
  content: "";
}

.ion-ios7-pricetag-outline:before {
  content: "";
}

.ion-ios7-pricetags:before {
  content: "";
}

.ion-ios7-pricetags-outline:before {
  content: "";
}

.ion-ios7-printer:before {
  content: "";
}

.ion-ios7-printer-outline:before {
  content: "";
}

.ion-ios7-pulse:before {
  content: "";
}

.ion-ios7-pulse-strong:before {
  content: "";
}

.ion-ios7-rainy:before {
  content: "";
}

.ion-ios7-rainy-outline:before {
  content: "";
}

.ion-ios7-recording:before {
  content: "";
}

.ion-ios7-recording-outline:before {
  content: "";
}

.ion-ios7-redo:before {
  content: "";
}

.ion-ios7-redo-outline:before {
  content: "";
}

.ion-ios7-refresh:before {
  content: "";
}

.ion-ios7-refresh-empty:before {
  content: "";
}

.ion-ios7-refresh-outline:before {
  content: "";
}

.ion-ios7-reload:before, .ion-ios7-reloading:before {
  content: "";
}

.ion-ios7-reverse-camera:before {
  content: "";
}

.ion-ios7-reverse-camera-outline:before {
  content: "";
}

.ion-ios7-rewind:before {
  content: "";
}

.ion-ios7-rewind-outline:before {
  content: "";
}

.ion-ios7-search:before {
  content: "";
}

.ion-ios7-search-strong:before {
  content: "";
}

.ion-ios7-settings:before {
  content: "";
}

.ion-ios7-settings-strong:before {
  content: "";
}

.ion-ios7-shrink:before {
  content: "";
}

.ion-ios7-skipbackward:before {
  content: "";
}

.ion-ios7-skipbackward-outline:before {
  content: "";
}

.ion-ios7-skipforward:before {
  content: "";
}

.ion-ios7-skipforward-outline:before {
  content: "";
}

.ion-ios7-snowy:before {
  content: "";
}

.ion-ios7-speedometer:before {
  content: "";
}

.ion-ios7-speedometer-outline:before {
  content: "";
}

.ion-ios7-star:before {
  content: "";
}

.ion-ios7-star-half:before {
  content: "";
}

.ion-ios7-star-outline:before {
  content: "";
}

.ion-ios7-stopwatch:before {
  content: "";
}

.ion-ios7-stopwatch-outline:before {
  content: "";
}

.ion-ios7-sunny:before {
  content: "";
}

.ion-ios7-sunny-outline:before {
  content: "";
}

.ion-ios7-telephone:before {
  content: "";
}

.ion-ios7-telephone-outline:before {
  content: "";
}

.ion-ios7-tennisball:before {
  content: "";
}

.ion-ios7-tennisball-outline:before {
  content: "";
}

.ion-ios7-thunderstorm:before {
  content: "";
}

.ion-ios7-thunderstorm-outline:before {
  content: "";
}

.ion-ios7-time:before {
  content: "";
}

.ion-ios7-time-outline:before {
  content: "";
}

.ion-ios7-timer:before {
  content: "";
}

.ion-ios7-timer-outline:before {
  content: "";
}

.ion-ios7-toggle:before {
  content: "";
}

.ion-ios7-toggle-outline:before {
  content: "";
}

.ion-ios7-trash:before {
  content: "";
}

.ion-ios7-trash-outline:before {
  content: "";
}

.ion-ios7-undo:before {
  content: "";
}

.ion-ios7-undo-outline:before {
  content: "";
}

.ion-ios7-unlocked:before {
  content: "";
}

.ion-ios7-unlocked-outline:before {
  content: "";
}

.ion-ios7-upload:before {
  content: "";
}

.ion-ios7-upload-outline:before {
  content: "";
}

.ion-ios7-videocam:before {
  content: "";
}

.ion-ios7-videocam-outline:before {
  content: "";
}

.ion-ios7-volume-high:before {
  content: "";
}

.ion-ios7-volume-low:before {
  content: "";
}

.ion-ios7-wineglass:before {
  content: "";
}

.ion-ios7-wineglass-outline:before {
  content: "";
}

.ion-ios7-world:before {
  content: "";
}

.ion-ios7-world-outline:before {
  content: "";
}

.ion-ipad:before {
  content: "";
}

.ion-iphone:before {
  content: "";
}

.ion-ipod:before {
  content: "";
}

.ion-jet:before {
  content: "";
}

.ion-key:before {
  content: "";
}

.ion-knife:before {
  content: "";
}

.ion-laptop:before {
  content: "";
}

.ion-leaf:before {
  content: "";
}

.ion-levels:before {
  content: "";
}

.ion-lightbulb:before {
  content: "";
}

.ion-link:before {
  content: "";
}

.ion-load-a:before, .ion-loading-a:before {
  content: "";
}

.ion-load-b:before, .ion-loading-b:before {
  content: "";
}

.ion-load-c:before, .ion-loading-c:before {
  content: "";
}

.ion-load-d:before, .ion-loading-d:before {
  content: "";
}

.ion-location:before {
  content: "";
}

.ion-locked:before {
  content: "";
}

.ion-log-in:before {
  content: "";
}

.ion-log-out:before {
  content: "";
}

.ion-loop:before, .ion-looping:before {
  content: "";
}

.ion-magnet:before {
  content: "";
}

.ion-male:before {
  content: "";
}

.ion-man:before {
  content: "";
}

.ion-map:before {
  content: "";
}

.ion-medkit:before {
  content: "";
}

.ion-merge:before {
  content: "";
}

.ion-mic-a:before {
  content: "";
}

.ion-mic-b:before {
  content: "";
}

.ion-mic-c:before {
  content: "";
}

.ion-minus:before {
  content: "";
}

.ion-minus-circled:before {
  content: "";
}

.ion-minus-round:before {
  content: "";
}

.ion-model-s:before {
  content: "";
}

.ion-monitor:before {
  content: "";
}

.ion-more:before {
  content: "";
}

.ion-mouse:before {
  content: "";
}

.ion-music-note:before {
  content: "";
}

.ion-navicon:before {
  content: "";
}

.ion-navicon-round:before {
  content: "";
}

.ion-navigate:before {
  content: "";
}

.ion-network:before {
  content: "";
}

.ion-no-smoking:before {
  content: "";
}

.ion-nuclear:before {
  content: "";
}

.ion-outlet:before {
  content: "";
}

.ion-paper-airplane:before {
  content: "";
}

.ion-paperclip:before {
  content: "";
}

.ion-pause:before {
  content: "";
}

.ion-person:before {
  content: "";
}

.ion-person-add:before {
  content: "";
}

.ion-person-stalker:before {
  content: "";
}

.ion-pie-graph:before {
  content: "";
}

.ion-pin:before {
  content: "";
}

.ion-pinpoint:before {
  content: "";
}

.ion-pizza:before {
  content: "";
}

.ion-plane:before {
  content: "";
}

.ion-planet:before {
  content: "";
}

.ion-play:before {
  content: "";
}

.ion-playstation:before {
  content: "";
}

.ion-plus:before {
  content: "";
}

.ion-plus-circled:before {
  content: "";
}

.ion-plus-round:before {
  content: "";
}

.ion-podium:before {
  content: "";
}

.ion-pound:before {
  content: "";
}

.ion-power:before {
  content: "";
}

.ion-pricetag:before {
  content: "";
}

.ion-pricetags:before {
  content: "";
}

.ion-printer:before {
  content: "";
}

.ion-pull-request:before {
  content: "";
}

.ion-qr-scanner:before {
  content: "";
}

.ion-quote:before {
  content: "";
}

.ion-radio-waves:before {
  content: "";
}

.ion-record:before {
  content: "";
}

.ion-refresh:before, .ion-refreshing:before {
  content: "";
}

.ion-reply:before {
  content: "";
}

.ion-reply-all:before {
  content: "";
}

.ion-ribbon-a:before {
  content: "";
}

.ion-ribbon-b:before {
  content: "";
}

.ion-sad:before {
  content: "";
}

.ion-scissors:before {
  content: "";
}

.ion-search:before {
  content: "";
}

.ion-settings:before {
  content: "";
}

.ion-share:before {
  content: "";
}

.ion-shuffle:before {
  content: "";
}

.ion-skip-backward:before {
  content: "";
}

.ion-skip-forward:before {
  content: "";
}

.ion-social-android:before {
  content: "";
}

.ion-social-android-outline:before {
  content: "";
}

.ion-social-apple:before {
  content: "";
}

.ion-social-apple-outline:before {
  content: "";
}

.ion-social-bitcoin:before {
  content: "";
}

.ion-social-bitcoin-outline:before {
  content: "";
}

.ion-social-buffer:before {
  content: "";
}

.ion-social-buffer-outline:before {
  content: "";
}

.ion-social-designernews:before {
  content: "";
}

.ion-social-designernews-outline:before {
  content: "";
}

.ion-social-dribbble:before {
  content: "";
}

.ion-social-dribbble-outline:before {
  content: "";
}

.ion-social-dropbox:before {
  content: "";
}

.ion-social-dropbox-outline:before {
  content: "";
}

.ion-social-facebook:before {
  content: "";
}

.ion-social-facebook-outline:before {
  content: "";
}

.ion-social-foursquare:before {
  content: "";
}

.ion-social-foursquare-outline:before {
  content: "";
}

.ion-social-freebsd-devil:before {
  content: "";
}

.ion-social-github:before {
  content: "";
}

.ion-social-github-outline:before {
  content: "";
}

.ion-social-google:before {
  content: "";
}

.ion-social-google-outline:before {
  content: "";
}

.ion-social-googleplus:before {
  content: "";
}

.ion-social-googleplus-outline:before {
  content: "";
}

.ion-social-hackernews:before {
  content: "";
}

.ion-social-hackernews-outline:before {
  content: "";
}

.ion-social-instagram:before {
  content: "";
}

.ion-social-instagram-outline:before {
  content: "";
}

.ion-social-linkedin:before {
  content: "";
}

.ion-social-linkedin-outline:before {
  content: "";
}

.ion-social-pinterest:before {
  content: "";
}

.ion-social-pinterest-outline:before {
  content: "";
}

.ion-social-reddit:before {
  content: "";
}

.ion-social-reddit-outline:before {
  content: "";
}

.ion-social-rss:before {
  content: "";
}

.ion-social-rss-outline:before {
  content: "";
}

.ion-social-skype:before {
  content: "";
}

.ion-social-skype-outline:before {
  content: "";
}

.ion-social-tumblr:before {
  content: "";
}

.ion-social-tumblr-outline:before {
  content: "";
}

.ion-social-tux:before {
  content: "";
}

.ion-social-twitter:before {
  content: "";
}

.ion-social-twitter-outline:before {
  content: "";
}

.ion-social-usd:before {
  content: "";
}

.ion-social-usd-outline:before {
  content: "";
}

.ion-social-vimeo:before {
  content: "";
}

.ion-social-vimeo-outline:before {
  content: "";
}

.ion-social-windows:before {
  content: "";
}

.ion-social-windows-outline:before {
  content: "";
}

.ion-social-wordpress:before {
  content: "";
}

.ion-social-wordpress-outline:before {
  content: "";
}

.ion-social-yahoo:before {
  content: "";
}

.ion-social-yahoo-outline:before {
  content: "";
}

.ion-social-youtube:before {
  content: "";
}

.ion-social-youtube-outline:before {
  content: "";
}

.ion-speakerphone:before {
  content: "";
}

.ion-speedometer:before {
  content: "";
}

.ion-spoon:before {
  content: "";
}

.ion-star:before {
  content: "";
}

.ion-stats-bars:before {
  content: "";
}

.ion-steam:before {
  content: "";
}

.ion-stop:before {
  content: "";
}

.ion-thermometer:before {
  content: "";
}

.ion-thumbsdown:before {
  content: "";
}

.ion-thumbsup:before {
  content: "";
}

.ion-toggle:before {
  content: "";
}

.ion-toggle-filled:before {
  content: "";
}

.ion-trash-a:before {
  content: "";
}

.ion-trash-b:before {
  content: "";
}

.ion-trophy:before {
  content: "";
}

.ion-umbrella:before {
  content: "";
}

.ion-university:before {
  content: "";
}

.ion-unlocked:before {
  content: "";
}

.ion-upload:before {
  content: "";
}

.ion-usb:before {
  content: "";
}

.ion-videocamera:before {
  content: "";
}

.ion-volume-high:before {
  content: "";
}

.ion-volume-low:before {
  content: "";
}

.ion-volume-medium:before {
  content: "";
}

.ion-volume-mute:before {
  content: "";
}

.ion-wand:before {
  content: "";
}

.ion-waterdrop:before {
  content: "";
}

.ion-wifi:before {
  content: "";
}

.ion-wineglass:before {
  content: "";
}

.ion-woman:before {
  content: "";
}

.ion-wrench:before {
  content: "";
}

.ion-xbox:before {
  content: "";
}

@font-face {
  font-family: 'themify';
  src: url("../fonts/themify9f24.eot?-fvbane");
  src: url("../fonts/themifyd41d.eot?#iefix-fvbane") format("embedded-opentype"), url("../fonts/themify9f24.woff?-fvbane") format("woff"), url("../fonts/themify9f24.ttf?-fvbane") format("truetype"), url("../fonts/themify9f24.svg?-fvbane#themify") format("svg");
  font-weight: normal;
  font-style: normal;
}

[class^="ti-"], [class*=" ti-"] {
  font-family: 'themify';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.ti-wand:before {
  content: "\e600";
}

.ti-volume:before {
  content: "\e601";
}

.ti-user:before {
  content: "\e602";
}

.ti-unlock:before {
  content: "\e603";
}

.ti-unlink:before {
  content: "\e604";
}

.ti-trash:before {
  content: "\e605";
}

.ti-thought:before {
  content: "\e606";
}

.ti-target:before {
  content: "\e607";
}

.ti-tag:before {
  content: "\e608";
}

.ti-tablet:before {
  content: "\e609";
}

.ti-star:before {
  content: "\e60a";
}

.ti-spray:before {
  content: "\e60b";
}

.ti-signal:before {
  content: "\e60c";
}

.ti-shopping-cart:before {
  content: "\e60d";
}

.ti-shopping-cart-full:before {
  content: "\e60e";
}

.ti-settings:before {
  content: "\e60f";
}

.ti-search:before {
  content: "\e610";
}

.ti-zoom-in:before {
  content: "\e611";
}

.ti-zoom-out:before {
  content: "\e612";
}

.ti-cut:before {
  content: "\e613";
}

.ti-ruler:before {
  content: "\e614";
}

.ti-ruler-pencil:before {
  content: "\e615";
}

.ti-ruler-alt:before {
  content: "\e616";
}

.ti-bookmark:before {
  content: "\e617";
}

.ti-bookmark-alt:before {
  content: "\e618";
}

.ti-reload:before {
  content: "\e619";
}

.ti-plus:before {
  content: "\e61a";
}

.ti-pin:before {
  content: "\e61b";
}

.ti-pencil:before {
  content: "\e61c";
}

.ti-pencil-alt:before {
  content: "\e61d";
}

.ti-paint-roller:before {
  content: "\e61e";
}

.ti-paint-bucket:before {
  content: "\e61f";
}

.ti-na:before {
  content: "\e620";
}

.ti-mobile:before {
  content: "\e621";
}

.ti-minus:before {
  content: "\e622";
}

.ti-medall:before {
  content: "\e623";
}

.ti-medall-alt:before {
  content: "\e624";
}

.ti-marker:before {
  content: "\e625";
}

.ti-marker-alt:before {
  content: "\e626";
}

.ti-arrow-up:before {
  content: "\e627";
}

.ti-arrow-right:before {
  content: "\e628";
}

.ti-arrow-left:before {
  content: "\e629";
}

.ti-arrow-down:before {
  content: "\e62a";
}

.ti-lock:before {
  content: "\e62b";
}

.ti-location-arrow:before {
  content: "\e62c";
}

.ti-link:before {
  content: "\e62d";
}

.ti-layout:before {
  content: "\e62e";
}

.ti-layers:before {
  content: "\e62f";
}

.ti-layers-alt:before {
  content: "\e630";
}

.ti-key:before {
  content: "\e631";
}

.ti-import:before {
  content: "\e632";
}

.ti-image:before {
  content: "\e633";
}

.ti-heart:before {
  content: "\e634";
}

.ti-heart-broken:before {
  content: "\e635";
}

.ti-hand-stop:before {
  content: "\e636";
}

.ti-hand-open:before {
  content: "\e637";
}

.ti-hand-drag:before {
  content: "\e638";
}

.ti-folder:before {
  content: "\e639";
}

.ti-flag:before {
  content: "\e63a";
}

.ti-flag-alt:before {
  content: "\e63b";
}

.ti-flag-alt-2:before {
  content: "\e63c";
}

.ti-eye:before {
  content: "\e63d";
}

.ti-export:before {
  content: "\e63e";
}

.ti-exchange-vertical:before {
  content: "\e63f";
}

.ti-desktop:before {
  content: "\e640";
}

.ti-cup:before {
  content: "\e641";
}

.ti-crown:before {
  content: "\e642";
}

.ti-comments:before {
  content: "\e643";
}

.ti-comment:before {
  content: "\e644";
}

.ti-comment-alt:before {
  content: "\e645";
}

.ti-close:before {
  content: "\e646";
}

.ti-clip:before {
  content: "\e647";
}

.ti-angle-up:before {
  content: "\e648";
}

.ti-angle-right:before {
  content: "\e649";
}

.ti-angle-left:before {
  content: "\e64a";
}

.ti-angle-down:before {
  content: "\e64b";
}

.ti-check:before {
  content: "\e64c";
}

.ti-check-box:before {
  content: "\e64d";
}

.ti-camera:before {
  content: "\e64e";
}

.ti-announcement:before {
  content: "\e64f";
}

.ti-brush:before {
  content: "\e650";
}

.ti-briefcase:before {
  content: "\e651";
}

.ti-bolt:before {
  content: "\e652";
}

.ti-bolt-alt:before {
  content: "\e653";
}

.ti-blackboard:before {
  content: "\e654";
}

.ti-bag:before {
  content: "\e655";
}

.ti-move:before {
  content: "\e656";
}

.ti-arrows-vertical:before {
  content: "\e657";
}

.ti-arrows-horizontal:before {
  content: "\e658";
}

.ti-fullscreen:before {
  content: "\e659";
}

.ti-arrow-top-right:before {
  content: "\e65a";
}

.ti-arrow-top-left:before {
  content: "\e65b";
}

.ti-arrow-circle-up:before {
  content: "\e65c";
}

.ti-arrow-circle-right:before {
  content: "\e65d";
}

.ti-arrow-circle-left:before {
  content: "\e65e";
}

.ti-arrow-circle-down:before {
  content: "\e65f";
}

.ti-angle-double-up:before {
  content: "\e660";
}

.ti-angle-double-right:before {
  content: "\e661";
}

.ti-angle-double-left:before {
  content: "\e662";
}

.ti-angle-double-down:before {
  content: "\e663";
}

.ti-zip:before {
  content: "\e664";
}

.ti-world:before {
  content: "\e665";
}

.ti-wheelchair:before {
  content: "\e666";
}

.ti-view-list:before {
  content: "\e667";
}

.ti-view-list-alt:before {
  content: "\e668";
}

.ti-view-grid:before {
  content: "\e669";
}

.ti-uppercase:before {
  content: "\e66a";
}

.ti-upload:before {
  content: "\e66b";
}

.ti-underline:before {
  content: "\e66c";
}

.ti-truck:before {
  content: "\e66d";
}

.ti-timer:before {
  content: "\e66e";
}

.ti-ticket:before {
  content: "\e66f";
}

.ti-thumb-up:before {
  content: "\e670";
}

.ti-thumb-down:before {
  content: "\e671";
}

.ti-text:before {
  content: "\e672";
}

.ti-stats-up:before {
  content: "\e673";
}

.ti-stats-down:before {
  content: "\e674";
}

.ti-split-v:before {
  content: "\e675";
}

.ti-split-h:before {
  content: "\e676";
}

.ti-smallcap:before {
  content: "\e677";
}

.ti-shine:before {
  content: "\e678";
}

.ti-shift-right:before {
  content: "\e679";
}

.ti-shift-left:before {
  content: "\e67a";
}

.ti-shield:before {
  content: "\e67b";
}

.ti-notepad:before {
  content: "\e67c";
}

.ti-server:before {
  content: "\e67d";
}

.ti-quote-right:before {
  content: "\e67e";
}

.ti-quote-left:before {
  content: "\e67f";
}

.ti-pulse:before {
  content: "\e680";
}

.ti-printer:before {
  content: "\e681";
}

.ti-power-off:before {
  content: "\e682";
}

.ti-plug:before {
  content: "\e683";
}

.ti-pie-chart:before {
  content: "\e684";
}

.ti-paragraph:before {
  content: "\e685";
}

.ti-panel:before {
  content: "\e686";
}

.ti-package:before {
  content: "\e687";
}

.ti-music:before {
  content: "\e688";
}

.ti-music-alt:before {
  content: "\e689";
}

.ti-mouse:before {
  content: "\e68a";
}

.ti-mouse-alt:before {
  content: "\e68b";
}

.ti-money:before {
  content: "\e68c";
}

.ti-microphone:before {
  content: "\e68d";
}

.ti-menu:before {
  content: "\e68e";
}

.ti-menu-alt:before {
  content: "\e68f";
}

.ti-map:before {
  content: "\e690";
}

.ti-map-alt:before {
  content: "\e691";
}

.ti-loop:before {
  content: "\e692";
}

.ti-location-pin:before {
  content: "\e693";
}

.ti-list:before {
  content: "\e694";
}

.ti-light-bulb:before {
  content: "\e695";
}

.ti-Italic:before {
  content: "\e696";
}

.ti-info:before {
  content: "\e697";
}

.ti-infinite:before {
  content: "\e698";
}

.ti-id-badge:before {
  content: "\e699";
}

.ti-hummer:before {
  content: "\e69a";
}

.ti-home:before {
  content: "\e69b";
}

.ti-help:before {
  content: "\e69c";
}

.ti-headphone:before {
  content: "\e69d";
}

.ti-harddrives:before {
  content: "\e69e";
}

.ti-harddrive:before {
  content: "\e69f";
}

.ti-gift:before {
  content: "\e6a0";
}

.ti-game:before {
  content: "\e6a1";
}

.ti-filter:before {
  content: "\e6a2";
}

.ti-files:before {
  content: "\e6a3";
}

.ti-file:before {
  content: "\e6a4";
}

.ti-eraser:before {
  content: "\e6a5";
}

.ti-envelope:before {
  content: "\e6a6";
}

.ti-download:before {
  content: "\e6a7";
}

.ti-direction:before {
  content: "\e6a8";
}

.ti-direction-alt:before {
  content: "\e6a9";
}

.ti-dashboard:before {
  content: "\e6aa";
}

.ti-control-stop:before {
  content: "\e6ab";
}

.ti-control-shuffle:before {
  content: "\e6ac";
}

.ti-control-play:before {
  content: "\e6ad";
}

.ti-control-pause:before {
  content: "\e6ae";
}

.ti-control-forward:before {
  content: "\e6af";
}

.ti-control-backward:before {
  content: "\e6b0";
}

.ti-cloud:before {
  content: "\e6b1";
}

.ti-cloud-up:before {
  content: "\e6b2";
}

.ti-cloud-down:before {
  content: "\e6b3";
}

.ti-clipboard:before {
  content: "\e6b4";
}

.ti-car:before {
  content: "\e6b5";
}

.ti-calendar:before {
  content: "\e6b6";
}

.ti-book:before {
  content: "\e6b7";
}

.ti-bell:before {
  content: "\e6b8";
}

.ti-basketball:before {
  content: "\e6b9";
}

.ti-bar-chart:before {
  content: "\e6ba";
}

.ti-bar-chart-alt:before {
  content: "\e6bb";
}

.ti-back-right:before {
  content: "\e6bc";
}

.ti-back-left:before {
  content: "\e6bd";
}

.ti-arrows-corner:before {
  content: "\e6be";
}

.ti-archive:before {
  content: "\e6bf";
}

.ti-anchor:before {
  content: "\e6c0";
}

.ti-align-right:before {
  content: "\e6c1";
}

.ti-align-left:before {
  content: "\e6c2";
}

.ti-align-justify:before {
  content: "\e6c3";
}

.ti-align-center:before {
  content: "\e6c4";
}

.ti-alert:before {
  content: "\e6c5";
}

.ti-alarm-clock:before {
  content: "\e6c6";
}

.ti-agenda:before {
  content: "\e6c7";
}

.ti-write:before {
  content: "\e6c8";
}

.ti-window:before {
  content: "\e6c9";
}

.ti-widgetized:before {
  content: "\e6ca";
}

.ti-widget:before {
  content: "\e6cb";
}

.ti-widget-alt:before {
  content: "\e6cc";
}

.ti-wallet:before {
  content: "\e6cd";
}

.ti-video-clapper:before {
  content: "\e6ce";
}

.ti-video-camera:before {
  content: "\e6cf";
}

.ti-vector:before {
  content: "\e6d0";
}

.ti-themify-logo:before {
  content: "\e6d1";
}

.ti-themify-favicon:before {
  content: "\e6d2";
}

.ti-themify-favicon-alt:before {
  content: "\e6d3";
}

.ti-support:before {
  content: "\e6d4";
}

.ti-stamp:before {
  content: "\e6d5";
}

.ti-split-v-alt:before {
  content: "\e6d6";
}

.ti-slice:before {
  content: "\e6d7";
}

.ti-shortcode:before {
  content: "\e6d8";
}

.ti-shift-right-alt:before {
  content: "\e6d9";
}

.ti-shift-left-alt:before {
  content: "\e6da";
}

.ti-ruler-alt-2:before {
  content: "\e6db";
}

.ti-receipt:before {
  content: "\e6dc";
}

.ti-pin2:before {
  content: "\e6dd";
}

.ti-pin-alt:before {
  content: "\e6de";
}

.ti-pencil-alt2:before {
  content: "\e6df";
}

.ti-palette:before {
  content: "\e6e0";
}

.ti-more:before {
  content: "\e6e1";
}

.ti-more-alt:before {
  content: "\e6e2";
}

.ti-microphone-alt:before {
  content: "\e6e3";
}

.ti-magnet:before {
  content: "\e6e4";
}

.ti-line-double:before {
  content: "\e6e5";
}

.ti-line-dotted:before {
  content: "\e6e6";
}

.ti-line-dashed:before {
  content: "\e6e7";
}

.ti-layout-width-full:before {
  content: "\e6e8";
}

.ti-layout-width-default:before {
  content: "\e6e9";
}

.ti-layout-width-default-alt:before {
  content: "\e6ea";
}

.ti-layout-tab:before {
  content: "\e6eb";
}

.ti-layout-tab-window:before {
  content: "\e6ec";
}

.ti-layout-tab-v:before {
  content: "\e6ed";
}

.ti-layout-tab-min:before {
  content: "\e6ee";
}

.ti-layout-slider:before {
  content: "\e6ef";
}

.ti-layout-slider-alt:before {
  content: "\e6f0";
}

.ti-layout-sidebar-right:before {
  content: "\e6f1";
}

.ti-layout-sidebar-none:before {
  content: "\e6f2";
}

.ti-layout-sidebar-left:before {
  content: "\e6f3";
}

.ti-layout-placeholder:before {
  content: "\e6f4";
}

.ti-layout-menu:before {
  content: "\e6f5";
}

.ti-layout-menu-v:before {
  content: "\e6f6";
}

.ti-layout-menu-separated:before {
  content: "\e6f7";
}

.ti-layout-menu-full:before {
  content: "\e6f8";
}

.ti-layout-media-right-alt:before {
  content: "\e6f9";
}

.ti-layout-media-right:before {
  content: "\e6fa";
}

.ti-layout-media-overlay:before {
  content: "\e6fb";
}

.ti-layout-media-overlay-alt:before {
  content: "\e6fc";
}

.ti-layout-media-overlay-alt-2:before {
  content: "\e6fd";
}

.ti-layout-media-left-alt:before {
  content: "\e6fe";
}

.ti-layout-media-left:before {
  content: "\e6ff";
}

.ti-layout-media-center-alt:before {
  content: "\e700";
}

.ti-layout-media-center:before {
  content: "\e701";
}

.ti-layout-list-thumb:before {
  content: "\e702";
}

.ti-layout-list-thumb-alt:before {
  content: "\e703";
}

.ti-layout-list-post:before {
  content: "\e704";
}

.ti-layout-list-large-image:before {
  content: "\e705";
}

.ti-layout-line-solid:before {
  content: "\e706";
}

.ti-layout-grid4:before {
  content: "\e707";
}

.ti-layout-grid3:before {
  content: "\e708";
}

.ti-layout-grid2:before {
  content: "\e709";
}

.ti-layout-grid2-thumb:before {
  content: "\e70a";
}

.ti-layout-cta-right:before {
  content: "\e70b";
}

.ti-layout-cta-left:before {
  content: "\e70c";
}

.ti-layout-cta-center:before {
  content: "\e70d";
}

.ti-layout-cta-btn-right:before {
  content: "\e70e";
}

.ti-layout-cta-btn-left:before {
  content: "\e70f";
}

.ti-layout-column4:before {
  content: "\e710";
}

.ti-layout-column3:before {
  content: "\e711";
}

.ti-layout-column2:before {
  content: "\e712";
}

.ti-layout-accordion-separated:before {
  content: "\e713";
}

.ti-layout-accordion-merged:before {
  content: "\e714";
}

.ti-layout-accordion-list:before {
  content: "\e715";
}

.ti-ink-pen:before {
  content: "\e716";
}

.ti-info-alt:before {
  content: "\e717";
}

.ti-help-alt:before {
  content: "\e718";
}

.ti-headphone-alt:before {
  content: "\e719";
}

.ti-hand-point-up:before {
  content: "\e71a";
}

.ti-hand-point-right:before {
  content: "\e71b";
}

.ti-hand-point-left:before {
  content: "\e71c";
}

.ti-hand-point-down:before {
  content: "\e71d";
}

.ti-gallery:before {
  content: "\e71e";
}

.ti-face-smile:before {
  content: "\e71f";
}

.ti-face-sad:before {
  content: "\e720";
}

.ti-credit-card:before {
  content: "\e721";
}

.ti-control-skip-forward:before {
  content: "\e722";
}

.ti-control-skip-backward:before {
  content: "\e723";
}

.ti-control-record:before {
  content: "\e724";
}

.ti-control-eject:before {
  content: "\e725";
}

.ti-comments-smiley:before {
  content: "\e726";
}

.ti-brush-alt:before {
  content: "\e727";
}

.ti-youtube:before {
  content: "\e728";
}

.ti-vimeo:before {
  content: "\e729";
}

.ti-twitter:before {
  content: "\e72a";
}

.ti-time:before {
  content: "\e72b";
}

.ti-tumblr:before {
  content: "\e72c";
}

.ti-skype:before {
  content: "\e72d";
}

.ti-share:before {
  content: "\e72e";
}

.ti-share-alt:before {
  content: "\e72f";
}

.ti-rocket:before {
  content: "\e730";
}

.ti-pinterest:before {
  content: "\e731";
}

.ti-new-window:before {
  content: "\e732";
}

.ti-microsoft:before {
  content: "\e733";
}

.ti-list-ol:before {
  content: "\e734";
}

.ti-linkedin:before {
  content: "\e735";
}

.ti-layout-sidebar-2:before {
  content: "\e736";
}

.ti-layout-grid4-alt:before {
  content: "\e737";
}

.ti-layout-grid3-alt:before {
  content: "\e738";
}

.ti-layout-grid2-alt:before {
  content: "\e739";
}

.ti-layout-column4-alt:before {
  content: "\e73a";
}

.ti-layout-column3-alt:before {
  content: "\e73b";
}

.ti-layout-column2-alt:before {
  content: "\e73c";
}

.ti-instagram:before {
  content: "\e73d";
}

.ti-google:before {
  content: "\e73e";
}

.ti-github:before {
  content: "\e73f";
}

.ti-flickr:before {
  content: "\e740";
}

.ti-facebook:before {
  content: "\e741";
}

.ti-dropbox:before {
  content: "\e742";
}

.ti-dribbble:before {
  content: "\e743";
}

.ti-apple:before {
  content: "\e744";
}

.ti-android:before {
  content: "\e745";
}

.ti-save:before {
  content: "\e746";
}

.ti-save-alt:before {
  content: "\e747";
}

.ti-yahoo:before {
  content: "\e748";
}

.ti-wordpress:before {
  content: "\e749";
}

.ti-vimeo-alt:before {
  content: "\e74a";
}

.ti-twitter-alt:before {
  content: "\e74b";
}

.ti-tumblr-alt:before {
  content: "\e74c";
}

.ti-trello:before {
  content: "\e74d";
}

.ti-stack-overflow:before {
  content: "\e74e";
}

.ti-soundcloud:before {
  content: "\e74f";
}

.ti-sharethis:before {
  content: "\e750";
}

.ti-sharethis-alt:before {
  content: "\e751";
}

.ti-reddit:before {
  content: "\e752";
}

.ti-pinterest-alt:before {
  content: "\e753";
}

.ti-microsoft-alt:before {
  content: "\e754";
}

.ti-linux:before {
  content: "\e755";
}

.ti-jsfiddle:before {
  content: "\e756";
}

.ti-joomla:before {
  content: "\e757";
}

.ti-html5:before {
  content: "\e758";
}

.ti-flickr-alt:before {
  content: "\e759";
}

.ti-email:before {
  content: "\e75a";
}

.ti-drupal:before {
  content: "\e75b";
}

.ti-dropbox-alt:before {
  content: "\e75c";
}

.ti-css3:before {
  content: "\e75d";
}

.ti-rss:before {
  content: "\e75e";
}

.ti-rss-alt:before {
  content: "\e75f";
}

@font-face {
  font-family: "dripicons-v2";
  src: url("../fonts/dripicons-v2.eot");
  src: url("../fonts/dripicons-v2d41d.eot?#iefix") format("embedded-opentype"), url("../fonts/dripicons-v2.woff") format("woff"), url("../fonts/dripicons-v2.ttf") format("truetype"), url("../fonts/dripicons-v2.svg#dripicons-v2") format("svg");
  font-weight: normal;
  font-style: normal;
}

[data-icon]:before {
  font-family: "dripicons-v2" !important;
  content: attr(data-icon);
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

[class^="dripicons-"]:before,
[class*=" dripicons-"]:before {
  font-family: "dripicons-v2" !important;
  font-style: normal !important;
  font-weight: normal !important;
  font-variant: normal !important;
  text-transform: none !important;
  speak: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dripicons-alarm:before {
  content: "\61";
}

.dripicons-align-center:before {
  content: "\62";
}

.dripicons-align-justify:before {
  content: "\63";
}

.dripicons-align-left:before {
  content: "\64";
}

.dripicons-align-right:before {
  content: "\65";
}

.dripicons-anchor:before {
  content: "\66";
}

.dripicons-archive:before {
  content: "\67";
}

.dripicons-arrow-down:before {
  content: "\68";
}

.dripicons-arrow-left:before {
  content: "\69";
}

.dripicons-arrow-right:before {
  content: "\6a";
}

.dripicons-arrow-thin-down:before {
  content: "\6b";
}

.dripicons-arrow-thin-left:before {
  content: "\6c";
}

.dripicons-arrow-thin-right:before {
  content: "\6d";
}

.dripicons-arrow-thin-up:before {
  content: "\6e";
}

.dripicons-arrow-up:before {
  content: "\6f";
}

.dripicons-article:before {
  content: "\70";
}

.dripicons-backspace:before {
  content: "\71";
}

.dripicons-basket:before {
  content: "\72";
}

.dripicons-basketball:before {
  content: "\73";
}

.dripicons-battery-empty:before {
  content: "\74";
}

.dripicons-battery-full:before {
  content: "\75";
}

.dripicons-battery-low:before {
  content: "\76";
}

.dripicons-battery-medium:before {
  content: "\77";
}

.dripicons-bell:before {
  content: "\78";
}

.dripicons-blog:before {
  content: "\79";
}

.dripicons-bluetooth:before {
  content: "\7a";
}

.dripicons-bold:before {
  content: "\41";
}

.dripicons-bookmark:before {
  content: "\42";
}

.dripicons-bookmarks:before {
  content: "\43";
}

.dripicons-box:before {
  content: "\44";
}

.dripicons-briefcase:before {
  content: "\45";
}

.dripicons-brightness-low:before {
  content: "\46";
}

.dripicons-brightness-max:before {
  content: "\47";
}

.dripicons-brightness-medium:before {
  content: "\48";
}

.dripicons-broadcast:before {
  content: "\49";
}

.dripicons-browser:before {
  content: "\4a";
}

.dripicons-browser-upload:before {
  content: "\4b";
}

.dripicons-brush:before {
  content: "\4c";
}

.dripicons-calendar:before {
  content: "\4d";
}

.dripicons-camcorder:before {
  content: "\4e";
}

.dripicons-camera:before {
  content: "\4f";
}

.dripicons-card:before {
  content: "\50";
}

.dripicons-cart:before {
  content: "\51";
}

.dripicons-checklist:before {
  content: "\52";
}

.dripicons-checkmark:before {
  content: "\53";
}

.dripicons-chevron-down:before {
  content: "\54";
}

.dripicons-chevron-left:before {
  content: "\55";
}

.dripicons-chevron-right:before {
  content: "\56";
}

.dripicons-chevron-up:before {
  content: "\57";
}

.dripicons-clipboard:before {
  content: "\58";
}

.dripicons-clock:before {
  content: "\59";
}

.dripicons-clockwise:before {
  content: "\5a";
}

.dripicons-cloud:before {
  content: "\30";
}

.dripicons-cloud-download:before {
  content: "\31";
}

.dripicons-cloud-upload:before {
  content: "\32";
}

.dripicons-code:before {
  content: "\33";
}

.dripicons-contract:before {
  content: "\34";
}

.dripicons-contract-2:before {
  content: "\35";
}

.dripicons-conversation:before {
  content: "\36";
}

.dripicons-copy:before {
  content: "\37";
}

.dripicons-crop:before {
  content: "\38";
}

.dripicons-cross:before {
  content: "\39";
}

.dripicons-crosshair:before {
  content: "\21";
}

.dripicons-cutlery:before {
  content: "\22";
}

.dripicons-device-desktop:before {
  content: "\23";
}

.dripicons-device-mobile:before {
  content: "\24";
}

.dripicons-device-tablet:before {
  content: "\25";
}

.dripicons-direction:before {
  content: "\26";
}

.dripicons-disc:before {
  content: "\27";
}

.dripicons-document:before {
  content: "\28";
}

.dripicons-document-delete:before {
  content: "\29";
}

.dripicons-document-edit:before {
  content: "\2a";
}

.dripicons-document-new:before {
  content: "\2b";
}

.dripicons-document-remove:before {
  content: "\2c";
}

.dripicons-dot:before {
  content: "\2d";
}

.dripicons-dots-2:before {
  content: "\2e";
}

.dripicons-dots-3:before {
  content: "\2f";
}

.dripicons-download:before {
  content: "\3a";
}

.dripicons-duplicate:before {
  content: "\3b";
}

.dripicons-enter:before {
  content: "\3c";
}

.dripicons-exit:before {
  content: "\3d";
}

.dripicons-expand:before {
  content: "\3e";
}

.dripicons-expand-2:before {
  content: "\3f";
}

.dripicons-experiment:before {
  content: "\40";
}

.dripicons-export:before {
  content: "\5b";
}

.dripicons-feed:before {
  content: "\5d";
}

.dripicons-flag:before {
  content: "\5e";
}

.dripicons-flashlight:before {
  content: "\5f";
}

.dripicons-folder:before {
  content: "\60";
}

.dripicons-folder-open:before {
  content: "\7b";
}

.dripicons-forward:before {
  content: "\7c";
}

.dripicons-gaming:before {
  content: "\7d";
}

.dripicons-gear:before {
  content: "\7e";
}

.dripicons-graduation:before {
  content: "\5c";
}

.dripicons-graph-bar:before {
  content: "\e000";
}

.dripicons-graph-line:before {
  content: "\e001";
}

.dripicons-graph-pie:before {
  content: "\e002";
}

.dripicons-headset:before {
  content: "\e003";
}

.dripicons-heart:before {
  content: "\e004";
}

.dripicons-help:before {
  content: "\e005";
}

.dripicons-home:before {
  content: "\e006";
}

.dripicons-hourglass:before {
  content: "\e007";
}

.dripicons-inbox:before {
  content: "\e008";
}

.dripicons-information:before {
  content: "\e009";
}

.dripicons-italic:before {
  content: "\e00a";
}

.dripicons-jewel:before {
  content: "\e00b";
}

.dripicons-lifting:before {
  content: "\e00c";
}

.dripicons-lightbulb:before {
  content: "\e00d";
}

.dripicons-link:before {
  content: "\e00e";
}

.dripicons-link-broken:before {
  content: "\e00f";
}

.dripicons-list:before {
  content: "\e010";
}

.dripicons-loading:before {
  content: "\e011";
}

.dripicons-location:before {
  content: "\e012";
}

.dripicons-lock:before {
  content: "\e013";
}

.dripicons-lock-open:before {
  content: "\e014";
}

.dripicons-mail:before {
  content: "\e015";
}

.dripicons-map:before {
  content: "\e016";
}

.dripicons-media-loop:before {
  content: "\e017";
}

.dripicons-media-next:before {
  content: "\e018";
}

.dripicons-media-pause:before {
  content: "\e019";
}

.dripicons-media-play:before {
  content: "\e01a";
}

.dripicons-media-previous:before {
  content: "\e01b";
}

.dripicons-media-record:before {
  content: "\e01c";
}

.dripicons-media-shuffle:before {
  content: "\e01d";
}

.dripicons-media-stop:before {
  content: "\e01e";
}

.dripicons-medical:before {
  content: "\e01f";
}

.dripicons-menu:before {
  content: "\e020";
}

.dripicons-message:before {
  content: "\e021";
}

.dripicons-meter:before {
  content: "\e022";
}

.dripicons-microphone:before {
  content: "\e023";
}

.dripicons-minus:before {
  content: "\e024";
}

.dripicons-monitor:before {
  content: "\e025";
}

.dripicons-move:before {
  content: "\e026";
}

.dripicons-music:before {
  content: "\e027";
}

.dripicons-network-1:before {
  content: "\e028";
}

.dripicons-network-2:before {
  content: "\e029";
}

.dripicons-network-3:before {
  content: "\e02a";
}

.dripicons-network-4:before {
  content: "\e02b";
}

.dripicons-network-5:before {
  content: "\e02c";
}

.dripicons-pamphlet:before {
  content: "\e02d";
}

.dripicons-paperclip:before {
  content: "\e02e";
}

.dripicons-pencil:before {
  content: "\e02f";
}

.dripicons-phone:before {
  content: "\e030";
}

.dripicons-photo:before {
  content: "\e031";
}

.dripicons-photo-group:before {
  content: "\e032";
}

.dripicons-pill:before {
  content: "\e033";
}

.dripicons-pin:before {
  content: "\e034";
}

.dripicons-plus:before {
  content: "\e035";
}

.dripicons-power:before {
  content: "\e036";
}

.dripicons-preview:before {
  content: "\e037";
}

.dripicons-print:before {
  content: "\e038";
}

.dripicons-pulse:before {
  content: "\e039";
}

.dripicons-question:before {
  content: "\e03a";
}

.dripicons-reply:before {
  content: "\e03b";
}

.dripicons-reply-all:before {
  content: "\e03c";
}

.dripicons-return:before {
  content: "\e03d";
}

.dripicons-retweet:before {
  content: "\e03e";
}

.dripicons-rocket:before {
  content: "\e03f";
}

.dripicons-scale:before {
  content: "\e040";
}

.dripicons-search:before {
  content: "\e041";
}

.dripicons-shopping-bag:before {
  content: "\e042";
}

.dripicons-skip:before {
  content: "\e043";
}

.dripicons-stack:before {
  content: "\e044";
}

.dripicons-star:before {
  content: "\e045";
}

.dripicons-stopwatch:before {
  content: "\e046";
}

.dripicons-store:before {
  content: "\e047";
}

.dripicons-suitcase:before {
  content: "\e048";
}

.dripicons-swap:before {
  content: "\e049";
}

.dripicons-tag:before {
  content: "\e04a";
}

.dripicons-tag-delete:before {
  content: "\e04b";
}

.dripicons-tags:before {
  content: "\e04c";
}

.dripicons-thumbs-down:before {
  content: "\e04d";
}

.dripicons-thumbs-up:before {
  content: "\e04e";
}

.dripicons-ticket:before {
  content: "\e04f";
}

.dripicons-time-reverse:before {
  content: "\e050";
}

.dripicons-to-do:before {
  content: "\e051";
}

.dripicons-toggles:before {
  content: "\e052";
}

.dripicons-trash:before {
  content: "\e053";
}

.dripicons-trophy:before {
  content: "\e054";
}

.dripicons-upload:before {
  content: "\e055";
}

.dripicons-user:before {
  content: "\e056";
}

.dripicons-user-group:before {
  content: "\e057";
}

.dripicons-user-id:before {
  content: "\e058";
}

.dripicons-vibrate:before {
  content: "\e059";
}

.dripicons-view-apps:before {
  content: "\e05a";
}

.dripicons-view-list:before {
  content: "\e05b";
}

.dripicons-view-list-large:before {
  content: "\e05c";
}

.dripicons-view-thumb:before {
  content: "\e05d";
}

.dripicons-volume-full:before {
  content: "\e05e";
}

.dripicons-volume-low:before {
  content: "\e05f";
}

.dripicons-volume-medium:before {
  content: "\e060";
}

.dripicons-volume-off:before {
  content: "\e061";
}

.dripicons-wallet:before {
  content: "\e062";
}

.dripicons-warning:before {
  content: "\e063";
}

.dripicons-web:before {
  content: "\e064";
}

.dripicons-weight:before {
  content: "\e065";
}

.dripicons-wifi:before {
  content: "\e066";
}

.dripicons-wrong:before {
  content: "\e067";
}

.dripicons-zoom-in:before {
  content: "\e068";
}

.dripicons-zoom-out:before {
  content: "\e069";
}

/* @FONT-FACE loads font into browser */
@font-face {
  font-family: 'typicons';
  font-weight: normal;
  font-style: normal;
  src: url("../fonts/typicons.eot");
  src: url("../fonts/typiconsd41d.eot?#iefix") format("embedded-opentype"), url("../fonts/typicons.woff") format("woff"), url("../fonts/typicons.ttf") format("truetype"), url("../fonts/typicons.svg#typicons") format("svg");
}

/* :before psuedo-selector inserts and styles icon */
.typcn:before {
  font-family: 'typicons';
  font-style: normal;
  font-weight: normal;
  speak: none;
  display: inline-block;
  text-decoration: inherit;
  width: 1em;
  height: 1em;
  font-size: 1em;
  text-align: center;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
}

/* Code for individual icons */
.typcn-adjust-brightness:before {
  content: '\e000';
  /* '' */
}

.typcn-adjust-contrast:before {
  content: '\e001';
  /* '' */
}

.typcn-anchor-outline:before {
  content: '\e002';
  /* '' */
}

.typcn-anchor:before {
  content: '\e003';
  /* '' */
}

.typcn-archive:before {
  content: '\e004';
  /* '' */
}

.typcn-arrow-back-outline:before {
  content: '\e005';
  /* '' */
}

.typcn-arrow-back:before {
  content: '\e006';
  /* '' */
}

.typcn-arrow-down-outline:before {
  content: '\e007';
  /* '' */
}

.typcn-arrow-down-thick:before {
  content: '\e008';
  /* '' */
}

.typcn-arrow-down:before {
  content: '\e009';
  /* '' */
}

.typcn-arrow-forward-outline:before {
  content: '\e00a';
  /* '' */
}

.typcn-arrow-forward:before {
  content: '\e00b';
  /* '' */
}

.typcn-arrow-left-outline:before {
  content: '\e00c';
  /* '' */
}

.typcn-arrow-left-thick:before {
  content: '\e00d';
  /* '' */
}

.typcn-arrow-left:before {
  content: '\e00e';
  /* '' */
}

.typcn-arrow-loop-outline:before {
  content: '\e00f';
  /* '' */
}

.typcn-arrow-loop:before {
  content: '\e010';
  /* '' */
}

.typcn-arrow-maximise-outline:before {
  content: '\e011';
  /* '' */
}

.typcn-arrow-maximise:before {
  content: '\e012';
  /* '' */
}

.typcn-arrow-minimise-outline:before {
  content: '\e013';
  /* '' */
}

.typcn-arrow-minimise:before {
  content: '\e014';
  /* '' */
}

.typcn-arrow-move-outline:before {
  content: '\e015';
  /* '' */
}

.typcn-arrow-move:before {
  content: '\e016';
  /* '' */
}

.typcn-arrow-repeat-outline:before {
  content: '\e017';
  /* '' */
}

.typcn-arrow-repeat:before {
  content: '\e018';
  /* '' */
}

.typcn-arrow-right-outline:before {
  content: '\e019';
  /* '' */
}

.typcn-arrow-right-thick:before {
  content: '\e01a';
  /* '' */
}

.typcn-arrow-right:before {
  content: '\e01b';
  /* '' */
}

.typcn-arrow-shuffle:before {
  content: '\e01c';
  /* '' */
}

.typcn-arrow-sorted-down:before {
  content: '\e01d';
  /* '' */
}

.typcn-arrow-sorted-up:before {
  content: '\e01e';
  /* '' */
}

.typcn-arrow-sync-outline:before {
  content: '\e01f';
  /* '' */
}

.typcn-arrow-sync:before {
  content: '\e020';
  /* '' */
}

.typcn-arrow-unsorted:before {
  content: '\e021';
  /* '' */
}

.typcn-arrow-up-outline:before {
  content: '\e022';
  /* '' */
}

.typcn-arrow-up-thick:before {
  content: '\e023';
  /* '' */
}

.typcn-arrow-up:before {
  content: '\e024';
  /* '' */
}

.typcn-at:before {
  content: '\e025';
  /* '' */
}

.typcn-attachment-outline:before {
  content: '\e026';
  /* '' */
}

.typcn-attachment:before {
  content: '\e027';
  /* '' */
}

.typcn-backspace-outline:before {
  content: '\e028';
  /* '' */
}

.typcn-backspace:before {
  content: '\e029';
  /* '' */
}

.typcn-battery-charge:before {
  content: '\e02a';
  /* '' */
}

.typcn-battery-full:before {
  content: '\e02b';
  /* '' */
}

.typcn-battery-high:before {
  content: '\e02c';
  /* '' */
}

.typcn-battery-low:before {
  content: '\e02d';
  /* '' */
}

.typcn-battery-mid:before {
  content: '\e02e';
  /* '' */
}

.typcn-beaker:before {
  content: '\e02f';
  /* '' */
}

.typcn-beer:before {
  content: '\e030';
  /* '' */
}

.typcn-bell:before {
  content: '\e031';
  /* '' */
}

.typcn-book:before {
  content: '\e032';
  /* '' */
}

.typcn-bookmark:before {
  content: '\e033';
  /* '' */
}

.typcn-briefcase:before {
  content: '\e034';
  /* '' */
}

.typcn-brush:before {
  content: '\e035';
  /* '' */
}

.typcn-business-card:before {
  content: '\e036';
  /* '' */
}

.typcn-calculator:before {
  content: '\e037';
  /* '' */
}

.typcn-calendar-outline:before {
  content: '\e038';
  /* '' */
}

.typcn-calendar:before {
  content: '\e039';
  /* '' */
}

.typcn-camera-outline:before {
  content: '\e03a';
  /* '' */
}

.typcn-camera:before {
  content: '\e03b';
  /* '' */
}

.typcn-cancel-outline:before {
  content: '\e03c';
  /* '' */
}

.typcn-cancel:before {
  content: '\e03d';
  /* '' */
}

.typcn-chart-area-outline:before {
  content: '\e03e';
  /* '' */
}

.typcn-chart-area:before {
  content: '\e03f';
  /* '' */
}

.typcn-chart-bar-outline:before {
  content: '\e040';
  /* '' */
}

.typcn-chart-bar:before {
  content: '\e041';
  /* '' */
}

.typcn-chart-line-outline:before {
  content: '\e042';
  /* '' */
}

.typcn-chart-line:before {
  content: '\e043';
  /* '' */
}

.typcn-chart-pie-outline:before {
  content: '\e044';
  /* '' */
}

.typcn-chart-pie:before {
  content: '\e045';
  /* '' */
}

.typcn-chevron-left-outline:before {
  content: '\e046';
  /* '' */
}

.typcn-chevron-left:before {
  content: '\e047';
  /* '' */
}

.typcn-chevron-right-outline:before {
  content: '\e048';
  /* '' */
}

.typcn-chevron-right:before {
  content: '\e049';
  /* '' */
}

.typcn-clipboard:before {
  content: '\e04a';
  /* '' */
}

.typcn-cloud-storage:before {
  content: '\e04b';
  /* '' */
}

.typcn-cloud-storage-outline:before {
  content: '\e054';
  /* '' */
}

.typcn-code-outline:before {
  content: '\e04c';
  /* '' */
}

.typcn-code:before {
  content: '\e04d';
  /* '' */
}

.typcn-coffee:before {
  content: '\e04e';
  /* '' */
}

.typcn-cog-outline:before {
  content: '\e04f';
  /* '' */
}

.typcn-cog:before {
  content: '\e050';
  /* '' */
}

.typcn-compass:before {
  content: '\e051';
  /* '' */
}

.typcn-contacts:before {
  content: '\e052';
  /* '' */
}

.typcn-credit-card:before {
  content: '\e053';
  /* '' */
}

.typcn-css3:before {
  content: '\e055';
  /* '' */
}

.typcn-database:before {
  content: '\e056';
  /* '' */
}

.typcn-delete-outline:before {
  content: '\e057';
  /* '' */
}

.typcn-delete:before {
  content: '\e058';
  /* '' */
}

.typcn-device-desktop:before {
  content: '\e059';
  /* '' */
}

.typcn-device-laptop:before {
  content: '\e05a';
  /* '' */
}

.typcn-device-phone:before {
  content: '\e05b';
  /* '' */
}

.typcn-device-tablet:before {
  content: '\e05c';
  /* '' */
}

.typcn-directions:before {
  content: '\e05d';
  /* '' */
}

.typcn-divide-outline:before {
  content: '\e05e';
  /* '' */
}

.typcn-divide:before {
  content: '\e05f';
  /* '' */
}

.typcn-document-add:before {
  content: '\e060';
  /* '' */
}

.typcn-document-delete:before {
  content: '\e061';
  /* '' */
}

.typcn-document-text:before {
  content: '\e062';
  /* '' */
}

.typcn-document:before {
  content: '\e063';
  /* '' */
}

.typcn-download-outline:before {
  content: '\e064';
  /* '' */
}

.typcn-download:before {
  content: '\e065';
  /* '' */
}

.typcn-dropbox:before {
  content: '\e066';
  /* '' */
}

.typcn-edit:before {
  content: '\e067';
  /* '' */
}

.typcn-eject-outline:before {
  content: '\e068';
  /* '' */
}

.typcn-eject:before {
  content: '\e069';
  /* '' */
}

.typcn-equals-outline:before {
  content: '\e06a';
  /* '' */
}

.typcn-equals:before {
  content: '\e06b';
  /* '' */
}

.typcn-export-outline:before {
  content: '\e06c';
  /* '' */
}

.typcn-export:before {
  content: '\e06d';
  /* '' */
}

.typcn-eye-outline:before {
  content: '\e06e';
  /* '' */
}

.typcn-eye:before {
  content: '\e06f';
  /* '' */
}

.typcn-feather:before {
  content: '\e070';
  /* '' */
}

.typcn-film:before {
  content: '\e071';
  /* '' */
}

.typcn-filter:before {
  content: '\e072';
  /* '' */
}

.typcn-flag-outline:before {
  content: '\e073';
  /* '' */
}

.typcn-flag:before {
  content: '\e074';
  /* '' */
}

.typcn-flash-outline:before {
  content: '\e075';
  /* '' */
}

.typcn-flash:before {
  content: '\e076';
  /* '' */
}

.typcn-flow-children:before {
  content: '\e077';
  /* '' */
}

.typcn-flow-merge:before {
  content: '\e078';
  /* '' */
}

.typcn-flow-parallel:before {
  content: '\e079';
  /* '' */
}

.typcn-flow-switch:before {
  content: '\e07a';
  /* '' */
}

.typcn-folder-add:before {
  content: '\e07b';
  /* '' */
}

.typcn-folder-delete:before {
  content: '\e07c';
  /* '' */
}

.typcn-folder-open:before {
  content: '\e07d';
  /* '' */
}

.typcn-folder:before {
  content: '\e07e';
  /* '' */
}

.typcn-gift:before {
  content: '\e07f';
  /* '' */
}

.typcn-globe-outline:before {
  content: '\e080';
  /* '' */
}

.typcn-globe:before {
  content: '\e081';
  /* '' */
}

.typcn-group-outline:before {
  content: '\e082';
  /* '' */
}

.typcn-group:before {
  content: '\e083';
  /* '' */
}

.typcn-headphones:before {
  content: '\e084';
  /* '' */
}

.typcn-heart-full-outline:before {
  content: '\e085';
  /* '' */
}

.typcn-heart-half-outline:before {
  content: '\e086';
  /* '' */
}

.typcn-heart-outline:before {
  content: '\e087';
  /* '' */
}

.typcn-heart:before {
  content: '\e088';
  /* '' */
}

.typcn-home-outline:before {
  content: '\e089';
  /* '' */
}

.typcn-home:before {
  content: '\e08a';
  /* '' */
}

.typcn-html5:before {
  content: '\e08b';
  /* '' */
}

.typcn-image-outline:before {
  content: '\e08c';
  /* '' */
}

.typcn-image:before {
  content: '\e08d';
  /* '' */
}

.typcn-infinity-outline:before {
  content: '\e08e';
  /* '' */
}

.typcn-infinity:before {
  content: '\e08f';
  /* '' */
}

.typcn-info-large-outline:before {
  content: '\e090';
  /* '' */
}

.typcn-info-large:before {
  content: '\e091';
  /* '' */
}

.typcn-info-outline:before {
  content: '\e092';
  /* '' */
}

.typcn-info:before {
  content: '\e093';
  /* '' */
}

.typcn-input-checked-outline:before {
  content: '\e094';
  /* '' */
}

.typcn-input-checked:before {
  content: '\e095';
  /* '' */
}

.typcn-key-outline:before {
  content: '\e096';
  /* '' */
}

.typcn-key:before {
  content: '\e097';
  /* '' */
}

.typcn-keyboard:before {
  content: '\e098';
  /* '' */
}

.typcn-leaf:before {
  content: '\e099';
  /* '' */
}

.typcn-lightbulb:before {
  content: '\e09a';
  /* '' */
}

.typcn-link-outline:before {
  content: '\e09b';
  /* '' */
}

.typcn-link:before {
  content: '\e09c';
  /* '' */
}

.typcn-location-arrow-outline:before {
  content: '\e09d';
  /* '' */
}

.typcn-location-arrow:before {
  content: '\e09e';
  /* '' */
}

.typcn-location-outline:before {
  content: '\e09f';
  /* '' */
}

.typcn-location:before {
  content: '\e0a0';
  /* '' */
}

.typcn-lock-closed-outline:before {
  content: '\e0a1';
  /* '' */
}

.typcn-lock-closed:before {
  content: '\e0a2';
  /* '' */
}

.typcn-lock-open-outline:before {
  content: '\e0a3';
  /* '' */
}

.typcn-lock-open:before {
  content: '\e0a4';
  /* '' */
}

.typcn-mail:before {
  content: '\e0a5';
  /* '' */
}

.typcn-map:before {
  content: '\e0a6';
  /* '' */
}

.typcn-media-eject-outline:before {
  content: '\e0a7';
  /* '' */
}

.typcn-media-eject:before {
  content: '\e0a8';
  /* '' */
}

.typcn-media-fast-forward-outline:before {
  content: '\e0a9';
  /* '' */
}

.typcn-media-fast-forward:before {
  content: '\e0aa';
  /* '' */
}

.typcn-media-pause-outline:before {
  content: '\e0ab';
  /* '' */
}

.typcn-media-pause:before {
  content: '\e0ac';
  /* '' */
}

.typcn-media-play-outline:before {
  content: '\e0ad';
  /* '' */
}

.typcn-media-play-reverse-outline:before {
  content: '\e0ae';
  /* '' */
}

.typcn-media-play-reverse:before {
  content: '\e0af';
  /* '' */
}

.typcn-media-play:before {
  content: '\e0b0';
  /* '' */
}

.typcn-media-record-outline:before {
  content: '\e0b1';
  /* '' */
}

.typcn-media-record:before {
  content: '\e0b2';
  /* '' */
}

.typcn-media-rewind-outline:before {
  content: '\e0b3';
  /* '' */
}

.typcn-media-rewind:before {
  content: '\e0b4';
  /* '' */
}

.typcn-media-stop-outline:before {
  content: '\e0b5';
  /* '' */
}

.typcn-media-stop:before {
  content: '\e0b6';
  /* '' */
}

.typcn-message-typing:before {
  content: '\e0b7';
  /* '' */
}

.typcn-message:before {
  content: '\e0b8';
  /* '' */
}

.typcn-messages:before {
  content: '\e0b9';
  /* '' */
}

.typcn-microphone-outline:before {
  content: '\e0ba';
  /* '' */
}

.typcn-microphone:before {
  content: '\e0bb';
  /* '' */
}

.typcn-minus-outline:before {
  content: '\e0bc';
  /* '' */
}

.typcn-minus:before {
  content: '\e0bd';
  /* '' */
}

.typcn-mortar-board:before {
  content: '\e0be';
  /* '' */
}

.typcn-news:before {
  content: '\e0bf';
  /* '' */
}

.typcn-notes-outline:before {
  content: '\e0c0';
  /* '' */
}

.typcn-notes:before {
  content: '\e0c1';
  /* '' */
}

.typcn-pen:before {
  content: '\e0c2';
  /* '' */
}

.typcn-pencil:before {
  content: '\e0c3';
  /* '' */
}

.typcn-phone-outline:before {
  content: '\e0c4';
  /* '' */
}

.typcn-phone:before {
  content: '\e0c5';
  /* '' */
}

.typcn-pi-outline:before {
  content: '\e0c6';
  /* '' */
}

.typcn-pi:before {
  content: '\e0c7';
  /* '' */
}

.typcn-pin-outline:before {
  content: '\e0c8';
  /* '' */
}

.typcn-pin:before {
  content: '\e0c9';
  /* '' */
}

.typcn-pipette:before {
  content: '\e0ca';
  /* '' */
}

.typcn-plane-outline:before {
  content: '\e0cb';
  /* '' */
}

.typcn-plane:before {
  content: '\e0cc';
  /* '' */
}

.typcn-plug:before {
  content: '\e0cd';
  /* '' */
}

.typcn-plus-outline:before {
  content: '\e0ce';
  /* '' */
}

.typcn-plus:before {
  content: '\e0cf';
  /* '' */
}

.typcn-point-of-interest-outline:before {
  content: '\e0d0';
  /* '' */
}

.typcn-point-of-interest:before {
  content: '\e0d1';
  /* '' */
}

.typcn-power-outline:before {
  content: '\e0d2';
  /* '' */
}

.typcn-power:before {
  content: '\e0d3';
  /* '' */
}

.typcn-printer:before {
  content: '\e0d4';
  /* '' */
}

.typcn-puzzle-outline:before {
  content: '\e0d5';
  /* '' */
}

.typcn-puzzle:before {
  content: '\e0d6';
  /* '' */
}

.typcn-radar-outline:before {
  content: '\e0d7';
  /* '' */
}

.typcn-radar:before {
  content: '\e0d8';
  /* '' */
}

.typcn-refresh-outline:before {
  content: '\e0d9';
  /* '' */
}

.typcn-refresh:before {
  content: '\e0da';
  /* '' */
}

.typcn-rss-outline:before {
  content: '\e0db';
  /* '' */
}

.typcn-rss:before {
  content: '\e0dc';
  /* '' */
}

.typcn-scissors-outline:before {
  content: '\e0dd';
  /* '' */
}

.typcn-scissors:before {
  content: '\e0de';
  /* '' */
}

.typcn-shopping-bag:before {
  content: '\e0df';
  /* '' */
}

.typcn-shopping-cart:before {
  content: '\e0e0';
  /* '' */
}

.typcn-social-at-circular:before {
  content: '\e0e1';
  /* '' */
}

.typcn-social-dribbble-circular:before {
  content: '\e0e2';
  /* '' */
}

.typcn-social-dribbble:before {
  content: '\e0e3';
  /* '' */
}

.typcn-social-facebook-circular:before {
  content: '\e0e4';
  /* '' */
}

.typcn-social-facebook:before {
  content: '\e0e5';
  /* '' */
}

.typcn-social-flickr-circular:before {
  content: '\e0e6';
  /* '' */
}

.typcn-social-flickr:before {
  content: '\e0e7';
  /* '' */
}

.typcn-social-github-circular:before {
  content: '\e0e8';
  /* '' */
}

.typcn-social-github:before {
  content: '\e0e9';
  /* '' */
}

.typcn-social-google-plus-circular:before {
  content: '\e0ea';
  /* '' */
}

.typcn-social-google-plus:before {
  content: '\e0eb';
  /* '' */
}

.typcn-social-instagram-circular:before {
  content: '\e0ec';
  /* '' */
}

.typcn-social-instagram:before {
  content: '\e0ed';
  /* '' */
}

.typcn-social-last-fm-circular:before {
  content: '\e0ee';
  /* '' */
}

.typcn-social-last-fm:before {
  content: '\e0ef';
  /* '' */
}

.typcn-social-linkedin-circular:before {
  content: '\e0f0';
  /* '' */
}

.typcn-social-linkedin:before {
  content: '\e0f1';
  /* '' */
}

.typcn-social-pinterest-circular:before {
  content: '\e0f2';
  /* '' */
}

.typcn-social-pinterest:before {
  content: '\e0f3';
  /* '' */
}

.typcn-social-skype-outline:before {
  content: '\e0f4';
  /* '' */
}

.typcn-social-skype:before {
  content: '\e0f5';
  /* '' */
}

.typcn-social-tumbler-circular:before {
  content: '\e0f6';
  /* '' */
}

.typcn-social-tumbler:before {
  content: '\e0f7';
  /* '' */
}

.typcn-social-twitter-circular:before {
  content: '\e0f8';
  /* '' */
}

.typcn-social-twitter:before {
  content: '\e0f9';
  /* '' */
}

.typcn-social-vimeo-circular:before {
  content: '\e0fa';
  /* '' */
}

.typcn-social-vimeo:before {
  content: '\e0fb';
  /* '' */
}

.typcn-social-youtube-circular:before {
  content: '\e0fc';
  /* '' */
}

.typcn-social-youtube:before {
  content: '\e0fd';
  /* '' */
}

.typcn-sort-alphabetically-outline:before {
  content: '\e0fe';
  /* '' */
}

.typcn-sort-alphabetically:before {
  content: '\e0ff';
  /* '' */
}

.typcn-sort-numerically-outline:before {
  content: '\e100';
  /* '' */
}

.typcn-sort-numerically:before {
  content: '\e101';
  /* '' */
}

.typcn-spanner-outline:before {
  content: '\e102';
  /* '' */
}

.typcn-spanner:before {
  content: '\e103';
  /* '' */
}

.typcn-spiral:before {
  content: '\e104';
  /* '' */
}

.typcn-star-full-outline:before {
  content: '\e105';
  /* '' */
}

.typcn-star-half-outline:before {
  content: '\e106';
  /* '' */
}

.typcn-star-half:before {
  content: '\e107';
  /* '' */
}

.typcn-star-outline:before {
  content: '\e108';
  /* '' */
}

.typcn-star:before {
  content: '\e109';
  /* '' */
}

.typcn-starburst-outline:before {
  content: '\e10a';
  /* '' */
}

.typcn-starburst:before {
  content: '\e10b';
  /* '' */
}

.typcn-stopwatch:before {
  content: '\e10c';
  /* '' */
}

.typcn-support:before {
  content: '\e10d';
  /* '' */
}

.typcn-tabs-outline:before {
  content: '\e10e';
  /* '' */
}

.typcn-tag:before {
  content: '\e10f';
  /* '' */
}

.typcn-tags:before {
  content: '\e110';
  /* '' */
}

.typcn-th-large-outline:before {
  content: '\e111';
  /* '' */
}

.typcn-th-large:before {
  content: '\e112';
  /* '' */
}

.typcn-th-list-outline:before {
  content: '\e113';
  /* '' */
}

.typcn-th-list:before {
  content: '\e114';
  /* '' */
}

.typcn-th-menu-outline:before {
  content: '\e115';
  /* '' */
}

.typcn-th-menu:before {
  content: '\e116';
  /* '' */
}

.typcn-th-small-outline:before {
  content: '\e117';
  /* '' */
}

.typcn-th-small:before {
  content: '\e118';
  /* '' */
}

.typcn-thermometer:before {
  content: '\e119';
  /* '' */
}

.typcn-thumbs-down:before {
  content: '\e11a';
  /* '' */
}

.typcn-thumbs-ok:before {
  content: '\e11b';
  /* '' */
}

.typcn-thumbs-up:before {
  content: '\e11c';
  /* '' */
}

.typcn-tick-outline:before {
  content: '\e11d';
  /* '' */
}

.typcn-tick:before {
  content: '\e11e';
  /* '' */
}

.typcn-ticket:before {
  content: '\e11f';
  /* '' */
}

.typcn-time:before {
  content: '\e120';
  /* '' */
}

.typcn-times-outline:before {
  content: '\e121';
  /* '' */
}

.typcn-times:before {
  content: '\e122';
  /* '' */
}

.typcn-trash:before {
  content: '\e123';
  /* '' */
}

.typcn-tree:before {
  content: '\e124';
  /* '' */
}

.typcn-upload-outline:before {
  content: '\e125';
  /* '' */
}

.typcn-upload:before {
  content: '\e126';
  /* '' */
}

.typcn-user-add-outline:before {
  content: '\e127';
  /* '' */
}

.typcn-user-add:before {
  content: '\e128';
  /* '' */
}

.typcn-user-delete-outline:before {
  content: '\e129';
  /* '' */
}

.typcn-user-delete:before {
  content: '\e12a';
  /* '' */
}

.typcn-user-outline:before {
  content: '\e12b';
  /* '' */
}

.typcn-user:before {
  content: '\e12c';
  /* '' */
}

.typcn-vendor-android:before {
  content: '\e12d';
  /* '' */
}

.typcn-vendor-apple:before {
  content: '\e12e';
  /* '' */
}

.typcn-vendor-microsoft:before {
  content: '\e12f';
  /* '' */
}

.typcn-video-outline:before {
  content: '\e130';
  /* '' */
}

.typcn-video:before {
  content: '\e131';
  /* '' */
}

.typcn-volume-down:before {
  content: '\e132';
  /* '' */
}

.typcn-volume-mute:before {
  content: '\e133';
  /* '' */
}

.typcn-volume-up:before {
  content: '\e134';
  /* '' */
}

.typcn-volume:before {
  content: '\e135';
  /* '' */
}

.typcn-warning-outline:before {
  content: '\e136';
  /* '' */
}

.typcn-warning:before {
  content: '\e137';
  /* '' */
}

.typcn-watch:before {
  content: '\e138';
  /* '' */
}

.typcn-waves-outline:before {
  content: '\e139';
  /* '' */
}

.typcn-waves:before {
  content: '\e13a';
  /* '' */
}

.typcn-weather-cloudy:before {
  content: '\e13b';
  /* '' */
}

.typcn-weather-downpour:before {
  content: '\e13c';
  /* '' */
}

.typcn-weather-night:before {
  content: '\e13d';
  /* '' */
}

.typcn-weather-partly-sunny:before {
  content: '\e13e';
  /* '' */
}

.typcn-weather-shower:before {
  content: '\e13f';
  /* '' */
}

.typcn-weather-snow:before {
  content: '\e140';
  /* '' */
}

.typcn-weather-stormy:before {
  content: '\e141';
  /* '' */
}

.typcn-weather-sunny:before {
  content: '\e142';
  /* '' */
}

.typcn-weather-windy-cloudy:before {
  content: '\e143';
  /* '' */
}

.typcn-weather-windy:before {
  content: '\e144';
  /* '' */
}

.typcn-wi-fi-outline:before {
  content: '\e145';
  /* '' */
}

.typcn-wi-fi:before {
  content: '\e146';
  /* '' */
}

.typcn-wine:before {
  content: '\e147';
  /* '' */
}

.typcn-world-outline:before {
  content: '\e148';
  /* '' */
}

.typcn-world:before {
  content: '\e149';
  /* '' */
}

.typcn-zoom-in-outline:before {
  content: '\e14a';
  /* '' */
}

.typcn-zoom-in:before {
  content: '\e14b';
  /* '' */
}

.typcn-zoom-out-outline:before {
  content: '\e14c';
  /* '' */
}

.typcn-zoom-out:before {
  content: '\e14d';
  /* '' */
}

.typcn-zoom-outline:before {
  content: '\e14e';
  /* '' */
}

.typcn-zoom:before {
  content: '\e14f';
  /* '' */
}
/*# sourceMappingURL=icons.css.map */