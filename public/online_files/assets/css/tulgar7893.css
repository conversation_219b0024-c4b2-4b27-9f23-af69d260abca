.logo-large {
    height: 40px!important;
}

.tlg-menu-item {
    margin-right: 5px;
}

.tlg-highlight {
    color: #e83e8c!important;
}

.bg-ozel {
    background: #e74c3c;
    margin-bottom: 15px!important;
}

.tlg-pd-conf {
    padding: 8px !important;
}

.loader,
.loader:before,
.loader:after {
    border-radius: 50%;
    width: 2.5em;
    height: 2.5em;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation: load7 1.8s infinite ease-in-out;
    animation: load7 1.8s infinite ease-in-out;
}

.sikayet-o<PERSON><PERSON>-kapandi {
    background-color: #e74c3c;
    color: white;
}

.sikayet-bekliyor {
    background-color: #34495e;
    color: white;
}

.sikayet-islemde {
    background-color: #9b59b6;
    color: white;
}

.sikayet-cozumlendi {
    background-color: #2ecc71;
    color: white;
}

.clickable-row:hover {
    background-color: #ecf0f1;
    cursor: pointer;
}

.sikayet-mahkemede {
    background-color: #95a5a6;
    color: white;
}

.sikayet-mahkeme-sonuclandi {
    background-color: #1abc9c;
    color: white;
}

.sikayet-tuketici-hakem-heyetinde {
    background-color: #d35400;
    color: white;
}

.loader {
    position: absolute!important;
    color: white;
    font-size: 10px;
    text-indent: -9999em;
    -webkit-transform: translateZ(0);
    -ms-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-animation-delay: -0.16s;
    animation-delay: -0.16s;
    z-index: 9999;
    vertical-align: middle;
    line-height: normal;
    display: inline-block;
    top: 50%;
    -ms-transform: translateY(-50%);
    transform: translateY(-50%);
}

.loader:before,
.loader:after {
    content: '';
    position: absolute;
    top: 0;
}

.loader:before {
    left: -3.5em;
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
}

.loader:after {
    left: 3.5em;
}

@-webkit-keyframes load7 {
    0%,
    80%,
    100% {
        box-shadow: 0 2.5em 0 -1.3em;
    }
    40% {
        box-shadow: 0 2.5em 0 0;
    }
}

@keyframes load7 {
    0%,
    80%,
    100% {
        box-shadow: 0 2.5em 0 -1.3em;
    }
    40% {
        box-shadow: 0 2.5em 0 0;
    }
}

.tlg-overlay {
    position: absolute;
    /* Sit on top of the page content */
    width: 100%;
    /* Full width (cover the whole page) */
    height: 100%;
    /* Full height (cover the whole page) */
    top: 0;
    line-height: 100%;
    text-align: center;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    /* Black background with opacity */
    z-index: 2;
    /* Specify a stack order in case you're using a different order for other elements */
    cursor: wait;
    /* Add a pointer on hover */
}

.ozelYaziClass {
    font-size: 1em!important;
}

.bg-ozel2 {
    background-color: #34495e;
    margin-bottom: 10px!important;
}

.ozelTablo td {
    padding: .14rem!important;
}

@media screen and (max-width: 767px) {
    li.paginate_button.previous {
        display: inline;
    }
    li.paginate_button.next {
        display: inline;
    }
    li.paginate_button {
        display: none;
    }
}

.OzetTablo {
    min-height: 600px!important;
}

.tlg-fsz {
    font-size: 0.8rem;
    margin: 0px!important;
}

.tlg-fsz2 {
    margin: 0px!important;
    font-size: 1rem;
}

.sidenav {
    background: #fff;
    -webkit-box-shadow: 0 1px 9px -3px rgba(0, 0, 0, .75);
    box-shadow: 0 1px 9px -3px rgba(0, 0, 0, .75);
    height: 100%;
    width: 250px;
    display: none;
    position: fixed;
    z-index: 99999;
    top: 0;
    left: 0;
    overflow-x: hidden;
    padding-top: 60px;
}

.sidenav a {
    padding: 8px 8px 8px 32px;
    text-decoration: none;
    font-size: 25px;
    color: #818181;
    display: block;
    transition: 0.3s;
}

.sidenav a:hover {
    color: #f1f1f1;
}

@media screen and (max-height: 450px) {
    .sidenav {
        padding-top: 15px;
    }
    .sidenav a {
        font-size: 18px;
    }
}