/*
 Template Name: lexa - Responsive Bootstrap 4 Admin Dashboard
 Author: Themesbrand
 Website: www.themesbrand.com
 File: Main js
 */

! function($) {
    "use strict";

    var MainApp = function() {};

    MainApp.prototype.initNavbar = function() {

            $('.navbar-toggle').on('click', function(event) {
                $(this).toggleClass('open');
                if (MobileApp) {
                    $("#mySidenav").animate({ 'width': 'toggle' });
                    $('.mobileAppNavigation').toggle();
                } else {
                    $('#navigation').slideToggle(400);
                }
            });

            $('.navigation-menu>li').slice(-2).addClass('last-elements');

            $('.navigation-menu li.has-submenu a[href="#"]').on('click', function(e) {
                if ($(window).width() < 992) {
                    e.preventDefault();
                    $(this).parent('li').toggleClass('open').find('.submenu:first').toggleClass('open');
                }
            });
        },
        MainApp.prototype.initScrollbar = function() {
            $('.slimscroll').slimScroll({
                height: 'auto',
                position: 'right',
                size: "7px",
                color: '#9ea5ab',
                wheelStep: 5,
                touchScrollStep: 50
            });
        }
        // === fo,llowing js will activate the menu in left side bar based on url ====
    MainApp.prototype.initMenuItem = function() {
            $(".navigation-menu a").each(function() {
                var pageUrl = window.location.href.split(/[?#]/)[0];
                if (this.href == pageUrl) {
                    $(this).parent().addClass("active"); // add active to li of the current link
                    $(this).parent().parent().parent().addClass("active"); // add active class to an anchor
                    $(this).parent().parent().parent().parent().parent().addClass("active"); // add active class to an anchor
                }
            });
        },
        MainApp.prototype.initComponents = function() {
            $('[data-toggle="tooltip"]').tooltip();
            $('[data-toggle="popover"]').popover();
        },

        MainApp.prototype.initHeaderCharts = function() {
            //header charts
        },

        MainApp.prototype.init = function() {
            if (!Login) {
                if (localStorage.getItem("UMRSESS") !== null && localStorage.getItem("stayLoggedIn") == 'true') {
                    $.get(AnaURL + "Ajax/Giris-Yap/Stay/" + localStorage.getItem("UMRSESS"), function(Dt) {
                        try {
                            var data = JSON.parse(Dt);
                        } catch (err) {
                            console.log("stayLoggedIn property error.");
                            return;
                        }
                        if (data.status == "success") {
                            console.log("stayLoggedIn property is ok");
                            window.location.reload(true);
                        } else {
                            console.log("UMRSESS property is invalid.");
                            console.log(localStorage.getItem("UMRSESS"));
                        }
                    });
                } else {
                    console.log("test");
                }
            }

            this.initNavbar();
            this.initScrollbar();
            this.initMenuItem();
            this.initComponents();
            this.initHeaderCharts();
            Waves.init();
        },

        //init
        $.MainApp = new MainApp, $.MainApp.Constructor = MainApp
}(window.jQuery),

//initializing
function($) {
    "use strict";
    $.MainApp.init();
}(window.jQuery);