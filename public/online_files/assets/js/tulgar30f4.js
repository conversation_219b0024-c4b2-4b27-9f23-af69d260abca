function ShowInfo(msg, Elem = 'Bilgi') {
    $("#" + Elem).html('<div class="alert alert-danger alert-dismissible fade show mb-10" role="alert"><button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>' + msg + '</div>');
}

function Cari_Tipi_Ayar() {
    if (document.getElementById("Cari_Tip_1").checked) {
        $("#cari_bireysel").fadeIn(1);
        $("#cari_kurumsal").fadeOut(1);
    } else {
        $("#cari_kurumsal").fadeIn(1);
        $("#cari_bireysel").fadeOut(1);
    }
}

$(".clickable-row").click(function() {
    if ($(this).data("target") == "_blank") {
        window.open($(this).data("href"));
        return;
    }
    window.location.assign($(this).data("href"));
});

function c_ilce() {
    var il = document.getElementById("cari_Il").value;
    $.post(AnaURL + "Ajax/Adres/Ilce", { il: il }, function(donenVeri) {
        donenVeri = jQuery.trim(donenVeri);
        if (donenVeri == "error") {
            alert('Bir hata mevcut, daha sonra tekrar deneyiniz.')
        } else {
            document.getElementById("cari_Ilce").innerHTML = donenVeri;
            c_semt();
        }
    });
}

function c_semt() {
    var ilce = document.getElementById("cari_Ilce").value;
    var il = document.getElementById("cari_Il").value;

    $.post(AnaURL + "Ajax/Adres/Semt", { ilce: ilce, il: il }, function(donenVeri) {
        donenVeri = jQuery.trim(donenVeri);
        if (donenVeri == "error") {
            alert('Bir hata mevcut, daha sonra tekrar deneyiniz.')
        } else {
            document.getElementById("cari_Semt").innerHTML = donenVeri;
            c_mah();
        }
    });
}

function c_mah() {
    var ilce = document.getElementById("cari_Ilce").value;
    var il = document.getElementById("cari_Il").value;
    var semt = document.getElementById("cari_Semt").value;

    $.post(AnaURL + "Ajax/Adres/Mahalle", { ilce: ilce, il: il, semt: semt }, function(donenVeri) {
        donenVeri = jQuery.trim(donenVeri);
        if (donenVeri == "error") {
            alert('Bir hata mevcut, daha sonra tekrar deneyiniz.')
        } else {
            document.getElementById("cari_Mah").innerHTML = donenVeri;
        }
    });
}