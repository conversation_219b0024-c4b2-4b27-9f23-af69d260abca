!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):t.Sweetalert2=e()}(this,function(){"use strict";function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}function s(){return(s=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}function c(t){return(c=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function o(t,e,n){return(o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch(t){return!1}}()?Reflect.construct:function(t,e,n){var o=[null];o.push.apply(o,e);var i=new(Function.bind.apply(t,o));return n&&u(i,n.prototype),i}).apply(null,arguments)}function l(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function d(t,e,n){return(d="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){var o=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=c(t)););return t}(t,e);if(o){var i=Object.getOwnPropertyDescriptor(o,e);return i.get?i.get.call(n):i.value}})(t,e,n||t)}var e="SweetAlert2:",i=function(e){return Object.keys(e).map(function(t){return e[t]})},p=function(t){return Array.prototype.slice.call(t)},C=function(t){console.warn("".concat(e," ").concat(t))},k=function(t){console.error("".concat(e," ").concat(t))},f=[],n=function(t,e){var n;n='"'.concat(t,'" is deprecated and will be removed in the next major release. Please use "').concat(e,'" instead.'),-1===f.indexOf(n)&&(f.push(n),C(n))},h=function(t){return"function"==typeof t?t():t},B=function(t){return t&&Promise.resolve(t)===t},t=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),m=function(t){var e={};for(var n in t)e[t[n]]="swal2-"+t[n];return e},x=m(["container","shown","height-auto","iosfix","popup","modal","no-backdrop","toast","toast-shown","toast-column","fade","show","hide","noanimation","close","title","header","content","actions","confirm","cancel","footer","icon","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl"]),g=m(["success","warning","info","question","error"]),v={previousBodyPadding:null},b=function(t,e){return t.classList.contains(e)},y=function(e,t,n){p(e.classList).forEach(function(t){-1===i(x).indexOf(t)&&-1===i(g).indexOf(t)&&e.classList.remove(t)}),t&&t[n]&&E(e,t[n])};function S(t,e){if(!e)return null;switch(e){case"select":case"textarea":case"file":return T(t,x[e]);case"checkbox":return t.querySelector(".".concat(x.checkbox," input"));case"radio":return t.querySelector(".".concat(x.radio," input:checked"))||t.querySelector(".".concat(x.radio," input:first-child"));case"range":return t.querySelector(".".concat(x.range," input"));default:return T(t,x.input)}}var A,P=function(t){if(t.focus(),"file"!==t.type){var e=t.value;t.value="",t.value=e}},L=function(t,e,n){t&&e&&("string"==typeof e&&(e=e.split(/\s+/).filter(Boolean)),e.forEach(function(e){t.forEach?t.forEach(function(t){n?t.classList.add(e):t.classList.remove(e)}):n?t.classList.add(e):t.classList.remove(e)}))},E=function(t,e){L(t,e,!0)},O=function(t,e){L(t,e,!1)},T=function(t,e){for(var n=0;n<t.childNodes.length;n++)if(b(t.childNodes[n],e))return t.childNodes[n]},M=function(t,e,n){n||0===parseInt(n)?t.style[e]="number"==typeof n?n+"px":n:t.style.removeProperty(e)},V=function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"flex";t.style.opacity="",t.style.display=e},q=function(t){t.style.opacity="",t.style.display="none"},j=function(t,e,n){e?V(t,n):q(t)},H=function(t){return!(!t||!(t.offsetWidth||t.offsetHeight||t.getClientRects().length))},I=function(){return document.body.querySelector("."+x.container)},R=function(t){var e=I();return e?e.querySelector(t):null},N=function(t){return R("."+t)},D=function(){return N(x.popup)},U=function(){var t=D();return p(t.querySelectorAll("."+x.icon))},_=function(){return N(x.title)},z=function(){return N(x.content)},W=function(){return N(x.image)},K=function(){return N(x["progress-steps"])},F=function(){return N(x["validation-message"])},Z=function(){return R("."+x.actions+" ."+x.confirm)},Q=function(){return R("."+x.actions+" ."+x.cancel)},Y=function(){return N(x.actions)},$=function(){return N(x.header)},J=function(){return N(x.footer)},X=function(){return N(x.close)},G=function(){var t=p(D().querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])')).sort(function(t,e){return t=parseInt(t.getAttribute("tabindex")),(e=parseInt(e.getAttribute("tabindex")))<t?1:t<e?-1:0}),e=p(D().querySelectorAll('a[href], area[href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled]), iframe, object, embed, [tabindex="0"], [contenteditable], audio[controls], video[controls]')).filter(function(t){return"-1"!==t.getAttribute("tabindex")});return function(t){for(var e=[],n=0;n<t.length;n++)-1===e.indexOf(t[n])&&e.push(t[n]);return e}(t.concat(e)).filter(function(t){return H(t)})},tt=function(){return!et()&&!document.body.classList.contains(x["no-backdrop"])},et=function(){return document.body.classList.contains(x["toast-shown"])},nt=function(){return"undefined"==typeof window||"undefined"==typeof document},ot='\n <div aria-labelledby="'.concat(x.title,'" aria-describedby="').concat(x.content,'" class="').concat(x.popup,'" tabindex="-1">\n   <div class="').concat(x.header,'">\n     <ul class="').concat(x["progress-steps"],'"></ul>\n     <div class="').concat(x.icon," ").concat(g.error,'">\n       <span class="swal2-x-mark"><span class="swal2-x-mark-line-left"></span><span class="swal2-x-mark-line-right"></span></span>\n     </div>\n     <div class="').concat(x.icon," ").concat(g.question,'"></div>\n     <div class="').concat(x.icon," ").concat(g.warning,'"></div>\n     <div class="').concat(x.icon," ").concat(g.info,'"></div>\n     <div class="').concat(x.icon," ").concat(g.success,'">\n       <div class="swal2-success-circular-line-left"></div>\n       <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>\n       <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>\n       <div class="swal2-success-circular-line-right"></div>\n     </div>\n     <img class="').concat(x.image,'" />\n     <h2 class="').concat(x.title,'" id="').concat(x.title,'"></h2>\n     <button type="button" class="').concat(x.close,'">&times;</button>\n   </div>\n   <div class="').concat(x.content,'">\n     <div id="').concat(x.content,'"></div>\n     <input class="').concat(x.input,'" />\n     <input type="file" class="').concat(x.file,'" />\n     <div class="').concat(x.range,'">\n       <input type="range" />\n       <output></output>\n     </div>\n     <select class="').concat(x.select,'"></select>\n     <div class="').concat(x.radio,'"></div>\n     <label for="').concat(x.checkbox,'" class="').concat(x.checkbox,'">\n       <input type="checkbox" />\n       <span class="').concat(x.label,'"></span>\n     </label>\n     <textarea class="').concat(x.textarea,'"></textarea>\n     <div class="').concat(x["validation-message"],'" id="').concat(x["validation-message"],'"></div>\n   </div>\n   <div class="').concat(x.actions,'">\n     <button type="button" class="').concat(x.confirm,'">OK</button>\n     <button type="button" class="').concat(x.cancel,'">Cancel</button>\n   </div>\n   <div class="').concat(x.footer,'">\n   </div>\n </div>\n').replace(/(^|\n)\s*/g,""),it=function(t){Ut.isVisible()&&A!==t.target.value&&Ut.resetValidationMessage(),A=t.target.value},rt=function(t){var e;if((e=I())&&(e.parentNode.removeChild(e),O([document.documentElement,document.body],[x["no-backdrop"],x["toast-shown"],x["has-column"]])),nt())k("SweetAlert2 requires document to initialize");else{var n=document.createElement("div");n.className=x.container,n.innerHTML=ot;var o,i,r,a,s,c,u,l,d,p,f,m,g="string"==typeof(o=t.target)?document.querySelector(o):o;g.appendChild(n),i=t,(r=D()).setAttribute("role",i.toast?"alert":"dialog"),r.setAttribute("aria-live",i.toast?"polite":"assertive"),i.toast||r.setAttribute("aria-modal","true"),a=g,"rtl"===window.getComputedStyle(a).direction&&E(I(),x.rtl),s=z(),c=T(s,x.input),u=T(s,x.file),l=s.querySelector(".".concat(x.range," input")),d=s.querySelector(".".concat(x.range," output")),p=T(s,x.select),f=s.querySelector(".".concat(x.checkbox," input")),m=T(s,x.textarea),c.oninput=it,u.onchange=it,p.onchange=it,f.onchange=it,m.oninput=it,l.oninput=function(t){it(t),d.value=l.value},l.onchange=function(t){it(t),l.nextSibling.value=l.value}}},at=function(t,e){t instanceof HTMLElement?e.appendChild(t):"object"===w(t)?st(e,t):t&&(e.innerHTML=t)},st=function(t,e){if(t.innerHTML="",0 in e)for(var n=0;n in e;n++)t.appendChild(e[n].cloneNode(!0));else t.appendChild(e.cloneNode(!0))},ct=function(){if(nt())return!1;var t=document.createElement("div"),e={WebkitAnimation:"webkitAnimationEnd",OAnimation:"oAnimationEnd oanimationend",animation:"animationend"};for(var n in e)if(e.hasOwnProperty(n)&&void 0!==t.style[n])return e[n];return!1}(),ut=function(t){var e=z().querySelector("#"+x.content);t.html?(at(t.html,e),V(e,"block")):t.text?(e.textContent=t.text,V(e,"block")):q(e),function(e){for(var t,n=z(),o=["input","file","range","select","radio","checkbox","textarea"],i=function(t){t.placeholder&&!e.inputPlaceholder||(t.placeholder=e.inputPlaceholder)},r=0;r<o.length;r++){var a=x[o[r]],s=T(n,a);if(t=S(n,o[r])){for(var c in t.attributes)if(t.attributes.hasOwnProperty(c)){var u=t.attributes[c].name;"type"!==u&&"value"!==u&&t.removeAttribute(u)}for(var l in e.inputAttributes)"range"===o[r]&&"placeholder"===l||t.setAttribute(l,e.inputAttributes[l])}s.className=a,e.inputClass&&E(s,e.inputClass),e.customClass&&E(s,e.customClass.input),q(s)}switch(e.input){case"text":case"email":case"password":case"number":case"tel":case"url":t=T(n,x.input),"string"==typeof e.inputValue||"number"==typeof e.inputValue?t.value=e.inputValue:B(e.inputValue)||C('Unexpected type of inputValue! Expected "string", "number" or "Promise", got "'.concat(w(e.inputValue),'"')),i(t),t.type=e.input,V(t);break;case"file":i(t=T(n,x.file)),t.type=e.input,V(t);break;case"range":var d=T(n,x.range),p=d.querySelector("input"),f=d.querySelector("output");p.value=e.inputValue,p.type=e.input,f.value=e.inputValue,V(d);break;case"select":var m=T(n,x.select);if(m.innerHTML="",e.inputPlaceholder){var g=document.createElement("option");g.innerHTML=e.inputPlaceholder,g.value="",g.disabled=!0,g.selected=!0,m.appendChild(g)}V(m);break;case"radio":var h=T(n,x.radio);h.innerHTML="",V(h);break;case"checkbox":var v=T(n,x.checkbox),b=S(n,"checkbox");b.type="checkbox",b.value=1,b.id=x.checkbox,b.checked=Boolean(e.inputValue),v.querySelector("span").innerHTML=e.inputPlaceholder,V(v);break;case"textarea":var y=T(n,x.textarea);y.value=e.inputValue,i(y),V(y);break;case null:break;default:k('Unexpected type of input! Expected "text", "email", "password", "number", "tel", "select", "radio", "checkbox", "textarea", "file" or "url", got "'.concat(e.input,'"'))}}(t),y(z(),t.customClass,"content")},lt=function(){for(var t=U(),e=0;e<t.length;e++)q(t[e])},dt=function(){for(var t=D(),e=window.getComputedStyle(t).getPropertyValue("background-color"),n=t.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix"),o=0;o<n.length;o++)n[o].style.backgroundColor=e},pt=function(i){var r=K(),a=parseInt(null===i.currentProgressStep?Ut.getQueueStep():i.currentProgressStep,10);i.progressSteps&&i.progressSteps.length?(V(r),r.innerHTML="",a>=i.progressSteps.length&&C("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),i.progressSteps.forEach(function(t,e){var n=document.createElement("li");if(E(n,x["progress-step"]),n.innerHTML=t,e===a&&E(n,x["active-progress-step"]),r.appendChild(n),e!==i.progressSteps.length-1){var o=document.createElement("li");E(o,x["progress-step-line"]),i.progressStepsDistance&&(o.style.width=i.progressStepsDistance),r.appendChild(o)}})):q(r)},ft=function(t){var e,n,o,i,r=$();y(r,t.customClass,"header"),pt(t),function(t){if(lt(),t.type)if(dt(),-1!==Object.keys(g).indexOf(t.type)){var e=D().querySelector(".".concat(x.icon,".").concat(g[t.type]));V(e),y(e,t.customClass,"icon"),t.animation&&E(e,"swal2-animate-".concat(t.type,"-icon"))}else k('Unknown type! Expected "success", "error", "warning", "info" or "question", got "'.concat(t.type,'"'))}(t),function(t){var e=W();if(!t.imageUrl)return q(e);V(e),e.setAttribute("src",t.imageUrl),e.setAttribute("alt",t.imageAlt),M(e,"width",t.imageWidth),M(e,"height",t.imageHeight),e.className=x.image,y(e,t.customClass,"image"),t.imageClass&&E(e,t.imageClass)}(t),e=t,n=_(),j(n,e.title||e.titleText),e.title&&at(e.title,n),e.titleText&&(n.innerText=e.titleText),y(n,e.customClass,"title"),o=t,i=X(),y(i,o.customClass,"closeButton"),j(i,o.showCloseButton),i.setAttribute("aria-label",o.closeButtonAriaLabel)},mt=function(t){var e,n,o,i;e=t,n=D(),M(n,"width",e.width),M(n,"padding",e.padding),e.background&&(n.style.background=e.background),n.className=x.popup,e.toast?(E([document.documentElement,document.body],x["toast-shown"]),E(n,x.toast)):E(n,x.modal),y(n,e.customClass,"popup"),"string"==typeof e.customClass&&E(n,e.customClass),e.animation?O(n,x.noanimation):E(n,x.noanimation),function(t){var e=I();if(e){if("string"==typeof t.backdrop?e.style.background=t.backdrop:t.backdrop||E([document.documentElement,document.body],x["no-backdrop"]),!t.backdrop&&t.allowOutsideClick&&C('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),t.position in x?E(e,x[t.position]):(C('The "position" parameter is not valid, defaulting to "center"'),E(e,x.center)),t.grow&&"string"==typeof t.grow){var n="grow-"+t.grow;n in x&&E(e,x[n])}y(e,t.customClass,"container"),t.customContainerClass&&E(e,t.customContainerClass)}}(t),ft(t),ut(t),function(t){var e=Y(),n=Z(),o=Q();if(t.showConfirmButton||t.showCancelButton?V(e):q(e),y(e,t.customClass,"actions"),j(n,t.showConfirmButton,"inline-block"),j(o,t.showCancelButton,"inline-block"),n.innerHTML=t.confirmButtonText,o.innerHTML=t.cancelButtonText,n.setAttribute("aria-label",t.confirmButtonAriaLabel),o.setAttribute("aria-label",t.cancelButtonAriaLabel),n.className=x.confirm,E(n,t.confirmButtonClass),t.customClass&&E(n,t.customClass.confirmButton),o.className=x.cancel,E(o,t.cancelButtonClass),t.customClass&&E(o,t.customClass.cancelButton),t.buttonsStyling){E([n,o],x.styled),t.confirmButtonColor&&(n.style.backgroundColor=t.confirmButtonColor),t.cancelButtonColor&&(o.style.backgroundColor=t.cancelButtonColor);var i=window.getComputedStyle(n).getPropertyValue("background-color");n.style.borderLeftColor=i,n.style.borderRightColor=i}else O([n,o],x.styled),n.style.backgroundColor=n.style.borderLeftColor=n.style.borderRightColor="",o.style.backgroundColor=o.style.borderLeftColor=o.style.borderRightColor=""}(t),o=t,i=J(),j(i,o.footer),o.footer&&at(o.footer,i),y(i,o.customClass,"footer")};var gt=[],ht=function(){var t=D();t||Ut.fire(""),t=D();var e=Y(),n=Z(),o=Q();V(e),V(n),E([t,e],x.loading),n.disabled=!0,o.disabled=!0,t.setAttribute("data-loading",!0),t.setAttribute("aria-busy",!0),t.focus()},vt={},bt={title:"",titleText:"",text:"",html:"",footer:"",type:null,toast:!1,customClass:"",customContainerClass:"",target:"body",backdrop:!0,animation:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showCancelButton:!1,preConfirm:null,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:null,confirmButtonClass:"",cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:null,cancelButtonClass:"",buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusCancel:!1,showCloseButton:!1,closeButtonAriaLabel:"Close this dialog",showLoaderOnConfirm:!1,imageUrl:null,imageWidth:null,imageHeight:null,imageAlt:"",imageClass:"",timer:null,width:null,padding:null,background:null,input:null,inputPlaceholder:"",inputValue:"",inputOptions:{},inputAutoTrim:!0,inputClass:"",inputAttributes:{},inputValidator:null,validationMessage:null,grow:!1,position:"center",progressSteps:[],currentProgressStep:null,progressStepsDistance:null,onBeforeOpen:null,onAfterClose:null,onOpen:null,onClose:null,scrollbarPadding:!0},yt={customContainerClass:"customClass",confirmButtonClass:"customClass",cancelButtonClass:"customClass",imageClass:"customClass",inputClass:"customClass"},wt=["allowOutsideClick","allowEnterKey","backdrop","focusConfirm","focusCancel","heightAuto","keydownListenerCapture"],Ct=function(t){return bt.hasOwnProperty(t)},kt=function(t){return yt[t]},Bt=Object.freeze({isValidParameter:Ct,isUpdatableParameter:function(t){return-1!==["customClass","title","titleText","text","html","type","showConfirmButton","showCancelButton","confirmButtonText","confirmButtonAriaLabel","confirmButtonColor","confirmButtonClass","cancelButtonText","cancelButtonAriaLabel","cancelButtonColor","cancelButtonClass","buttonsStyling","reverseButtons","imageUrl","imageWidth","imageHeigth","imageAlt","imageClass","progressSteps","currentProgressStep"].indexOf(t)},isDeprecatedParameter:kt,argsToParams:function(n){var o={};switch(w(n[0])){case"object":s(o,n[0]);break;default:["title","html","type"].forEach(function(t,e){switch(w(n[e])){case"string":o[t]=n[e];break;case"undefined":break;default:k("Unexpected type of ".concat(t,'! Expected "string", got ').concat(w(n[e])))}})}return o},isVisible:function(){return H(D())},clickConfirm:function(){return Z()&&Z().click()},clickCancel:function(){return Q()&&Q().click()},getContainer:I,getPopup:D,getTitle:_,getContent:z,getImage:W,getIcon:function(){var t=U().filter(function(t){return H(t)});return t.length?t[0]:null},getIcons:U,getCloseButton:X,getActions:Y,getConfirmButton:Z,getCancelButton:Q,getHeader:$,getFooter:J,getFocusableElements:G,getValidationMessage:F,isLoading:function(){return D().hasAttribute("data-loading")},fire:function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return o(this,e)},mixin:function(i){return function(t){function e(){return a(this,e),l(this,c(e).apply(this,arguments))}var n,o;return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&u(t,e)}(e,t),r((n=e).prototype,[{key:"_main",value:function(t){return d(c(e.prototype),"_main",this).call(this,s({},i,t))}}]),o&&r(n,o),e}(this)},queue:function(t){var r=this;gt=t;var a=function(t,e){gt=[],document.body.removeAttribute("data-swal2-queue-step"),t(e)},s=[];return new Promise(function(i){!function e(n,o){n<gt.length?(document.body.setAttribute("data-swal2-queue-step",n),r.fire(gt[n]).then(function(t){void 0!==t.value?(s.push(t.value),e(n+1,o)):a(i,{dismiss:t.dismiss})})):a(i,{value:s})}(0)})},getQueueStep:function(){return document.body.getAttribute("data-swal2-queue-step")},insertQueueStep:function(t,e){return e&&e<gt.length?gt.splice(e,0,t):gt.push(t)},deleteQueueStep:function(t){void 0!==gt[t]&&gt.splice(t,1)},showLoading:ht,enableLoading:ht,getTimerLeft:function(){return vt.timeout&&vt.timeout.getTimerLeft()},stopTimer:function(){return vt.timeout&&vt.timeout.stop()},resumeTimer:function(){return vt.timeout&&vt.timeout.start()},toggleTimer:function(){var t=vt.timeout;return t&&(t.running?t.stop():t.start())},increaseTimer:function(t){return vt.timeout&&vt.timeout.increase(t)},isTimerRunning:function(){return vt.timeout&&vt.timeout.isRunning()}}),xt={promise:new WeakMap,innerParams:new WeakMap,domCache:new WeakMap};function St(){var t=xt.innerParams.get(this),e=xt.domCache.get(this);t.showConfirmButton||(q(e.confirmButton),t.showCancelButton||q(e.actions)),O([e.popup,e.actions],x.loading),e.popup.removeAttribute("aria-busy"),e.popup.removeAttribute("data-loading"),e.confirmButton.disabled=!1,e.cancelButton.disabled=!1}var At=function(){null===v.previousBodyPadding&&document.body.scrollHeight>window.innerHeight&&(v.previousBodyPadding=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=v.previousBodyPadding+function(){if("ontouchstart"in window||navigator.msMaxTouchPoints)return 0;var t=document.createElement("div");t.style.width="50px",t.style.height="50px",t.style.overflow="scroll",document.body.appendChild(t);var e=t.offsetWidth-t.clientWidth;return document.body.removeChild(t),e}()+"px")},Pt=function(){return!!window.MSInputMethodContext&&!!document.documentMode},Lt=function(){var t=I(),e=D();t.style.removeProperty("align-items"),e.offsetTop<0&&(t.style.alignItems="flex-start")},Et={swalPromiseResolve:new WeakMap};function Ot(t){var e=I(),n=D(),o=xt.innerParams.get(this),i=Et.swalPromiseResolve.get(this),r=o.onClose,a=o.onAfterClose;if(n){null!==r&&"function"==typeof r&&r(n),O(n,x.show),E(n,x.hide);var s=function(){et()?Tt(a):(new Promise(function(t){var e=window.scrollX,n=window.scrollY;vt.restoreFocusTimeout=setTimeout(function(){vt.previousActiveElement&&vt.previousActiveElement.focus?(vt.previousActiveElement.focus(),vt.previousActiveElement=null):document.body&&document.body.focus(),t()},100),void 0!==e&&void 0!==n&&window.scrollTo(e,n)}).then(function(){return Tt(a)}),vt.keydownTarget.removeEventListener("keydown",vt.keydownHandler,{capture:vt.keydownListenerCapture}),vt.keydownHandlerAdded=!1),e.parentNode&&e.parentNode.removeChild(e),O([document.documentElement,document.body],[x.shown,x["height-auto"],x["no-backdrop"],x["toast-shown"],x["toast-column"]]),tt()&&(null!==v.previousBodyPadding&&(document.body.style.paddingRight=v.previousBodyPadding+"px",v.previousBodyPadding=null),function(){if(b(document.body,x.iosfix)){var t=parseInt(document.body.style.top,10);O(document.body,x.iosfix),document.body.style.top="",document.body.scrollTop=-1*t}}(),"undefined"!=typeof window&&Pt()&&window.removeEventListener("resize",Lt),p(document.body.children).forEach(function(t){t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")}))};ct&&!b(n,x.noanimation)?n.addEventListener(ct,function t(){n.removeEventListener(ct,t),b(n,x.hide)&&s()}):s(),i(t||{})}}var Tt=function(t){null!==t&&"function"==typeof t&&setTimeout(function(){t()})};function Mt(t,e,n){var o=xt.domCache.get(t);e.forEach(function(t){o[t].disabled=n})}function Vt(t,e){if(!t)return!1;if("radio"===t.type)for(var n=t.parentNode.parentNode.querySelectorAll("input"),o=0;o<n.length;o++)n[o].disabled=e;else t.disabled=e}var qt=function t(e,n){a(this,t);var o,i,r=n;this.running=!1,this.start=function(){return this.running||(this.running=!0,i=new Date,o=setTimeout(e,r)),r},this.stop=function(){return this.running&&(this.running=!1,clearTimeout(o),r-=new Date-i),r},this.increase=function(t){var e=this.running;return e&&this.stop(),r+=t,e&&this.start(),r},this.getTimerLeft=function(){return this.running&&(this.stop(),this.start()),r},this.isRunning=function(){return this.running},this.start()},jt={email:function(t,e){return/^[a-zA-Z0-9.+_-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]{2,24}$/.test(t)?Promise.resolve():Promise.resolve(e||"Invalid email address")},url:function(t,e){return/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)$/.test(t)?Promise.resolve():Promise.resolve(e||"Invalid URL")}};var Ht=function(t){var e=I(),n=D();null!==t.onBeforeOpen&&"function"==typeof t.onBeforeOpen&&t.onBeforeOpen(n),t.animation?(E(n,x.show),E(e,x.fade),O(n,x.hide)):O(n,x.fade),V(n),e.style.overflowY="hidden",ct&&!b(n,x.noanimation)?n.addEventListener(ct,function t(){n.removeEventListener(ct,t),e.style.overflowY="auto"}):e.style.overflowY="auto",E([document.documentElement,document.body,e],x.shown),t.heightAuto&&t.backdrop&&!t.toast&&E([document.documentElement,document.body],x["height-auto"]),tt()&&(t.scrollbarPadding&&At(),function(){if(/iPad|iPhone|iPod/.test(navigator.userAgent)&&!window.MSStream&&!b(document.body,x.iosfix)){var t=document.body.scrollTop;document.body.style.top=-1*t+"px",E(document.body,x.iosfix)}}(),"undefined"!=typeof window&&Pt()&&(Lt(),window.addEventListener("resize",Lt)),p(document.body.children).forEach(function(t){t===I()||function(t,e){if("function"==typeof t.contains)return t.contains(e)}(t,I())||(t.hasAttribute("aria-hidden")&&t.setAttribute("data-previous-aria-hidden",t.getAttribute("aria-hidden")),t.setAttribute("aria-hidden","true"))}),setTimeout(function(){e.scrollTop=0})),et()||vt.previousActiveElement||(vt.previousActiveElement=document.activeElement),null!==t.onOpen&&"function"==typeof t.onOpen&&setTimeout(function(){t.onOpen(n)})},It={select:function(t,e,i){var r=T(t,x.select);e.forEach(function(t){var e=t[0],n=t[1],o=document.createElement("option");o.value=e,o.innerHTML=n,i.inputValue.toString()===e.toString()&&(o.selected=!0),r.appendChild(o)}),r.focus()},radio:function(t,e,a){var s=T(t,x.radio);e.forEach(function(t){var e=t[0],n=t[1],o=document.createElement("input"),i=document.createElement("label");o.type="radio",o.name=x.radio,o.value=e,a.inputValue.toString()===e.toString()&&(o.checked=!0);var r=document.createElement("span");r.innerHTML=n,r.className=x.label,i.appendChild(o),i.appendChild(r),s.appendChild(i)});var n=s.querySelectorAll("input");n.length&&n[0].focus()}};var Rt,Nt=Object.freeze({hideLoading:St,disableLoading:St,getInput:function(t){var e=xt.innerParams.get(t||this);return S(xt.domCache.get(t||this).content,e.input)},close:Ot,closePopup:Ot,closeModal:Ot,closeToast:Ot,enableButtons:function(){Mt(this,["confirmButton","cancelButton"],!1)},disableButtons:function(){Mt(this,["confirmButton","cancelButton"],!0)},enableConfirmButton:function(){n("Swal.disableConfirmButton()","Swal.getConfirmButton().removeAttribute('disabled')"),Mt(this,["confirmButton"],!1)},disableConfirmButton:function(){n("Swal.enableConfirmButton()","Swal.getConfirmButton().setAttribute('disabled', '')"),Mt(this,["confirmButton"],!0)},enableInput:function(){return Vt(this.getInput(),!1)},disableInput:function(){return Vt(this.getInput(),!0)},showValidationMessage:function(t){var e=xt.domCache.get(this);e.validationMessage.innerHTML=t;var n=window.getComputedStyle(e.popup);e.validationMessage.style.marginLeft="-".concat(n.getPropertyValue("padding-left")),e.validationMessage.style.marginRight="-".concat(n.getPropertyValue("padding-right")),V(e.validationMessage);var o=this.getInput();o&&(o.setAttribute("aria-invalid",!0),o.setAttribute("aria-describedBy",x["validation-message"]),P(o),E(o,x.inputerror))},resetValidationMessage:function(){var t=xt.domCache.get(this);t.validationMessage&&q(t.validationMessage);var e=this.getInput();e&&(e.removeAttribute("aria-invalid"),e.removeAttribute("aria-describedBy"),O(e,x.inputerror))},getProgressSteps:function(){return n("Swal.getProgressSteps()","const swalInstance = Swal.fire({progressSteps: ['1', '2', '3']}); const progressSteps = swalInstance.params.progressSteps"),xt.innerParams.get(this).progressSteps},setProgressSteps:function(t){n("Swal.setProgressSteps()","Swal.update()");var e=s({},xt.innerParams.get(this),{progressSteps:t});xt.innerParams.set(this,e),pt(e)},showProgressSteps:function(){var t=xt.domCache.get(this);V(t.progressSteps)},hideProgressSteps:function(){var t=xt.domCache.get(this);q(t.progressSteps)},_main:function(t){var p=this;!function(t){for(var e in t)Ct(e)||C('Unknown parameter "'.concat(e,'"')),t.toast&&-1!==wt.indexOf(e)&&C('The parameter "'.concat(e,'" is incompatible with toasts')),kt(e)&&n(e,kt(e))}(t);var f=s({},bt,t);!function(e){e.inputValidator||Object.keys(jt).forEach(function(t){e.input===t&&(e.inputValidator=jt[t])}),e.showLoaderOnConfirm&&!e.preConfirm&&C("showLoaderOnConfirm is set to true, but preConfirm is not defined.\nshowLoaderOnConfirm should be used together with preConfirm, see usage example:\nhttps://sweetalert2.github.io/#ajax-request"),e.animation=h(e.animation),(!e.target||"string"==typeof e.target&&!document.querySelector(e.target)||"string"!=typeof e.target&&!e.target.appendChild)&&(C('Target parameter is not valid, defaulting to "body"'),e.target="body"),"string"==typeof e.title&&(e.title=e.title.split("\n").join("<br />"));var t=D(),n="string"==typeof e.target?document.querySelector(e.target):e.target;(!t||t&&n&&t.parentNode!==n.parentNode)&&rt(e)}(f),Object.freeze(f),xt.innerParams.set(this,f),vt.timeout&&(vt.timeout.stop(),delete vt.timeout),clearTimeout(vt.restoreFocusTimeout);var m={popup:D(),container:I(),content:z(),actions:Y(),confirmButton:Z(),cancelButton:Q(),closeButton:X(),validationMessage:F(),progressSteps:K()};xt.domCache.set(this,m),mt(f);var g=this.constructor;return new Promise(function(t){var n=function(t){p.closePopup({value:t})},s=function(t){p.closePopup({dismiss:t})};Et.swalPromiseResolve.set(p,t),f.timer&&(vt.timeout=new qt(function(){s("timer"),delete vt.timeout},f.timer)),f.input&&setTimeout(function(){var t=p.getInput();t&&P(t)},0);for(var c=function(e){f.showLoaderOnConfirm&&g.showLoading(),f.preConfirm?(p.resetValidationMessage(),Promise.resolve().then(function(){return f.preConfirm(e,f.validationMessage)}).then(function(t){H(m.validationMessage)||!1===t?p.hideLoading():n(void 0===t?e:t)})):n(e)},e=function(t){var e=t.target,n=m.confirmButton,o=m.cancelButton,i=n&&(n===e||n.contains(e)),r=o&&(o===e||o.contains(e));switch(t.type){case"click":if(i)if(p.disableButtons(),f.input){var a=function(){var t=p.getInput();if(!t)return null;switch(f.input){case"checkbox":return t.checked?1:0;case"radio":return t.checked?t.value:null;case"file":return t.files.length?t.files[0]:null;default:return f.inputAutoTrim?t.value.trim():t.value}}();f.inputValidator?(p.disableInput(),Promise.resolve().then(function(){return f.inputValidator(a,f.validationMessage)}).then(function(t){p.enableButtons(),p.enableInput(),t?p.showValidationMessage(t):c(a)})):p.getInput().checkValidity()?c(a):(p.enableButtons(),p.showValidationMessage(f.validationMessage))}else c(!0);else r&&(p.disableButtons(),s(g.DismissReason.cancel))}},o=m.popup.querySelectorAll("button"),i=0;i<o.length;i++)o[i].onclick=e,o[i].onmouseover=e,o[i].onmouseout=e,o[i].onmousedown=e;if(m.closeButton.onclick=function(){s(g.DismissReason.close)},f.toast)m.popup.onclick=function(){f.showConfirmButton||f.showCancelButton||f.showCloseButton||f.input||s(g.DismissReason.close)};else{var r=!1;m.popup.onmousedown=function(){m.container.onmouseup=function(t){m.container.onmouseup=void 0,t.target===m.container&&(r=!0)}},m.container.onmousedown=function(){m.popup.onmouseup=function(t){m.popup.onmouseup=void 0,(t.target===m.popup||m.popup.contains(t.target))&&(r=!0)}},m.container.onclick=function(t){r?r=!1:t.target===m.container&&h(f.allowOutsideClick)&&s(g.DismissReason.backdrop)}}f.reverseButtons?m.confirmButton.parentNode.insertBefore(m.cancelButton,m.confirmButton):m.confirmButton.parentNode.insertBefore(m.confirmButton,m.cancelButton);var a=function(t,e){for(var n=G(f.focusCancel),o=0;o<n.length;o++)return(t+=e)===n.length?t=0:-1===t&&(t=n.length-1),n[t].focus();m.popup.focus()};if(vt.keydownHandlerAdded&&(vt.keydownTarget.removeEventListener("keydown",vt.keydownHandler,{capture:vt.keydownListenerCapture}),vt.keydownHandlerAdded=!1),f.toast||(vt.keydownHandler=function(t){return function(t,e){if(e.stopKeydownPropagation&&t.stopPropagation(),"Enter"!==t.key||t.isComposing)if("Tab"===t.key){for(var n=t.target,o=G(e.focusCancel),i=-1,r=0;r<o.length;r++)if(n===o[r]){i=r;break}t.shiftKey?a(i,-1):a(i,1),t.stopPropagation(),t.preventDefault()}else-1!==["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Left","Right","Up","Down"].indexOf(t.key)?document.activeElement===m.confirmButton&&H(m.cancelButton)?m.cancelButton.focus():document.activeElement===m.cancelButton&&H(m.confirmButton)&&m.confirmButton.focus():"Escape"!==t.key&&"Esc"!==t.key||!0!==h(e.allowEscapeKey)||(t.preventDefault(),s(g.DismissReason.esc));else if(t.target&&p.getInput()&&t.target.outerHTML===p.getInput().outerHTML){if(-1!==["textarea","file"].indexOf(e.input))return;g.clickConfirm(),t.preventDefault()}}(t,f)},vt.keydownTarget=f.keydownListenerCapture?window:m.popup,vt.keydownListenerCapture=f.keydownListenerCapture,vt.keydownTarget.addEventListener("keydown",vt.keydownHandler,{capture:vt.keydownListenerCapture}),vt.keydownHandlerAdded=!0),p.enableButtons(),p.hideLoading(),p.resetValidationMessage(),f.toast&&(f.input||f.footer||f.showCloseButton)?E(document.body,x["toast-column"]):O(document.body,x["toast-column"]),"select"===f.input||"radio"===f.input){var u=z(),l=function(t){return It[f.input](u,(e=t,n=[],"undefined"!=typeof Map&&e instanceof Map?e.forEach(function(t,e){n.push([e,t])}):Object.keys(e).forEach(function(t){n.push([t,e[t]])}),n),f);var e,n};B(f.inputOptions)?(g.showLoading(),f.inputOptions.then(function(t){p.hideLoading(),l(t)})):"object"===w(f.inputOptions)?l(f.inputOptions):k("Unexpected type of inputOptions! Expected object, Map or Promise, got ".concat(w(f.inputOptions)))}else if(-1!==["text","email","number","tel","textarea"].indexOf(f.input)&&B(f.inputValue)){var d=g.getInput();g.showLoading(),q(d),f.inputValue.then(function(t){d.value="number"===f.input?parseFloat(t)||0:t+"",V(d),d.focus(),p.hideLoading()}).catch(function(t){k("Error in inputValue promise: "+t),d.value="",V(d),d.focus(),p.hideLoading()})}Ht(f),f.toast||(h(f.allowEnterKey)?f.focusCancel&&H(m.cancelButton)?m.cancelButton.focus():f.focusConfirm&&H(m.confirmButton)?m.confirmButton.focus():a(-1,1):document.activeElement&&"function"==typeof document.activeElement.blur&&document.activeElement.blur()),m.container.scrollTop=0})},update:function(e){var n={};Object.keys(e).forEach(function(t){Ut.isUpdatableParameter(t)?n[t]=e[t]:C('Invalid parameter to update: "'.concat(t,'". Updatable params are listed here: https://github.com/sweetalert2/sweetalert2/blob/master/src/utils/params.js'))});var t=s({},xt.innerParams.get(this),n);mt(t),xt.innerParams.set(this,t),Object.defineProperties(this,{params:{value:s({},this.params,e),writable:!1,enumerable:!0}})}});function Dt(){if("undefined"!=typeof window){"undefined"==typeof Promise&&k("This package requires a Promise library, please include a shim to enable it in this browser (See: https://github.com/sweetalert2/sweetalert2/wiki/Migration-from-SweetAlert-to-SweetAlert2#1-ie-support)"),Rt=this;for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=Object.freeze(this.constructor.argsToParams(e));Object.defineProperties(this,{params:{value:o,writable:!1,enumerable:!0,configurable:!0}});var i=this._main(this.params);xt.promise.set(this,i)}}Dt.prototype.then=function(t){return xt.promise.get(this).then(t)},Dt.prototype.finally=function(t){return xt.promise.get(this).finally(t)},s(Dt.prototype,Nt),s(Dt,Bt),Object.keys(Nt).forEach(function(e){Dt[e]=function(){var t;if(Rt)return(t=Rt)[e].apply(t,arguments)}}),Dt.DismissReason=t,Dt.version="8.8.0";var Ut=Dt;return Ut.default=Ut}),"undefined"!=typeof window&&window.Sweetalert2&&(window.swal=window.sweetAlert=window.Swal=window.SweetAlert=window.Sweetalert2);