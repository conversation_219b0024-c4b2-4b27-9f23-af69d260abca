INSERT INTO expertise_bodyworks (
    expertise_id, `key`, orijinal, boyali, degisen, duz, note
)
SELECT
    expertises.id as expertise_id,
    t116_testalanlari.transfer_key as `key`,
    CASE WHEN t440_kaporta.T440_orjinal = 1 THEN 1 ELSE 0 END as orijinal,
    CASE WHEN t440_kaporta.T440_boya = 1 THEN 1 ELSE 0 END as boyali,
    CASE WHEN t440_kaporta.T440_degisen = 1 THEN 1 ELSE 0 END as degisen,
    CASE WHEN t440_kaporta.T440_duz = 1 THEN 1 ELSE 0 END as duz,
    t440_kaporta.T440_aciklama as note
FROM t440_kaporta
         JOIN expertises ON expertises.uuid = t440_kaporta.T440_servis_UQ
         JOIN t400_srvbaslik ON t400_srvbaslik.T400_UQ = t440_kaporta.T440_servis_UQ
         JOIN t116_testalanlari ON t116_testalanlari.T116_kod = t440_kaporta.T440_alan_kodu
