INSERT INTO expertise_co2(expertise_id, `key`, status, `note`, created_at, updated_at, deleted_at)
SELECT
    e.id AS expertise_id,
    CASE
        WHEN t451.t451_sonuc_kodu = 1040 THEN 'sorunsuz'
        ELSE 'sorunlu'
        END AS `key`,
    1 AS status,
    NULL AS `note`,
    e.created_at AS created_at,
    e.updated_at AS updated_at,
    e.deleted_at AS deleted_at
FROM
    t451_mekaniktse t451
        JOIN expertises e ON e.uuid = t451.t451_servis_uq
WHERE t451.t451_alan_kodu = 109 AND t451.t451_sonuc_kodu IN (1040, 1050);
