INSERT INTO expertise_components (expertise_id, `key`, status,answer, note)
SELECT
    expertises.id as expertise_id,
    test_alan.transfer_key as `key`,
    CASE
        WHEN component.T451_sonuc_kodu = 1060 THEN 4
        WHEN component.T451_sonuc_kodu = 1030 THEN 1
        WHEN component.T451_sonuc_kodu = 1010 THEN 3
        WHEN component.T451_sonuc_kodu = 1120 THEN 5
        WHEN component.T451_sonuc_kodu = 1020 THEN 2
        ELSE 0
        END as status,
    t140_testsonuckod.value as answer,
    component.T451_aciklama as note
FROM t451_mekaniktse as component
         JOIN expertises ON expertises.uuid = component.T451_servis_UQ
         JOIN t116_testalanlari as test_alan ON test_alan.T116_kod = component.T451_alan_kodu
         LEFT JOIN t140_testsonuckod ON t140_testsonuckod.T140_kod = component.T451_sonuc_kodu
WHERE test_alan.T116_test_kodu = 8
  AND test_alan.T116_grupno IN (3,6)
  AND component.T451_alan_kodu IN (302,308,600,601,602,603)
