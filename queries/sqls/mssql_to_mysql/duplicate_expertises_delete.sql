DELETE FROM expertises
WHERE id IN (
    SELECT id
    FROM (
             SELECT e1.id
             FROM expertises e1
                      JOIN expertises e2
                           ON e1.uuid = e2.uuid AND e1.id > e2.id
             WHERE e1.uuid IN (
                 SELECT uuid
                 FROM expertises
                 WHERE old_id IS NOT NULL
                 GROUP BY uuid
                 HAVING COUNT(*) > 1
             )
         ) AS subquery
);
