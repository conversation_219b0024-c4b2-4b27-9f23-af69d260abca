INSERT INTO expertise_stocks (
    expertise_id, stock_id, campaign_id, sorgu_hizmeti, yol_yardimi, iskonto_amount,
    hizmet_tutari, liste_fiyati,created_at,updated_at,deleted_at
)
SELECT expertises.id as expertise_id,
       stocks.id as stock_id,
       0 as campaign_id,
       stocks.sorgu_hizmeti as sorgu_hizmeti,
       stocks.yol_yardimi as yol_yardimi,
       NULL as iskonto_amount,
       CASE
           WHEN t400_srvbaslik.T400_tltahsilattutari IS NOT NULL THEN t400_srvbaslik.T400_tltahsilattutari
           WHEN t400_srvbaslik.T400_kredi_tutar IS NOT NULL THEN t400_srvbaslik.T400_kredi_tutar
           ELSE t400_srvbaslik.T400_tlsatistutari
           END AS hizmet_tutari,
       t400_srvbaslik.T400_t<PERSON><PERSON><PERSON><PERSON><PERSON> as liste_fiyati,
       t400_srvbaslik.T400_belgetarihi as created_at,
       t400_srvbaslik.T400_belgetarihi as updated_at,
       NULL as deleted_at
FROM t400_srvbaslik
         LEFT JOIN expertises ON expertises.uuid = t400_srvbaslik.T400_UQ
         LEFT JOIN stocks ON stocks.kod = t400_srvbaslik.T400_hizmet_UQ
WHERE expertises.id IS NOT NULL
