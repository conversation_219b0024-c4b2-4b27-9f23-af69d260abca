INSERT INTO expertise_internal_controls (expertise_id, `key`, sorunlu_mu, answer, note)
SELECT
    expertises.id as expertise_id,
    t116_testalanlari.transfer_key as `key`,
    CASE
        WHEN t451_mekaniktse.T451_sonuc_kodu = 1050 THEN 1
        ELSE 0
        END as sorunlu_mu,
    t140_testsonuckod.value as answer,
    t451_mekaniktse.T451_aciklama as note
FROM t451_mekaniktse
         INNER JOIN expertises ON expertises.uuid = t451_mekaniktse.T451_servis_UQ
         LEFT JOIN t140_testsonuckod ON t140_testsonuckod.T140_kod = t451_mekaniktse.T451_sonuc_kodu
         LEFT JOIN t116_testalanlari ON t116_testalanlari.t116_kod = t451_mekaniktse.T451_alan_kodu
WHERE t451_mekaniktse.T451_alan_kodu BETWEEN 200 AND 210
