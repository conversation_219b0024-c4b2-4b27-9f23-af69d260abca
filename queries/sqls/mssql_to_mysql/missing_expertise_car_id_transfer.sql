INSERT INTO cars (branch_id, customer_id, plaka, sase_no, car_case_type_id, car_fuels_id, car_gears_id, model_yili, motor_hacmi, km, uq)
SELECT
    null as branch_id,
    0 as customer_id,
    t202_araclar.t202_plakano as plaka,
    t202_araclar.t202_saseno as sase_no,
    CASE
        WHEN t202_araclar.t202_kasatipikodu = 1 THEN 16
        WHEN t202_araclar.t202_kasatipikodu = 2 THEN 12
        WHEN t202_araclar.t202_kasatipikodu = 3 THEN 11
        WHEN t202_araclar.t202_kasatipikodu = 4 THEN 5
        WHEN t202_araclar.t202_kasatipikodu = 5 THEN 4
        WHEN t202_araclar.t202_kasatipikodu = 6 THEN 10
        WHEN t202_araclar.t202_kasatipikodu = 7 THEN 9
        WHEN t202_araclar.t202_kasatipikodu = 8 THEN 6
        WHEN t202_araclar.t202_kasatipikodu = 9 THEN 8
        WHEN t202_araclar.t202_kasatipikodu = 10 THEN 13
        WHEN t202_araclar.t202_kasatipikodu = 11 THEN 0
        WHEN t202_araclar.t202_kasatipikodu = 12 THEN 14
        WHEN t202_araclar.t202_kasatipikodu = 13 THEN 7
        WHEN t202_araclar.t202_kasatipikodu = 14 THEN 15
        WHEN t202_araclar.t202_kasatipikodu = 15 THEN 1
        WHEN t202_araclar.t202_kasatipikodu = 16 THEN 3
        WHEN t202_araclar.t202_kasatipikodu = 17 THEN 19
        WHEN t202_araclar.t202_kasatipikodu = 18 THEN 20

        WHEN t202_araclar.t202_kasatipikodu = 22 THEN 8
        WHEN t202_araclar.t202_kasatipikodu = 23 THEN 18
        ELSE
            0
        END as car_case_type_id,
    CASE
        WHEN t202_araclar.t202_motortipikodu = 1 THEN 1
        WHEN t202_araclar.t202_motortipikodu = 2 THEN 4
        WHEN t202_araclar.t202_motortipikodu = 3 THEN 2
        WHEN t202_araclar.t202_motortipikodu = 4 THEN 6
        WHEN t202_araclar.t202_motortipikodu = 6 THEN 8
        WHEN t202_araclar.t202_motortipikodu = 7 THEN 5
        WHEN t202_araclar.t202_motortipikodu = 8 THEN 6
        WHEN t202_araclar.t202_motortipikodu = 9 THEN 7
        ELSE
            0
        END as car_fuels_id,
    t202_araclar.t202_vitestipikodu as car_gears_id,
    t202_araclar.t202_modelyili as model_yili,
    t202_araclar.t202_motor_hacmi as motor_hacmi,
    t202_araclar.t202_arackm as km,
    t202_araclar.t202_uq as uq
FROM t202_araclar
         LEFT JOIN t400_srvbaslik on t400_srvbaslik.t400_arac_uq = t202_araclar.t202_uq
         LEFT JOIN expertises ON expertises.uuid = t400_srvbaslik.t400_uq
WHERE expertises.old_id is not null AND expertises.car_id is null;



-------------------------------- THEN --------------------------------------

UPDATE expertises JOIN t400_srvbaslik on t400_srvbaslik.t400_uq = expertises.uuid
    LEFT JOIN cars ON cars.uq = t400_srvbaslik.t400_arac_uq
    SET expertises.car_id = cars.id
WHERE expertises.old_id IS NOT NULL
  AND expertises.car_id IS NULL
