INSERT INTO expertise_payments(expertise_id, case_id, amount, type,created_at, updated_at, update_data)
SELECT
    expertises.id as expertise_id,
    0 as case_id,
    CASE
        WHEN t400_srvbaslik.T400_tahsilatbelgetipi = 410 THEN t400_srvbaslik.T400_tltahsilattutari
        WHEN t400_srvbaslik.T400_tahsilatbelgetipi = 420 THEN
            CASE
                WHEN t400_srvbaslik.T400_tltahsilattutari IS NOT NULL
                    THEN t400_srvbaslik.T400_tltahsilattutari
                ELSE t400_srvbaslik.T400_kredi_tutar
                END
        WHEN t400_srvbaslik.T400_tahsilatbelgetipi = 430 THEN t400_srvbaslik.T400_kredi_tutar
        WHEN t400_srvbaslik.T400_tahsilatbelgetipi = 9999 THEN(
        t400_srvbaslik.T400_kredi_tutar + t400_srvbaslik.T400_tltahsilattutari
        )
         WHEN
             t400_srvbaslik.T400_tahsilatbelgetipi = 0 THEN(
             t400_srvbaslik.t400_tlsatistutari
             )
        END as amount,
    CASE
        WHEN t400_srvbaslik.T400_tahsilatbelgetipi = 410 THEN 'nakit'
        WHEN t400_srvbaslik.T400_tahsilatbelgetipi = 420 THEN 'kredi_karti'
        WHEN t400_srvbaslik.T400_tahsilatbelgetipi = 430 THEN 'kredi_karti'
        WHEN t400_srvbaslik.T400_tahsilatbelgetipi = 9999 THEN 'kredi_karti'
        WHEN t400_srvbaslik.T400_tahsilatbelgetipi = 0 THEN 'yok'
    END as type,
    t400_srvbaslik.T400_belgetarihi as created_at,
    t400_srvbaslik.T400_belgetarihi as updated_at,
    2 as update_data
from
    t400_srvbaslik
        LEFT JOIN expertises ON expertises.uuid = t400_srvbaslik.T400_UQ
WHERE
    t400_srvbaslik.T400_tahsilatbelgetipi != 431
