INSERT IGNORE INTO expertises (
                    old_id, uuid, user_id, kayit_branch_id, branch_id, cari_id, satici_id, alici_id, car_id, km, sase_no, km_type, net_agirlik, nereden_ulast<PERSON>z,
                    sorgu_hizmeti, belge_tarihi, belge_no, belge_ozel_kodu, sigorta_teklif_ver, yayin_yasagi, kayit_yeri, status, odeme_turu, bodywork_image,
                    diagnostic_file, arac_kontrol_user, arac_kontrol, fren_kontrol_user, fren_kontrol, kaporta_kontrol_user, kaporta_kontrol,
                    diagnostic_kontrol_user, diagnostic_kontrol, ic_kontrol_user, ic_kontrol, lastik_jant_kontrol_user, lastik_jant_kontrol,
                    alt_motor_kontrol_user, alt_motor_kontrol, komponent_kontrol_user, komponent_kontrol, co2_kontrol_user, co2_kontrol, hasar_sorgu_sonuc,
                    kilometre_sorgu_sonuc, borc_sorgu_sonuc, ruhsat_sorgu_sonuc, payment_type, plus_card_payment_type, plus_kart_id, audio_url,
                    cikis_tarihi, manuel_save, employee_downloaded, ftp_ok, ftp_date, created_at, updated_at, deleted_at
                )
SELECT
    expertises.T400_ID AS old_id,
    expertises.T400_UQ AS uuid,
    0 AS user_id,
    kayitBranch.id AS kayit_branch_id,
    branch.id AS branch_id,
    satici.id AS cari_id,
    satici.id AS satici_id,
    alici.id AS alici_id,
    car.id AS car_id,
    expertises.T400_arackm AS km,
    car.sase_no AS sase_no,
    1 AS km_type,
    expertises.T400_agirlik AS net_agirlik,
    grup_deger.T122_aciklama AS nereden_ulastiniz,
    expertises.T400_hasar_sorgulama AS sorgu_hizmeti,
    expertises.T400_belgetarihi as belge_tarihi,
    expertises.T400_belgeno as belge_no,
    CASE WHEN expertises.T400_belgeozelkodu IN (0, -1) THEN 0 ELSE 1 END AS belge_ozel_kodu,
    expertises.T400_sigorta_teklifi AS sigorta_teklif_ver,
    expertises.T400_sitedeyayinyasak AS yayin_yasagi,
    expertises.T400_kayit_yeri as kayit_yeri,
    1 AS status,
    NULL as odeme_turu,
    NULL AS bodywork_image,
    NULL AS diagnostic_file,
    NULL AS arac_kontrol_user,
    CASE WHEN stock.car = 1 THEN 1 ELSE 0 END AS arac_kontrol,
    NULL AS fren_kontrol_user,
    CASE WHEN stock.brake = 1 THEN 1 ELSE 0 END AS fren_kontrol,
    NULL AS kaporta_kontrol_user,
    CASE WHEN stock.bodywork = 1 THEN 1 ELSE 0 END AS kaporta_kontrol,
    NULL AS diagnostic_kontrol_user,
    CASE WHEN stock.diagnostic = 1 THEN 1 ELSE 0 END AS diagnostic_kontrol,
    NULL AS ic_kontrol_user,
    CASE WHEN stock.internal_control = 1 THEN 1 ELSE 0 END AS ic_kontrol,
    NULL AS lastik_jant_kontrol_user,
    CASE WHEN stock.tire_and_rim = 1 THEN 1 ELSE 0 END AS lastik_jant_kontrol,
    NULL AS alt_motor_kontrol_user,
    CASE WHEN stock.sub_control_and_engine = 1 THEN 1 ELSE 0 END AS alt_motor_kontrol,
    NULL AS komponent_kontrol_user,
    CASE WHEN stock.component = 1 THEN 1 ELSE 0 END AS komponent_kontrol,
    NULL AS co2_kontrol_user,
    CASE WHEN stock.co2 = 1 THEN 1 ELSE 0 END AS co2_kontrol,
    NULL AS hasar_sorgu_sonuc,
    NULL AS kilometre_sorgu_sonuc,
    NULL AS borc_sorgu_sonuc,
    NULL AS ruhsat_sorgu_sonuc,
    CASE
        WHEN expertises.T400_tahsilatbelgetipi = 431 THEN 'plus_kart'
        WHEN expertises.T400_tahsilatbelgetipi IS NOT NULL THEN 'normal'
        ELSE NULL
        END AS payment_type,
    CASE
        WHEN t404_kupontakip.T404_kayittipi = 1 THEN 'kredi'
        WHEN t404_kupontakip.T404_kayittipi IS NOT NULL THEN 'puan'
        ELSE NULL
        END AS plus_card_payment_type,
    plus_card.id AS plus_kart_id,
    NULL AS audio_url,
    expertises.T400_cikistarihi AS cikis_tarihi,
    1 AS manuel_save,
    1 AS employee_downloaded,
    1 AS ftp_ok,
    expertises.T400_cikistarihi AS ftp_date,
    expertises.T400_belgetarihi AS created_at,
    expertises.T400_belgetarihi AS updated_at,
    NULL AS deleted_at
FROM t400_srvbaslik AS expertises
         LEFT JOIN t404_kupontakip ON t404_kupontakip.T404_bagli_tablo_uq = expertises.T400_UQ
         LEFT JOIN T200_HESPLAN ON T200_HESPLAN.T200_UQ = expertises.T400_musteri_UQ
         LEFT JOIN branches AS kayitBranch ON kayitBranch.belge_kod = expertises.T400_subekodu
         LEFT JOIN branches AS branch ON branch.belge_kod = expertises.T400_srvsubekodu
         LEFT JOIN customers as satici ON satici.kod = expertises.T400_satici_UQ
         LEFT JOIN customers as alici ON alici.kod = expertises.T400_musteri_UQ
         LEFT JOIN cars AS car ON car.UQ = expertises.T400_arac_UQ
         LEFT JOIN stocks AS stock ON stock.kod = expertises.T400_hizmet_UQ
         LEFT JOIN plus_cards AS plus_card ON plus_card.system_id = T200_HESPLAN.t200_barkodu
         LEFT JOIN t122_grupdeger AS grup_deger ON grup_deger.T122_kod = expertises.T400_reklamulasimyeri
