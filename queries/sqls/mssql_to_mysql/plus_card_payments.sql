INSERT INTO expertise_payments (expertise_id, case_id, amount, type, plus_card_id, created_at, updated_at, update_data)
SELECT
	expertises.id as expertise_id,
    0 as case_id,
    t404_kupontakip.T404_fiyat as amount,
    'plus_kart' as type,
    plus_cards.id as plus_card_id,
    t400_srvbaslik.T400_belgetarihi as created_at,
    t400_srvbaslik.T400_belgetarihi as updated_at,
    2 as update_data
FROM t400_srvbaslik
INNER JOIN expertises On expertises.uuid = t400_srvbaslik.T400_UQ
INNER JOIN plus_cards ON plus_cards.customer_id = expertises.alici_id
Left JOIN t404_kupontakip on t404_kupontakip.T404_UQ = t400_srvbaslik.T400_harcanan_plus_UQ
WHERE t400_srvbaslik.T400_tahsilatbelgetipi = 431
  AND expertises.deleted_at IS NULL
  AND plus_cards.deleted_at IS NULL;



--------------------------------

INSERT INTO expertise_payments (expertise_id, case_id, amount, type, plus_card_id, created_at, updated_at, update_data)
SELECT
    e.id AS expertise_id,
    0 AS case_id,
    t404_kupontakip.T404_fiyat AS amount,
    'plus_kart' AS type,
    pc.id AS plus_card_id,
    srv.T400_belgetarihi AS created_at,
    srv.T400_belgetarihi AS updated_at,
    2 AS update_data
FROM t400_srvbaslik srv
         JOIN customers c ON c.kod = srv.t400_musteri_uq
         JOIN plus_cards pc ON pc.customer_id = c.id
         JOIN expertises e ON e.uuid = srv.t400_uq
         LEFT JOIN t404_kupontakip ON t404_kupontakip.T404_UQ = srv.T400_harcanan_plus_UQ
WHERE srv.t400_tahsilatbelgetipi = 431
  AND e.id NOT IN (SELECT DISTINCT expertise_id FROM expertise_payments);
