DELIMITER $$

CREATE PROCEDURE InsertExpertisePaymentsByBatch()
BEGIN
    DECLARE start_id INT DEFAULT 0;
    DECLARE end_id INT DEFAULT 10000;
    DECLARE max_id INT DEFAULT 1423452;
    
    WHILE start_id < max_id DO
        -- 100,000 ID'lik grubu işle
        INSERT INTO expertise_payments (expertise_id, case_id, amount, type, plus_card_id, created_at, updated_at, update_data)
        SELECT
            expertises.id AS expertise_id,
            0 AS case_id,
            t404_kupontakip.T404_fiyat AS amount,
            'plus_kart' AS type,
            min_plus_cards.id AS plus_card_id,
            t400_srvbaslik.T400_belgetarihi AS created_at,
            t400_srvbaslik.T400_belgetarihi AS updated_at,
            2 AS update_data
        FROM t400_srvbaslik
        INNER JOIN expertises ON expertises.uuid = t400_srvbaslik.T400_UQ
        LEFT JOIN t404_kupontakip ON t404_kupontakip.T404_UQ = t400_srvbaslik.T400_harcanan_plus_UQ
        INNER JOIN (
            SELECT customer_id, MIN(id) AS id
            FROM plus_cards
            WHERE deleted_at IS NULL
            GROUP BY customer_id
        ) AS min_plus_cards ON min_plus_cards.customer_id = expertises.alici_id
        WHERE t400_srvbaslik.T400_tahsilatbelgetipi = 431 
          AND expertises.deleted_at IS NULL
          AND t400_srvbaslik.T400_ID >= start_id 
          AND t400_srvbaslik.T400_ID < end_id;
        
        -- ID aralığını güncelle
        SET start_id = end_id;
        SET end_id = end_id + 10000;

        -- Bitiş sınırını kontrol et
        IF end_id > max_id THEN
            SET end_id = max_id + 1;
        END IF;
    END WHILE;
END$$

DELIMITER ;
CALL InsertExpertisePaymentsByBatch();
