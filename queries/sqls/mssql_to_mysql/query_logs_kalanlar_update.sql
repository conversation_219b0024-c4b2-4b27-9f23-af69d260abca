UPDATE query_logs JOIN t405_sorgubaslik on t405_sorgubaslik.t405_servis_uq = query_logs.uuid
    SET `kilometre` = t405_sorgubaslik.t405_json, `kilometre_ucret` = t405_sorgubaslik.t405_tutar
WHERE query_logs.kilometre IS NULL
  AND query_logs.user_id IS NULL
  AND t405_sorgubaslik.t405_aktif = 1
  AND t405_sorgubaslik.T405_islem_tipi = 2
  AND t405_sorgubaslik.T405_tarih IS NOT NULL AND t405_sorgubaslik.T405_tutar IS NOT NULL;



UPDATE query_logs JOIN t405_sorgubaslik on t405_sorgubaslik.t405_servis_uq = query_logs.uuid
    SET `borc` = t405_sorgubaslik.t405_json, `borc_ucret` = t405_sorgubaslik.t405_tutar
WHERE query_logs.borc IS NULL AND query_logs.user_id IS NULL AND t405_sorgubaslik.t405_aktif = 1
  AND t405_sorgubaslik.T405_islem_tipi = 3 AND t405_sorgubaslik.T405_tarih IS NOT NULL AND t405_sorgubaslik.T405_tutar IS NOT NULL;

UPDATE query_logs JOIN t405_sorgubaslik on t405_sorgubaslik.t405_servis_uq = query_logs.uuid
    SET `ruhsat` = t405_sorgubaslik.t405_json, `ruhsat_ucret` = t405_sorgubaslik.t405_tutar
WHERE query_logs.ruhsat IS NULL
  AND query_logs.user_id IS NULL
  AND t405_sorgubaslik.t405_aktif = 1
  AND t405_sorgubaslik.T405_islem_tipi = 4
  AND t405_sorgubaslik.T405_tarih IS NOT NULL AND t405_sorgubaslik.T405_tutar IS NOT NULL;
