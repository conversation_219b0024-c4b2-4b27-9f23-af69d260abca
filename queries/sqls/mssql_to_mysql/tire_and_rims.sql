INSERT INTO expertise_tire_and_rims (expertise_id, `key`, sorunlu_mu,note, dis, basinc)
SELECT
    expertises.id AS expertise_id,
    t116_testalanlari.transfer_key AS `key`,
    CASE WHEN mekanik.T451_sonuc_kodu = 1050 THEN 1 ELSE 0 END AS sorunlu_mu,
    mekanik.T451_aciklama as note,
    mekanik.T451_deger_1 AS dis,
    mekanik.T451_deger_2 AS basinc
FROM t451_mekaniktse AS mekanik
         LEFT JOIN t116_testalanlari ON t116_testalanlari.T116_kod = mekanik.T451_alan_kodu
         LEFT JOIN t140_testsonuckod ON t140_testsonuckod.T140_kod = mekanik.T451_sonuc_kodu
         JOIN expertises ON expertises.uuid = mekanik.T451_servis_UQ
WHERE mekanik.T451_alan_kodu BETWEEN 400 AND 403
  AND t116_testalanlari.T116_grupno = 4;
