insert into customers(branch_id,eski_cari_kod,kod,unvan,cep,telefon,type,vergi_no,tc_no)
SELECT
    1 as branch_id,
    t200_hesplan.t200_hesa<PERSON><PERSON><PERSON> as eski_cari_kod,
    t201_cariler.t201_hesap_uq as kod,
    CASE
        WHEN t201_cariler.t201_unvan is not null or t201_cariler.t201_unvan != '' THEN t201_cariler.t201_unvan
        ELSE t201_cariler.t201_adi + t201_cariler.t201_soyadi
        <PERSON> as unvan,
    t201_cariler.t201_cep as cep,
    t201_cariler.t201_telefon as telefon,
    'bireysel' as type,
    t201_cariler.t201_vergino as vergi_no,
    t201_cariler.t201_tckimlikno as tc_no
FROM expertises
         JOIN t400_srvbaslik on expertises.uuid = t400_srvbaslik.t400_uq
         LEFT JOIN t201_cariler on t400_srvbaslik.t400_musteri_uq = t201_cariler.t201_hesap_uq
         LEFT JOIN t200_hesplan on t200_hesplan.t200_uq = t201_cariler.t201_hesap_uq
WHERE expertises.alici_id is null



-------- THEN

UPDATE expertises
    JOIN t400_srvbaslik ON t400_srvbaslik.t400_uq = expertises.uuid
    LEFT JOIN customers ON customers.kod = t400_srvbaslik.t400_musteri_uq
    SET expertises.alici_id = customers.id
WHERE expertises.alici_id IS NULL
