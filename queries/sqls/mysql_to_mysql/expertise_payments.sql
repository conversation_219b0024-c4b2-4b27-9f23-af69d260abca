INSERT IGNORE INTO umramdb_backup.expertise_payments (
    expertise_id,case_id,amount,type,payment_code,payment_detail,plus_card_id,plus_card_odeme_id,
       result, created_at, updated_at, deleted_at,update_data
)
SELECT
    expertise_id + 1000000 as expertise_id,case_id,amount,type,payment_code,payment_detail,plus_card_id,plus_card_odeme_id,
    result, created_at, updated_at, deleted_at,update_data
FROM dbtest_db.expertise_payments;
