-- id'si 11.000 den dü<PERSON><PERSON><PERSON> cariler

UPDATE cars
SET uq = UUID()
WHERE uq IS NULL;



INSERT INTO umramdb_backup.cars (
    branch_id, `customer_id`, `plaka`, `sase_no`, `car_group_tip_id`, `car_group_marka_id`, `car_group_model_id`, `car_case_type_id`,
    `car_fuels_id`, `car_gears_id`, `motor_hacmi`, `model_yili`, `cekis`, `km`, `km_type`, `il_kodu`, `ilce_kodu`, `ilk_tescil_tarihi`,
    `tescil_sira_no`, `tescil_tarihi`, `cinsi`, `renk`, `motor_no`, `net_agirlik`, `azami_agirlik`, `katar_agirlik`, `romork_agirlik`,
    `koltuk_sayisi`, `ayakta_yolcu_sayisi`, `silindir_hacmi`, `motor_gucu`, `guc_agirlik_orani`, `k<PERSON><PERSON><PERSON>_amaci`, `tip_onay_no`,
    `status`, `created_at`, `updated_at`, `uq`
)
SELECT
    branch_id, `customer_id`, `plaka`, `sase_no`, `car_group_tip_id`, `car_group_marka_id`, `car_group_model_id`, `car_case_type_id`,
    `car_fuels_id`, `car_gears_id`, `motor_hacmi`, `model_yili`, `cekis`, `km`, `km_type`, `il_kodu`, `ilce_kodu`, `ilk_tescil_tarihi`,
    `tescil_sira_no`, `tescil_tarihi`, `cinsi`, `renk`, `motor_no`, `net_agirlik`, `azami_agirlik`, `katar_agirlik`, `romork_agirlik`,
    `koltuk_sayisi`, `ayakta_yolcu_sayisi`, `silindir_hacmi`, `motor_gucu`, `guc_agirlik_orani`, `kullanim_amaci`, `tip_onay_no`,
    `status`, `created_at`, `updated_at`, `uq`
FROM dbtest_db.cars
WHERE dbtest_db.cars.uq NOT IN (
    SELECT DISTINCT umramdb_backup.cars.uq
    FROM umramdb_backup.cars
)
ORDER BY dbtest_db.cars.id DESC;


UPDATE umramdb_backup.expertises ue
    JOIN dbtest_db.t400_srvbaslik t40 ON t40.t400_uq = ue.uuid
    JOIN umramdb_backup.cars uc ON uc.uq = t40.t400_arac_uq
    SET ue.car_id = uc.id
WHERE ue.id > 1000000
