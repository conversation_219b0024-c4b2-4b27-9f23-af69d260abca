INSERT INTO pgsql.branch_informations
SELECT
    mysql.branches.id as branch_id,
    mysql.branches.logo,
    mysql.branches.paynet_code,
    mysql.branches.google_button_2,
    mysql.branches.plus_kart_satis,
    mysql.branches.eksper_merkez,
    mysql.branches.eksper_orani,
    mysql.branches.hasar_sorgulama,
    mysql.branches.plus_kart_yukle,
    mysql.branches.son_fiyat_degisiklik_tarihi,
    mysql.branches.bedelsiz_eksper,
    mysql.branches.max_indirim_tutari,
    mysql.branches.borc_risk_tutari,
    mysql.branches.cari_basi_max_indirim,
    mysql.branches.max_indirim_orani,
    mysql.branches.ip_addresses,
    mysql.branches.sleep_time,
    mysql.branches.max_cari_kod,
    mysql.branches.brake_values_auto,
    mysql.branches.status,
    mysql.branches.created_at,
    mysql.branches.updated_at,
    mysql.branches.deleted_at
FROM
    mysql.branches
