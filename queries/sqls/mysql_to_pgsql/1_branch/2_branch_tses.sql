INSERT INTO pgsql.branch_tses
SELECT
    mysql.branches.id as branch_id
    mysql.branch_tses.tse_kodu,
    mysql.branch_tses.gecer<PERSON><PERSON>_tarihi,
    mysql.branch_tses.created_at,
    mysql.branch_tses.updated_at,
    CASE
        WHEN mysql.branch_tses.status = 1 THEN null
        ELSE mysql.branch_tses.updated_at
        END as deleted_at
FROM
    mysql.branch_tses
        INNER JOIN
    mysql.branches ON mysql.branches.id = mysql.branch_tses.branch_id
