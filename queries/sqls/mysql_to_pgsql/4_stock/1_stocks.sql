INSERT INTO pgsql.stocks
SELECT
    mysql.stocks.kod,
    mysql.stocks.stock_kodu,
    mysql.stocks.ad,
    mysql.stocks.miktar,
    mysql.stocks.kdv,
    mysql.stock_prices.kdv_haric_fiyat,
    mysql.stocks.created_at,
    mysql.stocks.updated_at,
    CASE
        WHEN mysql.stocks.status = -1 THEN mysql.stocks.updated_at
        ELSE null
        END
FROM
    mysql.stocks
        LEFT JOIN mysql.stock_prices ON mysql.stock_prices.stock_id = mysql.stocks.id
WHERE mysql.stock_prices.campaign_id = 0
