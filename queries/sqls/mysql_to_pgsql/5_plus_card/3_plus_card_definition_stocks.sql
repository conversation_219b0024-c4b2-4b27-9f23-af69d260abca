INSERT INTO pgsql.plus_card_definition_stocks
SELECT
    pgsql.plus_card_definitions.id,
    pgsql.stocks.id,
    mysql.plus_card_definition_stocks.created_at,
    mysql.plus_card_definition_stocks.updated_at
FROM mysql.plus_card_definition_stocks
         LEFT JOIN
     mysql.plus_card_definitions ON mysql.plus_card_definitions.id = mysql.plus_card_definition_stocks.definition_id
         LEFT JOIN
     pgsql.plus_card_definitions ON mysql.plus_card_definitions.definition_name = pgsql.plus_card_definitions.definition_name
         LEFT JOIN
     mysql.stocks ON mysql.stocks.id = mysql.plus_card_definition_stocks.stock_id
         LEFT JOIN
     pgsql.stocks ON pgsql.stocks.ad = mysql.stocks.ad
