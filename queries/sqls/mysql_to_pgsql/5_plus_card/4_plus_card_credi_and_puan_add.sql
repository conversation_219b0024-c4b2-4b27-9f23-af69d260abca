INSERT INTO pgsql.plus_card_credi_and_puan_add
SELECT
    mysql.plus_card_credi_and_puan_add.id,
    mysql.plus_card_credi_and_puan_add.user_id,
    pgsql.stocks.id,
    pgsql.plus_card_definitions.id,
    mysql.plus_card_credi_and_puan_add.card_id,
    mysql.plus_card_credi_and_puan_add.balance_type,
    mysql.plus_card_credi_and_puan_add.valid_date,
    mysql.plus_card_credi_and_puan_add.created_at,
    mysql.plus_card_credi_and_puan_add.updated_at,
    mysql.plus_card_credi_and_puan_add.deleted_at
FROM
    mysql.plus_card_credi_and_puan_add
        LEFT JOIN
    mysql.stocks ON mysql.stocks.id = mysql.plus_card_credi_and_puan_add.stock_id
        LEFT JOIN
    pgsql.stocks ON mysql.stocks.ad = pgsql.stocks.ad
        LEFT JOIN
    mysql.plus_card_definitions ON mysql.plus_card_definitions.id = mysql.plus_card_credi_and_puan_add.definition_id
        LEFT JOIN
    pgsql.plus_card_definitions ON mysql.plus_card_definitions.definition_name = pgsql.plus_card_definitions.definition_name
