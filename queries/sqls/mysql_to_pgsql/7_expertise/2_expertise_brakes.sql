INSERT INTO pgsql.expertise_brakes
SELECT
    pgsql.expertise.id as expertise_id,
    pgsql.expertise_definitions.id,
    mysql.expertise_brakes.date,
    mysql.expertise_brakes.yanal_kayma_on,
    mysql.expertise_brakes.yanal_kayma_arka,
    mysql.expertise_brakes.max_kuvvet_on_1,
    mysql.expertise_brakes.max_kuvvet_on_2,
    mysql.expertise_brakes.max_kuvvet_arka_1,
    mysql.expertise_brakes.max_kuvvet_arka_2,
    mysql.expertise_brakes.dengesizlik_orani_on,
    mysql.expertise_brakes.dengesizlik_orani_arka,
    mysql.expertise_brakes.el_freni_dengesizlik_orani,
    mysql.expertise_brakes.yalpa_orani_on_1,
    mysql.expertise_brakes.yalpa_orani_on_2,
    mysql.expertise_brakes.yalpa_orani_arka_1,
    mysql.expertise_brakes.yalpa_orani_arka_2,
    mysql.expertise_brakes.suspansiyon_on_1,
    mysql.expertise_brakes.suspansiyon_on_2,
    mysql.expertise_brakes.suspansiyon_arka_1,
    mysql.expertise_brakes.suspansiyon_arka_2,
    mysql.expertise_brakes.on_dingil_bosta_a,
    mysql.expertise_brakes.on_dingil_bosta_b,
    mysql.expertise_brakes.arka_dingil_bosta_a,
    mysql.expertise_brakes.arka_dingil_bosta_b,
    mysql.expertise_brakes.el_freni_kuvvet_a,
    mysql.expertise_brakes.el_freni_kuvvet_b,
    mysql.expertise_brakes.type,
    mysql.expertise_brakes.created_at,
    mysql.expertise_brakes.updated_at
FROM
    mysql.expertise_brakes
        JOIN
    mysql.expertises ON pgsql.expertises.id = mysql.expertises.id
        JOIN
    pgsql.expertises ON pgsql.expertises.uuid = mysql.expertises.uuid
        JOIN
    pgsql.expertise_definitions ON pgsql.expertise_definitions.key = mysql.expertise_brakes
WHERE
    pgsql.expertise_definitions.place = 'brake'

