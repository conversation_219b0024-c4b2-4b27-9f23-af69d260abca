INSERT INTO pgsql.expertise_control_statuses
SELECT
    pgsql.expertises.id as expertise_id,
    mysql.expertises.arac_kontrol_user,
    mysql.expertises.arac_kontrol,
    mysql.expertises.fren_kontrol_user,
    mysql.expertises.fren_kontrol,
    mysql.expertises.kaporta_kontrol_user,
    mysql.expertises.kaporta_kontrol,
    mysql.expertises.diagnostic_kontrol_user,
    mysql.expertises.diagnostic_kontrol,
    mysql.expertises.ic_kontrol_user,
    mysql.expertises.ic_kontrol,
    mysql.expertises.lastik_jant_kontrol_user,
    mysql.expertises.lastik_jant_kontrol,
    mysql.expertises.alt_motor_kontrol_user,
    mysql.expertises.alt_motor_kontrol,
    mysql.expertises.komponent_kontrol_user,
    mysql.expertises.komponent_kontrol,
    mysql.expertises.co2_kontrol_user,
    mysql.expertises.co2_kontrol,
    mysql.expertises.createad_at,
    mysql.expertises.updated_at,
    mysql.expertises.deleted_at
FROM
    mysql.expertises
        JOIN
    pgsql.expertises ON pgsql.expertises.uuid = mysql.expertises.uuid
