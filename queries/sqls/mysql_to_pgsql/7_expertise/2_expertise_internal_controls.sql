INSERT INTO pgsql.expertise_internal_controls
SELECT
    pgsql.expertise.id as expertise_id,
    pgsql.expertise_definitions.id,
    mysql.expertise_internal_controls.sorunlu_mu,
    mysql.expertise_internal_controls.answer,
    mysql.expertise_internal_controls.note,
    mysql.expertise_internal_controls.created_at,
    mysql.expertise_internal_controls.updated_at
FROM
    mysql.expertise_internal_controls
        JOIN
    mysql.expertises ON pgsql.expertises.id = mysql.expertises.id
        JOIN
    pgsql.expertises ON pgsql.expertises.uuid = mysql.expertises.uuid
        JOIN
    pgsql.expertise_definitions ON pgsql.expertise_definitions.key = mysql.expertise_internal_controls
WHERE
    pgsql.expertise_definitions.place = 'internal_control'
