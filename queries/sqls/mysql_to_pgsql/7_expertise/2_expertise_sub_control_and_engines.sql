INSERT INTO pgsql.expertise_sub_control_and_engines
SELECT
    pgsql.expertise.id
    pgsql.expertise_definitions.id,
    mysql.expertise_sub_control_and_engines.status,
    mysql.expertise_sub_control_and_engines.note,
    mysql.expertise_sub_control_and_engines.created_at,
    mysql.expertise_sub_control_and_engines.updated_at
FROM
    mysql.expertise_sub_control_and_engines
        JOIN
    mysql.expertises ON pgsql.expertises.id = mysql.expertises.id
        JOIN
    pgsql.expertises ON pgsql.expertises.uuid = mysql.expertises.uuid
        JOIN
    pgsql.expertise_definitions ON pgsql.expertise_definitions.key = mysql.expertise_sub_control_and_engines
WHERE
    pgsql.expertise_definitions.place = 'sub_control_and_engine'
