INSERT INTO pgsql.expertise_tire_and_rims
SELECT
    pgsql.expertise.id as expertise_id,
    pgsql.expertise_definitions.id,
    mysql.expertise_tire_and_rims.sorunlu_mu,
    mysql.expertise_tire_and_rims.note,
    mysql.expertise_tire_and_rims.dis,
    mysql.expertise_tire_and_rims.basinc,
    mysql.expertise_tire_and_rims.created_at,
    mysql.expertise_tire_and_rims.updated_at
FROM
    mysql.expertise_tire_and_rims
        JOIN
    mysql.expertises ON pgsql.expertises.id = mysql.expertises.id
        JOIN
    pgsql.expertises ON pgsql.expertises.uuid = mysql.expertises.uuid
        JOIN
    pgsql.expertise_definitions ON pgsql.expertise_definitions.key = mysql.expertise_tire_and_rims
WHERE
    pgsql.expertise_definitions.place = 'tire_and_rim'
