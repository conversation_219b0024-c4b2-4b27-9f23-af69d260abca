INSERT INTO pgsql.expertise_sub_control_and_engine_notes
SELECT
    pgsql.expertises.id as expertise_id,
    mysql.expertise_sub_control_and_engine_notes.note,
    mysql.expertise_sub_control_and_engine_notes.created_at,
    mysql.expertise_sub_control_and_engine_notes.updated_at,
    mysql.expertise_sub_control_and_engine_notes.deleted_at
FROM
    mysql.expertise_sub_control_and_engine_notes
        JOIN
    mysql.expertises ON mysql.expertises.id = mysql.expertise_sub_control_and_engine_notes.expertise_id
        JOIN
    pgsql.expertises ON pgsql.expertises.uuid = mysql.expertises.uuid
