INSERT INTO pgsql.complaint_other_infos
SELECT
    mysql.complaints.id as complaint_id,
    mysql.complaints.branch_id,
    mysql.complaints.il_id,
    mysql.complaints.ilce_id,
    mysql.complaints.vergi_dairesi,
    mysql.complaints.tc_vergi_no,
    mysql.complaints.eposta,
    mysql.complaints.telefon,
    mysql.complaints.expertise_uuid,
    mysql.complaints.baslik,
    mysql.complaints.dosya,
    mysql.complaints.semt,
    mysql.complaints.mahalle,
    mysql.complaints.talep_nedeni,
    mysql.complaints.tur,
    mysql.complaints.yetkili_yanit,
    mysql.complaints.attachments,
    mysql.complaints.others,
    mysql.complaints.created_at,
    mysql.complaints.updated_at,
    mysql.complaints.deleted_at
FROM
    mysql.complaints
