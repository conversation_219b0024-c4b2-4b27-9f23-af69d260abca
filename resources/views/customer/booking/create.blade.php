@extends('pages.build')
@section('title','<PERSON><PERSON>')
@push('css')
    <style>
        input[type="text"] , select{
            padding: 10px;
            margin-bottom: 20px;
            border: unset;
            border-bottom: 1px solid #000;
            width: 100%;
            background: transparent;
        }

    </style>
@endpush
@section('content')
    <form method="post" action="{{ route('bookings.store') }}">@csrf
        <input type="hidden" name="type" value="normal">
        <input type="text" placeholder="Araç Plakası" required name="plaka">
        <input type="text" placeholder="Şasi Numarası" required name="sase_no">
        <select name="city_id">
            <option value="0">Şehir Seçiniz</option>
            @foreach(\App\Models\City::all() as $city)
                <option value="{{ $city->id }}">{{ $city->title }}</option>
            @endforeach
        </select>
        <select name="branch_id" style="display: none">
            <option value="0"><PERSON><PERSON></option>
        </select>
        @php
            $currentDateTime = \Carbon\Carbon::now();
            $startDateTime = \Carbon\Carbon::make('08:30');
        @endphp

        <select name="date" id="date" required style="display: none">
            <option value="">Lütfen Seçiniz</option>
            @for($tarih = 0; $tarih < 8; $tarih++)
                <option value="{{ now()->addDays($tarih)->format('Y-m-d') }}">
                    {{ now()->addDays($tarih)->format('d.m.Y') }}
                </option>
            @endfor
        </select>

        <select name="hour" id="hour" required style="display: none">
            <option value="">Lütfen Seçiniz</option>
            @for($tarih = 0; $tarih < 480; $tarih += 30)
                @php
                    $optionStartTime = $startDateTime->copy()->addMinutes($tarih);
                    $optionEndTime = $startDateTime->copy()->addMinutes($tarih + 30);
                    $disabled = ($optionStartTime < $currentDateTime && $optionStartTime->isToday()) ? 'disabled' : '';
                @endphp
                <option value="{{ $optionStartTime->format('H:i') }}" {{ $disabled }}>
                    {{ $optionStartTime->format('H:i') }} - {{ $optionEndTime->format('H:i') }}
                </option>
            @endfor
        </select>

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const dateSelect = document.getElementById('date');
                const hourSelect = document.getElementById('hour');

                dateSelect.addEventListener('change', function() {
                    const selectedDate = new Date(this.value);
                    const today = new Date();

                    hourSelect.querySelectorAll('option').forEach(option => {
                        const optionTime = option.value;
                        const optionDateTime = new Date(`${selectedDate.toISOString().split('T')[0]}T${optionTime}`);

                        if (selectedDate.toDateString() === today.toDateString() && optionDateTime < today) {
                            option.disabled = true;
                        } else {
                            option.disabled = false;
                        }
                    });
                });

                // Trigger change event to set the initial state
                dateSelect.dispatchEvent(new Event('change'));
            });
        </script>

        <p class="text-warning">Randevu saat aralığının başlangıcından itibaren 15dk öncesinde bayimize gelmeniz gerekmektedir. Randevu saatinin başlangıcından itibaren 2 saat içerisinde ekspteriz hizmeti verilecektir.</p>
        <div class="d-flex align-items-center justify-content-center mt-3" style="flex-direction: column">
            <button class="btn btn-danger">Randevu Al</button>
        </div>
    </form>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <script>
        $('input[name="plaka"]').on('keyup',function (){
            $('input[name="plaka"]').val($('input[name="plaka"]').val().replaceAll(' ','').toUpperCase())
        })
        $('input[name="sase_no"]').on('keyup',function (){
            $('input[name="sase_no"]').val($('input[name="sase_no"]').val().replaceAll(' ','').toUpperCase())
        })

        $('select[name="city_id"]').on('change',function (){
            $('select[name="branch_id"]').find('option').remove()
            $('select[name="branch_id"]').css('display','none')
            $('select[name="date"]').css('display','none')
            $('select[name="hour"]').css('display','none')
            $('select[name="branch_id"]').append("<option value=\"0\">Bayi Seçiniz</option>")
            if ($('select[name="city_id"]').val() > 0){
                $.ajax({
                    url: "{{ route('api.getBranches') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$('select[name="city_id"]').val()} ,
                    success: function (response) {
                        $('select[name="branch_id"]').css('display','inline-block')

                        $.each(response.items,function (index,item){
                            $('select[name="branch_id"]').append("<option value="+item.id+">"+item.kisa_ad+"</option>")
                        })
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }
        })

        $('select[name="branch_id"]').on('change',function (){
            $('select[name="date"]').css('display','none')
            $('select[name="hour"]').css('display','none')
            if ($('select[name="branch_id"]').val() > 0){
                $('select[name="date"]').css('display','inline-block')
                $('select[name="hour"]').css('display','inline-block')
            }
        })
    </script>
@endpush
