@extends('pages.build')
@section('title','Randevularınız')
@push('css')
   <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('content')
    @if(count($items))
        <div class="row">
            <div class="col-xl-12">
                <div class="card custom-card">
                    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                        <a href="{{ route('customer.newBooking') }}" class="nav-link btn btn-danger m-3">Randevu Oluştur</a>
                    </nav>
                    <div class="card-body">

                        <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                            <thead>
                            <tr>
                                <th>Plaka</th>
                                <th>Şase No</th>
                                <th>Bayi</th>
                                <th>Tarih</th>
                                <th>İşlemler</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($items as $key => $item)
                                @php
                                    $appointmentDateTime = \Carbon\Carbon::make($item->date . ' ' . $item->hour);
                                    $currentDateTime = \Carbon\Carbon::now();
                                @endphp
                                <tr>
                                    <td>{{ $item->plaka }}</td>
                                    <td>{{ $item->sase_no }}</td>
                                    <td>{{ $item->getBranch->unvan }}</td>
                                    <td>{{ $appointmentDateTime->format('d.m.Y H:i:s') }}</td>
                                    <td>
                                        @if($item->status == 0)
                                            <button disabled class="btn btn-sm btn-danger">İptal Edildi</button>
                                        @else
                                            <form method="post" action="{{ route('customer.deleteBooking') }}">@csrf
                                                <button
                                                    onclick="if (!confirm('Randevuyu iptal etmek istediğinize emin misiniz?')){return false;}"
                                                    name="booking_id"
                                                    value="{{ encrypt($item->id) }}"
                                                    class="btn btn-danger btn-sm" {{ $appointmentDateTime < $currentDateTime ? 'disabled' : '' }}>
                                                    İptal Et
                                                </button>
                                            </form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="d-flex align-items-center justify-content-center" style="flex-direction: column">
            <i class="ri-close-circle-line" style="font-size: 4rem;color: #ff1000"></i>
            <span style="margin-top: 2rem">Hiç Kayıt Bulunamadı!</span>
        </div>
        <div class="d-flex align-items-center justify-content-center mt-3" style="flex-direction: column">
            <a href="{{ route('customer.newBooking') }}" class="btn btn-danger">Yeni Randevu</a>
        </div>
    @endif


@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
       <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

    <script>
        $(function (e) {
            'use strict';
            $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',responsive: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });
        });
    </script>
@endpush
