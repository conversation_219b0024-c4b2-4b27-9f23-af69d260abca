@extends('pages.build')
@section('title','Yeni Uzaktan Ekspertiz')
@push('css')
    <style>
        input[type="text"] , select{
            padding: 10px;
            margin-bottom: 20px;
            border: unset;
            border-bottom: 1px solid #000;
            width: 100%;
            background: transparent;
        }

    </style>
@endpush
@section('content')
    <div class="wrapper">
        <div class="container mt-5">
            <button type="button" class="btn btn-danger btn-sm">Uzaktan Ekspertiz Şuan Aktif <PERSON>ğil</button>
        </div>
    </div>
    <form class="d-none" method="post" action="{{ route('bookings.store') }}">@csrf
        <input type="hidden" name="type" value="remote">
        <select name="city_id">
            <option value="0">Şehir Seçiniz</option>
            @foreach(\App\Models\City::all() as $city)
                <option value="{{ $city->id }}">{{ $city->title }}</option>
            @endforeach
        </select>
        <select name="branch_id" style="display: none">
            <option value="0">Bayi Seçiniz</option>
        </select>
        <label>Ekspertiz Yapılacağı Tarih</label>
        <input type="text" value="{{ now()->format('d.m.Y') }}" name="date" disabled>
        <label>Ödeme Yöntemi</label>
        <input type="text" value="-2 Plus Card Bakiyesi" disabled>
        <div class="form-group">
            <input type="checkbox" name="form_onay" required>
            <label style="cursor:pointer;" data-bs-toggle="modal" data-bs-target="#exampleModal">Uzaktan Ekspertiz İş Emri Formundaki maddeleri ve bilgileri okudum, onaylıyorum. <span class="text-danger">Onay Gereklidir!</span></label>
        </div>
        <div class="form-group">
            <input type="checkbox" name="sms_kabul" required>
            <label>Ekspertiz hizmeti aşamaları için bilgilendirmelerin tarafıma sms olarak gönderilmesini kabul ediyorum. <span class="text-danger">Onay Gereklidir!</span></label>
        </div>
        <div class="form-group">
            <input type="checkbox" name="yol_testi">
            <label>Gerekli görülmesi halinde araca yol testi yapılmasına onay veriyorum.</label>
        </div>
        <div class="form-group">
            <input type="checkbox" name="bulten_abone">
            <label>Umran Plus Otomotiv A.Ş. ve program ortaklarının sms ve e-mail yoluyla kampanya, ürün ve hizmetleri hakkında bilgilendirme yapmasını kabul ediyorum.</label>
        </div>
        <div class="d-flex align-items-center justify-content-center mt-3" style="flex-direction: column">
            <button class="btn btn-danger">Satın Al</button>
        </div>
    </form>

    <div class="modal fade d-none" id="exampleModal" tabindex="-1"
         aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="exampleModalLabel1">Uzaktan Ekspertiz İş Emri Formu</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h1>UMRAN BAĞIMSIZ ARAÇ EKSPERTİZ MERKEZLERİ</h1>
                    <h2>Uzaktan Ekspertiz İş Emri Formu</h2>

                    <h3>Tarafların sorumlulukları ve bilgilendirme</h3>

                    <p>Umran Bağımsız Araç Ekspertiz Merkezleri, aracın görünen kusurları ve o anki durumu ile ilgili bilgilendirme yapar. Satıcı ve üçüncü şahıslar tarafından gizlenmiş kusurların tespit edilememesinden ve sonrasında ortaya çıkacak problemlerden dolayı Umran Bağımsız Araç Ekspertiz Merkezleri sorumlu tutulamaz.</p>

                    <p>Servislerimizde ekspertiz kontrollerinde aracın hiçbir yerinde sökme takma işlemi yapılmamaktadır. Boyasız göçük düzeltme tespitleri yapılamamaktadır. Durum ve arıza tespiti için özel test gerektiren parçaların kontrolü yapılmamaktadır (Motor, Motor Bloğu, Kompresyon, sızdırmazlık vb.).</p>

                    <p>Servislerimizde kilometre orijinalliği tespitleri yapılmamaktadır.</p>

                    <p>Servislerimizde Airbag (hava yastığı) kontrollerinde herhangi bir sökme takma işlemi yapılmamaktadır. Kontroller cihazla yapılmaktadır.</p>

                    <p>Yalnızca sökme takma neticesinde anlaşılabilecek arızaların tespit edilememesinden dolayı şirketimizin herhangi bir sorumluluğu bulunmamaktadır.</p>

                    <p>Aracın çalıntı olması ve ya yasal olmayan şase numara aktarımı işlemleri kontrol edilmemekte olup Umran Bağımsız Araç Ekspertiz Merkezleri'ni bağlayıcı hukuki sorumluluğu yoktur.</p>

                    <p>Plastik parçalar, Far, Stop Lambaları, Sis Lambalarındaki değişim ve Cam değişimleri, Silecek lastikleri kontrol edilmemektedir. Cam, ayna ve koltuk ısıtıcıları (rezinstansları) kontrol edilmemektedir. Koltuk hafızası ve konfor elektriği kontrol edilmemektedir, fitil sızdırmazlıkları, kapı kilitleri, araç kumandaları ve anahtar, opsiyonel özellikler (Araç Paketleri), araç orijinalliğine uymayan yazılım ve parçalar (Modifiye Araçlar), tekerlek uygunluğu ve orijinalliği kontrol edilememektedir.</p>

                    <p>Araç karoseri üzerindeki kaplamalar, şeffaf folyo, reklam, araç giydirme vb. bulunan araçlarda boya ile ilgili kontroller yapılamamaktadır.</p>

                    <p>Ekspertizimizle ilgili problemlerimizde, araca herhangi bir müdahale (Tamirat) yaptırmadan aracı görmemiz gerekir. Aksi takdirde dışarıda yaptırdığınız müdahaleler Umran Bağımsız Araç Ekspertiz Merkezleri sorumluluk alanını kapsamaz.</p>

                    <p>Kaporta Boya değerlendirmesinin itiraz süresi 72 saat, Mekanik ile ilgili raporumuzun itiraz süresi 24 saattir</p>

                    <p>Umran Bağımsız Araç Ekspertiz Merkezleri verilen ekspertiz raporunda yanlışlık yapılması halinde, Karayolları Motorlu Araçlar Zorunlu Mali Sorumluluk Sigortası Genel Şartlarının Ek-1’inde yer alan “Değer kaybı hesaplaması” hükümleri çerçevesinde müşteriye değer kaybı ödemesi yapmayı kabul ve taahhüt eder.</p>

                    <p>Müşteri İşbu iş emri ile almış olduğu ekspertiz raporunun Umran Bağımsız Araç Ekspertiz Merkezlerine ait Web sitesinde yayınlanmasını ve yine web sitesi üzerinden bir başkasına satışını kabul etmiş sayılır.</p>

                    <p>Gerekli görülmesi halinde araçlara yol testi yapılmaktadır. Cihaz ile kontrollerin yapılamaması durumunda yol testi yapılabilmektedir.</p>

                    <p>Servislerimizde araçların periyodik bakım tespiti yapılmamaktadır. Bakımların zamanında yapılmamasından kaynaklı, ekspertiz işlemi sırasında ve sonrasında ortaya çıkabilecek problemlerden dolayı Umran Bağımsız Araç Ekspertiz Merkezleri sorumlu tutulamaz.</p>

                    <p>Ekspertiz faaliyeti kapsamında sunduğunuz bazı kişisel verileriniz tarafımızca işlenmektedir. Kişisel verilerinizin işlenmesi hakkında detaylı bilgi almak için <a href="http://www.umranoto.com.tr/kvkk">www.umranoto.com.tr/kvkk</a> linki üzerinden internet sitemizi ziyaret edebilirsiniz.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                            data-bs-dismiss="modal">Kapat</button>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <script>

        $('select[name="city_id"]').on('change',function (){
            $('select[name="branch_id"]').find('option').remove()
            $('select[name="branch_id"]').css('display','none')
            $('select[name="branch_id"]').append("<option value=\"0\">Bayi Seçiniz</option>")
            if ($('select[name="city_id"]').val() > 0){
                $.ajax({
                    url: "{{ route('api.getBranches') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$('select[name="city_id"]').val()} ,
                    success: function (response) {
                        $('select[name="branch_id"]').css('display','inline-block')

                        $.each(response.items,function (index,item){
                            $('select[name="branch_id"]').append("<option value="+item.id+">"+item.unvan+"</option>")
                        })
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }
        })
    </script>
@endpush
