@extends('pages.build')
@section('title','Uzaktan Ekspertiz İşlemleri')
@push('css')
   <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('content')
    @if(count($items))
        <div class="row">
            <div class="col-xl-12">
                <div class="card custom-card">
                    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                        <a href="{{ route('customer.newRemoteExpertise') }}" class="nav-link">Uzaktan Ekspertiz Oluştur</a>
                    </nav>
                    <div class="card-body">

                        <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                            <thead>
                            <tr>
                                <th>Bayi</th>
                                <th>Oluşturulma Tarihi</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($items as $key => $item)
                                <tr>
                                    <td>{{ $item->getBranch->unvan }}</td>
                                    <td>{{ \Carbon\Carbon::make($item->created_at)->format('d.m.Y') }}</td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div class="d-flex align-items-center justify-content-center" style="flex-direction: column">
            <i class="ri-close-circle-line" style="font-size: 4rem;color: #ff1000"></i>
            <span style="margin-top: 2rem">Hiç Kayıt Bulunamadı!</span>
        </div>
        <div class="d-flex align-items-center justify-content-center mt-3" style="flex-direction: column">
            <a href="{{ route('customer.newRemoteExpertise') }}" class="btn btn-danger">Yeni Uzaktan Ekspertiz</a>
        </div>
    @endif


@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
       <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

    <script>
        $(function (e) {
            'use strict';
            $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',responsive: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });
        });
    </script>
@endpush
