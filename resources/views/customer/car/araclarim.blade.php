@extends('pages.build')
@section('title','Araç Sorgu')
@push('css')
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #fff;
        }

        .search-container {
            position: relative;
            background-color: #fff;
            padding: 20px;
            text-align: center;
        }

        .tabs {
            margin-bottom: 20px;
        }


        input[type="text"] {
            padding: 10px;
            margin-bottom: 20px;
            border: unset;
            border-bottom: 1px solid #000;
            width: 100%;
        }
        #triangle-bottomright {
            position: absolute;
            right: 0;
            bottom: 0;
            width: 0;
            height: 0;
            border-bottom: 300px solid #e30614;
            border-left: 500px solid transparent;
        }
    </style>
@endpush
@section('content')
    <div class="search-container">
        @if(count($car) >= 1)
            <div class="card" style="background-color: #22559d9e;">
                @foreach($car as $car_detail)
                    <a href="{{route('customer.query',['type'=>'plaka','search'=>$car_detail->plaka])}}" class="card-body " style="color: #fff;border-top: 1px solid #fff;">
                        <div class="car-header d-flex justify-content-between">
                            <div class="car-left d-flex">
                                <i style="font-size: 2.5rem" class="ri ri-car-fill"></i>
                                <div class="d-flex" style="flex-direction: column;align-items: baseline;margin-left: 3px">
                                    <span>{{ $car_detail->getMarka->name ?? 'Marka Bilgisi Bulunamadı' }}</span>
                                    <span>{{ $car_detail->getModel->name ?? 'Model Bilgisi Bulunamadı'}} - {{ $car_detail->getGear->name ?? 'Vites Bilgisi Bulunamadı' }}</span>
                                    <span>{{ $car_detail->plaka ?? 'Plaka Bilgisi Bulunamadı'}}<br></span>
                                </div>
                            </div>
                            <div class="car-right">
                                <span>Yıl</span>
                                <span>{{ $car_detail->model_yili ?? 'Model Yılı Bilgisi Bulunamadı' }}</span>
                            </div>
                        </div>
                        <div class="car-footer d-flex justify-content-between">
                            <div class="car-left d-flex">
                                <i style="font-size: 2.5rem" class="fe fe-play"></i>
                                <div class="d-flex" style="flex-direction: column;align-items: baseline;margin-left: 3px">
                                    <span>{{ $car_detail->getFuel->name ?? 'Yakıt Bilgisi Bulunamadı'  }}</span>
                                    <span>{{ $car_detail->getCaseType->name ?? 'Kasa Bilgisi Bulunamadı' }}</span>
                                </div>
                            </div>
                        </div>
                    </a>
                @endforeach
            </div>
        @else
            <p class="text-danger text-center"><b>Sonuç Bulunamadı!</b></p>
        @endif
    </div>

@endsection
