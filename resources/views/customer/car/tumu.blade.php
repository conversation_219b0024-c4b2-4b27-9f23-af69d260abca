@extends('pages.build')
@section('title','<PERSON><PERSON><PERSON>')
@push('css')
    <style>
        .inpage-footer{
            width: 100%;
            position: fixed;
            bottom: 0;
            left: 0;
            background-color: #fff;
            padding: 8px 10px;
            display: flex;
            justify-content: space-around;
            margin-top: 4rem;
        }
        .inpage-footer span{
            font-size: .7rem;
            text-align: center;
        }
        .footer-item{
            flex-direction: column;
        }
        .footer-item.active{
            color: #ff1000;
        }
    </style>
@endpush
@section('content')
    @if(count($expertises) > 0)
        <div class="table-responsive mt-5">
            <table class="table">
                <thead>
                    <th>Plaka</th>
                    <th>Şase No</th>
                    <th>Paket</th>
                    <th>Bayi</th>
                    <th>Tarih</th>
                    <th></th>
                </thead>
                <tbody>
                @foreach($expertises as $e)
                    <tr>
                        <td>
                            {{$e['plaka'] ?? ''}}
                        </td>
                        <td>
                            {{$e['sase_no'] ?? ''}}
                        </td>
                        <td>
                            {{$e['stock_name'] ?? ''}}
                        </td>
                        <td>
                            {{$e['branch_name'] ?? ''}}
                        </td>
                        <td>
                            {{$e['created_at']}}
                        </td>
                        <td>
                            <a href="{{route('customer.ekspertizRaporu',[$e['uuid']])}}" class="btn btn-sm btn-warning">
                                <i class="fa fa-eye"></i>
                            </a>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
    @else
        <div class="d-flex align-items-center justify-content-center" style="flex-direction: column">
            <i class="ri-close-circle-line" style="font-size: 4rem;color: #ff1000"></i>
            <span style="margin-top: 2rem">Hiç Kayıt Bulunamadı!</span>
        </div>
    @endif

    <div class="inpage-footer">
        <a href="{{ route('customer.cars',['type'=>'alici']) }}" class="d-flex align-items-center justify-content-center footer-item @if(!isset($_GET['type']) || $_GET['type'] == 'alici') active @endif">
            <i class="fas fa-arrow-down"></i>
            <span>Alıcısı<br>Olduğum Araçlar</span>
        </a>
        <a href="{{ route('customer.cars',['type'=>'satici']) }}" class="d-flex align-items-center justify-content-center footer-item @if(isset($_GET['type']) && $_GET['type'] == 'satici') active @endif">
            <i class="fas fa-arrow-up"></i>
            <span>Satıcısı<br>Olduğum Araçlar</span>
        </a>
        <a href="{{ route('customer.cars',['type'=>'tumu']) }}" class="d-flex align-items-center justify-content-center footer-item @if(isset($_GET['type']) && $_GET['type'] == 'tumu') active @endif">
            <i class="ri-file-chart-line"></i>
            <span>Satın<br>Alınan Raporlar</span>
        </a>
    </div>
@endsection
