@extends('pages.build')
@section('title','Kodlar')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Filtrele</h5>
                </div>
                <div class="card-body">
                    <form action="{{ url()->current() }}">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Durum</label>
                                    <select class="form-control-sm form-control form-select" name="status">
                                        <option>Tümü</option>
                                        <option @if(isset($_GET['status']) && $_GET['status'] == 'used') selected @endif value="used">Kullanılmış</option>
                                        <option @if(isset($_GET['status']) && $_GET['status'] == 'not_used') selected @endif value="not_used">Boşta</option>
                                        <option @if(isset($_GET['status']) && $_GET['status'] == 'deleted') selected @endif value="deleted">Silinen</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-1">
                                <label></label>
                                <button class="btn btn-sm btn-danger mt-4">Filtrele</button>
                            </div>
                            @if(isset($_GET['status']) && $_GET['status'] == 'not_used')
                                <div class="col-md-2">
                                    <label></label>
                                    <button form="deleteContractCodeForm" class="btn btn-sm btn-danger mt-4">Seçilenleri Sil</button>
                                </div>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
    <form id="deleteContractCodeForm" action="{{ route('customer.deleteContractCode') }}" method="post">@csrf        </form>
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-body">
                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr>
                            @if(isset($_GET['status']) && $_GET['status'] == 'not_used')
                                <th onclick="$('input[name=\'selected_code[]\']').prop('checked', true);">Tümünü Seç</th>
                            @endif
                            <th>Sözleşme No</th>
                            <th>Kod</th>
                            <th>Durum</th>
                            <th>Plaka</th>
                            <th>Telefon</th>
                            <th>@if(isset($_GET['status']) && $_GET['status'] == 'used') Kullanılan @endif Bayi</th>
                            <th>Ödeme Tipi</th>
                            <th>@if(isset($_GET['status']) && $_GET['status'] == 'used') Kullanılan @endif Stok</th>

                            @if($status == 'not_used')
                                <th>Sil</th>
                            @endif
                            <th>Üretim Tarihi</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($items as $key => $item)
                            <tr>
                                @if(isset($_GET['status']) && $_GET['status'] == 'not_used')
                                    <td><input type="checkbox" name="selected_code[]" form="deleteContractCodeForm" value="{{ $item->id }}"></td>
                                @endif
                                <td>{{ $item->getContract?->no }}</td>
                                <td>{{ $item->code }}</td>
                                <td>{{ $item->used == 1 ? 'Kullanıldı' : 'Kullanılmadı' }}</td>
                                <td>{{ $item->plaka }}</td>
                                <td>{{ $item->telephone }}</td>
                                <td>
                                    @if($item->used == 1)
                                        {{ $branches[$item['expertise']['branch_id']]['kisa_ad'] }}
                                    @else
                                        {{ $item->getBranches->count() }} Bayi <span class="info-hover" title="@foreach($item->getBranches as $codeBranch) {{ $codeBranch->getBranch?->kisa_ad }}, @endforeach"><i class="fa fa-info"></i></span>
                                    @endif
                                </td>

                                <td>
                                    @if($item->type == 'tumu')
                                        Sözleşme Sahibi, Alıcı
                                    @elseif($item->type == 'alici')
                                        Alıcı
                                    @elseif($item->type == 'satici')
                                        Satıcı
                                    @else
                                        Sözleşme Sahibi
                                    @endif
                                </td>
                                <td>
                                    @if($item->used == 1)
                                        {{ $item['expertise']?->getStockHasOne?->getStock?->ad }}
                                    @else
                                        @foreach($item['stocks'] as $codeStock)
                                            {{ $codeStock['ad'] }}
                                        @endforeach
                                    @endif
                                </td>
                                @if($status == 'not_used')
                                    <th><button form="deleteContractCodeForm" name="selected_code[]" value="{{ $item->id }}" class="btn btn-sm btn-danger">Sil</button></th>
                                @endif
                                <td>{{ $item->created_at->translatedFormat('d F Y H:i') }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

    <script>
        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 50,
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı!",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı!",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });
        });
    </script>
@endpush
