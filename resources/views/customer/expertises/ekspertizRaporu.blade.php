@extends('pages.build')
@section('title','Ekspertiz Raporu')
@push('css')
    <style>
        table{
            border: 1px solid #000 !important;
        }
        th{
            background-color: #cecece57 !important
        }
        .page-break{
            page-break-after: always;
        }
        .table-bordered>:not(caption)>*>*{
            border-width: 1px;
        }
        .table td, .table th{
            padding: .35rem;
        }
        .table-col-6 td{
            width: 16.667% !important;
            border: 1px solid;
        }

        div#divRaporEkrani {
            padding: 10px;
            margin-top: 20px;
        }
    </style>
    <link href="http://127.0.0.1:8000/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet">
@endpush
@section('content')
    <div class="container">
        @include('pages.expertise.rapor.content',['expertise'=>$expertise])
        @if(!is_null($logs))
            <div class="row is-emri is-emri-sayfa rapor">
                <table class="table table-bordered" style="height: 950px">
                    <tbody>
                    <tr>
                        <td style="width: 33%;text-align: center">
                            <img src="/assets/isemri_logo.png" style="width: 100%">
                            <h5 style="color: darkorange">444 5 417</h5>
                        </td>
                        <td style="width: 33%;font-size: .813rem;" class="text-center">
                            <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                        </td>
                        <td colspan="2" style="width: 33%">
                            <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                            <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Plaka</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif"><b>
                                {{ $expertise['plaka'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif"><b>
                                {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                            </b>
                        </td>
                        <td>Şase No</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif"><b>
                                {{ $expertise['sase_no'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif"><b>
                                {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                            </b>
                        </td>
                    </tr>
                    <tr>
                        <td>Marka Model</td>
                        <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                        <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                        <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Model Yılı</td>
                        <td>{{ $expertise['model_yili'] }}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Alıcı Bilgileri</b></td>
                        <td colspan="2"><b>Satıcı Bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Adı Soyadı</td>
                        <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                        <td>Adı Soyadı</td>
                        <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                    </tr>
                    <tr>
                        <td>Firma Unvanı</td>
                        <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                        <td>Firma Unvanı</td>
                        <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                    </tr>
                    <tr>
                        <td>Adres</td>
                        <td>{{ $expertise['fullAddressAlici'] }}</td>
                        <td>Adres</td>
                        <td>{{ $expertise['fullAddressSatici'] }}</td>
                    </tr>
                    <tr>
                        <td>Telefon</td>
                        <td>{{ $expertise['getAliciTel'] }}</td>
                        <td>Telefon</td>
                        <td>{{ $expertise['getSaticiTel'] }}</td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                        <td colspan="2">
                            @foreach($expertise['getStocks'] as $expertiseStock)
                                {{ $expertiseStock['ad'] }},
                            @endforeach</td>
                    </tr>

                    <tr>
                        <td colspan="4" id="hasarSonucu">
                            @if($logs)
                                @php
                                    $hasar = json_decode($logs->hasar);
                                @endphp
                                @if($hasar != null)
                                    <a href="{{ $hasar->data->imageurl ?? '' }}" target="_blank"><p class="text-center">{{ $hasar->data->imageurl ?? '' }}</p></a>
                                    <p class="text-center">{{ $hasar->data->result ?? '' }}</p>
                                    <img src="{{ $hasar->data->imageurl ?? '' }}" height="400px">
                                @endif
                            @endif
                        </td>
                    </tr>
                    <tr><td colspan="4"><p>{{ $settings->hasar_sorgu_mesaji }}</p></td></tr>
                    <tr class="text-center">
                        <td class="imza" style="width: 33%">
                            Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                            <b>{{!empty($expertise['getBranch']) ? $expertise['getBranch'] : ''}}</b>

                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                        </td>
                        <td class="filtreli imza @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                        </td>
                        <td class="imza filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="page-break"></div>
            <div class="row is-emri is-emri-sayfa rapor">
                <table class="table table-bordered" style="height: 950px">
                    <tbody>
                    <tr>
                        <td style="width: 33%;text-align: center">
                            <img src="/assets/isemri_logo.png" style="width: 100%">
                            <h5 style="color: darkorange">444 5 417</h5>
                        </td>
                        <td style="width: 33%;font-size: .813rem;" class="text-center">
                            <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                        </td>
                        <td colspan="2" style="width: 33%">
                            <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                            <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Plaka</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                                {{ $expertise['plaka'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                                {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                            </b>
                        </td>
                        <td>Şase No</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                                {{ $expertise['sase_no'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                                {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                            </b>
                        </td>
                    </tr>
                    <tr>
                        <td>Marka Model</td>
                        <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                        <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                        <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Model Yılı</td>
                        <td>{{ $expertise['model_yili'] }}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Alıcı Bilgileri</b></td>
                        <td colspan="2"><b>Satıcı Bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Adı Soyadı</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">@if($expertise['getAliciType'] == 'bireysel') {{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }} @endif</td>
                        <td>Adı Soyadı</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">@if($expertise['getSaticiType'] == 'bireysel') {{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }} @endif</td>
                    </tr>
                    <tr>
                        <td>Firma Unvanı</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">@if($expertise['getAliciType'] == 'kurumsal') {{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }} @endif</td>
                        <td>Firma Unvanı</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">@if($expertise['getSaticiType'] == 'kurumsal') {{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }} @endif</td>
                    </tr>
                    <tr>
                        <td>Adres</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['fullAddressAlici'] }}</td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['fullAddressAlici']) ? substr($expertise['fullAddressAlici'], 0, 2) . str_repeat('*', (strlen($expertise['fullAddressAlici']) - 3)) . substr($expertise['fullAddressAlici'], -1) : '' }}</td>
                        <td>Adres</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['fullAddressSatici'] }}</td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['fullAddressSatici']) ? substr($expertise['fullAddressSatici'], 0, 2) . str_repeat('*', (strlen($expertise['fullAddressSatici']) - 3)) . substr($expertise['fullAddressSatici'], -1) : '' }}</td>
                    </tr>
                    <tr>
                        <td>Telefon</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['getAliciTel'] }}</td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['getAliciTel']) && strlen($expertise['getAliciTel']) > 3 ? substr($expertise['getAliciTel'], 0, 2) . str_repeat('*', (strlen($expertise['getAliciTel']) - 3)) . substr($expertise['getAliciTel'], -1) : '' }}</td>
                        <td>Telefon</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['getSaticiTel'] }}</td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['getSaticiTel']) ? substr($expertise['getSaticiTel'], 0, 2) . str_repeat('*', (strlen($expertise['getSaticiTel']) - 3)) . substr($expertise['getSaticiTel'], -1) : '' }}</td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                        <td colspan="2">
                            @foreach($expertise['getStocks'] as $expertiseStock)
                                {{ $expertiseStock['ad'] }},
                            @endforeach</td>
                    </tr>
                    <tr>
                        <td colspan="4" style="padding: 2rem 0 9rem 0;">
                            @if($logs)
                                @php
                                    $kilometre = json_decode($logs->kilometre);
                                @endphp
                                @if($kilometre != null)
                                    <a href="{{ $kilometre->data->imageurl ?? '' }}" target="_blank"><p class="text-center">{{ $kilometre->data->imageurl }}</p></a>
                                    <img src="{{ $kilometre->data->imageurl ?? '' }}" width="100%">
                                @endif
                            @endif
                        </td>
                    </tr>
                    <tr><td colspan="4"><p>{{ $settings->kilometre_sorgu_mesaji }}</p></td></tr>
                    <tr class="text-center">
                        <td class="imza" style="width: 33%">
                            Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                            <b>{{!empty($expertise['getBranch']) ? $expertise['getBranch'] : ''}}</b>
                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                        </td>
                        <td class="filtreli imza @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                        </td>
                        <td class="imza filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="page-break"></div>
            <div class="row is-emri is-emri-sayfa rapor">
                <table class="table table-bordered" style="height: 950px">
                    <tbody>
                    <tr>
                        <td style="width: 33%;text-align: center">
                            <img src="/assets/isemri_logo.png" style="width: 100%">
                            <h5 style="color: darkorange">444 5 417</h5>
                        </td>
                        <td style="width: 33%;font-size: .813rem;" class="text-center">
                            <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                        </td>
                        <td colspan="2" style="width: 33%">
                            <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                            <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Plaka</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                                {{ $expertise['plaka'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                                {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                            </b>
                        </td>
                        <td>Şase No</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                                {{ $expertise['sase_no'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                                {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                            </b>
                        </td>
                    </tr>
                    <tr>
                        <td>Marka Model</td>
                        <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                        <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                        <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Model Yılı</td>
                        <td>{{ $expertise['model_yili'] }}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Alıcı Bilgileri</b></td>
                        <td colspan="2"><b>Satıcı Bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Adı Soyadı</td>
                        <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                        <td>Adı Soyadı</td>
                        <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                    </tr>
                    <tr>
                        <td>Firma Unvanı</td>
                        <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                        <td>Firma Unvanı</td>
                        <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                    </tr>
                    <tr>
                        <td>Adres</td>
                        <td>{{ $expertise['fullAddressAlici'] }}</td>
                        <td>Adres</td>
                        <td>{{ $expertise['fullAddressSatici'] }}</td>
                    </tr>
                    <tr>
                        <td>Telefon</td>
                        <td>{{ $expertise['getAliciTel'] }}</td>
                        <td>Telefon</td>
                        <td>{{ $expertise['getSaticiTel'] }}</td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                        <td colspan="2">
                            @foreach($expertise['getStocks'] as $expertiseStock)
                                {{ $expertiseStock['ad'] }},
                            @endforeach</td>
                    </tr>
                    <tr>
                        <td colspan="4" @if($logs && json_decode($logs->borc) != null) style="padding: 2rem 0 14rem 0;" @else style="padding: 18rem 0 18rem 0;" @endif>
                            @if($logs)
                                @php
                                    $borc = json_decode($logs->borc);
                                @endphp
                                @if($borc != null)
                                    <table class="table">
                                        <tbody>
                                        <tr>
                                            <th>Tip</th>
                                            <th>Vade</th>
                                            <th>Borç</th>
                                            <th>G. Faizi</th>
                                            <th>Toplam</th>
                                        </tr>
                                        @foreach($borc->data->vehicletax->data as $brc)
                                            <tr>
                                                <td>{{ $brc->expirydate }}</td>
                                                <td>{{ $brc->installment }}</td>
                                                <td>{{ $brc->loanamount }}₺</td>
                                                <td>{{ $brc->delayamount }}₺</td>
                                                <td>{{ $brc->totalamount }}₺</td>
                                            </tr>
                                        @endforeach
                                        <tr>
                                            <td>Avrupa Otoyolu Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->avrupa ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->avrupa ?? '' }}₺</td>
                                        </tr>
                                        <tr>
                                            <td>HGS / OGS Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->hgsogs ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->hgsogs ?? '' }}₺</td>
                                        </tr>
                                        <tr>
                                            <td>Anadolu Otoyolu Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->anadolu ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->anadolu ?? '' }}₺</td>
                                        </tr>
                                        <tr>
                                            <td>Avrasya Tüneli Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->avrasya ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->avrasya ?? '' }}₺</td>
                                        </tr>
                                        <tr>
                                            <td>Kuzey Çevre Otoyolu + Yavuz Sultan Selim Köprü Geçiş Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->yssKuzeyCevre ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->yssKuzeyCevre ?? '' }}₺</td>
                                        </tr>
                                        <tr>
                                            <td>Gebze-İzmir Otoyolu + Osmangazi Köprü Geçiş Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->gebzeOrhangaziIzmir ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->gebzeOrhangaziIzmir ?? '' }}₺</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                @endif
                            @endif
                        </td>
                    </tr>
                    <tr><td colspan="4"><p>{{ $settings->borc_sorgu_mesaji }}</p></td></tr>
                    <tr class="text-center">
                        <td class="imza" style="width: 33%">
                            Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                            <b>{{!empty($expertise['getBranch']) ? $expertise['getBranch'] : ''}}</b>

                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                        </td>
                        <td class="filtreli imza @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                        </td>
                        <td class="imza filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="page-break"></div>
            <div class="row is-emri is-emri-sayfa rapor">
                <table class="table table-bordered" style="height: 950px">
                    <tbody>
                    <tr>
                        <td style="width: 33%;text-align: center">
                            <img src="/assets/isemri_logo.png" style="width: 100%">
                            <h5 style="color: darkorange">444 5 417</h5>
                        </td>
                        <td style="width: 33%;font-size: .813rem;" class="text-center">
                            <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                        </td>
                        <td colspan="2" style="width: 33%">
                            <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                            <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Plaka</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                                {{ $expertise['plaka'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                                {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                            </b>
                        </td>
                        <td>Şase No</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                                {{ $expertise['sase_no'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                                {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                            </b>
                        </td>
                    </tr>
                    <tr>
                        <td>Marka Model</td>
                        <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                        <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                        <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Model Yılı</td>
                        <td>{{ $expertise['model_yili'] }}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Alıcı Bilgileri</b></td>
                        <td colspan="2"><b>Satıcı Bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Adı Soyadı</td>
                        <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                        <td>Adı Soyadı</td>
                        <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                    </tr>
                    <tr>
                        <td>Firma Unvanı</td>
                        <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                        <td>Firma Unvanı</td>
                        <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                    </tr>
                    <tr>
                        <td>Adres</td>
                        <td>{{ $expertise['fullAddressAlici'] }}</td>
                        <td>Adres</td>
                        <td>{{ $expertise['fullAddressSatici'] }}</td>
                    </tr>
                    <tr>
                        <td>Telefon</td>
                        <td>{{ $expertise['getAliciTel'] }}</td>
                        <td>Telefon</td>
                        <td>{{ $expertise['getSaticiTel'] }}</td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                        <td colspan="2">
                            @foreach($expertise['getStocks'] as $expertiseStock)
                                {{ $expertiseStock['ad'] }},
                            @endforeach</td>
                    </tr>
                    <tr>
                        <td colspan="4" style="padding: 2rem 0 6rem 0;">
                            @if($logs)
                                @php
                                    $ruhsat = json_decode($logs->ruhsat);
                                @endphp
                                <table class="table">
                                    <tr><th>Seri Numarası</th><td>{{ $ruhsat->data->serialnumber ?? '' }}</td></tr>
                                    <tr><th>Plaka</th><td>{{ $ruhsat->data->plate ?? '' }}</td></tr>
                                    <tr><th>Şasi</th><td>{{ $ruhsat->data->chassis ?? '' }}</td></tr>
                                    <tr><th>Motor Numarası</th><td>{{ $ruhsat->data->enginenumber ?? '' }}</td></tr>
                                    <tr><th>Model Yılı</th><td>{{ $ruhsat->data->modelyear ?? '' }}</td></tr>
                                    <tr><th>Marka</th><td>{{ $ruhsat->data->brand ?? '' }}</td></tr>
                                    <tr><th>Model</th><td>{{ $ruhsat->data->modelname ?? '' }}</td></tr>
                                    <tr><th>Yakıt Tipi</th><td>{{ $ruhsat->data->fueltype ?? '' }}</td></tr>
                                    <tr><th>Kayıt Tarihi</th><td>{{ $ruhsat->data->registerdate ?? '' }}</td></tr>
                                    <tr><th>Kayıt Başlangıç Tarihi</th><td>{{ $ruhsat->data->registerdateFrom ?? '' }}</td></tr>
                                    <tr><th>Yolcu Sayısı</th><td>{{ $ruhsat->data->passengercount ?? '' }}</td></tr>
                                    <tr><th>Kimlik Numarası</th><td>{{ $ruhsat->data->idnumber ?? '' }}</td></tr>
                                    <tr><th>Adı Soyadı </th><td>{{ $ruhsat->data->surname ?? '' }}</td></tr>
                                    <tr><th>Renk </th><td>{{ $ruhsat->data->color ?? '' }}</td></tr>
                                    <tr><th>Motor Kapasitesi </th><td>{{ $ruhsat->data->enginecapacity ?? '' }}</td></tr>
                                    <tr><th>Motor Gücü </th><td>{{ $ruhsat->data->enginepower ?? '' }}</td></tr>
                                    <tr><th>Sakınca Durumu </th><td>{{ $ruhsat->data->sakincadurumu ?? '' }}</td></tr>
                                    <tr><th>Kasko Değeri </th><td>{{ $ruhsat->data->kaskodegeri ?? '' }}</td></tr>
                                </table>
                            @endif
                        </td>
                    </tr>
                    <tr><td colspan="4"><p>{{ $settings->arac_detay_sorgu_mesaji }}</p></td></tr>
                    <tr class="text-center">
                        <td class="imza" style="width: 33%">
                            Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                            <b>{{!empty($expertise['getBranch']) ? $expertise['getBranch'] : ''}}</b>

                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                        </td>
                        <td class="filtreli imza @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                        </td>
                        <td class="imza filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="page-break"></div>
            <div class="row is-emri is-emri-sayfa rapor">
                <table class="table table-bordered" style="height: 950px">
                    <tbody>
                    <tr>
                        <td style="width: 33%;text-align: center">
                            <img src="/assets/isemri_logo.png" style="width: 100%">
                            <h5 style="color: darkorange">444 5 417</h5>
                        </td>
                        <td style="width: 33%;font-size: .813rem;" class="text-center">
                            <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                        </td>
                        <td colspan="2" style="width: 33%">
                            <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                            <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Plaka</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                                {{ $expertise['plaka'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                                {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                            </b>
                        </td>
                        <td>Şase No</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                                {{ $expertise['sase_no'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                                {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                            </b>
                        </td>
                    </tr>
                    <tr>
                        <td>Marka Model</td>
                        <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                        <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                        <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Model Yılı</td>
                        <td>{{ $expertise['model_yili'] }}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Alıcı Bilgileri</b></td>
                        <td colspan="2"><b>Satıcı Bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Adı Soyadı</td>
                        <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                        <td>Adı Soyadı</td>
                        <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                    </tr>
                    <tr>
                        <td>Firma Unvanı</td>
                        <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                        <td>Firma Unvanı</td>
                        <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                    </tr>
                    <tr>
                        <td>Adres</td>
                        <td>{{ $expertise['fullAddressAlici'] }}</td>
                        <td>Adres</td>
                        <td>{{ $expertise['fullAddressSatici'] }}</td>
                    </tr>
                    <tr>
                        <td>Telefon</td>
                        <td>{{ $expertise['getAliciTel'] }}</td>
                        <td>Telefon</td>
                        <td>{{ $expertise['getSaticiTel'] }}</td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                        <td colspan="2">
                            @foreach($expertise['getStocks'] as $expertiseStock)
                                {{ $expertiseStock['ad'] }},
                            @endforeach</td>
                    </tr>
                    <tr>
                        <td colspan="4" @if($logs && json_decode($logs->degisen) != null) style="padding: 2rem 0 14rem 0;" @else style="padding: 9rem 0 9rem 0;" @endif>
                            @if($logs)
                                @php
                                    $degisenler = json_decode($logs->degisen);
                                @endphp
                                @if(is_array($degisenler))
                                    @foreach($degisenler as $detail)
                                        <img src="{{ $detail?->data?->imageurl }}" width="100%">
                                    @endforeach
                                @else
                                    <img src="{{ $degisenler?->data?->imageurl }}" width="100%">
                                @endif
                            @endif
                        </td>
                    </tr>
                    <tr><td colspan="4"><p>{{ $settings->degisen_sorgu_mesaji }}</p></td></tr>
                    <tr class="text-center">
                        <td class="imza" style="width: 33%">
                            Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                            <b>{{!empty($expertise['getBranch']) ? $expertise['getBranch'] : ''}}</b>

                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                        </td>
                        <td class="filtreli imza @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                        </td>
                        <td class="imza filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="page-break"></div>
            <div class="row is-emri is-emri-sayfa rapor">
                <table class="table table-bordered" style="height: 950px">
                    <tbody>
                    <tr>
                        <td style="width: 33%;text-align: center">
                            <img src="/assets/isemri_logo.png" style="width: 100%">
                            <h5 style="color: darkorange">444 5 417</h5>
                        </td>
                        <td style="width: 33%;font-size: .813rem;" class="text-center">
                            <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                        </td>
                        <td colspan="2" style="width: 33%">
                            <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                            <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Plaka</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                                {{ $expertise['plaka'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                                {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                            </b>
                        </td>
                        <td>Şase No</td>
                        <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                                {{ $expertise['sase_no'] }}
                            </b>
                        </td>
                        <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                                {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                            </b>
                        </td>
                    </tr>
                    <tr>
                        <td>Marka Model</td>
                        <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                        <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                        <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Model Yılı</td>
                        <td>{{ $expertise['model_yili'] }}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Alıcı Bilgileri</b></td>
                        <td colspan="2"><b>Satıcı Bilgileri</b></td>
                    </tr>
                    <tr>
                        <td>Adı Soyadı</td>
                        <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                        <td>Adı Soyadı</td>
                        <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                    </tr>
                    <tr>
                        <td>Firma Unvanı</td>
                        <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                        <td>Firma Unvanı</td>
                        <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                    </tr>
                    <tr>
                        <td>Adres</td>
                        <td>{{ $expertise['fullAddressAlici'] }}</td>
                        <td>Adres</td>
                        <td>{{ $expertise['fullAddressSatici'] }}</td>
                    </tr>
                    <tr>
                        <td>Telefon</td>
                        <td>{{ $expertise['getAliciTel'] }}</td>
                        <td>Telefon</td>
                        <td>{{ $expertise['getSaticiTel'] }}</td>
                    </tr>
                    <tr>
                        <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                        <td colspan="2">
                            @foreach($expertise['getStocks'] as $expertiseStock)
                                {{ $expertiseStock['ad'] }},
                            @endforeach</td>
                    </tr>
                    <tr>
                        <td colspan="4"  @if($logs && json_decode($logs->degisen) != null) style="padding: 2rem 0 14rem 0;" @else style="padding: 10rem 0 10rem 0;" @endif>
                            @if($logs)
                                @php
                                    $degisenler = json_decode($logs->degisen);
                                @endphp
                                @if(is_array($degisenler))
                                    @foreach($degisenler as $detail)
                                        <img src="{{ $detail?->data?->imageurl }}" width="100%">
                                    @endforeach
                                @else
                                    <img src="{{ $degisenler?->data?->imageurl }}" width="100%">
                                @endif
                            @endif
                        </td>
                    </tr>
                    <tr><td colspan="4"><p>{{ $settings->ruhsat_sorgu_mesaji }}</p></td></tr>
                    <tr class="text-center">
                        <td class="imza" style="width: 33%">
                            Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                            <b>{{!empty($expertise['getBranch']) ? $expertise['getBranch'] : ''}}</b>

                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                        </td>
                        <td class="filtreli imza @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                        </td>
                        <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                        </td>
                        <td class="imza filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                            MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        @endif
    </div>
    <div id="divRaporEkrani" class="d-none">
        <div class="table-responsive">
            <table class="table">
                <tbody>
                <tr>
                    <td colspan="2" style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 100%">
                        <h5 style="color: darkorange">444 5 417</h5>
                    </td>
                    <td colspan="3" style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU</b><br>
                        Bağımsız Oto Ekspertiz Merkezi<br> www.umranoto.com.tr<br><br>
                        <b>Umranak Oto Ekspertiz Hizm. Tic. Ltd. Şti. Göztepe</b>
                    </td>
                    <td colspan="1" class="text-center" style="width: 25%">
                        <b>TSE-HYB Belge No:</b><h5 class="text-danger">34-HYB-18692</h5>
                    </td>
                    <td colspan=""  style="width: 25%;text-align:center;">
                        {!! $expertise['qr'] !!}
                    </td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Bayi:</td>
                    <td colspan="2"><b>{{ $expertise['getBranch'] }}</b></td>
                    <td colspan="1">Marka/Model:</td>
                    <td colspan="1">{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td colspan="1">KM:</td>
                    <td colspan="1"><b>{{ number_format($expertise['km'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Belge No:</td>
                    <td colspan="2"><b class="text-danger">{{ $expertise['belge_no'] }}</b></td>
                    <td colspan="1">Model Yılı:</td>
                    <td colspan="1"><b>{{ $expertise['model_yili'] }}</b></td>
                    <td colspan="1">Motor Hacmi:</td>
                    <td colspan="1"><b>{{ number_format($expertise['motor_hacmi'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Giriş Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['created_at'] }}</b></td>
                    <td colspan="1">Şase Numarası:</td>
                    <td colspan="1"><b>
                            {{ $expertise['kvkk_durum'] == "true" && !empty($expertise['sase_no']) ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td colspan="1">Yakıt Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getFuel'] }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Çıkış Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['cikis_tarihi'] }}</b></td>
                    <td colspan="1">Plaka:</td>
                    <td colspan="1"><b>
                            {{ $expertise['kvkk_durum'] == "true" && !empty($expertise['plaka']) ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : $expertise['plaka'] }}
                        </b></td>
                    <td colspan="1">Vites Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getGear'] }}</b></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="row">
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>PARÇA ADI</th>
                        <th>ORJ.</th>
                        <th>BOY.</th>
                        <th>DEĞ.</th>
                        <th>DÜZ.</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($bodyworks1 as $key => $bodywork)
                        <tr>
                            <td><b>{{ $bodywork }}</b></td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>PARÇA ADI</th>
                        <th>ORJ.</th>
                        <th>BOY.</th>
                        <th>DEĞ.</th>
                        <th>DÜZ.</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($bodyworks2 as $key => $bodywork)
                        <tr>
                            <td><b>{{ $bodywork }}</b></td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
            <div class="col-md-6 table-responsive">
                <img src="{{ $expertise['bodywork_image'] }}" class="img-fluid">
            </div>
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>PARÇA ADI</th>
                        <th>ORJ.</th>
                        <th>BOY.</th>
                        <th>DEĞ.</th>
                        <th>DÜZ.</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($bodyworks3 as $key => $bodywork)
                        <tr>
                            <td><b>{{ $bodywork }}</b></td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                        </tr>
                    @endforeach
                    </tbody>
                    <tfoot>
                    <tr>
                        <td colspan="6">
                            Tamponlar plastik aksam olduğundan boya ve değişim değerlendirilmesi yapılmamıştır. ORJ: Orijinal Parça DÜZ: Düzeltme ve/veya macun işlemi uygulanmış parça ST : Sökülüp takılmış parça DEĞ: Değişmiş Parça
                            (ST:Kendi üzerindeki ya da aynı model araçtan alınan birebir parça)
                        </td>
                    </tr>
                    </tfoot>
                </table>
            </div>
            <div class="col-md-12 table-responsive">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th colspan="4" class="text-center">NOTLAR</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php
                        $td_count = 10;
                        if($td_count < count($expertise['allNotes'][1])){
                            $td_count = count($expertise['allNotes'][1]);
                        }

                        foreach($expertise['allNotes'][1] as $key => $notes_array){
                            $expertise['allNotes'][1][$key] = '[KPR] '.$notes_array;
                        }

                        // Toplam eleman sayısını bul
                        $totalCount = count($expertise['allNotes'][1]);




                        $firstArraykpr = array_slice($expertise['allNotes'][1], 0, ceil($td_count));
                        $secondArraykpr = array_splice($expertise['allNotes'][1], ceil($td_count));
                    @endphp
                    @for ($i = 0; $i < $td_count; $i++)
                        <tr>
                            <td class="col-6" @if(empty($firstArraykpr[$i])) style="padding:15px;" @endif>{{!empty($firstArraykpr[$i]) ? $firstArraykpr[$i]:''}} </td>
                            <td class="col-6" @if(empty($firstArraykpr[$i])) style="padding:15px;" @endif> {{!empty($secondArraykpr[$i]) ? $secondArraykpr[$i] : ''}} </td>
                        </tr>
                    @endfor
                    </tbody>
                </table>
                <table class="table table-bordered">

                    <tbody>
                    <tr>
                        <td style="background-color: #ff1000;color: #fff;text-align: center;font-weight: bolder" colspan="4">SERVİSLERİMİZDEN ALINAN EKSPERTİZ RAPORLARINA WWW.UMRANOTO.COM.TR ADRESİNDEN ULAŞABİLİRSİNİZ.</td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            Umran Oto Ekspertiz Merkezleri tüm dünyadaki ekspertiz hizmetleri gibi aracın o anki durumunu analiz eder ve bilgi verir. Ekspertiz yaptığı aracın kesinlikle ileride arıza ve problem çıkarmayacağını garanti edemez. Aracın o anki görülen hataları kontrol edilerek raporlanır. Kaporta boya değerlendirmesine itiraz 72 saat, motor ile ilgili değerlendirmeye itiraz 24 saattir. Bayilerimizde kilometre orijinalliği tespiti yapılmamaktadır. Elektronik arşiv süresi 5 yıldır.
                        </td>
                    </tr>
                    <tr class="text-center">
                        <td>
                            KAPORTA BOYA TEKNİKERİ
                        </td>
                        <td>
                            SERVİS MÜDÜRÜ
                        </td>
                        <td>
                            MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['kvkk_durum'] == "true" && !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : $expertise['getAlici'] }}</b>
                        </td>
                        <td>
                            MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['kvkk_durum'] == "true" && !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : $expertise['getSatici'] }}</b>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="d-flex justify-content-between" style="font-size: 1.1rem">
                    <span><span class="text-danger">KBR</span> 1/1 Örnek Mh. Fehmi Tokay Cad. No:18 B ATAŞEHİR İSTANBUL</span>
                    <span>ÜMRANİYE V.D. 0910564940</span>
                </div>
            </div>
        </div>
        <div class="page-break"></div>
        <div class="table-responsive">
            <table class="table">
                <tbody>
                <tr>
                    <td colspan="2" style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 100%">
                        <h5 style="color: darkorange">444 5 417</h5>
                    </td>
                    <td colspan="3" style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU</b><br>
                        Bağımsız Oto Ekspertiz Merkezi<br> www.umranoto.com.tr<br><br>
                        <b>Umranak Oto Ekspertiz Hizm. Tic. Ltd. Şti. Göztepe</b>
                    </td>
                    <td colspan="1" class="text-center" style="width: 25%">
                        <b>TSE-HYB Belge No:</b><h5 class="text-danger">34-HYB-18692</h5>
                    </td>
                    <td colspan=""  style="width: 25%;text-align:center;">
                        {!! $expertise['qr'] !!}
                    </td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Bayi:</td>
                    <td colspan="2"><b>{{ $expertise['getBranch'] }}</b></td>
                    <td colspan="1">Marka/Model:</td>
                    <td colspan="1">{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td colspan="1">KM:</td>
                    <td colspan="1"><b>{{ number_format($expertise['km'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Belge No:</td>
                    <td colspan="2"><b class="text-danger">{{ $expertise['belge_no'] }}</b></td>
                    <td colspan="1">Model Yılı:</td>
                    <td colspan="1"><b>{{ $expertise['model_yili'] }}</b></td>
                    <td colspan="1">Motor Hacmi:</td>
                    <td colspan="1"><b>{{ number_format($expertise['motor_hacmi'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Giriş Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['created_at'] }}</b></td>
                    <td colspan="1">Şase Numarası:</td>
                    <td colspan="1"><b>
                            {{ $expertise['kvkk_durum'] == "true" && !empty($expertise['sase_no']) ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td colspan="1">Yakıt Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getFuel'] }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Çıkış Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['cikis_tarihi'] }}</b></td>
                    <td colspan="1">Plaka:</td>
                    <td colspan="1"><b>
                            {{ $expertise['kvkk_durum'] == "true" && !empty($expertise['plaka']) ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : $expertise['plaka'] }}
                        </b></td>
                    <td colspan="1">Vites Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getGear'] }}</b></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="row">
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>MOTOR BÖLÜMÜ</th>
                        <th>DURUMU</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($engines1 as $key => $subControlAndEngine1)
                        @if(($key == "co2_kacak_testi" && $expertise['co2_kacak_testi'] == true) || $key != "co2_kacak_testi")
                            <tr>
                                <td><b>{{ $subControlAndEngine1 }}</b></td>
                                <td class="text-center">
                                    @if($expertise['getSubControlsAndEngines'][$key]['answer'] == "turbo_yok" || $expertise['getSubControlsAndEngines'][$key]['answer'] == "deger_giriniz")
                                        --
                                    @else
                                        {{ucfirst($expertise['getSubControlsAndEngines'][$key]['answer'])}}
                                    @endif
                                </td>
                                <td>{!! $expertise['getSubControlsAndEngines'][$key]['note'] !!}</td>
                            </tr>
                        @endif
                    @endforeach
                    </tbody>
                </table>
            </div>
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>İÇ KONTROLLER</th>
                        <th>DURUM</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach(__('arrays.internal_controls') as $key => $internalControl)
                        <tr>
                            <td><b>{{ $internalControl }}</b></td>
                            <td class="text-center">{!! $expertise['getInternalControls'][$key]['answer'] != 'yok' ? ucfirst($expertise['getInternalControls'][$key]['answer']) : '---' !!}</td>
                            <td>{!! $expertise['getInternalControls'][$key]['note'] !!}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>ALT KONTROLLER</th>
                        <th>DURUM</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($engines2 as $key => $engine2)
                        @if($engine2 != 'Elektrik Sistemi')

                            @if($key == 'fren_sistemi_kontrolu')

                                <tr>
                                    <td><b>{{__('arrays.components')['sanziman_vites_gecisleri_ve_ses_kontrolu']}} </b></td>
                                    <td class="text-center">
                                        @if($expertise['getComponents']['dortceker_kontrolu']['answer'] == "yok")
                                            ----
                                        @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "iyi")
                                            İyi
                                        @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "test_yapılmadı")
                                            Test Yapılmadı
                                        @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "kötü")
                                            Kötü
                                        @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "orta")
                                            Orta
                                        @endif
                                    </td>
                                    <td>{{$expertise['getComponents']['dortceker_kontrolu']['note']}}</td>
                                </tr>
                            @endif
                            <tr>
                                <td><b>{{ $engine2 }}</b></td>
                                <td class="text-center">
                                    {{ ucfirst($expertise['getSubControlsAndEngines'][$key]['answer']) }}
                                </td>
                                <td>{!! $expertise['getSubControlsAndEngines'][$key]['note'] !!}</td>
                            </tr>
                        @else
                            @php
                                $electrical_values[0] = [
                                    'name' => $engine2,
                                    'answer' => $expertise['getSubControlsAndEngines'][$key]['answer'],
                                    'note' => $expertise['getSubControlsAndEngines'][$key]['note'],
                                ];
                            @endphp
                        @endif

                    @endforeach
                    <tr>
                        <td><b>{{__('arrays.components')['dortceker_kontrolu']}}</b></td>
                        <td class="text-center">
                            @if($expertise['getComponents']['dortceker_kontrolu']['answer'] == "yok")
                                ----
                            @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "iyi")
                                İyi
                            @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "test_yapılmadı")
                                Test Yapılmadı
                            @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "kötü")
                                Kötü
                            @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "orta")
                                Orta
                            @endif
                        </td>
                        <td>{{$expertise['getComponents']['dortceker_kontrolu']['note']}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th class="text-center">LASTİK VE JANTLAR</th>
                        <th class="text-center">DURUM</th>
                        <th class="text-center">DİŞ(mm)</th>
                        <th class="text-center">BASINÇ</th>
                        <th class="text-center">Açıklama</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach(__('arrays.tire_and_rims') as $key => $tire)
                        <tr>
                            <td><b>{{ $tire }}</b></td>
                            <td >

                                @if($expertise['getTireAndRims'][$key]['sorunlu_mu'] == 1)
                                    Kontrol Edildi
                                @elseif($expertise['getTireAndRims'][$key]['sorunlu_mu'] != null)
                                    Kontrol Edilmedi {{$expertise['getTireAndRims'][$key]['sorunlu_mu']}}
                                @endif
                            </td>
                            <td class="text-center">{!! !empty($expertise['getTireAndRims'][$key]['dis']) ? number_format($expertise['getTireAndRims'][$key]['dis'], 1) : ''; !!}</td>
                            <td class="text-center">{!! $expertise['getTireAndRims'][$key]['basinc'] !!}</td>
                            <td>{{$expertise['getTireAndRims'][$key]['note']}}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>DİAGNOSTİK CİHAZ KONTROL SONUÇLARI</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php
                        $td_count = 4;
                        if(count($expertise['allNotes'][3]) > $td_count){
                            $td_count = count($expertise['allNotes'][3]);
                        }

                        foreach($expertise['allNotes'][3] as $key => $notes_array){
                            $expertise['allNotes'][3][$key] = '[DIAG] '.$notes_array;
                        }
                    @endphp
                    @for ($i = 0; $i < $td_count; $i++)
                        <tr>

                            <td >{{!empty($expertise['allNotes'][3][$i]) ? $expertise['allNotes'][3][$i] : ''}}</td>
                        </tr>
                    @endfor
                    </tbody>
                </table>
            </div>
            <div class="col-md-12 table-responsive mt-3">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th colspan="4" class="text-center">NOTLAR</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php
                        $td_count = 10;
                        $td_count_2 = (count($expertise['allNotes'][4]) + count($expertise['allNotes'][5])+ count($expertise['allNotes'][6]) + count($expertise['allNotes'][7]) + count($expertise['allNotes'][2])) / 2;
                        if($td_count_2 > 10){
                            $td_count = $td_count_2;
                        }

                        foreach($expertise['allNotes'][4] as $key => $notes_array){
                            $expertise['allNotes'][4][$key] = '[ICK] '.$notes_array;
                        }
                        foreach($expertise['allNotes'][5] as $key => $notes_array){
                            $expertise['allNotes'][5][$key] = '[LAJ] '.$notes_array;
                        }
                        foreach($expertise['allNotes'][6] as $key => $notes_array){
                            $expertise['allNotes'][6][$key] = '[ALTK] '.$notes_array;
                        }
                        foreach($expertise['allNotes'][7] as $key => $notes_array){
                            $expertise['allNotes'][7][$key] = '[ALTK] '.$notes_array;
                        }
                        foreach($expertise['allNotes'][2] as $key => $notes_array){
                            $expertise['allNotes'][2][$key] = '[FRN] '.$notes_array;
                        }

                        $mergedArray = array_merge(
                            $expertise['allNotes'][2    ],
                            $expertise['allNotes'][4],
                            $expertise['allNotes'][5],
                            $expertise['allNotes'][6],
                            $expertise['allNotes'][7]
                        );

                        // Toplam eleman sayısını bul

                        $firstArray = array_slice($mergedArray, 0, ceil($td_count));
                        $secondArray = array_splice($mergedArray, ceil($td_count));

                    @endphp
                    @for ($i = 0; $i < $td_count; $i++)
                        <tr>
                            <td class="col-6" @if(empty($firstArray[$i])) style="padding:15px;" @endif>{{!empty($firstArray[$i]) ? $firstArray[$i]:''}} </td>
                            <td @if(empty($firstArray[$i])) style="padding:15px;" @endif> {{!empty($secondArray[$i]) ? $secondArray[$i] : ''}} </td>
                        </tr>
                    @endfor

                    </tbody>
                </table>
            </div>
            <div class="col-12 table-responsive">
                <table class="table table-bordered">
                    <tbody>
                    <tr>
                        <td>
                            <p>1* Ekspertiz işlemlerinde triger seti kontrolü yapılmamaktadır.</p>
                            <p>2* Akü ölçümü ekspertiz işlemi anındaki değerlere göre yapılmaktadır. Sonraki kullanımdan kaynaklanan sorunlarda firmamızın herhangi bir garantisi yoktur.</p>
                            <p>3* Kumanda kontrol düğmeleri kontrolü; Far düğmeleri, Sinyal kolu, silecek kolu, elektrikli ayna(ısıtma hariç), cam düğmeleri, bağaj, depo kapağı düğmesi, cam perdeleri, tavan ve sunroof düğmesi kontrollerini kapsamaktadır. Bunların dışında kalan kumanda düğmeleri kontrol edilmemektedir.</p>
                            <p>4* Farlarda yalnızca kırık, çatlak ve yanıp yanmadığının kontrolü yapılmaktadır. Orijinallik, xenon ve su alma testi yapılmamaktadır.</p>
                        </td>
                        <td colspan="3">
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th>KOMPONENTLER</th>
                                    <th>İYİ</th>
                                    <th>ORTA</th>
                                    <th>KÖTÜ</th>
                                    <th>HAFİF</th>
                                    <th>YOK</th>
                                    <th>Açıklama </th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach(__('arrays.components') as $key => $component)
                                    @if($key != "dortceker_kontrolu" && $key != 'sanziman_vites_gecisleri_ve_ses_kontrolu')
                                        <tr>
                                            <td>{{ $component }}</td>
                                            <td>{!! $expertise['getComponents'][$key]['answer'] == 'iyi' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                            <td>{!! $expertise['getComponents'][$key]['answer'] == 'orta' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                            <td>{!! $expertise['getComponents'][$key]['answer'] == 'kötü' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                            <td>{!! $expertise['getComponents'][$key]['answer'] == 'hafif' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                            <td>{!! $expertise['getComponents'][$key]['answer'] == 'yok' ? '<i class="fa fa-check"></i>' : '' !!} </td>
                                            <td>
                                                @if($expertise['getComponents'][$key]['answer'] == 'test_yapılmadı')
                                                    Test Yapılmadı
                                                @else
                                                    {{$expertise['getComponents'][$key]['note']}}
                                                @endif
                                            </td>
                                        </tr>
                                    @endif
                                @endforeach
                                <tr>
                                    <td>{{$electrical_values[0]['name']}} </td>
                                    <td>{!! $electrical_values[0]['answer'] == 'iyi' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! $electrical_values[0]['answer'] == 'orta' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! $electrical_values[0]['answer'] == 'kötü' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! $electrical_values[0]['answer'] == 'hafif' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! $electrical_values[0]['answer'] == 'yok' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{{$electrical_values[0]['note']}}</td>
                                </tr>
                                </tbody>
                            </table>

                            <div class="d-flex justify-content-between" style="font-size: 1.1rem">
                                <span><span class="text-danger">KBR</span> 1/1 Örnek Mh. Fehmi Tokay Cad. No:18 B ATAŞEHİR İSTANBUL</span>
                                <span>ÜMRANİYE V.D. 0910564940</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="background-color: #ff1000;color: #fff;text-align: center;font-weight: bolder" colspan="4">SERVİSLERİMİZDEN ALINAN EKSPERTİZ RAPORLARINA WWW.UMRANOTO.COM.TR ADRESİNDEN ULAŞABİLİRSİNİZ.</td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            Umran Oto Ekspertiz Merkezleri tüm dünyadaki ekspertiz hizmetleri gibi aracın o anki durumunu analiz eder ve bilgi verir. Ekspertiz yaptığı aracın kesinlikle ileride arıza ve problem çıkarmayacağını garanti edemez. Aracın o anki görülen hataları kontrol edilerek raporlanır. Kaporta boya değerlendirmesine itiraz 72 saat, motor ile ilgili değerlendirmeye itiraz 24 saattir. Bayilerimizde kilometre orijinalliği tespiti yapılmamaktadır. Elektronik arşiv süresi 5 yıldır.
                        </td>
                    </tr>
                    <tr class="text-center">
                        <td>
                            KAPORTA BOYA TEKNİKERİ
                        </td>
                        <td>
                            SERVİS MÜDÜRÜ
                        </td>
                        <td>
                            MÜŞTERİ (ALICI)<br><br><b>{{ $expertise['kvkk_durum'] == "true" && !empty($expertise['getAlici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : $expertise['getAlici'] }}</b>
                        </td>
                        <td>
                            MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['kvkk_durum'] == "true" && !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : $expertise['getSatici'] }}</b>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
@endsection
