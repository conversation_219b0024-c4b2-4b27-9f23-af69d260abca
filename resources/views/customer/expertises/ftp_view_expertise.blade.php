@extends('pages.build')
@section('title','Ekspertiz Raporu')
@push('css')
    <style>
        table{
            border: 1px solid #000 !important;
        }
        th{
            background-color: #cecece57 !important
        }
        .page-break{
            page-break-after: always;
        }
        .table-bordered>:not(caption)>*>*{
            border-width: 1px;
        }
        .table td, .table th{
            padding: .35rem;
        }
        .table-col-6 td{
            width: 16.667% !important;
            border: 1px solid;
        }
        @media print{
            body{
                visibility: hidden;
            }

            #divRaporEkrani{
                visibility: visible;
            }
        }
        div#divRaporEkrani {
            padding: 10px;
            margin-top: 20px;
        }
    </style>
    <link href="http://127.0.0.1:8000/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet">
@endpush
@section('content')
    <div>
        <div class="wrapper text-center mt-5">
            <a href="{{ route('customer.ekspertizRaporu',$expertises->uuid) }}?type=not_show" class="btn btn-sm btn-danger"><PERSON>or Gözükmüyorsa Tıklayın</a>
        </div>
        {!! $icerik !!}
        @if(strlen($icerik) < 1000)

        @else
{{--            {!! preg_replace('/<style\b[^>]*>.*?<\/style>/s', '', $icerik) !!}--}}

        @endif
    </div>
@endsection
@push('js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelector("meta[name='viewport']").setAttribute('content','');
        });
    </script>
@endpush
