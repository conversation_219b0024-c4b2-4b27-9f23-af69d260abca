@extends('pages.build')
@section('title','Satın Al')
@push('css')
    <style>
        body {
            font-family: 'Arial', sans-serif;
        }


        .package {
            background: linear-gradient(99deg,#102048,#707070);
            border-radius: 4px;
            padding: 20px;
            margin-bottom: 10px;
            text-align: center;
            color: #fff;
            width: 100%;
        }

        .package h2 {
            color: #fff;
            margin: 0 0 10px 0;
        }

        .package p {
            margin: 5px 0;
        }
        .paynetj-button{
            display: none;
        }
    </style>
@endpush
@section('content')
    <div class="package-container mt-3">
        @foreach($plusCardDefinitions as $index => $plusCardDefinition)
            <div class="package">
                <div class="row">
                    <div class="col-8">
                        <h2>{{ $plusCardDefinition->definition_name }}</h2><hr>
                        <p>{{ $plusCardDefinition->unit_quantity }} Adet Plus Card Bakiyesi</p>
                    </div>
                    <div class="col-4 d-flex align-items-center">
                        <button type="button"
                                class="btn btn-danger paynetButton"
                                data-amount="{{ ($plusCardDefinition->unit_quantity * $plusCardDefinition->unit_price) * 100 }}"
                                data-button_label="Satın Al ({{ $plusCardDefinition->unit_quantity * $plusCardDefinition->unit_price }}₺)"
                                data-name="Plus Card Satın Al"
                                data-image="/assets/umram4.webp"
                                data-plus_card_definition_id="{{ $plusCardDefinition->id }}"
                                data-agent="1697"
                                data-index="{{ $index }}"
                                data-no_instalment="false"
                                data-tds_required="true"
                                data-form="#customerBuyPlusCardPaynetForm"
                                data-add_commission_amount="true"
                                data-reference_no="{{ strtoupper(\Illuminate\Support\Str::random(8)) }}"
                                data-description="{{ $plusCardDefinition->unit_quantity }} Adet Plus Card Bakiyesi Satın Alıyorsunuz."
                        >{{ $plusCardDefinition->unit_quantity * $plusCardDefinition->unit_price }} TL</button>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    <form id="customerBuyPlusCardPaynetForm" action="{{ route('customer.customerBuyPlusCardPaynetForm') }}" method="POST">@csrf
        <input type="hidden" name="return_url" value="{{ url()->full() }}">
        <input type="hidden" name="add_comission_amount" value="true">
        <input type="hidden" name="no_instalment" value="false">
        <input type="hidden" name="tds_required" value="true">
        <input type="hidden" name="ratio_code" value="">
        <input type="hidden" name="installments" value="">
        <input type="hidden" name="reference_no" value="">
        <input type="hidden" name="plus_card_definition_id" value="">
        <input type="hidden" name="plus_card_id" value="{{ $_GET['plus_card_id'] ?? '' }}">
        <script
            id="paynetScript"
            class="paynet-button"
            type="text/javascript"
            src="https://pj.paynet.com.tr/public/js/paynet.min.js"
            data-key="pbk_rPMxYmDP2b8nhT2aZuhxQqyNZoqg"
            data-amount=""
            data-name="Plus Card Satın Al"
            data-image="/assets/umram4.webp"
            data-agent="1697"
            data-no_instalment="false"
            data-tds_required="true"
            data-form=""
            data-add_commission_amount="true"
            data-reference_no=""
            data-description=""
        >
        </script>
    </form>
@endsection

@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script>
        document.querySelectorAll('.paynetButton').forEach(function(button) {
            button.addEventListener('click', function() {
                const $customerBuyPlusCardPaynetForm = document.getElementById('customerBuyPlusCardPaynetForm');
                $customerBuyPlusCardPaynetForm.querySelector('input[name="reference_no"]').value = button.getAttribute('data-reference_no');
                $customerBuyPlusCardPaynetForm.querySelector('input[name="plus_card_definition_id"]').value = button.getAttribute('data-plus_card_definition_id');
                document.querySelectorAll('.paynet-button').forEach(function (script){
                    // Transfer data attributes from the button to the script
                    script.setAttribute('data-amount', button.getAttribute('data-amount'));
                    script.setAttribute('data-button_label', button.getAttribute('data-button_label'));
                    script.setAttribute('data-form', button.getAttribute('data-form'));
                    script.setAttribute('data-reference_no', button.getAttribute('data-reference_no'));
                    script.setAttribute('data-description', button.getAttribute('data-description'));
                })
                // Trigger the click on the paynetj-button element
                document.querySelector('.paynetj-button').click();
            });
        });
    </script>
@endpush
