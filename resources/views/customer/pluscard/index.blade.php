@extends('pages.build')
@section('title','Plus Card Bakiye')
@section('content')
    <div class="row">
        <div class="col-12 mt-3"><p class="text-center">Kalan Plus Card Bakiyeniz</p></div>
        <div class="col-6">
            <div class="d-flex align-items-center">
                <i class="ri-bank-card-line" style="color: #ff1000;font-size: 1.8rem"></i><span style="margin-left: 1rem;font-weight: bold">{{ $credit }} Adet Kredi</span>
            </div>
        </div>
        <div class="col-6">
            <div class="d-flex justify-content-end align-items-center">
                <span style="width: 2rem;height: 2rem;padding: 5px;background-color: #ff1000;color: #fff;text-align: center;align-items: center;border-radius: 50%">{{ $point }} </span>Adet Puan
            </div>
        </div>
        <div class="col-6 mt-3">
            <span>Son <PERSON></span>
        </div>
        <div class="col-6 mt-3">
            <div class="d-flex justify-content-end align-items-center">
                <button class="btn btn-danger">Tüm İşlemler</button>
            </div>

        </div>
        @foreach($plusCards as $plusCard)
            <div class="col-12">
                <div class="table-responsive">
                    <table class="table table-bordered table-sm table-striped ">
                        <thead>
                            <tr>
                                <td class="text-center" colspan="10"><h5 class="text-danger ">{{ $plusCard->no }}</h5> numaralı plus card özeti</td>
                            </tr>
                            <tr>
                                <th>Tanım Adı</th>
                                <th>Yük.</th>
                                <th>Düş.</th>
                                <th>Kul.</th>
                                <th>Kalan</th>
                                <th>Tür</th>
                                <th>Bayi</th>
                                <th>Son K. Tarihi</th>
                                <th>Açıklama</th>
                                <th>Ödeme</th>
                            </tr>
                        </thead>
                        <tbody>
                        @forelse($plusCard->getBalance as $balance)
                            <tr class="have_plus_card_stock_{{$balance->id}}">
                                <td>@if($balance->definitions_id == 0 && $balance->devir_miktar > 0) Devir @else {{$balance->getDefinitions->definition_name ?? ''}} @endif</td>
                                <td class="balance{{ $balance->id }}">@if($balance->balance_type == "credits") {{$balance->credi}} @else {{$balance->puan}} @endif</td>
                                <td>{{ $balance->getRemoves->sum('amount') }}</td>
                                <td>{{ $balance->getUsages->count() }}</td>
                                <td>{{ ($balance->balance_type == "credits" ? $balance->credi : $balance->puan) - $balance->getRemoves->sum('amount') - $balance->getUsages->count() }}</td>
                                <td>@if($balance->balance_type == "credits") Kredi @else Puan @endif</td>
                                <td>{{!empty($balance->getBranche->kisa_ad) ? $balance->getBranche->kisa_ad : 'Hepsi'}}</td>
                                <td>{{$balance->valid_date}}</td>
                                <td>{{$balance->devir_aciklama}}</td>
                                <td>{{number_format((float)$balance->odenen_kdv_dahil_fiyat, 2, ',', '.')}}₺</td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10">
                                    <div class="d-flex align-items-center justify-content-center" style="flex-direction: column">
                                        <i class="ri-close-circle-line" style="font-size: 4rem;color: #ff1000"></i>
                                        <span style="margin-top: 2rem">Hiç Kayıt Bulunamadı!</span>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                        </tbody>
                        <tfoot>
                            <tr>
                                <td class="text-center" colspan="10">
                                    <a href="{{ route('customer.plusCardBuy',['plus_card_id'=>$plusCard->id]) }}" class="btn btn-danger">Satın Al</a>
                                </td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        @endforeach
    </div>
@endsection
