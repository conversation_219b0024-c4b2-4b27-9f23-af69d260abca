@extends('pages.build')
@section('title','Profilim')
@push('css')
    <style>
        input{
            width: 100%;
            border: unset;
            border-bottom: 1px solid;
            background: transparent;
        }
        label{
            color: #424242;
        }
    </style>
@endpush
@section('content')
    <div class="card">
        <div class="card-body">
            <form method="post" action="{{ route('customer.updateProfile') }}">@csrf
                <div class="form-group">
                    <label>İsim</label>
                    <input name="unvan" value="{{ auth('customer')->user()->unvan }}" required>
                </div>
                <div class="form-group">
                    <label>Telefon</label>
                    <input name="telefon" value="{{ auth('customer')->user()->telefon }}" @if(auth('customer')->user()->kvkk_verified == 1) disabled @else required @endif>
                </div>
                <div class="form-group">
                    <label>Cep</label>
                    <input name="cep" value="{{ auth('customer')->user()->cep }}">
                </div>
                <div class="form-group">
                    <label>E-posta</label>
                    <input name="eposta" type="email" value="{{ auth('customer')->user()->eposta }}">
                </div>
                <div class="form-group">
                    <label>Web Sitesi</label>
                    <input name="web" value="{{ auth('customer')->user()->web }}">
                </div>
                <div class="form-group">
                    <label>Şehir</label>
                    <input name="il" value="{{ auth('customer')->user()->il }}">
                </div>
                <div class="form-group">
                    <label>İlçe</label>
                    <input name="ilce" value="{{ auth('customer')->user()->ilce }}">
                </div>
                <div class="form-group">
                    <label>Semt</label>
                    <input name="semt" value="{{ auth('customer')->user()->semt }}">
                </div>
                <div class="form-group">
                    <label>Mahalle</label>
                    <input name="mahalle" value="{{ auth('customer')->user()->mahalle }}">
                </div>
                <div class="form-group">
                    <label>Sokak</label>
                    <input name="sokak" value="{{ auth('customer')->user()->sokak }}">
                </div>
                <button name="type" value="submit" class="btn btn-success btn-sm">Kaydet</button>
                <button type="button" class="btn btn-danger btn-sm" onclick="return confirmAndPrompt()">Hesabımı Sil</button>
                <div class="form-group" id="smsCodeContainer" style="display: none; margin-top: 15px;">
                    <label for="smsCode">SMS Kodu:</label>
                    <input type="text" id="smsCode" name="sms_code" class="form-control" placeholder="SMS kodunu girin">
                    <button name="type" value="delete" type="submit" class="btn btn-primary btn-sm" style="margin-top: 10px;">Hesabımı Sil</button>
                </div>
            </form>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/imask.js"></script>
    <script>
        function confirmAndPrompt() {
            if (confirm('Hesabınızı silmek istediğinize emin misiniz?')) {
                // Onay verildiyse SMS kodu kutucuğunu göster
                document.getElementById('smsCodeContainer').style.display = 'block';
                document.getElementById('smsCode').setAttribute('required','true');

                const xhr = new XMLHttpRequest();
                xhr.open('POST', '{{ route('api.customerDeleteSmsConfirmation') }}', true);
                xhr.setRequestHeader('Content-Type', 'application/json;charset=UTF-8');
                xhr.setRequestHeader('X-CSRF-TOKEN', '{{ csrf_token() }}'); // CSRF token ekle

                // İstek gövdesini JSON formatında gönder
                xhr.send();


                return false; // Formun hemen gönderilmesini engelle
            } else {
                return false; // Onay verilmezse hiçbir şey yapma
            }
        }

        if(document.querySelector('input[name="sms_code"]')){
            const maskTel = IMask(document.querySelector('input[name="sms_code"]'), {
                mask: 'UMR-00-00'
            });
        }
    </script>
@endpush
