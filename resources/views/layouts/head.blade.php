<head>

    <!-- Meta Data -->
    <meta charset="UTF-8">
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title> @yield('title','Ümran Ekspertiz') </title>
    <meta name="Description" content="">
    <meta name="keywords" content="">

    <!-- Favicon -->
    <link rel="icon" href="/assets/logo-icon.png" type="image/x-icon">

    <!-- Choices JS -->
    <script src="/assets/libs/choices.js/public/assets/scripts/choices.min.js"></script>

    <!-- Main Theme Js -->
    <script src="/assets/js/main.js"></script>

    <!-- Bootstrap Css -->
    <link id="style" href="/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet" >

    <!-- Style Css -->
    <link href="/assets/css/styles.min.css" rel="stylesheet" >

    <!-- Icons Css -->
    <link href="/assets/css/icons.css" rel="stylesheet" >

    <!-- Node Waves Css -->
    <link href="/assets/libs/node-waves/waves.min.css" rel="stylesheet" >

    <!-- Simplebar Css -->
    <link href="/assets/libs/simplebar/simplebar.min.css" rel="stylesheet" >

    <!-- Color Picker Css -->
    <link rel="stylesheet" href="/assets/libs/flatpickr/flatpickr.min.css">
    <link rel="stylesheet" href="/assets/libs/@simonwep/pickr/themes/nano.min.css">

    <!-- Choices Css -->
    <link rel="stylesheet" href="/assets/libs/choices.js/public/assets/styles/choices.min.css">
    @stack('css')
    <style>
        body {
            overflow-x: hidden;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered{
            line-height: 1.8rem !important;
        }
        .select2-container .select2-selection--single, .select2-container--default .select2-selection--single .select2-selection__arrow{
            height: 1.8rem !important;
        }
        .form-control{
            border-color: #9b9b9d;
            color: #3b3f48;
        }

        ::-webkit-scrollbar {
            width: 0.25rem;
            height: 0.65rem;
            background: #01e0f491;
            -webkit-transition: all ease 50ms;
            transition: all ease 50ms;
        }

        @media screen and (min-width: 500px) {
            .app-sidebar .side-menu__item{
                padding-inline-end: 50px;
            }
            .animated-arrow.hor-toggle{
                width: unset;
                font-size: unset;
            }
            .animated-arrow.hor-toggle span{
                left: -.5rem;
            }
            .main-header-container{
                align-items: center;
            }
        }
        .select2-container--default .select2-search--dropdown .select2-search__field{
            border: 1px solid #5f5f60!important;
        }
        .dropify-wrapper .dropify-message p{
            font-size: 13px;
        }
        .accordion-button{
            background-color: #f3f3f3;
        }
        .accordion-button:not(.collapsed){
            background-color: #e30614;
            color: #fff;
        }

        input[required],select[required],textarea[required]{
            border-color: #ffb9b9 !important;
        }

        .filter-results{
            padding: .5rem .85rem;
            background-color: #f3f3f3;
            max-height: 300px;
            overflow: auto;
            display: none;
        }
        .filter-item{
            cursor: pointer;
        }
        .filter-item:hover{
            color: #ff6565;
        }
        body{
            font-size: .913rem;
        }
        .app-sidebar .side-menu__label{
            margin-left: 7px;
        }
        #responsiveDataTable_filter input{
            background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+PHN2ZyAgIHhtbG5zOmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIgICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIiAgIHhtbG5zOnJkZj0iaHR0cDovL3d3dy53My5vcmcvMTk5OS8wMi8yMi1yZGYtc3ludGF4LW5zIyIgICB4bWxuczpzdmc9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgICB2ZXJzaW9uPSIxLjEiICAgaWQ9InN2ZzQ0ODUiICAgdmlld0JveD0iMCAwIDIxLjk5OTk5OSAyMS45OTk5OTkiICAgaGVpZ2h0PSIyMiIgICB3aWR0aD0iMjIiPiAgPGRlZnMgICAgIGlkPSJkZWZzNDQ4NyIgLz4gIDxtZXRhZGF0YSAgICAgaWQ9Im1ldGFkYXRhNDQ5MCI+ICAgIDxyZGY6UkRGPiAgICAgIDxjYzpXb3JrICAgICAgICAgcmRmOmFib3V0PSIiPiAgICAgICAgPGRjOmZvcm1hdD5pbWFnZS9zdmcreG1sPC9kYzpmb3JtYXQ+ICAgICAgICA8ZGM6dHlwZSAgICAgICAgICAgcmRmOnJlc291cmNlPSJodHRwOi8vcHVybC5vcmcvZGMvZGNtaXR5cGUvU3RpbGxJbWFnZSIgLz4gICAgICAgIDxkYzp0aXRsZT48L2RjOnRpdGxlPiAgICAgIDwvY2M6V29yaz4gICAgPC9yZGY6UkRGPiAgPC9tZXRhZGF0YT4gIDxnICAgICB0cmFuc2Zvcm09InRyYW5zbGF0ZSgwLC0xMDMwLjM2MjIpIiAgICAgaWQ9ImxheWVyMSI+ICAgIDxnICAgICAgIHN0eWxlPSJvcGFjaXR5OjAuNSIgICAgICAgaWQ9ImcxNyIgICAgICAgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNjAuNCw4NjYuMjQxMzQpIj4gICAgICA8cGF0aCAgICAgICAgIGlkPSJwYXRoMTkiICAgICAgICAgZD0ibSAtNTAuNSwxNzkuMSBjIC0yLjcsMCAtNC45LC0yLjIgLTQuOSwtNC45IDAsLTIuNyAyLjIsLTQuOSA0LjksLTQuOSAyLjcsMCA0LjksMi4yIDQuOSw0LjkgMCwyLjcgLTIuMiw0LjkgLTQuOSw0LjkgeiBtIDAsLTguOCBjIC0yLjIsMCAtMy45LDEuNyAtMy45LDMuOSAwLDIuMiAxLjcsMy45IDMuOSwzLjkgMi4yLDAgMy45LC0xLjcgMy45LC0zLjkgMCwtMi4yIC0xLjcsLTMuOSAtMy45LC0zLjkgeiIgICAgICAgICBjbGFzcz0ic3Q0IiAvPiAgICAgIDxyZWN0ICAgICAgICAgaWQ9InJlY3QyMSIgICAgICAgICBoZWlnaHQ9IjUiICAgICAgICAgd2lkdGg9IjAuODk5OTk5OTgiICAgICAgICAgY2xhc3M9InN0NCIgICAgICAgICB0cmFuc2Zvcm09Im1hdHJpeCgwLjY5NjQsLTAuNzE3NiwwLjcxNzYsMC42OTY0LC0xNDIuMzkzOCwyMS41MDE1KSIgICAgICAgICB5PSIxNzYuNjAwMDEiICAgICAgICAgeD0iLTQ2LjIwMDAwMSIgLz4gICAgPC9nPiAgPC9nPjwvc3ZnPg==);
            background-repeat: no-repeat;
            background-color: #fff;
            background-position: 0 1px !important;
            text-indent: 10px;
        }
        .nav.nav-style-1{
            background-color: #e1e1e194;
        }
        .pull-left{
            float: left;
        }
        .pull-right{
            float: right;
        }
        .page{
            justify-content: unset;
        }
        @if(auth('customer')->check())


            ul.main-menu{
                background: #e30614;
            }

        .app-sidebar .side-menu__item.active i,.app-sidebar .side-menu__item.active .side-menu__label,.app-sidebar .side-menu__item,.app-sidebar .side-menu__label{
                color: #fff;
            }
            .app-header{
                background: #830008;
                border-block-end: 1px solid #e30614;
            }
        .animated-arrow span:after, .animated-arrow span:before,.animated-arrow span{
            background: #fff;
        }
        @media screen and (max-width: 500px) {
            .toggle-logo{
                display: none !important;
            }
        }
        .btn-danger{
            --bs-btn-bg: #830008;
            --bs-btn-border-color: #830008;
            --bs-btn-hover-bg: #6b020a;
            --bs-btn-hover-border-color: #830008;
            --bs-btn-active-bg: #b02a37;
            --bs-btn-active-border-color: #a52834;
            --bs-btn-disabled-bg: #dc3545;
            --bs-btn-disabled-border-color: #dc3545;
        }
        @endif
        .dataTables_empty{
            text-align: start !important;
        }
        .nav.nav-style-1 .nav-link.completed{
            background-color: rgb(184 203 18);
            color: #fff;
        }
        .nav.nav-style-1 .nav-link.not-completed{
            background-color: rgb(243 67 67);
            color: #fff;
        }
        @media screen and (max-width: 500px) {
            .d-none-mobile{
                display: none !important;
            }
        }
        /*.select2-container--default .select2-selection--single .select2-selection__rendered{*/
        /*    border: 1px solid #9b9b9d!important;*/
        /*}*/
        .dotted-underline{
            text-decoration: underline;
            text-decoration-color: #ff1000;
            text-decoration-style: dotted
        }
        .select2-selection.select2-selection--multiple{
            max-height: 100px;
            overflow: auto;
        }
    </style>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-J2SK861303"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-J2SK861303');
    </script>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-MHGV9B3BGS"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-MHGV9B3BGS');
    </script>
</head>
