<header class="app-header">
    <!-- Start::main-header-container -->
    <div class="main-header-container container-fluid">
        <!-- Start::header-content-left -->
        <div class="header-content-left align-items-center">
            <!-- Start::header-element -->
            <div class="header-element">
                <div class="horizontal-logo">
                    <a href="{{ route('index') }}" class="header-logo">
                        <img src="/assets/umram4.webp" alt="logo" class="desktop-logo">
                        <img src="/assets/umram4.webp" alt="logo" class="toggle-logo">
                        <img src="/assets/umram4.webp" alt="logo" class="desktop-dark">
                        <img src="/assets/umram4.webp" alt="logo" class="toggle-dark">
                        <img src="/assets/umram4.webp" alt="logo" class="desktop-white">
                        <img src="/assets/umram4.webp" alt="logo" class="toggle-white">
                    </a>
                </div>
                <!-- Start::header-link -->
                <a aria-label="Hide Sidebar" class="sidemenu-toggle header-link animated-arrow hor-toggle horizontal-navtoggle"
                    data-bs-toggle="sidebar" href="javascript:void(0);">
                    <span></span>
                </a>
                <!-- End::header-link -->
                @if(auth()->check())
                    @if($authUser->isAdmin())
                        <button type="button" class="btn btn-danger btn-sm d-none-mobile" style="font-size: 1rem;margin-left: 10px;border-end-start-radius: 5px;border-start-start-radius: 5px;position: inherit;">
                            <i class="fe fe-hash" style="font-size: 1rem;margin-right: 2px;"></i>3402
                            <i class="fe fe-git-branch" style="font-size: 1rem;margin-left: 5px;margin-right: 2px;"></i>Umran Plus
                        </button>
                    @else
                        <button type="button" class="btn btn-danger btn-sm d-none-mobile" style="font-size: 1rem;margin-left: 10px;border-end-start-radius: 5px;border-start-start-radius: 5px;position: inherit;">
                            <i class="fe fe-hash" style="font-size: 1rem;margin-right: 2px;"></i>{{ $authUser->getBranch?->kod }}
                            <i class="fe fe-home" style="font-size: 1rem;margin-left: 5px;margin-right: 2px;"></i>{{ $authUser->getBranch?->kisa_ad }}
                        </button>
                    @endif
                @endif
            </div>
            <!-- End::header-element -->
        </div>

        <div class="header-content-center d-none-mobile"></div>

        <div class="header-content-right">
            <div class="header-element">
            @if(auth()->check())
                <span class="header-link d-none-mobile"><i class="fe fe-bell text-danger" style="font-size: 1rem;margin-right: 2px;"></i>
                    @if($totalNotifications > 0)
                        <a href="{{ route('bulletin.index') }}" class="notification-icon">
                            <span class="badge bg-danger">{{ $totalNotifications }}</span>
                        </a>
                    @endif
                </span>
                <span class="header-link d-none-mobile"><i class="fe fe-shield text-danger" style="font-size: 1rem;margin-right: 2px;"></i>{{ $authUser->getUserRoleGroup?->name }}</span>
                <span class="header-link d-none-mobile"><i class="fe fe-hash text-danger" style="font-size: 1rem;margin-right: 2px;"></i>{{ auth()->id() }}</span>
                <p class="fw-semibold mb-0 lh-1 d-none-mobile header-link">
                    <i class="fe fe-user text-danger" style="font-size: 1rem;margin-right: 2px;"></i>{{ $authUser->fullName() }}
                </p>
            @endif
                <!-- Start::header-link|dropdown-toggle -->
                <a href="#" class="header-link dropdown-toggle" id="mainHeaderProfile" data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false" style="left:10px;">
                    <div class="d-flex align-items-center">
                        <div class="me-sm-2 me-0">
                            <img src="{{ !auth('customer')->check() && $authUser->image ? '/storage/'.$authUser->image : '/assets/logo-icon.png' }}" alt="img" width="32" height="32" class="rounded-circle">
                        </div>
                        @if(auth('customer')->check())
                            <div class="d-xl-block d-none">
                                <p class="fw-semibold mb-0 lh-1">{{ auth('customer')->user()->unvan }}</p>
                            </div>
                        @endif
                    </div>
                </a>
            </div>
        </div>
        <!-- End::header-content-right -->
    </div>
    <!-- End::main-header-container -->

</header>
