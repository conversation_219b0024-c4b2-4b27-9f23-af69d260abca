<aside class="app-sidebar sticky" id="sidebar">
    <!-- Start::main-sidebar-header -->
    <div class="main-sidebar-header">
        <a href="{{ route('index') }}" class="header-logo">
            <img src="/assets/umram4.webp" alt="logo" class="desktop-logo">
            <img src="/assets/umram4.webp" style="width: 2.5rem;height: unset" alt="logo" class="toggle-logo">
            <img src="/assets/umram4.webp" alt="logo" class="desktop-dark">
            <img src="/assets/umram4.webp" alt="logo" class="toggle-dark">
            <img src="/assets/umram4.webp" alt="logo" class="desktop-white">
            <img src="/assets/umram4.webp" alt="logo" class="toggle-white">
        </a>
    </div>
    <!-- End::main-sidebar-header -->
    <!-- Start::main-sidebar -->
    <div class="main-sidebar" id="sidebar-scroll">
        @if(auth('customer')->check())
            <div align="center">
                <img src="/assets/umram4.webp" alt="logo" class="img-fluid" style="width: 160px">
                <p class="text-center mt-2"><b>{{ auth('customer')->user()->unvan }}</b></p>
                <p class="text-center" style="font-size: .7rem">{{ auth('customer')->user()->telefon }}</p>
                <p class="text-center" style="font-size: .7rem">#{{ auth('customer')->user()->cari_kod }}</p>
            </div>
        @endif
        <!-- Start::nav -->
        <nav class="main-menu-container nav nav-pills flex-column sub-open">
            <div class="slide-left" id="slide-left">
                <svg xmlns="http://www.w3.org/2000/svg" fill="#7b8191" width="24" height="24" viewBox="0 0 24 24"> <path d="M13.293 6.293 7.586 12l5.707 5.707 1.414-1.414L10.414 12l4.293-4.293z"></path> </svg>
            </div>
            <ul class="main-menu">
                @if(!auth('customer')->check() && !$authUser->isExpert())
                    <!-- Start::slide__category -->
                    <li class="slide__category"><span class="category-name">Yönetim</span></li>
                    <!-- End::slide__category -->
                @endif

                @if(!auth('customer')->check() && $authUser->getUserRoleGroup->getRoles->whereIn('key',['list_dashboard'])->first())
                <!-- Start::slide -->
                <li class="slide">
                    <a href="{{ route('index') }}" class="side-menu__item @if(url()->current() == route('index')) active @endif">
                        <i class="fe fe-home" style="font-size: 1.3rem"></i>
                        <span class="side-menu__label">Ana Sayfa</span>
                    </a>
                </li>
                <!-- End::slide -->
                @endif

                <!-- Start::slide__category -->
                @if(!auth('customer')->check() && !$authUser->isExpert())
                    <li class="slide__category"><span class="category-name">İçerik Yönetimi</span></li>
                @endif

                @if(!auth('customer')->check())
{{--                    @if($authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->whereIn('key',['add_expertise','list_expertise'])->first())--}}
{{--                        <li @class(['slide', 'open' => request()->routeIs('expertise.*')])>--}}
{{--                            <a href="{{ route('expertises.index') }}" class="side-menu__item @if(in_array(url()->current(),[route('expertises.index'),route('ekspertizIslemleri'),route('expertises.create'),route('notes.index')])) active @endif">--}}
{{--                                <i class="fe fe-file-text" style="font-size: 1.3rem"></i>--}}
{{--                                <span class="side-menu__label">{{ \App\Models\Menu::where('key','expertise')->first()->value }}</span>--}}
{{--                            </a>--}}
{{--                        </li>--}}
{{--                    @endif--}}

                    @if(in_array('*',$userRoles) || in_array('list_expertise',$userRoles) || in_array('add_expertise',$userRoles))
                        <li @class(['slide has-sub', 'open' => request()->routeIs('expertises.*')])>
                            <a href="javascript:void(0);" @class(['side-menu__item', 'active' => request()->routeIs('expertises.*')])>
                                <i class="fe fe-file-text" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Ekspertiz</span>
                                <i class="fe fe-chevron-right side-menu__angle"></i>
                            </a>
                            <ul class="slide-menu child1">
                                <li class="slide side-menu__label1">
                                    <a href="javascript:void(0)">Ekspertiz</a>
                                </li>
                                @if(in_array('*',$userRoles) || in_array('add_expertise',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('expertises.create') }}" @class(['side-menu__item', 'active' => request()->routeIs('expertises.create')])>Yeni Ön Kayıt</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_expertise',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('expertises.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('expertises.index')])>Ekspertiz Listesi</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    @if(in_array('*', $userRoles) || in_array('list_car', $userRoles) || in_array('list_car_group', $userRoles) || in_array('list_car_case_type', $userRoles) || in_array('list_car_fuel', $userRoles) || in_array('list_car_gear', $userRoles))
                        <li @class(['slide has-sub', 'open' => request()->routeIs('cars.*', 'car-groups.*', 'car-case-types.*', 'car-fuels.*', 'car-gears.index')])>
                            <a href="javascript:void(0);" @class(['side-menu__item', 'active' => request()->routeIs('cars.*', 'car-groups.*', 'car-case-types.*', 'car-fuels.*', 'car-gears.index')])>
                                <i class="bx bx-car" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Araç Yönetimi</span>
                                <i class="fe fe-chevron-right side-menu__angle"></i>
                            </a>
                            <ul class="slide-menu child1">
                                <li class="slide side-menu__label1">
                                    <a href="javascript:void(0)">Araç Yönetimi</a>
                                </li>
                                @if(in_array('*',$userRoles) || in_array('list_car',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('cars.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('cars.*')])>Araçlar</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_car_group',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('car-groups.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('car-groups.*')])>Grup Yönetimi</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_car_case_type',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('car-case-types.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('car-case-types.*')])>Kasa Tür Yönetimi</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_car_fuel',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('car-fuels.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('car-fuels.*')])>Yakıt Yönetimi</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_car_gear',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('car-gears.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('car-gears.*')])>Vites Yönetimi</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif
                    @if(in_array('*', $userRoles) || in_array('list_customer', $userRoles) || in_array('list_customer_type', $userRoles) || in_array('list_customer_group', $userRoles))
                        <li @class(['slide has-sub', 'open' => request()->routeIs('customers.*', 'customer-types.*', 'customer-groups.*')])>
                            <a href="javascript:void(0);" @class(['side-menu__item', 'active' => request()->routeIs('customers.*', 'customer-types.*', 'customer-groups.*')])>
                                <i class="fe fe-users" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Cari Yönetimi</span>
                                <i class="fe fe-chevron-right side-menu__angle"></i>
                            </a>
                            <ul class="slide-menu child1">
                                <li class="slide side-menu__label1">
                                    <a href="javascript:void(0)">Cari Yönetimi</a>
                                </li>
                                @if(in_array('*',$userRoles) || in_array('list_customer',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('customers.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('customers.*')])>Cariler</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_customer_type',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('customer-types.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('customer-types.*')])>Cari Türleri</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_customer_group',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('customer-groups.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('customer-groups.*')])>Cari Grupları</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    @if(in_array('*',$userRoles) || in_array('list_stock',$userRoles) || in_array('list_campaign',$userRoles) || in_array('list_stock_unit',$userRoles) || in_array('list_stock_group',$userRoles) || in_array('list_stock_type',$userRoles))
                        <li @class(['slide has-sub', 'open' => request()->routeIs('stocks.*', 'stock.*', 'campaigns.*', 'stock-*')])>
                            <a href="javascript:void(0);" @class(['side-menu__item', 'active' => request()->routeIs('stocks.*', 'stock.*', 'campaigns.*', 'stock-*')])>
                                <i class="fe fe-grid" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Stok Yönetimi</span>
                                <i class="fe fe-chevron-right side-menu__angle"></i>
                            </a>
                            <ul class="slide-menu child1">
                                <li class="slide side-menu__label1">
                                    <a href="javascript:void(0)">Stok Yönetimi</a>
                                </li>
                                @if(in_array('*',$userRoles) || in_array('list_stock',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('stocks.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('stocks.index')])>Stoklar</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_campaign',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('campaigns.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('campaigns.index')])>Kampanyalar</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_stock_unit',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('stock-units.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('stock-units.index')])>Stok Birimleri</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_stock_group',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('stock-groups.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('stock-groups.index')])>Stok Grupları</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_stock_type',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('stock-types.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('stock-types.index')])>Stok Türleri</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif

                    @if(in_array('*',$userRoles) || in_array('list_plus_card',$userRoles))
                        <li @class(['slide has-sub', 'open' => request()->routeIs('plus-cards.*')])>
                            <a href="javascript:void(0);" @class(['side-menu__item', 'active' => request()->routeIs('plus-cards.*')])>
                                <i class="fe fe-credit-card" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Plus Card</span>
                                <i class="fe fe-chevron-right side-menu__angle"></i>
                            </a>
                            <ul class="slide-menu child1">
                                <li class="slide">
                                    <a href="{{ route('plus-cards.index') }}" class="side-menu__item">
                                        Kart Listesi
                                    </a>
                                </li>
                                @if(in_array('*',$userRoles) || in_array('list_plus_card_transaction', $userRoles))
                                <li class="slide">
                                    <a href="{{ route('plus-cards.index') }}" class="side-menu__item">
                                        Kampanya İşlemi
                                    </a>
                                </li>
                                <li class="slide">
                                    <a href="{{ route('plus-card-campaign-usages.index') }}" class="side-menu__item">
                                        Kullanılan Kampanyalar
                                    </a>
                                </li>
                                @endif
                            </ul>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_contract',$userRoles))
                        <li @class(['slide', 'open' => request()->routeIs('plus-cards.*')])>
                            <a  href="{{ route('contracts.index') }}" class="side-menu__item @if(in_array(url()->current(),[route('contracts.index'),route('contracts.create')])) active @endif">
                                <i class="fe fe-file-plus" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Kurumsal Satış</span>
                            </a>

                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_branch',$userRoles))
                        <li @class(['slide has-sub', 'open' => request()->routeIs('branches.*')])>
                            <a href="javascript:void(0);" @class(['side-menu__item', 'active' => request()->routeIs('branches.*')])>
                                <i class="fe fe-copy" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Bayi Yönetimi</span>
                                <i class="fe fe-chevron-right side-menu__angle"></i>
                            </a>
                            <ul class="slide-menu child1">
                                <li class="slide">
                                    <a href="{{ route('branches.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('branches.*')])>Bayi Listesi</a>
                                </li>
                                @if(in_array('*',$userRoles) || in_array('list_branch_category', $userRoles))
                                <li class="slide">
                                    <a href="{{ route('branch-categories.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('branches.*')])>Kategoriler</a>
                                </li>
                                @endif
                            </ul>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_user',$userRoles) || in_array('list_user_group',$userRoles))
                        <li @class(['slide has-sub', 'open' => request()->routeIs('users.*', 'user-role-groups.*')])>
                            <a href="javascript:void(0);" @class(['side-menu__item', 'active' => request()->routeIs('users.*', 'user-role-groups.*')])>
                                <i class="bx bx-user" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Personel Yönetimi</span>
                                <i class="fe fe-chevron-right side-menu__angle"></i>
                            </a>
                            <ul class="slide-menu child1">
                                <li class="slide side-menu__label1">
                                    <a href="javascript:void(0)">Personel Yönetimi</a>
                                </li>
                                @if(in_array('*',$userRoles) || in_array('list_user',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('users.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('users.*')])>Personel Listesi</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_user_group',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('user-role-groups.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('user-role-groups.*')])>Yetki Grupları</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_page_excel_download',$userRoles))
                        <li class="slide @if(in_array(url()->current(),[route('bookings.index')])) open @endif">
                            <a href="{{ route('excelDownloads') }}" class="side-menu__item @if(in_array(url()->current(),[route('excelDownloads')])) active @endif">
                                <i class="fe fe-file" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Oluşturulmuş Excel İndir</span>

                            </a>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_booking',$userRoles))
                        <li class="slide @if(in_array(url()->current(),[route('bookings.index')])) open @endif">
                            <a href="{{ route('bookings.index') }}" class="side-menu__item @if(in_array(url()->current(),[route('bookings.index')])) active @endif">
                                <i class="fe fe-book-open" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Randevular</span>
                            </a>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_ticket',$userRoles))
                        <li class="slide @if(in_array(url()->current(),[route('tickets.index')])) open @endif">
                            <a href="{{ route('tickets.index',['status'=>2]) }}" class="side-menu__item @if(in_array(url()->current(),[route('tickets.index')])) active @endif">
                                <i class="fe fe-clipboard" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Ticket</span>
                            </a>
                        </li>
                    @endif
                    @if(in_array('*', $userRoles) || in_array('list_complaint', $userRoles))
                        <li @class(['slide has-sub', 'open' => request()->routeIs('complaints.*', 'vehicleQueries', 'vehicleQueries.*')])>
                            <a href="javascript:void(0);"
                                @class(['side-menu__item', 'active' => request()->routeIs('complaints.*', 'vehicleQueries', 'vehicleQueries.*')])>
                                <i class="fe fe-alert-triangle" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Müşteri İlişkileri</span>
                                <i class="fe fe-chevron-right side-menu__angle"></i>
                            </a>
                            <ul class="slide-menu child1">
                                <li class="slide">
                                    <a href="{{ route('complaints.index') }}" @class(['side-menu__item', 'active' => request()->routeIs('complaints.index')])>
                                        İstek, Öneri ve Şikayet
                                    </a>
                                </li>
                                @if(in_array('*',$userRoles) || in_array('list_complaint', $userRoles))
                                <li class="slide">
                                    <a href="{{ route('vehicleQueries') }}" @class(['side-menu__item', 'active' => request()->routeIs('vehicleQueries', 'vehicleQueries.*')])>Araç Sorgu</a>
                                </li>
                                @endif
                            </ul>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_bulletin',$userRoles))
                        <li class="slide @if(in_array(url()->current(),[route('bulletin.index')])) open @endif">
                            <a href="{{ route('bulletin.index') }}" class="side-menu__item @if(in_array(url()->current(),[route('bulletin.index')])) active @endif">
                                <i class="fe fe-bell" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Bültenler</span>
                            </a>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_customer_feedback',$userRoles))
                        <li class="slide @if(in_array(url()->current(),[route('complaints.index')])) open @endif">
                            <a href="{{ route('customer-feedbacks.index') }}" class="side-menu__item @if(in_array(url()->current(),[route('customer-feedbacks.index')])) active @endif">
                                <i class="fe fe-camera" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Çek Gönder</span>
                            </a>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_zone',$userRoles))
                        <li class="slide @if(in_array(url()->current(),[route('zones.index')])) open @endif">
                            <a href="{{ route('zones.index') }}" class="side-menu__item @if(in_array(url()->current(),[route('zones.index')])) active @endif">
                                <i class="fe fe-map" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Bölgeler</span>
                            </a>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_pool_question',$userRoles))
                        <li class="slide">
                            <a href="{{ route('pool-questions.index') }}" class="side-menu__item @if(url()->current() == route('pool-questions.index')) active @endif">
                                <i class="fe fe-check-square" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Anket Soruları</span>
                            </a>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_log',$userRoles))
                        <li class="slide">
                            <a href="{{ route('logs.index') }}" class="side-menu__item @if(url()->current() == route('logs.index')) active @endif">
                                <i class="bx bx-food-menu" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Log Kayıtları</span>
                            </a>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_telescope',$userRoles))
                        <li class="slide">
                            <a href="/telescope" target="_blank" class="side-menu__item">
                                <i class="fe fe-zoom-in" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Telescope</span>
                            </a>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_settings',$userRoles))
                        <li class="slide">
                            <a href="{{ route('settings.edit',\App\Models\Setting::first()) }}" class="side-menu__item @if(url()->current() == route('settings.edit',\App\Models\Setting::first())) active @endif">
                                <i class="ri-settings-line" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Ayarlar</span>
                            </a>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_invoice_definition',$userRoles))
                        <li class="slide has-sub @if(in_array(url()->current(),[route('invoice-definitions.index')])) open @endif">
                            <a href="#" class="side-menu__item">
                                <i class="bx bxs-inbox" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Tanımlamalar</span>
                                <i class="fe fe-chevron-right side-menu__angle"></i>
                            </a>
                            <ul class="slide-menu child1">
                                <li class="slide side-menu__label1">
                                    <a href="javascript:void(0)">Tanımlamalar</a>
                                </li>
                                @if(in_array('*',$userRoles) || in_array('list_invoice_definition',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('invoice-definitions.index') }}" class="side-menu__item">Fatura Tanımları</a>
                                    </li>
                                @endif
                                @if(in_array('*',$userRoles) || in_array('list_plus_cards_definition',$userRoles))
                                    <li class="slide">
                                        <a href="{{ route('plus-cards-definitions.index') }}" class="side-menu__item">Plus Card Tanımları</a>
                                    </li>
                                @endif
                            </ul>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_report',$userRoles))
                        <li class="slide has-sub @if(in_array(url()->current(),[route('reports',['type'=>'expertise']),route('reports',['type'=>'stock']),route('reports',['type'=>'customer']),route('reports',['type'=>'branch']),route('reports',['type'=>'car']),route('reports',['type'=>'query']),route('reports',['type'=>'user'])])) open @endif">
                            <a href="#" class="side-menu__item @if(in_array(url()->current(),[route('reports',['type'=>'expertise']),route('reports',['type'=>'stock']),route('reports',['type'=>'customer']),route('reports',['type'=>'branch']),route('reports',['type'=>'car']),route('reports',['type'=>'query']),route('reports',['type'=>'user'])])) active @endif">
                                <i class="bx bx-bar-chart" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Raporlar</span>
                                <i class="fe fe-chevron-right side-menu__angle"></i>
                            </a>
                            <ul class="slide-menu child1">
                                <li class="slide side-menu__label1">
                                    <a href="javascript:void(0)">Raporlar</a>
                                </li>
                                <li class="slide">
                                    <a href="{{ route('reports',['type'=>'expertise']) }}" class="side-menu__item">Ekspertiz</a>
                                </li>
                                <li class="slide">
                                    <a href="{{ route('reports',['type'=>'stock']) }}" class="side-menu__item">Stok</a>
                                </li>
                                <li class="slide">
                                    <a href="{{ route('reports',['type'=>'customer']) }}" class="side-menu__item">Cari</a>
                                </li>
                                <li class="slide">
                                    <a href="{{ route('reports',['type'=>'branch']) }}" class="side-menu__item">Bayi</a>
                                </li>
                                <li class="slide">
                                    <a href="{{ route('reports',['type'=>'car']) }}" class="side-menu__item">Araç</a>
                                </li>
                                <li class="slide">
                                    <a href="{{ route('reports',['type'=>'user']) }}" class="side-menu__item">Personel</a>
                                </li>
                                <li class="slide">
                                    <a href="{{ route('reports',['type'=>'endofdays']) }}" class="side-menu__item">Gün Sonu</a>
                                </li>
                                <li class="slide">
                                    <a href="{{ route('reports',['type'=>'query']) }}" class="side-menu__item">Sorgu</a>
                                </li>
                            </ul>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_slider',$userRoles))
                        <li class="slide @if(in_array(url()->current(),[route('sliders.index')])) open @endif">
                            <a href="{{ route('sliders.index') }}" class="side-menu__item @if(in_array(url()->current(),[route('sliders.index')])) active @endif">
                                <i class="fe fe-image" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Slider</span>
                            </a>
                        </li>
                    @endif
                    @if(in_array('*',$userRoles) || in_array('list_menu',$userRoles))
                        <li class="slide">
                            <a href="{{ route('menus.index') }}" class="side-menu__item @if(url()->current() == route('menus.index')) active @endif">
                                <i class="bx bx-menu" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Menü Başlıkları</span>
                            </a>
                        </li>
                    @endif
                @else
                    <li class="slide @if(in_array(url()->current(),[route('customer.cars')])) open @endif">
                        <a href="{{ route('customer.cars') }}" class="side-menu__item @if(in_array(url()->current(),[route('customer.cars')])) active @endif">
                            <i class="ri-file-text-line" style="font-size: 1.3rem"></i>
                            <span class="side-menu__label">Raporlarım</span>
                        </a>
                    </li>
                    <li class="slide">
                        <a href="{{ route('customer.query') }}" class="side-menu__item @if(in_array(url()->current(),[route('customer.query')])) active @endif">
                            <i class="ri-car-line" style="font-size: 1.3rem"></i>
                            <span class="side-menu__label">Araç Sorgulama</span>
                        </a>
                    </li>
                    <li class="slide">
                        <a href="{{ route('customer.my_query') }}" class="side-menu__item @if(in_array(url()->current(),[route('customer.my_query')])) active @endif">
                            <i class="ri-car-line" style="font-size: 1.3rem"></i>
                            <span class="side-menu__label">Araçlarım</span>
                        </a>
                    </li>
                    <li class="slide">
                        <a href="{{ route('customer.plusCard') }}" class="side-menu__item @if(in_array(url()->current(),[route('customer.plusCard')])) active @endif">
                            <i class="ri-bank-card-line" style="font-size: 1.3rem"></i>
                            <span class="side-menu__label">Plus Card</span>
                        </a>
                    </li>
                    <li class="slide">
                        <a href="{{ route('customer.booking') }}" class="side-menu__item @if(in_array(url()->current(),[route('customer.booking')])) active @endif">
                            <i class="ri-calendar-line" style="font-size: 1.3rem"></i>
                            <span class="side-menu__label">Randevu İşlemleri</span>
                        </a>
                    </li>
                    {{--<li class="slide">
                        <a href="{{ route('customer.remoteExpertise') }}" class="side-menu__item" @if(in_array(url()->current(),[route('customer.remoteExpertise')])) active @endif>
                            <i class="ri-calendar-line" style="font-size: 1.3rem"></i>
                            <span class="side-menu__label">Uzaktan Ekspertiz</span>
                        </a>
                    </li>--}}
                    <li class="slide @if(in_array(url()->current(),[route('complaints.index')])) open @endif">
                        <a href="{{ route('complaints.index') }}" class="side-menu__item @if(in_array(url()->current(),[route('complaints.index')])) active @endif">
                            <i class="ri-emotion-happy-line" style="font-size: 1.3rem"></i>
                            <span class="side-menu__label">Çözüm Merkezi</span>
                        </a>
                    </li>
                    <li class="slide">
                        <a href="#" class="side-menu__item">
                            <i class="ri-home-smile-line" style="font-size: 1.3rem"></i>
                            <span class="side-menu__label">Bayilerimiz</span>
                        </a>
                    </li>
                    <li class="slide">
                        <a href="{{ route('kvkkOnay') }}" class="side-menu__item @if(url()->current() == route('kvkkOnay')) active @endif">
                            <i class="ri-shield-check-line" style="font-size: 1.3rem"></i>
                            <span class="side-menu__label">Gizlilik & KVKK</span>
                        </a>
                    </li>
                    <li class="slide">
                        <a href="{{ route('customer.profile') }}" class="side-menu__item @if(in_array(url()->current(),[route('customer.profile')])) active @endif">
                            <i class="ri-user-6-line" style="font-size: 1.3rem"></i>
                            <span class="side-menu__label">Profilim</span>
                        </a>
                    </li>
                        <li class="slide">
                            <a href="{{ route('customer-feedbacks.create') }}" class="side-menu__item @if(in_array(url()->current(),[route('customer-feedbacks.create')])) active @endif">
                                <i class="fa fa-camera" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Çek Gönder</span>
                            </a>
                        </li>
                        <li class="slide">
                            <a href="{{ route('customer.contracts.index') }}" class="side-menu__item @if(in_array(url()->current(),[route('customer.contracts.index')])) active @endif">
                                <i class="fa fa-file-alt" style="font-size: 1.3rem"></i>
                                <span class="side-menu__label">Kurumsal Satış</span>
                            </a>
                        </li>
                @endif
                <li class="slide">
                    <form id="logoutForm" method="post" action="{{ route('logout') }}">@csrf</form>
                    <a href="#" onclick="document.querySelector('#logoutForm').submit()" class="side-menu__item">
                        <i class="ri-logout-box-line" style="font-size: 1.3rem"></i>
                        <span class="side-menu__label">Çıkış Yap</span>
                    </a>
                </li>
            </ul>
            <div class="slide-right" id="slide-right"><svg xmlns="http://www.w3.org/2000/svg" fill="#7b8191" width="24" height="24" viewBox="0 0 24 24"> <path d="M10.707 17.707 16.414 12l-5.707-5.707-1.414 1.414L13.586 12l-4.293 4.293z"></path> </svg></div>
        </nav>
        <!-- End::nav -->
    </div>
    <!-- End::main-sidebar -->
</aside>

