<!-- Popper JS -->
<script src="/assets/libs/@popperjs/core/umd/popper.min.js"></script>

<!-- Bootstrap JS -->
<script src="/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>

<!-- Defaultmenu JS -->
<script src="/assets/js/defaultmenu.min.js"></script>

<!-- Node Waves JS-->
<script src="/assets/libs/node-waves/waves.min.js"></script>

<!-- Sticky JS -->
<script src="/assets/js/sticky.js"></script>

<!-- Simplebar JS -->
<script src="/assets/libs/simplebar/simplebar.min.js"></script>
<script src="/assets/js/simplebar.js"></script>

<!-- Color Picker JS -->
<script src="/assets/libs/@simonwep/pickr/pickr.es5.min.js"></script>
@if(auth()->check() && $authUser->session_lifetime > 0)
{{--    <script>--}}
{{--        setTimeout(function (){--}}
{{--            window.location.reload()--}}
{{--        },(1000 * {{ $authUser->session_lifetime }}) + 1000)--}}
{{--    </script>--}}
@endif



<!-- Custom JS -->
{{--<script src="/assets/js/custom.js"></script>--}}
<script src="/assets/js/imask.js"></script>
<script src="/assets/js/<EMAIL>"></script>
<script>
    @if(session('success'))
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
        Toast.fire({
            icon: "success",
            title: "{{ session('success') }}"
        });
    @endif
    @if(session('error'))
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
        Toast.fire({
            icon: "error",
            title: "{{ session('error') }}"
        });
    @endif

    function checkBeforeDelete(formID){
        Swal.fire({
            title: "Emin misiniz?",
            text: "Veri Silinecek",
            icon: "error",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: "Evet, Sil!",
            cancelButtonText: "Hayır, Silme!"
        }).then((result) => {
            if (result.isConfirmed) {
                document.querySelector('#'+formID).submit()
            }
        });
    }

    if(document.querySelector('.mask-tel')){
        document.querySelectorAll('.mask-tel').forEach(function (item,index){
            const maskTel = IMask(item, {
                mask: '(0*************'
            });
        })
    }

    if(document.querySelector('input[name="telephone"]')){
        document.querySelectorAll('input[name="telephone"]').forEach(function (item,index){
            const maskTel = IMask(item, {
                mask: '(0*************'
            });
        })
    }
    if(document.querySelector('input[name="customer_telephone"]')){
        document.querySelectorAll('input[name="customer_telephone"]').forEach(function (item,index){
            const maskTel = IMask(item, {
                mask: '(0*************'
            });
        })
    }

    if(document.querySelector('input[name="telephone[]"]')){
        document.querySelectorAll('input[name="telephone[]"]').forEach(function (item,index){
            const maskTel2 = IMask(item, {
                mask: '(0*************'
            });
        })
    }

    if(document.querySelector('.tel-mask')){
        document.querySelectorAll('.tel-mask').forEach(function (item,index){
            const maskTel5 = IMask(item, {
                mask: '(*************'
            });
        })
    }

    if(document.querySelector('input[name="telefon"]')){
        document.querySelectorAll('input[name="telefon"]').forEach(function (item,index){
            const maskTel3 = IMask(item, {
                mask: '(0*************',
            });
        })
    }
    if(document.querySelector('input[name="mersis_no"]')){
        document.querySelectorAll('input[name="mersis_no"]').forEach(function (item,index){
            const maskTel3 = IMask(item, {
                mask: '0000000000000000',
            });
        })
    }

    if(document.querySelector('input[name="gsm"]')){
        document.querySelectorAll('input[name="gsm"]').forEach(function (item,index){
            const maskGsm = IMask(item, {
                mask: '(*************'
            });
        })
    }

    if(document.querySelector('input[name="telephones[]"]')){
        document.querySelectorAll('input[name="telephones[]"]').forEach(function (item,index){
            const maskTel3 = IMask(item, {
                mask: '(0*************'
            });
        })
    }


    if(document.querySelector('input[name="cep"]')){
        document.querySelectorAll('input[name="cep"]').forEach(function (item,index){
            const maskCep = IMask(item, {
                mask: '(0*************'
            });
        })
    }

    if(document.querySelector('input[name="fax"]')){
        document.querySelectorAll('input[name="fax"]').forEach(function (item,index){
            const maskFax = IMask(item, {
                mask: '(*************'
            });
        })

    }

    if(document.querySelector('input[name="vergi_no"]')){
        document.querySelectorAll('input[name="vergi_no"]').forEach(function (item,index){
            const maskVergiNo = IMask(item, {
                mask: '0000000000'
            });
        })
    }

    if(document.querySelector('input[name="tc_no"]')){
        document.querySelectorAll('input[name="tc_no"]').forEach(function (item,index){
            const maskTcNo = IMask(item, {
                mask: '00000000000'
            });
        })
    }

    if(document.querySelector('input[name="tc"]')){
        document.querySelectorAll('input[name="tc"]').forEach(function (item,index){
            const maskTcNo2 = IMask(item, {
                mask: '00000000000'
            });
        })
    }

    document.querySelector(".sidemenu-toggle").addEventListener('click',function (){
        if (localStorage.getItem("nowaverticalstyles") == "overlay"){
            localStorage.setItem("nowaverticalstyles", 'default');
        }else{
            localStorage.setItem("nowaverticalstyles", 'overlay');
        }
    })


    document.querySelectorAll('input[type="date"]').forEach(function (item,index){
        item.addEventListener('click', function (event) {
            item.focus();
            item.select();
        });
    })


    // document.addEventListener("DOMContentLoaded", (event) => {
    //     document.querySelector('.sidemenu-toggle').click()
    // });

    // if(document.querySelector('input[name="sase_no"]')){
    //     const maskSaseNo = IMask(document.querySelector('input[name="sase_no"]'), {
    //         mask: '00000000000000000'
    //     });
    // }
</script>
@stack('js')
