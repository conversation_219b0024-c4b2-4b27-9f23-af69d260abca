<header id="topnav">
    <div class="topbar-main">
        <div class="container-fluid">
            <!-- Logo container-->
            <div class="logo">
                <a href="https://umranoto.com/" class="logo">
                    <img src="/online_files/assets/images/logo-sm.png" alt="Umran Oto Ekspertiz Online İşlem Merkezi" class="logo-small" />
                    <img src="/online_files/assets/images/logo.png" alt="Umran Oto Ekspertiz Online İşlem Merkezi" class="logo-large" />
                </a>
            </div>
            <!-- End Logo container-->

            <div class="menu-extras topbar-custom">
                <ul class="float-right list-unstyled mb-0">
                    <li class="dropdown notification-list d-none d-sm-block tlg-menu-item">
                        <form role="search" class="app-search">
                            <div class="form-group mb-0">
                                <a href="https://umranoto.com/" class="btn btn-block btn-primary btn-block">ANA SİTEYE DÖN</a>
                            </div>
                        </form>
                    </li>
                    <li class="dropdown notification-list d-none d-sm-block tlg-menu-item">
                        <form role="search" class="app-search">
                            <div class="form-group mb-0">
                                <a href="{{ route('online.bookingNew') }}" class="btn btn-block btn-info btn-block">RANDEVU AL</a>
                            </div>
                        </form>
                    </li>
                    @if(auth('customer')->check())
                        <li class="dropdown notification-list d-none d-sm-block">
                            <form role="search" class="app-search">
                                <div class="form-group mb-0">
                                    <a href="#" onclick="document.querySelector('#logoutForm').submit()" class="btn btn-block btn-warning btn-block">Çıkış Yap</a>
                                </div>
                            </form>
                        </li>
                    @else
                        <li class="dropdown notification-list d-none d-sm-block">
                            <form role="search" class="app-search">
                                <div class="form-group mb-0">
                                    <a href="{{ route('online.login') }}" class="btn btn-block btn-warning btn-block">GİRİŞ YAP</a>
                                </div>
                            </form>
                        </li>
                    @endif

                    <li class="menu-item">
                        <!-- Mobile menu toggle-->
                        <a class="navbar-toggle nav-link" id="mobileToggle">
                            <div class="lines">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </a>
                        <!-- End mobile menu toggle-->
                    </li>
                </ul>
            </div>
            <!-- end menu-extras -->

            <div class="clearfix"></div>
        </div>
        <!-- end container -->
    </div>
    <!-- end topbar-main -->
    <div class="container-fluid">
        <!-- Page-Title -->
        <div class="row">
            <div class="col-sm-12">
                <div class="page-title-box">
                    <h4 class="page-title">@yield('title','Umran Oto Online İşlem Merkezi')</h4>
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item active">
                            Umran Oto Online İşlem Merkezi
                        </li>
                    </ol>
                    @if(auth('customer')->check())
                        <div class="state-information">
                            <div class="state-graph">
                                <div class="info">Kod: {{ auth('customer')->user()->cari_kod }}</div>
                                {{--                                <div class="info">Plus Card: 0</div>--}}
                            </div>
                            <div class="state-graph">
                                <div class="info">Plus Card: {{ (auth('customer')->user()->getPlusCard?->getTotalBalance()['credits'] ?? 0) }}</div>
{{--                                <div class="info">Plus Card: 0</div>--}}
                            </div>
                            <div class="state-graph">
                                <div class="info">Toplam Sorgulama: {{ auth('customer')->user()->getQueries->count() }}</div>
                            </div>
                            <div class="state-graph">
                                <div class="info">Ücretsiz Sorgu: 0</div>
                            </div>
                            <div class="state-graph">
                                <div class="info">Satın Alınan Rapor: {{ \App\Models\CustomerBuyExpertise::where('customer_id',auth('customer')->id())->where('status',1)->count() }}</div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <!-- MENU Start -->
    <div class="navbar-custom">
        <div class="container-fluid">
            <div id="navigation">
                <!-- Navigation Menu-->
                <ul id="Menu__" class="navigation-menu">
                    <li class="has-submenu @if(in_array(url()->current(),[route('online.index')])) active @endif">
                        <a href="{{ route('online.index') }}">
                            <i class="ti-home"></i>
                            <span>Anasayfa</span>
                        </a>
                    </li>
                    @if(auth('customer')->check())
                        <li class="has-submenu @if(in_array(url()->current(),[route('online.reports',['type'=>'alici']),route('online.reports',['type'=>'satici']),route('online.reports',['type'=>'buy'])])) active @endif">
                            <a href="#"><i class="ti-receipt"></i>Raporlarım</a>
                            <ul class="submenu">
                                <li><a href="{{ route('online.reports',['type'=>'alici']) }}">Alıcısı Olduğum Araçlar</a></li>
                                <li><a href="{{ route('online.reports',['type'=>'satici']) }}">Satıcısı Olduğum Araçlar</a></li>
                                <li><a href="{{ route('online.reports',['type'=>'buy']) }}">Satın Aldığım Raporlar</a></li>
                            </ul>
                        </li>
                        <li class="has-submenu @if(in_array(url()->current(),[route('online.plusCardSummary'),route('online.plusCardBuy')])) active @endif">
                            <a href="#"><i class="ti-credit-card"></i>Plus Card</a>
                            <ul class="submenu">
                                <li><a href="{{ route('online.plusCardSummary') }}">Hesap Özeti</a></li>
                                <li><a href="{{ route('online.plusCardBuy') }}">Yükleme Yap</a></li>
                            </ul>
                        </li>
                        <li class="has-submenu @if(in_array(url()->current(),[route('online.bookingNew'),route('online.bookingIndex')])) active @endif">
                            <a href="#"><i class="ti-calendar"></i>Randevu İşlemleri</a>
                            <ul class="submenu">
                                <li><a href="{{ route('online.bookingNew') }}">Randevu Al</a></li>
                                <li><a href="{{ route('online.bookingIndex') }}">Randevularım</a></li>
                            </ul>
                        </li>
                        <li class="has-submenu @if(in_array(url()->current(),[route('online.remoteNew'),route('online.remoteIndex')])) active @endif">
                            <a href="#"><i class="ti-car"></i>Uzaktan Ekspertiz</a>
                            <ul class="submenu">
{{--                                <li><a href="{{ route('online.remoteNew') }}">Kayıt Oluştur</a></li>--}}
                                <li><a href="#">Kayıt Oluştur</a></li>
                                <li><a href="{{ route('online.remoteIndex') }}">Kayıtlarım</a></li>
                            </ul>
                        </li>
                        <li class="has-submenu last-elements @if(in_array(url()->current(),[route('online.profile')])) active @endif">
                            <a href="{{ route('online.profile') }}">
                                <i class="ti-user"></i>
                                <span>Profilim</span>
                            </a>
                        </li>
                        <li class="has-submenu last-elements @if(in_array(url()->current(),[route('online.contracts')])) active @endif">
                            <a href="{{ route('online.contracts') }}">
                                <i class="ti-file"></i>
                                <span>Sözleşmelerim</span>
                            </a>
                        </li>
                        <li class="has-submenu last-elements">
                            <a href="#" onclick="document.querySelector('#logoutForm').submit()">
                                <i class="ti-lock"></i>
                                <span>Çıkış Yap</span>
                            </a>
                        </li>
                        <form id="logoutForm" method="post" action="{{ route('online.logout') }}">@csrf</form>
                    @else
                        <li class="has-submenu @if(in_array(url()->current(),[route('online.bookingNew')])) active @endif">
                            <a href="{{ route('online.bookingNew') }}">
                                <i class="ti-calendar"></i>
                                <span>Randevu Al</span>
                            </a>
                        </li>
                        <li class="has-submenu">
                            <a href="{{ route('online.login') }}">
                                <i class="ti-user"></i>
                                <span>Giriş Yap</span>
                            </a>
                        </li>
                    @endif
                </ul>
            </div>
        </div>
    </div>
</header>
