<script src="/online_files/assets/js/jquery.min.js"></script>
<script src="/online_files/assets/js/bootstrap.bundle.min.js"></script>
<script src="/online_files/assets/js/jquery.slimscroll.js"></script>
<script src="/online_files/assets/js/waves.min.js"></script>
<script type="text/javascript">
    console.stdlog = console.log.bind(console);
    console.logs = [];
    console.log = function () {
        console.logs.push(Array.from(arguments));
        console.stdlog.apply(console, arguments);
    };
    var AnaURL = "index.html";
    var QueryString = [];
    var Now = "index.html";

    var Login = false;
    var MobileApp = false;
</script>
<script src="/online_files/assets/js/app5e1f.js?v=2"></script>
<script src="/online_files/assets/AjaxForm3860.js?v=1"></script>
<script src="/online_files/assets/jquery.formatter.min3860.js?v=1"></script>
<script src="/online_files/assets/Mask3860.js?v=1"></script>

<script src="/online_files/assets/js/tulgar30f4.js?v=3"></script>

<script src="/online_files/assets/js/Sorgula.js"></script>
<script src="/online_files/assets/js/Giris-Yap.js"></script>
<script src="/online_files/assets/plugins/raphael/raphael-min.js"></script>
<script src="/online_files/assets/pages/dashboard.js"></script>
<script src="/online_files/assets/plugins/morris/morris.min.js"></script>
<script src="/assets/js/<EMAIL>"></script>
<script src="/assets/js/imask.js"></script>
<script>
    @if(session('success'))
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });
    Toast.fire({
        icon: "success",
        title: "{{ session('success') }}"
    });
    @endif
    @if(session('error'))
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });
    Toast.fire({
        icon: "error",
        title: "{{ session('error') }}"
    });
    @endif

    if(document.querySelector('input[name="telephone"]')){
        document.querySelectorAll('input[name="telephone"]').forEach(function (item,index){
            const maskTel = IMask(item, {
                mask: '(0000) 000 0000'
            });
        })
    }
</script>
@stack('js')
