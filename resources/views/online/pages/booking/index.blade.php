@extends('online.pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('content')
    <div class="wrapper">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <table id="datatable" class="table table-bordered dt-responsive nowrap dataTable no-footer dtr-inline" style="border-collapse: collapse; border-spacing: 0px; width: 100%;">
                                        <thead>
                                        <tr role="row">
                                            <th><PERSON><PERSON></th>
                                            <th><PERSON><PERSON><PERSON></th>
                                            <th><PERSON><PERSON><PERSON></th>
                                            <th><PERSON>luş<PERSON><PERSON><PERSON></th>
                                            <th>Şube</th>
                                            <th>Detay</th>
                                            <th>İptal</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($bookings->reverse() as $item)
                                            @php
                                                $appointmentDateTime = \Carbon\Carbon::make($item->date . ' ' . $item->hour);
                                                $currentDateTime = \Carbon\Carbon::now();
                                            @endphp
                                            <tr>
                                                <td>{{ $item->plaka }}/{{ $item->sase_no }}</td>
                                                <td>{{ \Carbon\Carbon::make($item->date)->format('d.m.Y') }}</td>
                                                <td>{{ \Carbon\Carbon::make($item->date)->format('H:i') }}</td>
                                                <td>{{ $item->created_at->format('d.m.Y') }}</td>
                                                <td>{{ $item->getBranch?->kisa_ad }}</td>
                                                <td></td>
                                                <td>
                                                    @if($item->status == 0)
                                                        <button disabled class="btn btn-sm btn-danger">İptal Edildi</button>
                                                    @else
                                                        <form method="post" action="{{ route('online.deleteBooking') }}">@csrf
                                                            <button
                                                                    onclick="if (!confirm('Randevuyu iptal etmek istediğinize emin misiniz?')){return false;}"
                                                                    name="booking_id"
                                                                    value="{{ encrypt($item->id) }}"
                                                                    class="btn btn-danger btn-sm" {{ $appointmentDateTime < $currentDateTime ? 'disabled' : '' }}>
                                                                İptal Et
                                                            </button>
                                                        </form>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script>
        $(function (e) {
            'use strict';
            $('#datatable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });
        });
    </script>
@endpush
