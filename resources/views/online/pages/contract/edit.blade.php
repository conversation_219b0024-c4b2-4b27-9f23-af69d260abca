@extends('online.pages.build')
@section('title')
    Sözleşme Detay - @if($contract->type == 'sabit') Sabit @elseif($contract->type == 'hedefli') Hedefli @else Hakediş @endif Sözleşme
@endsection
@push('css')
    <style>
        .month-box{
            width: 100%;
            border: 1px solid;
            border-radius: 10px;
            padding: 1rem;
        }
    </style>
@endpush
@section('content')
    <div class="wrapper">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-5">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label">Cari</label>
                                        <input type="text" class="form-control form-control-sm" value="{{ $contract->getCustomer->fullName }}" disabled>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="form-group">
                                        <label class="form-label">Sözleşme No</label>
                                        <input type="text" class="form-control form-control-sm" value="{{ $contract->no }}" disabled>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Sözleşme Başlangıç Tarihi</label>
                                        <input type="date" class="form-control form-control-sm" disabled value="{{ \Carbon\Carbon::make($contract->start_date)->format('Y-m-d') }}">
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="form-group">
                                        <label class="form-label">Sözleşme Süresi (Ay)</label>
                                        <input type="number" min="1" value="{{ $contract->duration_month }}" disabled id="contractDuration" name="duration_month" required class="form-control-sm form-control">
                                    </div>
                                </div>
                                <!-- <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Ödeme Tipi</label>
                                        <select class="form-control-sm form-control" disabled>
                                            <option value="veresiye">Veresiye</option>
                                        </select>
                                    </div>
                                </div> -->
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body">
                            <div class="row" id="filesContainer">
                                @foreach($contract->getFiles as $file)
                                    <div class="col">
                                        <a href="https://umram.online/storage/{{ $file->file }}" target="_blank" class="btn btn-danger btn-sm">{{ $loop->index + 1 }}. Sözleşme Görüntüle</a>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-7">
                    <div class="card">
                        <div class="card-body">
                            <div id="contractContainer">
                                @foreach($contract->getPeriods as $period)
                                    <div class="row">
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="form-label">Dönem Baş. Tarihi</label>
                                                <input type="date" class="form-control form-control-sm" disabled name="period_start_date[{{ $period->id }}]" value="{{ $period->start_date }}">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="form-label">Dönem Bit. Tarihi</label>
                                                <input type="date" class="form-control form-control-sm" disabled name="period_end_date[{{ $period->id }}]" value="{{ $period->end_date }}">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="form-label">Hesap Kesim</label>
                                                <input type="date" class="form-control form-control-sm" disabled name="period_last_payment_date[{{ $period->id }}]" value="{{ $period->last_payment_date }}">
                                            </div>
                                        </div>
                                        <div class="col-md-3 d-none">
                                            <div class="form-group">
                                                <label class="form-label">{{ $period->payment_type == 'pesin' ? 'Ödeme Tutarı' : 'Risk Tutarı' }}</label>
                                                <input type="text" class="form-control form-control-sm" disabled name="period_payment_amount[{{ $period->id }}]" value="{{ $period->payment_amount }}">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label class="form-label">Ödeme Alındı mı?</label>
                                                <select class="form-control-sm form-control" disabled name="period_payment_completed[{{ $period->id }}]">
                                                    <option value="0">Hayır</option>
                                                    <option @if($period->payment_completed == 1) selected @endif value="1">Evet</option>
                                                </select>
                                            </div>
                                        </div>
                                        @if($contract->type == 'hakedis')
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Aylık Limit Tipi</label>
                                                    <select class="form-control-sm form-control" disabled name="period_limit_type[{{ $period->id }}]">
                                                        <option @if($period->limit_type == 'count') selected @endif value="count">Ekspertiz Adeti</option>
                                                        <option @if($period->limit_type == 'amount') selected @endif value="amount">Ekspertiz Tutarı</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Aylık Limit</label>
                                                    <input type="text" class="form-control @if($period->limit_type == 'amount') turk-lirasi @endif form-control-sm" value="{{ $period->limit_value - $period->getAddeds->sum('amount') }}" disabled name="period_limit_value[{{ $period->id }}]">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Takviye Limit</label>
                                                    <input type="text" class="form-control @if($period->limit_type == 'amount') turk-lirasi @endif form-control-sm" value="{{ $period->getAddeds->sum('amount') }}" disabled name="period_limit_value[{{ $period->id }}]">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Kalan Limit</label>
                                                    <input type="text" class="form-control @if($period->limit_type == 'amount') turk-lirasi @endif form-control-sm" value="{{ $period->limit_value -  ($period->limit_type == 'amount' ? $contractPayments->whereBetween('created_at',[$period->start_date,$period->end_date])->sum('amount') : $contractPayments->whereBetween('created_at',[$period->start_date,$period->end_date])->count()) }}" disabled name="period_limit_value[{{ $period->id }}]">
                                                </div>
                                            </div>
                                            @if($period->getAddeds)
                                                <div class="col-12 mb-3">
                                                    <div class="table-responsive">
                                                        <table class="table table-striped">
                                                            <thead>
                                                            <tr>
                                                                <td><b>Limit Eklenen Tutar</b></td>
                                                                <td><b>Limit Ekleyen Kişi</b></td>
                                                                <td><b>Tarih</b></td>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            @foreach($period->getAddeds->reverse() as $added)
                                                                <tr>
                                                                    <td><input style="width: 140px" class="form-control-sm @if($period->limit_type == 'amount') turk-lirasi @endif form-control" disabled value="{{ $added->amount }}"></td>
                                                                    <td>{{ $added->name . ' ' . $added->second_name . ' ' . $added->surname  }}</td>
                                                                    <td>{{ $added->created_at->translatedFormat('d F Y H:i') }}</td>
                                                                </tr>
                                                            @endforeach
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            @endif
                                        @elseif($contract->type != 'sabit')
                                            <div class="col-12 mt-2">
                                                <div class="table-responsive">
                                                    <table class="table table-striped table-hover">
                                                        <thead>
                                                        <tr>
                                                            <th>Hedef Miktarı</th>
                                                            <th>Birim Fiyatı</th>
                                                            <th>Hedef Durum</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>
                                                        @php
                                                            $count = $contract->getTargetCount($period->id);
                                                            $totalCount = 0;
                                                            $unitPrice = 0;
							                                $targets = $period->getTargets->sortBy('min_count');
                                                        @endphp
                                                        @foreach($targets as $target)
                                                            @php
                                                                $totalCount += $count;
                                                                if ($count >= $target->min_count){
                                                                    $unitPrice = $target->unit_price;
                                                                }
                                                            @endphp
                                                            <tr>
                                                                <td>{{ $target->min_count }}</td>
                                                                <td>{{ $target->unit_price }}₺</td>
                                                                <td>{!! $count >= $target->min_count ? '<i class="fa fa-check"></i>' : '-' !!}</td>
                                                            </tr>
                                                        @endforeach
                                                        </tbody>
                                                        <tfoot>
                                                        <tr>
                                                            <td></td>
                                                            <td></td>
                                                            <td>Toplam Tutar : {{ $unitPrice * $count }}₺</td>
                                                        </tr>
                                                        <tr>
                                                            <td></td>
                                                            <td></td>
                                                            <td>Yapılan Adet : {{ $count }}</td>
                                                        </tr>
                                                        @if(\App\Models\ContractTarget::where('contract_id',$contract->id)->where('contract_period_id',$period->id)->onlyTrashed()->exists())
                                                            <tr class="text-center">
                                                                <th colspan="4">Fiyat Geçmişi</th>
                                                            </tr>
                                                            <tr>
                                                                <th>Hedef Miktarı</th>
                                                                <th>Birim Fiyatı(₺)</th>
                                                                <th>Fiyat Geçerlilik Tarihi</th>
                                                            </tr>
                                                            @forelse(\App\Models\ContractTarget::where('contract_id',$contract->id)->where('contract_period_id',$period->id)->onlyTrashed()->get() as $deletedPrices)
                                                                <tr>
                                                                    <td>{{ $deletedPrices->min_count }}</td>
                                                                    <td>{{ $deletedPrices->unit_price }}₺</td>
                                                                    <td>{{ $deletedPrices->created_at->translatedFormat('d F Y H:i') }} - {{ $deletedPrices->deleted_at->translatedFormat('d F Y H:i') }}</td>
                                                                </tr>
                                                            @empty
                                                            @endforelse
                                                        @endif
                                                        </tfoot>
                                                    </table>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                    @if($contract->type == 'hakedis')
                                        <div class="table-responsive">
                                            <h5>Özet</h5>
                                            <table class="table table-striped">
                                                <thead>
                                                <tr>
                                                    <th>Hizmet</th>
                                                    <th>Yapılan Adet</th>
                                                    <th>Toplam Tutar</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                @foreach($contract->getStockIds->groupBy('stock_id') as $stockID => $stockPrices)
                                                    @php
                                                        $periodStockExpertises = \App\Models\Expertise::whereHas('getStockhasOne',function ($getStockhasOne) use ($stockID){
                                                            return $getStockhasOne->where('stock_id',$stockID);
                                                        })
                                                        ->whereHas('getPayment',function ($getPayment) use ($contract,$contractCodes){
                                                            return $getPayment->where('payment_code',$contract->no)
                                                            ->whereIn('payment_detail',$contractCodes);
                                                        })
                                                        ->where('status',1)
                                                        ->whereBetween('created_at',[$period->start_date,$period->end_date])
                                                        ->get();

                                                        $periodExpertisePayments = \App\Models\ExpertisePayment::whereIn('expertise_id',$periodStockExpertises->pluck('id')->toArray())
                                                        ->where('payment_code',$contract->no)
                                                        ->whereIn('payment_detail',$contractCodes)
                                                        ->sum('amount');
                                                    @endphp
                                                    <tr>
                                                        <th>{{ \App\Models\Stock::find($stockID)->first()?->ad }}</th>
                                                        <th>{{ $periodStockExpertises->count() }}</th>
                                                        <th>{{ $periodExpertisePayments }}₺</th>
                                                    </tr>
                                                @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @endif
                                    <hr style="border-top: 2px solid rgb(255 0 0)">
                                @endforeach
                            </div>
                        </div>
                    </div>
                    @if($contract->type == 'hakedis' || $contract->type == 'sabit')
                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover table-striped">
                                        <thead>
                                        <tr>
                                            <td>Hizmet</td>
                                            @if($contract->type == 'sabit')
                                                <td>Alıcı Fiyatı</td>
                                            @endif
                                            {{--                                            <td>Satıcı Fiyatı</td>--}}
                                            <td>Sözleşme Sahibi Fiyatı</td>
                                        </tr>
                                        </thead>
                                        <tbody id="hizmetListesi">
                                        @forelse($contract->getStockIds->groupBy('stock_id') as $stockId => $contractStockIds)
                                            @php $contractStock = \App\Models\ContractStock::where('contract_id',$contract->id)->where('stock_id',$stockId)->first()?->getStock; @endphp
                                            <tr>
                                                <td>
                                                    <input class="form-control form-control-sm" readonly value="{{ $contractStock?->ad }}">
                                                    <input type="hidden" name="stock_id[]" form="mainForm" value="{{ $contractStock?->id }}">
                                                </td>
                                                @if($contract->type == 'sabit')
                                                    <td>
                                                        <input class="form-control form-control-sm" form="mainForm" disabled name="buyer_price[]" value="{{ $contractStockIds->where('type','alici')->first()?->price }}">
                                                    </td>
                                                @endif
                                                {{--                                                <td>--}}
                                                {{--                                                    <input class="form-control form-control-sm" form="mainForm" disabled name="seller_price[]" value="{{ $contractStockIds->where('type','satici')->first()?->price }}">--}}
                                                {{--                                                </td>--}}
                                                <td>
                                                    <input class="form-control form-control-sm" form="mainForm" disabled name="contract_owner_price[]" value="{{ $contractStockIds->where('type','sozlesme_sahibi')->first()?->price }}">
                                                </td>
                                            </tr>
                                        @empty
                                        @endforelse

                                        </tbody>
                                        @if(\App\Models\ContractStock::where('contract_id',$contract->id)->onlyTrashed()->exists())
                                            <tfoot>
                                            <tr class="text-center">
                                                <th colspan="4">Fiyat Geçmişi</th>
                                            </tr>
                                            <tr>
                                                <td>Stok</td>
                                                <td>Geçerli Cari</td>
                                                <td>Fiyat</td>
                                                <td>Geçerlilik Tarihi</td>
                                            </tr>
                                            @forelse(\App\Models\ContractStock::where('contract_id',$contract->id)->onlyTrashed()->get() as $deletedPrices)
                                                <tr>
                                                    <td>{{ $deletedPrices?->getStock->ad }}</td>
                                                    <td>
                                                        @if($deletedPrices->type == 'satici')
                                                            Satıcı
                                                        @elseif($deletedPrices->type == 'alici')
                                                            Alıcı
                                                        @else
                                                            Sözleşme Sahibi
                                                        @endif
                                                    </td>
                                                    <td>{{ $deletedPrices->price }}₺</td>
                                                    <td>{{ $deletedPrices->created_at->translatedFormat('d F Y H:i') }} - {{ $deletedPrices->deleted_at->translatedFormat('d F Y H:i') }}</td>
                                                </tr>
                                            @empty
                                            @endforelse
                                            </tfoot>
                                        @endif

                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script>
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }



        $('.create-codes').on('click', function () {
            let count = $('input[name="code_count"]').val();
            if (count > 0) {
                let html = '';
                for (let i = 0; i < count; i++) {
                    html += `<tr><td><input minlength="10" maxlength="10" form="createContractCode" name="special_code[]" value="${generateCustomCode()}" readonly class="form-control-sm special_code form-control"></td>` +
                        '<td><input name="plaka[]" form="createContractCode" class="form-control-sm plaka form-control"></td>' +
                        `<td><select class="form-control contract_stock_id form-control-sm" required form="createContractCode" name="contract_stock_id[`+i+`][]"><option value="">Sözleşme Stokları</option> @foreach(\App\Models\ContractStock::where('contract_id',$contract->id)->get()->groupBy('stock_id') as $stockId => $stocks)  <option value="{{ $stocks[0]->branch_id }}" data-select="@foreach($stocks as $stc) <option value='{{ $stc->type }}'>{{ $stc->type == 'alici' ? 'Alıcı' : ($stc->type == 'satici' ? 'Satıcı' : $contract->getCustomer->fullName) }}</option> @endforeach">{{ $stocks[0]->getStock?->ad }}</option> @endforeach </select></td>` +
                        `<td><select class="select2" multiple form="createContractCode" name="contract_branch_id[`+i+`][]"><option value="0">Sözleşme Bayileri</option> @foreach(\App\Models\ContractBranch::where('contract_id',$contract->id)->get() as $branch) <option value="{{ $branch->branch_id }}">{{ $branch->getBranch?->kisa_ad }}</option> @endforeach</select></td>` +
                        '<td><select required class="form-control contract_type form-control-sm" form="createContractCode" name="contract_type[]"><option value="">Seçilmedi</option><option value="alici">Alıcı</option><option value="sozlesme_sahibi">{{ $contract->getCustomer->fullName }}</option></select></td>' +
                        '<td><select required class="form-control contract_must_payment form-control-sm" form="createContractCode" name="contract_must_payment[]"><option value="">Seçilmedi</option><option value="1">Evet</option>@if($contract->type != 'sabit')<option value="0">Hayır</option>@endif</select></td>' +
                        '<td><button type="button" class="btn btn-danger btn-sm delete-row">Sil</button></td>' +
                        '</tr>';
                }
                $('.codes').html(html);
            }

            // Sözleşme türüne göre ödeme alanını ayarlama
            $('.contract_type').on('change', function () {
                let paymentField = $(this).parent().parent().find('.contract_must_payment');
                if ($(this).val() === 'sozlesme_sahibi') {
                    paymentField.html('<option value="0">Hayır</option>');
                } else {
                    paymentField.html('<option value="1">Evet</option><option value="0">Hayır</option>');
                }
            });

            // Select2 alanını başlatma
            $(".select2").select2({
                placeholder: "Ara.."
            });

            // Contract stock seçimine göre contract_type güncelleme
            $('.contract_stock_id').on('change', function () {
                if ($(this).val() !== '') {
                    $(this).parent().parent().find('.contract_type').html('<option value="">Seçilmedi</option><option value="alici">Alıcı</option><option value="sozlesme_sahibi">{{ $contract->getCustomer->fullName }}</option>');
                } else {
                    $(this).parent().parent().find('.contract_type').html($(this).find('option:selected').data('select'));
                }
            });

            // Satırdaki "Sil" düğmesine basıldığında satırı silme işlevi
            $(document).on('click', '.delete-row', function () {
                $(this).closest('tr').remove();
            });

            // Özel kod ve plaka alanlarındaki boşlukları temizleme ve büyük harfe çevirme
            $('.special_code, .plaka').on('keyup', function () {
                $(this).val($(this).val().replaceAll(' ', '').toUpperCase());
            });
        });

        function generateCustomCode() {
            const prefix = 'UMR{{ str_pad((auth('customer')->user()->il_kodu ?? 1),2,0,STR_PAD_LEFT) }}';
            const characters = 'ABCDEFGHJKLMNOPQRSTUVWXYZ023456789';
            let result = prefix;
            const charactersLength = characters.length;
            for (let i = 0; i < 4; i++) {
                result += characters.charAt(Math.floor(Math.random() * charactersLength));
            }
            return result;
        }

        $(document).ready(function() {
            // Türk Lirası giriş alanları için işlem
            document.querySelectorAll('.turk-lirasi').forEach(input => {
                // Harflerin ve geçersiz karakterlerin girişini engelleme
                input.addEventListener('input', function(event) {
                    this.value = this.value.replace(/[^0-9.,]+/g, ""); // Sadece rakam, virgül ve nokta izinli
                });

                // Kuruşları ekleme ve doğru formatlama (blur olayında)
                const formatCurrency = (element) => {
                    let value = element.value.replace(/[^0-9.,]+/g, "").replace(',', '.'); // Geçersiz karakterleri temizle
                    let amount = parseFloat(value); // Sayıya çevir

                    // Eğer geçerli bir sayıysa formatla
                    if (!isNaN(amount)) {
                        let formattedAmount = new Intl.NumberFormat('tr-TR', {
                            style: 'currency',
                            currency: 'TRY',
                            minimumFractionDigits: 2
                        }).format(amount);
                        element.value = formattedAmount; // Formatlanmış değeri geri yaz
                    } else {
                        element.value = ''; // Geçersiz ise boş bırak
                    }
                };

                // DOM yüklendiğinde formatlama
                formatCurrency(input);

                // Blur olayında formatlama
                input.addEventListener('blur', function() {
                    formatCurrency(this);
                });
            });
        });
    </script>
@endpush


