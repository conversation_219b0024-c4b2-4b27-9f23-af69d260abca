@extends('online.pages.build')
@section('content')
    <div class="wrapper">
        <div class="container-fluid"><div class="row app-wrap">
                <div class="col-xl-3 col-md-6 header-info-wrap iwrap-left">
                    <div class="card mini-stat bg-ozel">
                        <div class="card-body mini-stat-img tlg-pd-conf">
                            <div class="mini-stat-icon">
                                <i class="ti ti-search float-right"></i>
                            </div>
                            <div class="text-white">
                                <h6 class="text-uppercase tlg-fsz mb-3">TOPLAM YAPTIĞIM<br>SORGULAMA</h6>
                                <h4 class="tlg-fsz2">{{ auth('customer')->user()->getQueries->count() }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 header-info-wrap iwrap-right">
                    <div class="card mini-stat bg-ozel">
                        <div class="card-body mini-stat-img tlg-pd-conf">
                            <div class="mini-stat-icon">
                                <i class="mdi mdi-crown float-right"></i>
                            </div>
                            <div class="text-white">
                                <h6 class="text-uppercase tlg-fsz mb-3">ÜCRETSİZ SORGU<br>HAKKIM</h6>
                                <h4 class="tlg-fsz2">0 <small>Adet</small></h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 header-info-wrap iwrap-left">
                    <div class="card mini-stat bg-ozel">
                        <div class="card-body mini-stat-img tlg-pd-conf">
                            <div class="mini-stat-icon">
                                <i class="mdi mdi-file-document float-right"></i>
                            </div>
                            <div class="text-white">
                                <h6 class="text-uppercase tlg-fsz mb-3">SATIN ALDIĞIM<br>RAPORLAR</h6>
                                <h4 class="tlg-fsz2">0 <small>Adet</small></h4>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 header-info-wrap iwrap-right">
                    <div class="card mini-stat bg-ozel">
                        <div class="card-body mini-stat-img tlg-pd-conf">
                            <div class="mini-stat-icon">
                                <i class="ti ti-credit-card float-right"></i>
                            </div>
                            <div class="text-white">
                                <h6 class="text-uppercase tlg-fsz mb-3">PLUS CARD<br>BAKİYEM</h6>
                                <h4 class="tlg-fsz2">0 <small>Adet</small></h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row arac-sorgula-wrap">
                <div class="col-12">
                    <div class="card m-b-20">
                        <div class="card-body">
                            <h4 class="mt-0 header-title">Rapor Sorgula</h4>
                            <p class="text-muted m-b-10">
                                Araç Plakasının <span class="tlg-highlight">tamamını</span> veya Şasi Numarasının <span class="tlg-highlight">son 8</span> hanesini girerek araç sorgulaması yapabilirsiniz.
                            </p>

                            <form action="{{ route('online.query') }}">
                                <div id="Form_Elements">

                                    <div class="form-group row">
                                        <label for="DataID" class="col-md-12 col-form-label">Plaka-Şasi No</label>
                                        <div class="col-md-12 ">
                                            <input class="form-control" type="text" placeholder="Lütfen Plakayı ya da Şasi Numarasını giriniz" id="DataID" name="query">
                                        </div>
                                    </div>

                                </div>

                                <div class="form-group row">
                                    <div class="col-md-12">
                                        <button id="SearchButton" class="btn btn-md btn-primary btn-block"><i class="fa fa-search"></i> Sorgula</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-xl-6">
                    <div class="card m-b-20 PlusBakiye">
                        <div class="card-body plus-wrap">
                            <ul class="nav nav-tabs nav-tabs-custom" role="tablist">
                                <li class="nav-item">
                                    <input type="hidden" id="donutokey_0" value="ok">
                                    <a data-key="0" class="tabdonutlink nav-link active" data-toggle="tab" href="#PlusBakiye_0" role="tab">Tümü</a>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active p-3" id="PlusBakiye_0" role="tabpanel">
                                    <div class="row text-center m-t-20">
                                        <div class="col-6">
                                            <h5 class="">0</h5>
                                            <p class="text-muted ">Toplam Bakiye</p>
                                        </div>
                                        <div class="col-6">
                                            <h5 class="">0</h5>
                                            <p class="text-muted ">Kalan Bakiye</p>
                                        </div>
                                    </div>
                                    <div id="plusbakiyedonut_0" class="dashboard-charts morris-charts"><svg height="300" version="1.1" width="616" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="overflow: hidden; position: relative; left: -0.796875px; top: -0.28125px;"><desc style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);">Created with Raphaël 2.1.2</desc><defs style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></defs><path fill="none" stroke="#e74c3c" d="M308.0975,243.33333333333331" stroke-width="2" opacity="1" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 1;"></path><path fill="#e74c3c" stroke="#ffffff" d="M308.0975,246.33333333333331A140,140,0,0,1,308.0975,290Z" stroke-width="3" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></path><path fill="none" stroke="#2980b9" d="M,0,0" stroke-width="2" opacity="0" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"></path><path fill="#2980b9" stroke="#ffffff" d="Z" stroke-width="3" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></path><text x="308.0975" y="140" text-anchor="middle" font-family="&quot;Arial&quot;" font-size="15px" stroke="none" fill="#000000" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); text-anchor: middle; font-family: Arial; font-size: 15px; font-weight: 800;" font-weight="800" transform="matrix(1.2772,0,0,1.2772,-85.4007,-41.786)" stroke-width="0.7829706101190476"><tspan style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);" dy="5.25">Kullanılan Bakiye</tspan></text><text x="308.0975" y="160" text-anchor="middle" font-family="&quot;Arial&quot;" font-size="14px" stroke="none" fill="#000000" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); text-anchor: middle; font-family: Arial; font-size: 14px;" transform="matrix(1.5954,0,0,1.5954,-183.4541,-89.4651)" stroke-width="0.6267857142857143"><tspan style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);" dy="4.75">0</tspan></text></svg></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-6">
                    <div class="card m-b-20 SonRaporlar" style="min-height: 510.594px;">
                        <div class="card-body">

                            <ul class="nav nav-tabs nav-tabs-custom" role="tablist">
                                <li class="nav-item">
                                    <a class="nav-link active" data-toggle="tab" href="#SonSorgu" role="tab" aria-selected="true">Sorgulamalarım</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-toggle="tab" href="#Raporlarim" role="tab" aria-selected="false">Raporlarım</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-toggle="tab" href="#Araclarim" role="tab" aria-selected="false">Araçlarım</a>
                                </li>
                            </ul>

                            <!-- Tab panes -->
                            <div class="tab-content">
                                <div class="tab-pane p-3 active" id="SonSorgu" role="tabpanel">
                                    <div class="table-responsive">
                                        <table class="table table-vertical mb-1 ozelTablo">
                                            <thead>
                                            <tr>
                                                <td>#</td>
                                                <td><b>Plaka/Şase</b></td>
                                                <td width="100px"><b>-</b></td>
                                            </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($queries as $query)
                                                    <tr>
                                                        <td>#{{ $query->id }}</td>
                                                        <td>{{ $query->query }}</td>
                                                        <td width="100px">
                                                            <a href="{{ route('online.query',['query'=>$query->query]) }}" class="btn btn-secondary btn-sm waves-effect waves-light">Tekrar Sorgula</a>
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="tab-pane p-3" id="Raporlarim" role="tabpanel">
                                    <div class="table-responsive">
                                        <table class="table table-vertical mb-1 ozelTablo">
                                            <thead>
                                            <tr>
                                                <td>#</td>
                                                <td><b>Belge No</b></td>
                                                <td width="100px"><b>-</b></td>
                                            </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($expertises as $index => $expertise)
                                                    <tr>
                                                        <td>{{ $index+1 }}</td>
                                                        <td>{{ $expertise->belge_no ?? ($expertise->T400_belgeno ?? '') }}</td>
                                                        <td><button class="btn btn-danger btn-sm">Rapor Görüntüle</button></td>
                                                    </tr>
                                                @endforeach

                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="tab-pane p-3" id="Araclarim" role="tabpanel">
                                    <div class="row">
                                        <div class="col-md-6 col-sm-12">
                                            <div class="table-responsive">
                                                <table class="table table-vertical mb-1 ozelTablo">
                                                    <thead>
                                                    <tr>
                                                        <td colspan="3" class="text-center">Alıcısı Olduğunuz</td>
                                                    </tr>
                                                    <tr>
                                                        <td>#</td>
                                                        <td><b>Plaka</b></td>
                                                        <td width="100px"><b>-</b></td>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    @foreach($buyCars as $index => $buyCar)
                                                        <tr>
                                                            <td>#{{ $index+1 }}</td>
                                                            <td>{{ $buyCar }}</td>
                                                            <td width="100px">
                                                                <a href="{{ route('online.query',['query'=>$buyCar]) }}" class="btn btn-secondary btn-sm waves-effect waves-light">Raporları Gör</a>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                        <div class="col-md-6 col-sm-12">
                                            <div class="table-responsive">
                                                <table class="table table-vertical mb-1 ozelTablo">
                                                    <thead>
                                                    <tr>
                                                        <td colspan="3" class="text-center">Satıcısı Olduğunuz</td>
                                                    </tr>
                                                    <tr>
                                                        <td>#</td>
                                                        <td><b>Plaka</b></td>
                                                        <td width="100px"><b>-</b></td>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    @foreach($saleCars as $index => $saleCar)
                                                        <tr>
                                                            <td>#{{ $index+1 }}</td>
                                                            <td>{{ $saleCar }}</td>
                                                            <td width="100px">
                                                                <a href="{{ route('online.query',['query'=>$saleCar]) }}" class="btn btn-secondary btn-sm waves-effect waves-light">Raporları Gör</a>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
@endsection
