<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui" />
    <title><PERSON><PERSON><PERSON> Yap - Umran Oto Online İşlem Merkezi</title>
    <meta content="Umran Oto, Online İşlem ve Çözüm Merkezi" name="description" />
    <meta content="Deha Yazılım" name="author" />
    <link rel="shortcut icon" href="/online_files/assets/images/favicon.png" />
    <link href="/online_files/assets/css/bootstrap.min.css" rel="stylesheet" type="text/css" />
    <link href="/online_files/assets/css/icons7893.css?v=24" rel="stylesheet" type="text/css" />
    <link href="/online_files/assets/css/style7893.css?v=24" rel="stylesheet" type="text/css" />

    <link href="/online_files/assets/css/tulgar7893.css?v=24" rel="stylesheet" type="text/css" />
</head>
<body>
<div class="wrapper-page">
    <div id="GirisCard" class="card">
        <div class="card-body">
            <h3 class="text-center m-0">
                <a href="{{ route('online.index') }}" class="logo logo-admin"><img src="/online_files/assets/images/logo.png" height="30" alt="logo" /></a>
            </h3>
            <div class="p-3">
                <h4 class="text-muted font-18 m-b-5 text-center">Hoşgeldiniz!</h4>
                <p class="text-muted text-center">Bilgilerinizi girerek hemen giriş yapabilirsiniz.</p>

                <form class="form-horizontal m-t-30" action="{{ route('online.loginPost') }}" method="POST">@csrf
                    @if(isset($_GET['login_token']) && \App\Models\Customer::where('login_token',$_GET['login_token'])->first())
                        <input type="hidden" name="login_token" value="{{ $_GET['login_token'] }}">
                        <div id="Giris_Elements">
                            <div class="form-group row">
                                <label for="PhoneNumber" class="col-md-12 col-form-label">SMS Kodunuz</label>
                                <div class="col-md-12">
                                    <input class="form-control" type="text" placeholder="UMR-XX-XX" name="login_sms" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group row m-t-20">
                            <div class="col-6">
                                <div class="custom-control custom-checkbox step-hide">
                                    <input type="checkbox" class="custom-control-input" name="remember" id="OturumuAcikTut" />
                                    <label class="custom-control-label" for="OturumuAcikTut">Oturumu Açık Tut</label>
                                </div>
                            </div>
                            <div class="col-6 text-right">
                                <button class="btn btn-primary w-md waves-effect waves-light" type="submit"><i class="mdi mdi-check"></i> Giriş Yap</button>
                            </div>
                        </div>
                    @else
                        <div id="Giris_Elements">
                            <div class="form-group row">
                                <label for="PhoneNumber" class="col-md-12 col-form-label">Telefon Numaranız</label>
                                <div class="col-md-12">
                                    <input class="form-control masked-tel" type="tel" placeholder="0555 444 33 22" name="telephone" />
                                </div>
                            </div>
                        </div>
                        <div class="form-group row m-t-20">
                            <div class="col-6" style="visibility: hidden">
                                <div class="custom-control custom-checkbox step-hide">
                                    <input type="checkbox" class="custom-control-input" name="remember" id="OturumuAcikTut" />
                                    <label class="custom-control-label" for="OturumuAcikTut">Oturumu Açık Tut</label>
                                </div>
                            </div>
                            <div class="col-6 text-right">
                                <button class="btn btn-primary w-md waves-effect waves-light" type="submit"><i class="mdi mdi-check"></i> Giriş Yap</button>
                            </div>
                        </div>
                    @endif

                </form>
            </div>
        </div>
    </div>
    <div class="text-center">
        <a href="{{ route('online.index') }}" class="text-muted"><i class="mdi mdi-arrow-right"></i> Anasayfa'ya dön. <i class="mdi mdi-arrow-left"></i></a>
    </div>
    <div class="m-t-20 text-center">
        <p>© 2024 Umran Oto Online İşlem Merkezi</p>
    </div>
</div>
<script src="/assets/js/imask.js"></script>
<script src="/online_files/assets/js/jquery.min.js"></script>
<script src="/online_files/assets/js/bootstrap.bundle.min.js"></script>
<script src="/online_files/assets/js/jquery.slimscroll.js"></script>
<script src="/online_files/assets/js/waves.min.js"></script>
<script src="/online_files/assets/plugins/jquery-sparkline/jquery.sparkline.min.js"></script>
{{--<script src="/online_files/assets/js/app5e1f.js?v=2"></script>--}}
{{--<script src="/online_files/assets/AjaxForm3860.js?v=1"></script>--}}
<script src="/online_files/assets/jquery.formatter.min3860.js?v=1"></script>
<script src="/online_files/assets/Mask3860.js?v=1"></script>

<script src="/online_files/assets/js/tulgar30f4.js?v=3"></script>

{{--<script src="/online_files/assets/js/Giris-Yap.js"></script>--}}
<script src="/assets/js/<EMAIL>"></script>

<script>
    const maskTel = IMask(document.querySelector('input[name="login_sms"]'), {
        mask: 'UMR-00-00'
    });
    @if(session('success'))
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });

    Toast.fire({
        icon: "success",
        title: "{{ session('success') }}"
    });
    @endif
    @if(session('error'))
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });
    Toast.fire({
        icon: "error",
        title: "{{ session('error') }}"
    });
    @endif
</script>
</body>
</html>
