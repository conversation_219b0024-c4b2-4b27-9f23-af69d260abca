@extends('online.pages.build')
@push('css')
    <style>
        .paynetj-button{
            display: none;
        }
    </style>
@endpush
@section('content')
    <div class="wrapper">
        <div class="container-fluid"><div class="row">
                <div class="col-12">
                    <div class="alert alert-info text-center">A<PERSON><PERSON>ğıdan Paket Seçimi Yaparak Kartınıza Yükleme Yapabilirsiniz</div>
                </div>
            </div>

            <div class="row">
                @if(isset($_GET['plus_card_id']) && $_GET['plus_card_id'] != '')
                    @foreach($plusCardDefinitions as $index => $plusCardDefinition)
                        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-4 col-xl-4">
                            <div class="card">
                                <div class="card-body plus-wrap">
                                    <div class="row">
                                        <div class="col-xs-12 col-sm-12 col-md-6 wrap-t2">
                                            <div class="media">
                                                {{--                                            <img class="d-flex align-self-start rounded mr-3" src="/assets/ccard.png" alt="Paket" height="50">--}}
                                                <div class="media-body">
                                                    <h5 class="mt-2 font-16">{{ $plusCardDefinition->definition_name }}</h5>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xs-12 col-sm-12 col-md-6">
                                            <button type="button" class="btn btn-block btn-primary paynetButton"
                                                    data-amount="{{ ($plusCardDefinition->unit_quantity * $plusCardDefinition->unit_price) * 100 }}"
                                                    data-button_label="Satın Al ({{ $plusCardDefinition->unit_quantity * $plusCardDefinition->unit_price }}₺)"
                                                    data-name="Plus Kart Satın Al"
                                                    data-image="/assets/umram4.webp"
                                                    data-plus_card_definition_id="{{ $plusCardDefinition->id }}"
                                                    data-agent="1697"
                                                    data-index="{{ $index }}"
                                                    data-no_instalment="false"
                                                    data-tds_required="true"
                                                    data-form="#customerBuyPlusCardPaynetForm"
                                                    data-add_commission_amount="true"
                                                    data-reference_no="{{ strtoupper(\Illuminate\Support\Str::random(8)) }}"
                                                    data-description="{{ $plusCardDefinition->unit_quantity }} Adet Plus Card Bakiyesi Satın Alıyorsunuz."
                                            >Satın Al<br><small>{{ $plusCardDefinition->unit_quantity * $plusCardDefinition->unit_price }} ₺</small></button>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <ul>
                                            <li>Fiyatı: {{ $plusCardDefinition->unit_quantity * $plusCardDefinition->unit_price }} ₺</li>
                                            <li>{{ $plusCardDefinition->unit_quantity }} Adet Plus Card Bakiyesi</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                    <form id="customerBuyPlusCardPaynetForm" action="{{ route('online.customerBuyPlusCardPaynetForm') }}" method="POST">@csrf
                            <input type="hidden" name="return_url" value="{{ url()->full() }}">
                            <input type="hidden" name="add_comission_amount" value="true">
                            <input type="hidden" name="no_instalment" value="false">
                            <input type="hidden" name="tds_required" value="true">
                            <input type="hidden" name="ratio_code" value="">
                            <input type="hidden" name="installments" value="">
                            <input type="hidden" name="reference_no" value="">
                            <input type="hidden" name="plus_card_definition_id" value="">
                            <input type="hidden" name="plus_card_id" value="{{ $_GET['plus_card_id'] ?? '' }}">
                            <script
                                    id="paynetScript"
                                    class="paynet-button"
                                    type="text/javascript"
                                    src="https://pj.paynet.com.tr/public/js/paynet.min.js"
                                    data-key="pbk_rPMxYmDP2b8nhT2aZuhxQqyNZoqg"
                                    data-amount=""
                                    data-name="Plus Kart Satın Al"
                                    data-image="/assets/umram4.webp"
                                    data-agent="1697"
                                    data-no_instalment="false"
                                    data-tds_required="true"
                                    data-form=""
                                    data-add_commission_amount="true"
                                    data-reference_no=""
                                    data-description=""
                            >
                            </script>
                </form>
                        <script>
                            document.querySelectorAll('.paynetButton').forEach(function(button) {
                                button.addEventListener('click', function() {
                                    const $customerBuyPlusCardPaynetForm = document.getElementById('customerBuyPlusCardPaynetForm');
                                    $customerBuyPlusCardPaynetForm.querySelector('input[name="reference_no"]').value = button.getAttribute('data-reference_no');
                                    $customerBuyPlusCardPaynetForm.querySelector('input[name="plus_card_definition_id"]').value = button.getAttribute('data-plus_card_definition_id');
                                    document.querySelectorAll('.paynet-button').forEach(function (script){
                                        // Transfer data attributes from the button to the script
                                        script.setAttribute('data-amount', button.getAttribute('data-amount'));
                                        script.setAttribute('data-button_label', button.getAttribute('data-button_label'));
                                        script.setAttribute('data-form', button.getAttribute('data-form'));
                                        script.setAttribute('data-reference_no', button.getAttribute('data-reference_no'));
                                        script.setAttribute('data-description', button.getAttribute('data-description'));
                                    })
                                    // Trigger the click on the paynetj-button element
                                    document.querySelector('.paynetj-button').click();
                                });
                            });
                        </script>
                @else
                    @foreach($plusCards as $index => $plusCard)
                        <div class="col-xs-12 col-sm-12 col-md-6 col-lg-4 col-xl-4">
                            <div class="card">
                                <div class="card-body plus-wrap">
                                    <div class="row">
                                        <div class="col-xs-12 col-sm-12 col-md-6 wrap-t2">
                                            <div class="media">
                                                <div class="media-body">
                                                    <h5 class="mt-2 font-16">{{ $plusCard->no }}</h5>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xs-12 col-sm-12 col-md-6">
                                            <a href="{{ route('online.plusCardBuy',['plus_card_id'=>$plusCard->id]) }}" class="btn btn-block btn-primary">Yükleme Yap</a>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <ul>
                                            <li>Mevcut Kredi : {{ $plusCard->getTotalBalance()['credits'] }}</li>
                                            <li>Mevcut Puan : {{ $plusCard->getTotalBalance()['points'] }}</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @endif

            </div>

        </div>
    </div>
@endsection
