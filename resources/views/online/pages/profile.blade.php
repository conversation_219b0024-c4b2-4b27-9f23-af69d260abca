@extends('online.pages.build')
@section('content')
    <div class="wrapper">
        <div class="container-fluid"><div class="row">
                <div class="col-lg-12">
                    <div class="card m-b-30">
                        <div class="card-header">Profilim</div>
                        <div class="card-body">
                            <form method="post" action="{{ route('online.profileUpdate') }}">@csrf
                                <div class="row">
                                    <div class="col-md-6 col-lg-6 col-sm-12 fg1">
                                        <div class="form-group">
                                            <label class="control-label" for="cari_GSM">Cep Telefonu (GSM) *</label>
                                            <input class="form-control masked-tel" type="tel" placeholder="0550 000 00 00" value="{{ auth('customer')->user()->cep }}" id="cari_GSM" name="cep" required="">
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label" for="cari_Telefon">Telefon (Sabit)</label>
                                            <input class="form-control masked-tel" type="tel" placeholder="Sabit Telefonunuz (Zorunlu Değil)" value="{{ auth('customer')->user()->telefon }}" id="cari_Telefon" name="telefon" required="">
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-lg-6 col-sm-12 fg1">
                                        <div class="form-group">
                                            <label class="control-label" for="cari_Fax">Fax</label>
                                            <input type="tel" id="cari_Fax" name="fax" class="form-control masked-tel" value="{{ auth('customer')->user()->fax }}" placeholder="Fax Numaranız (Zorunlu değil)">
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label" for="cari_Eposta">E-Posta</label>
                                            <input value="{{ auth('customer')->user()->eposta }}" type="email" id="cari_Eposta" name="eposta" class="form-control" placeholder="E-Posta Adresiniz (Zorunlu değil)">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group fg1">
                                    <label class="control-label" for="cari_Web">Web Adresiniz</label>
                                    <input value="{{ auth('customer')->user()->web }}" type="text" id="cari_Web" name="web" class="form-control" placeholder="www.domain.com (Zorunlu değil)">
                                </div>
                                <div class="form-group fg1">
                                    <div class="row">
                                        <div class="col-md-3">
                                            <label class="control-label" for="cari_Il">İl *</label>
                                            <select id="cari_Il" class="form-control" name="il_kodu">
                                                <option value="0" disabled="" selected="">-- İl seçiniz.</option>
                                                @foreach($cities as $city)
                                                    <option @if(auth('customer')->user()->il_kodu == $city->id) selected @endif value="{{ $city->id }}">{{ $city->title }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="control-label" for="cari_Ilce">İlçe *</label>
                                            <select id="cari_Ilce" class="form-control" name="ilce_kodu">
                                                <option value="0" disabled="">-- İlçe seçiniz. .</option>
                                                @if(auth('customer')->user()->ilce_kodu > 0)
                                                    <option selected value="{{ auth('customer')->user()->ilce_kodu }}">{{ \App\Models\Town::where('ilce_id',auth('customer')->user()->ilce_kodu)->first()?->ilce_title }}</option>
                                                @endif
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="control-label" for="cari_Semt">Semt *</label>
                                            <input value="{{ auth('customer')->user()->semt }}" type="text" id="cari_Semt" name="semt" class="form-control" placeholder="... Semt (Zorunlu)" required="">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="control-label" for="cari_Mah">Mahalle *</label>
                                            <input value="{{ auth('customer')->user()->mahalle }}" type="text" id="cari_Mah" name="mahalle" class="form-control" placeholder="... Mahalle (Zorunlu)" required="">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group fg1">
                                    <label class="control-label" for="cari_Web">Sokak *</label>
                                    <input value="{{ auth('customer')->user()->sokak }}" type="text" id="cari_Sokak" name="sokak" class="form-control" placeholder="... Sokak (Zorunlu)" required="">
                                </div>
                                <div class="form-group text-right">
                                    <button type="submit" class="btn btn-md btn-success">
                                        <i class="fa fa-save"></i>
                                        Kaydet
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div></div>
    </div>
@endsection
@push('js')
    <script>
        $('select[name="il_kodu"]').on('change',function (){
            let $val = $(this).val()
            $('select[name="ilce_kodu"]').find('option').remove()
            if($val > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$val} ,
                    success: function (response) {
                        if (response.items.length > 0){
                            $.each(response.items, function (index, item) {
                                $('select[name="ilce_kodu"]').append('<option value="' + item.ilce_id + '">' + item.ilce_title + '</option>')
                            })

                            $('select[name="ilce_kodu"]').trigger('change')
                        }
                    }
                });
            }
        })
    </script>
@endpush
