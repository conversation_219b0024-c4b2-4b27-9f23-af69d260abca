@extends('online.pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('content')
    <div class="wrapper">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <table id="datatable" class="table table-bordered dt-responsive nowrap dataTable no-footer dtr-inline" style="border-collapse: collapse; border-spacing: 0px; width: 100%;">
                                        <thead>
                                        <tr role="row">
                                            <th><PERSON><PERSON></th>
                                            <th>Tarih</th>
                                            <th>Şube</th>
                                            <th>Durum</th>
                                            <th>Detay</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($bookings->reverse() as $item)
                                            <tr>
                                                <td>Araç Bilgisi Eklenmemiş</td>
                                                <td>{{ \Carbon\Carbon::make($item->date)->format('d.m.Y') }}</td>
                                                <td>{{ $item->getBranch?->kisa_ad }}</td>
                                                <td></td>
                                                <td><a href="{{ route('online.remoteShow',$item->id) }}" class="btn btn-danger btn-sm">Detaylar</a> </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script>
        $(function (e) {
            'use strict';
            $('#datatable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });
        });
    </script>
@endpush
