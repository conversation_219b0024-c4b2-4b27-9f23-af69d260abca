@extends('online.pages.build')
@section('content')
    <div class="wrapper">
        <div class="container-fluid"><div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body" id="UzaktanCard">
                            <h6 class="card-title">Uzaktan Ekspertiz Kaydı Aç</h6>
                            <form action="{{ route('online.remoteStore') }}" method="post">@csrf
                                <div id="Form_Elements">
                                    <p>Ekspertizi yapılacak araç bilgileri, araç şubeye geldiğinde kaydınıza işlenecektir.<br><strong>Bilgi:</strong> Plus Ekspertiz - FULL Paketine ilave olarak araçta +80 nokta kontrolü yapılmaktadır.</p>

                                    <div class="form-group">
                                        <label for="Sube">Bayi</label>
                                        <select name="branch_id" class="form-control" id="Sube">
                                            <option value="0"><PERSON><PERSON></option>
                                            @foreach($branches as $branch)
                                                <option value="{{ $branch->id }}">{{ $branch->getCity?->title }} - {{ $branch->kisa_ad }}</option>
                                            @endforeach
                                        </select>

                                    </div>


                                    <div style="display:none;" class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="OdemeYontem">Ödeme Yöntemi</label>
                                                <input autocomplete="off" readonly="" value="Plus Card Bakiyesi ile Öde (-2 Bakiye)" name="OdemeYontem" class="form-control" placeholder="">
                                                <small>Bakiyenizden düşüm işlemi araç şubeye gittiğinde gerçekleşecektir.</small>
                                            </div>
                                        </div>
                                        <div class="col-md-6">

                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="datepicker">Ekspertize Gidilecek Tarih</label>
                                        <div class="input-group">
                                            <input autocomplete="off" disabled="" value="{{ now()->format('Y-m-d') }}" name="date" class="form-control" placeholder="Ay/Gün/Yıl">
                                            <div class="input-group-append">
                                                <span class="input-group-text"><i class="mdi mdi-calendar"></i></span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="onay1" name="onay1">
                                            <label class="custom-control-label" for="onay1"><a style="color:blue;" href="https://online.umranoto.com.tr/is-emri" target="_blank">Uzaktan Ekspertiz İş Emri</a> Formundaki maddeleri ve bilgileri okudum, onaylıyorum. <span style="color: red;">(*)</span></label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="onay2" name="sms_kabul">
                                            <label class="custom-control-label" for="onay2"> Ekspertiz hizmeti aşamaları için bilgilendirmelerin tarafıma sms olarak gönderilmesini kabul ediyorum. <span style="color: red;">(*)</span></label>
                                        </div>
                                    </div>
                                    <div style="display: none;" class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input checked="" readonly="" type="checkbox" class="custom-control-input" id="onay3" name="yol_testi">
                                            <label class="custom-control-label" for="onay3">Gerekli görülmesi halinde araca yol testi yapılmasına onay veriyorum.</label>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="custom-control custom-checkbox">
                                            <input type="checkbox" class="custom-control-input" id="onay4" name="onay4">
                                            <label class="custom-control-label" for="onay4">Umran Plus Otomotiv A.Ş. ve program ortaklarının sms ve e-mail yoluyla kampanya, ürün ve hizmetleri hakkında bilgilendirme yapmasını kabul ediyorum.</label>
                                        </div>
                                    </div>
                                </div>

                                <button id="SearchButton" class="btn btn-block btn-primary btn-md"><i class="fa fa-check"></i> Uzaktan Ekspertiz Kaydını Aç</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
@endsection
