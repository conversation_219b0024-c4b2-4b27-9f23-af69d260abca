@extends('online.pages.build')
@push('css')
    <style>
        .paynetj-button{
            display: none;
        }
    </style>
@endpush
@section('content')
    <div class="wrapper">
        <div class="container-fluid"><div class="row app-wrap">
                <div class="col-xl-2 col-md-4 col-lg-4 col-sm-12 header-info-wrap iwrap-left">
                    <div class="card mini-stat bg-ozel2">
                        <div class="card-body mini-stat-img tlg-pd-conf tlg-pd-conf-2">
                            <div class="text-white">
                                <h6 class="tlg-fsz text-uppercase mb-3 ozelYaziClass">MARKA/MODEL</h6>
                                <h4 class="tlg-fsz2 ozelYaziClass">{{ $car->getMarka?->name }} / {{ $car->getModel?->name }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-md-4 col-lg-4 col-sm-12 header-info-wrap iwrap-right">
                    <div class="card mini-stat bg-ozel2">
                        <div class="card-body mini-stat-img tlg-pd-conf tlg-pd-conf-2">
                            <div class="text-white">
                                <h6 class="tlg-fsz text-uppercase mb-3 ozelYaziClass">MODEL YILI</h6>
                                <h4 class="tlg-fsz2 ozelYaziClass">{{ $car->model_yili }}</h4>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-2 col-md-4 col-lg-4 col-sm-12 header-info-wrap iwrap-right">
                    <div class="card mini-stat bg-ozel2">
                        <div class="card-body mini-stat-img tlg-pd-conf tlg-pd-conf-2">
                            <div class="text-white">
                                <h6 class="tlg-fsz text-uppercase mb-3 ozelYaziClass">VİTES TİPİ</h6>
                                <h4 class="tlg-fsz2 ozelYaziClass">{{ $car->getGear?->name }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-md-4 col-lg-4 col-sm-12 header-info-wrap iwrap-left">
                    <div class="card mini-stat bg-ozel2">
                        <div class="card-body mini-stat-img tlg-pd-conf tlg-pd-conf-2">
                            <div class="text-white">
                                <h6 class="tlg-fsz text-uppercase mb-3 ozelYaziClass">KASA TİPİ</h6>
                                <h4 class="tlg-fsz2 ozelYaziClass">{{ $car->getCaseType?->name }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-2 col-md-4 col-lg-4 col-sm-12 header-info-wrap iwrap-right">

                    <div class="card mini-stat bg-ozel2">
                        <div class="card-body mini-stat-img tlg-pd-conf tlg-pd-conf-2">
                            <div class="text-white">
                                <h6 class="tlg-fsz text-uppercase mb-3 ozelYaziClass">MOTOR TİPİ</h6>
                                <h4 class="tlg-fsz2 ozelYaziClass">{{ $car->getFuel?->name }}</h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                @foreach($items as $index => $expertise)
                    <div class="col-xs-12 col-sm-12 col-md-6 col-lg-4 col-xl-4">
                        <div class="card">
                            <div class="card-body plus-wrap">
                                <div class="row">
                                    <div class="col-xs-12 col-sm-12 col-md-6">
                                        <div class="media">
                                            <img class="d-flex align-self-start rounded mr-3" src="/online_files/assets/images/report.png" alt="Rapor" height="40">
                                            <div class="media-body">
                                                <h5 class="mt-2 font-16">{{ $expertise['tarih'] }}</h5>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xs-12 col-sm-12 col-md-6">
                                        @if(auth('customer')->user()->id == $expertise['cari_id'] || auth('customer')->user()->id == $expertise['satici_id'] ||
                                            auth('customer')->user()->id == $expertise['alici_id'] ||
                                            (!is_null(auth('customer')->user()->kod) && auth('customer')->user()->kod == $expertise['musteri_uq']) ||
                                            in_array($expertise['id'],$customerBuyExpertises))
                                            <a href="{{ route('online.ekspertizRaporu',$expertise['uuid']) }}" class="btn btn-block btn-primary">
                                                Raporu Görüntüle
                                            </a>
                                        @else
                                            <a href="#" data-uuid="{{ $expertise['uuid'] }}" class="btn btn-block paynetButton btn-primary"
                                               data-amount="{{ $customerBuyExpertisePrice * 100 }}"
                                               data-button_label="Satın Al ({{ $customerBuyExpertisePrice }}₺)"
                                               data-name="Ekspertiz Raporu Satın Al"
                                               data-image="/assets/umram4.webp"
                                               data-agent="1697"
                                               data-index="{{ $index }}"
                                               data-no_instalment="false"
                                               data-tds_required="true"
                                               data-form="#customerBuyExpertisePaynetForm"
                                               data-add_commission_amount="true"
                                               data-reference_no="{{ $expertise['uuid'] }}_{{ auth('customer')->id() }}"
                                               data-description="{{ $expertise['plaka'] }} - {{ $expertise['sase_no'] }} Raporu Satın Alıyorsunuz."
                                            >
                                                Raporu Satın Al ({{ $settings->customer_buy_expertise_price }}₺)
                                            </a>
                                        @endif

                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <ul>
                                        <li>Şube: {{ $expertise['sube'] }}</li>
                                        <li>Plaka: {{ $expertise['plaka'] }}</li>
                                        <li>{{ substr_replace($expertise['sase_no'], '**********', 0, 10) }}
</li>
                                    </ul>
                                </div>

                                <hr>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    @if(count($items))
        <form id="customerBuyExpertisePaynetForm" action="{{ route('online.customerBuyExpertisePaynetForm') }}" method="POST">@csrf
            <input type="hidden" name="return_url" value="{{ url()->full() }}">
            <input type="hidden" name="add_comission_amount" value="true">
            <input type="hidden" name="no_instalment" value="false">
            <input type="hidden" name="tds_required" value="true">
            <input type="hidden" name="ratio_code" value="">
            <input type="hidden" name="installments" value="">
            <input type="hidden" name="reference_no" value="">
            <script
                    id="paynetScript"
                    class="paynet-button"
                    type="text/javascript"
                    src="https://pj.paynet.com.tr/public/js/paynet.min.js"
                    data-key="pbk_rPMxYmDP2b8nhT2aZuhxQqyNZoqg"
                    data-amount=""
                    data-name="Ekspertiz Raporu Satın Al"
                    data-image="/assets/umram4.webp"
                    data-agent="1697"
                    data-no_instalment="false"
                    data-tds_required="true"
                    data-form=""
                    data-add_commission_amount="true"
                    data-reference_no=""
                    data-description=""
            >
            </script>
        </form>
    @endif
@endsection
@push('js')
    <script>
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
    </script>
    <script>
        document.querySelectorAll('.paynetButton').forEach(function(button) {
            button.addEventListener('click', function() {
                const $customerBuyExpertisePaynetForm = document.getElementById('customerBuyExpertisePaynetForm');
                $customerBuyExpertisePaynetForm.querySelector('input[name="reference_no"]').value = button.getAttribute('data-reference_no');
                document.querySelectorAll('.paynet-button').forEach(function (script){
                    // Transfer data attributes from the button to the script
                    script.setAttribute('data-amount', button.getAttribute('data-amount'));
                    script.setAttribute('data-button_label', button.getAttribute('data-button_label'));
                    script.setAttribute('data-form', button.getAttribute('data-form'));
                    script.setAttribute('data-reference_no', button.getAttribute('data-reference_no'));
                    script.setAttribute('data-description', button.getAttribute('data-description'));
                })
                // Trigger the click on the paynetj-button element
                document.querySelector('.paynetj-button').click();
            });
        });
    </script>
@endpush