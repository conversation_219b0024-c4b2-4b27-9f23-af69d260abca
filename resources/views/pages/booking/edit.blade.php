@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@section('title','Randevu Detay')
@section('content')
    <form method="post" action="{{ route('bookings.update',$booking) }}">@csrf @method('put')
        <div class="card">
            <div class="card-body">
                <div class="row">
                    @if($booking->status == 2)
                        <button class="btn btn-danger">Servis Kaydı Oluştur</button>
                    @endif

                    <p class="mt-4">Randevu # {{ $booking->id }}</p>
                    <hr>
                    <div class="d-flex justify-content-between">
                        <div>
                            Müşteri:<br>
                            {{ $booking->getCustomer?->fullName }}<br>
                            {{ $booking->telephone }}<br>
                            Araç Bilgileri:<br>
                            Plaka: {{ $booking->plaka }}<br>
                            Şasi: {{ $booking->sase_no }}
                        </div>
                        <div>
                            <PERSON><PERSON><PERSON>:<br>
                            {{ $booking->getBranch?->unvan }}
                        </div>
                    </div>
                    <p class="mt-4">
                        Randevu Tarihi:<br>
                        {{ \Carbon\Carbon::make($booking->date)->format('d.m.Y') . '/' . \Carbon\Carbon::make($booking->hour)->format('H:i') . ' - ' . \Carbon\Carbon::make($booking->hour)->addMinutes(30)->format('H:i') }}
                    </p>
                    <hr>
                    <p>
                        Durum<br>
                        @if($booking->status == 2)
                            Servis Aktarımı Bekliyor
                        @elseif($booking->status == 1)
                            Servise Aktarıldı
                        @else
                            Servise Aktarılmadı
                        @endif
                    </p>
                </div>
            </div>
        </div>
    </form>

@endsection
