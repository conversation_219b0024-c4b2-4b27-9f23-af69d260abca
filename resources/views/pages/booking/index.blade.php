@extends('pages.build')
@section('title','Randevular')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form method="get" action="{{ route('bookings.index') }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mt-1">
                                <div class="form-group">
                                    <label><small>Başlangıç Tarihi </small></label>
                                    <input type="date" class="form-control" name="start_date" value="{{ isset($_GET['start_date'] ) ? $_GET['start_date'] : now()->subDays(7)->format('Y-m-d') }}">
                                </div>
                            </div>
                            <div class="col-md-3 mt-1">
                                <div class="form-group">
                                    <label><small>Bitiş Tarihi </small></label>
                                    <input type="date" class="form-control" name="end_date" value="{{ isset($_GET['end_date'] ) ? $_GET['end_date'] : now()->format('Y-m-d') }}">
                                </div>
                            </div>
                            @if(count($authUser->getBranchIds()) > 1)
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label><small>Bayi</small></label>
                                        <select class="form-control select2" name="branch_id">
                                            @if($authUser->type == 'admin')
                                                <option value="">Hepsi</option>
                                            @endif
                                            @foreach(\App\Models\Branch::where('status',1)->whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                                <option @if((isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id)) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-3">
                                <button class="btn btn-sm btn-success mt-4" name="type" value="customer">Listele</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    @if(in_array('*',$userRoles) || in_array('list_booking',$userRoles))
                        <a href="{{ route('bookings.index') }}" class="nav-link active">Randevular</a>
                    @endif
                </nav>
                <div class="card-body">

                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr>
                            <th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th>Araç</th>
                            <th>Müşteri</th>
                            <th>Randevu Tarihi/Saati</th>
                            <th>Durum</th>
                            <th>Bayi</th>
                            <th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($items as $key => $item)
                            <tr>
                                <td></td>
                                <td>{{ $item->plaka }}</td>
                                <td>{{ $item->getCustomer?->fullName }}</td>
                                <td>
                                    @if($item->date && $item->hour)
                                        {{ \Carbon\Carbon::make($item->date)->format('d.m.Y') . ' / ' . \Carbon\Carbon::make($item->hour)->format('H:i') . ' - ' . \Carbon\Carbon::make($item->hour)->addMinutes(30)->format('H:i') }}
                                    @else
                                        -
                                    @endif
                                </td>
                                <td>
                                    @if($item->status == 2)
                                        Servis Aktarımı Bekliyor
                                    @elseif($item->status == 1)
                                        Servise Aktarıldı
                                    @else
                                        Servise Aktarılmadı
                                    @endif
                                </td>
                                <td>{{ $item->getBranch?->kisa_ad }}</td>
                                <td>
                                    @if($authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','edit_booking')->first())
                                        <a class="btn btn-primary btn-sm" href="{{ route('bookings.edit',$item) }}">Düzenle</a>
                                    @endif
                                    @if($authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','delete_booking')->first())
                                        <button class="btn btn-danger btn-sm" type="button" onclick="checkBeforeDelete('deleteForm{{ $key }}')">Sil</button>
                                        <form method="post" action="{{ route('bookings.destroy',$item) }}" id="deleteForm{{ $key }}">@csrf @method('delete')</form>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

    <script>
        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı!",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı!",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();
            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })
        });
    </script>
@endpush
