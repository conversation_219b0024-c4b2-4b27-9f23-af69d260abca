@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','<PERSON><PERSON>kle')
@section('content')
    <form id="mainForm" method="post" action="{{ route('branches.store') }}" enctype="multipart/form-data">@csrf
        <div class="row">
            <div class="col-md-6">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="subeBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#subeBilgiler" aria-expanded="true"
                                    aria-controls="subeBilgiler">
                                <PERSON><PERSON>
                            </button>
                        </h2>
                        <div id="subeBilgiler" class="accordion-collapse collapse show" aria-labelledby="subeBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row ortaklar">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Hizmet Verdiği İl</label>
                                        <select name="il_kodu" class="select2">
                                            @foreach($cities as $city)
                                                <option value="{{ $city->id }}">{{ $city->title }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Hizmet Verdiği İlçe</label>
                                        <select name="ilce_kodu" class="select2">

                                        </select>
                                    </div>
                                    <div class="col-xl-6 d-none mt-2">
                                        <label class="form-label">Bayi Kodu</label>
                                        <input type="text" class="form-control form-control-sm" name="kod" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['kod'] : '' }}" placeholder="Bayi Kodu">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Kısa Adı <span class="text-danger">*</span> </label>
                                        <input type="text" class="form-control form-control-sm" name="kisa_ad" placeholder="Kısa Adı" value="{{ isset($_GET['kisa_ad']) && $_GET['kisa_ad'] != '' ? $_GET['kisa_ad'] : (\Cache::has('branch_create') ? \Cache::get('branch_create')['kisa_ad'] : '') }}" required>
                                    </div>
                                    <div class="col-xl-5 mt-2">
                                        <label class="form-label">Bayi Kategori <span class="text-danger">*</span></label>
                                        <select required class="select2" name="branch_category">
                                            <option value="0" disabled selected>Seçiniz</option>
                                            @foreach($branchCategories as $category)
                                                <option value="{{ $category->id }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-1 mt-2">
                                        <span class="info-hover" title="@foreach($branchCategories as $category) # {{ $category->name }} # -> @foreach($category->userLimits as $limit) {{ $limit->group->name }}:{{ $limit->qty }}, @endforeach @endforeach" style="margin-top: 32px;display: block;"><i class="fa fa-info"></i></span> 
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Unvan <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control form-control-sm" name="unvan" placeholder="Unvan" value="{{ isset($_GET['unvan']) && $_GET['unvan'] != '' ? $_GET['unvan'] : (\Cache::has('branch_create') ? \Cache::get('branch_create')['unvan'] : '') }}" required>
                                    </div>
                                    <div class="col-xl-8 mt-2">
                                        <label class="form-label">Ortak/Yetkili</label>
                                        <select class="select2" name="partner_id[]">
                                            <option value="0">Seçilmedi</option>
                                            @foreach($owners as $owner)
                                                <option value="{{ $owner->id }}">{{ $owner->fullName() }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-4 mt-2 ortak-yuzde">
                                        <label class="form-label">Yüzde(%)</label>
                                        <input type="text" class="form-control form-control-sm" name="percent[]" placeholder="Yüzde(%)">
                                    </div>
                                    <div class="offset-xl-8 col-xl-4">
                                        <button type="button" class="btn btn-success mt-3 btn-sm ortak-ekle">Ortak Ekle</button>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="adresBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#adresBilgiler" aria-expanded="true"
                                    aria-controls="adresBilgiler">
                                Adres Bilgileri
                            </button>
                        </h2>
                        <div id="adresBilgiler" class="accordion-collapse collapse show" aria-labelledby="adresBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Mahalle</label>
                                        <input type="text" class="form-control form-control-sm" name="mahalle" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['mahalle'] : '' }}" placeholder="Mahalle">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Cadde</label>
                                        <input type="text" class="form-control form-control-sm" name="cadde" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['cadde'] : '' }}" placeholder="Cadde">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Sokak</label>
                                        <input type="text" class="form-control form-control-sm" name="sokak" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['sokak'] : '' }}" placeholder="Sokak">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Semt</label>
                                        <input type="text" class="form-control form-control-sm" name="semt" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['semt'] : '' }}" placeholder="Semt">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Lat</label>
                                        <input type="text" class="form-control form-control-sm" name="lat" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['lat'] : '' }}" placeholder="Lat">
                                    </div>
{{--                                    <div class="col-xl-6 mt-2">--}}
{{--                                        <label class="form-label">İlçe</label>--}}
{{--                                        <input type="text" class="form-control form-control-sm" name="ilce" placeholder="İlçe">--}}
{{--                                    </div>--}}
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Lng</label>
                                        <input type="text" class="form-control form-control-sm" name="lng" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['lng'] : '' }}" placeholder="Lng">
                                    </div>
{{--                                    <div class="col-xl-6 mt-2">--}}
{{--                                        <label class="form-label">İl</label>--}}
{{--                                        <input type="text" class="form-control form-control-sm" name="il" placeholder="İl">--}}
{{--                                    </div>--}}
{{--                                    <div class="col-xl-6 mt-2">--}}
{{--                                        <label class="form-label">İl Kodu</label>--}}
{{--                                        <input type="text" class="form-control form-control-sm" name="il_kodu" placeholder="İl Kodu">--}}
{{--                                    </div>--}}
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Konum</label>
                                        <input type="text" class="form-control form-control-sm" name="konum" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['konum'] : '' }}" placeholder="Konum">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="iletisimBilgiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#iletisimBilgi" aria-expanded="true"
                                    aria-controls="iletisimBilgi">
                                İletişim Bilgileri
                            </button>
                        </h2>
                        <div id="iletisimBilgi" class="accordion-collapse collapse show" aria-labelledby="iletisimBilgiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Telefon</label>
                                        <input type="text" class="form-control form-control-sm" name="telefon" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['telefon'] : '' }}" placeholder="Telefon">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Whatsapp Telefon</label>
                                        <input type="text" class="form-control form-control-sm" name="whatsapp_telefon" value="{{ \Cache::has('branch_create') && is_array(\Cache::get('branch_create')) && array_key_exists('whatsapp_telefon', \Cache::get('branch_create')) ? \Cache::get('branch_create')['whatsapp_telefon'] : '' }}" placeholder="Whatsapp Telefon">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Fax</label>
                                        <input type="text" class="form-control form-control-sm" name="fax" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['fax'] : '' }}" placeholder="Fax">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Gsm</label>
                                        <input type="text" class="form-control form-control-sm" name="gsm" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['gsm'] : '' }}" placeholder="Gsm">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Eposta</label>
                                        <input type="text" class="form-control form-control-sm" name="email" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['email'] : '' }}" placeholder="Eposta">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Web</label>
                                        <input type="text" class="form-control form-control-sm" name="web" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['web'] : '' }}" placeholder="Web">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="vergiDairesiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#vergiDairesi" aria-expanded="true"
                                    aria-controls="vergiDairesi">
                                Vergi Dairesi Bilgileri
                            </button>
                        </h2>
                        <div id="vergiDairesi" class="accordion-collapse collapse show" aria-labelledby="vergiDairesiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Vergi Dairesi</label>
                                        <input type="text" class="form-control form-control-sm" name="vergi_dairesi" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['vergi_dairesi'] : '' }}" placeholder="Vergi Dairesi">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Vergi No</label>
                                        <input type="text" class="form-control form-control-sm" name="vergi_no" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['vergi_no'] : '' }}" placeholder="Vergi No">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">T.C. Kimlik No</label>
                                        <input type="text" class="form-control form-control-sm" name="tc_no" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['tc_no'] : '' }}" placeholder="T.C. Kimlik No">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="digerBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#digerBilgiler" aria-expanded="true"
                                    aria-controls="digerBilgiler">
                                Diğer Bilgiler
                            </button>
                        </h2>
                        <div id="digerBilgiler" class="accordion-collapse collapse show" aria-labelledby="digerBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Mersis No</label>
                                        <input type="text" class="form-control form-control-sm" name="mersis_no" value="{{ \Cache::has('branch_create') && isset(\Cache::get('branch_create')['mersis_no']) ? \Cache::get('branch_create')['mersis_no'] : '' }}" placeholder="Fatura Form Adı">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Fatura Form Adı</label>
                                        <input type="text" class="form-control form-control-sm" name="fatura_form_adi" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['fatura_form_adi'] : '' }}" placeholder="Fatura Form Adı">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Makbuz Form Adı</label>
                                        <input type="text" class="form-control form-control-sm" name="makbuz_form_adi" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['makbuz_form_adi'] : '' }}" placeholder="Makbuz Form Adı">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Çek Form Adı</label>
                                        <input type="text" class="form-control form-control-sm" name="cek_form_adi" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['cek_form_adi'] : '' }}" placeholder="Çek Form Adı">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Senet Form Adı</label>
                                        <input type="text" class="form-control form-control-sm" name="senet_form_adi" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['senet_form_adi'] : '' }}" placeholder="Senet Form Adı">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">E-Fatura Key</label>
                                        <input type="text" class="form-control form-control-sm" name="efatura_key" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['efatura_key'] : '' }}" placeholder="E-Fatura Key">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Kep Mail</label>
                                        <input type="text" class="form-control form-control-sm" name="kep_mail" value="{{ \Cache::has('branch_create') && !empty(\Cache::get('branch_create')['kep_mail']) ? \Cache::get('branch_create')['kep_mail'] : '' }}" placeholder="Kep Mail">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Paynet Bayi Kodu</label>
                                        <input type="text" class="form-control form-control-sm" name="paynet_code" placeholder="Paynet Bayi Kodu">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Google Seo Button Bağlantısı</label>
                                        <input type="text" class="form-control form-control-sm" name="google_button" placeholder="Bağlantı URL Https ile Başlamalıdır.">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Fren Verileri Cihazdan Geliyor</label>
                                        <select name="brake_values_auto" class="form-control form-control-sm">
                                            <option value="0">Hayır</option>
                                            <option value="1">Evet</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Dyno Verileri Cihazdan Geliyor</label>
                                        <select name="dyno_values_auto" class="form-control form-control-sm">
                                            <option value="0">Hayır</option>
                                            <option value="1">Evet</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kayıtlı IP Adresleri (virgül ile ayırabilirsiniz)</label>
                                        <textarea class="form-control form-control-sm" name="ip_addresses">{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['ip_addresses'] : '' }}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="firmaLogoHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#firmaLogo" aria-expanded="true"
                                    aria-controls="firmaLogo">
                                Firma Logosu
                            </button>
                        </h2>
                        <div id="firmaLogo" class="accordion-collapse collapse show" aria-labelledby="firmaLogoHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <input type="file" class="dropify" data-height="250" name="logo"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="firmaSozlesmeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#firmaSozlesme" aria-expanded="true"
                                    aria-controls="firmaSozlesme">
                                Firma Sözleşme
                            </button>
                        </h2>
                        <div id="firmaSozlesme" class="accordion-collapse collapse show" aria-labelledby="firmaSozlesmeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <input type="file" class="dropify" data-height="250" name="sozlesme"/>

                                    <div class="col-12 mt-3">
                                        <label>Sözleşme Başlangıç Tarihi</label>
                                        <input type="date" name="sozlesme_baslangic_tarihi" id="sozlesme_baslangic_tarihi" class="form-control form-control-sm" value="{{ \Cache::has('branch_create') && array_key_exists('sozlesme_baslangic_tarihi', \Cache::get('branch_create')) ? \Cache::get('branch_create')['sozlesme_baslangic_tarihi'] : date('Y-m-d') }}">
                                    </div>
                                    <div class="col-12 mt-3">
                                        <label>Sözleşme Bitiş Tarihi</label>
                                        <input type="date" name="sozlesme_bitis_tarihi" id="sozlesme_bitis_tarihi" class="form-control form-control-sm" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['sozlesme_bitis_tarihi'] : date('Y-m-d', strtotime('+1 year', strtotime(date('Y-m-d')))) }}">
                                    </div>
                                    <div class="col-12 mt-3">
                                        <label>Sözleşme Yılı</label>
                                        <input type="number" min="1" name="sozlesme_yili" id="sozlesme_yili" class="form-control form-control-sm" value="{{ \Cache::has('branch_create') && array_key_exists('sozlesme_yili', \Cache::get('branch_create')) ? \Cache::get('branch_create')['sozlesme_yili'] : "1" }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="subeKarlilikOranHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#subeKarlilikOran" aria-expanded="true"
                                    aria-controls="subeKarlilikOran">
                                Bayi Karlılık Oranları
                            </button>
                        </h2>
                        <div id="subeKarlilikOran" class="accordion-collapse collapse show" aria-labelledby="subeKarlilikOranHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Bayi Hesabı</label>
                                        <input type="text" class="form-control form-control-sm" name="sube_hesabi" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['sube_hesabi'] : '' }}" placeholder="Bayi Hesabı">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Plus Card Satış O.</label>
                                        <input type="text" class="form-control form-control-sm" name="plus_kart_satis" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['plus_kart_satis'] : '' }}" placeholder="Plus Card Satış O.">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">TSE Kodu</label>
                                        <input type="text" class="form-control form-control-sm" name="tse_kodu[]" placeholder="TSE Kodu">
                                    </div>
                                    <div class="col-xl-6 mt-2 tse-tarih">
                                        <label class="form-label">TSE Geçerlilik Tarihi</label>
                                        <input type="date" class="form-control form-control-sm" name="tse_gecerlilik_tarihi[]" placeholder="TSE Geçerlilik Tarihi">
                                    </div>
                                    <button class="btn btn-dark mt-3 btn-sm tse-ekle" type="button">TSE Belgesi Ekle</button>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Eksper Merkez O.</label>
                                        <input type="text" class="form-control form-control-sm" name="eksper_merkez" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['eksper_merkez'] : '' }}" placeholder="Eksper Merkez O.">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Eksper Oranı</label>
                                        <input type="text" class="form-control form-control-sm" name="eksper_orani" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['eksper_orani'] : '' }}" placeholder="Eksper Oranı">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Hasar Sorgulama</label>
                                        <select name="hasar_sorgulama" class="form-control form-control-sm">
                                            <option value="1">Ototramer Sorgula</option>
                                            <option  value="0">Yok</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Plus Card Yükler</label>
                                        <select name="plus_kart_yukle" class="form-control form-control-sm">
                                            <option value="1">Evet</option>
                                            <option value="0">Hayır</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Son Fiyat Değişiklik Tarihi</label>
                                        <input type="date" class="form-control form-control-sm" name="son_fiyat_degisiklik_tarihi" placeholder="Son Fiyat Değişiklik" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['son_fiyat_degisiklik_tarihi'] : date('Y-m-d') }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Bedelsiz Eksper</label>
                                        <input type="text" class="form-control form-control-sm" name="bedelsiz_eksper" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['bedelsiz_eksper'] : '' }}" placeholder="Bedelsiz Eksper">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Max İndirim Tipi</label>
                                        <select class="form-control form-control-sm iskonto_type" name="iskonto_tip" >
                                            <option {{ \Cache::has('branch_create') && !empty(\Cache::get('branch_create')['iskonto_tip']) && \Cache::get('branch_create')['iskonto_tip'] == 1  ? 'selected=""' : '' }} value="1">Miktar</option>
                                            <option  {{ \Cache::has('branch_create') && !empty(\Cache::get('branch_create')['iskonto_tip']) && \Cache::get('branch_create')['iskonto_tip'] == 2  ? 'selected=""' : '' }} selected="" value="2">Yüzde</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2 iskonto_tip_percentage">
                                        <label class="form-label">Max İndirim Oranı</label>
                                        <input type="text" class="form-control form-control-sm" name="max_indirim_orani" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['max_indirim_orani'] : '' }}" placeholder="Max İndirim Oranı">
                                    </div>
                                    <div class="col-xl-6 mt-2 d-none iskonto_tip_amount">
                                        <label class="form-label">Max İndirim Tutarı</label>
                                        <input type="text" class="form-control form-control-sm" name="max_indirim_tutari" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['max_indirim_tutari'] : '' }}" placeholder="Max İndirim Tutarı">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Borç Risk Tutarı</label>
                                        <input type="text" class="form-control form-control-sm" name="borc_risk_tutari" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['borc_risk_tutari'] : '' }}" placeholder="Borç Risk Tutarı">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Cari Başı Max. İndirim</label>
                                        <input type="text" class="form-control form-control-sm" name="cari_basi_max_indirim" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['cari_basi_max_indirim'] : '' }}" placeholder="Cari Başı Max. İndirim">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="yetkiliBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#yetkiliBilgiler" aria-expanded="true"
                                    aria-controls="yetkiliBilgiler">
                                Yetkili Bilgileri
                            </button>
                        </h2>
                        <div id="yetkiliBilgiler" class="accordion-collapse collapse show" aria-labelledby="yetkiliBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Ad Soyad</label>
                                        <input type="text" class="form-control form-control-sm" name="yetkili_ad_soyad" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['yetkili_ad_soyad'] : '' }}" placeholder="Ad Soyad">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Gsm</label>
                                        <input type="text" class="form-control form-control-sm" name="yetkili_gsm" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['yetkili_gsm'] : '' }}" placeholder="Gsm">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Dahili</label>
                                        <input type="text" class="form-control form-control-sm" name="yetkili_dahili" value="{{ \Cache::has('branch_create') ? \Cache::get('branch_create')['yetkili_dahili'] : '' }}" placeholder="Dahili">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control form-control-sm">
                                            <option value="1">Aktif</option>
                                            <option value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
        <input type="hidden" name="session_name" value="branch_create">
    </form>
    @if(\Illuminate\Support\Facades\Cache::has('branch_create'))
        <div class="modal fade" id="deleteCacheModal" tabindex="-1"
             aria-labelledby="deleteCacheModal" data-bs-keyboard="false"
             aria-hidden="true">
            <!-- Scrollable modal -->
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="staticBackdropLabel2">Mevcut Kayıt Bulundu!
                        </h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Önceden oluşturduğunuz kayıt kapanmadı!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success"
                                data-bs-dismiss="modal">Kayıta Devam Et</button>
                        <button type="submit" form="deleteCache" class="btn btn-danger">Yeni Kayıt Oluştur</button>
                        <form id="deleteCache" method="post" action="{{ route('deleteCache') }}">@csrf<input type="hidden" name="type" value="branch_create"></form>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script src="/assets/js/dropify.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script src="/assets/js/imask.js"></script>

    <script>
        if(document.querySelector('input[name="whatsapp_telefon"]')){
            const maskTel = IMask(document.querySelector('input[name="whatsapp_telefon"]'), {
                mask: '(0000) 000 0000'
            });
        }
        addEventListener("DOMContentLoaded", (event) => {
            @if(\Illuminate\Support\Facades\Cache::has('branch_create'))
                $('#deleteCacheModal').modal('toggle')
            @endif
        });

        var drEvent = $('.dropify').dropify({
            messages: {
                'default': 'Dosya Sürükle veya Tıkla',
                'replace': 'Dosya Sürükle veya Tıkla',
                'remove':  'Kaldır',
                'error':   'Bir Hata Ortaya Çıktı'
            }
        });

        drEvent.on('dropify.beforeClear', function(event, element){
            console.log(event,element)
            return confirm("Do you really want to delete \"" + element.file.name + "\" ?");
        });

        $(".select2").select2({
            placeholder: "Ara.."
        });

        $('.ortak-ekle').on('click',function (){
            let html = '<div class="col-xl-8 mt-2"><label class="form-label">Ortak/Yetkili</label>' +
            '<select class="select2" name="partner_id[]"> <option value="0">Seçilmedi</option>' +
                @foreach($owners as $owner)
                '<option value="{{ $owner->id }}">{{ $owner->name }}</option>' +
                @endforeach
            '</select></div> <div class="col-xl-4 mt-2"> <label class="form-label">Yüzde(%)</label>' +
               ' <input type="text" class="form-control form-control-sm" name="percent[]" placeholder="Yüzde(%)"> </div>'

            $('.ortak-yuzde').after(html)
            $(".select2").select2({
                placeholder: "Ara.."
            });

            $('input[name="percent[]"]').on('keyup',function (){
                sumPartnerPercent()
            })
        })

        $('.tse-ekle').on('click',function (){
            let html = '<div class="col-xl-6 mt-2"><label class="form-label">TSE Kodu</label>' +
            '<input type="text" class="form-control form-control-sm" name="tse_kodu[]" placeholder="TSE Kodu"></div>' +
            '<div class="col-xl-6 mt-2"><label class="form-label">TSE Geçerlilik Tarihi</label>' +
            '<input type="date" class="form-control form-control-sm" name="tse_gecerlilik_tarihi[]" placeholder="TSE Geçerlilik Tarihi"></div>'

            $('.tse-tarih').after(html)
        })

        $('input[name="percent[]"]').on('keyup',function (){
            sumPartnerPercent()
        })
        function sumPartnerPercent(){
            let total = 0;
            $('input[name="percent[]"]').each(function (){
                total += parseInt($(this).val())
            })
            if (total > 100){
                alert("Toplam 100'ü Geçiyor! ("+total+")")
            }
        }

        $('select[name="il_kodu"]').on('change',function (){
            let $val = $(this).val()
            $('select[name="ilce_kodu"]').find('option').remove()
            if($val > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$val} ,
                    success: function (response) {
                        if (response.items.length > 0){
                            $.each(response.items,function (index,item){
                                $('select[name="ilce_kodu"]').append('<option value="'+item.ilce_id+'">'+item.ilce_title+'</option>')
                            })
                            $('select[name="ilce_kodu"]').trigger('change')
                        }
                    }
                });
            }
        })

        addEventListener("DOMContentLoaded", (event) => {
            $('select[name="il_kodu"]').trigger('change')
        });

        setInterval(function (){
            $.ajax({
                url: "{{ route('saveToSession') }}",
                type: "post",
                data: $('#mainForm').serialize(),
                success: function (response) {

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        },30 * 1000)

        $('.iskonto_type').change('on',function(){
            var value = $(this).val();
            if(value == 1){
                $('.iskonto_tip_percentage').addClass('d-none')
                $('.iskonto_tip_amount').removeClass('d-none')
            }
            if(value == 2){
                $('.iskonto_tip_percentage').removeClass('d-none')
                $('.iskonto_tip_amount').addClass('d-none')
            }
        })
        $('#sozlesme_yili').change(function(){
            calc_sozlesme(1);
        })
        $('#sozlesme_bitis_tarihi').change(function(){
            calc_sozlesme(2);
        })

        function calc_sozlesme(type){
            var start_date = $('#sozlesme_baslangic_tarihi').val();
            var finish_date = $('#sozlesme_bitis_tarihi').val();
            var sozlesme_yili = $('#sozlesme_yili').val();
            if(type == 1){
                var start_date = $('#sozlesme_baslangic_tarihi').val();
                var sozlesme_yili = parseInt($('#sozlesme_yili').val(), 10);

                var startDate = new Date(start_date);

                startDate.setFullYear(startDate.getFullYear() + sozlesme_yili);

                var finish_date = startDate.toISOString().split('T')[0];

                $('#sozlesme_bitis_tarihi').val(finish_date);

            }else{
                startDate = new Date(start_date)
                finishDate = new Date(finish_date)
                sozlesme_yili = finishDate.getFullYear() - startDate.getFullYear();
                if(sozlesme_yili > 0){
                    $('#sozlesme_yili').val(sozlesme_yili)
                }

            }
        }
    </script>
@endpush
