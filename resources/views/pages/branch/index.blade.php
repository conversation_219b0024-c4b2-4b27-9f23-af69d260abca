@extends('pages.build')
@section('title','Bayiler')
@push('css')
   <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
   <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('excel')
    @if(in_array('*', $userRoles) || in_array('download_excel_branch', $userRoles))
        <a href="javascript:void(0);" class="btn btn-sm btn-success export-btn">Excel Olarak İndir <i class="fa fa-file-excel"></i> </a>
    @endif
    @if(in_array('*', $userRoles) || in_array('upload_excel_branch', $userRoles))
        <a href="{{ route('importExcel',['type'=>'branch']) }}" class="btn btn-sm btn-danger">Excelden Aktar <i class="fa fa-file-excel"></i> </a>
    @endif
@endsection
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form id="getSavedFilter" method="get" action="{{ route('branches.index') }}"></form>
                <form method="get" action="{{ route('branches.index') }}">
                    <div class="card-body">
                        <div class="row">

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Unvan</label>
                                    <input type="text" class="form-control form-control-sm" name="unvan" value="{{ $filters['unvan'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Kısa Ad</label>
                                    <input type="text" class="form-control form-control-sm" name="kisa_ad" value="{{ $filters['kisa_ad'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label">Kategori</label>
                                    <select name="branch_category" class="select2">
                                        <option value="0">Seçilmedi</option>
                                        @foreach($branchCategories as $category)
                                            <option @if($filters['branch_category'] == $category->id) selected @endif value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Bayi Kodu</label>
                                    <input type="number" min="1" class="form-control form-control-sm" name="kod" value="{{ $filters['kod'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Hizmet Verdiği İl</label>
                                <select name="il_kodu" class="select2">
                                    <option value="0">Seçilmedi</option>
                                    @foreach($cities as $city)
                                        <option @if($filters['il_kodu'] == $city->id ) selected @endif value="{{ $city->id }}">{{ $city->title }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">İlçe</label>
                                <select name="ilce_kodu" class="select2">
                                    <option value="0">Seçilmedi</option>
                                    @if($filters['ilce_kodu'] > 0)
                                        <option selected value="{{ $filters['ilce_kodu'] }}">{{ \App\Models\Town::where('ilce_id', $filters['ilce_kodu'])->first()->title }}</option>
                                    @endif
                                </select>
                            </div>
                            @if($authUser->type == 'admin' || $authUser->zone_id > 0)
                                <div class="col-md-3">
                                    <label class="form-label">Bölge</label>
                                    <select name="zone_id" class="form-control form-control-sm">
                                        <option value="">Hepsi</option>
                                        @foreach($zones as $zone)
                                            <option @if($filters['zone_id'] == $zone->id) selected="" @endif value="{{ $zone->id }}">{{ $zone->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            @endif
                            <div class="col-md-3">
                                <label class="form-label">Durum</label>
                                <select name="status" class="form-control form-control-sm">
                                    <option value="">Hepsi</option>
                                    <option @if($filters['status'] == 1) selected="" @endif value="1">Aktif</option>
                                    <option @if($filters['status'] == 2) selected="" @endif value="2">Pasif</option>

                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label class="form-label"><small>Kayıtlı Şablonlar</small></label>
                                    <select form="getSavedFilter" onchange="$('#getSavedFilter').submit()" class="form-control select2" name="saved_filter_id">
                                        <option value="0">Seçilmedi</option>
                                        @foreach($savedFilters as $savedFilter)
                                            <option @if($filters['saved_filter_id'] == $savedFilter->id) selected @endif value="{{ $savedFilter->id }}">{{ $savedFilter->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col mt-4">
                                <button class="btn btn-success btn-sm mt-1">Listele</button>
                                <button type="button" class="btn btn-sm btn-danger mt-1 save-filter-btn" >Aramayı Kaydet</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                @if(in_array('*', $userRoles) || in_array('add_branch', $userRoles))
                    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                        <a href="{{ route('branches.create') }}" class="nav-link">Kayıt Ekle</a>
                        <a href="{{ route('branches.index') }}" class="nav-link active">Bayi Listesi</a>
                    </nav>
                @endif
                <div class="card-body">

                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                            <tr>
                                <th onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                                <th>Bayi Kodu</th>
                                <th>Kısa Ad</th>
                                <th>Kategori</th>
                                <th>Bölge</th>
                                <th>Unvan</th>
                                <th>İlçe</th>
                                <th>İl</th>
                                <th>Belge Kodu</th>
                                <th>Dahili No</th>
                                <th>GoogleSEO Bakiye</th>
                                <th>Durum</th>
                                <th>İşlemler</th>
                            </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="saveFilter" tabindex="-1"
         aria-labelledby="saveFilter" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Filtre Şablonu Oluştur
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Şablon Adı</small></label>
                            <input type="text" class="form-control form-control-sm" name="saved_filter_name">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger save-filter">Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
       <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>

        $('select[name="il_kodu"]').on('change',function (){
            let $val = $(this).val()
            $('select[name="ilce_kodu"]').find('option').remove()
            if($val > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$val} ,
                    success: function (response) {
                        if (response.items.length > 0){
                            $('select[name="ilce_kodu"]').append('<option value="">Lütfen seçim yapınız.</option>')

                            $.each(response.items, function (index, item) {
                                if (item.ilce_id == "{{ request()->input('ilce_kodu') }}"){
                                    $('select[name="ilce_kodu"]').append('<option value="' + item.ilce_id + '" selected>' + item.ilce_title + '</option>')
                                } else {
                                    $('select[name="ilce_kodu"]').append('<option value="' + item.ilce_id + '">' + item.ilce_title + '</option>')
                                }
                            });

                            $('select[name="ilce_kodu"]').trigger('change')
                        }
                    }
                });
            }
        })

        addEventListener("DOMContentLoaded", (event) => {
            $('select[name="il_kodu"]').trigger('change')
        });

        $(".select2").select2({
            placeholder: "Ara.."
        });
    </script>
    <script>
        // Datatable
        $(function () {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({
                "dom": '<"pull-left"f><"pull-right"l>rtip',// Allocate the search bar at the left side
                "stateSave": true, // save the current state of the table
                "scrollX": true,// Enable horizontal scrolling
                "ordering": true,// Enable sorting
                "aaSorting": [],// Set the initial sorting order
                "pageLength": 10,// Set the default number of rows per page
                "processing": true,// Enable processing spinner
                "serverSide": true,// Enable server-side processing
                "searching": true,// Enable search
                "ajax": {
                    "url": "{{ route('bayiListAjax', $filters) }}",
                    "dataType": "json",
                    "type": "POST",
                    "data": function (d) {
                        d._token = "{{ csrf_token() }}";
                        d.filter_id = $('select[name="saved_filter_id"]').val();
                        d.name = $('input[name="saved_filter_name"]').val();
                        d.unvan = $('input[name="unvan"]').val();
                        d.kod = $('input[name="kod"]').val();
                        d.kisa_ad = $('input[name="kisa_ad"]').val();
                        d.branch_category = $('select[name="branch_category"]').val();
                        d.il_kodu = $('select[name="il_kodu"]').val();
                        d.ilce_kodu = $('select[name="ilce_kodu"]').val();
                        d.zone_id = $('select[name="zone_id"]').val();
                        d.status = $('select[name="status"]').val();
                    },
                    "dataSrc": "data"
                },
                "columns": [
                    { "data": "id", "title": "Sütunlar" },
                    { "data": "kod", "title": "Bayi Kodu" },
                    { "data": "kisa_ad", "title": "Kısa Ad" },
                    { "data": "kategori", "title": "Kategori" },
                    { "data": "bolge", "title": "Bölge" },
                    { "data": "unvan", "title": "Unvan" },
                    { "data": "ilce", "title": "İlçe" },
                    { "data": "il", "title": "İl" },
                    { "data": "belge_kodu", "title": "Belge Kodu" },
                    { "data": "dahili_no", "title": "Dahili No" },
                    { "data": "google_buttons", "title": "GoogleSEO Bakiye", "orderable": false },
                    { "data": "status", "title": "Durum" },
                    { "data": "actions", "title": "İşlemler", "orderable": false }
                ],
                "language": {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı!",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı!",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                },
            });

            // Open column settings modal when clicking "Sütunlar"
            $('#openColumnsModal').on('click', function () {
                $('#setColumns').modal('toggle');

                // Sync checkboxes with currently visible table headers
                $('.column-show-hide').each(function () {
                    let columnIndex = $(this).val();
                    let column = responsiveDatatable.column(columnIndex);
                    $(this).prop('checked', column.visible());
                });
            });

            // Generate checkboxes dynamically for column visibility
            var columns = responsiveDatatable.columns().header().toArray();
            let columnsHtml = '<div class="row">';

            // "Tümü" (Select All) checkbox
            columnsHtml += `
                <div class="col-xl-4 mt-2 form-check-sm">
                    <label class="form-label" for="column0"><i class="fe fe-corner-down-right"></i> Tümü</label>
                    <input type="checkbox" class="form-check-input column-show-hide" id="column0" value="0">
                </div>`;

            // Generate checkboxes for each column (excluding "Sütunlar")
            columns.forEach(function (column, index) {
                if (index !== 0) {
                    columnsHtml += `
                        <div class="col-xl-4 mt-2 form-check-sm">
                            <label class="form-label" for="column${index}"><i class="fe fe-corner-down-right"></i> ${$(column).text()}</label>
                            <input type="checkbox" class="form-check-input column-show-hide" id="column${index}" value="${index}">
                        </div>`;
                }
            });

            columnsHtml += "</div>";
            $('.datatable-columns').html(columnsHtml);

            // Sync checkboxes with visible columns on page load
            $('.column-show-hide').each(function () {
                let columnIndex = $(this).val();
                let column = responsiveDatatable.column(columnIndex);
                $(this).prop('checked', column.visible());
            });

            // Toggle column visibility and update checkboxes
            $('.column-show-hide').on('click', function () {
                let columnIndex = $(this).val();

                if (columnIndex == 0) { // If "Tümü" checkbox is clicked
                    let isChecked = $(this).prop('checked');

                    // Toggle all checkboxes except "Sütunlar"
                    $('.column-show-hide').not('#column0').each(function () {
                        let colIndex = $(this).val();
                        $(this).prop('checked', isChecked);
                        responsiveDatatable.column(colIndex).visible(isChecked);
                    });

                } else { // Toggle individual column visibility
                    let column = responsiveDatatable.column(columnIndex);
                    let newState = !column.visible();
                    column.visible(newState);
                    $(this).prop('checked', newState);
                }
            });
        });

        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }


        $('.save-filter').on('click',function (){
            if ($('input[name="saved_filter_name"]').val() != ''){
                $.ajax({
                    url: "{{ route('saveFilters') }}",
                    type: "post",
                    data: {
                        '_token':'{{ csrf_token() }}',
                        'filter_id': $('select[name="saved_filter_id"]').val(),
                        'name':$('input[name="saved_filter_name"]').val(),
                        'unvan':$('input[name="unvan"]').val(),
                        'kod':$('input[name="kod"]').val(),
                        'kisa_ad':$('input[name="kisa_ad"]').val(),
                        'branch_category':$('select[name="branch_category"]').val(),
                        'il_kodu':$('select[name="il_kodu"]').val(),
                        'ilce_kodu':$('select[name="ilce_kodu"]').val(),
                        'type':'branch'
                    } ,
                    success: function (response) {
                        if (response.success == 'true') {
                            Toast.fire({
                                icon: "success",
                                title: "Şablon Kayıt Edildi"
                            });
                            $('select[name="saved_filter_id"]').append('<option value="'+response.id+'" selected="selected">'+response.name+'</option>');
                            $('#getSavedFilter').submit()
                        } else {
                            Toast.fire({
                                icon: "error",
                                title: "Şablon Kayıt Edilemedi"
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Şablon Adı Girilmedi"
                });
            }
        })

        $('.save-filter-btn').on('click', function () {
            let filterName = '';

            if (parseInt($('select[name="saved_filter_id"]').val()) > 0) {
                filterName = $('select[name="saved_filter_id"] option:selected').text();
            }

            $('input[name="saved_filter_name"]').val(filterName);

            $('#saveFilter').modal('toggle');
        });

        $('.export-btn').on('click',function (){
            let filterData = {
                'filter_id': $('select[name="saved_filter_id"]').val(),
                'name': $('input[name="saved_filter_name"]').val(),
                'unvan': $('input[name="unvan"]').val(),
                'kod': $('input[name="kod"]').val(),
                'kisa_ad': $('input[name="kisa_ad"]').val(),
                'branch_category': $('select[name="branch_category"]').val(),
                'il_kodu': $('select[name="il_kodu"]').val(),
                'ilce_kodu': $('select[name="ilce_kodu"]').val(),
                'zone_id': $('select[name="zone_id"]').val(),
                'status': $('select[name="status"]').val()
            };

            let queryString = $.param(filterData);

            window.location.href = "/excel/subeler?" + queryString;
        });
    </script>
@endpush
