<!DOCTYPE html>
<html lang="{{ config('app.locale') }}">
<head>

    <meta charset="UTF-8">
    <meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=0'>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>En Yakın Umran</title>


    <!-- Favicon -->
    <link rel="icon" href="/assets/logo-icon.png" type="image/x-icon">

    <!-- Main Theme Js -->
    <script src="/assets/js/authentication-main.js"></script>

    <!-- Bootstrap Css -->
    <link id="style" href="/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet" >

    <!-- Style Css -->
    <link href="/assets/css/styles.min.css" rel="stylesheet" >

    <!-- Icons Css -->
    <link href="/assets/css/icons.min.css" rel="stylesheet" >
    <style>
        .warning-text{
            background-color: #ff1000;
            padding: 10px 20px;
            height: 40vh;
            display: flex;
            align-items: center;
            color: #fff;
            border-bottom-right-radius: 223px;
        }
        .warning-icon{
            height: 55vh;
            display: flex;
            align-items: center;
            font-size: 15rem;
            justify-content: center;
        }
        .title{
            padding: 10px;
            background: #e30614;
            color: #fff;
            text-align: center;
            font-weight: bolder;
        }
        .title i{
            position: absolute;
            left: 1rem;
        }
        .location-info{
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 15px 0;
            height: 17vh;
        }
        .location-info i{
            font-size: 3rem;
            color: #e30614;
            flex: 2;
            text-align: center;
        }
        .location-sub{
            display: flex;
            flex-direction: column;
            flex: 3;
        }
        .city{
            font-weight: bold;
        }
        .location-buttons{
            display: flex;
            justify-content: space-around;
            align-items: center;
            height: 10vh;
        }
        .location-button{
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .location-button i{
            font-size: 2rem;
            color: #e30614;
        }
        .location-button a{
            margin-top: 10px;
        }
    </style>
    <script src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAP_API_KEY') }}&libraries=places" async defer></script>
</head>
<body class="ltr error-page1 ">
<div class="row">
    <div class="title">
        <i onclick="window.location.href = '{{ route('landing') }}'" class="fa fa-bars"></i>
        <span>En Yakın Umran</span>
    </div>
    <div class="col-12">
        <div class="need-location">
            <p class="warning-text uyari">Umran Oto yakınlarınızdaki şubelerini size gösterebilmek için konumunuza erişim izni istiyor. Bu özelliği kullanabilmek için konum servislerinin aktif olması ve gerekli izinleri vermeniz gerekmektedir.</p>
            <div class="warning-icon">
                <i class="fa fa-search-location"></i>
            </div>
        </div>
        <div class="location-finded d-none">
            <div class="location-info">
                <i class="fe fe-map"></i>
                <div class="location-sub">
                    <span class="city"></span>
                    <span class="yetkili"></span>
                    <span class="sabit"></span>
                    <span class="adres"></span>
                </div>
            </div>
            <div class="location-buttons">
                <div class="location-button">
                    <i class="fa fa-arrow-right"></i>
                    <a class="location-konum">Yol Tarifi</a>
                </div>
                <div class="location-button">
                    <i class="fa fa-calendar"></i>
                    <a href="{{ route('customer.booking') }}">Randevu Al</a>
                </div>
                <div class="location-button">
                    <i class="fa fa-phone"></i>
                    <a class="sabit2"></a>
                </div>
                <div class="location-button">
                    <i class="fa fa-mobile"></i>
                    <a class="cep">Cep</a>
                </div>
            </div>
            <div id="map" style="height: 68vh;width: 100%"></div>
        </div>
    </div>
</div>
<!-- Popper JS -->
<script src="/assets/libs/@popperjs/core/umd/popper.min.js"></script>

<!-- Bootstrap JS -->
<script src="/assets/libs/bootstrap/js/bootstrap.bundle.min.js"></script>


<!-- Node Waves JS-->
<script src="/assets/libs/node-waves/waves.min.js"></script>


<!-- Color Picker JS -->
<script src="/assets/libs/@simonwep/pickr/pickr.es5.min.js"></script>

<script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="/assets/js/<EMAIL>"></script>

<script>
    var map;

    document.addEventListener("DOMContentLoaded", (event) => {

        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });

        @if(session('success'))
        Toast.fire({
            icon: "success",
            title: "{{ session('success') }}"
        });
        @endif
        @if(session('error'))
        Toast.fire({
            icon: "error",
            title: "{{ session('error') }}"
        });
        @endif

        var enKisaMesafe = Infinity;

        function bulEnYakinSube(kullaniciKonumu, subeListesi) {
            var enYakinSube;


            for (var i = 0; i < subeListesi.length; i++) {
                var sube = subeListesi[i];
                var mesafe = hesaplaMesafe(kullaniciKonumu, sube);

                if (mesafe < enKisaMesafe) {
                    enKisaMesafe = mesafe;
                    enYakinSube = sube;
                }
            }

            return enYakinSube;
        }

// İki nokta arasındaki mesafeyi hesaplayan fonksiyon (Haversine formülü kullanılmıştır)
        function hesaplaMesafe(konum1, konum2) {
            var R = 6371; // Dünya yarıçapı, km cinsinden
            var dLat = dereceToRadyan(konum2.lat - konum1.lat);
            var dLng = dereceToRadyan(konum2.lng - konum1.lng);
            var a =
                Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(dereceToRadyan(konum1.lat)) * Math.cos(dereceToRadyan(konum2.lat)) *
                Math.sin(dLng / 2) * Math.sin(dLng / 2);
            var c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
            var distance = R * c; // Mesafe, km cinsinden
            return distance;
        }

// Dereceyi radyana çeviren fonksiyon
        function dereceToRadyan(degrees) {
            return degrees * (Math.PI / 180);
        }



        function markSube(sube) {
            var marker = new google.maps.Marker({
                position: sube,
                map: map,
                title: sube.kisa_ad
            });

            document.querySelector('.city').innerHTML = sube.city  + ' - ' + enKisaMesafe.toFixed(2) + 'KM';
            document.querySelector('.yetkili').innerHTML = 'Yetkili: ' + sube.yetkili + '-' + sube.yetkili_dahili;
            document.querySelector('.sabit').innerHTML = 'Sabit: ' + sube.yetkili_gsm;
            document.querySelector('.sabit2').href = 'tel:'+sube.telefon;
            document.querySelector('.sabit2').innerHTML = sube.telefon;
            document.querySelector('.cep').href = 'tel:'+sube.gsm;
            document.querySelector('.cep').innerHTML = sube.gsm;
            document.querySelector('.adres').innerHTML = 'Adres: ' + sube.adres;
            document.querySelector('.location-konum').href = sube.konum
        }

        if (navigator.geolocation) {
            if (confirm("Umran Oto yakınlarınızdaki şubelerini size gösterebilmek için konumunuza erişim izni istiyor.")){
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        var userLocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude
                        };

                        map = new google.maps.Map(document.getElementById("map"), {
                            center: userLocation,
                            zoom: 12
                        });

                        var subeler = [
                                @foreach($branches as $branch)
                                @if($branch->lat && $branch->lng)
                            { lat: {{ $branch->lat }},
                                lng: {{ $branch->lng }},
                                'kisa_ad':'{{ $branch->kisa_ad }}',
                                'city':'{{ $branch->getCity ? $branch->getCity->title : '' }}',
                                'yetkili':'{{ trim($branch->yetkili_ad_soyad) }}',
                                'yetkili_dahili':'{{ trim($branch->yetkili_dahili) }}',
                                'yetkili_gsm':'{{ trim($branch->yetkili_gsm) }}',
                                'adres':'{{ $branch->mahalle . ' ' . $branch->cadde . ' ' . $branch->sokak . ' ' . $branch->semt }}',
                                'konum':'{{ $branch->konum }}',
                                'telefon':'{{ $branch->telefon }}',
                                'gsm':'{{ $branch->gsm }}',
                            },
                            @endif
                            @endforeach
                        ];

                        var enYakinSube = bulEnYakinSube(userLocation, subeler);

// Kullanıcıya en yakın bayi bilgisini kullanabilirsiniz.
                        console.log(enYakinSube);


                        markSube(enYakinSube);

                        document.querySelector('.need-location').classList.add('d-none')
                        document.querySelector('.location-finded').classList.remove('d-none')
                    },
                    function(error) {
                        switch (error.code) {
                            case error.PERMISSION_DENIED:
                                Toast.fire({
                                    icon: "error",
                                    title: "Konum izni verilmedi. En yakın bayi bulma işlemi için konum izni gereklidir."
                                });
                                break;
                            case error.POSITION_UNAVAILABLE:
                                Toast.fire({
                                    icon: "error",
                                    title: "Konum bilgisi alınamıyor. Lütfen başka bir konum deneyin."
                                });
                                break;
                            case error.TIMEOUT:
                                Toast.fire({
                                    icon: "error",
                                    title: "Konum bilgisi almak için zaman aşımına uğrundu. Lütfen tekrar deneyin."
                                });
                                break;
                            case error.UNKNOWN_ERROR:
                                Toast.fire({
                                    icon: "error",
                                    title: "Bilinmeyen bir hata oluştu. Lütfen tekrar deneyin."
                                });
                                break;
                        }
                    }
                );
            }

        } else {
            Toast.fire({
                icon: "error",
                title: "Tarayıcı konum özelliğini desteklemiyor."
            });
        }
    });
</script>

</body>
</html>
