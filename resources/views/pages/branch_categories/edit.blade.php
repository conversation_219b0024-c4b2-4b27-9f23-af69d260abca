@extends('pages.build')
@section('title','<PERSON><PERSON><PERSON>')
@push('css')
<link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
<link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
<link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('content')
<form action="{{ route('branch-categories.update', $branchCategory->id) }}" method="POST">
    @csrf 
    @method('PUT')
    <div class="row">
        <div class="col-md-4">
            <div class="accordion" id="accordionPanelsStayOpenExample">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="categoryInfoHeading">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#categoryInfo" aria-expanded="true"
                                aria-controls="categoryInfo">
                            Kategori Bilgileri
                        </button>
                    </h2>
                    <div id="categoryInfo" class="accordion-collapse collapse show" aria-labelledby="categoryInfoHeading">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-xl-12">
                                    <label class="form-label">Kategori Adı <span class="text-danger">*</span></label>
                                    <input class="form-control form-control-sm" type="text" name="name" value="{{ $branchCategory->name }}" required>
                                </div>
                                <div class="col-12 mt-2">
                                    <label class="form-label">Durum</label>
                                    <select name="status" class="form-control">
                                        <option value="1" {{ $branchCategory->status ? 'selected' : '' }}>Aktif</option>
                                        <option value="0" {{ !$branchCategory->status ? 'selected' : '' }}>Pasif</option>
                                    </select>
                                </div>
                                <div class="col-12 mt-2">
                                    <label class="form-label">Sıralama</label>
                                    <input class="form-control form-control-sm" type="number" name="sort" value="{{ $branchCategory->sort }}" required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="accordion" id="accordionEmployee">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="employeeInfoHeading">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#employeeInfo" aria-expanded="true"
                                aria-controls="employeeInfo">
                            Personel Bilgileri
                        </button>
                    </h2>
                    <div id="employeeInfo" class="accordion-collapse collapse show" aria-labelledby="employeeInfoHeading">
                        <div class="accordion-body">
                            <div class="row">
                                <table id="employeeResponsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th style="width:1%">No</th>
                                            <th>Görev</th>
                                            <th style="width:5%">Personel Sayısı</th>
                                            <th>Durum</th>
                                            <th>Sıralama</th>
                                            <th>Eklenme Tarihi</th>
                                            <th>Güncellenme Tarihi</th>
                                            <th>İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($branchCategory->userLimits as $key => $userLimit)
                                        <tr>
                                            <td>{{ $userLimit->id }}</td>
                                            <td>{{ $userLimit->group->name }}</td>
                                            <td>{{ $userLimit->qty }}</td>
                                            @if($userLimit->status)
                                                <td><span class="badge bg-success-transparent">Aktif</span></td>
                                            @else
                                                <td><span class="badge bg-light text-dark">Pasif</span></td>
                                            @endif
                                            <td>{{ $userLimit->sort }}</td>
                                            <td>{{ $userLimit->created_at->format('d.m.Y H:i:s') }}</td>
                                            <td>{{ $userLimit->updated_at->format('d.m.Y H:i:s') }}</td>
                                            <td>
                                                @if(in_array('*', $userRoles) || in_array('edit_branch', $userRoles))
                                                    <a class="text-info fs-14 lh-1 edit-user-limit modal-effect" href="javascript:void(0)" data-bs-effect="effect-rotate-right" data-bs-toggle="modal" data-bs-target="#userLimitModal"
                                                       data-id="{{ $userLimit->id }}" data-url="{{ route('branch-category-user-limits.update', $userLimit) }}"
                                                       data-group_id="{{ $userLimit->group->id }}" data-qty="{{ $userLimit->qty }}" data-status="{{ $userLimit->status }}" data-sort="{{ $userLimit->sort }}">
                                                        <i class="ri-edit-line"></i>
                                                    </a>
                                                @endif
                                                @if(in_array('*', $userRoles) || in_array('edit_branch', $userRoles))
                                                    <a data-id="{{ $userLimit->id }}" data-url="{{ route('branch-category-user-limits.destroy', $userLimit) }}" class="text-danger fs-14 lh-1 remove-user-limit" href="javascript:void(0)"><i class="ri-delete-bin-5-line"></i></a>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                    <tfoot id="employeeRow" style="display: none;">
                                        <tr>
                                            <td></td>
                                            <td>
                                                <select disabled name="userLimit[0][group_id]" class="form-control form-control-sm select2">
                                                    @foreach($groups as $group)
                                                        <option value="{{ $group->id }}">{{ $group->name }}</option>
                                                    @endforeach
                                                </select>
                                            </td>
                                            <td>
                                                <input disabled type="text" class="form-control form-control-sm" name="userLimit[0][qty]">
                                            </td>
                                            <td>
                                                <select disabled name="userLimit[0][status]" class="form-control form-control-sm">
                                                    <option value="1">Aktif</option>
                                                    <option value="0">Pasif</option>
                                                </select>
                                            </td>
                                            <td>
                                                <input disabled type="text" class="form-control form-control-sm" name="userLimit[0][sort]">
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-danger btn-sm remove-user-limit">Sil</button>
                                            </td>
                                            <td></td>
                                            <!-- <th>İşlemler</th> -->
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <div class="row">
                                <div class="col-12 mt-2">
                                    <button type="button" class="btn btn-sm btn-info add-user-limit">Personel Limiti Ekle</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12 mt-2">
            <button type="submit" class="btn btn-success mb-3">Kaydet</button>
        </div>
    </div>
</form>
<div class="modal fade" id="userLimitModal" tabindex="-1" aria-labelledby="userLimitModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userLimitModalLabel">Personel Limiti</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editForm" method="POST">
            @csrf
            <div class="modal-body">
                <div class="row">   
                    <div class="col-12">
                        <div class="form-group">
                            <label>Grup</label>
                            <select name="userLimit[group_id]" class="form-control form-control-sm select2">
                                @foreach($groups as $group)
                                    <option value="{{ $group->id }}">{{ $group->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label>Limit</label>
                            <input type="text" class="form-control form-control-sm" name="userLimit[qty]">
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label>Sıralama</label>
                            <input type="text" class="form-control form-control-sm" name="userLimit[sort]">
                        </div>
                    </div>  
                    <div class="col-12">
                        <div class="form-group">
                            <label>Durum</label>
                            <select name="userLimit[status]" class="form-control form-control-sm">
                                <option value="1">Aktif</option>
                                <option value="0">Pasif</option>
                            </select>
                        </div>
                    </div>
                    <input type="hidden" name="userLimit[id]">
                    <input type="hidden" name="userLimit[url]">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                <button type="button" class="btn btn-success save-user-limit">Kaydet</button>
            </div>
            </form>
        </div>
    </div>
</div>
@endsection
<script src="/assets/js/jquery_3.6.1.min.js" ></script>
<script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
<script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
<script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
<script src="/assets/js/select2.min.js"></script>
<script>
    $(document).ready(function () {
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        $('#employeeResponsiveDataTable').DataTable({
            lengthChange: false,
            language: {
                "sEmptyTable": "Tabloda herhangi bir veri mevcut değil",
                "sInfo": "_TOTAL_ kayıttan _START_ - _END_ arası gösteriliyor",
                "sInfoEmpty": "Kayıt yok",
                "sInfoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                "sInfoPostFix": "",
                "sInfoThousands": ".",
                "sLengthMenu": "Sayfada _MENU_ kayıt göster",
                "sLoadingRecords": "Yükleniyor...",
                "sProcessing": "İşleniyor...",
                "sSearch": "Ara:",
                "sZeroRecords": "Eşleşen kayıt bulunamadı",
                "oPaginate": {
                    "sFirst": "İlk",
                    "sLast": "Son",
                    "sNext": "Sonraki",
                    "sPrevious": "Önceki"
                },
                "oAria": {
                    "sSortAscending": ": artan sütun sıralamasını aktifleştirmek için tıklayın",
                    "sSortDescending": ": azalan sütun sıralamasını aktifleştirmek için tıklayın"
                }
            },
        });
        
        var employeeRowIndex = 0;

        $('.add-user-limit').on('click', function () {
            employeeRowIndex++;

            var employeeRow = $('#employeeRow tr').clone().removeAttr('id').show();
            
            employeeRow.find('select, input').each(function() {
                var name = $(this).attr('name');

                if (name) {
                    $(this).prop('disabled', false);

                    name = name.replace('0', employeeRowIndex);
                    $(this).attr('name', name);
                }
            });
            
            $('#employeeResponsiveDataTable tbody').append(employeeRow);
        });

        $(document).on('click', '.remove-user-limit', function (e) {
            e.preventDefault();

            if ($(this).data('id')) {
                var id = $(this).data('id');
                var url = $(this).data('url');

                if (confirm('Silmek istediğinizden emin misiniz?') == false) {
                    return false;
                }

                $.ajax({
                    url: url,
                    type: 'DELETE',
                    data: {
                        'id': id,
                        '_token': '{{ csrf_token() }}'
                    },
                    success: function (response) {
                        if (response.success) {
                            Toast.fire({
                                icon: "success",
                                title: response.message
                            });
                        }
                    },
                    error: function (xhr, status, error) {
                        Toast.fire({
                            icon: "error",
                            title: "Kategori personel bilgisi silinemedi."
                        });
                    }
                });
            }

            $(this).closest('tr').remove();
        });

        $(document).on('click', '.edit-user-limit', function (e) {
            e.preventDefault();

            var data = $(this).data();

            var $modal = $('#userLimitModal');

            $modal.find('input[name="userLimit[id]"]').val(data.id);
            $modal.find('input[name="userLimit[url]"]').val(data.url);
            $modal.find('input[name="userLimit[qty]"]').val(data.qty);
            $modal.find('input[name="userLimit[sort]"]').val(data.sort);
            $modal.find('select[name="userLimit[group_id]"]').val(data.group_id);
            $modal.find('select[name="userLimit[status]"]').val(data.status);
            $modal.find('select[name="userLimit[group_id]"]').trigger('change');
            $modal.find('select[name="userLimit[status]"]').trigger('change');
        });

        $(document).on('click', '.save-user-limit', function (e) {
            e.preventDefault();

            var form = $('#editForm');
            var url = form.find('input[name="userLimit[url]"]').val();

            $.ajax({
                url: url,
                type: 'PUT',
                data: form.serialize(),
                success: function (response) {
                    if (response.success) {
                        Toast.fire({
                            icon: "success",
                            title: response.message
                        });

                        $('#userLimitModal').modal('hide');

                        location.reload();
                    }
                },
                error: function (xhr, status, error) {
                    Toast.fire({
                        icon: "error",
                        title: "Kategori personel bilgisi güncellenemedi."
                    });
                }
            });
        });
    });
</script>