@extends('pages.build')
@section('title','Kategoriler')
@push('css')
<link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
<link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
<link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form method="get" action="{{ route('branch-categories.index') }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Ad</label>
                                    <input type="text" class="form-control form-control-sm" name="name" value="{{ $filters['name'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Durum</label>
                                <select name="status" class="form-control form-control-sm">
                                    <option value="">Hepsi</option>
                                    <option @if($filters['status'] == 1) selected="" @endif value="1">Aktif</option>
                                    <option @if($filters['status'] == 2) selected="" @endif value="2">Pasif</option>
                                </select>
                            </div>
                            <div class="col mt-4">
                                <button class="btn btn-success btn-sm mt-1">Listele</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                @if(in_array('*', $userRoles) || in_array('add_branch', $userRoles))
                    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                        <a href="{{ route('branch-categories.create') }}" class="nav-link">Kayıt Ekle</a>
                        <a href="{{ route('branches.index') }}" class="nav-link active">Bayi Listesi</a>
                    </nav>
                @endif
                <div class="card-body">
                    <table id="branchCategoryResponsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr>
                            <th style="width:1%">No</th>
                            <th>Kategori Adı</th>
                            <th>Durum</th>
                            <th>Sıralama</th>
                            <th>Eklenme Tarihi</th>
                            <th>Güncellenme Tarihi</th>
                            <th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($categories as $key => $category)
                            <tr>
                                <td>{{ $category->id }}</td>
                                <td>{{ $category->name }}</td>
                                <td>
                                    @if($category->status)
                                        <span class="badge bg-success-transparent">Aktif</span>
                                    @else
                                        <span class="badge bg-light text-dark">Pasif</span>
                                    @endif    
                                </td>
                                <td>{{ $category->sort }}</td>
                                <td>{{ $category->created_at->format('d.m.Y H:i:s') }}</td>
                                <td>{{ $category->updated_at->format('d.m.Y H:i:s') }}</td>
                                <td>
                                    @if(in_array('*', $userRoles) || in_array('edit_branch', $userRoles))
                                        <a class="text-info fs-14 lh-1" href="{{ route('branch-categories.edit', $category) }}"><i class="ri-edit-line"></i></a>
                                    @endif
                                    <form action="{{ route('branch-categories.destroy', $category->id) }}" method="POST" style="display: inline-block">
                                        @csrf
                                        @method('DELETE')
                                        <a class="text-danger fs-14 lh-1" href="#" onclick="event.preventDefault(); this.closest('form').submit();">
                                            <i class="ri-delete-bin-5-line"></i>
                                        </a>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
<script src="/assets/js/jquery_3.6.1.min.js" ></script>
<script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
<script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
<script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
<script src="/assets/js/select2.min.js"></script>
<script>
    $(document).ready(function () {
        $('#branchCategoryResponsiveDataTable').DataTable({
            lengthChange: false,
            language: {
                "sEmptyTable": "Tabloda herhangi bir veri mevcut değil",
                "sInfo": "_TOTAL_ kayıttan _START_ - _END_ arası gösteriliyor",
                "sInfoEmpty": "Kayıt yok",
                "sInfoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                "sInfoPostFix": "",
                "sInfoThousands": ".",
                "sLengthMenu": "Sayfada _MENU_ kayıt göster",
                "sLoadingRecords": "Yükleniyor...",
                "sProcessing": "İşleniyor...",
                "sSearch": "Ara:",
                "sZeroRecords": "Eşleşen kayıt bulunamadı",
                "oPaginate": {
                    "sFirst": "İlk",
                    "sLast": "Son",
                    "sNext": "Sonraki",
                    "sPrevious": "Önceki"
                },
                "oAria": {
                    "sSortAscending": ": artan sütun sıralamasını aktifleştirmek için tıklayın",
                    "sSortDescending": ": azalan sütun sıralamasını aktifleştirmek için tıklayın"
                }
            },
        });
    });
</script>