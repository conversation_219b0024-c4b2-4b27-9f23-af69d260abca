<!DOCTYPE html>
<html lang="tr" dir="ltr" data-nav-layout="vertical" data-theme-mode="light" data-header-styles="light" data-menu-styles="light" data-toggled="close" style="--primary-rgb: 223, 90, 90;">

@include('layouts.head')

@livewireStyles

<body>


<div id="loader" >
    <img src="/assets/images/media/loader.svg" alt="">
</div>

<div class="page">

    @include('layouts.header')

    @include('layouts.menu')
    <div class="main-content app-content" @stack('main-content-css')>

        <div class="main-container @if(!auth('customer')->check() && url()->current() != route('index')) container-fluid @endif ">

            @if(!auth('customer')->check())
                <!-- breadcrumb -->
                <div class="breadcrumb-header justify-content-between">
                    <div class="left-content">
                        <span class="main-content-title mg-b-0 mg-b-lg-1">@yield('title','Ümran Ekspertiz') @stack('message')</span>
                    </div>
                    <div class="justify-content-center mt-2">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item fs-15"><a href="{{ route('index') }}">Ana Sayfa</a></li>
                            <li class="breadcrumb-item active" aria-current="page">@yield('title','Ümran Ekspertiz') </li>
                            <li class="breadcrumb-item">
                                @yield('excel')
                            </li>
                        </ol>
                    </div>
                </div>
                <!-- /breadcrumb -->
            @endif


            <!-- row -->
            @yield('content')
            <!-- row closed -->
        </div>
        <!-- Container closed -->
    </div>
    <!-- main-content closed -->

    <div class="modal fade" id="setColumns" tabindex="-1"
         aria-labelledby="setColumns" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Sütunlar
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body datatable-columns">

                </div>
            </div>
        </div>
    </div>
</div>


<!-- Scroll To Top -->
<div class="scrollToTop">
    <span class="arrow"><i class="ri-arrow-up-s-fill fs-20"></i></span>
</div>
<div id="responsive-overlay"></div>
<!-- Scroll To Top -->

@include('layouts.script')

@livewireScripts

</body>

</html>
