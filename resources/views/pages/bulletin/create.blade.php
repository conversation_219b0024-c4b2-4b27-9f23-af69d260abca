@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@push('message')
@endpush
@push('css')
    <style>
        span.select2-selection.select2-selection--multiple {
            min-height: 30px !important;
        }
    </style>
@endpush
@section('title','Bülten Ekle')
@section('content')
    <div class="row">
        <form id="mainForm" method="post" action="{{ route('bulletin.store') }}" enctype="multipart/form-data">@csrf
            <div class="row">
                <div class="col-md-12">
                    <div class="accordion" id="accordionPanelsStayOpenExample">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="bultenBilgileriHeading">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#bultenBilgileri" aria-expanded="true"
                                        aria-controls="bultenBilgileri">
                                    Bülten Bilgileri
                                </button>
                            </h2>
                            <div id="bultenBilgileri" class="accordion-collapse collapse show"
                                 aria-labelledby="bultenBilgileriHeading">
                                @if ($errors->any())
                                    <div class="alert alert-danger">
                                        <ul class="mb-0">
                                            @foreach ($errors->all() as $error)
                                                <li>{{ $error }}</li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <div class="row">
                                                <div class="col-md-3 mb-3">
                                                    <label class="form-label">Bülten Gönderim Yönetimi</label>
                                                    <select id="bulletinTarget" class="form-control form-control-sm select2" name="bulletinTarget">
                                                        <option value="0" {{ old('bulletinTarget') == 0 ? 'selected' : '' }}>Hepsi</option>
                                                        <option value="1" {{ old('bulletinTarget') == 1 ? 'selected' : '' }}>Departman</option>
                                                        <option value="2" {{ old('bulletinTarget') == 2 ? 'selected' : '' }}>Görev Grubu</option>
                                                        <option value="3" {{ old('bulletinTarget') == 3 ? 'selected' : '' }}>Personel</option>
                                                    </select>
                                                </div>

                                                <div class="col-md-3" id="departmentDiv" style="display: none;">
                                                    <label class="form-label">Departman</label>
                                                    <select
                                                        class="form-control form-control-sm select2 redBorderColorUnset"
                                                        name="department_ids[]"
                                                        multiple
                                                    >
                                                        <option value="" disabled>Hepsi</option>
                                                        @foreach($departments as $department)
                                                            <option
                                                                value="{{ $department->id }}"
                                                                {{ collect(old('department_ids'))->contains($department->id) ? 'selected' : '' }}
                                                            >
                                                                {{ $department->description }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-md-3" id="groupDiv" style="display: none;">
                                                    <label class="form-label">Görev Grubu</label>
                                                    <select
                                                        class="form-control form-control-sm select2 redBorderColorUnset"
                                                        name="user_role_group_ids[]"
                                                        multiple
                                                    >
                                                        <option value="" disabled>Hepsi</option>
                                                        @foreach($userRoleGroups as $userRoleGroup)
                                                            <option
                                                                value="{{ $userRoleGroup->id }}"
                                                                {{ collect(old('user_role_group_ids'))->contains($userRoleGroup->id) ? 'selected' : '' }}
                                                            >
                                                                {{ $userRoleGroup->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-md-3" id="userDiv" style="display: none;">
                                                    <label class="form-label">Personel</label>
                                                    <select
                                                        class="form-control form-control-sm select2 redBorderColorUnset"
                                                        name="user_ids[]"
                                                        multiple
                                                    >
                                                        <option value="" disabled>Seçiniz</option>
                                                        @foreach($users as $user)
                                                            <option
                                                                value="{{ $user->id }}"
                                                                {{ collect(old('user_ids'))->contains($user->id) ? 'selected' : '' }}
                                                            >
                                                                {{ $user->name . ' ' . $user->surname }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-md-3">
                                                    <label class="form-label">Bülten Konu Başlığı</label>
                                                    <input class="form-control form-control-sm border border-light" name="title"
                                                           value="{{ old('title') }}">
                                                </div>
                                                <div class="col-md-3">
                                                    <label class="form-label">Bülten Numarası</label>
                                                    <input class="form-control form-control-sm border border-light" name="no"
                                                           value="{{ old('no') }}">
                                                </div>
                                                <div class="col-md-3">
                                                    <label class="form-label">Bülten Dosyası</label>
                                                    <input type="file" class="form-control form-control-sm border border-light" name="file"
                                                           accept=".pdf">
                                                    <small class="text-danger">* Sadece PDF formatında dosya
                                                        yükleyebilirsiniz.</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button type="submit" class="btn btn-success mt-3 mb-3 save_form">
                    <i class="fa fa-save"></i> Kaydet
                </button>
            </div>
        </form>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <script>
        $(".select2").select2({});
        $(document).ready(function () {
            $('.save_form').on('click', function (e) {
                e.preventDefault();
                Swal.fire({
                    title: 'Bülteni yayınlamak istediğinize emin misiniz?',
                    text: 'Bu işlemle birlikte bülteniniz hemen yayına alınacaktır.',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Evet, yayınla',
                    cancelButtonText: 'Hayır, vazgeç'
                }).then((result) => {
                    if (result.isConfirmed) {
                        Swal.fire({
                            title: 'Kaydediliyor...',
                            text: 'Lütfen bekleyin',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });
                        $(e.target).closest('form').submit();
                    }
                });
            });

            const Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });

            function hideAll() {
                $('#departmentDiv').hide();
                $('#groupDiv').hide();
                $('#userDiv').hide();
            }

            $('#bulletinTarget').on('change', function() {
                hideAll();

                const selectedValue = $(this).val();

                if (selectedValue === '1') {
                    $('#departmentDiv').show();
                } else if (selectedValue === '2') {
                    $('#groupDiv').show();
                } else if (selectedValue === '3') {
                    $('#userDiv').show();
                }
            });
        })
    </script>
@endpush
