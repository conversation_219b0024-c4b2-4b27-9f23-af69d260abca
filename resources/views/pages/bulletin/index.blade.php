@extends('pages.build')
@section('title','<PERSON>ültenler')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <style>
        .dataTables_filter input {
            width: 300px !important;
            font-size: 14px;
            border-color: #f2f2f2 !important;
        }
    </style>
@endpush
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form method="get" action="{{ route('bulletin.index') }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label><PERSON><PERSON>ı</label>
                                    <input type="text" class="form-control form-control-sm" id="title" name="title" value="{{ $filters['title'] }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Bülten No</label>
                                    <input type="text" class="form-control form-control-sm" id="no" name="no" value="{{ $filters['no'] }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Başlangıç Tarihi</label>
                                    <input type="date" class="form-control form-control-sm" id="start_date" name="start_date" value="{{ $filters['startDate'] }}" max="{{ now()->format('Y-m-d') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Bitiş Tarihi</label>
                                    <input type="date" class="form-control form-control-sm" id="end_date" name="end_date" value="{{ $filters['endDate'] }}" max="{{ now()->format('Y-m-d') }}">
                                </div>
                            </div>
                            <div class="col-md-2 mt-4">
                                <button class="btn btn-success btn-sm mt-1">Listele</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <div class="card-header d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">Bülten Listesi</h5>
                    @if(in_array('*', $userRoles))
                        <a href="{{ route('bulletin.create') }}" class="btn btn-sm btn-primary">
                            <i class="bi bi-plus-lg me-1"></i> Yeni Bülten Oluştur
                        </a>
                    @endif
                </div>
                <div class="card-body">
                    <table id="responsiveDataTable" class="table table-bordered table-hover text-nowrap " style="width:100%">
                        <div class="position-absolute translate-middle-x" style="z-index:10; left: 30% !important;">
                            <button
                                type="button"
                                class="btn btn-outline-primary py-1"
                                onclick="$('#setColumns').modal('toggle')">
                                <i class="bi bi-eye"></i> Gözter / Gizle
                            </button>
                        </div>
                        <thead class="table-dark">
                        <tr>
                            <th>Yayınlanma Tarihi</th>
                            <th>Bülten No</th>
                            <th>Konu Başlığı</th>
                            <th>Yayınlayan Kullanıcı</th>
{{--                            <th>Öncelik Tipi</th>--}}
                            <th>Yayınlanma Durumu</th>
                            <th>İşlemler</th>
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="pdfModal" tabindex="-1" aria-labelledby="pdfModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="pdfModalLabel">PDF Önizleme</h5>
                    <!-- <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Kapat"></button> -->
                </div>
                <div class="modal-body p-0">
                    <div id="pdf-container" style="width:100%; height:70vh; overflow-y: auto;">
                        <div id="pdf-loading" class="text-center py-5">
                            <div class="spinner-border text-primary"></div>
                            <p class="mt-2">PDF yükleniyor...</p>
                        </div>
                        <div id="pdf-viewer"></div>
                    </div>
                    <div id="pdf-controls" class="bg-light p-2 d-flex justify-content-between align-items-center" style="display: none !important;">
                        <div>
                            <button id="prev-page" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-chevron-left"></i> Önceki
                            </button>
                            <button id="next-page" class="btn btn-sm btn-outline-secondary">
                                Sonraki <i class="bi bi-chevron-right"></i>
                            </button>
                        </div>
                        <div class="text-center">
                            <span id="page-num"></span> / <span id="page-count"></span>
                        </div>
                        <div>
                            <button id="zoom-out" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-dash-lg"></i>
                            </button>
                            <button id="zoom-in" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-plus-lg"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a id="pdfDownload" class="btn btn-success">
                        <i class="bi bi-download"></i>  İndir
                    </a>
                    <button type="button" id="printPdf" class="btn btn-primary">
                        <i class="bi bi-printer"></i> Yazdır
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal" id="confirmCloseBtn">
                        Kapat
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js"></script>
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <script>
        $(function () {
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
            var pageNumPending = null;
            var scale = 1.850;
            const pdfModalEl = document.getElementById('pdfModal');

            pdfModalEl.addEventListener('show.bs.modal', function(event) {
                const trigger       = event.relatedTarget;
                const bulletinId    = trigger.getAttribute('data-bulletin-id');
                const title         = trigger.getAttribute('data-title');
                const filepath      = trigger.getAttribute('data-filepath');
                const viewer        = document.getElementById('pdf-viewer');
                const loading       = document.getElementById('pdf-loading');
                const controls      = document.getElementById('pdf-controls');

                $('#confirmCloseBtn')
                    .data('bulletin-id', bulletinId)
                    .data('is-read', trigger.getAttribute('data-is-read'));

                pdfModalEl.querySelector('.modal-title').textContent = 'PDF Önizleme Konu Başlığı: ' + title;
                viewer.innerHTML    = '';
                loading.style.display   = 'block';
                controls.style.display  = 'none';
                loadPDF(filepath);

                const downloadBtn = pdfModalEl.querySelector('#pdfDownload');
                downloadBtn.href = filepath;
                downloadBtn.setAttribute('download', title + '.pdf');

                const printBtn = pdfModalEl.querySelector('#printPdf');
                printBtn.onclick = function() {
                    printPDF(filepath);
                };
            });

            function loadPDF(url) {
                pageNum = 1;
                pdfjsLib.getDocument(url).promise.then(function(pdf) {
                    pdfDoc = pdf;
                    document.getElementById('page-count').textContent = pdf.numPages;
                    document.getElementById('page-num').textContent = pageNum;
                    document.getElementById('pdf-controls').style.display = 'flex';
                    document.getElementById('pdf-loading').style.display = 'none';

                    renderPage(pageNum);
                }).catch(function(error) {
                    document.getElementById('pdf-loading').style.display = 'none';
                    document.getElementById('pdf-viewer').innerHTML =
                        '<div class="alert alert-danger m-3">PDF yüklenirken bir hata oluştu: ' + error.message + '</div>';
                });
            }

            function renderPage(num) {
                pageRendering = true;
                pdfDoc.getPage(num).then(function(page) {
                    var viewport = page.getViewport({ scale: scale });
                    var container = document.getElementById('pdf-viewer');
                    var canvas = document.createElement('canvas');

                    canvas.id = 'page-' + num;
                    canvas.className = 'pdf-page mb-3';

                    container.innerHTML = '';
                    container.appendChild(canvas);

                    var context = canvas.getContext('2d');
                    canvas.height = viewport.height;
                    canvas.width = viewport.width;

                    var renderContext = {
                        canvasContext: context,
                        viewport: viewport
                    };

                    var renderTask = page.render(renderContext);

                    renderTask.promise.then(function() {
                        pageRendering = false;
                        if (pageNumPending !== null) {
                            renderPage(pageNumPending);
                            pageNumPending = null;
                        }
                    });
                });
                document.getElementById('page-num').textContent = num;
            }

            function prevPage() {
                if (pageNum <= 1) {
                    return;
                }
                pageNum--;
                queueRenderPage(pageNum);
            }

            function nextPage() {
                if (pageNum >= pdfDoc.numPages) {
                    return;
                }
                pageNum++;
                queueRenderPage(pageNum);
            }

            function queueRenderPage(num) {
                if (pageRendering) {
                    pageNumPending = num;
                } else {
                    renderPage(num);
                }
            }

            function zoomIn() {
                scale += 0.1;
                renderPage(pageNum);
            }

            function zoomOut() {
                if (scale <= 0.5) return;
                scale -= 0.1;
                renderPage(pageNum);
            }

            function printPDF(url) {
                if (!url) return console.error('printPDF: URL eksik!');

                const iframe = document.createElement('iframe');
                Object.assign(iframe.style, {
                    visibility: 'hidden',
                    position: 'fixed',
                    right: '0',
                    bottom: '0',
                    width: '0',
                    height: '0'
                });
                document.body.appendChild(iframe);
                const doc = iframe.contentDocument || iframe.contentWindow.document;

                pdfjsLib.getDocument(url).promise.then(function(pdf) {
                    const pageCount = pdf.numPages;
                    const canvasPromises = [];
                    for (let i = 1; i <= pageCount; i++) {
                        canvasPromises.push(
                            pdf.getPage(i).then(page => {
                                const viewport = page.getViewport({ scale: 1.5 });
                                const canvas   = document.createElement('canvas');
                                canvas.width   = viewport.width;
                                canvas.height  = viewport.height;
                                return page.render({
                                    canvasContext: canvas.getContext('2d'),
                                    viewport: viewport
                                }).promise.then(() => canvas);
                            })
                        );
                    }
                    Promise.all(canvasPromises).then(canvases => {
                        canvases.forEach(canvas => {
                            doc.body.appendChild(canvas);
                        });
                        setTimeout(() => {
                            iframe.contentWindow.focus();
                            iframe.contentWindow.print();
                        }, 300);
                    });
                }).catch(err => console.error('PDF yüklenirken hata:', err));
            }

            document.getElementById('prev-page').addEventListener('click', prevPage);
            document.getElementById('next-page').addEventListener('click', nextPage);
            document.getElementById('zoom-in').addEventListener('click', zoomIn);
            document.getElementById('zoom-out').addEventListener('click', zoomOut);
        });
    </script>

    <script>
        $(function () {
            'use strict';
            const responsiveDatatable = $('#responsiveDataTable').DataTable({
                responsive: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('bulletin.index') }}",
                    type: 'GET',
                    data: function (d) {
                        d.title = $('#title').val();
                        d.no = $('#no').val();
                        d.start_date = $('#start_date').val();
                        d.end_date = $('#end_date').val();
                    }
                },
                aaSorting: [],
                pageLength: 10,
                columns: [
                    { data: 'publish_at', name: 'publish_at' },
                    { data: 'no', name: 'no' },
                    { data: 'title', name: 'title' },
                    { data: 'user', name: 'user' },
                    { data: 'priority', name: 'priority' },
                    { data: 'actions', name: 'actions', orderable: false, searchable: false }
                ],
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ","
                },
                dom: '<"pull-left"f>' +
                    'rt' +
                    '<"row mt-3"<"col-sm-4"l><"col-sm-4 text-center"i><"col-sm-4 text-end"p>>'
            });

            $('#openColumnsModal').on('click', function() {
                $('#setColumns').modal('toggle');
                syncCheckboxesWithColumns();
            });

            var columns = responsiveDatatable.columns().header().toArray();
            let columnsHtml = '<div class="row">';

            columnsHtml += `
                <div class="col-xl-4 mt-2 form-check-sm">
                    <label class="form-label" for="column0"><i class="fe fe-corner-down-right"></i> Tümü</label>
                    <input type="checkbox" class="form-check-input column-show-hide" id="column0" value="all">
                </div>`;

            columns.forEach(function(column, index) {
                if (index !== 0) {
                    columnsHtml += `
                <div class="col-xl-4 mt-2 form-check-sm">
                    <label class="form-label" for="column${index}"><i class="fe fe-corner-down-right"></i> ${$(column).text()}</label>
                    <input type="checkbox" class="form-check-input column-show-hide" id="column${index}" value="${index}">
                </div>`;
                }
            });

            columnsHtml += "</div>";
            $('.datatable-columns').html(columnsHtml);
            loadColumnVisibility();

            $('.column-show-hide').on('click', function() {
                let columnIndex = $(this).val();

                if (columnIndex === "all") {
                    let isChecked = $(this).prop('checked');

                    $('.column-show-hide').not('#column0').each(function() {
                        let colIndex = $(this).val();
                        $(this).prop('checked', isChecked);
                        responsiveDatatable.column(colIndex).visible(isChecked);
                    });

                } else {
                    let column = responsiveDatatable.column(columnIndex);
                    let newState = !column.visible();
                    column.visible(newState);
                    $(this).prop('checked', newState);
                }
                saveColumnVisibility();
            });

            function saveColumnVisibility() {
                let columnVisibility = {};

                $('.column-show-hide').each(function() {
                    columnVisibility[$(this).val()] = $(this).prop('checked');
                });

                localStorage.setItem('columnVisibility', JSON.stringify(columnVisibility));
            }

            function loadColumnVisibility() {
                let columnVisibility = JSON.parse(localStorage.getItem('columnVisibility')) || {};

                $('.column-show-hide').each(function() {
                    let columnIndex = $(this).val();
                    let isVisible = columnVisibility[columnIndex] !== undefined ? columnVisibility[
                        columnIndex] : true;

                    responsiveDatatable.column(columnIndex).visible(isVisible);
                    $(this).prop('checked', isVisible);
                });
                syncCheckboxesWithColumns();
            }

            function syncCheckboxesWithColumns() {
                let allChecked = $('.column-show-hide:not(#column0)').length === $(
                    '.column-show-hide:not(#column0):checked').length;
                $('#column0').prop('checked', allChecked);
            }
        });
    </script>
    <script>
        $(document).ready(function () {
            $('#confirmCloseBtn').on('click', function (e) {
                e.preventDefault();
                const isRead = $(this).data('is-read');
                const bulletinId = $(this).data('bulletin-id');

                if (isRead) {
                    $('#pdfModal').modal('hide');
                    return;
                }
                Swal.fire({
                    title: 'Okudum, anladım, onaylıyorum',
                    icon: 'warning',
                    showCancelButton: false,
                    confirmButtonText: 'Onaylıyorum',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    allowEnterKey: false,
                }).then((result) => {
                    if (!result.isConfirmed) return;

                    fetch(`/bulletin/${bulletinId}/mark-as-read`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        }
                    })
                    .then(response => {
                        if (!response.ok) throw new Error('Network error');
                        return response.json();
                    })
                    .then(() => {
                        Swal.fire('Başarılı!', 'Bülten okundu olarak işaretlendi.', 'success')
                            .then(() => {
                                window.location.reload();
                            });
                    })
                    .catch(err => {
                        console.error('Mark as read failed:', err);
                        Swal.fire('Hata!', 'İşlem sırasında bir hata oluştu.', 'error');
                    });
                });
            });
        });
    </script>
@endpush
