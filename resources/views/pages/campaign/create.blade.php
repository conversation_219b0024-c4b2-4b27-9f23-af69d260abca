@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','<PERSON><PERSON><PERSON><PERSON>')
@section('content')
    <form id="mainForm" method="post" action="{{ route('campaigns.store') }}">@csrf
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="temelBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#temelBilgiler" aria-expanded="true"
                                    aria-controls="temelBilgiler">
                                Temel Bilgiler
                            </button>
                        </h2>
                        <div id="temelBilgiler" class="accordion-collapse collapse show" aria-labelledby="temelBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Başlık <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="name" value="{{ \Cache::has('campaign_create') ? \Cache::get('campaign_create')['name'] : '' }}" placeholder="Başlık" required>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control">
                                            <option value="1">Aktif</option>
                                            <option value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
        <input type="hidden" name="session_name" value="campaign_create">
    </form>
    @if(\Illuminate\Support\Facades\Cache::has('campaign_create'))
        <div class="modal fade" id="deleteCacheModal" tabindex="-1"
             aria-labelledby="deleteCacheModal" data-bs-keyboard="false"
             aria-hidden="true">
            <!-- Scrollable modal -->
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="staticBackdropLabel2">Mevcut Kayıt Bulundu!
                        </h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Önceden oluşturduğunuz kayıt kapanmadı!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success"
                                data-bs-dismiss="modal">Kayıta Devam Et</button>
                        <button type="submit" form="deleteCache" class="btn btn-danger">Yeni Kayıt Oluştur</button>
                        <form id="deleteCache" method="post" action="{{ route('deleteCache') }}">@csrf<input type="hidden" name="type" value="campaign_create"></form>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script>
        addEventListener("DOMContentLoaded", (event) => {
            @if(\Illuminate\Support\Facades\Cache::has('campaign_create'))
            $('#deleteCacheModal').modal('toggle')
            @endif
        });

        setInterval(function (){
            $.ajax({
                url: "{{ route('saveToSession') }}",
                type: "post",
                data: $('#mainForm').serialize(),
                success: function (response) {

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        },30 * 1000)
    </script>
@endpush
