@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        #otherBilgiler .form-control{
            border-radius: 0;
        }
        #otherBilgiler .row>*{
            padding-right: unset;
            padding-left: unset;
            padding: 3px 5px;
        }
        .ruhsat-form > div{
            border: 1px solid #00000029;
            padding: 3px;
        }
        .ruhsat-form .form-control{
            padding: unset;
        }
       .select2-container--default .select2-selection--single .select2-selection__rendered{
            border: unset !important;
        }
        .ruhsat-form > div > input, .ruhsat-form > div > select{
            border: unset;
        }
    </style>
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','<PERSON><PERSON>')
@section('content')
    <form id="mainForm" method="post" action="{{ route('cars.store') }}">@csrf
        <input type="hidden" name="return_url" value="{{ $_GET['return_url'] ?? '' }}">
        <div class="row">
            <div class="col-md-8">


                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="temelBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#temelBilgiler" aria-expanded="true"
                                    aria-controls="temelBilgiler">
                                Araç Bilgileri
                            </button>
                        </h2>
                        <div id="temelBilgiler" class="accordion-collapse collapse show" data-bs-parent="#accordionPanelsStayOpenExample" aria-labelledby="temelBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Cari Seçimi</label>
                                        <select name="customer_id" class="form-control select2-customers">
                                            <option value="">Seçilmedi</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Plaka <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="plaka" value="{{ isset($_GET['plaka']) && $_GET['plaka'] != '' ? $_GET['plaka'] : '' }}" placeholder="Plaka" required>
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="form-group mt-2">
                                            <label class="form-label">Model Ara <span class="text-danger">*</span></label>
                                            <input type="hidden" name="car_id">
                                            <input type="text" class="form-control search-group" required maxlength="17" placeholder="Aramak için yazınız (En az 3 kelime)">
                                            <div class="filter-results find-car"></div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Tipi</label>
                                        <input type="text" disabled class="form-control" name="tip_name">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Marka</label>
                                        <input type="text" disabled class="form-control" name="marka_name">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Model</label>
                                        <input type="hidden" name="car_group_model_id">
                                        <input type="text" disabled class="form-control" name="model_name">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Şase Numarası</label>
                                        <input type="text" class="form-control" name="sase_no" value="{{ isset($_GET['sase_no']) && $_GET['sase_no'] != '' ? $_GET['sase_no'] : '' }}" minlength="6" maxlength="17" required placeholder="Şase Numarası">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Kasa Tipi</label>
                                        <select name="car_case_type_id" class="form-control" required="">
                                            <option value="">Seçilmedi</option>
                                            @foreach($carCaseTypes as $carCaseType)
                                                <option value="{{$carCaseType->id}}">{{ $carCaseType->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Yakıt Tipi</label>
                                        <select name="car_fuels_id" class="form-control" required="">
                                            <option value="">Seçilmedi</option>
                                            @foreach($carFuels as $carFuel)
                                                <option value="{{$carFuel->id}}">{{ $carFuel->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Vites Tipi</label>
                                        <select name="car_gears_id" class="form-control" required="">
                                            <option value="">Seçilmedi</option>
                                            @foreach($carGears as $carGear)
                                                <option value="{{$carGear->id}}">{{ $carGear->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Çekişi</label>
                                        <select name="cekis" class="form-control">
                                            <option value="">Seçilmedi</option>
                                            <option value="4x2">4x2</option>
                                            <option value="4x4">4x4</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Motor Hacmi</label>
                                        <input type="text" class="form-control" name="motor_hacmi" required="" placeholder="Motor Hacmi">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">KM</label>
                                        <input type="number" class="form-control" min="0" name="km" placeholder="KM">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Km/Mil</label>
                                        <select name="km_type" class="form-control" id="km_type">
                                            <option value="1">Km</option>
                                            <option value="2">Mil</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Model Yılı</label>
                                        <select name="model_yili" class="form-control" required="">
                                            <option value="">Seçilmedi</option>
                                            @for($i = 1970;$i <= now()->year;$i++)
                                                <option value="{{ $i }}">{{ $i }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control">
                                            <option value="1">Aktif</option>
                                            <option value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="otherBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#otherBilgiler" aria-expanded="true"
                                    aria-controls="otherBilgiler">
                                Ruhsat Bilgileri
                            </button>
                        </h2>
                        <div id="otherBilgiler" class="accordion-collapse collapse" data-bs-parent="#accordionPanelsStayOpenExample" aria-labelledby="otherBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row ruhsat-form">
                                    <div class="col-xl-6">
                                        <label class="form-label">İl</label>
                                        <select name="il_kodu" class="select2">
                                            @foreach($cities as $city)
                                                <option @if($authUser->getBranch && $authUser->getBranch->il_kodu == $city->id) selected @endif value="{{ $city->id }}">{{ $city->title }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">İlçe</label>
                                        <select name="ilce_kodu" class="select2">

                                        </select>
                                    </div>
                                    <div class="col-xl-12">
                                        <label class="form-label">İlk Tescil Tarihi</label>
                                        <input type="date" class="form-control" name="ilk_tescil_tarihi">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Tescil Sıra No</label>
                                        <input type="text" class="form-control" name="tescil_sira_no">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Tescil Tarihi</label>
                                        <input type="date" class="form-control" name="tescil_tarihi">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Cinsi</label>
                                        <input type="text" class="form-control" name="cinsi">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Rengi</label>
                                        <input type="text" class="form-control" name="renk">
                                    </div>
                                    <div class="col-xl-12">
                                        <label class="form-label">Motor No</label>
                                        <input type="text" class="form-control" name="motor_no">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Net Ağırlığı</label>
                                        <input type="text" class="form-control" name="net_agirlik">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Azami Yüklü Ağırlık</label>
                                        <input type="text" class="form-control" name="azami_agirlik">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Katar Ağırlığı</label>
                                        <input type="text" class="form-control" name="katar_agirlik">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Romörk Azami Yüklü Ağırlığı</label>
                                        <input type="text" class="form-control" name="romork_agirlik">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Koltuk Sayısı (Sürücü Dahil)</label>
                                        <input type="text" class="form-control" name="koltuk_sayisi">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Ayakta Yolcu Sayısı</label>
                                        <input type="text" class="form-control" name="ayakta_yolcu_sayisi">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Silindir Hacmi</label>
                                        <input type="text" class="form-control" name="silindir_hacmi">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Motor Gücü (HP-Beygir)</label>
                                        <input type="text" class="form-control" name="motor_gucu">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Güç Ağırlık Oranı</label>
                                        <input type="text" class="form-control" name="guc_agirlik_orani">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Kullanım Amacı</label>
                                        <input type="text" class="form-control" name="kullanim_amaci">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Tip Onay No</label>
                                        <input type="text" class="form-control" name="tip_onay_no">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
            <div class="col-md-4">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="kasaGorselHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#kasaGorsel" aria-expanded="true"
                                    aria-controls="kasaGorsel">
                                Kasa Görseli
                            </button>
                        </h2>
                        <div id="kasaGorsel" class="accordion-collapse collapse show" aria-labelledby="kasaGorselHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2 kasa-resim">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3" id="save_button">Kaydet</button>
            </div>
        </div>
        <input type="hidden" name="session_name" value="car_create">
    </form>

@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/js/select2.min.js"></script>
    <script>

        $(".select2").select2({
            placeholder: "Ara.."
        });

        $(".select2-customers").select2({
            ajax: {
                url: '{{ route('getCustomersForSelect') }}', // Replace with your actual API endpoint
                dataType: 'json',
                delay: 250,
                type: "POST",
                data: function (params) {
                    return {
                        unvan: params.term, // search term
                        page: params.page,
                        _token: '{{ csrf_token() }}'
                    };
                },
                processResults: function (data, params) {
                    return {
                        results: data.items, // adjust the property accordingly
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },
            minimumInputLength: 3,
            placeholder: "Ara.."
        });

        $('select[name="il_kodu"]').on('change',function (){
            let $val = $(this).val()
            $('select[name="ilce_kodu"]').find('option').remove()
            if($val > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$val} ,
                    success: function (response) {
                        if (response.items.length > 0){
                            $.each(response.items,function (index,item){
                                $('select[name="ilce_kodu"]').append('<option value="'+item.ilce_id+'">'+item.ilce_title+'</option>')
                            })
                            $('select[name="ilce_kodu"]').trigger('change')
                        }
                    }
                });
            }
        })

        addEventListener("DOMContentLoaded", (event) => {
            $('select[name="il_kodu"]').trigger('change')
            setTimeout(function (){
                $('select[name="ilce_kodu"]').val({{ $authUser->getBranch ? $authUser->getBranch->ilce_kodu : '' }}).change();
            },1000)
        });

        $('select[name="car_case_type_id"]').on('change',function (){
            if ($(this).val() > 0){
                let $val = $(this).val()
                $.ajax({
                    url: "{{ route('api.getCarCaseType') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','id':$val} ,
                    success: function (response) {
                        $('.kasa-resim').html('<img src="/storage/'+response.item.image+'" style="width:100%"/>')
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }else{
                $('.kasa-resim').html()
            }
        })

        $('input[name="plaka"]').on('keyup',function (){
            $('input[name="plaka"]').val($('input[name="plaka"]').val().replaceAll(' ','').toUpperCase())
        })
        $('input[name="sase_no"]').on('keyup',function (){
            $('input[name="sase_no"]').val($('input[name="sase_no"]').val().replaceAll(' ','').toUpperCase())
        })

        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        $('#mainForm').on('submit',function (e){
            e.preventDefault()
            if ($('input[name="sase_no"]').val().length != 17 ){
                Toast.fire({
                    icon: "error",
                    title: "Şase No 17 Karakter Olmalıdır."
                });
                return false;
            }
            $('#save_button').attr('disabled','');
            $('#mainForm').unbind('submit').submit()

        })

        $('.search-group').on('keyup',function (){
            let $val = $(this).val();
            if ($(this).val().length > 1){
                $.ajax({
                    url: "{{ route('api.getCarModels') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','search':$val} ,
                    success: function (response) {
                        $('.find-car').css('display','block')
                        if (response.items.length > 0){
                            let html = ''
                            $.each(response.items,function (index,item){
                                html += '<div class="filter-item" data-id="'+item.id+'" data-name="'+item.name+'" data-parent="'+item.parent+'" data-parent2="'+item.parent2+'" data-nameonly="'+item.name_only_name+'">'+item.name+'</div>'
                            })
                            $('.find-car').html(html)

                            $('.find-car .filter-item').on('click',function (){
                                $('input[name=car_group_model_id]').val($(this).data('id'))
                                $('input[name=model_name]').val($(this).data('nameonly'))
                                $('input[name=marka_name]').val($(this).data('parent'))
                                $('input[name=tip_name]').val($(this).data('parent2'))
                                $('.find-car').css('display','none')
                                $('.search-group').val('')
                                $('.search-group').removeAttr('required')
                            })

                        }else{
                            $('.find-car').html("Hiçbir Kayıt Bulunamadı! <a class='text-danger' href='{{ route('cars.create') }}'>Yeni Kayıt Ekle</a>")
                        }
                    }
                });
            }else{
                $('.find-car').css('display','none')
            }
        })


        setInterval(function (){
            $.ajax({
                url: "{{ route('saveToSession') }}",
                type: "post",
                data: $('#mainForm').serialize(),
                success: function (response) {

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        },30 * 1000)
    </script>
@endpush
