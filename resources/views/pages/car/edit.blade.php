@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
    <style>
        #otherBilgiler .form-control{
            border-radius: 0;
        }
        #otherBilgiler .row>*{
            padding-right: unset;
            padding-left: unset;
            padding: 3px 5px;
        }
        .ruhsat-form > div{
            border: 1px solid #00000029;
            padding: 3px;
        }
        .ruhsat-form .form-control{
            padding: unset;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered{
            border: unset !important;
        }
        .ruhsat-form > div > input, .ruhsat-form > div > select{
            border: unset;
        }
    </style>
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','<PERSON><PERSON>')
@section('content')
    <form method="post" action="{{ route('cars.update',$car) }}">@csrf @method('put')

        <input type="hidden" name="return_url" value="{{$_GET['return_url'] ?? null}}">
        <div class="row">
            <div class="col-md-8">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="temelBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#temelBilgiler" aria-expanded="true"
                                    aria-controls="temelBilgiler">
                                Araç Bilgileri
                            </button>
                        </h2>
                        <div id="temelBilgiler" class="accordion-collapse collapse show" aria-labelledby="temelBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Cari Seçimi</label>
                                        <select name="customer_id" class="form-control select2-customers">
                                            <option value="0">Seçilmedi</option>
                                            @if($car->customer_id > 0)
                                                <option selected value="{{ $car->customer_id }}">{{ $car->getCustomer ? $car->getCustomer->unvan ?? $car->getCustomer->ad . ' ' . $car->getCustomer->soyad : '' }}</option>
                                            @endif
                                            {{--                                            @foreach($customers as $customer)--}}
                                            {{--                                                <option @if($car->customer_id == $customer->id) selected @endif value="{{ $customer->id }}">{{ $customer->unvan }}</option>--}}
                                            {{--                                            @endforeach--}}
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Plaka <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="plaka" placeholder="Plaka" value="{{ $car->plaka }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Hizmet Verdiği İl</label>
                                        <select name="il_kodu" class="select2">
                                            @foreach($cities as $city)
                                                <option @if($city->id == $car->il_kodu) selected @endif value="{{ $city->id }}">{{ $city->title }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Hizmet Verdiği İlçe</label>
                                        <select name="ilce_kodu" class="select2">

                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">İlk Tescil Tarihi</label>
                                        <input type="date" class="form-control" name="ilk_tescil_tarihi" placeholder="İlk Tescil Tarihi" value="{{ old('ilk_tescil_tarihi', $car->ilk_tescil_tarihi) }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Tescil Sıra No</label>
                                        <input type="text" class="form-control" name="tescil_sira_no" placeholder="Tescil Sıra No" value="{{ $car->tescil_sira_no }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Tescil Tarihi</label>
                                        <input type="date" class="form-control" name="tescil_tarihi" placeholder="Tescil Tarihi" value="{{ old('ilk_tescil_tarihi', $car->tescil_tarihi) }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <div class="form-group mt-2">
                                            <label class="form-label">Model Ara</label>
                                            <input type="hidden" name="car_id">
                                            <input type="text" class="form-control search-group" maxlength="17" placeholder="Aramak için yazınız (En az 3 kelime)">
                                            <div class="filter-results find-car"></div>
                                        </div>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Tipi</label>
                                        <input type="text" disabled class="form-control" value="{{ $car->getGroup ? $car->getGroup->name : '' }}" name="tip_name">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Marka</label>
                                        <input type="text" disabled class="form-control" value="{{ $car->getMarka ? $car->getMarka->name : '' }}" name="marka_name">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Model</label>
                                        <input type="hidden" name="car_group_model_id" value="{{ $car->car_group_model_id }}">
                                        <input type="text" disabled class="form-control" value="{{ $car->getModel ? $car->getModel->name : '' }}" name="model_name">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Cinsi</label>
                                        <input type="text" class="form-control" name="cinsi" placeholder="Cinsi" value="{{ $car->cinsi }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Rengi</label>
                                        <input type="text" class="form-control" name="renk" placeholder="Rengi" value="{{ $car->renk }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Motor No</label>
                                        <input type="text" class="form-control" name="motor_no" placeholder="Motor No" value="{{ $car->motor_no }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Şase Numarası</label>
                                        <input type="text" class="form-control" name="sase_no" minlength="6" maxlength="17" placeholder="Şase Numarası" value="{{ $car->sase_no }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Net Ağırlığı</label>
                                        <input type="text" class="form-control" name="net_agirlik" placeholder="Net Ağırlığı" value="{{ $car->net_agirlik }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Azami Yüklü Ağırlık</label>
                                        <input type="text" class="form-control" name="azami_agirlik" placeholder="Azami Ağırlık" value="{{ $car->azami_agirlik }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Katar Ağırlığı</label>
                                        <input type="text" class="form-control" name="katar_agirlik" placeholder="Katar Ağırlığı" value="{{ $car->katar_agirlik }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Romörk Azami Yüklü Ağırlığı</label>
                                        <input type="text" class="form-control" name="romork_agirlik" placeholder="Romörk Azami Yüklü Ağırlığı" value="{{ $car->romork_agirlik }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Koltuk Sayısı (Sürücü Dahil)</label>
                                        <input type="text" class="form-control" name="koltuk_sayisi" placeholder="Koltuk Sayısı (Sürücü Dahil)" value="{{ $car->koltuk_sayisi }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Ayakta Yolcu Sayısı</label>
                                        <input type="text" class="form-control" name="ayakta_yolcu_sayisi" placeholder="Ayakta Yolcu Sayısı" value="{{ $car->ayakta_yolcu_sayisi }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Silindir Hacmi</label>
                                        <input type="text" class="form-control" name="silindir_hacmi" placeholder="Silindir Hacmi" value="{{ $car->silindir_hacmi }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Motor Gücü (HP-Beygir)</label>
                                        <input type="text" class="form-control" name="motor_gucu" placeholder="Motor Gücü" value="{{ $car->motor_gucu * 1.341 }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Güç Ağırlık Oranı</label>
                                        <input type="text" class="form-control" name="guc_agirlik_orani" placeholder="Güç Ağırlık Oranı" value="{{ $car->guc_agirlik_orani }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Kullanım Amacı</label>
                                        <input type="text" class="form-control" name="kullanim_amaci" placeholder="Kullanım Amacı" value="{{ $car->kullanim_amaci }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Tip Onay No</label>
                                        <input type="text" class="form-control" name="tip_onay_no" placeholder="Tip Onay No" value="{{ $car->tip_onay_no }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Kasa Tipi</label>
                                        <select name="car_case_type_id" class="form-control" required="">
                                            <option value="0">Seçilmedi</option>
                                            @foreach($carCaseTypes as $carCaseType)
                                                <option @if($car->car_case_type_id == $carCaseType->id) selected @endif value="{{$carCaseType->id}}">{{ $carCaseType->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Yakıt Tipi</label>
                                        <select name="car_fuels_id" class="form-control" required="">
                                            <option value="0">Seçilmedi</option>
                                            @foreach($carFuels as $carFuel)
                                                <option @if($car->car_fuels_id == $carFuel->id) selected @endif value="{{$carFuel->id}}">{{ $carFuel->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Vites Tipi</label>
                                        <select name="car_gears_id" class="form-control" required>
                                            <option value="0">Seçilmedi</option>
                                            @foreach($carGears as $carGear)
                                                <option @if($car->car_gears_id == $carGear->id) selected @endif value="{{$carGear->id}}">{{ $carGear->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Çekişi</label>
                                        <select name="cekis" class="form-control" required>
                                            <option value="0">Seçilmedi</option>
                                            <option @if($car->cekis == "4x2") selected @endif value="4x2">4x2</option>
                                            <option @if($car->cekis == "4x4") selected @endif value="4x4">4x4</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Motor Hacmi</label>
                                        <input type="text" class="form-control" name="motor_hacmi" required placeholder="Motor Hacmi" value="{{ $car->motor_hacmi }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">KM</label>
                                        <input type="number" min="0" class="form-control" name="km" placeholder="KM" value="{{ $car->km }}">
                                    </div>

                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Km/Mil</label>
                                        <select name="km_type" class="form-control" id="km_type">
                                            <option @if($car->km_type == 1) selected="" @endif value="1">Km</option>
                                            <option @if($car->km_type  == 2) selected="" @endif value="2">Mil</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Model Yılı</label>
                                        <select name="model_yili" class="form-control" required>
                                            <option value="0">Seçilmedi</option>
                                            @for($i = 1970;$i <= now()->year;$i++)
                                                <option @if($car->model_yili == $i) selected @endif value="{{ $i }}">{{ $i }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control">
                                            <option value="1">Aktif</option>
                                            <option @if($car->status == 0) selected @endif value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="otherBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#otherBilgiler" aria-expanded="true"
                                    aria-controls="otherBilgiler">
                                Ruhsat Bilgileri
                            </button>
                        </h2>
                        <div id="otherBilgiler" class="accordion-collapse collapse" data-bs-parent="#accordionPanelsStayOpenExample" aria-labelledby="otherBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row ruhsat-form">
                                    <div class="col-xl-6">
                                        <label class="form-label">İl</label>
                                        <select name="il_kodu" class="select2">
                                            @foreach($cities as $city)
                                                <option @if($car->il_kodu == $city->id) selected @endif value="{{ $city->id }}">{{ $city->title }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">İlçe</label>
                                        <select name="ilce_kodu" class="select2">
                                            @if($car->ilce_kodu > 0)
                                                <option value="{{ $car->ilce_kod }}">{{ \App\Models\Town::where('ilce_id',$car->ilce_kodu)->first() }}</option>
                                            @endif
                                        </select>
                                    </div>
                                    <div class="col-xl-12">
                                        <label class="form-label">İlk Tescil Tarihi</label>
                                        <input type="date" class="form-control" value="{{$car->ilk_tescil_tarihi}}" name="ilk_tescil_tarihi">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Tescil Sıra No</label>
                                        <input type="text" class="form-control" value="{{$car->tescil_sira_no}}" name="tescil_sira_no">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Tescil Tarihi</label>
                                        <input type="date" class="form-control" value="{{$car->tescil_tarihi}}" name="tescil_tarihi">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Cinsi</label>
                                        <input type="text" class="form-control" name="cinsi" value="{{ $car->cinsi }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Rengi</label>
                                        <input type="text" class="form-control" name="renk" value="{{ $car->renk }}">
                                    </div>
                                    <div class="col-xl-12">
                                        <label class="form-label">Motor No</label>
                                        <input type="text" class="form-control" name="motor_no" value="{{ $car->motor_no }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Net Ağırlığı</label>
                                        <input type="text" class="form-control" name="net_agirlik" value="{{ $car->net_agirlik }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Azami Yüklü Ağırlık</label>
                                        <input type="text" class="form-control" name="azami_agirlik" value="{{ $car->azami_agirlik }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Katar Ağırlığı</label>
                                        <input type="text" class="form-control" name="katar_agirlik" value="{{ $car->katar_agirlik }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Romörk Azami Yüklü Ağırlığı</label>
                                        <input type="text" class="form-control" name="romork_agirlik" value="{{ $car->romork_agirlik }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Koltuk Sayısı (Sürücü Dahil)</label>
                                        <input type="text" class="form-control" name="koltuk_sayisi" value="{{ $car->koltuk_sayisi }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Ayakta Yolcu Sayısı</label>
                                        <input type="text" class="form-control" name="ayakta_yolcu_sayisi" value="{{ $car->ayakta_yolcu_sayisi }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Silindir Hacmi</label>
                                        <input type="text" class="form-control" name="silindir_hacmi" value="{{ $car->silindir_hacmi }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Motor Gücü (HP-Beygir)</label>
                                        <input type="text" class="form-control" name="motor_gucu" value="{{ $car->motor_gucu }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Güç Ağırlık Oranı</label>
                                        <input type="text" class="form-control" name="guc_agirlik_orani" value="{{ $car->guc_agirlik_orani }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Kullanım Amacı</label>
                                        <input type="text" class="form-control" name="kullanim_amaci" value="{{ $car->kullanim_amaci }}">
                                    </div>
                                    <div class="col-xl-6">
                                        <label class="form-label">Tip Onay No</label>
                                        <input type="text" class="form-control" name="tip_onay_no" value="{{ $car->tip_onay_no }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="kasaGorselHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#kasaGorsel" aria-expanded="true"
                                    aria-controls="kasaGorsel">
                                Kasa Görseli
                            </button>
                        </h2>
                        <div id="kasaGorsel" class="accordion-collapse collapse show" aria-labelledby="kasaGorselHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <img src="/storage/{{ $car->getCaseType ? $car->getCaseType->image : '' }}" style="width: 100%">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(".select2").select2({
            placeholder: "Ara.."
        });

        $(".select2-customers").select2({
            ajax: {
                url: '{{ route('getCustomersForSelect') }}', // Replace with your actual API endpoint
                dataType: 'json',
                delay: 250,
                type: "POST",
                data: function (params) {
                    return {
                        unvan: params.term, // search term
                        page: params.page,
                        _token: '{{ csrf_token() }}'
                    };
                },
                processResults: function (data, params) {
                    return {
                        results: data.items, // adjust the property accordingly
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: true
            },
            minimumInputLength: 3,
            placeholder: "Ara.."
        });

        $('select[name="il_kodu"]').on('change',function (){
            let $val = $(this).val()
            $('select[name="ilce_kodu"]').find('option').remove()
            if($val > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$val} ,
                    success: function (response) {
                        if (response.items.length > 0){
                            let townID = {{ $car->ilce_kodu }}
                            $.each(response.items,function (index,item){
                                $('select[name="ilce_kodu"]').append('<option value="'+item.ilce_id+'">'+item.ilce_title+'</option>')
                            })
                            $('select[name="ilce_kodu"] option[value="'+townID+'"]').prop('selected', true);
                            $('select[name="ilce_kodu"]').trigger('change')

                        }
                    }
                });
            }
        })

        addEventListener("DOMContentLoaded", (event) => {
            $('select[name="il_kodu"]').trigger('change')
        });

        $('select[name="car_group_tip_id"]').on('change',function (){
            $('select[name="car_group_marka_id"]').find('option').remove().end().append('<option value="0">Seçilmedi</option>')
            $('select[name="car_group_model_id"]').find('option').remove().end().append('<option value="0">Seçilmedi</option>')
            if ($('select[name="car_group_tip_id"]').val() > 0){
                $.ajax({
                    url: "{{ route('api.getCarBrands') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','parent_id':$('select[name="car_group_tip_id"]').val()} ,
                    success: function (response) {
                        $.each(response.items,function (index,item){
                            $('select[name="car_group_marka_id"]').append('<option value="'+item.id+'">'+item.name+'</option>')
                        })
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }

        })

        $('select[name="car_group_marka_id"]').on('change',function (){
            $('select[name="car_group_model_id"]').find('option').remove().end().append('<option value="0">Seçilmedi</option>')
            if ($('select[name="car_group_marka_id"]').val() > 0){
                $.ajax({
                    url: "{{ route('api.getCarBrands') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','parent_id':$('select[name="car_group_marka_id"]').val()} ,
                    success: function (response) {
                        $.each(response.items,function (index,item){
                            $('select[name="car_group_model_id"]').append('<option value="'+item.id+'">'+item.name+'</option>')
                        })
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }

        })

        $('input[name="plaka"]').on('keyup',function (){
            $('input[name="plaka"]').val($('input[name="plaka"]').val().replaceAll(' ','').toUpperCase())
        })
        $('input[name="sase_no"]').on('keyup',function (){
            $('input[name="sase_no"]').val($('input[name="sase_no"]').val().replaceAll(' ','').toUpperCase())
        })

        $('#mainForm').on('submit',function (e){
            e.preventDefault()
            if ($('input[name="sase_no"]').val().length < 6){
                Toast.fire({
                    icon: "error",
                    title: "Şase No En Az 6 Karakter Olmalıdır."
                });
                return false;
            }
            $('#mainForm').unbind('submit').submit()

        })

        $('.search-group').on('keyup',function (){
            let $val = $(this).val();
            if ($(this).val().length > 1){
                $.ajax({
                    url: "{{ route('api.getCarModels') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','search':$val} ,
                    success: function (response) {
                        $('.find-car').css('display','block')
                        if (response.items.length > 0){
                            let html = ''
                            $.each(response.items,function (index,item){
                                html += '<div class="filter-item" data-id="'+item.id+'" data-name="'+item.name+'" data-parent="'+item.parent+'" data-parent2="'+item.parent2+'" data-nameonly="'+item.name_only_name+'">'+item.name+'</div>'
                            })
                            $('.find-car').html(html)

                            $('.find-car .filter-item').on('click',function (){
                                $('input[name=car_group_model_id]').val($(this).data('id'))
                                $('input[name=model_name]').val($(this).data('nameonly'))
                                $('input[name=marka_name]').val($(this).data('parent'))
                                $('input[name=tip_name]').val($(this).data('parent2'))
                                $('.find-car').css('display','none')
                                $('.search-group').val('')
                            })

                        }else{
                            $('.find-car').html("Hiçbir Kayıt Bulunamadı!")
                        }
                    }
                });
            }else{
                $('.find-car').css('display','none')
            }
        })

        function tcNoDogrula(){
            let $tc = $('input[name="tc_no"]').val()
            let $ad = $('input[name="ad"]').val()
            let $soyad = $('input[name="soyad"]').val()
            let $dogum_tarihi = $('input[name="dogum_tarihi"]').val()

            if($tc != ''){
                if($ad != ''){
                    if($soyad != ''){
                        if($dogum_tarihi != ''){
                            $.ajax({
                                url: "{{ route('api.tcNoDogrula') }}",
                                type: "post",
                                data: {'_token':'{{ csrf_token() }}','tc':$tc,'ad':$ad,'soyad':$soyad,'dogum_tarihi':$dogum_tarihi} ,
                                success: function (response) {
                                    if (response.success == 'true')
                                        Toast.fire({
                                            icon: "success",
                                            title: "T.C. Kimlik No Doğrulandı!"
                                        });
                                    else
                                        Toast.fire({
                                            icon: "error",
                                            title: "T.C. Kimlik No Doğrulamanadı!"
                                        });
                                }
                            });
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "Doğum Tarihi Zorunludur!"
                            });
                        }
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Soyisim Zorunludur!"
                        });
                    }
                }else{
                    Toast.fire({
                        icon: "error",
                        title: "İsim Zorunludur!"
                    });
                }
            }else{
                Toast.fire({
                    icon: "error",
                    title: "T.C. Kimlik No Zorunludur!"
                });
            }
        }
    </script>
@endpush
