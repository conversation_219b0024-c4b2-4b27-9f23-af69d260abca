@extends('pages.build')
@section('title','Araçlar')
@push('css')
   <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('excel')
    @if(in_array('*', $userRoles) || in_array('download_excel_car', $userRoles))
        <a href="/excel/araclar?plaka={{ $_GET['plaka'] ?? '' }}&sase_no={{ $_GET['sase_no'] ?? '' }}&customer_name={{ $_GET['customer_name'] ?? '' }}&branch_id={{ $_GET['branch_id'] ?? '' }}" class="btn btn-sm btn-success">Excel Olarak İndir <i class="fa fa-file-excel"></i> </a>
    @endif
    @if(in_array('*', $userRoles) || in_array('upload_excel_car', $userRoles))
        <a href="{{ route('importExcel',['type'=>'car']) }}" class="btn btn-sm btn-danger">Excelden Aktar <i class="fa fa-file-excel"></i> </a>
    @endif
@endsection
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form id="getSavedFilter" method="get" action="{{ route('cars.index') }}"></form>
                <form method="get" action="{{ route('cars.index') }}">
                    <div class="card-body">
                        <div class="row">

                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Plaka</label>
                                    <input type="text" class="form-control" name="plaka" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->plaka : ($_GET['plaka'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Şase No</label>
                                    <input type="text" class="form-control" name="sase_no" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->sase_no : ($_GET['sase_no'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Cari Adı</label>
                                    <input type="text" class="form-control" name="customer_name" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->customer_name : ($_GET['customer_name'] ?? '') }}">
                                </div>
                            </div>
                            @if(count($authUser->getBranchIds()) > 1)
                                <div class="col">
                                    <div class="form-group">
                                        <label>Bayi</label>
                                        <select class="form-control select2" name="branch_id">
                                            @if($authUser->type == 'admin')
                                                <option value="0">Tüm Bayiler</option>
                                            @endif
                                            @foreach(\App\Models\Branch::where('status',1)->whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                                <option @if((isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id)) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><small>Kayıtlı Şablonlar</small></label>
                                    <select form="getSavedFilter" onchange="$('#getSavedFilter').submit()" class="form-control select2" name="saved_filter_id">
                                        <option value="0">Seçilmedi</option>
                                        @foreach($savedFilters as $savedFilter)
                                            <option @if(isset($_GET['saved_filter_id']) && $_GET['saved_filter_id'] == $savedFilter->id) selected @endif value="{{ $savedFilter->id }}">{{ $savedFilter->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col mt-4">
                                <button class="btn btn-sm mt-1 btn-success">Listele</button>
                                <button onclick="$('#saveFilter').modal('toggle')" type="button" class="btn btn-sm btn-danger mt-1" >Kaydet</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    @if(in_array('*', $userRoles) || in_array('add_car', $userRoles))
                        <a href="{{ route('cars.create') }}" class="nav-link">Kayıt Ekle</a>
                    @endif
                </nav>
                <div class="card-body">

                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr>
                            <th style="width: 1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th>Plaka</th>
                            <th>Şase No</th>
                            <th>Araç Tipi</th>
                            <th>Marka</th>
                            <th>Model</th>
                            <th>Kasa Tipi</th>
                            <th>Yakıt Tipi</th>
                            <th>Vites Tipi</th>
                            <th>Model Yılı</th>
                            <th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody>
{{--                        @foreach($items as $key => $item)--}}
{{--                            <tr>--}}
{{--                                <td>{{ $item->plaka }}</td>--}}
{{--                                <td>{{ $item->sase_no }}</td>--}}
{{--                                <td>--}}
{{--                                    @if($authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','edit_car')->first())--}}
{{--                                    <a class="btn btn-primary btn-sm" href="{{ route('cars.edit',$item) }}">Düzenle</a>--}}
{{--                                    @endif--}}
{{--                                    @if($authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','delete_car')->first())--}}
{{--                                        <button class="btn btn-danger btn-sm" type="button" onclick="checkBeforeDelete('deleteForm{{ $key }}')">Sil</button>--}}
{{--                                        <form method="post" action="{{ route('cars.destroy',$item) }}" id="deleteForm{{ $key }}">@csrf @method('delete')</form>--}}
{{--                                    @endif--}}
{{--                                </td>--}}
{{--                            </tr>--}}
{{--                        @endforeach--}}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="saveFilter" tabindex="-1"
         aria-labelledby="saveFilter" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Filtre Şablonu Oluştur
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Şablon Adı</small></label>
                            <input type="text" class="form-control" name="saved_filter_name">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger save-filter">Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
       <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

    <script>
        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                "processing": true,
                "serverSide": true,
                "searching":false,
                "ajax": {
                    "url": "{{ route('getCarsForAjax') }}",
                    "dataType": "json",
                    "type": "POST",
                    "data":{
                        _token: "{{csrf_token()}}",
                        @if(isset($_GET['plaka']) && $_GET['plaka'] != '')
                        'plaka':"{{ $_GET['plaka'] }}",
                        @endif
                            @if(isset($_GET['sase_no']) && $_GET['sase_no'] != '')
                        'sase_no':"{{ $_GET['sase_no'] }}",
                        @endif
                            @if(isset($_GET['customer_name']) && $_GET['customer_name'] != '')
                        'customer_name':"{{ $_GET['customer_name'] }}",
                        @endif

                    },
                    "dataSrc":'data'
                },
                "columns": [
                    { "data": 'x','defaultContent': '' },
                    { "data": "plaka" },
                    { "data": "sase_no" },
                    { "data": "arac_tipi" },
                    { "data": "marka" },
                    { "data": "model" },
                    { "data": "kasa_tipi" },
                    { "data": "yakit_tipi" },
                    { "data": "vites_tipi" },
                    { "data": "model_yili" },
                    { "data": "options" },
                    {{--                    {--}}
                    {{--                        "render": function(data, type) {--}}
                    {{--                            return '<div class="d-flex" style="flex-direction: row;gap:1px">' +--}}
                    {{--                            @if($authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','edit_customer')->first())--}}
                    {{--                            '<a class="btn btn-primary btn-sm" href="/customers/'+full.id+'/edit">Düzenle</a>' +--}}
                    {{--                            @endif--}}
                    {{--                            @if($authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','delete_customer')->first())--}}
                    {{--                            '<button class="btn btn-danger btn-sm" type="button" onclick="checkBeforeDelete(\'deleteForm'+full.id+'\')">Sil</button>' +--}}
                    {{--                            '<form method="post" action="{{ route('customers.destroy',$item) }}" id="deleteForm'+full.id+'">@csrf @method('delete')</form>' +--}}
                    {{--                            @endif--}}
                    {{--                            '<a href="{{ route('expertises.index',['type'=>'customer','search'=>$item->unvan]) }}" class="btn btn-info">Cari Hareketleri</a>' +--}}
                    {{--                            '</div>'--}}
                    {{--                        }--}}
                    {{--                    }--}}
                ],
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı!  @if(in_array('*', $userRoles) || in_array('add_car', $userRoles)) <a href='{{ route("cars.create",['plaka'=>$_GET['plaka'] ?? '','sase_no'=>$_GET['sase_no'] ?? '']) }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_car', $userRoles)) <a href='{{ route("cars.create",['plaka'=>$_GET['plaka'] ?? '','sase_no'=>$_GET['sase_no'] ?? '']) }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();
            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })
        });

        if (!Swal.isVisible()) {
            const Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        $('.save-filter').on('click',function (){
            if ($('input[name="saved_filter_name"]').val() != ''){
                $.ajax({
                    url: "{{ route('saveFilters') }}",
                    type: "post",
                    data: {
                        '_token':'{{ csrf_token() }}',
                        'name':$('input[name="saved_filter_name"]').val(),
                        'plaka':$('input[name="plaka"]').val(),
                        'sase_no':$('input[name="sase_no"]').val(),
                        'customer_name':$('input[name="customer_name"]').val(),
                        'branch_id':$('select[name="branch_id"]').val(),
                        'type':'car'
                    } ,
                    success: function (response) {

                        if (response.success == 'true'){
                            Toast.fire({
                                icon: "success",
                                title: "Şablon Kayıt Edildi"
                            });
                            $('select[name="saved_filter_id"]').append('<option value="'+response.id+'" selected="selected">'+response.name+'</option>');
                            $('#getSavedFilter').submit()
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "Şablon Kayıt Edilemedi"
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Şablon Adı Girilmedi"
                });
            }
        })
    </script>
@endpush
