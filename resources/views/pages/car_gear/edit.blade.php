@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Araç Vites Türü Düzenle')
@section('content')
    <form method="post" action="{{ route('car-gears.update',$carGear) }}">@csrf @method('put')
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgi" aria-expanded="true"
                                    aria-controls="aracBilgi">
                                Temel Bilgiler
                            </button>
                        </h2>
                        <div id="aracBilgi" class="accordion-collapse collapse show" aria-labelledby="aracBilgiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Vites Türü <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="name" value="{{ $carGear->name }}" placeholder="Vites Türü" required>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control">
                                            <option @if($carGear->status == 1) selected @endif value="1">Aktif</option>
                                            <option @if($carGear->status == 0) selected @endif value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
