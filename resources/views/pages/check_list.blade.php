<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>Kontrol Listesi</title>
</head>
<body>
<!-- resources/views/check.blade.php -->

<table border="1">
    <tr>
        <th>Sıra</th>
        <th>Tarih</th>
        <th>Belge Numarası</th>
        <th>Bayi</th>
{{--        <th>Durum</th>--}}
        <th>Plus Card Var/Yok</th>
        <th>Cari Linki</th>
        <th>Eski Kod</th>
        <th>Yeni <PERSON>d</th>
        <th>Plus Card Detay</th>
{{--        <th>Puan</th>--}}
{{--        <th>Kredi</th>--}}
        <th>Telefon</th>
        <th><PERSON>ks<PERSON><PERSON></th>
        <th>Bulunamadı</th>
        <th>Not</th>
    </tr>
    @foreach ($expertises as $index => $expertise)
        <tr>
            <td>{{ $index + 1 }}</td>
            <td>{{ $expertise->created_at->format('d.m.Y H:i') }}</td>
            <td><a style="color:#aa00ff" target="_blank" href="{{ route('expertises.edit', [$expertise->uuid]) }}">{{ $expertise->belge_no }}</a></td>
            <td>{{ $subeler[$expertise->branch_id] ?? '' }}</td>
            <td>
                @if (count($expertise->getAlici->getPlusCards))
                    <span style="color: forestgreen">@foreach($expertise->getAlici->getPlusCards as $aliciPlusCard) {{ $aliciPlusCard->no }} @endforeach</span>
                @else
                    <span style="color: red">Plus Card Yok</span>
                @endif
            </td>
            <td><a style="color:#c97855" target="_blank" href="{{ route('customers.edit', [$expertise->getAlici]) }}">Cari Linki ({{ $expertise->getAlici->fullName }})</a></td>
            <td>{{ $expertise->getAlici->cari_kod }}</td>
            <td>{{ $expertise->getAlici->eski_cari_kod }}</td>
            <td>
                @if (count($expertise->getAlici->getPlusCards))
                    @foreach ($expertise->getAlici->getPlusCards as $getPlusCard)
                        <a style="color:#416cd7" target="_blank" href="{{ route('plus-cards.edit', [$getPlusCard]) }}">Plus Card Detay</a> Puan: {{ $getPlusCard->getTotalBalance()['points'] }} Kredi: {{ $getPlusCard->getTotalBalance()['credits'] }} <br>
                    @endforeach
                @endif
            </td>
            <td><span onclick="copy('{{ str_replace(' ', '', $expertise->getAlici->phone()) }}')">{{ $expertise->getAlici->phone() }}</span></td>
            <td></td>
            <td><input type="checkbox" name="checklist[]" class="checklist" @if(in_array($expertise->id,$checkedItems)) checked @endif value="{{ $expertise->id }}"></td>
            <td><input type="text" class="form-control-sm form-control note" data-id="{{ $expertise->id }}" value="{{ \App\Models\CheckListControl::whereNotNull('note')->where(['expertise_id'=>$expertise->id])->first()?->note }}" name="note[]"></td>
        </tr>
    @endforeach
</table>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    function copy(txt) {
        navigator.clipboard.writeText(txt).then(() => {}).catch(err => {});
    }
</script>
<script>
    $(document).ready(function () {
        $('.checklist').change(function () {
            var checkedValue = $(this).val();

            $.ajax({
                url: "{{ route('checkListUpdate') }}",
                type: "post",
                data: {
                    '_token': '{{ csrf_token() }}',
                    'val': checkedValue,
                },
                success: function (response) {
                    $('input[name="uuid"]').val(response.expertise_uuid)
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        });

        $('.note').on('change',function (){
            var checkedValue = $(this).val();
            let id = $(this).data('id')
            $.ajax({
                url: "{{ route('checkListUpdate') }}",
                type: "post",
                data: {
                    '_token': '{{ csrf_token() }}',
                    'val': checkedValue,
                    id:id
                },
                success: function (response) {
                    $('input[name="uuid"]').val(response.expertise_uuid)
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        })
    });
</script>
</body>
</html>
