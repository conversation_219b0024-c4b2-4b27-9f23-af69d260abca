@extends('pages.build')
@section('title','<PERSON><PERSON> Talep Oluştur')
@push('css')
    @if(auth('customer')->check())
        <link rel="stylesheet" href="/assets/css/dropify.min.css" />
        <style>
            input[type="text"] , select,textarea{
                padding: 10px;
                margin-bottom: 20px;
                border: unset;
                border-bottom: 1px solid #000;
                width: 100%;
                background: transparent;
            }
        </style>
    @endif

@endpush
@section('content')
    <div class="card">
        <div class="card-body">
            @if(auth('customer')->check())
                <div class="example">
                    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                        <a class="nav-link active" data-bs-toggle="tab" role="tab" href="#nav-genel" aria-selected="true">Genel Şikayet</a>
                        <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-rapor" aria-selected="false"><PERSON><PERSON></a>
                    </nav>
                    <div class="tab-content">
                        <div class="tab-pane show active text-muted" id="nav-genel" role="tabpanel">
                            <form method="post" action="{{ route('complaints.store') }}" enctype="multipart/form-data">@csrf
                                <input type="hidden" name="talep_nedeni" value="Genel Şikayet">
                                <div class="row">
                                    <div class="col-md-12">
                                        <select name="city_id">
                                            <option value="0">Şehir Seçiniz</option>
                                            @foreach(\App\Models\City::all() as $city)
                                                <option value="{{ $city->id }}">{{ $city->title }}</option>
                                            @endforeach
                                        </select>
                                        <select name="branch_id">
                                            <option value="0">Bayi Seçiniz</option>
                                        </select>
                                        <input type="text" placeholder="Başlık" required name="baslik">
                                        <textarea placeholder="Detay" name="musteri_ifadesi" required></textarea>
                                        <span class="text-danger">Şikayetinizi görsellerle desteklemek için alttaki kısımdan dosya/kamera izni vererek görsel yükleme yapabilirsiniz.</span>
                                        <input type="file" data-show-remove="false" data-height="250" name="dosya" accept="image/*" onclick="return confirmBeforeFileSelect(this);" onchange="handleFileSelect(this)"/>
                                    </div>
                                </div>
                                <button class="btn btn-danger btn-sm mt-3">Kaydet</button>
                            </form>
                        </div>
                        <div class="tab-pane text-muted" id="nav-rapor" role="tabpanel">
                            <form method="post" action="{{ route('complaints.store') }}" enctype="multipart/form-data">
                                @csrf
                                <input type="hidden" name="talep_nedeni" value="Rapora İtiraz">
                                <select name="car_id">
                                    <option value="0">Araç Seçiniz</option>
                                    @foreach($customerCars as $customerCar)
                                        <option value="{{ $customerCar->car_id }}">{{ $customerCar->getCar?->plaka }}</option>
                                    @endforeach
                                </select>
                                <select name="expertise_id">
                                    <option value="0">Raporlar İçin Önce Araç Seçiniz</option>
                                </select>
                                <input type="text" placeholder="Başlık" required name="baslik">
                                <textarea placeholder="Detay" name="musteri_ifadesi" required></textarea>
                                <span class="text-danger">Şikayetinizi görsellerle desteklemek için alttaki kısımdan dosya/kamera izni vererek görsel yükleme yapabilirsiniz.</span>
                                <input type="file" data-show-remove="false" data-height="250" name="dosya" accept="image/*" onclick="return confirmBeforeFileSelect(this);" onchange="handleFileSelect(this)"/>
                                <button class="btn btn-danger btn-sm mt-3">Kaydet</button>
                            </form>
                        </div>
                    </div>
                </div>
            @else
                <div class="row">
                    <div class="col-md-3" id="search">
                        <div class="form-group">
                            <label>
                                <small>
                                    <button class="btn btn-sm btn-success mt-1" data-type="customer">
                                        Cari Ara
                                    </button>
                                    <button class="btn btn-sm btn-success mt-1" data-type="plate">
                                        Plaka Ara
                                    </button>
                                    <button class="btn btn-sm btn-success mt-1" data-type="chassis">
                                        Şase Ara
                                    </button>
                                </small>
                            </label>
                            <input type="text" class="form-control" name="search" value="{{ $search }}">
                        </div>
                    </div>
                </div>
                <div class="row d-flex justify-content-around">
                    <div class="example">
                        <form method="post" action="{{ route('complaints.store') }}"> @csrf
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Ad <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="ad" required>
                                        <input type="hidden" class="form-control" name="expertiseUuid">
                                        <input type="hidden" class="form-control" name="expertiseBranchId">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>Soyad <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="soyad" required>
                                    </div>
                                </div>
                                {{--                                                <div class="col-md-4">--}}
                                {{--                                                    <div class="form-group">--}}
                                {{--                                                        <label>E-posta Adresi <span class="text-danger">*</span></label>--}}
                                {{--                                                        <input type="email" class="form-control" name="email" required>--}}
                                {{--                                                    </div>--}}
                                {{--                                                </div>--}}
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Telefon Numarası <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control telefon" name="telephone" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Bayi</label>
                                        <select name="branch_id" class="form-control select2">
                                            <option value="">Seçiniz</option>
                                            @foreach($branches as $branch)
                                                <option value="{{ $branch->id }}">{{ $branch->kisa_ad }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label>Talep Nedeni <span class="text-danger">*</span></label>
                                        <select class="form-control" name="talep_nedeni">
                                            <option>Öneri</option>
                                            <option>İstek</option>
                                            <option>Rapora İtiraz</option>
                                            <option>Genel Şikayet</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label>Müşteri İfadesi</label>
                                        <textarea class="form-control" name="musteri_ifadesi" required rows="6"></textarea>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <button class="btn btn-danger">Talep Girişi</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            @endif
        </div>
    </div>

    <div class="modal fade" id="searchModal" aria-labelledby="notCompleteForm" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <p class="text-danger">Kayıt Bulunamadı</p>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script src="/assets/js/dropify.min.js"></script>
    <script>
        $('.dropify').dropify({
            messages: {
                'default': 'Dosya Sürükle veya Tıkla',
                'replace': 'Dosya Sürükle veya Tıkla',
                'remove':  'Kaldır',
                'error':   'Bir Hata Ortaya Çıktı'
            }
        });

        @if(auth('customer')->check())
            $('select[name="car_id"]').on('change',function (){
                let $val = $(this).val()
                $('select[name="expertise_id"]').find('option').remove()
                if ($val > 0){
                    $.ajax({
                        url: "{{ route('api.getCarExpertises') }}",
                        type: "post",
                        data: {'_token':'{{ csrf_token() }}','id':$val} ,
                        success: function (response) {
                            $.each(response.items,function (index,item){
                                $('select[name="expertise_id"]').append("<option value="+item.uuid+">"+item.belge_no+"</option>")
                            })
                        }
                    });
                }
            })
        @endif

        function confirmBeforeFileSelect(input) {
            // Kamera ve dosya erişim izni için onay mesajı
            if (confirm('Umran Ekspertiz görüntü dosyalarınıza erişmek istiyor. Kamera ve dosya erişimi için onay veriyor musunuz?')) {
                return true; // Onay verildiyse yükleme ekranı açılacak
            } else {
                return false; // Onay verilmezse yükleme ekranı açılmayacak
            }
        }

        function handleFileSelect(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];

                // Sadece resim dosyalarını kabul et
                if (!file.type.startsWith('image/')) {
                    alert('Lütfen sadece resim dosyası yükleyin.');
                    input.value = ''; // Dosya seçimini temizle
                    return;
                }
            }
        }

        $(document).ready(function() {
            $('#search').on('click', 'button', function() {
                var button = $(this);
                var type = button.data('type');
                var search = $('input[name=search]').val();

                $('#searchModal .modal-body').empty();
                $('#searchModal .modal-body').html('<p>Lütfen bekleyiniz aramanız yapılıyor...</p>');
                $("#searchModal").modal("show");

                $.ajax({
                    url: "{{ route('complaints.search') }}",
                    method: 'GET',
                    data: {
                        type: type,
                        search: search,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#searchModal p[class=text-danger]').hide();
                            $('#searchModal .modal-body').empty();

                            if (response.data && response.data.length > 0) {
                                let table = $('<table class="table table-bordered table-striped table-sm">');

                                let thead = $('<thead>');
                                var headers = ['Rapor No', 'Müşteri', 'Plaka', 'Şase No'];
                                let headerRow = $('<tr>');
                                headers.forEach(function(key) {
                                    headerRow.append('<th>' + key + '</th>');
                                });
                                thead.append(headerRow);
                                table.append(thead);

                                let tbody = $('<tbody>');
                                response.data.forEach(function(row) {
                                    let tr = $('<tr>');
                                    tr.data('expertiseId', row['expertiseId']);
                                    tr.data('expertiseUuid', row['expertiseUuid']);
                                    tr.data('expertiseBranchId', row['expertiseBranchId']);
                                    tr.data('buyerName', row['buyer']['name']);
                                    tr.data('buyerSurname', row['buyer']['surname']);
                                    tr.data('buyerEmail', row['buyer']['email']);
                                    tr.data('buyerPhone', row['buyer']['phone']);

                                    tr.append('<td>' + row['expertiseNo'] + '</td>');
                                    tr.append('<td>' + row['buyer']['fullName'] + '</td>');
                                    tr.append('<td>' + row['plate'] + '</td>');
                                    tr.append('<td>' + row['chassis'] + '</td>');

                                    tbody.append(tr);
                                });
                                table.append(tbody);

                                $('#searchModal .modal-body').append(table);
                            } else {
                                $('#searchModal .modal-body').html('<p>Hiç kayıt bulunamadı.</p>');
                            }
                        } else {
                            $('#searchModal p[class=text-danger]').show();
                            $('#searchModal .modal-body').html('');
                        }
                    },
                    error: function(xhr) {
                        console.error('Hata:', xhr.responseText);
                    }
                });
            });

            $(document).on('click', '#searchModal table tr', function() {
                $('input[name=expertiseUuid]').val($(this).data('expertiseUuid'));
                $('input[name=expertiseBranchId]').val($(this).data('expertiseBranchId'));
                $('input[name=ad]').val($(this).data('buyerName'));
                $('input[name=soyad]').val($(this).data('buyerSurname'));
                $('input[name=email]').val($(this).data('buyerEmail'));
                $('input[name=telephone]').val($(this).data('buyerPhone'));
                $('select[name=branch_id]').val($(this).data('expertiseBranchId'));

                $("#searchModal").modal("toggle");
            });
        });
    </script>
@endpush
