@extends('pages.build')
@section('title','Talep Detay')
@section('content')
    <div class="container ">
        <div class="card ">
            <div class="card-body ">
                <form method="post" action="{{ route('complaints.update',$complaint) }}"> @csrf @method('put')
                    <input type="hidden" name="tur" value="Motor Mekanik">
                    <div class="row">
                        <div class="col-md-6">
                            <h4>#{{ $complaint->talep_no }}</h4>
                        </div>
                        <div class="col-md-6 d-flex justify-content-end">
                            <h6>{{ $complaint->ad_unvan . ' ' . $complaint->soyad }}</h6>
                        </div>
                        <div class="col-md-12 d-flex align-items-end" style="flex-direction: column">
                            <h5>Talep Nedeni</h5>
                            <h6><b class="text-danger">{{ $complaint->talep_nedeni }}</b></h6>
                        </div>
                        <div class="col-md-4 mt-3">
                            <button type="button" onclick="$('input[name=\'tur\']').val('Motor Mekanik');$('button[type=\'button\']').removeClass('btn-info');$(this).addClass('btn-info')" class="btn btn-block @if($complaint->tur == 'Motor Mekanik') btn-info @else btn-warning @endif btn-sm">Motor Mekanik</button>
                        </div>
                        <div class="col-md-4 mt-3">
                            <button type="button" onclick="$('input[name=\'tur\']').val('Fiyat');$('button[type=\'button\']').removeClass('btn-info');$(this).addClass('btn-info')" class="btn btn-block btn-warning @if($complaint->tur == 'Fiyat') btn-info @else btn-warning @endif btn-sm">Fiyat</button>
                        </div>
                        <div class="col-md-4 mt-3">
                            <button type="button" onclick="$('input[name=\'tur\']').val('Sunumdan Şikayet');$('button[type=\'button\']').removeClass('btn-info');$(this).addClass('btn-info')" class="btn btn-block btn-warning @if($complaint->tur == 'Sunumdan Şikayet') btn-info @else btn-warning @endif btn-sm">Sunumdan Şikayet</button>
                        </div>
                        <div class="col-md-4 mt-3">
                            <button type="button" onclick="$('input[name=\'tur\']').val('Kaporta');$('button[type=\'button\']').removeClass('btn-info');$(this).addClass('btn-info')" class="btn btn-block btn-warning @if($complaint->tur == 'Kaporta') btn-info @else btn-warning @endif btn-sm">Kaporta</button>
                        </div>
                        <div class="col-md-4 mt-3">
                            <button type="button" onclick="$('input[name=\'tur\']').val('Personel');$('button[type=\'button\']').removeClass('btn-info');$(this).addClass('btn-info')" class="btn btn-block btn-warning @if($complaint->tur == 'Personel') btn-info @else btn-warning @endif btn-sm">Personel</button>
                        </div>
                        <div class="col-md-4 mt-3">
                            <button type="button" onclick="$('input[name=\'tur\']').val('Teknik Sistem');$('button[type=\'button\']').removeClass('btn-info');$(this).addClass('btn-info')" class="btn btn-block btn-warning @if($complaint->tur == 'Teknik Sistem') btn-info @else btn-warning @endif btn-sm">Teknik Sistem</button>
                        </div>
                        <div class="col-md-12">
                            <div class="form-control">
                                <label>Müşteri İfadesi</label>
                                <textarea class="form-control" name="musteri_ifadesi" disabled rows="6">{{ $complaint->musteri_ifadesi }}</textarea>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="form-control">
                                <label>Müşteri Hizmetleri Yetkili Yanıtı</label>
                                <textarea class="form-control" name="yetkili_yanit" required rows="6">{{ $complaint->yetkili_yanit }}</textarea>
                            </div>
                        </div>
                        <div class="col-12 text-center">
                            <button class="btn btn-danger btn-sm">Cevap Ekle</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <script>
        addEventListener("DOMContentLoaded", (event) => {
            $('select[name="city_id"]').trigger('change')
        });

        $('select[name="city_id"]').on('change',function (){
            $('select[name="town_id"]').find('option').remove()
            if ($('select[name="city_id"]').val() > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$('select[name="city_id"]').val()} ,
                    success: function (response) {
                        $.each(response.items,function (index,item){
                            $('select[name="town_id"]').append("<option value="+item.ilce_id+">"+item.ilce_title+"</option>")
                        })
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }
        })

        function change(type){
            if (type == 'bireysel'){
                $('.kurumsal').addClass('d-none').removeAttr('required');
                $('.bireysel').removeClass('d-none').attr('required','true')
            }else{
                $('.kurumsal').removeClass('d-none').attr('required','true');
                $('.bireysel').addClass('d-none').removeAttr('required')
            }
        }
    </script>
@endpush
