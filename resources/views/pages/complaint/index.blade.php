@extends('pages.build')
@section('title', \App\Models\Menu::where('key','complaint')->first()->value)
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('excel')
    @if(in_array('*', $userRoles) || in_array('download_excel_complaint', $userRoles))
        <a href="/excel/sikayetler?talep_no={{ $_GET['talep_no'] ?? '' }}&ad_unvan={{ $_GET['ad_unvan'] ?? '' }}&soyad={{ $_GET['soyad'] ?? '' }}&plaka={{ $_GET['plaka'] ?? '' }}&telefon={{ $_GET['telefon'] ?? '' }}&talep_nedeni={{ $_GET['talep_nedeni'] ?? '' }}&status={{ $_GET['status'] ?? '' }}&start_date={{ $_GET['start_date'] ?? now()->subDays(7)->format('Y-m-d') }}&end_date={{ $_GET['end_date'] ?? now()->format('Y-m-d') }}&branch_id={{ $_GET['branch_id'] ?? '' }}" class="btn btn-sm btn-success">
            Excel Olarak İndir <i class="fa fa-file-excel"></i>
        </a>
    @endif
@endsection
@section('content')
    <div class="row">
        <div class="col-xl-12">
            @if(auth()->check())
                <div class="card custom-card">
                    <form method="get" action="{{ route('complaints.index') }}">
                        <div class="card-body">
                            <div class="row">
                                <div class="col">
                                    <div class="form-group">
                                        <label>Talep No</label>
                                        <input type="text" class="form-control form-control-sm" name="talep_no" value="{{ $_GET['talep_no'] ?? '' }}">
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label>Cari Unvan</label>
                                        <input type="text" class="form-control form-control-sm" name="ad_unvan" value="{{ $_GET['ad_unvan'] ?? '' }}">
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label>Cari Soyad</label>
                                        <input type="text" class="form-control form-control-sm" name="soyad" value="{{ $_GET['soyad'] ?? '' }}">
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label>Plaka</label>
                                        <input type="text" class="form-control form-control-sm" name="soyad" value="{{ $_GET['plaka'] ?? '' }}">
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label>Telefon</label>
                                        <input type="text" class="form-control form-control-sm" name="telefon" value="{{ $_GET['telefon'] ?? '' }}">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="form-group">
                                        <label>Talep Nedeni</label>
                                        <select class="form-control form-control-sm" name="talep_nedeni">
                                            <option value="">Tümü</option>
                                            <option @if(isset($_GET['talep_nedeni']) && $_GET['talep_nedeni'] == 'Öneri') selected @endif>Öneri</option>
                                            <option @if(isset($_GET['talep_nedeni']) && $_GET['talep_nedeni'] == 'İstek') selected @endif>İstek</option>
                                            <option @if(isset($_GET['talep_nedeni']) && $_GET['talep_nedeni'] == 'Rapora İtiraz') selected @endif>Rapora İtiraz</option>
                                            <option @if(isset($_GET['talep_nedeni']) && $_GET['talep_nedeni'] == 'Genel Şikayet') selected @endif>Genel Şikayet</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label>Durum</label>
                                        <select class="form-control form-control-sm" name="status">
                                            <option value="">Tümü</option>
                                            @foreach(__('arrays.complaint_statuses') as $key => $complaintStatus)
                                                <option @if(isset($_GET['status']) && $_GET['status'] == $key) selected @endif value="{{ $key }}">{{ $complaintStatus }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label>Baş. Tarihi</label>
                                        <input type="date" class="form-control form-control-sm" name="start_date" value="{{ $filters['startDate'] }}">
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label>Bit. Tarihi</label>
                                        <input type="date" class="form-control form-control-sm" name="end_date" value="{{ $filters['endDate'] }}">
                                    </div>
                                </div>
                                @if(in_array('*', $userRoles) || $authUser->user_role_group_id == 30 || $authUser->user_role_group_id == 38 || $authUser->user_role_group_id == 39 || count($authUser->getBranchIds()) > 1)
                                    <div class="col">
                                        <div class="form-group">
                                            <label>Bayi</label>
                                            <select class="form-control form-control-sm select2" name="branch_id">
                                                @if(in_array('*', $userRoles) || $authUser->user_role_group_id == 30 || $authUser->user_role_group_id == 38 || $authUser->user_role_group_id == 39)
                                                    <option value="">Tüm Bayiler</option>
                                                @endif
                                                @foreach(\App\Models\Branch::where('status',1)->whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                                    <option @if((isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id)) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                @endif
                                <div class="col mt-4">
                                    <button class="btn btn-sm btn-success">Listele</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            @endif

            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    <a href="{{ route('complaints.create') }}" class="nav-link">Yeni Kayıt Oluştur</a>
                </nav>

                <div class="card-body">

                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr><th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th>Talep No</th>
                            @if(auth()->check())
                                <th>Müşteri</th>
                            @endif
                            <th>Başlık</th>
                            <th>Kaynak</th>
                            <th>Tarih</th>
                            <th>Durum</th>
                            <th>İşlem</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($items->reverse() as $key => $item)
                            <tr>
                                <td></td>
                                <td>{{ $item->talep_no }}</td>
                                @if(auth()->check())
                                    <td>{{ $item->ad_unvan . ' ' . $item->soyad }}</td>
                                @endif
                                <td>{{ mb_substr($item->musteri_ifadesi, 0, 20, "UTF-8") }}..</td>
                                <td>@if($item->source_type == 0) <button type="button" class="btn btn-danger btn-sm">UMRAN</button> @else <button type="button" class="btn btn-success btn-sm">ŞİKAYETVAR</button> @endif </td>
                                <td>{{ $item->created_at->format('d.m.Y H:i') }}</td>
                                <td>{{ __('arrays.complaint_statuses')[$item->status] }}</td>
                                <td>
                                    @if(auth('customer')->check())
                                        <a class="btn btn-primary btn-sm" href="{{ route('complaints.show',$item->talep_no) }}">Görüntüle</a>
                                    @elseif(in_array('*', $userRoles) || in_array('edit_complaint', $userRoles))
                                        <a class="btn btn-primary btn-sm" href="{{ route('complaints.show',$item->talep_no) }}">Düzenle</a>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(".select2").select2({
            placeholder: "Ara.."
        });
        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({
                "dom": '<"pull-left"f><"pull-right"l>rtip',
                responsive: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })
        });
    </script>
@endpush
