@extends('pages.build')
@section('title','Destek Detay')
@push('css')
    <style>
        .main-chart-wrapper .chat-user-details{
            height: unset;
        }
        .complaint-link {
            text-decoration: underline;
            color: rgba(var(--bs-link-color-rgb),var(--bs-link-opacity,1))
        }

        .main-content-app {
            height: auto;
        }
    </style>
@endpush
@section('content')
    <div class="row row-sm mb-4">
        <div class="col-xl-12">
            <div class="row main-chart-wrapper">
                <div class="col-lg-12 col-xxl-8">
                    <div class="card">
                        <div class="card-body">
                            <b>{{ $complaint->baslik }}</b>
                            <p class="mt-3"> {{ $complaint->musteri_ifadesi }}</p>
                        </div>
                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6 d-flex" style="flex-direction: column">
                                    <span><b>{{ $complaint->ad_unvan . ' ' . $complaint->soyad}}</b></span>
                                    <!-- <small>{{ formatTelephoneNumber($complaint->telefon) }}</small> -->
                                    <small>{{  formatTelephoneNumber((substr(clearPhone($complaint->telefon), 0, 1) !== '0' ? '0' : '') . clearPhone($complaint->telefon)) }}</small>
                                </div>
                                <div class="col-md-6 d-flex justify-content-end">
                                    <i class="fe fe-user" style="font-size: 2rem"></i>
                                </div>
                            </div>
                        </div>
                        @if($complaint->dosya)
                            <div class="card-footer">
                                <div class="row">
                                    <div class="col-md-6 d-flex" style="flex-direction: column">
                                        <span><b>Görsel</b></span>
                                    </div>
                                    <div class="col-md-3 d-flex justify-content-end">
                                        <a href="/storage/{{ $complaint->dosya }}" target="_blank">
                                            <img src="/storage/{{ $complaint->dosya }}" class="img-fluid">
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endif
{{--                        @if($complaint->talep_nedeni == 'Rapora İtiraz')--}}
{{--                            <div class="card-footer">--}}
{{--                                <p>Şikayete konu olan {{ $complaint->getCar()?->plaka }} plakalı {{ $complaint->getCar()?->model_yili }} model {{ $complaint->getCar()?->getModel?->name }}, {{ $complaint->getCar()?->getMarka?->name }} araç. Aracın ekspertizi yapıldığı sırada {{ $complaint->getExpertise()?->km ?? $complaint->getExpertise()?->T400_arackm }} km'de olduğu bilgisi yer alıyor.</p>--}}
{{--                            </div>--}}
{{--                        @endif--}}
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <b>Şirket İçi Aksiyonlar</b>
                        </div>
                        @foreach($complaint->getActivities->reverse() as $activity)
                            <div class="card-footer">
                                <div class="message-item">
                                    <b>{{ $activity->getUser?->fullName() }}</b><br>
                                    <p>{!! $activity->message !!}</p>
                                    <button class="btn btn-danger btn-sm" type="button">{{ $activity->created_at->format('d.m.Y') }} - {{ $activity->created_at->format('H:i') }}</button> <button class="btn btn-dark btn-sm">{{ __('arrays.complaint_statuses')[$activity->status] }}</button>
                                </div>
                            </div>
                        @endforeach

                    </div>
                    @if(auth()->check())
                        <div class="card">
                            <div class="card-body">
                                <nav class="nav"></nav>
                                <form id="mainForm" method="post" action="{{ route('complaints.update',$complaint) }}">@csrf @method('put')
                                    @if($complaint->source_type == 1)
                                    <label>
                                        <input type="checkbox" name="send_customer" value="1"> Müşteriye cevap olarak iletilsin.
                                    </label>
                                    @endif
                                    <textarea class="form-control" placeholder="Mesajınız" form="mainForm" rows="5" required name="message"></textarea>
                                    <button form="mainForm" class="main-msg-send btn-success mt-3" href="">Cevap Ekle</button>
                                    <input type="hidden" name="status" value="{{ $complaint->status }}">
                                </form>
                            </div>
                        </div>
                    @endif
                </div>
                <div class="col-lg-12 col-xxl-4">
                    @if(auth()->check())
                        <div class="card">
                            <div class="card-body">
                                <div class="accordion" id="accordionPanelsStayOpenExample">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="subeBilgilerHeading">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                                    data-bs-target="#subeBilgiler" aria-expanded="true"
                                                    aria-controls="subeBilgiler">
                                                Durum Ayarı
                                            </button>
                                        </h2>
                                        <div id="subeBilgiler" class="accordion-collapse collapse" aria-labelledby="subeBilgilerHeading">
                                            <div class="accordion-body">
                                                @foreach(__('arrays.complaint_statuses') as $key => $status)
                                                    <button type="button" onclick="$('input[name=\'status\']').val({{ $key }});$('button[type=\'button\']').removeClass('btn-dark').addClass('btn-light');$(this).removeClass('btn-light').addClass('btn-dark')" class="btn @if($complaint->status == $key) btn-dark @else btn-light @endif btn-block mt-1">{{ $status }}</button>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="card overflow-hidden">
                        <div class="main-content-app">
                            <div class="card-body p-0 chat-main">
                                <div class="chat-user-details" id="chat-user-details">
                                    <div class="text-center border-bottom chat-image p-4 pb-0 mb-4 br-5 mt-3">
                                        <div class="main-img-user avatar-xl main-avatar mb-3 mx-auto">
                                            <a class="" href="#"><img alt="avatar" class="rounded-circle" src="{{ !auth('customer')->check() && $authUser->image ? '/storage/'.$authUser->image : '/assets/logo-icon.png' }}"></a>
                                        </div>
                                        <a href="#"><h5 class="mb-1">{{ $complaint->ad_unvan . ' ' . $complaint->soyad}}</h5></a>
                                    </div>
                                    <div class="">
                                        <div class="px-4">
                                            <h6 class="mb-3">Sohbet Detayları :</h6>
                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">Konu</p>
                                                    <p class="fs-12 text-muted">{{ $complaint->baslik }}</p>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">Kaynak</p>
                                                    <p class="fs-12 text-muted">@if($complaint->source_type == 0) UMRAN @else ŞİKAYETVAR @endif</p>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">İlgili Bayi</p>
                                                    <p class="fs-12 text-muted">
                                                        @if($complaint->getBranch != null)
                                                        <a class="complaint-link" href="{{ route('branches.edit', $complaint->getBranch?->id)}}">
                                                            {{ $complaint->getBranch?->kisa_ad }}
                                                        </a>
                                                        @else
                                                            Bayi Bulunamadı
                                                        @endif
                                                    </p>
                                                </div>
                                            </div>

                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">İlgili Rapor</p>
                                                    <p class="fs-12 text-muted">
                                                        @if($complaint->expertise_uuid != null)
                                                        <a class="complaint-link" href="{{ route('ekspertizRaporu', $complaint->expertise_uuid) }}">
                                                            Raporu Görüntüle
                                                        </a>
                                                        @else
                                                            Ekspertiz Bulunamadı
                                                        @endif
                                                    </p>
                                                </div>
                                            </div>

                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">İlgili Araç</p>
                                                    <p class="fs-12 text-muted">
                                                        @if($complaint->expertise_uuid != null
                                                            && $complaint->expertise != null
                                                            && $complaint->expertise->getCar != null
                                                        )
                                                        <a class="complaint-link" href="{{ route('cars.edit', $complaint->expertise->getCar?->id) }}">
                                                            Plaka: {{ $complaint->expertise->getCar?->plaka }} <br/>
                                                            Şase No: {{ $complaint->expertise->getCar?->sase_no }}
                                                        </a>
                                                        @else
                                                            Araç Bulunamadı
                                                        @endif
                                                    </p>
                                                </div>
                                            </div>

                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">İlgili Cari</p>
                                                    <p class="fs-12 text-muted">
                                                        @if($complaint->customer)
                                                        <a class="complaint-link" href="{{ route('customers.edit', $complaint->customer_id) }}">
                                                            {{ $complaint->customer?->fullName }}
                                                        </a>
                                                        @else
                                                            Cari Bulunamadı
                                                        @endif
                                                    </p>
                                                </div>
                                            </div>

                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">İlgili Ekspertiz Belge No</p>
                                                    <p class="fs-12 text-muted">{{ $complaint->getExpertise()?->belge_no ?? $complaint->getExpertise()?->T400_belgeno  }}</p>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">Haklılık Durumu</p>
                                                    <p class="fs-12 text-muted">
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input" type="radio" name="resultStatus"
                                                               value="0"
                                                               @if($complaint->il_id == 0) checked @endif>
                                                        <label class="form-check-label" for="resultStatus">
                                                            Bekliyor
                                                        </label>
                                                    </div>
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input form-checked-success"
                                                               type="radio" name="resultStatus" value="1"
                                                               @if($complaint->il_id == 1) checked @endif>
                                                        <label class="form-check-label" for="resultStatus">
                                                            Umran
                                                        </label>
                                                    </div
                                                    <div class="form-check mb-2">
                                                        <input class="form-check-input form-checked-secondary"
                                                               type="radio" name="resultStatus" value="2"
                                                               @if($complaint->il_id == 2) checked @endif>
                                                        <label class="form-check-label" for="resultStatus">
                                                            Müşteri
                                                        </label>
                                                    </div>
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <script>
        $(document).ready(function () {
            if (typeof Toast === 'undefined') {
                var Toast = Swal.mixin({
                    toast: true,
                    position: "top-end",
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.onmouseenter = Swal.stopTimer;
                        toast.onmouseleave = Swal.resumeTimer;
                    }
                });
            }

            $('input[name="resultStatus"]').on('click', function () {
                var selectedValue = $('input[name="resultStatus"]:checked').val();

                $.ajax({
                    url: '{{ route('complaints.resultStatus', $complaint) }}', // backend endpoint
                    method: 'POST',
                    data: {
                        resultStatus: selectedValue,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function (response) {
                        if (response.success === true) {
                            Toast.fire({
                                icon: "success",
                                title: response.message
                            });
                        } else {
                            Toast.fire({
                                icon: "error",
                                title: response.message
                            });
                        }
                    },
                    error: function (xhr) {
                        Toast.fire({
                            icon: "error",
                            title: xhr.responseText
                        });
                    }
                });
            });
        });
    </script>
@endpush
