@extends('pages.build')
@section('title')
    @if(isset($_GET['type']) && $_GET['type'] == 'hedefli')
        Hedefli Sözleşme Ekle
    @elseif(isset($_GET['type']) && $_GET['type'] == 'sabit')
        Sabit Sözleşme Ekle
    @else
        Hakediş Sözleşme Ekle
    @endif
@endsection
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        .month-box{
            width: 100%;
            border: 1px solid;
            border-radius: 10px;
            padding: 1rem;
        }
    </style>
@endpush
@section('content')
    <form id="mainForm" method="post" enctype="multipart/form-data" action="{{ route('contracts.store') }}">@csrf
        <input type="hidden" name="type" value="{{ $_GET['type'] }}">
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="temelBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#temelBilgiler" aria-expanded="true"
                                    aria-controls="temelBilgiler">
                                Sözleşme Detayları
                            </button>
                        </h2>
                        <div id="temelBilgiler" class="accordion-collapse collapse show" aria-labelledby="temelBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="form-group col-10">
                                        <label class="form-label">Cari</label>
                                        <input type="hidden" name="customer_id" id="customer_id">
                                        <input type="text" class="form-control form-control-sm search-customer" placeholder="Aramak için yazınız (En az 1 harf)" required>
                                        <div class="filter-results find-customer"></div>
                                    </div>
                                    <div class="col-2 mt-4">
                                        <button type="button" class="btn btn-sm btn-primary customer-search">Ara</button>
                                    </div>
                                    <div class="col-12 hide-until-customer">
                                        <div class="form-group">
                                            <label class="form-label">Cari Risk Tutari</label>
                                            <input type="text" class="form-control form-control-sm" disabled name="risk_tutari">
                                        </div>
                                    </div>
                                    <div class="col-md-6 col-sm-12 col-xs-12 hide-until-customer">
                                        <div class="form-group">
                                            <label class="form-label">İlk Dönem Başlangıç Tarihi</label>
                                            <input type="date" class="form-control form-control-sm" id="startDate" required name="start_date" min="{{ now()->format('Y-m-d') }}" value="{{ now()->format('Y-m-d') }}">
                                        </div>
                                    </div>
                                    <div class="col-md-4 col-sm-6 col-xs-6 hide-until-customer">
                                        <div class="form-group">
                                            <label class="form-label">Sözleşme Süresi (Ay)</label>
                                            <input type="number" min="1" max="12" value="1" id="contractDuration" name="duration_month" required class="form-control-sm form-control">
                                        </div>
                                    </div>
                                    <!-- <div class="col-md-12 hide-until-customer">
                                        <div class="form-group">
                                            <label class="form-label">Ödeme Tipi</label>
                                            <select class="form-control-sm form-control" required onchange="paymentTypeChange(this)" name="period_payment_type">

                                                <option value="veresiye">Veresiye</option>
                                            </select>
                                        </div>
                                    </div> -->
                                    <div class="col-md-2 col-sm-6 col-xs-6 hide-until-customer">
                                        <div class="form-group">
                                            <label class="form-label">Durum</label>
                                            <select name="status" class="form-control form-control-sm">
                                                <option value="1">Aktif</option>
                                                <option value="0">Pasif</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-xl-12 mt-2 hide-until-customer">
                                        <label class="form-label">Sözleşme Yapılan Bayi</label>
                                        <select name="contract_branch_id" class="select2" required="">
                                            <option value="">Lütfen Seçiniz</option>
                                            @foreach($branches as $branch)
                                                <option value="{{$branch->id}}">{{$branch->kisa_ad}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @if(auth()->user()->type == 'admin')
                                        <div class="col-xl-12 mt-2 hide-until-customer">
                                            <label class="form-label">Geçerli Bayiler</label>
                                            <select name="branch_id[]" id="branches_id" class="select2" required="" multiple>
                                                <option value="0">Tümü</option>
                                                @foreach($cities as $city)
                                                    <option value="city_{{$city->id}}">{{$city->title}} Bayileri</option>
                                                @endforeach
                                                @foreach($branches as $branch)
                                                    <option value="{{$branch->id}}">{{$branch->kisa_ad}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    @endif
                                    <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12 mt-2 hide-until-customer">
                                        <button class="btn btn-sm btn-success">Kaydet</button>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-7">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="ayrintilarHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#ayrintilar" aria-expanded="false"
                                    aria-controls="ayrintilar">
                                Ayrıntılar
                            </button>
                        </h2>
                        <div id="ayrintilar" class="accordion-collapse collapse show" aria-labelledby="ayrintilarHeading">

                            <div class="accordion-body">
                                <div id="contractContainer">

                                </div>
                            </div>
                            <button class="btn btn-sm btn-danger m-3 d-none add-period" type="button">Dönem Ekle</button>
                        </div>
                    </div>
                        @if(isset($_GET['type']) && ($_GET['type'] == 'hakedis' || $_GET['type'] == 'sabit'))
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="fiyatlarHeading">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#fiyatlar" aria-expanded="false"
                                            aria-controls="fiyatlar">
                                        Fiyatlar
                                    </button>
                                </h2>
                                <div id="fiyatlar" class="accordion-collapse collapse" aria-labelledby="fiyatlarHeading">
                                    <div class="accordion-body">
                                        <div class="table-responsive">
                                            <table class="table table-hover table-striped">
                                                <thead>
                                                <tr>
                                                    <td colspan="4">
                                                        <select id="hizmetSecim" class="form-control-sm form-control">
                                                            <option value="">Hizmet Ekleyin</option>
                                                            @foreach($stocks as $stock)
                                                                <option value="{{ $stock->id }}" data-name="{{ $stock->ad }}">{{ $stock->ad }} ({{ $stock->getStandartPrices?->kdv_dahil_fiyat }}₺)</option>
                                                            @endforeach
                                                        </select>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>Hizmet</td>
                                                    @if(isset($_GET['type']) && $_GET['type'] == 'sabit')
                                                        <td>Alıcı Fiyatı</td>
                                                    @endif

{{--                                                    <td>Satıcı Fiyatı</td>--}}
                                                    <td>Sözleşme Sahibi Fiyatı</td>
                                                    <td>Kaldır</td>
                                                </tr>
                                                </thead>
                                                <tbody id="hizmetListesi">
                                                <!-- Yeni hizmet satırları buraya eklenecek -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="accordion-item">
                            <h2 class="accordion-header" id="dosyalarHeading">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#dosyalar"
                                        aria-expanded="false" aria-controls="dosyalar">
                                    Sözleşme Dosyaları
                                </button>
                            </h2>
                            <div id="dosyalar" class="accordion-collapse collapse" aria-labelledby="dosyalarHeading">
                                <div class="accordion-body">
                                    <div class="row" id="filesContainer">
                                        <!-- Dinamik dosya yükleme alanları buraya eklenecek -->
                                    </div>
                                    <button type="button" class="btn btn-sm btn-danger mt-3" id="addFileButton">Sözleşme Dosyası Ekle</button>
                                </div>
                            </div>
                        </div>
                </div>
            </div>

        </div>
    </form>
    <div class="modal fade" id="searchCustomerModal" tabindex="-1"
         aria-labelledby="searchCustomerModal" data-bs-keyboard="false"
         aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Bulunan Sonuçlar
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body" style="max-height: 300px;overflow: auto; padding-top: 0px;">
                    <table class="table table-striped table-responsive">
                        <thead style="position: sticky;top: 0">
                        <tr>
                            <th>Hesap Kodu</th>
                            <th>Ad Soyad</th>
                            <th>Bayi</th>
                            <th>Telefon</th>
                            <th>T.C/Vergi No</th>
                            <th>Plus Card</th>
                        </tr>
                        </thead>
                        <tbody class="filter-results-new">

                        </tbody>
                    </table>
                    <table id="header-fixed"></table>
                </div>
                <div class="modal-footer cari">

                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/js/select2.min.js"></script>
    <script>
       $(document).on("input", ".turk-lirasi", function (e) {
            let value = $(this).val();
            // Remove TL symbol if it exists, then clean the input
            let numericValue = value.replace(/[^0-9,\.]/g, '');
            // Ensure only one comma or one dot is allowed
            let commaParts = numericValue.split(',');

            if (commaParts.length > 2) {
                numericValue = commaParts[0] + ',' + commaParts.slice(1).join('');
            }

            let dotParts = numericValue.split('.');

            if (dotParts.length > 2) {
                numericValue = dotParts[0] + '.' + dotParts.slice(1).join('');
            }

            // Add TL symbol to the left of the cleaned value
            $(this).val(`₺ ${numericValue}`);
        });

        $(document).on("keydown", ".turk-lirasi", function (e) {
            // Allow backspace, arrow keys, delete, tab, comma (188), numpad comma (110), and dot (190)
            if ([8, 37, 39, 46, 9, 188, 190, 110].includes(e.keyCode)) {
                return true;
            }

            // Allow numeric keys and numpad keys
            if (
                !(e.keyCode >= 48 && e.keyCode <= 57) &&  // Top-row numbers
                !(e.keyCode >= 96 && e.keyCode <= 105) &&  // Numpad numbers
                e.keyCode !== 188 &&  // Comma (,)
                e.keyCode !== 190 &&  // Dot (.)
                e.keyCode !== 110 &&  // Numpad comma (,)
                !e.ctrlKey // Disallow other Ctrl combinations
            ) {
                e.preventDefault();
            }
        });

        $(document).ready(function(){
            $(".hide-until-customer").hide();

            $(".search-customer").on("keyup", function(){
                $(".hide-until-customer").hide();
            });

            setTimeout(function (){
                $('.add-period').click();
            },500)
        });

        $(".select2").select2({
            placeholder: "Ara.."
        });

        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        $('.customer-search').click(function(){
            let $val = $('.search-customer').val();
            if ($val){
                $(this).html("Aranıyor..").attr('disabled','true');
                $.ajax({
                    url: "{{ route('api.getCustomersForContract') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','search':$val,'type':'kurumsal'} ,
                    success: function (response) {
                        $('.customer-search').html("Ara").removeAttr('disabled');
                        if (response.items.length > 0) {
                            if (response.items.length > 20){
                                Toast.fire({
                                    icon: "error",
                                    title: "Daha Detaylı Arama Yapınız!"
                                });
                                $(".hide-until-customer").hide();
                            }else{
                                let html = ""
                                $.each(response.items, function (index, item) {
                                    let tcVergiNo = item.vergi_no || item.tc_no;
                                    let cep = item.cep;
                                    let mask;
                                    let mask2;
                                    let riskTutari;
                                    if (tcVergiNo) {
                                        let first4 = tcVergiNo.substring(0, 4);
                                        let tcVergi = tcVergiNo.substring(4, tcVergiNo.length - 4).replace(/\d/g, "*");
                                        mask = first4 + tcVergi
                                    }
                                    if (cep) {
                                        let first4x = cep.substring(0, 2);
                                        let last5x = cep.substring(cep.length - 4);

                                        let stars = cep.substring(0, cep.length - 4).replace(/\d/g, "*");
                                        mask2 = stars + last5x
                                    }

                                    html += '<tr class="filter-item" ' +
                                        'data-id="' + item.id + '" ' +
                                        'data-unvan="' + item.unvan + '" ' +
                                        'data-telefon="' + (item.telefon ? item.telefon : item.cep) + '" ' +
                                        'data-kod="' + item.cari_kod + '" ' +
                                        'data-vade="' + item.vade + '" ' +
                                        'data-risk="' + item.risk + '" ' +
                                        'data-tc="' + item.tc + '">'
                                    html += '<td>' + (item.cari_kod || '') + '</td>'
                                    html += '<td>' + item.unvan + '</td>'
                                    html += '<td>' + item.branch_name + '</td>'
                                    html += '<td>' + (mask2 || '') + '</td>'
                                    html += '<td>' + (mask || '') + '</td>'
                                    html += '<td>' + (item.plusCardNo || '') + '</td>'
                                    html += '</tr>'
                                })
                                $('.filter-results-new').html(html)
                                $('#searchCustomerModal').modal('show')
                                $('.filter-results-new .filter-item').on('click', function () {
                                    if ($(this).data('vade')){
                                        var vadeGunu = new Date($(this).data('vade'));

                                        var day = vadeGunu.getDate();

                                        $('select[name="period_last_payment_day"]').val(day);
                                    }
                                    $('input[name=customer_id]').val($(this).data('id'))
                                    $('.search-customer').val($(this).data('unvan'));


                                    let riskData = $(this).data('risk'); // data-risk değerini al
                                    riskData = (typeof riskData === 'string') ? riskData : String(riskData); // String'e dönüştür

                                    let cleanValue = riskData.replace(/[^0-9.,]+/g, "").replace(',', '.'); // Geçersiz karakterleri temizle
                                    let amount = parseFloat(cleanValue); // Sayıya çevir

                                    let inputField = $('input[name="risk_tutari"]');
                                    if (!isNaN(amount)) {
                                        let formattedAmount = new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY',
                                            minimumFractionDigits: 2
                                        }).format(amount);
                                        inputField.val(formattedAmount); // Formatlanmış değeri ata
                                    } else {
                                        inputField.val(''); // Geçersizse boş bırak
                                    }



                                    $('#searchCustomerModal').modal('hide');
                                    console.log('modal kapandı');
                                    $(".hide-until-customer").show();
                                })
                            }
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "Eşleşen Kayıt Bulunamadı!"
                            });
                            $(".hide-until-customer").hide();
                        }
                    }
                });
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Cari arama yapınız!"
                });
                return false;
            }

        })
    </script>
    <script>
        function formatDate(date) {
            var year = date.getFullYear();
            var month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
            var day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }

        $('#mainForm').on('submit', function(e) {
            e.preventDefault();

            // Validate customer selection
            if ($('input[name="customer_id"]').val() < 1) {
                Toast.fire({
                    icon: "error",
                    title: "Cari Listesinden Seçim Yapınız!"
                });
                return false;
            }

            let fileInputs = document.querySelectorAll('input[type="file"]');
            let fileAdded = false;

            fileInputs.forEach(function(fileInput) {
                if (fileInput.files.length > 0) {
                    fileAdded = true; // En az bir dosya yüklendiyse işaretleyin
                }
            });

            if (!fileAdded) {
                Toast.fire({
                    icon: "error",
                    title: "Dosya Eklemediniz!"
                });
                return false;
            }

            @if(isset($_GET['type']) && ($_GET['type'] == 'sabit' || $_GET['type'] == 'hakedis'))
                let isValid = false;

                let hizmetListesiRow = document.querySelectorAll('#hizmetListesi tr');

                // Check if at least one service is added
                if (hizmetListesiRow.length < 1) {
                    Toast.fire({
                        icon: "error",
                        title: "Hizmet Eklemediniz!"
                    });
                    return false;
                }

                hizmetListesiRow.forEach(function(row) {
                    @if(isset($_GET['type']) && $_GET['type'] == 'sabit')
                    const buyerPrice = row.querySelector('input[name="buyer_price[]"]').value;
                    @endif

                    // const sellerPrice = row.querySelector('input[name="seller_price[]"]').value;
                    const contractOwnerPrice = row.querySelector('input[name="contract_owner_price[]"]').value;

                    // Ensure at least one price is filled
                    if (contractOwnerPrice) {
                        isValid = true;
                    }else{
                        isValid = false;
                    }
                });

                // If no valid price is entered, show an error and stop form submission
                if (!isValid) {
                    Toast.fire({
                        icon: "error",
                        title: "Sözleşme sahibi fiyatlarından en az birisini doldurmanız gerekmektedir."
                    });
                    return false;
                }
            @endif



            // If validation passes, allow form submission
            $('#mainForm').unbind('submit').submit();
        });

    </script>
    <script>
        let fileIndex = 0;

        document.getElementById('addFileButton').addEventListener('click', function () {
            fileIndex++;
            const container = document.getElementById('filesContainer');

            // Dosya alanı oluşturuluyor
            const fileWrapper = document.createElement('div');
            fileWrapper.className = 'col-12 mt-2';
            fileWrapper.id = `fileContainer-${fileIndex}`;

            const fileTitle = document.createElement('input');
            fileTitle.type = 'text';
            fileTitle.className = 'form-control form-control-sm mb-2';
            fileTitle.placeholder = 'Dosya Başlığı';
            fileTitle.name = 'file_name[]';

            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.className = 'form-control form-control-sm mb-2';
            fileInput.name = 'file_file[]';
            fileInput.accept = 'application/pdf'; // Sadece PDF kabul et

            // Add an event listener to check file size
            fileInput.addEventListener('change', function () {
                const file = this.files[0]; // Get the selected file
                if (file) {
                    // Check if the file size exceeds 2MB (2 * 1024 * 1024 bytes)
                    const maxSizeInBytes = 60 * 1024 * 1024;
                    if (file.size > maxSizeInBytes) {
                        alert('En Fazla 60mb dosya yükleyebilirsiniz.');
                        this.value = ''; // Clear the input
                    }
                }
            });

            const removeButton = document.createElement('button');
            removeButton.className = 'btn btn-sm btn-danger';
            removeButton.type = 'button';
            removeButton.innerText = 'Kaldır';
            removeButton.addEventListener('click', function () {
                document.getElementById(`fileContainer-${fileIndex}`).remove();
            });

            fileWrapper.appendChild(fileTitle);
            fileWrapper.appendChild(fileInput);
            fileWrapper.appendChild(removeButton);
            container.appendChild(fileWrapper);
        });
    </script>
    <script>
        function paymentTypeChange(select){
            console.log($(select).val());
            if ($(select).val() == 'pesin'){
                $(select).closest('.row').find('.period_payment_amount').html('Ödeme Tutarı');
            }else{
                $(select).closest('.row').find('.period_payment_amount').html('Risk Tutarı');
            }
        }
        @if(isset($_GET['type']) && $_GET['type'] == 'hakedis')
            $(document).on("click", '.add-period', function() {
                var periodHtml = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Ödeme Günü</label>
                            <select class="form-control form-control-sm" name="period_last_payment_day" required>
                                <option value="">Gün Seçiniz</option>
                                @for ($day = 1; $day <= 30; $day++)
                                    <option value="{{ $day }}">Her ayın {{ $day }}. günü</option>
                                @endfor
                            </select>
                        </div>
                    </div>
                     <div class="col-md-3 d-none">
                        <div class="form-group">
                            <label class="form-label period_payment_amount">Ödeme Tutarı</label>
                            <input type="text" class="form-control form-control-sm turk-lirasi" name="period_payment_amount">
                        </div>
                    </div>
                    <div class="col-md-3 d-none">
                        <div class="form-group">
                            <label class="form-label">Ödeme Alındı mı?</label>
                            <select class="form-control-sm form-control" required name="period_payment_completed">
                                     <option value="1">Evet</option>
                                <option value="0">Hayır</option>

                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Aylık Limit Tipi</label>
                            <select class="form-control-sm form-control" required name="period_limit_type">
                                <option value="count">Ekspertiz Adeti</option>
                                <option value="amount">Ekspertiz Tutarı</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Aylık Limit</label>
                            <input type="text" class="form-control form-control-sm" required name="period_limit_value">
                        </div>
                    </div>
                    <div class="col-md-1 d-none">
                        <div class="form-group">
                            <button class="btn btn-sm mt-4 btn-danger" onclick="$(this).closest('.row').remove()">Kaldır</button>
                        </div>
                    </div>
                </div>`;

                $('#contractContainer').append(periodHtml);

                $('select[name="period_limit_type"]').on('change', function () {
                    let $val = $(this).val();
                    let $input = $('input[name="period_limit_value"]');

                    if ($val === 'amount') {
                        $input.addClass('turk-lirasi').val('');
                    } else {
                        $input.removeClass('turk-lirasi').val('');
                    }
                });
            });
        @elseif(isset($_GET['type']) && $_GET['type'] == 'hedefli')
            function formatDate(date) {
                var year = date.getFullYear();
                var month = String(date.getMonth() + 1).padStart(2, '0'); // Months are zero-based
                var day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }
            $(document).ready(function () {
                var periodIndex = 0; // Dönem sayacını başlatıyoruz

                $('.add-period').click(function () {
                    var firstRow = '';
                    if (parseInt($('input[name="total_count"]').val()) > 0) {
                        firstRow = `
                            <tr>
                                <td><input type="number" class="form-control form-control-sm" min="1" value="" required name="min_adet[]" data-date="${periodIndex}" data-index=""></td>
                                <td>araç ve üzeri</td>
                                <td><input type="number" class="form-control form-control-sm turk-lirasi" min="1" required value="${$('input[name="best_price"]').val()}" name="birim_fiyat[]" data-date="${periodIndex}" data-index=""></td>
                                <td>₺ birim fiyat</td>
                                <td><button class="btn btn-danger btn-sm remove-row">Sil</button></td>
                            </tr>
                        `;
                    }

                    var monthBox = `
                    <div class="col-12 mt-2">
                        <div class="month-box">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">Ödeme Günü</label>
                                        <select class="form-control form-control-sm" required name="period_last_payment_day">
                                            <option value="">Gün Seçiniz</option>
                                            @for ($day = 1; $day <= 30; $day++)
                                                    <option value="{{ $day }}">Her ayın {{ $day }}. günü</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3 d-none">
                                    <div class="form-group">
                                        <label class="form-label period_payment_amount">Risk Tutarı</label>
                                        <input type="text" class="form-control form-control-sm turk-lirasi" name="period_payment_amount">
                                    </div>
                                </div>
                                <div class="col-md-3 d-none">
                                    <div class="form-group">
                                        <label class="form-label">Ödeme Alındı mı?</label>
                                        <select class="form-control-sm form-control" required name="period_payment_completed">
                                            <option value="1">Evet</option>
                                            <option value="0">Hayır</option>

                                        </select>
                                    </div>
                                </div>
                                @if($_GET['type'] == 'hedefli')
                                    <div class="col-md-3 hide-until-customer">
                                        <label class="form-label">Stok</label>
                                        <select name="stock_id[]" id="stock_id" class="select2" required="">
                                            <option value="">Lütfen Seçim Yapınız</option>
                                            @foreach($stocks as $s)
                                                <option value="{{$s->id}}">{{$s->ad}} ({{ $s->getStandartPrices?->kdv_dahil_fiyat }}₺)</option>
                                            @endforeach
                                        </select>
                                    </div>
                            @endif
                                <div class="col-md-1 d-none">
                                    <div class="form-group">
                                        <button class="btn btn-sm mt-4 btn-danger" onclick="$(this).closest('.month-box').remove()">Kaldır</button>
                                    </div>
                                </div>
                            </div>
                            <div class="items">
                                <table class="table table-striped">
                                    <tbody>${firstRow}</tbody>
                                </table>
                            </div>
                            <input type="hidden" name="month[]" value="${periodIndex}">
                            <button class="btn btn-danger btn-sm mt-3 add-row" type="button" data-date="${periodIndex}" data-index="">Hedef Ekle</button>
                        </div>
                    </div>`;

                    $('#contractContainer').append(monthBox);

                    $(".select2").select2({
                        placeholder: "Ara.."
                    });

                    periodIndex++; // Her yeni dönem eklenince sayacı arttırıyoruz

                    $('.add-row').off('click').click(function () {
                        var date = $(this).data('date');
                        var index = $(this).data('index');
                        var row = `
                    <tr>
                        <td><input type="number" class="form-control form-control-sm" min="1" required name="min_adet[]" data-date="${date}" data-index=""></td>
                        <td>araç ve üzeri</td>
                        <td><input type="text" class="form-control form-control-sm turk-lirasi" required name="birim_fiyat[]" data-date="${date}" data-index=""></td>
                        <td>₺ birim fiyat</td>
                        <td><button class="btn btn-danger btn-sm remove-row">Sil</button></td>
                    </tr>
                `;
                        $(this).siblings('.items').find('tbody').append(row);
                    });

                    $(document).on('click', '.remove-row', function () {
                        $(this).closest('tr').remove();
                    });
                });
            });
        @elseif(isset($_GET['type']) && $_GET['type'] == 'sabit')
            $(document).on('click', '.add-period', function() {
                var periodHtml = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="form-group">
                            <label class="form-label">Ödeme Günü</label>
                            <select class="form-control form-control-sm" name="period_last_payment_day" required>
                                <option value="">Gün Seçiniz</option>
                                @for ($day = 1; $day <= 30; $day++)
                                <option value="{{ $day }}">Her ayın {{ $day }}. günü</option>
                            @endfor
                            </select>
                        </div>
                    </div>
                </div>`;

                $('#contractContainer').append(periodHtml);

                $('select[name="period_limit_type"]').on('change', function () {
                    let $val = $(this).val();
                    let $input = $('input[name="period_limit_value"]');
                    $input.off('keyup blur');
                    if ($val === 'amount') {
                        $input.addClass('turk-lirasi');
                        $input.on('keyup', function () {
                            this.value = this.value.replace(/[^0-9.,]+/g, "");
                        });
                        $input.on('blur', function () {
                            let rawValue = this.value.replace(/[^0-9,.-]+/g, "").replace(',', '.');
                            let amount = parseFloat(rawValue);
                            if (!isNaN(amount)) {
                                this.value = new Intl.NumberFormat('tr-TR', {
                                    style: 'currency',
                                    currency: 'TRY',
                                    minimumFractionDigits: 2
                                }).format(amount);
                            } else {
                                this.value = '';
                            }
                        });
                    } else {
                        $input.removeClass('turk-lirasi').val('');
                    }
                });
            });
        @endif

        $(document).ready(function() {
            $('#hizmetSecim').on('change', function() {
                var selectedOption = $(this).find('option:selected');
                var hizmetId = selectedOption.val();
                var hizmetAdi = selectedOption.data('name');

                // Hizmet zaten eklenmiş mi kontrol et
                var hizmetZatenEklendi = $('#hizmetListesi input[name="stock_id[]"]').filter(function() {
                    return $(this).val() == hizmetId;
                }).length > 0;

                if(hizmetZatenEklendi) {
                    Toast.fire({
                        icon: "error",
                        title: "Bu hizmet zaten eklendi!"
                    });
                } else if(hizmetId) {
                    var newRow = `
                        <tr>
                            <td>
                                <input class="form-control form-control-sm" readonly value="${hizmetAdi}">
                                <input type="hidden" name="stock_id[]" value="${hizmetId}">
                            </td>
                            @if(isset($_GET['type']) && $_GET['type'] == 'sabit')
                            <td>
                                <input class="form-control form-control-sm turk-lirasi" name="buyer_price[]">
                            </td>
                              @endif
<!--                            <td>-->
<!--                                <input class="form-control form-control-sm turk-lirasi" readonly name="seller_price[]">-->
<!--                            </td>-->
                            <td>
                                <input class="form-control form-control-sm turk-lirasi" name="contract_owner_price[]">
                            </td>
                            <td>
                                <button type="button" class="btn btn-danger btn-sm kaldir-btn">Kaldır</button>
                            </td>
                        </tr>
                    `;

                    $('#hizmetListesi').append(newRow);
                }
            });

            // Kaldır butonuna tıklayınca satırı silme işlemi
            $(document).on('click', '.kaldir-btn', function() {
                $(this).closest('tr').remove();
            });
        });
    </script>
@endpush


