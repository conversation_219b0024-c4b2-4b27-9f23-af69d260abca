@extends('pages.build')
@section('title')
    @if($contract->type == 'hedefli')
        Hede<PERSON><PERSON>zleşme Düzenle
    @elseif($contract->type == 'sabit')
        Sabit Sözleşme Düzenle
    @else
        Hakediş Sözleşme Düzenle
    @endif
@endsection
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        .month-box{
            width: 100%;
            border: 1px solid;
            border-radius: 10px;
            padding: 1rem;
        }
    </style>
@endpush
@section('content')
    <form id="mainForm" method="post" enctype="multipart/form-data" action="{{ route('contracts.update', $contract->id) }}">@csrf
        @method('PUT')
        <input type="hidden" name="type" value="{{ $contract->type }}">
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="temelBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#temelBilgiler" aria-expanded="true"
                                    aria-controls="temelBilgiler">
                                Sözleşme Detayları
                            </button>
                        </h2>
                        <div id="temelBilgiler" class="accordion-collapse collapse show" aria-labelledby="temelBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="form-group col-12">
                                        <label class="form-label">Cari</label>
                                        <input type="text" class="form-control form-control-sm" value="{{ $contract->getCustomer?->fullName }}" disabled>
                                    </div>
                                    <div class="form-group col-12">
                                        <label class="form-label">Cari Risk Tutarı</label>
                                        <input type="text" class="form-control turk-lirasi form-control-sm" value="{{ $contract->getCustomer?->risk_tutari }}" disabled>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="form-label">Sözleşme No</label>
                                            <input type="text" minlength="6" class="form-control form-control-sm" readonly disabled name="no" maxlength="6" value="{{ $contract->no }}">
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="form-label">Sözleşme Süresi (Ay)</label>
                                            <input type="number" min="1" value="{{ $contract->duration_month }}" id="contractDuration" name="duration_month" disabled class="form-control-sm form-control">
                                        </div>
                                    </div>
                                    <!-- <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="form-label">Ödeme Tipi</label>
                                            <select class="form-control-sm form-control" @if(auth()->user()->type == 'admin') readonly="" @else disabled @endif name="period_payment_type">
                                                <option value="veresiye">Veresiye</option>
                                            </select>
                                        </div>
                                    </div> -->
                                    <div class="col-4">
                                        <div class="form-group">
                                            <label class="form-label">Durum</label>
                                            <select name="status" @if(auth()->user()->type != 'admin') disabled @endif class="form-control form-control-sm">
                                                <option value="1" {{ $contract->status == 1 ? 'selected' : '' }}>Aktif</option>
                                                <option value="0" {{ $contract->status == 0 ? 'selected' : '' }}>Pasif</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Sözleşme Yapılan Bayi</label>
                                        <select name="contract_branch_id" class="select2" disabled>
                                            <option value="">Lütfen Seçiniz</option>
                                            @foreach($branches as $branch)
                                                <option value="{{ $branch->id }}" {{ $contract->branch_id == $branch->id ? 'selected' : '' }}>{{ $branch->kisa_ad }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    @if(auth()->user()->type == 'admin')
                                        <div class="col-xl-12 mt-2">
                                            @php $isAll = false; if ($contract->getBranchIds->count() == \App\Models\Branch::where('status', 1)->count()){ $isAll = true;} @endphp
                                            <label class="form-label">Geçerli Bayiler</label>
                                            <select name="branch_id[]" id="branches_id" class="select2" required="" multiple>
                                                <option @if($isAll) selected @endif value="0">Tümü</option>
                                                @foreach($branches as $branch)
                                                    <option @if(!$isAll && in_array($branch->id,$contract->getBranchIds->pluck('branch_id')->toArray())) selected @endif value="{{$branch->id}}">{{$branch->kisa_ad}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    @endif
                                    @if($contract->type == 'hedefli')
                                        <div class="col-xl-12 mt-2">
                                            <label class="form-label">Stok</label>
                                            <input class="form-control-sm form-control" disabled value="@foreach($contract->getStockIds as $contractStockId) {{ \App\Models\Stock::where('id',$contractStockId->stock_id)->first()?->ad }} @endforeach">
                                        </div>
                                    @endif
                                    @if(auth()->user()->type == 'admin')
                                        <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12 mt-2">
                                            <button class="btn btn-sm btn-success">Güncelle</button>
                                        </div>
                                    @endif

                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-7">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="ayrintilarHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#ayrintilar" aria-expanded="false"
                                    aria-controls="ayrintilar">
                                Ayrıntılar
                            </button>
                        </h2>
                        <div id="ayrintilar" class="accordion-collapse collapse show" aria-labelledby="ayrintilarHeading">
                            <div class="accordion-body">
                                <div id="contractContainer">
                                    @foreach($contract->getPeriods as $index => $period)
                                        <div class="row mt-3">
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Dönem Baş. Tarihi</label>
                                                    <input type="date" class="form-control form-control-sm" @if(auth()->user()->type != 'admin') disabled @endif name="period_start_date[{{ $period->id }}]" value="{{ $period->start_date }}">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Dönem Bit. Tarihi</label>
                                                    <input type="date" class="form-control form-control-sm" @if(auth()->user()->type != 'admin') disabled @endif name="period_end_date[{{ $period->id }}]" value="{{ $period->end_date }}">
                                                </div>
                                            </div>
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Ödeme Günü</label>
                                                    <input type="date" class="form-control form-control-sm" @if(auth()->user()->type != 'admin') disabled @endif name="period_last_payment_date[{{ $period->id }}]" value="{{ $period->last_payment_date }}">
                                                </div>
                                            </div>
                                            @if(auth()->user()->type == 'admin' && $contract->type == 'hedefli' && now()->lessThanOrEqualTo($period->end_date . ' 23:59:59'))
                                                <div class="col-md-3" style="display: flex;align-items: center;padding-bottom: 5px;">
                                                    <button type="submit" form="endContractPeriod" name="period_id" value="{{ $period->id }}" class="btn btn-sm mt-3 btn-danger">Dönemi Bitir</button>
                                                </div>
                                            @endif
                                            @if(auth()->user()->type == 'admin' && $contract->type == 'hedefli' &&  now()->greaterThan($period->end_date . ' 23:59:59'))
                                                <div class="col-md-3">
                                                    <button type="submit" form="endContractPeriod" name="continue_period_id" value="{{ $period->id }}" class="btn btn-sm mt-3 btn-danger">Dönemi Devam Ettir</button>
                                                </div>
                                            @endif
                                            {{--                                                @if($index == 0)--}}
                                            {{--                                                    <div class="col-md-3 d-none">--}}
                                            {{--                                                        <div class="form-group">--}}
                                            {{--                                                            <label class="form-label">{{ $period->payment_type == 'pesin' ? 'Ödeme Tutarı' : 'Risk Tutarı' }}</label>--}}
                                            {{--                                                            <input type="text" class="form-control form-control-sm" @if(auth()->user()->type == 'admin') readonly="" @else disabled @endif name="period_payment_amount[{{ $period->id }}]" value="{{ $period->payment_amount }}">--}}
                                            {{--                                                        </div>--}}
                                            {{--                                                    </div>--}}
                                            {{--                                                @endif--}}
                                            <div class="col-md-3">
                                                <div class="form-group">
                                                    <label class="form-label">Ödeme Alındı mı?</label>
                                                    <select class="form-control-sm form-control" @if(auth()->user()->type == 'admin') @else disabled @endif name="period_payment_completed[{{ $period->id }}]">
                                                        <option value="0">Hayır</option>
                                                        <option @if($period->payment_completed == 1) selected @endif value="1">Evet</option>
                                                    </select>
                                                </div>
                                            </div>
                                            @if($contract->type == 'hakedis')
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label class="form-label">Aylık Limit Tipi</label>
                                                        <select class="form-control-sm form-control" disabled name="period_limit_type[{{ $period->id }}]">
                                                            <option @if($period->limit_type == 'count') selected @endif value="count">Ekspertiz Adeti</option>
                                                            <option @if($period->limit_type == 'amount') selected @endif value="amount">Ekspertiz Tutarı</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label class="form-label">Aylık Limit</label>
                                                        <input type="text" class="form-control @if($period->limit_type == 'amount') turk-lirasi @endif form-control-sm" value="{{ $period->limit_value - $period->getAddeds->sum('amount') }}" disabled name="period_limit_value[{{ $period->id }}]">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label class="form-label">Takviye Limit</label>
                                                        <input type="text" class="form-control @if($period->limit_type == 'amount') turk-lirasi @endif form-control-sm" value="{{ $period->getAddeds->sum('amount') }}" disabled name="period_limit_value[{{ $period->id }}]">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label class="form-label">
                                                            Kalan Limit
                                                        </label>
                                                        <input type="text" class="form-control @if($period->limit_type == 'amount') turk-lirasi @endif form-control-sm" value="{{ $period->limit_value -  ($period->limit_type == 'amount' ? $contractFallFromLimitPayments->whereBetween('created_at',[$period->start_date,$period->end_date])->sum('amount') : $contractFallFromLimitPayments->whereBetween('created_at',[$period->start_date,$period->end_date])->count()) }}" disabled name="period_limit_value[{{ $period->id }}]">
                                                    </div>
                                                    <button type="button" onclick="$('#addLimitModal{{ $period->id}}').modal('show')" class="btn btn-sm btn-danger">Limit Ekle</button>
                                                    <div class="modal fade" id="addLimitModal{{ $period->id }}" tabindex="-1" aria-labelledby="addLimitModal{{ $period->id }}" data-bs-keyboard="false" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered">
                                                            <div class="modal-content">
                                                                <div class="modal-header">
                                                                    <h6 class="modal-title text-danger" id="staticBackdropLabel2">Limit Ekle</h6>
                                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                </div>
                                                                <div class="modal-body">
                                                                    <input type="hidden" name="id" form="addContractPeriodLimit{{ $period->id }}" value="{{ $period->id }}">
                                                                    <input type="hidden" name="type" form="addContractPeriodLimit{{ $period->id }}" value="{{ $period->limit_type }}">
                                                                    <div class="form-group">
                                                                        <label>Eklenecek Limit</label>
                                                                        <input type="text" required form="addContractPeriodLimit{{ $period->id }}" name="amount" class="form-control-sm @if($period->limit_type == 'amount') turk-lirasi @endif form-control">
                                                                    </div>
                                                                    <div class="form-group">
                                                                        <button form="addContractPeriodLimit{{ $period->id }}" class="btn btn-sm btn-danger">Ekle</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                @if($period->getAddeds)
                                                    <div class="col-12 mb-3">
                                                        <div class="table-responsive">
                                                            <table class="table table-striped">
                                                                <thead>
                                                                <tr>
                                                                    <td><b>Limit Eklenen Miktar</b></td>
                                                                    <td><b>Limit Ekleyen Kişi</b></td>
                                                                    <td><b>Tarih</b></td>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                @foreach($period->getAddeds->reverse() as $added)
                                                                    <tr>
                                                                        <td><input style="width: 140px" class="form-control-sm @if($period->limit_type == 'amount') turk-lirasi @endif form-control" disabled value="{{ $added->amount }}"></td>
                                                                        <td>{{ $added->name . ' ' . $added->second_name . ' ' . $added->surname  }}</td>
                                                                        <td>{{ $added->created_at->translatedFormat('d F Y H:i') }}</td>
                                                                    </tr>
                                                                @endforeach
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                @endif
                                            @elseif($contract->type == 'hedefli')
                                                <div class="col-12 mt-2">
                                                    <div class="table-responsive">
                                                        <table class="table table-striped table-hover">
                                                            <thead>
                                                            <tr>
                                                                <th>Hedef Miktarı</th>
                                                                <th>Birim Fiyatı(₺)</th>
                                                                <th>Hedef Durum</th>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            @php
                                                                $count = $contract->getTargetCount($period->id);
                                                                $lastUnitPrice = 0;
                                                            @endphp
                                                            @foreach($period->getTargets as $target)
                                                                @php
                                                                    if ($count >= $target->min_count){
                                                                        $lastUnitPrice = $target->unit_price;
                                                                    }
                                                                @endphp
                                                                <tr>
                                                                    <td>{{ $target->min_count }}</td>
                                                                    <td><input type="number" class="form-control-sm form-control" name="target_price[{{ $target->id }}]" style="width: 200px" value="{{ $target->unit_price }}"></td>
                                                                    <td>{!! $count >= $target->min_count ? '<i class="fa fa-arrow-up"></i>' : '-' !!}</td>
                                                                </tr>
                                                            @endforeach
                                                            </tbody>
                                                            <tfoot>
                                                            <tr style="text-align: right">
                                                                <td colspan="3">Yapılan Adet : {{ $count }}</td>
                                                            </tr>
                                                            @if(now()->greaterThanOrEqualTo($period->end_date))
                                                                <tr style="text-align: right">
                                                                    {{--                                                                            <td colspan="3"><a target="_blank" href="{{ route('expertises.index',['contract_id'=>$contract->id,'contract_no'=>$contract->no,'start_date'=>$period->start_date,'end_date'=>$period->end_date]) }}" class="btn btn-sm btn-danger">Kayıtlar</a> </td>--}}
                                                                    <td colspan="3">
                                                                        <button type="button" onclick="$('#branchRecords{{ $period->id}}').modal('show')" class="btn btn-sm btn-danger">Bayi Hakediş</button>
                                                                        <div class="modal fade" id="branchRecords{{ $period->id }}" tabindex="-1" aria-labelledby="branchRecords{{ $period->id }}" data-bs-keyboard="false" aria-hidden="true">
                                                                            <div class="modal-dialog modal-lg modal-dialog-centered">
                                                                                <div class="modal-content">
                                                                                    <div class="modal-header">
                                                                                        <h6 class="modal-title text-danger" id="staticBackdropLabel2">Bayi Hakediş</h6>
                                                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                                                    </div>
                                                                                    <div class="modal-body">
                                                                                        <div class="table-responsive">
                                                                                            <table class="table table-striped">
                                                                                                <thead>
                                                                                                <tr>
                                                                                                    <td>Bayi</td>
                                                                                                    <td>İşlem Adeti</td>
                                                                                                    <td>Dönem İçi Alınan</td>
                                                                                                    <td>Dönem Sonu Hakediş</td>
                                                                                                    <td>Fark</td>
                                                                                                </tr>
                                                                                                </thead>
                                                                                                <tbody>
                                                                                                @foreach($period->getExpertisePayments()->groupBy('branch_id') as $branchID => $values)
                                                                                                    <tr>
                                                                                                        <td>{{ $values[0]?->kisa_ad }}</td>
                                                                                                        <td>{{ count($values) }}</td>
                                                                                                        <td>{{ $values->sum('amount') }}₺</td>
                                                                                                        <td>{{ $lastUnitPrice * $count }}₺</td>
                                                                                                        <td>{{ ($lastUnitPrice * $count) - $values->sum('amount') }}₺</td>
                                                                                                    </tr>
                                                                                                @endforeach
                                                                                                </tbody>
                                                                                            </table>
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </td>
                                                                </tr>
                                                            @endif
                                                            @if(\App\Models\ContractTarget::where('contract_id',$contract->id)->where('contract_period_id',$period->id)->onlyTrashed()->exists())
                                                                <tr class="text-center">
                                                                    <th colspan="4">Fiyat Geçmişi</th>
                                                                </tr>
                                                                <tr>
                                                                    <th>Hedef Miktarı</th>
                                                                    <th>Birim Fiyatı(₺)</th>
                                                                    <th>Fiyat Geçerlilik Tarihi</th>
                                                                </tr>
                                                                @forelse(\App\Models\ContractTarget::where('contract_id',$contract->id)->where('contract_period_id',$period->id)->onlyTrashed()->get() as $deletedPrices)
                                                                    <tr>
                                                                        <td>{{ $deletedPrices->min_count }}</td>
                                                                        <td>{{ $deletedPrices->unit_price }}₺</td>
                                                                        <td>{{ $deletedPrices->created_at->translatedFormat('d F Y H:i') }} - {{ $deletedPrices->deleted_at->translatedFormat('d F Y H:i') }}</td>
                                                                    </tr>
                                                                @empty
                                                                @endforelse
                                                            @endif
                                                            </tfoot>
                                                        </table>
                                                    </div>
                                                </div>
                                            @endif
                                        </div>
                                        @if($contract->type == 'hakedis')
                                            <div class="table-responsive">
                                                <h5>Özet</h5>
                                                <table class="table table-striped">
                                                    <thead>
                                                    <tr>
                                                        <th><b>Hizmet</b></th>
                                                        <th><b>Yapılan Adet</b></th>
                                                        <th><b>Toplam Tutar</b></th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    @foreach($contract->getStockIds->groupBy('stock_id') as $stockID => $stockPrices)
                                                        @php
                                                            $periodStockExpertises = \App\Models\Expertise::whereHas('getStockhasOne',function ($getStockhasOne) use ($stockID){
                                                                return $getStockhasOne->where('stock_id',$stockID);
                                                            })
                                                            ->whereHas('getPayment',function ($getPayment) use ($contract,$contractCodes){
                                                                return $getPayment->where('payment_code',$contract->no)
                                                                ->whereIn('payment_detail',$contractCodes);
                                                            })
                                                            ->where('status',1)
                                                            ->whereBetween('created_at',[$period->start_date,$period->end_date])
                                                            ->get();

                                                            $periodExpertisePayments = \App\Models\ExpertisePayment::whereIn('expertise_id',$periodStockExpertises->pluck('id')->toArray())
                                                            ->where('payment_code',$contract->no)
                                                            ->whereIn('payment_detail',$contractCodes)
                                                            ->sum('amount');
                                                        @endphp
                                                        <tr>
                                                            <th>{{ \App\Models\Stock::where('id',$stockID)->first()?->ad }}</th>
                                                            <th>{{ $periodStockExpertises->count() }}</th>
                                                            <th>{{ $periodExpertisePayments }}₺</th>
                                                        </tr>
                                                    @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        @endif
                                        <hr style="color: #ff1000;border-top: 3px solid">
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    </div>
                        @if($contract->type == 'hakedis' || $contract->type == 'sabit')
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="fiyatlarHeading">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#fiyatlar" aria-expanded="true"
                                        aria-controls="fiyatlar">
                                    Fiyatlar
                                </button>
                            </h2>
                            <div id="fiyatlar" class="accordion-collapse collapse show" aria-labelledby="fiyatlarHeading">
                                <div class="accordion-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover table-striped">
                                            <thead>
                                            <tr>
                                                <td colspan="2">Hizmet</td>
                                                    @if($contract->type == 'sabit')
                                                        <td>Alıcı Fiyatı</td>
                                                    @endif

{{--                                                <td>Satıcı Fiyatı</td>--}}
                                                <td>Sözleşme Sahibi Fiyatı</td>
                                            </tr>
                                            </thead>
                                            <tbody id="hizmetListesi">
                                            @forelse($contract->getStockIds->groupBy('stock_id') as $stockId => $contractStockIds)
                                                @php $contractStock = \App\Models\ContractStock::where('contract_id',$contract->id)->where('stock_id',$stockId)->first()?->getStock; @endphp
                                                <tr>
                                                    <td colspan="2">
                                                        <input class="form-control form-control-sm" readonly value="{{ $contractStock?->ad }}">
                                                        <input type="hidden" name="stock_id[]" form="mainForm" value="{{ $contractStock?->id }}">
                                                    </td>
                                                    @if($contract->type == 'sabit')
                                                        <td>
                                                            <input class="form-control form-control-sm" form="mainForm" name="buyer_price[]" value="{{ $contractStockIds->where('type','alici')->first()?->price }}">
                                                        </td>
                                                    @endif

{{--                                                    <td>--}}
{{--                                                        <input class="form-control form-control-sm" form="mainForm" name="seller_price[]" value="{{ $contractStockIds->where('type','satici')->first()?->price }}">--}}
{{--                                                    </td>--}}
                                                    <td>
                                                        <input class="form-control form-control-sm" form="mainForm" name="contract_owner_price[]" value="{{ $contractStockIds->where('type','sozlesme_sahibi')->first()?->price }}">
                                                    </td>
                                                </tr>
                                            @empty
                                            @endforelse
                                            </tbody>
                                            @if(\App\Models\ContractStock::where('contract_id',$contract->id)->onlyTrashed()->exists())
                                                <tfoot>
                                                <tr class="text-center">
                                                    <th colspan="4">Fiyat Geçmişi</th>
                                                </tr>
                                                <tr>
                                                    <td>Stok</td>
                                                    <td>Geçerli Cari</td>
                                                    <td>Fiyat</td>
                                                    <td>Geçerlilik Tarihi</td>
                                                </tr>
                                                @forelse(\App\Models\ContractStock::where('contract_id',$contract->id)->onlyTrashed()->get() as $deletedPrices)
                                                    <tr>
                                                        <td>{{ $deletedPrices?->getStock->ad }}</td>
                                                        <td>
                                                            @if($deletedPrices->type == 'satici')
                                                                Satıcı
                                                            @elseif($deletedPrices->type == 'alici')
                                                                Alıcı
                                                            @else
                                                                Sözleşme Sahibi
                                                            @endif
                                                        </td>
                                                        <td>{{ $deletedPrices->price }}₺</td>
                                                        <td>{{ $deletedPrices->created_at->translatedFormat('d F Y H:i') }} - {{ $deletedPrices->deleted_at->translatedFormat('d F Y H:i') }}</td>
                                                    </tr>
                                                @empty
                                                @endforelse
                                                </tfoot>
                                            @endif

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endif
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="dosyalarHeading">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#dosyalar"
                                        aria-expanded="false" aria-controls="dosyalar">
                                    Sözleşme Dosyaları
                                </button>
                            </h2>
                            <div id="dosyalar" class="accordion-collapse collapse" aria-labelledby="dosyalarHeading">
                                <div class="accordion-body">
                                    <div class="row" id="filesContainer">
                                        @foreach($contract->getFiles as $file)
                                            <div class="col-12 mt-2" id="fileContainer-{{ $loop->index }}">
                                                <input type="text" class="form-control form-control-sm mb-2" placeholder="Dosya Başlığı" name="file_name[]" value="{{ $file->name }}">
                                                <a href="/storage/{{ $file->file }}" target="_blank" type="button" class="btn btn-sm btn-danger">Sözleşme Görüntüle</a>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
            </div>
            <div class="col-md-12 d-none">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="ozelKodHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#ozelKod" aria-expanded="true"
                                    aria-controls="ozelKod">
                                Özel Kod Üret
                            </button>
                        </h2>
                        <div id="ozelKod" class="accordion-collapse collapse show" aria-labelledby="ozelKodHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label class="form-label">Üretilecek Adet</label>
                                            <input type="number" min="1" class="form-control form-control-sm" name="code_count" value="1">
                                        </div>
                                        <button type="button" class="btn btn-success create-codes btn-sm">Üret</button>
                                    </div>
                                    <div class="special-codes">
                                        <table class="table table-striped">
                                            <thead>
                                            <tr>
                                                <td>Kod</td>
                                                <td>Plaka (Opsiyonel)</td>
                                                <td>Geçerli Stok</td>
                                                <td>Geçerli Bayi</td>
                                                <td>Müşteri</td>
                                                <td>Ödeme Alınsın mı?</td>
                                            </tr>
                                            </thead>
                                            <tbody class="codes"></tbody>
                                            <tfoot>
                                            <tr>
                                                <td><button form="createContractCode" class="btn btn-sm btn-success">Kaydet</button> </td>
                                            </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="codesHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#codes" aria-expanded="true"
                                    aria-controls="codes">
                                Üretilen Kodlar
                            </button>
                        </h2>
                        <div id="codes" class="accordion-collapse collapse show" aria-labelledby="codesHeading">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                                        <thead>
                                        <tr>
                                            <th>Kod</th>
                                            <th>Kodu Kullanan Plaka</th>
                                            <th>Geçerli Bayi</th>
                                            <th>Üretim Tarihi</th>
                                            <th>Cari Türü</th>
                                            <th>Fatura Carisi</th>
                                            <th>Ödeme Alınsın mı?</th>
                                            <th>Sil</th>
                                        </tr>
                                        </thead>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="expertisesHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#expertises" aria-expanded="true"
                                    aria-controls="expertises">
                                Ekspertiz Kayıtları
                            </button>
                        </h2>
                        <div id="expertises" class="accordion-collapse collapse show" aria-labelledby="expertisesHeading">
                            <div class="accordion-body">
                                <div class="table-responsive">
                                    <table id="responsiveDataTable2" class="table table-bordered text-nowrap" style="width:100%">
                                        <thead>
                                        <tr>
                                            <th>Cari Adı</th>
                                            <th>Bayi Kısa Adı</th>
                                            <th>Belge Tarihi</th>
                                            <th>Paket İsmi</th>
                                            <th>Ekspertiz Tutarı</th>
{{--                                            <th>Dönem Kapama Tutarı</th>--}}
{{--                                            <th>Dönem Kapama Farkı</th>--}}
                                            <th>Plaka</th>
                                            <th>Şase No</th>
                                            <th>Alıcı</th>
                                            <th>Kod</th>
                                            <th>Satıcı</th>
                                            <th>Belge No</th>
                                            <th>Muhasebe Mutabakat?</th>
                                        </tr>
                                        </thead>
                                    </table>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <form id="createContractCode" method="post" action="{{ route('createContractCode',$contract) }}"> @csrf    </form>
    <form id="endContractPeriod" method="post" action="{{ route('endContractPeriod') }}">@csrf</form>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(document).ready(function () {
            $('#responsiveDataTable').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('contract.codes') }}",
                    data: function (d) {
                        d._token = '{{ csrf_token() }}';
                        d.contract_id = '{{ $contract->id }}';
                    }
                },
                order: [[3, "desc"]],
                columns: [
                    { data: 'code', name: 'code' },
                    { data: 'plate', name: 'plate' },
                    { data: 'branch_count', name: 'branch_count' },
                    { data: 'created_at', name: 'created_at' },
                    { data: 'customer_type', name: 'customer_type' },
                    { data: 'invoice_customer', name: 'invoice_customer' },
                    { data: 'must_payment', name: 'must_payment' },
                    { data: 'action', name: 'action', orderable: false, searchable: false }
                ],
                language: {
                    "sEmptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "sInfo": "_TOTAL_ kayıttan _START_ - _END_ arası gösteriliyor",
                    "sInfoEmpty": "Kayıt yok",
                    "sInfoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "sLengthMenu": "Sayfada _MENU_ kayıt göster",
                    "sLoadingRecords": "Yükleniyor...",
                    "sProcessing": "İşleniyor...",
                    "sSearch": "Ara:",
                    "sZeroRecords": "Eşleşen kayıt bulunamadı",
                    "oPaginate": {
                        "sFirst": "İlk",
                        "sLast": "Son",
                        "sNext": "Sonraki",
                        "sPrevious": "Önceki"
                    },
                    "oAria": {
                        "sSortAscending": ": artan sıralama için tıklayın",
                        "sSortDescending": ": azalan sıralama için tıklayın"
                    }
                }
            });

            $(document).on('click', '.delete-btn', function () {
                var contractCodeId = $(this).data('id');
                if (confirm('Are you sure you want to delete this contract?')) {
                    $.ajax({
                        url: "{{ route('contract.codes.destroy', 'ID') }}".replace('ID', contractCodeId),
                        type: 'POST',
                        data: {
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            $('#responsiveDataTable').DataTable().ajax.reload();
                        }
                    });
                }
            });
            $('#responsiveDataTable2').DataTable({
                processing: true,
                serverSide: true,
                ajax: {
                    url: "{{ route('contract.periods') }}",
                    data: function (d) {
                        d._token = '{{ csrf_token() }}';
                        d.contract_id = '{{ $contract->id }}';
                    }
                },
                order: [[2, "desc"]], // Order by 'Belge Tarihi'  descending
                columns: [
                    { data: 'cari_adi', name: 'cari_adi' },
                    { data: 'bayi_kisa_adi', name: 'bayi_kisa_adi' },
                    { data: 'belge_tarihi', name: 'belge_tarihi' },
                    { data: 'paket_ismi', name: 'paket_ismi' },
                    { data: 'ekspertiz_tutari', name: 'ekspertiz_tutari' },
                    { data: 'plaka', name: 'plaka' },
                    { data: 'sase_no', name: 'sase_no' },
                    { data: 'alici', name: 'alici' },
                    { data: 'kod', name: 'kod' },
                    { data: 'satici', name: 'satici' },
                    { data: 'belge_no', name: 'belge_no' },
                    { data: 'muhasebe_mutabakat', name: 'muhasebe_mutabakat' }
                ],
                language: {
                    "sEmptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "sInfo": "_TOTAL_ kayıttan _START_ - _END_ arası gösteriliyor",
                    "sInfoEmpty": "Kayıt yok",
                    "sInfoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "sLengthMenu": "Sayfada _MENU_ kayıt göster",
                    "sLoadingRecords": "Yükleniyor...",
                    "sProcessing": "İşleniyor...",
                    "sSearch": "Ara:",
                    "sZeroRecords": "Eşleşen kayıt bulunamadı",
                    "oPaginate": {
                        "sFirst": "İlk",
                        "sLast": "Son",
                        "sNext": "Sonraki",
                        "sPrevious": "Önceki"
                    },
                    "oAria": {
                        "sSortAscending": ": artan sıralama için tıklayın",
                        "sSortDescending": ": azalan sıralama için tıklayın"
                    }
                }
            });
        });

        $(".select2").select2({
            placeholder: "Ara.."
        });
        $('.create-codes').on('click', function () {
            let count = $('input[name="code_count"]').val();
            if (count > 0) {
                let html = '';
                for (let i = 0; i < count; i++) {
                    html += `<tr><td><input minlength="10" maxlength="10" form="createContractCode" name="special_code[]" value="${generateCustomCode()}" readonly class="form-control-sm special_code form-control"></td>` +
                        '<td><input name="plaka[]" form="createContractCode" class="form-control-sm plaka form-control"></td>' +
                        `<td><select class="form-control contract_stock_id form-control-sm" required form="createContractCode" name="contract_stock_id[`+i+`][]"><option value="0">Sözleşme Stokları</option> @foreach(\App\Models\ContractStock::where('contract_id',$contract->id)->get()->groupBy('stock_id') as $stockId => $stocks)  <option value="{{ $stocks[0]->branch_id }}" data-select="@foreach($stocks as $stc) <option value='{{ $stc->type }}'>{{ $stc->type == 'alici' ? 'Alıcı' : ($stc->type == 'satici' ? 'Satıcı' : $contract->getCustomer?->fullName) }}</option> @endforeach">{{ $stocks[0]->getStock?->ad }}</option> @endforeach </select></td>` +
                        `<td><select class="select2" multiple form="createContractCode" name="contract_branch_id[`+i+`][]"><option value="0">Sözleşme Bayileri</option> @foreach(\App\Models\ContractBranch::where('contract_id',$contract->id)->get() as $branch) <option value="{{ $branch->branch_id }}">{{ $branch->getBranch?->kisa_ad }}</option> @endforeach</select></td>` +
                        '<td><select required class="form-control contract_type form-control-sm" form="createContractCode" name="contract_type[]"><option value="">Seçilmedi</option><option value="sozlesme_sahibi">Sözleşme Sahibi</option></select></td>' +
                        '<td><select required class="form-control contract_must_payment form-control-sm" form="createContractCode" name="contract_must_payment[]"><option value="">Seçilmedi</option>@if($contract->type != 'hedefli')<option value="1">Evet</option>@endif @if($contract->type != 'sabit')<option value="0">Hayır</option>@endif</select></td>' +
                        '<td><button type="button" class="btn btn-danger btn-sm delete-row">Sil</button></td>' +
                        '</tr>';
                }
                $('.codes').html(html);
            }

            // Sözleşme türüne göre ödeme alanını ayarlama
            $('.contract_type').on('change', function () {
                let paymentField = $(this).parent().parent().find('.contract_must_payment');
                @if($contract->type == 'sabit')
                    paymentField.html('<option value="1">Evet</option>');
                @elseif($contract->type == 'hakedis')
                    if ($(this).val() === 'sozlesme_sahibi') {
                        paymentField.html('<option value="0">Hayır</option>');
                    } else {
                        paymentField.html('<option value="1">Evet</option>');
                    }
                @else
                    if ($(this).val() === 'sozlesme_sahibi') {
                        paymentField.html('<option value="0">Hayır</option>');
                    } else {
                        paymentField.html('<option value="1">Evet</option>@if($period?->payment_type == 'pesin')<option value="0">Hayır</option>@endif');
                    }
                @endif

            });

            // Select2 alanını başlatma
            $(".select2").select2({
                placeholder: "Ara.."
            });

            // Contract stock seçimine göre contract_type güncelleme
            $('.contract_stock_id').on('change', function () {
                if ($(this).val() !== '') {
                    $(this).parent().parent().find('.contract_type').html('<option value="">Seçilmedi</option><option value="sozlesme_sahibi">Sözleşme Sahibi</option>');
                } else {
                    $(this).parent().parent().find('.contract_type').html($(this).find('option:selected').data('select'));
                }
            });

            // Satırdaki "Sil" düğmesine basıldığında satırı silme işlevi
            $(document).on('click', '.delete-row', function () {
                $(this).closest('tr').remove();
            });

            // Özel kod ve plaka alanlarındaki boşlukları temizleme ve büyük harfe çevirme
            $('.special_code, .plaka').on('keyup', function () {
                $(this).val($(this).val().replaceAll(' ', '').toUpperCase());
            });
        });


        function generateCustomCode() {
            const prefix = 'UMR{{ str_pad(($contract->getCustomer?->il_kodu ?? 1),2,0,STR_PAD_LEFT) }}';
            const characters = 'ABCDEFGHJKLMNOPQRSTUVWXYZ023456789';
            let result = prefix;
            const charactersLength = characters.length;
            for (let i = 0; i < 4; i++) {
                result += characters.charAt(Math.floor(Math.random() * charactersLength));
            }
            return result;
        }

        $(document).on("input", ".turk-lirasi", function (e) {
            let value = $(this).val();
            // Remove TL symbol if it exists, then clean the input
            let numericValue = value.replace(/[^0-9,\.]/g, '');
            // Ensure only one comma or one dot is allowed
            let commaParts = numericValue.split(',');

            if (commaParts.length > 2) {
                numericValue = commaParts[0] + ',' + commaParts.slice(1).join('');
            }

            let dotParts = numericValue.split('.');

            if (dotParts.length > 2) {
                numericValue = dotParts[0] + '.' + dotParts.slice(1).join('');
            }

            // Add TL symbol to the left of the cleaned value
            $(this).val(`₺ ${numericValue}`);
        });

        $(document).on("keydown", ".turk-lirasi", function (e) {
            // Allow backspace, arrow keys, delete, tab, comma (188), numpad comma (110), and dot (190)
            if ([8, 37, 39, 46, 9, 188, 190, 110].includes(e.keyCode)) {
                return true;
            }

            // Allow numeric keys and numpad keys
            if (
                !(e.keyCode >= 48 && e.keyCode <= 57) &&  // Top-row numbers
                !(e.keyCode >= 96 && e.keyCode <= 105) &&  // Numpad numbers
                e.keyCode !== 188 &&  // Comma (,)
                e.keyCode !== 190 &&  // Dot (.)
                e.keyCode !== 110 &&  // Numpad comma (,)
                !e.ctrlKey // Disallow other Ctrl combinations
            ) {
                e.preventDefault();
            }
        });

    </script>
@endpush
