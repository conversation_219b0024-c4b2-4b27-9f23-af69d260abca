@extends('pages.build')
@section('title','<PERSON><PERSON><PERSON>')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        .select2-container{
            display: block;
            z-index: 999;
            position: unset;
        }
        .step{
            display: none;
        }
    </style>
@endpush
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card p-2">
                <form method="get">
                    <div class="row">
                        <div class="col-12 col-lg-4 col-md-4 col-sm-12">
                            <label for="status">Durumu</label>
                            <select name="status" id="status" class="form-control">
                                <option value="">Hepsi</option>
                                <option @if($status == 1) selected="" @endif value="1">Aktif</option>
                                <option @if($status == 2) selected="" @endif value="2">Pasif</option>
                            </select>
                        </div>
                        <div class="col-12 col-lg-4 col-md-4 col-sm-12 mt-2">
                            <br>
                            <button type="submit" class="btn btn-success">Gönder</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    @if(in_array('*', $userRoles) || in_array('add_contract', $userRoles))
                        <a href="{{ route('contracts.create',['type'=>'hakedis']) }}" class="nav-link">Hakediş Türü Sözleşme Ekle</a>
                        <a href="{{ route('contracts.create',['type'=>'sabit']) }}" class="nav-link">Sabit Fiyatlı Sözleşme Ekle</a>
                        <a href="{{ route('contracts.create',['type'=>'hedefli']) }}" class="nav-link">Hedefli Sözleşme Ekle</a>
                        <a href="{{ route('contracts.periods') }}" class="nav-link">Ödeme Tablosu</a>
                        <!-- <a href="#" class="nav-link" onclick="$('#createCodeModal').modal('show')">Kod Üret</a> -->
                    @endif
                </nav>

                <div class="card-body">
                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr><th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th>Sözleşme No</th>
                            <th>Müşteri</th>
                            <th>Hakediş Sözleşme Sayısı</th>
                            <th>Sabit Sözleşme Sayısı</th>
                            <th>Hedefli Sözleşme Sayısı</th>
{{--                            <th>Durumu</th>--}}
{{--                            <th>Sözleşme Türü</th>--}}
{{--                            <th>Oluşturma Tarihi</th>--}}
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($items as $no => $values)
                            <tr>
                                <td></td>
                                <td><a href="{{ route('contractDetails',['no'=>$no]) }}" class="dotted-underline">{{ $no }}</a> </td>
                                <td>{{ $values[0]->getCustomer?->fullName }}</td>
                                <td>{{ $values->where('type','hakedis')->count() }}</td>
                                <td>{{ $values->where('type','sabit')->count() }}</td>
                                <td>{{ $values->where('type','hedefli')->count() }}</td>
{{--                                <td>{{ $values[0]->status == 1 ? 'Aktif':'Pasif' }} </td>--}}
{{--                                <td>--}}
{{--                                    @if($values[0]->type == 'hakedis')--}}
{{--                                        Hakediş--}}
{{--                                    @elseif($values[0]->type == 'sabit')--}}
{{--                                        Sabit--}}
{{--                                    @elseif($values[0]->type == 'hedefli')--}}
{{--                                        Hedefli--}}
{{--                                    @else--}}

{{--                                    @endif--}}
{{--                                </td>--}}
{{--                                <td>{{ $values[0]->created_at->translatedFormat('d F Y') }}</td>--}}
{{--                                <td>--}}
{{--                                    @if(in_array('*', $userRoles) || in_array('edit_contract', $userRoles))--}}
{{--                                        <a class="btn btn-primary btn-sm" href="{{ route('contracts.edit',$values[0]) }}">Düzenle</a>--}}
{{--                                    @endif--}}
{{--                                    @if(in_array('*', $userRoles))--}}
{{--                                        <button class="btn btn-danger btn-sm" type="button" onclick="checkBeforeDelete('deleteForm{{ $key }}')">Sil</button>--}}
{{--                                        <form method="post" action="{{ route('contracts.destroy',$values[0]) }}" id="deleteForm{{ $key }}">@csrf @method('delete')</form>--}}
{{--                                    @endif--}}
{{--                                </td>--}}
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="createCodeModal" aria-labelledby="notCompleteForm" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    @if(count($items) > 0)
                        <form id="createContractCodeForm" method="post" action="{{ route('createContractCode',$values[0]) }}"> @csrf
                            <div class="form-group">
                                <label>Kurumsal Cari</label>
                                <select class="form-control form-control-sm" required name="customer_id">
                                    <option value="">Lütfen Seçiniz</option>
                                    @foreach($customers as $customer)
                                        <option data-phone="{{ $customer->phone() }}" value="{{$customer->id}}">{{ $customer->fullName }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="form-group step step2">
                                <label>Sözleşme Tipi</label>
                                <select name="type" required class="form-control form-control-sm"></select>
                            </div>
                            <div class="form-group step step3">
                                <label>Sözleşme Seçimi</label>
                                <select name="contract_id" required class="form-control form-control-sm"></select>
                            </div>
                            <div class="form-group step step4">
                                <label>Stok Türü</label>
                                <select class="form-control contract_stock_id form-control-sm" required name="contract_stock_id[]"></select>
                            </div>
                            <div class="form-group step step5">
                                <label>Kod Kimin Adına Üretilecek?</label>
                                <select required class="form-control contract_type form-control-sm" name="contract_type">
                                    <option value="">Lütfen Seçiniz</option>
                                </select>
                            </div>
                            <div class="form-group step step6">
                                <label>Üretilecek Adet</label>
                                <input type="number" name="special_code_count" value="1" min="1" class="form-control-sm form-control">
                            </div>
                            <div class="form-group step step6">
                                <label>Ödeme Alınsın mı?</label>
                                <select required class="form-control contract_must_payment form-control-sm" name="contract_must_payment"></select>
                            </div>
                            <div class="form-group step step6">
                                <label>Plaka</label>
                                <input name="plaka" class="form-control-sm plaka form-control">
                            </div>
                            <div class="form-group step step6">
                                <label>Telefon Numarası</label>
                                <input name="telephone" required class="form-control-sm form-control">
                            </div>
                            <div class="form-group step step6">
                                <label>Geçerli Bayi</label>
                                <select class="select2" multiple required name="contract_branch_id[]"></select>
                            </div>
                            <button class="btn btn-danger step step6 btn-sm">Kaydet</button>
                        </form>
                    @else
                        <p class="text-danger">Sözleşme Bulunamadı</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>

    <script>
        document.addEventListener("DOMContentLoaded", (event) => {
            $(".select2").select2({
                placeholder: "Ara.."
            });
        });

        $(document).ready(function() {
            // Form gönderme işlemi sırasında required alanları kontrol et
            $('#createContractCodeForm').on('submit', function(event) {
                event.preventDefault();
                let $mustPay = $('select[name="contract_must_payment"]').val();
                if (confirm(`Bu kod için ödeme ${$mustPay == 1 ? 'alınacaktır' : 'alınmayacaktır'}. Onaylıyor musunuz?`)){
                    $('#createContractCodeForm').unbind('submit').submit();
                }
            });
        });


        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı!",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı!",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            });

            $('select[name="customer_id"]').on('change', function () {
                let $val = $(this).val();
                $('.step').css('display', 'none'); // Tüm adımları kapat
                if ($val != "") {
                    $.ajax({
                        url: "{{ route('getContractCustomerDetails') }}",
                        type: "post",
                        data: {
                            _token: '{{ csrf_token() }}',
                            customer_id: $val
                        },
                        success: function (response) {
                            $('select[name="type"]').off('change'); // Olayı kaldır
                            $('select[name="type"]').html(response.typeOptions).on('change', handleTypeChange); // Seçenekleri güncelle ve olayı tekrar ata
                            $('select[name="contract_id"]').html(response.contracts);
                            $('.step2').css('display', 'block'); // 2. adımı aç
                        }
                    });
                }
            });

            $('select[name="type"]').on('change', handleTypeChange);

            $('select[name="contract_id"]').on('change', function () {
                let $val = $(this).val();
                let $customerID = $('select[name="customer_id"]').val(); // customer_id değerini al
                let $type = $('select[name="type"]').val();
                $('.step').not('.step1, .step2, .step3').css('display', 'none'); // 4. adımdan sonrasını kapat
                if ($val != "") {
                    $.ajax({
                        url: "{{ route('getContractCustomerDetails') }}",
                        type: "post",
                        data: {
                            _token: '{{ csrf_token() }}',
                            customer_id: $customerID,
                            type: $type,
                            contract_id: $val,
                        },
                        success: function (response) {
                            $('.contract_stock_id').html(response.stockTypesHtml);
                            $('select[name="contract_branch_id[]"]').html(response.contractBranches);
                            $('.step4').css('display', 'block'); // 4. adımı aç
                        }
                    });
                }
            });

            $('.contract_stock_id').on('change', function () {
                $('.step').not('.step1, .step2, .step3, .step4').css('display', 'none'); // 5. ve sonrasını kapat
                $('.contract_type').html($(this).find('option:selected').data('select')).trigger('change');
                $('.step5').css('display', 'block'); // 5. adımı aç
            });

            $('.contract_type').on('change', function () {
                $('.step').not('.step1, .step2, .step3, .step4, .step5').css('display', 'none'); // 6. ve sonrasını kapat
                $('.step6').css('display', 'block'); // 6. adımı aç
            });

// Başlangıçta olayı bağla
            function handleTypeChange() {
                let $val = $(this).val();
                let $customerID = $('select[name="customer_id"]').val(); // customer_id değerini al
                $('.step').not('.step1, .step2').css('display', 'none'); // 3. adımdan sonrasını kapat
                if ($val != "") {
                    $.ajax({
                        url: "{{ route('getContractCustomerDetails') }}",
                        type: "post",
                        data: {
                            _token: '{{ csrf_token() }}',
                            customer_id: $customerID,
                            type: $val
                        },
                        success: function (response) {
                            $('select[name="contract_id"]').html(response.contracts);
                            $('.step3').css('display', 'block'); // 3. adımı aç
                        }
                    });
                }
            }

            $('.contract_type').on('change', function () {
                let paymentField = $(this).parent().parent().find('.contract_must_payment');
                let $type = $('select[name="type"]').val();
                if($type == 'sabit'){
                    paymentField.html('<option value="1">Evet</option>');
                }
                else if($type == 'hakedis'){
                    if ($(this).val() === 'sozlesme_sahibi') {
                        paymentField.html('<option value="0">Hayır</option>');
                    } else {
                        paymentField.html('<option value="1">Evet</option>');
                    }
                }
                else{
                    if ($(this).val() === 'sozlesme_sahibi') {
                        paymentField.html('<option value="0">Hayır</option>');
                    } else {
                        paymentField.html('<option value="1">Evet</option>');
                    }
                }
                $('.contract_must_payment').trigger('change');
                if ($(this).val() === 'sozlesme_sahibi'){
                    let $phone = $('select[name="customer_id"] option:selected').data('phone');
                    $('input[name="telephone"]').val($phone).attr('readonly', true)
                }else{
                    $('input[name="telephone"]').val('').removeAttr('readonly')
                }
            });

            // $('.contract_must_payment').on('change',function (){
            //     let $this = $(this);
            //     if ($this.find('option').length < 2)
            //         $($this).closest('.form-group').addClass('d-none');
            //     else
            //         $($this).closest('.form-group').removeClass('d-none');
            // });
        });
    </script>
@endpush
