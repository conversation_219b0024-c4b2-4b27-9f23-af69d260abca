@extends('pages.build')
@section('title','<PERSON><PERSON><PERSON>')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card p-2">
                <form method="get">
                    <div class="row">
                        <div class="col-12 col-lg-4 col-md-4 col-sm-12">
                            <label for="status">Durumu</label>
                            <select name="status" id="status" class="form-control form-control-sm">
                                <option value="">Hepsi</option>
                                <option @if($status == 1) selected="" @endif value="1">Aktif</option>
                                <option @if($status == 2) selected="" @endif value="2">Pasif</option>
                            </select>
                        </div>
                        <div class="col-12 col-lg-4 col-md-4 col-sm-12 mt-2">
                            <br>
                            <button type="submit" class="btn btn-sm btn-success">Gönder</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    @if(in_array('*', $userRoles) || in_array('add_contract', $userRoles))
                        <a href="{{ route('contracts.create',['type'=>'hakedis']) }}" class="nav-link">Hakediş Türü Sözleşme Ekle</a>
                        <a href="{{ route('contracts.create',['type'=>'sabit']) }}" class="nav-link">Sabit Fiyatlı Sözleşme Ekle</a>
                        <a href="{{ route('contracts.create',['type'=>'hedefli']) }}" class="nav-link">Hedefli Sözleşme Ekle</a>
                    @endif
                </nav>

                <div class="card-body">
                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr><th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            @if(!isset($_GET['type']))
                                <th>Sözleşme Türü</th>
                            @else
{{--                                <th>Müşteri</th>--}}
                                <th>Stok</th>
                                <th>Durumu</th>
                                <th>Oluşturma Tarihi</th>
                                <th>Detaylar</th>
                            @endif
                        </tr>
                        </thead>
                        <tbody>
                        @if(!isset($_GET['type']))
                            @foreach($items->groupBy('type') as $key => $item)
                                <tr>
                                    <td></td>
                                    <td>
                                        @if($key == 'hakedis')
                                            <a href="{{ route('contractDetails',['no'=>$_GET['no'],'type'=>$key]) }}" class="dotted-underline">Hakediş ({{ count($item) }} Adet)</a>
                                        @elseif($key == 'sabit')
                                            <a href="{{ route('contractDetails',['no'=>$_GET['no'],'type'=>$key]) }}" class="dotted-underline">Sabit ({{ count($item) }} Adet)</a>
                                        @elseif($key == 'hedefli')
                                            <a href="{{ route('contractDetails',['no'=>$_GET['no'],'type'=>$key]) }}" class="dotted-underline">Hedefli ({{ count($item) }} Adet)</a>
                                        @else

                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            @foreach($items as $key => $item)
                                <tr>
                                    <td></td>
{{--                                    <td>--}}
{{--                                        @if(in_array('*', $userRoles) || in_array('edit_contract', $userRoles))--}}
{{--                                            <a class="dotted-underline" href="{{ route('contracts.edit',$item) }}">--}}
{{--                                                {{ $item->getCustomer->fullName }}--}}
{{--                                            </a>--}}
{{--                                        @else--}}
{{--                                            Düzenle--}}
{{--                                        @endif--}}
{{--                                    </td>--}}
                                    <td>
                                        <a class="dotted-underline" href="{{ route('contracts.edit',$item) }}">
                                            @foreach(\App\Models\Stock::whereIn('id',$item->getStockIds->pluck('stock_id')->toArray())->where('status',1)->get() as $stock)
                                                {{ $stock->ad }}<br>
                                            @endforeach
                                        </a>
                                    </td>
                                    <td>{{ $item->status == 1 ? 'Aktif':'Pasif' }} </td>
                                    <td>{{ $item->created_at->translatedFormat('d F Y') }}</td>
                                    <td>
                                        @if(in_array('*', $userRoles))
                                            <button class="btn btn-danger btn-sm" type="button" onclick="checkBeforeDelete('deleteForm{{ $key }}')">Sil</button>
                                            <form method="post" action="{{ route('contracts.destroy',$item) }}" id="deleteForm{{ $key }}">@csrf @method('delete')</form>
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

    <script>
        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_contract', $userRoles)) <a href='{{ route("contracts.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_contract', $userRoles)) <a href='{{ route("contracts.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })
        });
    </script>
@endpush
