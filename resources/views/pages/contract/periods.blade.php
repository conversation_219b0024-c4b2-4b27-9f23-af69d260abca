@extends('pages.build')
@section('title','Sözleşme Ödeme Tablosu')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        .select2-container{
            display: block;
            z-index: 999;
            position: unset;
        }
        .step{
            display: none;
        }
    </style>
@endpush
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card p-2">
                <form method="get">
                    <div class="row">
                        <div class="col-12 col-lg-3 col-md-4 col-sm-12">
                            <label for="payment_status">Durumu</label>
                            <select name="payment_status" id="payment_status" class="form-control form-control-sm">
                                <option value="">Hepsi</option>
                                <option @if(isset($_GET['payment_status']) && $_GET['payment_status'] == 1) selected="" @endif value="1">Ödeme Alındı</option>
                                <option @if(isset($_GET['payment_status']) && $_GET['payment_status'] == 0) selected="" @endif value="0">Ödeme Alınmadı</option>
                            </select>
                        </div>
                        <div class="col-12 col-lg-3 col-md-4 col-sm-12">
                            <label>Dönem Ay</label>
                            <select name="period_month" class="form-control form-control-sm">
                                <option value="">Hepsi</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 1) selected="" @endif value="01">Ocak</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 2) selected="" @endif value="02">Şubat</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 3) selected="" @endif value="03">Mart</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 4) selected="" @endif value="04">Nisan</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 5) selected="" @endif value="05">Mayıs</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 6) selected="" @endif value="06">Haziran</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 7) selected="" @endif value="07">Temmuz</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 8) selected="" @endif value="08">Ağustos</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 9) selected="" @endif value="09">Eylül</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 10) selected="" @endif value="10">Ekim</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 11) selected="" @endif value="11">Kasım</option>
                                <option @if(isset($_GET['period_month']) && $_GET['period_month'] == 12) selected="" @endif value="12">Aralık</option>
                            </select>
                        </div>
                        <div class="col-12 col-lg-3 col-md-4 col-sm-12">
                            <label>Dönem Yıl</label>
                            <select name="period_year" class="form-control form-control-sm">
                                <option value="">Hepsi</option>
                                @for($yil = 2024;$yil <= now()->year;$yil++)
                                    <option @if(isset($_GET['period_year']) && $_GET['period_year'] == $yil) selected="" @endif value="{{ $yil }}">{{ $yil }}</option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-12 col-lg-3 col-md-4 col-sm-12 mt-2">
                            <br>
                            <button type="submit" class="btn btn-sm btn-danger">Filtrele</button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <div class="card-body">
                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr><th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th>Cari</th>
                            <th>Dönem Başlangıç Tarihi</th>
                            <th>Dönem Başlangıç Tarihi</th>
                            <th>Ödeme</th>
                            <th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($items as $no => $item)
                            <tr>
                                <td></td>
                                <td><a href="{{ route('customers.edit',$item->customer_id) }}" class="dotted-underline">{{ $item->customer_name }}</a> </td>
                                <td>{{ \Carbon\Carbon::parse($item->start_date)->translatedFormat('d F Y') }}</td>
                                <td>{{ \Carbon\Carbon::parse($item->end_date)->translatedFormat('d F Y') }}</td>
                                <td style="background-color: {{ $item->payment_completed ? '#16ff00' : '#ff1000' }};color: #fff">{{ $item->payment_completed ? 'Alındı' : 'Alınmadı' }}</td>
                                <td>
                                    <a href="{{ route('contracts.edit',$item->contract_id) }}" class="btn btn-sm btn-danger">Sözleşmeye Git</a>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>

    <script>
        document.addEventListener("DOMContentLoaded", (event) => {
            $(".select2").select2({
                placeholder: "Ara.."
            });
        });

        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_contract', $userRoles)) <a href='{{ route("contracts.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_contract', $userRoles)) <a href='{{ route("contracts.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            });
        });
    </script>
@endpush
