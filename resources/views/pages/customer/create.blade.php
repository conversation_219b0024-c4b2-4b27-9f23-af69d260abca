@extends('pages.build')
@section('title','<PERSON><PERSON>')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        input[required],select[required],textarea[required],.select2-container--default .select2-selection--single .select2-selection__rendered{
            border-color: #ffb9b9 !important;
        }
    </style>
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('content')
    <div class="row">
        <div class="col-md-7">
            <div class="card">
                <div class="card-body">
                    <form id="mainForm" method="post" action="{{ route('customers.store') }}" enctype="multipart/form-data">@csrf
                        <input type="hidden" name="return_url" value="{{$_GET['return_url'] ?? ''}}">
                        <input type="hidden" name="return_customer_type" value="{{ $_GET['customer'] ?? '' }}">
                        <div class="example">
                            <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                                <a class="nav-link active" data-bs-toggle="tab" role="tab" href="#nav-temel" aria-selected="true">Temel Bilgiler</a>
                                <a class="nav-link cari_tab disabled" data-bs-toggle="tab" role="tab" href="#nav-cari" aria-selected="false">Cari Bilgileri</a>
                                <a class="nav-link contact_tab disabled" data-bs-toggle="tab" role="tab" href="#nav-iletisim" aria-selected="false">İletişim Bilgileri</a>
                                <a class="nav-link address_tab disabled" data-bs-toggle="tab" role="tab" href="#nav-adres" aria-selected="false">Adres Bilgileri</a>
                                <a class="nav-link other_tab disabled" data-bs-toggle="tab" role="tab" href="#nav-diger" aria-selected="false">Diğer Bilgiler</a>
                            </nav>
                            <div class="tab-content">
                                <div class="tab-pane show active text-muted" id="nav-temel" role="tabpanel">
                                    <div class="row">
                                        @php
                                            $name = null;
                                            $last_name = null;
                                            if(!empty($_GET['unvan'])){
                                                $explode = explode(' ',$_GET['unvan']);
                                                if(!empty($explode)){
                                                    $name = $explode[0];
                                                    unset($explode[0]);
                                                }
                                                if(!empty($explode)){
                                                    foreach($explode as $e){
                                                        $last_name .= $e." ";
                                                    }
                                                }
                                            }
                                        @endphp
                                        <div class="col-xl-6 mt-2 @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') d-none @endif bireysel">
                                            <label class="form-label">Adı <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="ad" placeholder="Adı" value="{{ isset($_GET['unvan']) ? $name : '' }}">
                                        </div>
                                        <div class="col-xl-6 mt-2 @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') d-none @endif bireysel">
                                            <label class="form-label">Soyadı <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="soyad" value="{{ isset($_GET['unvan']) ? $last_name : '' }}" placeholder="Soyadı">
                                        </div>
                                        <div class="col-xl-6 mt-2  @if(isset($_GET['type']) && $_GET['type'] == 'bireysel') d-none @endif kurumsal">
                                            <label class="form-label">Unvan <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="unvan" placeholder="Unvan" value="{{ isset($_GET['unvan']) ? $_GET['unvan'] : '' }}">
                                        </div>
                                        <div class="col-xl-6 mt-2  @if(isset($_GET['type']) && $_GET['type'] == 'bireysel') d-none @endif kurumsal">
                                            <label class="form-label">Yetkili Adı <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="yetkili_adi" value="" placeholder="Yetkili Adı">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">@if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') Vergi No @else T.C. Kimlik No @endif <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="tc_no" minlength="@if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') 10 @else 11 @endif" value="{{ isset($_GET['tc']) ? $_GET['tc'] : '' }}" maxlength="@if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') 10 @else 11 @endif"  placeholder="@if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') Vergi No @else T.C. Kimlik No @endif">
                                            <input type="checkbox" class=" @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') d-none @endif mt-2" id="yabanci_uyruklu" onchange="togglePasaportFields()"> Türkiye / Suriye / Kıbrıs Harici Uyruklu
                                        </div>
                                        <div class="col-xl-6  @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') d-none @endif mt-2">
                                            <label class="form-label">Doğum Tarihi <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control form-control-sm" name="dogum_tarihi"  placeholder="Doğum Tarihi" max="{{ now()->format('Y-m-d') }}" value="">
                                        </div>
                                        <div class="col-xl-6 @if(isset($_GET['type']) && $_GET['type'] == 'bireysel') d-none @endif mt-2">
                                            <label class="form-label">Vergi Dairesi</label>
                                            @if(isset($vergiDaireleri) && count($vergiDaireleri) > 0)
                                                <select class="select2" name="vergi_dairesi">
                                                @foreach($vergiDaireleri as $daire)
                                                    <option value="">Vergi Dairesi Seçiniz</option>
                                                    <option value="{{ $daire->vdAdi }}">{{ $daire->vdAdi }}</option>
                                                @endforeach
                                                </select>
                                            @else
                                                <input type="text" class="form-control form-control-sm" name="vergi_dairesi" value="" placeholder="Vergi Dairesi">
                                            @endif
                                        </div>
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Vergi Dairesi Kodu</label>
                                            <input type="text" class="form-control form-control-sm" name="vergi_dairesi_kodu" value="" placeholder="Kodu">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Mersis @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') <span class="text-danger">*</span> @endif</label>
                                            <input type="text" class="form-control form-control-sm" name="mersis" value="" @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') required @endif placeholder="Mersis" maxlength="16">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">ESBİS</label>
                                            <input type="text" class="form-control form-control-sm" name="esbis_no" value="" maxlength="16" placeholder="ESBİS">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Yetki Belge Numarası</label>
                                            <input type="text" class="form-control form-control-sm" name="yetki_belge_no" value="" placeholder="Kodu">
                                        </div>
                                        <div class="col-xl-6 d-none mt-2 pasaport-fields">
                                            <label class="form-label">Pasaport Numarası</label>
                                            <input type="text" class="form-control form-control-sm" name="pasaport_no"  placeholder="Pasaport Numarası">
                                        </div>
                                        <div class="col-xl-6 d-none mt-2 pasaport-fields">
                                            <label class="form-label">Pasaport Belgesi</label>
                                            <input type="file" class="form-control form-control-sm" name="pasaport_belge">
                                        </div>
                                    </div>
                                    <button class="btn btn-danger btn-sm req-field float-right mt-3" onclick="requiredFieldControl('general_info')">Sonraki</button>
                                </div>
                                <div class="tab-pane text-muted" id="nav-cari" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Cari Türü</label>
                                            <select name="type" class="form-control form-control-sm">
                                                <option value="bireysel">Bireysel</option>
                                                <option @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') selected @endif value="kurumsal">Kurumsal</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Muhasebe Türü</label>
                                            <select name="muhasebe_type" class="form-control form-control-sm">
                                                <option value="alici">Alıcı</option>
                                                <option @if(isset($_GET['muhasebe_type']) && $_GET['muhasebe_type'] == 'satici') selected @endif value="satici">Satıcı</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Tipi</label>
                                            <select name="tip_id" class="form-control form-control-sm">
                                                <option value="0">Seçilmedi</option>
                                                @foreach($customerTypes as $customerType)
                                                    <option value="{{ $customerType->id }}">{{ $customerType->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Grubu</label>
                                            <select name="grup_id" class="form-control form-control-sm">
                                                <option value="0">Seçilmedi</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Cari Kodu</label>
                                            <input type="text" class="form-control form-control-sm" name="kod" value="" placeholder="Cari Kodu" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Ad Soyad</label>
                                            <input type="text" class="form-control form-control-sm" name="hesap_adi" value="" placeholder="Ad Soyad" >
                                        </div>
                                    </div>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-temel\']').click()">Önceki</a>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-iletisim\']').click()">Sonraki</a>
                                </div>
                                <div class="tab-pane text-muted" id="nav-iletisim" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-11 mt-2">
                                            <label class="form-label">Telefon <span class="text-danger">*</span></label>
                                            <input type="text" required class="form-control form-control-sm" name="telefon" placeholder="Telefon" value="{{ isset($_GET['value']) && (int)$_GET['value'] != 0 ? $_GET['value'] : '' }}" >
                                        </div>
                                        <div class="col-xl-1" style="margin-top: 3rem">
                                            <button type="button" onclick="$(this).css('display','none')" class="btn btn-sm btn-success add-phone">Ekle</button>
                                        </div>
                                        <div class="telefon col-xl-12"></div>
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Fax</label>
                                            <input type="text" class="form-control form-control-sm" name="fax" value="" placeholder="Fax" >
                                        </div>
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Cep</label>
                                            <input type="text" class="form-control form-control-sm" name="cep" value="" placeholder="Cep" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Eposta</label>
                                            <div class="input-group">
                                                <input type="text" class="form-control form-control-sm" name="eposta" placeholder="Eposta" >
                                                <div class="input-group-append d-none">
                                                    <select id="email_domain" name="email_domain" class="form-control form-select">
                                                        <option value="@gmail.com">@gmail.com</option>
                                                        <option value="@outlook.com">@outlook.com</option>
                                                        <option value="@hotmail.com">@hotmail.com</option>
                                                        <option value="@umranoto.com">@umranoto.com</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Web</label>
                                            <input type="text" class="form-control form-control-sm" name="web" value="" placeholder="Web" >
                                        </div>
                                    </div>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-cari\']').click()">Önceki</a>
                                    <button class="btn btn-danger req-field btn-sm float-right mt-3" onclick="requiredFieldControl('contact_info')">Sonraki</button>
                                </div>
                                <div class="tab-pane text-muted" id="nav-adres" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Mahalle</label>
                                            <input type="text" class="form-control form-control-sm" name="mahalle" value="" @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') required @endif placeholder="Mahalle" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Cadde</label>
                                            <input type="text" class="form-control form-control-sm" name="cadde" value="" @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') required @endif placeholder="Cadde" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Sokak</label>
                                            <input type="text" class="form-control form-control-sm" name="sokak" value="" @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') required @endif placeholder="Sokak" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Semt</label>
                                            <input type="text" class="form-control form-control-sm" name="semt" value="" @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') @endif placeholder="Semt" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Kapı No</label>
                                            <input type="text" class="form-control form-control-sm" name="kapi_no" value="" @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') required @endif placeholder="Kapı No" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">İl</label>
                                            <select name="il_kodu" @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') required @endif class="select2">
                                                <option value="">Lütfen Seçiniz</option>
                                                @foreach($cities as $city)
                                                    <option value="{{ $city->id }}">{{ $city->title }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">İlçe</label>
                                            <select name="ilce_kodu" @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal') required @endif class="select2">

                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Ülke</label>
                                            <input type="text" class="form-control form-control-sm" name="ulke" value="Türkiye" placeholder="Ülke" >
                                        </div>
                                    </div>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-iletisim\']').click()">Önceki</a>
                                    <button class="btn btn-danger req-field btn-sm float-right mt-3" onclick="requiredFieldControl('address');">Sonraki</button>
                                </div>
                                <div class="tab-pane text-muted" id="nav-diger" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-6 @if(!isset($_GET['type']) || $_GET['type'] == 'bireysel') d-none @endif mt-2">
                                            <label class="form-label">Risk Tutarı</label>
                                            <input type="text" class="form-control turk-lirasi form-control-sm" name="risk_tutari" placeholder="Risk Tutarı" value="">
                                        </div>
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Evlilik Tarihi</label>
                                            <input type="date" class="form-control form-control-sm" name="evlilik_tarihi" placeholder="Evlilik Tarihi" value="">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Vade Günü</label>
                                            <input type="date" class="form-control form-control-sm" name="vade_gunu" placeholder="Vade Günü" min="{{ now()->format('Y-m-d') }}" value="">
                                        </div>
                                        <!--<div class="col-xl-6 mt-2">
                                            <label class="form-label">Geçerli Kampanya</label>
                                            <select name="campaign_id" class="form-control form-control-sm">
                                                <option value="0">Seçilmedi
                                                @foreach($campaigns as $campaign)
                                                    <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>-->
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Plus Card</label>
                                            <input type="text" class="form-control form-control-sm" name="plus_kart" placeholder="Plus Card" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Sitede Yayınla</label>
                                            <select name="sitede_yayinla" class="form-control form-control-sm">
                                                <option value="1">Evet</option>
                                                <option value="0">Hayır</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Garantek Üye</label>
                                            <select name="garantek_uye" class="form-control form-control-sm">
                                                <option value="1">Evet</option>
                                                <option value="0">Hayır</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 d-none  mt-2">
                                            <label class="form-label">Sms Gönderilsin</label>
                                            <select name="sms_gonder" class="form-control form-control-sm">
                                                <option value="1">Evet</option>
                                                <option value="0">Hayır</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Durum</label>
                                            <select name="status" class="form-control form-control-sm">
                                                <option value="1">Aktif</option>
                                                <option value="0">Pasif</option>
                                            </select>
                                        </div>
                                        <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                                            <button class="btn btn-sm btn-success mt-3 mb-3">Kaydet</button>
                                        </div>
                                    </div>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-adres\']').click()">Önceki</a>
                                </div>
                            </div>
                        </div>
                        <input type="hidden" name="session_name" value="customer_create">
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="phone_check" tabindex="-1"
         aria-labelledby="phone_check" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Bu Telefon Numarası Daha Önce Kayıt Edilmiş!
                    </h6>

                </div>
                <div class="modal-body phone_list_modal">
                    <h6 class="modal-title" id="staticBackdropLabel2">Lütfen Başka Bir Telefon Numarası Giriniz!
                    </h6>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal"
                            aria-label="Close">Kapat</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="phone_verified" tabindex="-1"
         aria-labelledby="phone_verified" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Telefon Numarası Doğrula
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body phone_list_modal">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success send_verification_code"
                    >Doğrulama Kodu Gönder</button>
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal"
                            aria-label="Close">İptal Et</button>

                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/js/select2.min.js"></script>
    <script>
        let tc_verified = false
        addEventListener("DOMContentLoaded", (event) => {
            // No cache modal trigger here
        });
        $(".select2").select2({
            placeholder: "Ara.."
        });

        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });

        $('select[name="type"]').trigger('change')

        $('.add-phone').on('click',function (){
            let html = '<div class="col-xl-5 mt-2"><label class="form-label">Telefon Başlık</label><input type="text" class="form-control form-control-sm" name="telephones_title[]" placeholder="Telefon Başlık" ></div>'
            html +=    '<div class="col-xl-6 mt-2">'
            html +=    '<label class="form-label">Telefon</label>'
            html +=     '<input type="text" class="form-control form-control-sm" name="telephones[]" placeholder="Telefon" >'
            html += '</div>'
            html += '<div class="col-xl-1 mt-5"><button type="button" class="btn btn-sm btn-success add-phone">Ekle</button></div>'
            $('.telefon').after(html)

            document.querySelectorAll('input[name="telephones[]"]').forEach(function (item,index){
                const maskTel3 = IMask(item, {
                    mask: '(0000) 000 0000'
                });
            })

            $('.add-phone').on('click',function (){
                let html = '<div class="col-12"><div class="row"><div class="col-xl-5 mt-2"><label class="form-label">Telefon Başlık</label><input type="text" class="form-control form-control-sm" name="telephones_title[]" placeholder="Telefon Başlık" ></div>'
                html +=    '<div class="col-xl-5 mt-2">'
                html +=    '<label class="form-label">Telefon</label>'
                html +=     '<input type="text" class="form-control form-control-sm" name="telephones[]" placeholder="Telefon" >'
                html += '</div>'
                html += '<div class="col-xl-2 mt-2 d-flex flex-column justify-content-center align-items-start"><button type="button>" class="telephones_delete btn btn-danger btn-sm mt-4"><i class="fa fa-trash"></i></button></div>'
                html += '</div></div></div>'
                $('.telefon').after(html)

                document.querySelectorAll('input[name="telephones[]"]').forEach(function (item,index){
                    const maskTel3 = IMask(item, {
                        mask: '(0000) 000 0000'
                    });
                })
                $('.telephones_delete').click(function(){
                    $(this).parent('div').parent('div').remove();
                })
            })
        })


        function togglePasaportFields() {
            var checkbox = document.getElementById("yabanci_uyruklu");
            var pasaportFields = document.querySelectorAll(".pasaport-fields");
            if (checkbox.checked) {
                pasaportFields.forEach(function(field) {
                    field.classList.remove("d-none");
                });
            } else {
                pasaportFields.forEach(function(field) {
                    field.classList.add("d-none");
                });
            }
        }

        function tcNoDogrula(){
            let $tc = $('input[name="tc_no"]').val()
            let $ad = $('input[name="ad"]').val()
            let $soyad = $('input[name="soyad"]').val()
            let $dogum_tarihi = $('input[name="dogum_tarihi"]').val()

            if($tc.length == 11){
                if($ad != ''){
                    if($soyad != ''){

                        if($dogum_tarihi != ''){
                            $.ajax({
                                url: "{{ route('api.tcNoDogrula') }}",
                                type: "post",
                                data: {'_token':'{{ csrf_token() }}','tc':$tc,'ad':$ad,'soyad':$soyad,'dogum_tarihi':$dogum_tarihi} ,
                                success: function (response) {
                                    if (response.success == 'true')
                                    {
                                        tc_verified = response.success
                                        Toast.fire({
                                            icon: "success",
                                            title: "T.C. Kimlik No Doğrulandı!"
                                        });

                                        if(tc_verified){
                                            $('.cari_tab').removeClass('disabled')
                                            $('.contact_tab').removeClass('disabled')
                                            document.querySelector('a[href=\'#nav-cari\']').click()
                                        }else{
                                            $('.cari_tab').addClass('disabled')
                                            $('.contact_tab').addClass('disabled')
                                            $('.address_tab').addClass('disabled');
                                            $('.other_tab').addClass('disabled');
                                        }
                                    }
                                    else{
                                        if(response.tc_uniq == 2){
                                            Toast.fire({
                                                icon: "error",
                                                title: response.message + ' Kayıtlı Cari İle Yönlendiriliyorsunuz.',
                                            });
                                            @if(!empty($_GET['return_url']))
                                            if(response.old_customer_id !== undefined && response.customer_type){
                                                window.location = "{{$_GET['return_url']}}?customer_id="+response.old_customer_id+'&return_customer_type={{$_GET['customer']}}';
                                            }
                                            @else
                                            setTimeout(function() {
                                                var name = $('input[name=ad]').val();
                                                var soyad = $('input[name=soyad]').val();
                                                window.location = "/customers?id="+response.old_customer_id
                                            },2000)
                                            @endif

                                        }else{
                                            Toast.fire({
                                                icon: "error",
                                                title: "T.C. Kimlik No Doğrulamanadı!"
                                            });
                                        }

                                    }
                                    $('.req-field').removeAttr('disabled');
                                }
                            });
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "Doğum Tarihi Zorunludur!"
                            });
                        }
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Soyisim Zorunludur!"
                        });
                    }
                }else{
                    Toast.fire({
                        icon: "error",
                        title: "İsim Zorunludur!"
                    });
                }
            }else{
                Toast.fire({
                    icon: "error",
                    title: "T.C. Kimlik No 11 Karakter Olmalıdır!"
                });
            }
            $('.req-field').removeAttr('disabled');
        }
        function requiredFieldControl(type){
            $('.req-field').attr('disabled','disabled');
            if(type == "general_info"){
                var checkbox = document.getElementById("yabanci_uyruklu");
                @if(!isset($_GET['type']) || $_GET['type'] == 'bireysel')
                    if(checkbox.checked){
                        var pasaport_no = $('input[name=pasaport_no]').val();
                        if(pasaport_no != ''){
                            $('.cari_tab').removeClass('disabled')
                            $('.contact_tab').removeClass('disabled')
                            document.querySelector('a[href=\'#nav-cari\']').click()
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "Pasaport Numarası Zorunludur!"
                            });
                        }
                        $('.req-field').removeAttr('disabled');
                    }else{
                        @if(auth()->user()->type != 'admin')
                            tcNoDogrula()
                        @else
                            $('.cari_tab').removeClass('disabled')
                            $('.contact_tab').removeClass('disabled')
                            $('.req-field').removeAttr('disabled');
                            document.querySelector('a[href=\'#nav-cari\']').click()
                        @endif

                    }
                @else

                var unvan = $('input[name=unvan]').val();
                var unvanRegex = /[a-zA-Z]/; // En az bir harf içeriyor mu kontrolü

                if (!unvanRegex.test(unvan)) {
                    Toast.fire({
                        icon: "error",
                        title: "Geçersiz Unvan. Unvan en az bir harf içermelidir."
                    });
                    $('.req-field').removeAttr('disabled');
                    return false;
                }
                var $vergiNo = $('input[name=tc_no]').val();

                var $vergiRegex = /^[0-9]{10}$/;

                if (!$vergiRegex.test($vergiNo)) {
                    Toast.fire({
                        icon: "error",
                        title: "Geçersiz vergi numarası. Lütfen 10 haneli bir sayı giriniz."
                    });
                    $('.req-field').removeAttr('disabled');
                    return false;
                }


                var mersis = $('input[name=mersis]').val();

                var mersisRegex = /^[0-9]{16}$/;

                if (!mersisRegex.test(mersis)) {
                    Toast.fire({
                        icon: "error",
                        title: "Geçersiz MERSIS numarası. Lütfen 16 haneli bir sayı giriniz."
                    });
                    $('.req-field').removeAttr('disabled');
                    return false;
                }

                $.ajax({
                    url: "{{ route('api.vergiNoKontrol') }}",
                    type: "post",
                    data: {'_token':'{{@csrf_token()}}','vergi_no':$vergiNo} ,
                    success: function (response) {
                        if(response.success === 'true'){
                            $('.cari_tab').removeClass('disabled')
                            $('.contact_tab').removeClass('disabled')
                            document.querySelector('a[href=\'#nav-cari\']').click()
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: response.message
                            }).then(function (){
                                window.location = "/customers?id="+response.customer_id
                            });
                        }
                        $('.req-field').removeAttr('disabled');
                    }
                });


                @endif

            }
            if(type == 'contact_info'){
                var phone = $('input[name=telefon]').val();
                @if(auth()->user()->type != 'admin')
                    if(phone != ''){

                        var phoneRegex =  /^\(\d{4}\) \d{3} \d{4}$/;
                        console.log(phone)
                        if (phoneRegex.test(phone)) {
                            $.ajax({
                                url: "{{ route('phoneControl') }}",
                                type: "post",
                                data: {'_token':'{{@csrf_token()}}','phone':phone} ,
                                success: function (response) {
                                    if(response.success === 'false'){
                                        Toast.fire({
                                            icon: "error",
                                            title: response.message
                                        }).then(function (){
                                            window.location = "/customers?id="+response.customer_id
                                        });
                                    }else{
                                        html = "<div class='col-12'><div class='row'>";
                                        html += "<div class='form-group col-4'><input checked='' type='radio' id='send_sms_radio_1' name='sms_tel' value='"+phone+"'> <label for='send_sms_radio_1'>"+phone+"</label></div>"
                                        html += "</div></div>";
                                        $('.phone_list_modal').html(html)
                                        $('#phone_verified').modal('show')
                                    }
                                    $('.req-field').removeAttr('disabled');
                                }
                            });




                        }else{

                            $('.address_tab').addClass('disabled');
                            $('.other_tab').addClass('disabled');
                            Toast.fire({
                                icon: "error",
                                title: "Telefon numarası uygun formatta değil!"
                            });
                            $('.req-field').removeAttr('disabled');
                        }
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Telefon numarası  zorunludur!"
                        });
                        $('.req-field').removeAttr('disabled');
                    }
                @else
                    $('.req-field').removeAttr('disabled');
                    $('.address_tab').removeClass('disabled');
                    document.querySelector('a[href=\'#nav-adres\']').click()
                @endif

            }
            if (type == 'address'){
                @if(isset($_GET['type']) && $_GET['type'] == 'kurumsal')
                let $mahalle = $('input[name="mahalle"]').val();
                let $cadde = $('input[name="cadde"]').val();
                let $sokak = $('input[name="sokak"]').val();
                let $semt = $('input[name="semt"]').val();
                let $kapiNo = $('input[name="kapi_no"]').val();
                let $il_kodu = $('select[name="il_kodu"]').val();
                let $ilce_kodu = $('select[name="ilce_kodu"]').val();
                if ($mahalle == ''){
                    Toast.fire({
                        icon: "error",
                        title: "Mahalle zorunludur!"
                    });
                    $('.req-field').removeAttr('disabled');
                    return false;
                }
                if ($cadde == ''){
                    Toast.fire({
                        icon: "error",
                        title: "Cadde zorunludur!"
                    });
                    $('.req-field').removeAttr('disabled');
                    return false;
                }
                if ($sokak == ''){
                    Toast.fire({
                        icon: "error",
                        title: "Sokak zorunludur!"
                    });
                    $('.req-field').removeAttr('disabled');
                    return false;
                }
                // if ($semt == ''){
                //     Toast.fire({
                //         icon: "error",
                //         title: "Semt zorunludur!"
                //     });
                //     return false;
                // }
                if ($kapiNo == ''){
                    Toast.fire({
                        icon: "error",
                        title: "Kapı No zorunludur!"
                    });
                    $('.req-field').removeAttr('disabled');
                    return false;
                }
                if ($il_kodu == ''){
                    Toast.fire({
                        icon: "error",
                        title: "İl zorunludur!"
                    });
                    $('.req-field').removeAttr('disabled');
                    return false;
                }
                if ($ilce_kodu == ''){
                    Toast.fire({
                        icon: "error",
                        title: "İlçe zorunludur!"
                    });
                    $('.req-field').removeAttr('disabled');
                    return false;
                }
                @endif

                $('.other_tab').removeClass('disabled');
                document.querySelector('a[href=\'#nav-diger\']').click()
            }
        }
        $('.send_verification_code').click(function(){
            var sms_tel =  $('input[type=radio][name=sms_tel]:checked').val()
            var thisbtn = $(this)
            if(sms_tel !== undefined){
                $.ajax({
                    url: "{{ route('api.smsVerification') }}",
                    type: "post",
                    data: {
                        _token:'{{@csrf_token()}}',
                        phone:sms_tel,
                        page:'customer_create',
                    },
                    success: function (response) {
                        $('.req-field').removeAttr('disabled');
                        if(response.success){
                            htmltext = '<div class="col-12"><input type="hidden" name="sms_verification_id" value="'+response.sms_verification_id+'"><input class="form-control form-control-sm" placeholder="UMR-XX-XX" name="login_sms" value="'+response.code+'" required type="text"></div>';
                            htmltext += '<div class="col-12 mt-2"><button type="button" class="btn btn-sm btn-success use_code">Kodu Doğrula</button></div>'
                            $('.phone_list_modal').html(htmltext);
                            if(document.querySelector('input[name="login_sms"]')){
                                const maskTel = IMask(document.querySelector('input[name="login_sms"]'), {
                                    mask: 'UMR-00-00'
                                });
                            }

                            $('.use_code').click(function(){
                                $.ajax({
                                    url: "{{ route('api.smsVerificationUse') }}",
                                    type: "post",
                                    data: {
                                        _token:'{{@csrf_token()}}',
                                        code:$('input[name=login_sms]').val(),
                                        sms_verification_id:$('input[name=sms_verification_id]').val(),
                                        page:'customer_create',
                                    },
                                    success: function (response) {
                                        if(response.success){
                                            $('.address_tab').removeClass('disabled');
                                            document.querySelector('a[href=\'#nav-adres\']').click()
                                            $('#phone_verified').modal('hide')
                                        }else{
                                            Toast.fire({
                                                icon: "error",
                                                title: "Kod Hatalı Tekrar Deneyiniz.!"
                                            });
                                        }
                                    },
                                    error: function(jqXHR, textStatus, errorThrown) {
                                        console.log(textStatus, errorThrown);
                                    }
                                });
                            })
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "Tekrar kod almak için 3 dk bekleyiniz.!"
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Sms Gönderilecek Numarayı Seçiniz!"
                });
                $('.req-field').removeAttr('disabled');
            }
        })
        $('select[name="il_kodu"]').on('change',function (){
            let $val = $(this).val()
            $('select[name="ilce_kodu"]').find('option').remove()
            if($val > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$val} ,
                    success: function (response) {
                        $('.req-field').removeAttr('disabled');
                        if (response.items.length > 0){
                            $.each(response.items,function (index,item){
                                $('select[name="ilce_kodu"]').append('<option value="'+item.ilce_id+'">'+item.ilce_title+'</option>')
                            })
                            $('select[name="ilce_kodu"]').trigger('change')
                        }
                    }
                });
            }
        })

        addEventListener("DOMContentLoaded", (event) => {
            $('select[name="il_kodu"]').trigger('change');
            document.querySelectorAll('.turk-lirasi').forEach(input => {
                input.addEventListener('input', function () {
                    let value = this.value;
                    // Remove TL symbol if it exists, then clean the input
                    let numericValue = value.replace(/[^0-9,\.]/g, '');

                    // Ensure only one comma or one dot is allowed
                    let commaParts = numericValue.split(',');
                    if (commaParts.length > 2) {
                        numericValue = commaParts[0] + ',' + commaParts.slice(1).join('');
                    }

                    let dotParts = numericValue.split('.');
                    if (dotParts.length > 2) {
                        numericValue = dotParts[0] + '.' + dotParts.slice(1).join('');
                    }

                    // Add TL symbol to the left of the cleaned value
                    this.value = `₺ ${numericValue}`;
                });

                input.addEventListener('keydown', function (e) {
                    // Allow backspace, arrow keys, delete, tab, comma (188), numpad comma (110), and dot (190)
                    if ([8, 37, 39, 46, 9, 188, 190, 110].includes(e.keyCode)) {
                        return true;
                    }

                    // Allow numeric keys and numpad keys
                    if (
                        !(e.keyCode >= 48 && e.keyCode <= 57) &&  // Top-row numbers
                        !(e.keyCode >= 96 && e.keyCode <= 105) &&  // Numpad numbers
                        e.keyCode !== 188 &&  // Comma (,)
                        e.keyCode !== 190 &&  // Dot (.)
                        e.keyCode !== 110 &&  // Numpad comma (,)
                        !e.ctrlKey // Disallow other Ctrl combinations
                    ) {
                        e.preventDefault();
                    }
                });
            });
        });

    </script>
@endpush
