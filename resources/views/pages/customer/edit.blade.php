@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('title','Cari Düzenle')
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('content')
    <a href="{{ route('exports.pdf.single-customer', $customer) }}" class="btn btn-sm btn-danger mb-2"><i class="bi bi-printer"></i> Yazdır</a>
    <div id="divRaporEkrani" class="row">
        <div class="col-md-7">
            <div class="card">
                <div class="card-body">
                    <form method="post" action="{{ route('customers.update',$customer) }}" enctype="multipart/form-data">@csrf @method('put')
                        <div class="example">
                            <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                                <a class="nav-link active" data-bs-toggle="tab" role="tab" href="#nav-temel" aria-selected="true">Temel Bilgiler</a>
                                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-cari" aria-selected="false">Cari Bilgileri</a>
                                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-iletisim" aria-selected="false">İletişim Bilgileri</a>
                                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-adres" aria-selected="false">Adres Bilgileri</a>
                                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-diger" aria-selected="false">Diğer Bilgiler</a>
                            </nav>
                            <div class="tab-content">
                                <div class="tab-pane show active text-muted" id="nav-temel" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-6 mt-2 @if($customer->type == 'kurumsal') d-none @endif bireysel">
                                            <label class="form-label">Adı <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="ad" placeholder="Adı" value="{{ $customer->ad }}">
                                        </div>
                                        <div class="col-xl-6 mt-2 @if($customer->type == 'kurumsal') d-none @endif bireysel">
                                            <label class="form-label">Soyadı <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="soyad" placeholder="Soyadı" value="{{ $customer->soyad }}">
                                        </div>
                                        <div class="col-xl-6 mt-2  @if($customer->type == 'bireysel') d-none @endif kurumsal">
                                            <label class="form-label">Unvan <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="unvan" placeholder="Unvan" value="{{ $customer->unvan }}">
                                        </div>
                                        <div class="col-xl-6 mt-2  @if($customer->type == 'bireysel') d-none @endif kurumsal">
                                            <label class="form-label">Yetkili Adı <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="yetkili_adi" placeholder="Yetkili Adı" value="{{ $customer->yetkili_adi }}">
                                        </div>
                                        <div class="col-xl-6 mt-2 @if($customer->type == 'kurumsal') d-none @endif">
                                            <label class="form-label"> T.C. Kimlik No<span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="tc_no" minlength="11" maxlength="11" value="{{ $customer->tc_no }}" placeholder="T.C. Kimlik No">
                                        </div>
                                        <div class="col-xl-6 mt-2 @if($customer->type == 'bireysel') d-none @endif">
                                            <label class="form-label">Vergi No  <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control form-control-sm" name="vergi_no" minlength="10" maxlength="10" value="{{ $customer->vergi_no }}" placeholder="Vergi No">
                                        </div>
                                        <div class="col-xl-6  @if($customer->type == 'kurumsal') d-none @endif mt-2">
                                            <label class="form-label">Doğum Tarihi <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control form-control-sm" name="dogum_tarihi" value="{{ $customer->dogum_tarihi }}" max="{{ now()->format('Y-m-d') }}" placeholder="Doğum Tarihi">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Vergi Dairesi</label>
                                            <input type="text" class="form-control form-control-sm" name="vergi_dairesi" value="{{ $customer->vergi_dairesi }}" placeholder="Vergi Dairesi">
                                        </div>
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Vergi Dairesi Kodu</label>
                                            <input type="text" class="form-control form-control-sm" name="vergi_dairesi_kodu" value="{{ $customer->vergi_dairesi_kodu }}" placeholder="Kodu">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Mersis @if($customer->type == 'kurumsal') <span class="text-danger">*</span> @endif</label>
                                            <input type="text" class="form-control form-control-sm" name="mersis" @if($customer->type == 'kurumsal') required @endif value="{{ $customer->mersis }}" maxlength="16" placeholder="Mersis">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">ESBİS</label>
                                            <input type="text" class="form-control form-control-sm" name="esbis_no" value="{{ $customer->esbis_no }}" placeholder="ESBİS">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Yetki Belge Numarası</label>
                                            <input type="text" class="form-control form-control-sm" name="yetki_belge_no" value="{{ $customer->yetki_belge_no }}" placeholder="Kodu">
                                        </div>

                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Müşteri Grubu</label>
                                            <select name="customer_group_id" class="form-control form-control-sm">
                                                <option value="0">Seçilmedi</option>
                                                @foreach($customerGroups as $customerGroup)
                                                    @foreach(\App\Models\CustomerGroup::where('parent_id',$customerGroup->id)->get() as $secondCustomerGroup)
                                                        @foreach(\App\Models\CustomerGroup::where('parent_id',$secondCustomerGroup->id)->get() as $thirdCustomerGroup)
                                                            <option @if($customer->customer_group_id == $thirdCustomerGroup->id) selected @endif value="{{ $thirdCustomerGroup->id }}">{{ $customerGroup->name . ' > ' . $secondCustomerGroup->name . ' > ' . $thirdCustomerGroup->name }}</option>
                                                        @endforeach
                                                    @endforeach
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-cari\']').click()">Sonraki</a>
                                </div>
                                <div class="tab-pane text-muted" id="nav-cari" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-6 @if(!auth()->user()->isAdmin()) d-none @endif mt-2">
                                            <label class="form-label">Cari Türü</label>
                                            <select name="type" class="form-control form-control-sm">
                                                <option value="bireysel">Bireysel</option>
                                                <option @if($customer->type == 'kurumsal') selected @endif value="kurumsal">Kurumsal</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Muhasebe Türü</label>
                                            <select name="muhasebe_type" class="form-control form-control-sm">
                                                <option value="alici">Alıcı</option>
                                                <option @if($customer->muhasebe_type == 'satici') selected @endif value="satici">Satıcı</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Tipi</label>
                                            <select name="tip_id" class="form-control form-control-sm">
                                                <option value="0">Seçilmedi</option>
                                                @foreach($customerTypes as $customerType)
                                                    <option @if($customer->tip_id == $customerType->id) selected @endif value="{{ $customerType->id }}">{{ $customerType->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Grubu</label>
                                            <select name="grup_id" class="form-control form-control-sm">
                                                <option value="0">Seçilmedi</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Cari Kodu</label>
                                            <input type="text" class="form-control form-control-sm" disabled name="kod" value="{{ $customer->cari_kod }}" placeholder="Cari Kodu" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Ad Soyad</label>
                                            <input type="text" class="form-control form-control-sm" name="hesap_adi" value="{{ $customerType->hesap_adi }}" placeholder="Ad Soyad" >
                                        </div>
                                    </div>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-temel\']').click()">Önceki</a>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-iletisim\']').click()">Sonraki</a>
                                </div>
                                <div class="tab-pane text-muted" id="nav-iletisim" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-12 mt-2 telefon">
                                            <label class="form-label">Telefon <span class="text-danger add-phone" style="cursor: pointer">Kayıt Ekle</span></label>
                                            <input type="text" class="form-control form-control-sm" name="telefon" placeholder="Telefon" value="{{ $customer->telefon }}" >
                                        </div>
                                        @foreach($customer->getTelephones as $customerTelephone)
                                            <div class="col-xl-6 mt-2">
                                                <label class="form-label">Telefon Başlık</label>
                                                <input type="text" class="form-control form-control-sm" name="telephones_title[]" placeholder="Telefon Başlık" value="{{ $customerTelephone->title }}" >
                                            </div>
                                            <div class="col-xl-6 mt-2">
                                                <label class="form-label">Telefon</label>
                                                <input type="text" class="form-control form-control-sm" name="telephones[]" placeholder="Telefon" value="{{ $customerTelephone->telephone }}" >
                                            </div>
                                        @endforeach
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Fax</label>
                                            <input type="text" class="form-control form-control-sm" name="fax" value="{{ $customer->fax }}" placeholder="Fax" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Cep</label>
                                            <input type="text" class="form-control form-control-sm" name="cep" value="{{ $customer->cep }}" placeholder="Cep" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Eposta</label>
                                            <input type="email" class="form-control form-control-sm" name="eposta" value="{{ $customer->eposta }}" placeholder="Eposta" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Web</label>
                                            <input type="text" class="form-control form-control-sm" name="web" value="{{ $customer->web }}" placeholder="Web" >
                                        </div>
                                    </div>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-cari\']').click()">Önceki</a>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-adres\']').click()">Sonraki</a>
                                </div>
                                <div class="tab-pane text-muted" id="nav-adres" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Mahalle</label>
                                            <input type="text" class="form-control form-control-sm" name="mahalle" value="{{ $customer->mahalle }}" placeholder="Mahalle" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Cadde</label>
                                            <input type="text" class="form-control form-control-sm" name="cadde" value="{{ $customer->cadde }}" placeholder="Cadde" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Sokak</label>
                                            <input type="text" class="form-control form-control-sm" name="sokak" value="{{ $customer->sokak }}" placeholder="Sokak" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Semt</label>
                                            <input type="text" class="form-control form-control-sm" name="semt" value="{{ $customer->semt }}" placeholder="Semt" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Kapı No</label>
                                            <input type="text" class="form-control form-control-sm" name="kapi_no" value="{{ $customer->kapi_no }}" placeholder="Kapı No" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">İl</label>
                                            <select name="il_kodu" class="select2">
                                                @foreach($cities as $city)
                                                    <option @if($customer->il_kodu == $city->id) selected @endif value="{{ $city->id }}">{{ $city->title }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">İlçe</label>
                                            <select name="ilce_kodu" class="select2">
                                                @if($customer->ilce_kodu > 0)<option value="{{ $customer->ilce_kodu }}">{{ \App\Models\Town::where('ilce_id',$customer->ilce_kodu)->first()->ilce_title }}</option> @endif
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Ülke</label>
                                            <input type="text" class="form-control form-control-sm" name="ulke" value="{{ $customer->ulke }}" placeholder="Ülke" >
                                        </div>
                                    </div>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-iletisim\']').click()">Önceki</a>
                                    <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-diger\']').click()">Sonraki</a>
                                </div>
                                <div class="tab-pane text-muted" id="nav-diger" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-6 @if($customer->type == 'bireysel') d-none @endif mt-2">
                                            <label class="form-label">Risk Tutarı</label>
                                            <input type="text" class="form-control turk-lirasi form-control-sm" name="risk_tutari" placeholder="Risk Tutarı" value="{{ $customer->risk_tutari }}">
                                        </div>
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Evlilik Tarihi</label>
                                            <input type="date" class="form-control form-control-sm" name="evlilik_tarihi" value="{{ $customer->evlilik_tarihi }}" placeholder="Evlilik Tarihi" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Vade Günü</label>
                                            <input type="date" class="form-control form-control-sm" name="vade_gunu" value="{{ $customer->vade_gunu }}" placeholder="Vade Günü" >
                                        </div>
                                        <!--<div class="col-xl-6 mt-2">
                                            <label class="form-label">Geçerli Kampanya</label>
                                            <select name="campaign_id" class="form-control form-control-sm">
                                                <option value="0">Seçilmedi
                                                @foreach($campaigns as $campaign)
                                                    <option value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>-->
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Plus Card</label>
                                            <input type="text" class="form-control form-control-sm" name="plus_kart" value="{{ $customer->plus_card }}" placeholder="Plus Card" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Sitede Yayınla</label>
                                            <select name="sitede_yayinla" class="form-control form-control-sm">
                                                <option value="1">Evet</option>
                                                <option @if($customer->sitede_yayinla == 0) selected @endif value="0">Hayır</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Garantek Üye</label>
                                            <select name="garantek_uye" class="form-control form-control-sm">
                                                <option value="1">Evet</option>
                                                <option @if($customer->garantek_uye == 0) selected @endif value="0">Hayır</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 d-none mt-2">
                                            <label class="form-label">Sms Gönderilsin</label>
                                            <select name="sms_gonder" class="form-control form-control-sm">
                                                <option value="1">Evet</option>
                                                <option @if($customer->sms_gonder == 0) selected @endif value="0">Hayır</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Durum</label>
                                            <select name="status" class="form-control form-control-sm">
                                                <option value="1">Aktif</option>
                                                <option @if($customer->status == 0) selected @endif value="0">Pasif</option>
                                            </select>
                                        </div>
                                        <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                                            <a class="btn btn-danger btn-sm float-right mt-3" onclick="document.querySelector('a[href=\'#nav-adres\']').click()">Önceki</a>
                                            <button class="btn btn-success btn-sm mt-3">Kaydet</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(".select2").select2({
            placeholder: "Ara.."
        });

        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });

        $('select[name="type"]').trigger('change')

        $('.add-phone').on('click',function (){
            let html = '<div class="col-xl-5 mt-2"><label class="form-label">Telefon Başlık</label><input type="text" class="form-control form-control-sm" name="telephones_title[]" placeholder="Telefon Başlık" ></div>'
            html +=    '<div class="col-xl-6 mt-2">'
            html +=    '<label class="form-label">Telefon</label>'
            html +=     '<input type="text" class="form-control form-control-sm" name="telephones[]" placeholder="Telefon" >'
            html += '</div>'
            html += '<div class="col-xl-1 mt-2"><button type="button" class="btn btn-sm btn-success">Ekle</button></div>'
            $('.telefon').after(html)

            document.querySelectorAll('input[name="telephones[]"]').forEach(function (item,index){
                const maskTel3 = IMask(item, {
                    mask: '(0000) 000 0000',
                });
            })
        })

        function tcNoDogrula(){
            let $tc = $('input[name="tc_no"]').val()
            let $ad = $('input[name="ad"]').val()
            let $soyad = $('input[name="soyad"]').val()
            let $dogum_tarihi = $('input[name="dogum_tarihi"]').val()

            if($tc != ''){
                if($ad != ''){
                    if($soyad != ''){
                        if($dogum_tarihi != ''){
                            $.ajax({
                                url: "{{ route('api.tcNoDogrula') }}",
                                type: "post",
                                data: {'_token':'{{ csrf_token() }}','tc':$tc,'ad':$ad,'soyad':$soyad,'dogum_tarihi':$dogum_tarihi} ,
                                success: function (response) {
                                    if (response.success == 'true')
                                        Toast.fire({
                                            icon: "success",
                                            title: "T.C. Kimlik No Doğrulandı!"
                                        });
                                    else
                                        Toast.fire({
                                            icon: "error",
                                            title: "T.C. Kimlik No Doğrulamanadı!"
                                        });
                                }
                            });
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "Doğum Tarihi Zorunludur!"
                            });
                        }
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Soyisim Zorunludur!"
                        });
                    }
                }else{
                    Toast.fire({
                        icon: "error",
                        title: "İsim Zorunludur!"
                    });
                }
            }else{
                Toast.fire({
                    icon: "error",
                    title: "T.C. Kimlik No Zorunludur!"
                });
            }
        }
    </script>
    <script>
        $('select[name="il_kodu"]').on('change',function (){
            let $val = $(this).val()
            $('select[name="ilce_kodu"]').find('option').remove()
            if($val > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$val} ,
                    success: function (response) {
                        if (response.items.length > 0){
                            $.each(response.items,function (index,item){
                                $('select[name="ilce_kodu"]').append('<option value="'+item.ilce_id+'">'+item.ilce_title+'</option>')
                            })
                            $('select[name="ilce_kodu"]').trigger('change')
                        }
                    }
                });
            }
        })

        $('select[name="type"]').on('change', function () {
            let $val = $(this).val();

            if ($val == 'bireysel') {
                $('input[name="mahalle"]').removeAttr('required');
                $('input[name="cadde"]').removeAttr('required');
                $('input[name="sokak"]').removeAttr('required');
                $('input[name="semt"]').removeAttr('required');
                $('input[name="kapi_no"]').removeAttr('required');
                $('input[name="ad"]').parent().removeClass('d-none');
                $('input[name="soyad"]').parent().removeClass('d-none');
                $('input[name="yetkili_adi"]').parent().removeClass('d-none');
                $('input[name="dogum_tarihi"]').parent().removeClass('d-none');
                $('input[name="tc_no"]').parent().removeClass('d-none');
                $('input[name="risk_tutari"]').parent().addClass('d-none');
                $('input[name="unvan"]').parent().addClass('d-none');
                $('input[name="vergi_no"]').parent().addClass('d-none');
            } else {
                $('input[name="mahalle"]').attr('required','true');
                $('input[name="cadde"]').attr('required','true');
                $('input[name="sokak"]').attr('required','true');
                $('input[name="semt"]').removeAttr('required');
                $('input[name="kapi_no"]').attr('required','true');
                $('input[name="risk_tutari"]').parent().removeClass('d-none');
                $('input[name="unvan"]').parent().removeClass('d-none');
                $('input[name="vergi_no"]').parent().removeClass('d-none');
                $('input[name="ad"]').parent().addClass('d-none');
                $('input[name="soyad"]').parent().addClass('d-none');
                $('input[name="yetkili_adi"]').parent().addClass('d-none');
                $('input[name="dogum_tarihi"]').parent().addClass('d-none');
                $('input[name="tc_no"]').parent().addClass('d-none');
            }
        });

        document.addEventListener("DOMContentLoaded", (event) => {
            // Trigger select field on page load
            $('select[name="type"]').trigger('change');

            // Handle all .turk-lirasi inputs
            document.querySelectorAll('.turk-lirasi').forEach(input => {
                input.addEventListener("input", function (e) {
                    let value = this.value;
                    // Remove TL symbol if it exists, then clean the input
                    let numericValue = value.replace(/[^0-9,\.]/g, '');

                    // Ensure only one comma or one dot is allowed
                    let commaParts = numericValue.split(',');
                    if (commaParts.length > 2) {
                        numericValue = commaParts[0] + ',' + commaParts.slice(1).join('');
                    }

                    let dotParts = numericValue.split('.');
                    if (dotParts.length > 2) {
                        numericValue = dotParts[0] + '.' + dotParts.slice(1).join('');
                    }

                    // Add TL symbol to the left of the cleaned value
                    this.value = `₺ ${numericValue}`;
                });

                input.addEventListener("keydown", function (e) {
                    // Allow backspace, arrow keys, delete, tab, comma (188), numpad comma (110), and dot (190)
                    if ([8, 37, 39, 46, 9, 188, 190, 110].includes(e.keyCode)) {
                        return true;
                    }

                    // Allow numeric keys and numpad keys
                    if (
                        !(e.keyCode >= 48 && e.keyCode <= 57) &&  // Top-row numbers
                        !(e.keyCode >= 96 && e.keyCode <= 105) &&  // Numpad numbers
                        e.keyCode !== 188 &&  // Comma (,)
                        e.keyCode !== 190 &&  // Dot (.)
                        e.keyCode !== 110 &&  // Numpad comma (,)
                        !e.ctrlKey // Disallow other Ctrl combinations
                    ) {
                        e.preventDefault();
                    }
                });
            });
        });


    </script>
@endpush
