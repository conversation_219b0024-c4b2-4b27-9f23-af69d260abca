@extends('pages.build')
@section('title','Cari Dü<PERSON>le')
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('content')
    <form method="post" action="{{ route('customers.update',$customer) }}" enctype="multipart/form-data">@csrf @method('put')
        <button type="button" class="btn btn-primary mt-2">Marka İlişkileri</button>
        <button type="button" class="btn btn-primary mt-2">İlişkili Müşteriler</button>
        <button type="button" class="btn btn-primary mt-2">Müşteri Belgeleri</button>
        <button type="button" class="btn btn-primary mt-2">İzin Bilgileri</button>
        <button type="button" class="btn btn-primary mt-2"><PERSON><PERSON> Tarihçesi</button>
        <button type="button" class="btn btn-primary mt-2">Kampanya Bilgileri</button>
        <button type="button" class="btn btn-primary mt-2">Sözleşme</button>
        <button type="button" class="btn btn-primary mt-2">Tanıma Notları</button>
        <button type="button" class="btn btn-primary mt-2">Tedarikçi Bilgileri</button>
        <button type="button" class="btn btn-primary mt-2">E-fatura ve E-irsaliye Müşteri Tanımları</button>
        <button type="button" class="btn btn-primary mt-2">Anket Bilgileri</button>
        <button type="button" class="btn btn-primary mt-2">İrtibat Bilgileri</button>

        <div class="row">
            <div class="col-md-6">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="temelBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#temelBilgiler" aria-expanded="true"
                                    aria-controls="temelBilgiler">
                                Hesap Bilgileri
                            </button>
                        </h2>
                        <div id="temelBilgiler" class="accordion-collapse collapse show" aria-labelledby="temelBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Cari Türü</label>
                                        <select name="type" class="form-control">
                                            <option value="bireysel">Bireysel</option>
                                            <option @if($customer->type == 'kurumsal') selected @endif value="kurumsal">Kurumsal</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Tipi</label>
                                        <select name="tip_id" class="form-control">
                                            <option value="0">Seçilmedi</option>
                                            @foreach($customerTypes as $customerType)
                                                <option @if($customer->tip_id == $customerType->id) selected @endif value="{{ $customerType->id }}">{{ $customerType->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Grubu</label>
                                        <select name="grup_id" class="form-control">
                                            <option value="0">Seçilmedi</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Cari Kodu</label>
                                        <input type="text" class="form-control" name="kod" placeholder="Cari Kodu" value="{{ $customer->kod }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Hesap Adı</label>
                                        <input type="text" class="form-control" name="hesap_adi" placeholder="Hesap Adı" value="{{ $customer->hesap_adi }}" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="cariBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#cariBilgiler" aria-expanded="true"
                                    aria-controls="cariBilgiler">
                                Cari Bilgileri
                            </button>
                        </h2>
                        <div id="cariBilgiler" class="accordion-collapse collapse show" aria-labelledby="cariBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Unvan</label>
                                        <input type="text" class="form-control" name="unvan" placeholder="Unvan" value="{{ $customer->unvan }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Adı</label>
                                        <input type="text" class="form-control" name="ad" placeholder="Adı" value="{{ $customer->ad }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Soyadı</label>
                                        <input type="text" class="form-control" name="soyad" placeholder="Soyadı" value="{{ $customer->soyad }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Yetkili Adı</label>
                                        <input type="text" class="form-control" name="yetkili_adi" placeholder="Yetkili Adı" value="{{ $customer->yetkili_adi }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Müşteri Grubu</label>
                                        <select name="customer_group_id" class="form-control">
                                            <option value="0">Seçilmedi</option>
                                            @foreach($customerGroups as $customerGroup)
                                                @foreach(\App\Models\CustomerGroup::where('parent_id',$customerGroup->id)->get() as $secondCustomerGroup)
                                                    @foreach(\App\Models\CustomerGroup::where('parent_id',$secondCustomerGroup->id)->get() as $thirdCustomerGroup)
                                                        <option @if($customer->customer_group_id == $thirdCustomerGroup->id) selected @endif value="{{ $thirdCustomerGroup->id }}">{{ $customerGroup->name . ' > ' . $secondCustomerGroup->name . ' > ' . $thirdCustomerGroup->name }}</option>
                                                    @endforeach
                                                @endforeach
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="adresBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#adresBilgiler" aria-expanded="true"
                                    aria-controls="adresBilgiler">
                                Adres Bilgileri
                            </button>
                        </h2>
                        <div id="adresBilgiler" class="accordion-collapse collapse show" aria-labelledby="adresBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Mahalle</label>
                                        <input type="text" class="form-control" name="mahalle" placeholder="Mahalle" value="{{ $customer->mahalle }}" required>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Cadde</label>
                                        <input type="text" class="form-control" name="cadde" placeholder="Cadde" value="{{ $customer->cadde }}" required>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Sokak</label>
                                        <input type="text" class="form-control" name="sokak" placeholder="Sokak" value="{{ $customer->sokak }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Semt</label>
                                        <input type="text" class="form-control" name="semt" placeholder="Semt" value="{{ $customer->semt }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">İlçe</label>
                                        <input type="text" class="form-control" name="ilce" placeholder="İlçe" value="{{ $customer->ilce }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">İl</label>
                                        <input type="text" class="form-control" name="il" placeholder="İl" value="{{ $customer->il }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">İlçe Kodu</label>
                                        <input type="text" class="form-control" name="ilce_kodu" placeholder="İlçe Kodu" value="{{ $customer->ilce_kodu }}" required>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Ülke</label>
                                        <input type="text" class="form-control" name="ulke" placeholder="Ülke" value="{{ $customer->ulke }}" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="iletisimBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#iletisimBilgiler" aria-expanded="true"
                                    aria-controls="iletisimBilgiler">
                                İletişim Bilgileri
                            </button>
                        </h2>
                        <div id="iletisimBilgiler" class="accordion-collapse collapse show" aria-labelledby="iletisimBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Telefon</label>
                                        <input type="text" class="form-control" name="telefon" placeholder="Telefon" value="{{ $customer->telefon }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Fax</label>
                                        <input type="text" class="form-control" name="fax" placeholder="Fax" value="{{ $customer->fax }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Cep</label>
                                        <input type="text" class="form-control" name="cep" placeholder="Cep" value="{{ $customer->cep }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Eposta</label>
                                        <input type="text" class="form-control" name="eposta" placeholder="Eposta" value="{{ $customer->eposta }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Web</label>
                                        <input type="text" class="form-control" name="web" placeholder="Web" value="{{ $customer->web }}" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="digerBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#digerBilgiler" aria-expanded="true"
                                    aria-controls="digerBilgiler">
                                Diğer Bilgiler
                            </button>
                        </h2>
                        <div id="digerBilgiler" class="accordion-collapse collapse show" aria-labelledby="digerBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Doğum Tarihi</label>
                                        <input type="date" class="form-control" name="dogum_tarihi" placeholder="Doğum Tarihi" value="{{ $customer->dogum_tarihi }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Evlilik Tarihi</label>
                                        <input type="date" class="form-control" name="evlilik_tarihi" placeholder="Evlilik Tarihi" value="{{ $customer->evlilik_tarihi }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Vade Günü</label>
                                        <input type="date" class="form-control" name="vade_gunu" placeholder="Vade Günü" value="{{ $customer->vade_gunu }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Geçerli Kampanya</label>
                                        <select name="campaign_id" class="form-control">
                                            <option value="0">Seçilmedi
                                            @foreach($campaigns as $campaign)
                                                <option @if($customer->campaign_id == $campaign->id) selected @endif value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="cariOzelHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#cariOzel" aria-expanded="true"
                                    aria-controls="cariOzel">
                                Cari Özel Bilgileri
                            </button>
                        </h2>
                        <div id="cariOzel" class="accordion-collapse collapse show" aria-labelledby="cariOzelHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Plus Card</label>
                                        <input type="text" class="form-control" name="plus_kart" placeholder="Plus Card" value="{{ $customer->plus_kart }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Sitede Yayınla</label>
                                        <select name="sitede_yayinla" class="form-control">
                                            <option @if($customer->sitede_yayinla == 1) selected @endif value="1">Evet</option>
                                            <option @if($customer->sitede_yayinla == 0) selected @endif value="0">Hayır</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Garantek Üye</label>
                                        <select name="garantek_uye" class="form-control">
                                            <option @if($customer->garantek_uye == 1) selected @endif value="1">Evet</option>
                                            <option @if($customer->garantek_uye == 0) selected @endif value="0">Hayır</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Sms Gönderilsin</label>
                                        <select name="sms_gonder" class="form-control">
                                            <option @if($customer->sms_gonder == 1) selected @endif value="1">Evet</option>
                                            <option @if($customer->sms_gonder == 0) selected @endif value="0">Hayır</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control">
                                            <option @if($customer->status == 1) selected @endif value="1">Aktif</option>
                                            <option @if($customer->status == 0) selected @endif value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="vergiDairesiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#vergiDairesi" aria-expanded="true"
                                    aria-controls="vergiDairesi">
                                Vergi Dairesi Bilgileri
                            </button>
                        </h2>
                        <div id="vergiDairesi" class="accordion-collapse collapse show" aria-labelledby="vergiDairesiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Vergi Dairesi</label>
                                        <input type="text" class="form-control" name="vergi_dairesi" placeholder="Vergi Dairesi" value="{{ $customer->vergi_dairesi }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Kodu</label>
                                        <input type="text" class="form-control" name="vergi_dairesi_kodu" placeholder="Kodu" value="{{ $customer->vergi_dairesi_kodu }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Vergi No</label>
                                        <input type="text" class="form-control" name="vergi_no" placeholder="Vergi No" value="{{ $customer->vergi_no }}" required>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">T.C. Kimlik No</label>
                                        <input type="text" class="form-control" name="tc_no" placeholder="T.C. Kimlik No" value="{{ $customer->tc_no }}" required>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
