@extends('pages.build')
@section('title','Cariler')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('excel')
    @if(in_array('*', $userRoles) || in_array('download_excel_customer', $userRoles))
        <a href="/excel/cariler?unvan={{$_GET['unvan'] ?? ''}}&kod={{$_GET['kod'] ?? ''}}&telefon={{$_GET['telefon'] ?? ''}}&eposta={{$_GET['eposta'] ?? ''}}&web={{$_GET['web'] ?? ''}}&il_kodu={{$_GET['il_kodu'] ?? ''}}&plus_kart={{$_GET['plus_kart'] ?? ''}}&sms_gonder={{$_GET['sms_gonder'] ?? ''}}&type={{$_GET['type'] ?? ''}}&branch_id={{$_GET['branch_id'] ?? ''}}" class="btn btn-sm btn-success">Excel Olarak İndir <i class="fa fa-file-excel"></i> </a>
    @endif
    @if(in_array('*', $userRoles) || in_array('upload_excel_customer', $userRoles))
        <a href="{{ route('importExcel',['type'=>'customer']) }}" class="btn btn-sm btn-danger">Excelden Aktar <i class="fa fa-file-excel"></i> </a>
    @endif
@endsection
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form id="getSavedFilter" method="get" action="{{ route('customers.index') }}"></form>
                <form method="get" action="{{ route('customers.index') }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Unvan</label>
                                    <input type="text" class="form-control" name="unvan" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->unvan : $_GET['unvan'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Cari Kod</label>
                                    <input type="text" class="form-control" name="kod" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->kod : $_GET['kod'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Telefon Numarası</label>
                                    <input type="text" class="form-control" name="telefon" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->telefon : $_GET['telefon'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>E-posta Adresi</label>
                                    <input type="text" class="form-control" name="eposta" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->eposta : $_GET['eposta'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Web Adresi</label>
                                    <input type="text" class="form-control" name="web" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->web : $_GET['web'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Hizmet Verdiği İl</label>
                                <select name="il_kodu" class="select2">
                                    <option value="0">Seçilmedi</option>
                                    @foreach($cities as $city)
                                        <option @if($selectedSavedFilter != null && $selectedSavedFilter->il_kodu == $city->id ) selected @endif value="{{ $city->id }}" {!! request('il_kodu') == $city->id?'selected':''  !!}>{{ $city->title }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">İlçe</label>
                                <select name="ilce_kodu" class="select2">
                                    <option value="0">Seçilmedi</option>
                                    @if($selectedSavedFilter != null && (int)$selectedSavedFilter->ilce_kodu > 0)
                                        <option selected value="{{ (int)$selectedSavedFilter->ilce_kodu }}">{{ \App\Models\Town::where('ilce_id',(int)$selectedSavedFilter->ilce_kodu)->first()->title }}</option>
                                    @endif
                                </select>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Plus Card</label>
                                    <input type="text" class="form-control" name="plus_kart" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->plus_kart : $_GET['plus_kart'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Sms Gönder</label>
                                    <select class="form-control" name="sms_gonder">
                                        <option value="">Tümü</option>
                                        <option @if(isset($_GET['sms_gonder']) && $_GET['sms_gonder'] == 1) selected @endif value="1">Evet</option>
                                        <option @if(($selectedSavedFilter != null && $selectedSavedFilter->sms_gonder == 0) || (isset($_GET['sms_gonder']) && $_GET['sms_gonder'] == 0)) selected @endif value="0">Hayır</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Tür</label>
                                    <select class="form-control" name="type">
                                        <option value="">Tümü</option>
                                        <option @if(isset($_GET['type']) && $_GET['type'] == 'bireysel') selected @endif value="bireysel">Bireysel</option>
                                        <option @if(($selectedSavedFilter != null && $selectedSavedFilter->type == 'kurumsal') ||( isset($_GET['type']) && $_GET['type'] == 'kurumsal')) selected @endif value="kurumsal">Kurumsal</option>
                                    </select>
                                </div>
                            </div>
                            @if(count($authUser->getBranchIds()) > 1)
                                <div class="col">
                                    <div class="form-group">
                                        <label>Bayi</label>
                                        <select class="form-control select2" name="branch_id">
                                            @if($authUser->type == 'admin')
                                                <option value="0">Tüm Bayiler</option>
                                            @endif
                                            @foreach(\App\Models\Branch::where('status',1)->whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                                <option @if((isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id)) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><small>Kayıtlı Şablonlar</small></label>
                                    <select form="getSavedFilter" onchange="$('#getSavedFilter').submit()" class="form-control select2" name="saved_filter_id">
                                        <option value="0">Seçilmedi</option>
                                        @foreach($savedFilters as $savedFilter)
                                            <option @if(isset($_GET['saved_filter_id']) && $_GET['saved_filter_id'] == $savedFilter->id) selected @endif value="{{ $savedFilter->id }}">{{ $savedFilter->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col mt-4">
                                <button class="btn btn-sm mt-1 btn-success">Listele</button>
                                <button onclick="$('#saveFilter').modal('toggle')" type="button" class="btn btn-sm btn-danger mt-1" >Kaydet</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    @if(in_array('*', $userRoles) || in_array('add_customer', $userRoles))
                        <a href="{{ route('customers.create',['type'=>'kurumsal','unvan'=>$_GET['unvan'] ?? '']) }}" class="nav-link">Kurumsal Kayıt Ekle</a>
                        <a href="{{ route('customers.create',['type'=>'bireysel','unvan'=>$_GET['unvan'] ?? '']) }}" class="nav-link">Bireysel Kayıt Ekle</a>
                    @endif
                    @if(in_array('*', $userRoles) || in_array('list_customer', $userRoles))
                        <a href="{{ route('customers.index') }}" class="nav-link active">{{ \App\Models\Menu::where('key','customer_list')->first()->value }}</a>
                    @endif
                    @if(in_array('*', $userRoles) || in_array('list_customer_type', $userRoles))
                        <a href="{{ route('customer-types.index') }}" class="nav-link">{{ \App\Models\Menu::where('key','customer_type_list')->first()->value }}</a>
                    @endif
                    @if(in_array('*', $userRoles) || in_array('list_customer_group', $userRoles))
                        <a href="{{ route('customer-groups.index') }}" class="nav-link">{{ \App\Models\Menu::where('key','customer_group_list')->first()->value }}</a>
                    @endif
                </nav>
                <div class="card-body">
                    @if(true)
                        <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                            <thead>
                            <tr>
                                <th style="width: 1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                                <th>Eski Cari Kodu</th>
                                <th>Cari Kodu</th>
                                <th>Unvan</th>
                                <th>T.C./Vergi No</th>
                                <th>Telefon Numarası</th>
                                <th>İşlemler</th>
                            </tr>
                            </thead>
                            <tbody>
                            {{-- Example rows can be added here --}}
                            </tbody>
                        </table>
                    @else
                        <div class="text-center">
                            <p class="text-danger">Hiçbir Kayıt Bulunamadı!</p>
                            <a href="{{route('customers.create',['type'=>'kurumsal','unvan'=>$_GET['unvan'] ?? ''])}}" class="btn btn-sm btn-danger">Kurumsal Cari Ekle</a>
                            <a href="{{route('customers.create',['type'=>'bireysel','unvan'=>$_GET['unvan'] ?? ''])}}" class="btn btn-sm btn-danger">Bireysel Cari Ekle</a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="saveFilter" tabindex="-1" aria-labelledby="saveFilter" data-bs-keyboard="false" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Filtre Şablonu Oluştur</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Şablon Adı</small></label>
                            <input type="text" class="form-control" name="saved_filter_name">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger save-filter">Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js"></script>
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $('select[name="il_kodu"]').on('change',function (){
            let $val = $(this).val()
            $('select[name="ilce_kodu"]').find('option').remove()
            if($val > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$val} ,
                    success: function (response) {
                        if (response.items.length > 0){
                            $.each(response.items, function (index, item) {
                                $('select[name="ilce_kodu"]').append('<option value="' + item.ilce_id + '">' + item.ilce_title + '</option>')
                            })
                            @if(request()->get('ilce_kodu'))
                            $('select[name="ilce_kodu"] option[value="{{ request()->input('ilce_kodu') }}"]').prop('selected', true);
                            @endif
                            $('select[name="ilce_kodu"]').trigger('change')
                        }
                    }
                });
            }
        })

        addEventListener("DOMContentLoaded", (event) => {
            $('select[name="il_kodu"]').trigger('change')
        });

        $(".select2").select2({
            placeholder: "Ara.."
        });

        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({
                "dom": '<"pull-left"f><"pull-right"l>rtip',
                scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                "processing": true,
                "serverSide": true,
                "searching":false,
                "ajax": {
                    "url": "{{ route('getCustomersForAjax') }}",
                    "dataType": "json",
                    "type": "POST",
                    "data":{
                        _token: "{{csrf_token()}}",
                        @if(isset($_GET['unvan']) && $_GET['unvan'] != '')
                        'unvan':"{{ $_GET['unvan'] }}",
                        @endif
                            @if(isset($_GET['kod']) && $_GET['kod'] != '')
                        'kod':"{{ $_GET['kod'] }}",
                        @endif
                            @if(isset($_GET['telefon']) && $_GET['telefon'] != '')
                        'telefon':"{{ $_GET['telefon'] }}",
                        @endif
                            @if(isset($_GET['eposta']) && $_GET['eposta'] != '')
                        'eposta':"{{ $_GET['eposta'] }}",
                        @endif
                            @if(isset($_GET['web']) && $_GET['web'] != '')
                        'web':"{{ $_GET['web'] }}",
                        @endif
                            @if(isset($_GET['il_kodu']) && $_GET['il_kodu'] != '')
                        'il_kodu':"{{ $_GET['il_kodu'] }}",
                        @endif
                            @if(isset($_GET['ilce_kodu']) && $_GET['ilce_kodu'] != '')
                        'ilce_kodu':"{{ $_GET['ilce_kodu'] }}",
                        @endif
                            @if(isset($_GET['plus_kart']) && $_GET['plus_kart'] != '')
                        'plus_kart':"{{ $_GET['plus_kart'] }}",
                        @endif
                            @if(isset($_GET['sms_gonder']) && $_GET['sms_gonder'] != '')
                        'sms_gonder':"{{ $_GET['sms_gonder'] }}",
                        @endif
                            @if(isset($_GET['type']) && $_GET['type'] != '')
                        'type':"{{ $_GET['type'] }}",
                        @endif
                            @if(isset($_GET['branch_id']) && $_GET['branch_id'] != '')
                        'branch_id':"{{ $_GET['branch_id'] }}",
                        @endif
                        @if(isset($_GET['id']) && $_GET['id'] != '')
                        'id':"{{ $_GET['id'] }}",
                        @endif
                    },
                    "dataSrc":'data'
                },
                "columns": [
                    { "data": 'x','defaultContent': '' },
                    { "data": "eski_kod" },
                    { "data": "kod" },
                    { "data": "unvan" },
                    { "data": "tc_no" },
                    { "data": "telefon" },
                    { "data": "options" },
                ],
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil @if(in_array('*', $userRoles) || in_array('add_customer', $userRoles)) <a href='{{ route("customers.create",['unvan'=>$_GET['unvan'] ?? '','type'=>'bireysel']) }}' class='btn btn-sm btn-danger'>Bireysel Kayıt Ekle</a> <a href='{{ route("customers.create",['unvan'=>$_GET['unvan'] ?? '','type'=>'kurumsal']) }}' class='btn btn-sm btn-danger'>Kurumsal Kayıt Ekle</a> @endif",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı @if(in_array('*', $userRoles) || in_array('add_customer', $userRoles)) <a href='{{ route("customers.create",['unvan'=>$_GET['unvan'] ?? '','type'=>'bireysel']) }}' class='btn btn-sm btn-danger'>Bireysel Kayıt Ekle</a> <a href='{{ route("customers.create",['unvan'=>$_GET['unvan'] ?? '','type'=>'kurumsal']) }}' class='btn btn-sm btn-danger'>Kurumsal Kayıt Ekle</a> @endif",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();

            let columnsHtml = '<div class="row">';
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">';
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>';
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">';
                    columnsHtml += "</div>";
                }
            });
            columnsHtml += "</div>";
            $('.datatable-columns').html(columnsHtml);

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());
                column.visible(!column.visible());
            });
        });

        if (!Swal.isVisible()) {
            const Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        $('.save-filter').on('click',function (){
            if ($('input[name="saved_filter_name"]').val() != ''){
                $.ajax({
                    url: "{{ route('saveFilters') }}",
                    type: "post",
                    data: {
                        '_token':'{{ csrf_token() }}',
                        'name':$('input[name="saved_filter_name"]').val(),
                        'unvan':$('input[name="unvan"]').val(),
                        'kod':$('input[name="kod"]').val(),
                        'telefon':$('input[name="telefon"]').val(),
                        'eposta':$('input[name="eposta"]').val(),
                        'web':$('input[name="web"]').val(),
                        'il_kodu':$('select[name="il_kodu"]').val(),
                        'ilce_kodu':$('select[name="ilce_kodu"]').val(),
                        'plus_kart':$('input[name="plus_kart"]').val(),
                        'sms_gonder':$('select[name="sms_gonder"]').val(),
                        'customer_type':$('select[name="type"]').val(),
                        'branch_id':$('select[name="branch_id"]').val(),
                        'type':'customer'
                    } ,
                    success: function (response) {
                        if (response.success == 'true'){
                            Toast.fire({
                                icon: "success",
                                title: "Şablon Kayıt Edildi"
                            });
                            $('select[name="saved_filter_id"]').append('<option value="'+response.id+'" selected="selected">'+response.name+'</option>');
                            $('#getSavedFilter').submit()
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "Şablon Kayıt Edilemedi"
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Şablon Adı Girilmedi"
                });
            }
        });
    </script>
@endpush
