@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Müşteri Grubu Düzenle')
@section('content')
    <form method="post" action="{{ route('customer-groups.update',$customerGroup) }}">@csrf @method('put')
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="temelBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#temelBilgiler" aria-expanded="true"
                                    aria-controls="temelBilgiler">
                                Temel Bilgiler
                            </button>
                        </h2>
                        <div id="temelBilgiler" class="accordion-collapse collapse show" aria-labelledby="temelBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Üst Grup</label>
                                        <select name="parent_id" class="form-control">
                                            <option value="0">Yok</option>
                                            @foreach($customerGroups as $item)
                                                <option @if($customerGroup->parent_id == $item->id) selected @endif value="{{ $item->id }}">{{ $item->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kod <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="kod" value="{{ $customerGroup->kod }}" placeholder="Kod" required>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Başlık <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="name" value="{{ $customerGroup->name }}" placeholder="Başlık" required>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control">
                                            <option @if($customerGroup->status == 1) selected @endif value="1">Aktif</option>
                                            <option @if($customerGroup->status == 0) selected @endif value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
