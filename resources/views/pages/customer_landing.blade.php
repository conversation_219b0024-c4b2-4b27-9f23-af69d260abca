<!DOCTYPE html>
<html lang="{{ config('app.locale') }}">
<head>

    <meta charset="UTF-8">
    <meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=0'>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>Giri<PERSON></title>


    <!-- Favicon -->
    <link rel="icon" href="/assets/logo-icon.png" type="image/x-icon">

    <!-- Main Theme Js -->
    <script src="/assets/js/authentication-main.js"></script>

    <!-- Bootstrap Css -->
    <link id="style" href="/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet" >

    <!-- Style Css -->
    <link href="/assets/css/styles.min.css" rel="stylesheet" >

    <!-- Icons Css -->
    <link href="/assets/css/icons.min.css" rel="stylesheet" >
    <style>
        .card-sigin{
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .bg-image{
            background-image: url('/assets/images/slider.jpeg');
            background-position: center;
            background-size: cover;
            background-repeat: no-repeat;
        }
    </style>
    <style>
        .list-item{
            flex: 1 1;
            border: 1px solid #fff;
            border-top: unset;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            cursor: pointer;
            margin-top: 1rem;
        }
        .list-item:nth-child(1){
            border-left: unset;
        }
        .list-item:nth-child(2){
            border-right: unset;
        }
        .list-item i{
            font-size: 2.5rem;
        }
        .list-item span{
            text-align: end;
            font-size: 1rem;
            font-family: Poppins,sans-serif !important;
        }
        .slider-item{
            background-repeat: no-repeat;
            background-size: contain;
            height: 261px;
            background-color: #ff0c0b;
            background-position: top;
        }
        @media screen and (max-width: 799px) {
            .footer-logo{
                height: 35vh;
            }
        }
    </style>
</head>
<body class="ltr error-page1 ">
<div class="row">
    <div class="col-12">
        <div id="carouselExampleControls" class="carousel slide" data-bs-ride="carousel">
            <div class="carousel-inner">
                @foreach(\App\Models\Slider::where('place','customer')->get() as $index => $slider)
                    <div class="carousel-item @if($index == 0) active @endif">
                        <div class="slider-item" style="background-image: url('/storage/{{ $slider->image }}')"></div>
{{--                        <img src="/storage/{{ $slider->image }}"  alt="...">--}}
                    </div>
                @endforeach
            </div>
        </div>
    </div>
    <div class="col-12" style="background-color: #e30614;height: 100vh">
        <div class="d-flex">
            <a class="list-item" href="{{ route('nearbyBranches') }}">
                <i class="fe fe-map"></i>
                <span>EN YAKIN <br> UMRAN</span>
            </a>
            <a class="list-item" href="{{ route('loginPage',['type'=>'customer']) }}">
                <i class="fe fe-log-in"></i>
                <span>GİRİŞ <br> YAPIN</span>
            </a>
        </div>
        <div class="d-flex align-items-center footer-logo">
            <img src="/assets/logo-bg.png" alt="logo">
        </div>
    </div>
</div>
<!-- JQuery min js -->
<script src="/assets/plugins/jquery/jquery.min.js"></script>

<!-- Bootstrap js -->
<script src="/assets/plugins/bootstrap/js/popper.min.js"></script>
<script src="/assets/plugins/bootstrap/js/bootstrap.min.js"></script>

<!-- Moment js -->
<script src="/assets/plugins/moment/moment.js"></script>

<!-- eva-icons js -->
<script src="/assets/js/eva-icons.min.js"></script>

<!-- generate-otp js -->
<script src="/assets/js/generate-otp.js"></script>

<!--Internal  Perfect-scrollbar js -->
<script src="/assets/plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>

<!-- Theme Color js -->
<script src="/assets/js/themecolor.js"></script>

<!-- custom js -->
<script src="/assets/js/custom.js"></script>

<script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="/assets/js/<EMAIL>"></script>
<script src="/assets/js/imask.js"></script>
<script>
    if(document.querySelector('input[name="telefon"]')){
        const maskTel = IMask(document.querySelector('input[name="telefon"]'), {
            mask: '(*************'
        });
    }

    if(document.querySelector('input[name="login_sms"]')){
        const maskTel = IMask(document.querySelector('input[name="login_sms"]'), {
            mask: 'UMR-00-00'
        });
    }

    if(document.querySelector('input[name="totp_code"]')){
        const maskTel = IMask(document.querySelector('input[name="totp_code"]'), {
            mask: '000 000'
        });
    }



    @if(session('success'))
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });
    Toast.fire({
        icon: "success",
        title: "{{ session('success') }}"
    });
    @endif
    @if(session('error'))
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });
    Toast.fire({
        icon: "error",
        title: "{{ session('error') }}"
    });
    @endif
</script>

</body>
</html>
