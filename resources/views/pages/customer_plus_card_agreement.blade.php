<!DOCTYPE html>
<html lang="{{ config('app.locale') }}">
<head>

    <meta charset="UTF-8">
    <meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=0'>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>PLUS CARD GENEL KULLANIM ŞARTLARI PROTOKOLÜ</title>


    <!-- Favicon -->
    <link rel="icon" href="/assets/logo-icon.png" type="image/x-icon">

    <!-- Main Theme Js -->
    <script src="/assets/js/authentication-main.js"></script>

    <!-- Bootstrap Css -->
    <link id="style" href="/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet" >

    <!-- Style Css -->
    <link href="/assets/css/styles.min.css" rel="stylesheet" >

    <!-- Icons Css -->
    <link href="/assets/css/icons.min.css" rel="stylesheet" >
    <style>
        .loader-body{
            position: fixed !important;
        }
    </style>
</head>
<body class="ltr error-page1 ">
<div class="loader-body loader" style="display: none">
    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
</div>
<div class="row">
    @if($agreement->agreement_approval == 2)
    <div class="agreement_body table-responsive p-3">
        <style>
            tr {
                border: 1px solid #fff;
            }

            td {
                padding: 2px;
                text-align: justify;
            }
            button.button {
                margin: 0 auto;
                width: 100%;
                border: none;
                background-color: green;
                color: #fff;
                padding: 5px;
                border-radius: 10px;
                margin-top: 10px;
                margin-bottom: 40px;
            }
        </style>
        <table style="width: 100%">
            <thead>
                <th style="width: 100%;text-align: center;font-size: 20px;" colspan="6">PLUS CARD GENEL KULLANIM ŞARTLARI PROTOKOLÜ</th>
            </thead>
            <tbody>
                <tr style="width: 100%;">
                    <td colspan="6"><b>1.Taraflar</b></td>
                </tr>
                <tr style="width: 100%;">
                    <td colspan="6">İşbu genel kullanım şart ve koşulları;</td>
                </tr>
                <tr style="width: 100%;">
                    <td colspan="6">Örnek Mah. Fehmi Tokay Cad. No:18/A Kadıköy İstanbul adresinde mukim Umran Plus Otomotiv San. ve Tic. A.Ş.(Bundan böyle Umran Plus olarak anılacaktır.) Telefon No:4445417, </td>
                </tr>
                <tr style="width: 100%;">
                    <td colspan="6"><b>Mersis : 0892031858200017, VergiDairesi/No : ÜMRANİYE / 8920318582 İle,</b></td>
                </tr>
                <tr style="width: 100%;">
                    <td colspan="6">{{$agreement->getCustomer->mahalle ?? ''}} {{$agreement->getCustomer->cadde ?? ''}} {{$agreement->getCustomer->sokak ?? ''}} {{$agreement->getCustomer->semt ?? ''}} {{$agreement->getCustomer->getCity->title ?? ''}} / {{$agreement->getCustomer->getTown->ilce_title ?? ''}} adresinde mukim {{ucfirst($agreement->getCustomer->ad)}} {{ucfirst($agreement->getCustomer->soyad)}}</td>
                </tr>
                <tr style="width: 100%;">
                    <td colspan="6">
                        (Bundan böyle Kullanıcı olarak anılacaktır.)
                    </td>
                </tr>
                <tr style="width: 100%;">
                    <td colspan="3" style="width: 50%;">
                        <b>Telefon No</b> : {{$agreement->getCustomer->telefon}}
                    </td>
                    <td colspan="3" style="width: 50%;">
                        <b>TC/VKN No:</b> : {{$agreement->getCustomer->vergi_no}}
                    </td>
                </tr>
                <tr style="width: 100%;">
                    <td colspan="3" style="width: 50%;">
                        <b>Mersis</b> : {{$agreement->getCustomer->mersis}}
                    </td>
                    <td colspan="3" style="width: 50%;">
                        <b>Vergi Dairesi:</b> : {{$agreement->getCustomer->vergi_dairesi}}
                    </td>
                </tr>
                <tr style="width: 100%;">
                    <td colspan="6"><b>2.Tanımlar</b></td>
                </tr>
                <tr style="width: 100%;">
                    <td colspan="6"><b>Plus Card:</b> İkinci el motorlu kara taşıtları ticareti ile iştigal eden Kullanıcı'nın ön ödemeli satış sistemi ile Umran Plus'tan ileriye yönelik yalnızca alıcısı olduğu araçlarda kullanmak üzere hizmet almasını sağlayan üründür. (Bundan böyle kart olarak da anılacaktır.)</td>
                </tr>
                <tr>
                    <td colspan="6">
                        <b>Full Ekspertiz Paketi:</b> Umran Plus'ın kapsam ve içeriğini belirlediği bir ekspertiz paketidir. Kapsam ve içeriği: <b>Kaporta-Boya, Motor-Mekanik, İç ve Alt Kontroller, Fren Testi, Süspansiyon Testi, Yanal Kayma Testi, Diagnostic Test, Dyno Testi veya Yol Testi ‘ dir.</b></b>
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <b>Kredi</b>: Plus Card'a yüklenen bakiyelerin her bir adetidir.
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <b>3. KONU VE KAPSAM</b>
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        Umran Plus'ın sunduğu Plus Card ürününden Kullanıcıların faydalandırılmasına ilişkin genel koşulların, karşılıklı hak ve yükümlülüklerin ve mali koşulların belirlenmesidir.
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <b>4. GENEL KOŞULLAR,- HAK VE YÜKÜMLÜLÜKLER</b>
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>4.1</b> Plus Card, yalnızca ikinci el motorlu kara taşıtları ticareti ile iştigal eden gerçek ve/veya tüzel kişilere yönelik bir üründür. Plus Card; Kullanıcının, yalnızca alıcısı olduğu araçlarda kullanılabilir. Kullanıcının satıcısı olduğu araçlarda Plus Card kullanılamaz. Kullanıcı, Plus Card'ı satıcısı olduğu araçlarda ekspertiz hizmeti almak için kullanmayacağını kabul, beyan ve taahhüt eder.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>4.2</b> Plus Card, Umran Plus Otomotiv San. ve Tic. A.Ş.'nin malı olup; Kullanıcı, belirtilen süreler içerisinde kartın sadece kullanıcısıdır.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>4.3</b> Plus Card, sadece Umran Plus Otomotiv San. ve Tic. A.Ş. bayilerinde geçerli olup yalnızca Full Ekspertiz Paketi hizmetini belirtilen süreler içerisinde indirimli olarak satın almak için kullanılabilir. Umran Plus, karta konu hizmet paketini değiştirme hakkını saklı tutar.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>4.4</b> Plus Card; en az 5 kredi olmak üzere veya 10 ve katları şeklinde yüklenmesi şartıyla Umran Plus bayilerinden, Umran Plus resmi internet adresi üzerinden ve Umran Plus mobil uygulamaları üzerinden nakit ödeme, kredi kartı veya havale ödeme sistemi ile satın alınabilir.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>4.5</b> Plus Card ile satın alınan tüm hizmetler Umran Plus bayilerinden satın alınan tüm ekspertiz hizmetleri gibi İş Emri genel hüküm ve koşullarına tabi olacaktır. Kullanıcı Plus Card üzerinden satın aldığı her ekspertiz hizmeti için o tarihteki güncel İş Emri genel hüküm ve koşullarını peşinen kabul etmiş sayılır. Umran Plus ekspertiz hizmetleri İş Emri genel hüküm ve koşullarını değiştirme hakkını saklı tutar.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>4.6</b> Plus Card kullanım hakkı yalnızca işbu kartı satın alan Kullanıcıya aittir. Kullanıcı satın almış olduğu Plus Card'ı hiçbir surette üçüncü bir gerçek ve/veya tüzel kişiye kullandıramaz, devredemez ve satamaz. Umran Plus böyle bir durumu tespit etmesi halinde bahse konu Kullanıcının Plus Card'ını iptal edebilecektir. Böyle bir durumda bedel iadesi yapılmaz.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>4.7</b> Plus Card'ın kullanımından ve saklanmasından yalnızca Kullanıcı sorumludur. Kullanıcı; kartın çalınması, kaybedilmesi ve hasara uğraması hallerinde derhal bu durumu Umran Plus'a bildirecektir.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>4.8</b> Umran Plus; Kullanıcı tarafından işbu protokolde belirlenen hizmet şart ve kapsamların dışında kullanıldığının tespit edilmesi durumlarında Kullanıcıya tahsis etmiş olduğu Plus Card ürün ve hizmetini ve yüklenen bakiyeleri kısıtlama, iptal etme, kapatma hakkını saklı tutar. Böyle bir durumda Kullanıcıya bedel iadesi yapılmaz.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>4.9</b> Plus Card'ın satın alınması ve/veya kullanılması; kartın satın alınması esnasında Kullanıcıya sunulan ve aynı zamanda herhangi bir Umran Plus bayisinde, Umran Plus resmi internet adresinde ve Umran Plus mobil uygulamalarında da yer alan bu şart ve koşulların kesin kabulünü gerektirmektedir. Umran Plus, söz konusu koşul ve kapsamlarını, Plus Card ürününün ve bu karta konu hizmet paketlerinin; kapsam, içerik, süre ve ücretlerini herhangi bir zamanda değiştirme hakkını saklı tutmaktadır.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>4.10</b> İşbu sözleşme kapsamında işlenen kişisel verilerinize yönelik aydınlatma metnini incelemek isterseniz
                        <a target="_blank" href="https://www.umranoto.com.tr/kvkk">https://www.umranoto.com.tr/kvkk</a> internet adresimizi ziyaret edebilirsiniz.
                    </td>
                </tr>

                <tr>
                    <td colspan="6">
                        <b>5. ÜCRET</b>
                    </td>
                </tr>

                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>5.1</b> Plus Card ürünü vergiler dahil toplam ücreti; işbu kartın satışının gerçekleştiği tarihte Umran Plus bayilerinde, Umran Plus resmi internet adresinde ve Umran Plus mobil uygulamalarında yürürlükte olan güncel satış fiyatıdır.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>5.2</b> Plus Card, satış bedelinin Umran Plus'a ödenmesi ile birlikte Kullanıcıya teslim edilecek ve Umran Plus sistemlerine tanımlaması yapılarak kredileri aktif hale getirilecek ve/veya satışı gerçekleştirilecektir.
                    </td>
                </tr>

                <tr>
                    <td colspan="6">
                        <b>6. SÜRE</b>
                    </td>
                </tr>

                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>6.1</b> Plus Card'ın kullanım süresi aşağıda belirtilmiştir: <br>
                        Yükleme tarihi başlangıç alınmak üzere,<br>
                        -  5 - 49 kredi yüklemeleri arası 6 Ay <br>
                        - 50 kredi ve üzeri yüklemelerinde ise 1 YIL'dır. <br>

                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>6.2</b> Kullanıcı yükleme yaptığı kredi adedine bağlı olan süreler içerisinde dilediği zaman dilediği Umran Plus bayisinde hizmet alımını gerçekleştirecektir.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>6.3</b> Satın alınan kredi adedine bağlı süreler içerisinde kullanılmamış bakiyeler, süre bitiminde bir sonraki döneme olduğu şekliyle devredilemez.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>6.4</b> Kredi adedine bağlı süre bitiminde; <br>
                        1. Kullanıcı, dilerse kullanılmamış kredilerinin toplam TL karşılığıyla Umran Plus bayilerinde, Umran Plus resmi internet adresinde ve Umran Plus mobil uygulamalarında yürürlükte olan güncel kredi birim TL tutarına karşılık gelen adette krediye çevrilmesine onay verebilir.
                        <br>
                        2. Kullanıcı, dilerse mevcut kredi tutarının Umran Plus bayilerinde, Umran Plus resmi internet adresinde ve Umran Plus mobil uygulamalarında yürürlükte olan güncel kredi tutarı ile arasındaki farkı ödeyerek kredilerini aktif hale getirebilir.

                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>6.5</b> Kullanıcı yukarıda bahsedilen seçimlik haklardan hangisini seçtiğini 7 gün içerisinde Umran Plus'a veya bayisine bildirecektir.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>6.6</b> Kullanıcının 2. hakkı seçmesi durumunda ortaya çıkan fark ödemesini süre bitiminden itibaren 7 gün içerisinde Umran Plus'a ödemesi halinde kredileri aktif hale getirilecektir.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>6.7</b> Bahsi geçen 7 günlük süre içerisinde Kullanıcının Umran Plus'a; ortaya çıkan fark ödemesini yapmaması durumunda karttaki kullanılmamış krediler otomatik olarak kullanılmamış kredilerin toplam TL karşılığıyla Umran Plus bayilerinde, Umran Plus resmi internet adresinde ve Umran Plus mobil uygulamalarında yürürlükte olan güncel kredi birim TL tutarına karşılık gelen adette krediye çevrilecektir.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>6.8</b> Her iki şekilde de bir sonraki döneme devredilen krediler; adedine bağlı olarak yukarıdaki sürelere tabii olacaktır.
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <b>7. MALİ KOŞULLAR</b>
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>7.1</b> Plus Card, ön ödemeli olarak satın alınabilen ve hizmete konu ekspertiz işlemi ifasının satıştan sonra gerçekleştiği bir Umran Plus ürünüdür.
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>7.2</b>Yasal mevzuata göre  fatura; Plus Card'ın satışı anında değil her bir ekspertiz hizmetinin gerçekleştirildiği tarihten itibaren 7 gün içerisinde hizmetin gerçekleştiği tutarda hizmeti gerçekleştiren Umran Plus bayisi tarafından düzenlenerek Kullanıcıya iletilecektir. İşbu faturanın düzenlenme yükümlülüğü Umran Plus Bayisine aittir, bu hususta Umran Plus'ın herhangi bir sorumluluğu bulunmamaktadır.
                </tr>
                <tr>
                    <td colspan="6">
                        <b>8. UYUŞMAZLIKLARIN ÇÖZÜMÜ</b>
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        Kullanıcı; Plus Card bakiye ve işlem adetlerini Umran Plus resmi internet sitesi üzerinden, Umran Plus mobil uygulamaları üzerinden ve talep etmesi halinde Umran Plus bayilerinden takip edebilecektir. Kullanıcı ile Umran Plus'ın bakiye ve işlem adetleri kayıtlarının farklı olması halinde Umran Plus'ın kayıtları geçerli kabul edilecektir.
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <b>9. İŞLEM ÖZETİ</b>
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <b>İşbu protokole göre  {{$agreement->getPlusCard->no}} nolu karta   {{number_format((float)$agreement->payment_amount, 2, ',', '.')}}₺ TL karşılığında {{$agreement->unit_quantity}} Adet kredi kullanıcıya tanımlanmıştır.
                            <br></b>
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <b>Tanımlanan kredilerin son kullanma tarihi {{date('d/m/Y',strtotime($agreement->valid_date))}}'dir. Süre bitiminde işbu protokoldeki şartlar geçerli olacaktır.</b>
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <b>Bu hizmetin/satışın faturası her bir kredinin karşılığı olan ekspertiz hizmetinin gerçekleştiği tarihten itibaren 7 gün içerisinde hizmetin gerçekleştiği tutarda ve hizmeti gerçekleştiren Umran Plus bayisi tarafından düzenlenerek Kullanıcıya iletilecektir.</b>
                    </td>
                </tr>
                <tr>
                    <td colspan="6">
                        <b>10. DİĞER HÜKÜMLER</b>
                    </td>
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>10.1</b> İşbu genel kullanım şart ve koşulları protokolü Umran Plus tarafından satışa yetkilendirilmiş olan {{$agreement->getPersonel->name}} {{$agreement->getPersonel->surname}} Yetkili   ile  Kullanıcı {{$agreement->getCustomer->ad}} {{$agreement->getCustomer->soyad}} {{date('d/m/Y')}} tarihinde 10 madde olarak düzenlenmiştir.
                </tr>

                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>10.2</b> Esasen kanuna aykırı yöntemlerle elde edilen Plus Card'lar geçersiz sayılacak, hizmet satın almak için kullanılmayacak ve söz konusu kartların bedeli iade edilmeyecektir
                </tr>
                <tr>
                    <td colspan="6" style="padding-left: 15px;">
                        <b>10.3</b> İşbu protokolün uygulanmasından doğacak her türlü ihtilafın çözümünde İstanbul Mahkemeleri ve İcra Daireleri yetkilidir.
                </tr>
                <tr>
                    <td colspan="6" >
                       Bu belge {{$agreement->getPersonel->getBranch->unvan}} tarafından hazırlanmıştır. {{$agreement->getCustomer->ad}} {{$agreement->getCustomer->soyad}} tarafından @if(!empty(__('arrays.payment_types')[$agreement->payment_type]))
                        {{__('arrays.payment_types')[$agreement->payment_type]}} @else Nakit @endif Ödenmiştir.
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="col-12 text-center">
        <label for="agreement_accept" style="    width: 100%;
    text-align: center;
    font-size: 16px;
    font-weight: 500;"><input type="checkbox" id="agreement_accept" name="agreement_accept" value="1"> Okudum, Kabul ediyorum</label>
        <button type="button" class="btn col-12 btn-success mt-3 mb-5 send">Gönder</button>
    </div>
    @else
        <div class="col-12 text-center p-3" style="display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;">
           <span class="text-success" style="font-size: 50px;
}">
                <i class="fa fa-check-circle"></i>
                <br>
                    Sözleşmeyi Onayladınız.
           </span>
        </div>
    @endif
</div>
<!-- JQuery min js -->
<script src="/assets/js/jquery_3.6.1.min.js"></script>

<!-- Bootstrap js -->
<script src="/assets/plugins/bootstrap/js/popper.min.js"></script>
<script src="/assets/plugins/bootstrap/js/bootstrap.min.js"></script>

<!-- Moment js -->
<script src="/assets/plugins/moment/moment.js"></script>

<!-- eva-icons js -->
<script src="/assets/js/eva-icons.min.js"></script>

<!-- generate-otp js -->
<script src="/assets/js/generate-otp.js"></script>

<!--Internal  Perfect-scrollbar js -->
<script src="/assets/plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>

<!-- Theme Color js -->
<script src="/assets/js/themecolor.js"></script>

<!-- custom js -->
<script src="/assets/js/custom.js"></script>

<script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="/assets/js/<EMAIL>"></script>
<script src="/assets/js/imask.js"></script>
<script>
$('.send').click(function(){
    var agreement_accept = $('#agreement_accept').prop('checked');
    var agreement_integer = 2;
    if(agreement_accept){
        agreement_integer = 1;
    }
    if(agreement_integer == 2){
        Swal.fire({
            title: "Uyarı!",
            text: "Okudum, Kabul ediyorum kutucuğunu işaretlemediniz.",
            icon: "error",
            confirmButtonText: "Tamam"

        });
    }else{
        var agreement_body = $('.agreement_body').html();
        $('.loader').show()
        $.ajax({
            url: "{{ route('api.sendAgreementCustomer') }}",
            type: "POST",
            data: {
                agreement_integer:agreement_integer,
                agreement_body:agreement_body,
                id:'{{$id}}',
                _token:'{{csrf_token()}}',

            },
            success: function (response) {
                if(response.success){
                    location.reload();
                }else{
                    Swal.fire({
                        title: "Uyarı!",
                        text: "Bir Hata Oluştu Lütfen Yeni Sözleşme İsteyin.",
                        icon: "error",
                        confirmButtonText: "Tamam"

                    });
                }

            },
            error: function(jqXHR, textStatus, errorThrown) {
                console.log(textStatus, errorThrown);
            }
        });
    }
})
</script>

</body>
</html>
