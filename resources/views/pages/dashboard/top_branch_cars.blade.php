@extends('pages.build')
@section('title',$startDate->translatedFormat('d F Y') . ' - ' . $endDate->translatedFormat('d F Y') . ' <PERSON><PERSON>h Arası Bayi Raporu')
@push('css')
<link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
<link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('content')
<div class="row">
    <div class="col-xl-12">
        <div class="card custom-card">
            <div class="card-body">

                <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                    <thead>
                        <tr>
                            <th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th>Bayi</th>
                            <th>Bireysel</th>
                            <th>Plus Card</th>
                            <th>Toplam Araç Sayısı</th>
                            <th>İL</th>
                            <th>BÖLGE</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($branches as $branch)
                        <tr>
                            <td>{{ $loop->iteration }}</td>
                            <td>{{ $branch['kisa_ad'] }}</td>
                            <td>
                                {{ $branch['bireysel_count'] }}
                                @if(isset($branch['bireysel_diff']))
                                    @php
                                        $bDiff = $branch['bireysel_diff'];
                                        $bIcon = $bDiff >= 0 ? 'bx bx-caret-up' : 'bx bx-caret-down';
                                        $bColor = $bDiff >= 0 ? 'success' : 'danger';
                                    @endphp
                                    <small class="ms-1 text-{{ $bColor }}">
                                        <i class="{{ $bIcon }}"></i> {{ abs($bDiff) }}
                                    </small>
                                @endif
                            </td>
                            <td>
                                {{ $branch['plus_card_count'] }}
                                @if(isset($branch['plus_diff']))
                                    @php
                                        $pDiff = $branch['plus_diff'];
                                        $pIcon = $pDiff >= 0 ? 'bx bx-caret-up' : 'bx bx-caret-down';
                                        $pColor = $pDiff >= 0 ? 'success' : 'danger';
                                    @endphp
                                    <small class="ms-1 text-{{ $pColor }}">
                                        <i class="{{ $pIcon }}"></i> {{ abs($pDiff) }}
                                    </small>
                                @endif
                            </td>
                            <td>
                                {{ $branch['car_count'] }}
                                @if(isset($branch->change_amount))
                                    @php
                                        $diff = $branch->change_amount;
                                        $icon = $diff >= 0 ? 'bx bx-caret-up' : 'bx bx-caret-down';
                                        $color = $diff >= 0 ? 'success' : 'danger';
                                    @endphp
                                    <small class="ms-1 text-{{ $color }}">
                                        <i class="{{ $icon }}"></i>
                                        {{ abs($diff) }}
                                    </small>
                                @endif
                            </td>
                            <td>{{ $branch->il_name }}</td>
                            <td>{{ $branch->zone_name }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endsection
@push('js')
<script src="/assets/js/jquery_3.6.1.min.js"></script>
<!-- Datatables Cdn -->
<script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
<script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
<script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

<script>
    $(function(e) {
        'use strict';
        var responsiveDatatable = $('#responsiveDataTable').DataTable({
            "dom": '<"pull-left"f><"pull-right"l>rtip',
            scrollX: true,
            "aaSorting": [],
            "pageLength": 10,
            language: {
                "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                "infoEmpty": "Kayıt yok",
                "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                "infoThousands": ".",
                "lengthMenu": "Sayfada _MENU_ kayıt göster",
                "loadingRecords": "Yükleniyor...",
                "processing": "İşleniyor...",
                "search": "Ara:",
                "zeroRecords": "Eşleşen kayıt bulunamadı",
                "paginate": {
                    "first": "İlk",
                    "last": "Son",
                    "next": "Sonraki",
                    "previous": "Önceki"
                },
                "decimal": ",",
            }
        });

        var columns = responsiveDatatable.columns().header().toArray();

        // Her bir sütunun başlığını yazdırma
        let columnsHtml = '<div class="row">'
        columns.forEach(function(column) {
            if (column.cellIndex != 0) {
                columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                columnsHtml += '<label class="form-label" for="column' + column.cellIndex + '"><i class="fe fe-corner-down-right"></i>' + $(column).text() + '</label>'
                columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column' + column.cellIndex + '" value="' + column.cellIndex + '" style="border: 1px solid #0e0e0e">'
                columnsHtml += "</div>"
            }
        });
        columnsHtml += "</div>"
        $('.datatable-columns').html(columnsHtml)

        $('.column-show-hide').on('click', function() {
            let column = responsiveDatatable.column($(this).val());

            // Toggle the visibility
            column.visible(!column.visible());
        })
    });
</script>
@endpush
