@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@section('title','Fatura Tanımlamaları')
@section('content')
    <form id="mainForm" method="post" action="{{ route('invoice-definitions.store') }}">@csrf
        <div class="row">
            <div class="col-md-4">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgi" aria-expanded="true"
                                    aria-controls="aracBilgi">
                                Performans Tanımları
                            </button>
                        </h2>
                        <div id="aracBilgi" class="accordion-collapse collapse show" aria-labelledby="aracBilgiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label"><PERSON>i <PERSON> Tipi</label>
                                        <select name="cari_hesap_arama_tipi" class="form-control">
                                            <option>Aramadan Sonra Listele</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Cari Hesap Bakiyesi Gösterilsin</label>
                                        <select name="cari_hesap_bakiyesi_gosterilsin" class="form-control">
                                            <option value="1">Evet</option>
                                            <option value="0">Hayır</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kupon Takibi Yapılacak</label>
                                        <select name="kupon_takibi_yapilacak" class="form-control">
                                            <option value="1">Evet</option>
                                            <option value="0">Hayır</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kasa Banka Hesabı Seçilsin</label>
                                        <select name="kasa_banka_hesabi_secilsin" class="form-control">
                                            <option value="1">Evet</option>
                                            <option value="0">Hayır</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Barkod Giriş Yapılacak</label>
                                        <select name="barkod_girisi_yapilacak" class="form-control">
                                            <option value="1">Evet</option>
                                            <option value="0">Hayır</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="satisBelgeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#satisBelge" aria-expanded="true"
                                    aria-controls="satisBelge">
                                Satış Belge Tanımları
                            </button>
                        </h2>
                        <div id="satisBelge" class="accordion-collapse collapse show" aria-labelledby="satisBelgeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Cari Hesap Grubu</label>
                                        <input class="form-control" name="satis_cari_hesap_grubu">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Cari Hesap</label>
                                        <input class="form-control" name="satis_cari_hesap">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kasa Hesap</label>
                                        <input class="form-control" name="satis_kasa_hesabi">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kredi Kartı Hesap</label>
                                        <input class="form-control" name="satis_kredi_karti_hesabi">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Belge Özel Kodu</label>
                                        <select name="belge_ozel_kodu" class="form-control">
                                            <option value="1">Onaylı</option>
                                            <option value="0">Onaysız</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="alisBelgeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#alisBelge" aria-expanded="true"
                                    aria-controls="alisBelge">
                                Alış Belge Tanımları
                            </button>
                        </h2>
                        <div id="alisBelge" class="accordion-collapse collapse show" aria-labelledby="alisBelgeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Cari Hesap Grubu</label>
                                        <input class="form-control" name="alis_cari_hesap_grubu">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Cari Hesap</label>
                                        <input class="form-control" name="alis_cari_hesap">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kasa Hesap</label>
                                        <input class="form-control" name="alis_kasa_hesabi">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kredi Kartı Hesap</label>
                                        <input class="form-control" name="alis_kredi_karti_hesabi">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
        <input type="hidden" name="session_name" value="definition_invoice_create">
    </form>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script>
        setInterval(function (){
            $.ajax({
                url: "{{ route('saveToSession') }}",
                type: "post",
                data: $('#mainForm').serialize(),
                success: function (response) {

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        },30 * 1000)
    </script>
@endpush
