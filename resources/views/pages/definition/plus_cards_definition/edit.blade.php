@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />

    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('title','Plus Card Tanımlamaları')
@section('content')
    <form id="mainForm" method="post" action="{{ route('plus-cards-definitions.update',[$plusCardsDefinitions]) }}">@csrf @method('put')
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgi" aria-expanded="true"
                                    aria-controls="aracBilgi">
                                Tanım Detayı
                            </button>
                        </h2>
                        <div id="aracBilgi" class="accordion-collapse collapse show" aria-labelledby="aracBilgiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Tanım Adı</label>
                                        <input type="text" class="form-control" required="" placeholder="Tanım Adı"  name="definition_name" value="{{$plusCardsDefinitions->definition_name}}">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Tanım Miktarı</label>
                                        <input type="number" class="form-control" required=""  placeholder="Tanım Miktarı" name="unit_quantity" value="{{$plusCardsDefinitions->unit_quantity}}">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Tanım Adet Fiyatı</label>
                                        <input type="number" required="" class="form-control" placeholder="Tanım Adet Fiyatı" name="unit_price" value="{{$plusCardsDefinitions->unit_price}}">
                                        <label for="multiple_prices">Adete Göre Fiyat Belirle</label>
                                        <input type="checkbox" id="multiple_prices" @if($plusCardsDefinitions->getPrices->count() > 0) checked @endif name="multiple_prices" value="1">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Komisyon</label>
                                        <input type="number" class="form-control" required="" placeholder="Tanım Adet Fiyatı" name="commission" value="{{$plusCardsDefinitions->commission}}">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Fatura Kodu</label>
                                        <input type="text   " class="form-control" required="" placeholder="Fatura Kodu" name="definition_invoice_code" value="{{$plusCardsDefinitions->definition_invoice_code}}">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Stok</label>
                                        <select id="stock_id" class="select2" required="" name="stock_id[]" multiple="multiple">
                                            <option value="">Lütfen Seçim Yapınız</option>
                                            @foreach($stocks as $s)
                                                @php
                                                    $isSelected = false;
                                                @endphp
                                                @foreach($plusCardsDefinitions->getStockPivot as $def)
                                                    @if($s->id == $def->stock_id)
                                                        @php
                                                            $isSelected = true;
                                                        @endphp
                                                        @break
                                                    @endif
                                                @endforeach
                                                <option @if($isSelected) selected @endif value="{{$s->id}}">{{$s->ad}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-7 @if($plusCardsDefinitions->getPrices->count() == 0) d-none @endif" id="pricesTab">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="pricesHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#prices" aria-expanded="true"
                                    aria-controls="prices">
                                Fiyat Tablosu
                            </button>
                        </h2>
                        <div id="prices" class="accordion-collapse collapse show" aria-labelledby="pricesHeading">
                            <div class="accordion-body prices-list">

                                    @forelse($plusCardsDefinitions->getPrices as $definitionPrice)
                                        <div class="row">
                                            <div class="col-xl-4 mt-2">
                                                <label class="form-label">Min. Adet</label>
                                                <input type="text" class="form-control form-control-sm" placeholder="Min. Adet" value="{{ $definitionPrice->min_count }}" name="min_count[]">
                                            </div>
                                            <div class="col-xl-4 mt-2">
                                                <label class="form-label">Adet Fiyat</label>
                                                <input type="number" class="form-control form-control-sm" placeholder="Adet Fiyat" value="{{ $definitionPrice->price }}" name="price[]">
                                            </div>
                                            <div class="col-xl-4 mt-4">
                                                <button type="button" class="btn btn-danger remove-price btn-sm">Kaldır</button>
                                            </div>
                                        </div>
                                    @empty
                                        <div class="row">
                                            <div class="col-xl-4 mt-2">
                                                <label class="form-label">Min. Adet</label>
                                                <input type="text" class="form-control form-control-sm" placeholder="Min. Adet" name="min_count[]">
                                            </div>
                                            <div class="col-xl-4 mt-2">
                                                <label class="form-label">Fiyat</label>
                                                <input type="number" class="form-control form-control-sm" placeholder="Fiyat" name="price[]">
                                            </div>
                                            <div class="col-xl-4 mt-4">
                                                <button type="button" class="btn btn-danger remove-price btn-sm">Kaldır</button>
                                            </div>
                                        </div>
                                    @endforelse
                                </div>
                            </div>
                            <button type="button" class="btn btn-success btn-sm mt-4 add-row">Satır Ekle</button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12 mt-2">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
        <input type="hidden" name="session_name" value="definition_invoice_create">
    </form>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(".select2").select2({
            placeholder: "Ara.."
        });

        $('.remove-price').on('click',function (){
            $(this).parent().parent().remove()
        });

        $('#multiple_prices').on('click',function (){
            let pricesTab = $('#pricesTab')
            if ($(this)[0].checked){
                pricesTab.removeClass('d-none')
            }else{
                pricesTab.addClass('d-none')
            }

        })

        $('.add-row').on('click',function (){
            let html = '<div class="row"><div class="col-xl-4 mt-2"><label class="form-label">Min. Adet</label><input type="text" class="form-control form-control-sm" placeholder="Min. Adet" name="min_count[]"></div>'
            html += '<div class="col-xl-4 mt-2"><label class="form-label">Adet Fiyat</label><input type="number" class="form-control form-control-sm" placeholder="Fiyat" name="price[]"></div>'
            html += '<div class="col-xl-4 mt-4"><button type="button" class="btn btn-danger remove-price btn-sm">Kaldır</button></div></div>'
            $('.prices-list').append(html)

            $('.remove-price').on('click',function (){
                $(this).parent().parent().remove()
            });
        })
    </script>
@endpush
