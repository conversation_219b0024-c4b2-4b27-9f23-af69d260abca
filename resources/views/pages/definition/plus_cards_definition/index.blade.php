@extends('pages.build')
@section('title','Plus Card')
@push('css')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('content')
    <div class="row">
        <div class="col-xl-12">

            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    @if(in_array('*', $userRoles) || in_array('add_plus_cards_definition', $userRoles))
                        <a href="{{ route('plus-cards-definitions.create') }}" class="nav-link"><PERSON><PERSON><PERSON></a>
                    @endif
                    @if(in_array('*', $userRoles) || in_array('list_plus_cards_definition', $userRoles))
                        <a href="{{ route('plus-cards-definitions.index') }}" class="nav-link active">Plus Card Tanımları</a>
                    @endif

                </nav>
                <div class="card-body">

                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr>
                            <th>Tanım Adı</th>
                            <th>Tanım Miktarı</th>
                            <th>Tanım Adet Fiyatı</th>
                            <th>Komisyon</th>
                            <th>Fatura Kodu</th>
                            <th>Hizmet</th>
                            <th></th>
                        </tr>
                        </thead>
                        <tbody>
                            @foreach($plusCardsDefinitions as $pcd)
                                <tr>
                                    <td>{{$pcd->definition_name}}</td>
                                    <td>{{$pcd->unit_quantity}}</td>
                                    <td>{{$pcd->unit_price}}</td>
                                    <td>{{$pcd->commission}} %</td>
                                    <td>{{$pcd->definition_invoice_code}}</td>
                                    <td>
                                        @foreach($pcd->getStockPivot as $pivot)
                                            <p>{{$pivot->stock->ad}}</p>
                                        @endforeach
                                    </td>
                                    <td>
                                        <a href="{{ route('plus-cards-definitions.edit',[$pcd->id]) }}" class="btn btn-sm btn-success">
                                            <i class="fa fa-edit"></i>
                                        </a>
                                        @if(in_array('*', $userRoles) || in_array('delete_plus_cards_definition', $userRoles))
                                            <button type="button" class="btn btn-sm btn-danger delete" data-id="{{ $pcd->id }}">
                                                <i class="fa fa-trash"></i>
                                            </button>
                                        @endif

                                    </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>


    @endsection
    @push('js')
        <script src="/assets/js/jquery_3.6.1.min.js" ></script>
        <!-- Datatables Cdn -->
        <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
        <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
        <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

        <script>
            $(function (e) {
                'use strict';
                var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                    "aaSorting": [],
                    "pageLength": 10,
                    language: {
                        "emptyTable": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_plus_cards_definition', $userRoles)) <a href='{{ route("plus-cards-definitions.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                        "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                        "infoEmpty": "Kayıt yok",
                        "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                        "infoThousands": ".",
                        "lengthMenu": "Sayfada _MENU_ kayıt göster",
                        "loadingRecords": "Yükleniyor...",
                        "processing": "İşleniyor...",
                        "search": "Ara:",
                        "zeroRecords": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_plus_cards_definition', $userRoles)) <a href='{{ route("plus-cards-definitions.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                        "paginate": {
                            "first": "İlk",
                            "last": "Son",
                            "next": "Sonraki",
                            "previous": "Önceki"
                        },
                        "decimal": ",",
                    }
                });

                var columns = responsiveDatatable.columns().header().toArray();

                // Her bir sütunun başlığını yazdırma
                let columnsHtml = '<div class="row">'
                columns.forEach(function(column) {
                    if (column.cellIndex != 0){
                        columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                        columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                        columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                        columnsHtml += "</div>"
                    }
                });
                columnsHtml += "</div>"
                $('.datatable-columns').html(columnsHtml)

                $('.column-show-hide').on('click',function (){
                    let column = responsiveDatatable.column($(this).val());

                    // Toggle the visibility
                    column.visible(!column.visible());
                })
            });
            $(document).ready(function(){

                const Toast = Swal.mixin({
                    toast: true,
                    position: "top-end",
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.onmouseenter = Swal.stopTimer;
                        toast.onmouseleave = Swal.resumeTimer;
                    }
                });
                $('.delete').click(function(){
                    var id = $(this).attr('data-id');
                    var thisbtn = $(this)
                    Swal.fire({
                        title: "Emin misiniz?",
                        text: "Seçili Tanım Silinecek",
                        icon: "error",
                        showCancelButton: true,
                        confirmButtonColor: "#d33",
                        cancelButtonColor: "#3085d6",
                        confirmButtonText: "Evet, Sil!",
                        cancelButtonText: "Hayır, Silme!"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            var id = $(this).attr('data-id')
                            $.ajax({
                                url: "{{route('api.plusCardsDefinitionsDelete')}}",
                                type: "POST",
                                data: {
                                    '_token':'{{ csrf_token() }}',
                                    'id':id
                                } ,
                                success: function (response) {
                                    if (response.success){
                                        thisbtn.closest('tr').remove();
                                        Toast.fire({
                                            icon: "success",
                                            title: "Tanım Silindi"
                                        });
                                    }else{
                                        Toast.fire({
                                            icon: "error",
                                            title: "Bir Hata Oluştu Yeniden Deneyiniz."
                                        });
                                    }
                                },
                                error: function(jqXHR, textStatus, errorThrown) {
                                    console.log(textStatus, errorThrown);
                                }
                            });
                        }
                    });
                })
            })
        </script>
    @endpush
