@php $isAdmin = auth()->check() && auth()->user()->type == 'admin' ? true : false; @endphp
@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        .find-ruhsat-sahibi {
            padding: .5rem .85rem;
            background-color: #f3f3f3;
            max-height: 300px;
            overflow: auto;
            display: none;
        }

        .swal2-container .swal2-styled.swal2-confirm {
            background-color: #4ec3f0 !important;
        }
        .startPay {
            display: none !important;
        }
    </style>
@endpush
@push('message')
    - Belge Tarihi: <span
        class="text-danger">{{ $newDB->belge_tarihi }}</span> Belge Numarası:
    <span
        class="text-danger">{{ $newDB->belge_no }}</span>
@endpush
@section('title')
    Kayıt Detay
@endsection
@section('content')
    @include('pages.expertise.tabs')
    <form id="mainForm" method="post" action="{{ route('expertises.update',$newDB) }}" enctype="multipart/form-data">@csrf @method('put')
        <input type="hidden" name="uuid"
               value="{{ isset($newDB) && $newDB != null ? $newDB->uuid : '' }}">
        <input type="hidden" name="return_url" value="{{ url()->previous() }}">
        <div class="row">

            <div class="col-md-6">

                <div class="accordion" id="accordionExample">
                    <div class="accordion-item">
                        @php $getContractCode = $newDB->getPayment?->getContractCode;@endphp
                        <h2 class="accordion-header" id="sozlemeliHeading">
                            <button class="accordion-button sozlesmeli-button" @if($getContractCode) style="background-color: #06e328" @endif type="button" data-bs-toggle="collapse"
                                    data-bs-target="#sozlesmeliBilgiler" aria-expanded="false"
                                    aria-controls="sozlesmeliBilgiler">
                                Sözleşme | {{ $newDB->getPayment?->payment_code }}
                            </button>
                        </h2>
                        <div id="sozlesmeliBilgiler" class="accordion-collapse collapse" data-bs-parent="#accordionExample" aria-labelledby="sozlesmeliBilgilerHeading">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <div class="d-flex">
                                        {{--                                        <input type="text" class="form-control form-control-sm" value="{{ $newDB->getPayment?->payment_code }}" name="contract_no" readonly placeholder="Sözleşme No">--}}
                                        {{--                                        <button type="button" class="btn btn-danger btn-sm search-sozlesme-button">Ara--}}
                                        {{--                                        </button>--}}
                                    </div>
                                </div>
                                <div class="tab-pane show active text-muted" id="nav-sozlesme" role="tabpanel">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label class="form-label">Sözleşme No</label>
                                                <input type="text" minlength="1" class="form-control form-control-sm" readonly value="{{ $newDB->getPayment?->payment_code }}" name="contract_no">
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label class="form-label">Özel Kod</label>
                                                <div class="d-flex">
                                                    <input style="flex: 2;height: 30px" type="text" minlength="1" readonly value="{{ $newDB->getPayment?->payment_detail }}" class="form-control form-control-sm"  name="contract_code">
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" name="contract_status" value="{{ $getContractCode ? 1 : 0 }}">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="cariBilgilerHeading">
                            <button class="accordion-button ruhsat-sahibi-buton" @if($ruhsatCustomer != null) style="background-color: #06e328" @endif type="button" data-bs-toggle="collapse"
                                    data-bs-target="#cariBilgiler" aria-expanded="true"
                                    aria-controls="cariBilgiler">
                                Ruhsat Sahibi
                                @if($ruhsatCustomer != null)
                                    ({{ $ruhsatCustomer->fullName }}
                                    - {{ $ruhsatCustomer->telefon ?? $ruhsatCustomer->cep }})
                                @endif
                            </button>
                        </h2>
                        <div id="cariBilgiler" class="accordion-collapse collapse show"
                             data-bs-parent="#accordionExample" aria-labelledby="cariBilgilerHeading">
                            <div class="accordion-body">
                                <div class="form-group">
                                    @if($isAdmin)
                                        <div class="d-flex">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="ruhsat_search_type"
                                                       value="gsm" id="ruhsatSearchTypeGsm">
                                                <label class="form-check-label" for="ruhsatSearchTypeGsm">
                                                    GSM
                                                </label>
                                            </div>
                                            <div class="form-check mx-3">
                                                <input class="form-check-input" type="radio" name="ruhsat_search_type"
                                                       value="plus_card" id="ruhsatSearchTypePlusCard">
                                                <label class="form-check-label" for="ruhsatSearchTypePlusCard">
                                                    Plus Card
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" checked type="radio"
                                                       name="ruhsat_search_type" value="unvan" id="ruhsatSearchTypeUnvan">
                                                <label class="form-check-label" for="ruhsatSearchTypeUnvan">
                                                    Unvan
                                                </label>
                                            </div>
                                        </div>
                                    @endif

                                    @if($ruhsatCustomer != null)
                                        <input type="hidden" name="cari_id" value="{{ $ruhsatCustomer->id }}">
                                    @else
                                        <input type="hidden" name="cari_id">
                                    @endif
                                    @if($isAdmin)
                                        <div class="d-flex">
                                            <input type="text" class="form-control form-control-sm search-ruhsat-sahibi"
                                                   placeholder="Aramak için yazınız (En az 3 kelime)">
                                            <button type="button" class="btn btn-danger btn-sm search-ruhsat-sahibi-button">
                                                Ara
                                            </button>
                                        </div>

                                        <div class="find-ruhsat-sahibi"></div>
                                    @endif
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Kod</label>
                                        </div>
                                        <div class="col-md-10">
                                            @if($ruhsatCustomer != null)
                                                <input disabled class="form-control form-control-sm" name="cari_kod"
                                                       value="{{ $ruhsatCustomer->cari_kod }}">
                                            @else
                                                <input disabled class="form-control form-control-sm" name="cari_kod">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Unvan</label>
                                        </div>
                                        <div class="col-md-10">
                                            @if($ruhsatCustomer != null)
                                                <input disabled class="form-control form-control-sm" name="cari_unvan"
                                                       value="{{ $ruhsatCustomer->fullName }}">
                                            @else
                                                <input disabled class="form-control form-control-sm" name="cari_unvan">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Nereden Ulaştınız</label>
                                        </div>
                                        <div class="col-md-10">
                                            @if(!$isAdmin)
                                                <input disabled class="form-control form-control-sm" value="{{ $newDB->nereden_ulastiniz }}" name="nereden_ulastiniz">
                                            @else
                                                <select name="nereden_ulastiniz" class="form-control form-control-sm">
                                                    <option value="yok">Seçiniz</option>
                                                    <option
                                                        @if($newDB->nereden_ulastiniz == 'Reklam') selected
                                                        @endif value="Reklam">Reklam
                                                    </option>
                                                    <option
                                                        @if($newDB->nereden_ulastiniz == 'Galeri Müşterisi') selected
                                                        @endif value="Galeri Müşterisi">Galeri Müşterisi
                                                    </option>
                                                    <option
                                                        @if($newDB->nereden_ulastiniz == 'Arkadaş / Tavsiye') selected
                                                        @endif value="Arkadaş / Tavsiye">Arkadaş / Tavsiye
                                                    </option>
                                                    <option
                                                        @if($newDB->nereden_ulastiniz == 'Google') selected
                                                        @endif value="Google">Google
                                                    </option>
                                                    <option
                                                        @if($newDB->nereden_ulastiniz == 'Tanıtım ve Stand Faaliyetleri') selected
                                                        @endif value="Tanıtım ve Stand Faaliyetleri">Tanıtım ve Stand
                                                        Faaliyetleri
                                                    </option>
                                                </select>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="satici_ayni">Ruhsat sahibi ile satıcı aynı</label>
                                            <input type="checkbox" id="satici_ayni" name="satici_ayni" @if(!$isAdmin) disabled @endif
                                            @if($newDB->cari_id == $newDB->satici_id) checked @endif>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="alici_ayni">Ruhsat sahibi ile alıcı aynı</label>
                                            <input type="checkbox" id="alici_ayni" name="alici_ayni" @if(!$isAdmin) disabled @endif
                                            @if($newDB->cari_id == $newDB->alici_id) checked @endif>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="accordion-item saticiBilgiler"
                         @if($ruhsatCustomer != null && $saticiCustomer != null && $ruhsatCustomer->id == $saticiCustomer->id) style="display: none" @endif>
                        <h2 class="accordion-header" id="saticiBilgilerHeading">
                            <button class="accordion-button satici-buton" @if($saticiCustomer != null) style="background-color: #06e328" @endif type="button" data-bs-toggle="collapse"
                                    data-bs-target="#saticiBilgiler" aria-expanded="false"
                                    aria-controls="saticiBilgiler">
                                Satıcı Bilgileri
                                @if($saticiCustomer != null)
                                    ({{ $saticiCustomer->fullName }}
                                    - {{ $saticiCustomer->telefon ?? $saticiCustomer->cep }})
                                @endif
                            </button>
                        </h2>
                        <div id="saticiBilgiler" class="accordion-collapse collapse" data-bs-parent="#accordionExample"
                             aria-labelledby="saticiBilgilerHeading">
                            <div class="accordion-body">
                                <div class="form-group">
                                    @if($isAdmin)
                                        <div class="d-flex">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="satici_search_type"
                                                       value="gsm" id="saticiSearchTypeGsm">
                                                <label class="form-check-label" for="saticiSearchTypeGsm">
                                                    GSM
                                                </label>
                                            </div>
                                            <div class="form-check mx-3">
                                                <input class="form-check-input" type="radio" name="satici_search_type"
                                                       value="plus_card" id="saticiSearchTypePlusCard">
                                                <label class="form-check-label" for="saticiSearchTypePlusCard">
                                                    Plus Card
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" checked type="radio"
                                                       name="satici_search_type" value="unvan" id="saticiSearchTypeUnvan">
                                                <label class="form-check-label" for="saticiSearchTypeUnvan">
                                                    Unvan
                                                </label>
                                            </div>
                                        </div>
                                    @endif

                                    @if($saticiCustomer != null)
                                        <input type="hidden" name="satici_id" value="{{ $saticiCustomer->id }}">
                                    @else
                                        <input type="hidden" name="satici_id">
                                    @endif
                                    @if($isAdmin)
                                        <div class="d-flex">
                                            <input type="text" class="form-control form-control-sm search-satici"
                                                   placeholder="Aramak için yazınız (En az 3 kelime)">
                                            <button type="button" class="btn btn-danger btn-sm search-satici-button">Ara
                                            </button>
                                        </div>
                                        <div class="filter-results find-satici"></div>
                                    @endif

                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Kod</label>
                                        </div>
                                        <div class="col-md-10">
                                            @if($saticiCustomer != null)
                                                <input disabled class="form-control form-control-sm" name="satici_kod"
                                                       value="{{ $saticiCustomer->cari_kod }}">
                                            @else
                                                <input disabled class="form-control form-control-sm" name="satici_kod">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Unvan</label>
                                        </div>
                                        <div class="col-md-10">
                                            @if($saticiCustomer != null)
                                                <input disabled class="form-control form-control-sm" name="satici_unvan"
                                                       value="{{ $saticiCustomer->fullName }}">
                                            @else
                                                <input disabled class="form-control form-control-sm"
                                                       name="satici_unvan">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item aliciBilgiler"
                         @if($ruhsatCustomer != null && $aliciCustomer != null && $ruhsatCustomer->id == $aliciCustomer->id) style="display: none" @endif>
                        <h2 class="accordion-header" id="aliciBilgilerHeading">
                            <button class="accordion-button alici-buton" @if($aliciCustomer != null) style="background-color: #06e328" @endif type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aliciBilgiler" aria-expanded="false"
                                    aria-controls="aliciBilgiler">
                                Alıcı Bilgileri
                                @if($aliciCustomer != null)
                                    ({{ $aliciCustomer->fullName }}
                                    - {{ $aliciCustomer->telefon ?? $aliciCustomer->cep }})
                                @endif
                            </button>
                        </h2>
                        <div id="aliciBilgiler" class="accordion-collapse collapse" data-bs-parent="#accordionExample"
                             aria-labelledby="aliciBilgilerHeading">
                            <div class="accordion-body">
                                <div class="form-group">
                                    @if($isAdmin)
                                        <div class="d-flex">
                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="alici_search_type"
                                                       value="gsm" id="aliciSearchTypeGsm">
                                                <label class="form-check-label" for="aliciSearchTypeGsm">
                                                    GSM
                                                </label>
                                            </div>
                                            <div class="form-check mx-3">
                                                <input class="form-check-input" type="radio" name="alici_search_type"
                                                       value="plus_card" id="aliciSearchTypePlusCard">
                                                <label class="form-check-label" for="aliciSearchTypePlusCard">
                                                    Plus Card
                                                </label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" checked type="radio"
                                                       name="alici_search_type" value="unvan" id="aliciSearchTypeUnvan">
                                                <label class="form-check-label" for="aliciSearchTypeUnvan">
                                                    Unvan
                                                </label>
                                            </div>
                                        </div>
                                    @endif

                                    @if($aliciCustomer != null)
                                        <input type="hidden" name="alici_id" value="{{ $aliciCustomer->id }}">
                                    @else
                                        <input type="hidden" name="alici_id">
                                    @endif
                                    @if($isAdmin)
                                        <div class="d-flex">
                                            <input type="text" class="form-control form-control-sm search-alici"
                                                   placeholder="Aramak için yazınız (En az 3 kelime)">
                                            <button type="button" class="btn btn-danger btn-sm search-alici-button">Ara
                                            </button>
                                        </div>
                                        <div class="filter-results find-alici"></div>
                                    @endif
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Kod</label>
                                        </div>
                                        <div class="col-md-10">
                                            @if($aliciCustomer != null)
                                                <input disabled class="form-control form-control-sm" name="alici_kod"
                                                       value="{{ $aliciCustomer->cari_kod }}">
                                            @else
                                                <input disabled class="form-control form-control-sm" name="alici_kod">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Unvan</label>
                                        </div>
                                        <div class="col-md-10">
                                            @if($aliciCustomer != null)
                                                <input disabled class="form-control form-control-sm" name="alici_unvan"
                                                       value="{{ $aliciCustomer->fullName }}">
                                            @else
                                                <input disabled class="form-control form-control-sm" name="alici_unvan">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgilerHeading">
                            <button class="accordion-button car-buton" @if($car != null) style="background-color: #06e328" @endif type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgiler" aria-expanded="false"
                                    aria-controls="aracBilgiler">
                                Araç Bilgileri
                                @if($car != null)
                                    ({{ $car->plaka }} - {{ $car->sase_no }})
                                @endif
                            </button>
                        </h2>
                        <div id="aracBilgiler" class="accordion-collapse collapse" data-bs-parent="#accordionExample"
                             aria-labelledby="aracBilgilerHeading">
                            <div class="accordion-body">
                                @if($isAdmin)
                                    <div class="form-group">
                                        <label>Ara <span class="text-danger">*</span></label>
                                        <div class="d-flex">
                                            <input type="text" class="form-control form-control-sm search-car"
                                                   placeholder="Aramak için yazınız (En az 6 kelime)">
                                            <button type="button" class="btn btn-danger btn-sm search-car-button">Ara
                                            </button>
                                        </div>

                                        <div class="filter-results find-car"></div>
                                    </div>
                                @endif

                                @if($car != null)
                                    <input type="hidden" name="car_id" value="{{ $car->id }}">
                                @else
                                    <input type="hidden" name="car_id">
                                @endif
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Plaka</label>
                                        </div>
                                        <div class="col-md-7">
                                            @if($car != null)
                                                <input disabled class="form-control form-control-sm" name="arac_plaka"
                                                       value="{{ $car->plaka }}">
                                            @else
                                                <input disabled class="form-control form-control-sm" name="arac_plaka">
                                            @endif
                                        </div>
                                        @if($isAdmin)
                                            <div class="col-md-3">
                                                <a href="{{ route('cars.create',['return_url'=>url()->current()]) }}"
                                                   class="btn  change_info_car">
                                                    <i class="fa fa-copy"></i>
                                                </a>
                                                @if($car != null)
                                                    <a href="{{route('cars.edit',['car'=>$car,'return_url'=>url()->current()])}}"
                                                       class="btn  edit_info_car">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                @else
                                                    <a href="#"
                                                       class="btn  edit_info_car">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        @endif

                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Şase</label>
                                        </div>
                                        <div class="col-md-7">
                                            @if($car != null)
                                                <input disabled class="form-control form-control-sm" name="arac_sase"
                                                       value="{{ $car->sase_no }}">
                                            @else
                                                <input disabled class="form-control form-control-sm" name="arac_sase">
                                            @endif
                                        </div>
                                        @if($isAdmin)
                                            <div class="col-md-3">
                                                <a href="{{ route('cars.create',['return_url'=>url()->current()]) }}"
                                                   class="btn  change_info_car">
                                                    <i class="fa fa-copy"></i>
                                                </a>
                                                @if($car != null)
                                                    <a href="{{route('cars.edit',['car'=>$car,'return_url'=>url()->current()])}}"
                                                       class="btn  edit_info_car">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                @else
                                                    <a href="#"
                                                       class="btn  edit_info_car">
                                                        <i class="fa fa-edit"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        @endif

                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Model</label>
                                        </div>
                                        <div class="col-md-10">
                                            @if($car != null)
                                                <input disabled class="form-control form-control-sm"
                                                       name="arac_marka_model"
                                                       value="{{ $car->getMarka?->name }} {{ $car->getModel?->name }}">
                                            @else
                                                <input disabled class="form-control form-control-sm"
                                                       name="arac_marka_model">
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>KM</label>
                                        </div>
                                        <div class="col-md-10">
                                            <input type="text" name="km" class="form-control form-control-sm" id="km" @if(!$isAdmin) disabled @endif
                                            placeholder="0" value="{{$car->km ?? ''}}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group ">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Km/Mil</label>
                                        </div>
                                        <div class="col-md-10">
                                            <select name="km_type" @if(!$isAdmin) disabled @endif class="form-control form-control-sm" id="km_type"
                                                    required="">
                                                <option @if(!empty($car->km_type)  && $car->km_type == 1) selected=""
                                                        @endif value="1">Km
                                                </option>
                                                <option @if(!empty($car->km_type)  && $car->km_type == 2) selected=""
                                                        @endif value="2">Mil
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="hizmetBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#hizmetBilgiler" aria-expanded="false"
                                    aria-controls="hizmetBilgiler">
                                Hizmet
                            </button>
                        </h2>
                        <div id="hizmetBilgiler" class="accordion-collapse collapse" data-bs-parent="#accordionExample" aria-labelledby="hizmetBilgilerHeading">
                            @php $canSeeDetailed = in_array(\auth()->user()->user_role_group_id,__('arrays.expertiseDetailSeeUsers')); @endphp
                            <div class="accordion-body row">
                                @if($canSeeDetailed)
                                    <div class="col-md-12">
                                        @php
                                            if ($getContractCode){
                                                $contractStocks = \App\Models\ContractStock::where('contract_id',$getContractCode->contract_id)->where('type',$getContractCode->invoice_customer_id == $aliciCustomer->id ? 'alici' : 'satici')->get();
                                                $contractStockIds = $contractStocks->pluck('stock_id')->toArray();
                                                $stocks = \App\Models\Stock::whereIn('id',$contractStockIds)->get();
                                            }
                                        @endphp
                                        @if(!$getContractCode)
                                            <div class="form-group">
                                                <label>Ara</label>
                                                @php
                                                    $fiyat = $newDB->getStockhasOne?->liste_fiyati ?? 0;
                                                @endphp
                                                <select class="form-control form-control-sm find-stock">
                                                    <option value="0">Hizmet Ekleyin</option>

                                                    @foreach($stocks as $stock)
                                                        @php
                                                            if (!empty($car) && $car->getFuel?->name === 'Elektrik' && !in_array($stock->id, [9, 187])) {
                                                                continue;
                                                            }

                                                            $kdvHaricFiyat = $stock->getPrices->where('campaign_id',0)->first() ? $stock->getPrices->where('campaign_id',0)->first()->kdv_haric_fiyat : 0;
                                                            $kdvDahilFiyat = $stock->getPrices->where('campaign_id',0)->first() ? $stock->getPrices->where('campaign_id',0)->first()->kdv_dahil_fiyat : 0;
                                                            $max_iskonto_amount = $getContractCode ? 0 : $stock->iskonto_tutari;
                                                        @endphp
                                                        @if(auth()->user()->type == 'admin')
                                                            <option value="{{ $stock->id }}" class="filter-item"
                                                                    data-id="{{ $stock->id }}"
                                                                    data-ad="{{ $stock->ad }}"
                                                                    data-kdv="{{ $stock->kdv }}"
                                                                    data-kdvHaricfiyat="{{ $kdvHaricFiyat }}"
                                                                    data-fiyat="{{ $kdvDahilFiyat }}"
                                                                    data-yolYardimi="{{ $stock->yol_yardimi == 1 ? 'Var' : 'Yok' }}"
                                                                    data-sorguHizmeti="{{ $stock->sorgu_hizmeti == 1 ? 'Var' : 'Yok' }}"
                                                                    data-maxIskonto="{{$max_iskonto_amount}}"
                                                                    data-normalpayment="{{$stock->active_normal_payment}}"
                                                                    data-pluscartpayment="{{$stock->active_plus_cart}}"
                                                            >{{ $stock->ad }}
                                                                ( {{ $stock->getPrices->where('campaign_id',0)->first() ? $kdvDahilFiyat : 0 }}
                                                                ₺)
                                                            </option>
                                                        @else
                                                            @if(($stock->getPrices->where('campaign_id',0)->first() ? $kdvDahilFiyat : 0 ) >= $fiyat)
                                                                <option value="{{ $stock->id }}" class="filter-item"
                                                                        data-id="{{ $stock->id }}"
                                                                        data-ad="{{ $stock->ad }}"
                                                                        data-kdv="{{ $stock->kdv }}"
                                                                        data-kdvHaricfiyat="{{ $kdvHaricFiyat }}"
                                                                        data-fiyat="{{ $kdvDahilFiyat }}"
                                                                        data-yolYardimi="{{ $stock->yol_yardimi == 1 ? 'Var' : 'Yok' }}"
                                                                        data-sorguHizmeti="{{ $stock->sorgu_hizmeti == 1 ? 'Var' : 'Yok' }}"
                                                                        data-maxIskonto="{{$max_iskonto_amount}}"
                                                                        data-normalpayment="{{$stock->active_normal_payment}}"
                                                                        data-pluscartpayment="{{$stock->active_plus_cart}}"
                                                                >{{ $stock->ad }}
                                                                    ( {{ $stock->getPrices->where('campaign_id',0)->first() ? $kdvDahilFiyat : 0 }}
                                                                    ₺)
                                                                </option>
                                                            @endif
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        @endif
                                    </div>
                                @endif

                                <div class="services row">
                                    @if(isset($newDB) && $newDB != null)
                                        @foreach($newDB->getStocks as $expertiseStock)
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Hizmet</label>
                                                        <input type="text" class="form-control form-control-sm" readonly
                                                               value="{{ $expertiseStock->getStock->ad }}">
                                                        <input type="hidden" class="form-control form-control-sm"
                                                               name="stock_id[]"
                                                               value="{{ $expertiseStock->stock_id }}">

                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Geçerli Kampanya
                                                        </label>
                                                        @if(!$canSeeDetailed || $getContractCode)
                                                            <input placeholder="Seçilmedi" class="form-control-sm form-control" disabled>
                                                            <input type="hidden" name="campaign_id[]" value="0">
                                                        @else
                                                            <select name="campaign_id[]"
                                                                    class="campaing_select select2 js-states">
                                                                @php
                                                                    $max_iskonto_amount = 0;

                                                                    if(!empty($expertiseStock->getStock->iskonto_tutari)){
                                                                        $max_iskonto_amount = $expertiseStock->getStock->iskonto_tutari;
                                                                    }
                                                                @endphp
                                                                <option
                                                                    data-kdvharicfiyat="{{$expertiseStock->getStock->getStandartPrices->kdv_haric_fiyat}}"
                                                                    data-fiyat="{{$expertiseStock->getStock->getStandartPrices->kdv_dahil_fiyat}}"
                                                                    data-maxiskonto="{{$max_iskonto_amount}}" value="0">
                                                                    Seçilmedi
                                                                </option>
                                                                @foreach($expertiseStock->getStock->getPrices->where('campaign_id','!=',0) as $price)
                                                                    @php
                                                                        $max_iskonto_amount = 0;
                                                                        if($price->getCampaing->id == 261)
                                                                        {
                                                                            $max_iskonto_amount = $expertiseStock->getStock->iskonto_tutari;
                                                                        }
                                                                    @endphp
                                                                    <option data-kdvharicfiyat="{{$price->kdv_haric_fiyat}}"
                                                                            data-fiyat="{{$price->kdv_dahil_fiyat}}"
                                                                            data-maxiskonto="{{$max_iskonto_amount}}"
                                                                            @if($expertiseStock->campaign_id == $price->getCampaing->id) selected
                                                                            @endif value="{{ $price->getCampaing->id }}">{{$price->getCampaing->name}}</option>
                                                                @endforeach
                                                            </select>
                                                        @endif

                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Sorgu Hizmeti</label>
                                                        <input @if(!$canSeeDetailed || $getContractCode) readonly @endif type="text" class="form-control form-control-sm" name="sorgu_hizmeti[]"
                                                               value="{{ $expertiseStock->getStock->sorgu_hizmeti == 1 ? 'Var' : 'Yok' }}">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Yol Yardimi</label>
                                                        <input @if(!$canSeeDetailed || $getContractCode) readonly @endif type="text" class="form-control form-control-sm" name="yol_yardimi[]"
                                                               value="{{ $expertiseStock->getStock->yol_yardimi == 1 ? 'Var' : 'Yok' }}">
                                                    </div>
                                                </div>

                                                <div class="col-md-3">
                                                    <div class="form-group">

                                                        <label>İskonto Tutarı</label>
                                                        <input @if(!$canSeeDetailed || $getContractCode) readonly @endif type="text" name="iskonto_amount[]" min="0"
                                                               max="{{$expertiseStock->getStock->iskonto_tutari}}"
                                                               class="form-control form-control-sm iskonto_amount_hizmet"
                                                               value="{{$expertiseStock->iskonto_amount ?? 0}}" @if(auth()->user()->type != 'admin') readonly @endif>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Hizmet Tutarı</label>
                                                        <input @if(!$canSeeDetailed || $getContractCode) readonly @endif type="text"
                                                               class="form-control form-control-sm service_price" name="hizmet_tutari[]"
                                                               value="{{ $expertiseStock->hizmet_tutari }}">
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="form-group">
                                                        <label>Liste Fiyatı</label>
                                                        <input @if(!$canSeeDetailed || $getContractCode) readonly @endif type="text"
                                                               class="form-control form-control-sm list_price" name="liste_fiyati[]"
                                                               value="{{ $expertiseStock->liste_fiyati }}">
                                                    </div>
                                                </div>
                                                @if($isAdmin && !$getContractCode)
                                                    <div class="col-md-12 text-end">
                                                        <button type="button"
                                                                class="btn btn-danger btn-sm mt-4 remove-service"
                                                                data-id="{{ $expertiseStock->stock_id }}">Sil
                                                        </button>
                                                    </div>
                                                @endif
                                            </div>
                                        @endforeach
                                    @endif
                                    <hr>
                                </div>
                                <table class="table table-hover">
                                    <thead>
                                    <tr>
                                        <th>Hizmet</th>
                                        {{--                                        <th>KDV Oranı</th>--}}
                                        <th>Tutarı</th>
                                    </tr>
                                    </thead>
                                    <tbody class="selectedStocks"></tbody>
                                    <tfoot>
                                    <tr>
                                        <td colspan="1" style="text-align: end">Alt Toplam:</td>
                                        <td class="subTotal">0₺</td>
                                    </tr>

                                    <tr>
                                        <td colspan="1" style="text-align: end">İskonto:</td>
                                        <td class="iskonto_amount_cont">0₺</td>
                                    </tr>
                                    <tr class="kdv-item d-none kdv-0">
                                        <td colspan="1" style="text-align: end">KDV(%0):</td>
                                        <td class="kdv-value-0">0₺</td>
                                    </tr>
                                    <tr class="kdv-item d-none kdv-1">
                                        <td colspan="1" style="text-align: end">KDV(%1):</td>
                                        <td class="kdv-value-1">0₺</td>
                                    </tr>
                                    <tr class="kdv-item d-none kdv-8">
                                        <td colspan="1" style="text-align: end">KDV(%8):</td>
                                        <td class="kdv-value-8">0₺</td>
                                    </tr>
                                    <tr class="kdv-item d-none kdv-18">
                                        <td colspan="1" style="text-align: end">KDV(%18):</td>
                                        <td class="kdv-value-18">0₺</td>
                                    </tr>
                                    <tr class="kdv-item d-none kdv-20">
                                        <td colspan="1" style="text-align: end">KDV(%20):</td>
                                        <td class="kdv-value-20">0₺</td>
                                    </tr>
                                    <tr>
                                        <td colspan="1" style="text-align: end">Genel Toplam:</td>
                                        <td class="allTotal">0₺</td>
                                    </tr>
                                    </tfoot>
                                </table>
                                @if($newDB->payment_type != 'sozlesme')
                                    <div class="example">
                                        <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                                            <a class="nav-link normal_payment_tab @if($newDB->payment_type == 'normal') active @endif"
                                               data-bs-toggle="tab" role="tab" href="#nav-normal-odeme"
                                               onclick="$('input[name=\'payment_type\']').val('normal')"
                                               aria-selected="true">Normal Ödeme</a>
                                            <a class="nav-link pluscart_payment_tab @if($newDB->payment_type == 'plus_card' || $newDB->payment_type == 'plus_kart') active @endif"
                                               data-bs-toggle="tab" role="tab" href="#nav-plus-kart"
                                               onclick="$('input[name=\'payment_type\']').val('plus_kart')"
                                               aria-selected="false">Plus Card</a>
                                            {{--                                        <a class="nav-link sozlesme_payment_tab @if($newDB->payment_type == 'sozlesme') active @endif"--}}
                                            {{--                                           data-bs-toggle="tab" role="tab" href="#nav-sozlesme"--}}
                                            {{--                                           onclick="$('input[name=\'payment_type\']').val('sozlesme')"--}}
                                            {{--                                           aria-selected="false">Sözleşme</a>--}}
                                        </nav>
                                        <div class="tab-content">
                                            <div
                                                class="tab-pane @if($newDB->payment_type == 'normal') show active @endif text-muted"
                                                id="nav-normal-odeme" role="tabpanel">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <button type="button" @if(!$canSeeDetailed) disabled @endif class="btn btn-sm add-payment btn-success">
                                                            Ödeme Ekle
                                                        </button>
                                                        <div class="row ">
                                                            <label class="payment-total text-end">Ödeme Tutar :
                                                                <span>0₺</span>
                                                            </label>
                                                        </div>
                                                        <div class="row payments">
                                                            @if(!empty($newDB->getPayment) && $newDB->getPayment->type != 'plus_kart')
                                                                @foreach($newDB->getPayments as $payments)
                                                                    <div class="row odeme_tr_{{strtotime($payments->created_at)}} odeme_delete_column_{{$payments->id}}">
                                                                        <div class="col-md-3 ">
                                                                            <div class="form-group">
                                                                                <label>Ödeme Türü</label>
                                                                                <select name="odeme_tip[]" @if(!$canSeeDetailed) disabled @else required @endif data-timestamp="{{strtotime($payments->created_at)}}" id="odeme_tip_{{strtotime($payments->created_at)}}" class="form-control-sm form-control odeme_tip_select">
                                                                                    @if(auth()->user()->type == 'admin')
                                                                                        @foreach(__('arrays.payment_types') as $paymentKey => $paymentValue)
                                                                                            <option @if($payments->type == $paymentKey) selected="" @endif value="{{ $paymentKey }}">{{ $paymentValue }}</option>
                                                                                        @endforeach
                                                                                    @else
                                                                                        <option value="">Lütfen Seçim Yapınız</option>
                                                                                        <option @if($payments->type == "kredi_karti") selected="" @endif value="kredi_karti">Kredi Kartı</option>
                                                                                        <option @if($payments->type == "nakit") selected="" @endif value="nakit">Nakit</option>
                                                                                        <option @if($payments->type == "banka") selected="" @endif value="banka">Banka</option>
                                                                                        <option @if($payments->type == "acik_hesap") selected="" @endif value="acik_hesap">Açık Hesap</option>
                                                                                    @endif
                                                                                </select>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-3">
                                                                            <div class="form-group">
                                                                                <label>Hesap</label>
                                                                                <select @if(!$canSeeDetailed) disabled @endif name="odeme_hesap[]"
                                                                                        id="odeme_hesap_{{strtotime($payments->created_at)}}"
                                                                                        class="form-control-sm form-control">
                                                                                    <option value="0">Seçilmedi</option>
                                                                                </select>
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-3">
                                                                            <div class="form-group">
                                                                                <label>Tutar </label>
                                                                                <input @if(!$canSeeDetailed) disabled @endif
                                                                                type="text" name="odeme_tutar[]"
                                                                                       value="₺{{number_format((float)$payments->amount, 2, ',', '.')}}"
                                                                                       id="odeme_tutar_{{strtotime($payments->created_at)}}"
                                                                                       class="form-control form-control-sm odeme_tutar_input">
                                                                            </div>
                                                                        </div>
                                                                        <div class="col-md-3 @if(!$canSeeDetailed) d-none @endif">
                                                                            <button type="button" class="btn btn-danger btn-sm mt-4 deleteOdeme" data-id="{{$payments->id}}"><i class="fa fa-trash"></i></button>
                                                                        </div>
                                                                    </div>

                                                                @endforeach
                                                            @endif
                                                        </div>
                                                        <div class="row ">
                                                            <label class="payment-left text-end">Kalan Tutar :
                                                                <span>0₺</span>
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="tab-pane @if($newDB->payment_type == 'plus_card' || $newDB->payment_type == 'plus_kart') show active @endif text-muted"
                                                id="nav-plus-kart" role="tabpanel">
                                                <div class="form-group">
                                                    <label for="plus_kredi">Kredi Kullan</label>
                                                    <input @if(!$canSeeDetailed) disabled @endif type="radio" id="plus_kredi" name="plus_card_payment_type" @if($newDB->getPayments->where('type','plus_kart')->first() && $newDB->getPayments->where('type','plus_kart')->first()->getPlusCardOdeme?->balance_type == 'credits') checked @endif
                                                    value="kredi">
                                                </div>
                                                <div class="form-group">
                                                    <label for="plus_puan">Puan Kullan</label>
                                                    <input @if(!$canSeeDetailed) disabled @endif type="radio" id="plus_puan" name="plus_card_payment_type"  @if($newDB->getPayments->where('type','plus_kart')->first() && $newDB->getPayments->where('type','plus_kart')->first()->getPlusCardOdeme?->balance_type == 'points') checked @endif
                                                    value="puan">
                                                </div>
                                                <div class="row">
                                                    <table class="table table-bordered">
                                                        <thead>
                                                        <tr>
                                                            <th>No</th>
                                                            <th>ID</th>
                                                            <th>Kredi</th>
                                                            <th>Puan</th>
                                                            <th>Seç</th>
                                                        </tr>
                                                        </thead>
                                                        <tbody class="alici-plus-kartlar">
                                                        @if(!empty($aliciCustomer->getPlusCards))
                                                            @foreach($aliciCustomer->getPlusCards as $pluscard)
                                                                <tr>
                                                                    <td>{{$pluscard->no}}</td>
                                                                    <td>{{$pluscard->system_id}}</td>
                                                                    <td class="creditstd">{{!empty($newDB->getStockhasOne->getStock->id) ? $pluscard->getTotalBalanceStock($newDB->getStockhasOne->getStock->id,$newDB->branch_id)['credits'] : 0}} </td>
                                                                    <td class="pointstd">{{!empty($newDB->getStockhasOne->getStock->id) ? $pluscard->getTotalBalanceStock($newDB->getStockhasOne->getStock->id,$newDB->branch_id)['points']:0}}</td>
                                                                    <td>
                                                                        @if(($newDB->getPayments->where('plus_card_id',$pluscard->id)->first()))
                                                                            <input @if(!$canSeeDetailed) disabled @endif type="radio" name="plus_kart_id"
                                                                                   data-credits="{{$pluscard->getTotalBalanceStock($newDB->getStockhasOne->getStock->id,$newDB->branch_id)['credits']}}"
                                                                                   data-points="0"
                                                                                   @if(($newDB->getPayments->where('plus_card_id',$pluscard->id)->first())) checked @endif
                                                                                   value="{{$pluscard->id}}">
                                                                        @elseif($canSeeDetailed)
                                                                            @if(empty($newDB->getStockhasOne->getStock->id))
                                                                                <a href="{{route('plus-cards.index')}}"></a>

                                                                                <a href="/plus-cards/{{$pluscard->id}}/edit?return_url={{url()->current()}}"
                                                                                   class="btn btn-sm btn-success add_balance_plus_cards">Bakiye
                                                                                    Yükle</a>
                                                                            @elseif( $pluscard->getTotalBalanceStock($newDB->getStockhasOne->getStock->id,$newDB->branch_id)['credits'] == 0 && $pluscard->getTotalBalanceStock($newDB->getStockhasOne->getStock->id,$newDB->branch_id)['points'] == 0)
                                                                                <a class="btn btn-sm btn-success add_balance_plus_cards"
                                                                                   href="{{route('plus-cards.edit',['plus_card'=>$pluscard->id,'return_url' => url()->current()])}}">Bakiye
                                                                                    Yükle</a>
                                                                            @else
                                                                                <input type="radio" name="plus_kart_id"
                                                                                       data-credits="{{$pluscard->getTotalBalanceStock($newDB->getStockhasOne->getStock->id,$newDB->branch_id)['credits']}}"
                                                                                       data-points="0"
                                                                                       @if(($newDB->getPayments->where('plus_card_id',$pluscard->id)->first())) checked @endif
                                                                                       value="{{$pluscard->id}}">
                                                                            @endif
                                                                        @endif



                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        @endif
                                                        </tbody>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 col-xl-12 col-xs-12 mt-3 col-sm-12">
                            <button class="btn btn-info mb-3 " type="submit" name="save_type" value="normal">
                                Kaydet
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    @if($isAdmin)
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="kuponBakiyelerHeading">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#kuponBakiyeler" aria-expanded="true"
                                        aria-controls="kuponBakiyeler">
                                    Kupon Bakiyeleri - Plus Card
                                </button>
                            </h2>
                            <div id="kuponBakiyeler" class="accordion-collapse collapse show"
                                 data-bs-parent="#accordionExample" aria-labelledby="kuponBakiyelerHeading">
                                <div class="accordion-body">
                                    <table class="table table-bordered">
                                        <thead>
                                        <tr>
                                            <th>No</th>
                                            <th>ID</th>
                                            <th>Kredi</th>
                                            <th>Puan</th>
                                        </tr>
                                        </thead>
                                        <tbody class="alici-plus-kartlar_2">
                                        @if(!empty($aliciCustomer->getPlusCards))
                                            @foreach($aliciCustomer->getPlusCards as $pluscard)
                                                <tr>
                                                    <td>{{$pluscard->no}}</td>
                                                    <td>{{$pluscard->system_id}}</td>
                                                    <td>{{!empty($newDB->getStockhasOne->getStock->id) ? $pluscard->getTotalBalanceStock($newDB->getStockhasOne->getStock->id,$newDB->branch_id)['credits'] : 0}}</td>
                                                    <td>{{!empty($newDB->getStockhasOne->getStock->id) ? $pluscard->getTotalBalanceStock($newDB->getStockhasOne->getStock->id,$newDB->branch_id)['points'] : 0}}</td>

                                                </tr>
                                            @endforeach
                                        @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="sonEkspertizRaporlariHeading">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#sonEkspertizRaporlari" aria-expanded="true"
                                        aria-controls="sonEkspertizRaporlari">
                                    Son Ekspertiz Raporları
                                </button>
                            </h2>
                            <div id="sonEkspertizRaporlari" class="accordion-collapse collapse show"
                                 data-bs-parent="#accordionExample" aria-labelledby="sonEkspertizRaporlariHeading">
                                <div class="accordion-body">
                                    <table class="table table-bordered">
                                        <thead>
                                        <tr>
                                            <th>Belge Tarihi</th>
                                            <th>Belge No</th>
                                            <th>Bayi</th>
                                            <th>Plaka</th>
                                            <th>Belge</th>
                                        </tr>
                                        </thead>
                                        <tbody class="son-ekspertiz-raporlari">
                                        @if($newDB->getCar)
                                            @foreach(getCarExpertises($newDB->getCar->id) as $carLastExpertise)
                                                <tr>
                                                    <td>{{ \Carbon\Carbon::make($carLastExpertise['belge_tarihi'])->format('d.m.Y H:i:s') }}</td>
                                                    <td>{{ $carLastExpertise['belge_no'] }}</td>
                                                    <td>{{ $carLastExpertise['sube'] }}</td>
                                                    <td>{{ $carLastExpertise['plaka'] }}</td>

                                                    {{-- Son veriye goprint sınıfını eklemek için kontrol ekleyelim --}}
                                                    @php
                                                        $goprintClass = ($loop->first) ? ' goprint' : '';
                                                        $goprintAtlass = ($loop->first) ? '?goprint=1' : '';
                                                    @endphp

                                                    <td><a class="btn btn-success{{ $goprintClass }}"
                                                           @if(!empty($carLastExpertise['uuid']))
                                                               uuid="{{$carLastExpertise['uuid']}}"
                                                           @endif
                                                           href="/ekspertiz-raporu/{{ $carLastExpertise['uuid'].$goprintAtlass }}"
                                                           target="_blank">Belge</a></td>
                                                </tr>
                                            @endforeach
                                        @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endif
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="belgeBilgileriHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#belgeBilgileri" aria-expanded="true"
                                    aria-controls="belgeBilgileri">
                                Belge Bilgileri
                            </button>
                        </h2>
                        <div id="belgeBilgileri" class="accordion-collapse collapse show"
                             data-bs-parent="#accordionExample" aria-labelledby="belgeBilgileriHeading">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Belge Tarihi
                                        </div>
                                        <div class="col-md-9">
                                            <input type="datetime-local" class="form-control form-control-sm"
                                                   name="belge_tarihi" @if(!$isAdmin) disabled @endif
                                                   value="{{ $newDB ? $newDB->belge_tarihi : now()->format('Y-m-d H:i:s') }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Belge No
                                        </div>
                                        <div class="col-md-9">
                                            <input type="text" class="form-control form-control-sm" name="belge_no" disabled

                                                   value="{{ $newDB ? $newDB->belge_no : createBelgeNo() }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Belge Özel Kodu
                                        </div>
                                        <div class="col-md-9">
                                            <select name="belge_ozel_kodu" @if(!$isAdmin) disabled @endif class="select2">
                                                <option value="1">Onaylı</option>
                                                <option
                                                    @if($newDB->belge_ozel_kodu == 0) selected
                                                    @endif value="0">Hayır
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Sigorta Teklifi Ver
                                        </div>
                                        <div class="col-md-9">
                                            <select name="sigorta_teklif_ver" @if(!$isAdmin) disabled @endif class="select2">
                                                <option value="1">Evet</option>
                                                <option
                                                    @if($newDB->sigorta_teklif_ver == 0) selected
                                                    @endif value="0">Hayır
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Yayın Yasağı
                                        </div>
                                        <div class="col-md-9">
                                            <select name="yayin_yasagi" @if(!$isAdmin) disabled @endif class="select2">
                                                <option value="0">Hayır</option>
                                                <option @if($newDB->yayin_yasagi == 1) selected @endif value="1">Evet</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Kayıt Bayi
                                        </div>
                                        <div class="col-md-9">

                                            <select name="kayit_branch_id" @if(!$isAdmin) disabled @endif class="select2">
                                                @foreach($branches as $branch)
                                                    <option
                                                        @if($newDB->kayit_branch_id == $branch->id) selected
                                                        @endif value="{{ $branch->id }}">{{ $branch->kisa_ad }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Servis Bayi
                                        </div>
                                        <div class="col-md-9">
                                            <select name="branch_id" @if(!$isAdmin) disabled @endif class="select2">
                                                @foreach($branches as $branch)
                                                    <option
                                                        @if($newDB->branch_id == $branch->id) selected
                                                        @endif value="{{ $branch->id }}">{{ $branch->kisa_ad }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Durum
                                        </div>
                                        <div class="col-md-9">
                                            <select name="status" class="select2">
                                                <option value="1">Aktif</option>
                                                <option value="0">Pasif</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Çıkış Tarihi
                                        </div>
                                        <div class="col-md-9">
                                            <input type="datetime-local" class="form-control form-control-sm"
                                                   name="cikis_tarihi" @if(!$isAdmin) disabled @endif
                                                   value="{{ $newDB ? $newDB->cikis_tarihi : '' }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @if($isAdmin)
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="cariHesapKrediBilgilerHeading">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#cariHesapKrediBilgiler" aria-expanded="true"
                                        aria-controls="cariHesapKrediBilgiler">
                                    Cari Hesap TL / Kredi Bilgileri
                                </button>
                            </h2>
                            <div id="cariHesapKrediBilgiler" class="accordion-collapse collapse show"
                                 data-bs-parent="#accordionExample" aria-labelledby="cariHesapKrediBilgilerHeading">
                                <div class="accordion-body">
                                    <div class="form-group">
                                        <div class="row align-items-center">
                                            <div class="col-md-3">
                                                <label>Toplam Borç</label>
                                            </div>
                                            <div class="col-md-9">
                                                <input disabled class="form-control form-control-sm" name="toplam_borc">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="row align-items-center">
                                            <div class="col-md-3">
                                                <label>Borç Bakiye</label>
                                            </div>
                                            <div class="col-md-9">
                                                <input disabled class="form-control form-control-sm" name="borc_bakiye">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="row align-items-center">
                                            <div class="col-md-3">
                                                <label>Toplam Alacak</label>
                                            </div>
                                            <div class="col-md-9">
                                                <input disabled class="form-control form-control-sm" name="toplam_alacak">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <div class="row align-items-center">
                                            <div class="col-md-3">
                                                <label>Alacak Bakiye</label>
                                            </div>
                                            <div class="col-md-9">
                                                <input disabled class="form-control form-control-sm" name="alacak_bakiye">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <input type="hidden" name="payment_type"
               value="{{ $newDB->payment_type }}">
        <input type="hidden" name="plus_card_sms_verified" value="0">
        <input type="hidden" name="plus_card_sms_telefon" value="">
        <input type="hidden" name="plus_card_sms_cep" value="">
        <input type="hidden" name="plus_card_sms_verified_type" value="">
        <input type="hidden" name="plus_card_sms_cep_code" value="{{ $plusCardSmsCodeCep }}">
        <input type="hidden" name="plus_card_sms_telefon_code" value="{{ $plusCardSmsCodeTelefon }}">
    </form>
    @if($isAdmin)
        <div class="modal fade" id="searchCustomerModal" tabindex="-1"
             aria-labelledby="searchCustomerModal" data-bs-keyboard="false"
             aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="staticBackdropLabel2">Bulunan Sonuçlar
                        </h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body" style="max-height: 300px;overflow: auto; padding-top: 0px;">
                        <table class="table table-striped table-responsive">
                            <thead style="position: sticky;top: 0">
                            <tr>
                                <th>Hesap Kodu</th>
                                <th>Ad Soyad</th>
                                <th>Bayi</th>
                                <th>Telefon</th>
                                <th>T.C/Vergi No</th>
                                <th>Plus Card</th>
                            </tr>
                            </thead>
                            <tbody class="filter-results-new ruhsat-sahibi">

                            </tbody>
                            <tbody class="filter-results-new satici">

                            </tbody>
                            <tbody class="filter-results-new alici">

                            </tbody>
                        </table>
                        <table id="header-fixed"></table>
                    </div>
                    <div class="modal-footer cari">

                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="searchCarModal" tabindex="-1"
             aria-labelledby="searchCarModal" data-bs-keyboard="false"
             aria-hidden="true">
            <div class="modal-dialog modal-lg modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="staticBackdropLabel2">Bulunan Sonuçlar
                        </h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body" style="max-height: 300px;overflow: auto">
                        <table class="table table-striped table-responsive">
                            <thead>
                            <tr>
                                <th>Plaka No</th>
                                <th>Şase No</th>
                                <th>Araç Tipi</th>
                                <th>Marka</th>
                                <th>Model</th>
                                <th>Kasa Tipi</th>
                                <th>Yakıt Tipi</th>
                                <th>Vites Tipi</th>
                                <th>Model Yılı</th>
                            </tr>
                            </thead>
                            <tbody class="find-car-new">

                            </tbody>
                        </table>
                    </div>
                    <div class="modal-footer arc">

                    </div>
                </div>
            </div>
        </div>
    @endif


    <div class="modal fade" id="paymentModal" data-bs-backdrop="static" tabindex="-1"
         aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <form action="{{ url('paynet') }}" id="paynetForm" method="post">
                @csrf
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="myModalLabel">Ödeme AL</h3>
                    </div>
                    <div class="modal-body">
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal fade " id="plusCardSmsVerification" tabindex="-1"
         aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" style="margin-top: -7rem;margin-right: 8rem">

            <div class="modal-content" style="    border: 1px solid #06e327;">
                <div class="modal-header" style="background: #06e328;">
                    <h5 id="myModalLabel" style="color: #fff">Plus Card Sms Doğrula</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p style="font-size: .8rem;line-break: anywhere">Sms Gönderilen  : <br> <b class="plus-card-sms-customer-name">{{ isset($aliciCustomer) ? $aliciCustomer->fullName : ''  }}</b>
{{--                        |  --}}
                        <b class="plus-card-sms-customer-telephone">{{ isset($aliciCustomer) ? maskPhoneNumber($aliciCustomer->cep) . ' ' . maskPhoneNumber($aliciCustomer->telefon) : ''  }}</b>
                    </p>
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-sm btn-success plus-card-sms-verification-send-button">SMS Gönder
                        </button>
                        <input class="form-control-sm form-control" form="mainForm" style="width: 50%" placeholder="Doğrulama Kodu" name="plus_card_sms">
                        <button type="button" class="btn btn-sm btn-danger plus-card-sms-verification-button">Doğrula
                        </button>
                    </div>
                </div>
                <div class="modal-footer">

                </div>
            </div>
        </div>
    </div>
    <input type="hidden" id="create_timestamp" value="{{strtotime(date('Y-m-d H:i:s'))}}">
@endsection
@push('js')
    <style type="text/css">
        #header-fixed {
            position: fixed;
            top: 0px;
            display: none;
            background-color: white;
        }
    </style>
    <script src="/assets/js/jquery_3.6.1.min.js"></script>

    <!-- Select2 Cdn -->
    <script src="/assets/js/select2.min.js"></script>
    <script>

        var selectedStocks = [];
        var allTotal = 0;

        var currentTime = new Date();
        var unixTimestamp = Math.floor(currentTime.getTime() / 1000);


        var create_timestamp = '{{strtotime(date('Y-m-d H:i:s'))}}';
        console.log(unixTimestamp, create_timestamp)
        if ((unixTimestamp - create_timestamp) > 100) {
            location.reload();
        }

        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        $(document).ready(function () {
            $('.check-contract').on('click',function (){
                let $no = $('input[name="contract_no"]').val();
                let $code = $('input[name="contract_code"]').val();
                let $carID = $('input[name="car_id"]').val();
                let $paymentID = '{{ $newDB->getPayment->id ?? '' }}';
                $('input[name="contract_status"]').val(0)
                if ($no == ''){
                    Toast.fire({
                        icon: "error",
                        title: "Sözleşme Numarası Girmediniz!"
                    });
                    return false;
                }
                if ($code == ''){
                    Toast.fire({
                        icon: "error",
                        title: "Özel Kodu Girmediniz!"
                    });
                    return false;
                }
                $.ajax({
                    url: "{{ route('checkContract') }}",
                    type: "post",
                    data: {
                        _token:'{{ csrf_token() }}',
                        'no':$no,
                        'code':$code,
                        'carID':$carID,
                        'paymentID':$paymentID,
                    },
                    success: function (response) {
                        if (response.success == 'true'){
                            $('input[name="contract_status"]').val(1)
                            Toast.fire({
                                icon: "success",
                                title: response.message
                            });
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: response.message
                            });
                        }
                    }
                });
            })

            $('.deleteOdeme').click(function(){
                var payment_id = $(this).attr('data-id');
                Swal.fire({
                    title: "Emin misiniz?",
                    text: "Ödeme Silinecek",
                    icon: "error",
                    showCancelButton: true,
                    confirmButtonColor: "#d33",
                    cancelButtonColor: "#3085d6",
                    confirmButtonText: "Evet, Sil!",
                    cancelButtonText: "Hayır, Silme!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('deleteExpertisePayment') }}",
                            type: "post",
                            data: {
                                _token:"{{ @csrf_token() }}",
                                payment_id:payment_id,
                            },
                            success: function (response) {
                                if(response.success){
                                    if (typeof Toast === 'undefined') {
                                        var Toast = Swal.mixin({
                                            toast: true,
                                            position: "top-end",
                                            showConfirmButton: false,
                                            timer: 3000,
                                            timerProgressBar: true,
                                            didOpen: (toast) => {
                                                toast.onmouseenter = Swal.stopTimer;
                                                toast.onmouseleave = Swal.resumeTimer;
                                            }
                                        });
                                    }
                                    $('.odeme_delete_column_'+payment_id).remove();
                                    setSummary();
                                    Toast.fire({
                                        icon: "success",
                                        title: "Ödeme Silindi"
                                    });
                                }else{
                                    Toast.fire({
                                        icon: "error",
                                        title: "Silme İşlemi Gerçekleşmedi"
                                    });
                                }

                            },
                            error: function(jqXHR, textStatus, errorThrown) {
                                console.log(textStatus, errorThrown);
                            }
                        });
                    }
                });
            });

            setSummary()
            if ($('input[name="odeme_tutar[]"]').length > 0) {
                var total_payment = 0
                $.each($('input[name="odeme_tutar[]"]'), function (index, item) {
                    total_payment += parseFloat($(item).val().replace(/[^0-9,-]+/g, "").replace(',', '.'))
                });
                $('.payment-total span').html(total_payment.toLocaleString('tr-TR', {
                    style: 'currency',
                    currency: 'TRY'
                }))

                setTimeout(function (){
                    $('.payment-left span').html((allTotal - total_payment).toLocaleString('tr-TR', {
                        style: 'currency',
                        currency: 'TRY'
                    }))
                },1000)


            }
            $('.odeme_tutar_input').change('on', function () {
                var total_payment = 0
                $.each($('input[name="odeme_tutar[]"]'), function (index, item) {
                    total_payment += parseFloat($(item).val().replace(/[^0-9,-]+/g, "").replace(',', '.'))
                });
                $('.payment-total span').html(total_payment.toLocaleString('tr-TR', {
                    style: 'currency',
                    currency: 'TRY'
                }))

                $('.payment-left span').html((allTotal - total_payment).toLocaleString('tr-TR', {
                    style: 'currency',
                    currency: 'TRY'
                }))
            })
            $('.odeme_tutar_input').blur(function () {
                $.each($('input[name="odeme_tutar[]"]'), function (index, item) {
                    var value = $(item).val();
                    if (value !== '' && /^\d+$/.test(value)) {
                        value = parseFloat(value.replace(/[^0-9,-]+/g, "").replace(',', '.'))
                        $(item).val(value.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}));
                    }
                })
            });


            $('.search-ruhsat-sahibi').keypress(function (event) {
                if (event.keyCode == 13) {
                    $('.search-ruhsat-sahibi-button').trigger('click');
                    return false;
                }
            });

            $('.change_info_car').click(function (e) {
                e.preventDefault()
                var href = $(this).attr('href');
                href += "&plaka_copy=" + $('input[name=car_id]').val() + "&copy=1";
                saveAndGo(href)
            })
            $('.add_balance_plus_cards').click(function () {
                e.preventDefault()
                var href = $(this).attr('href');
                saveAndGo(href)
            })
            @foreach($newDB->getStocks as $expertiseStock)
                selectedStocks = [];
            selectedStocks.push({
                'id': '{{$expertiseStock->getStock->id}}',
                'ad': '{{$expertiseStock->getStock->ad}}',
                'kdv': {{$expertiseStock->getStock->kdv}},
                'iskonto_amount': {{$expertiseStock->iskonto_amount ?? 0}},
                'kdvHaricFiyat': {{ (($getContractCode ? $newDB->getPayment->amount : $expertiseStock->liste_fiyati) / (1 + $expertiseStock->getStock->kdv / 100)) ?? 0}},
            });
            $('.iskonto_amount_cont').html({{$expertiseStock->iskonto_amount}} + ' ₺')
            setSummary()
            @endforeach

            var creditstd = $('.creditstd')
            var pointstd = $('.pointstd')
            var credits = 0
            var points = 0
            creditstd.each(function (index, element) {
                credits = credits + parseInt($(element).text());
            });
            pointstd.each(function (index, element) {
                points = points + parseInt($(element).text());
            });
            if (credits == 0) {
                $('#plus_kredi').attr('disabled', '')
            }
            if (points == 0) {
                $('#plus_puan').attr('disabled', '')
            }
        });

        (function () {
            $(document).on("keyup", ".iskonto_amount_hizmet", function () {
                if (this.value.match(/[^0-9]/g)) {
                    this.value = this.value.replace(/[^0-9]/g, '');
                }
            });
            $('.iskonto_amount_hizmet').change('on', function () {
                var value = $(this).val();
                var max = $(this).attr('max')
                var min = $(this).attr('min')

                if (parseInt(value) > parseInt(max)) {
                    value = max
                    $(this).val(parseInt(max));

                    Toast.fire({
                        icon: "error",
                        title: "İskonto Tutarını Fazla Girdiniz!"
                    });
                }

                if (parseInt(value) < parseInt(min)) {
                    value = min
                    $(this).val(parseInt(min));
                    Toast.fire({
                        icon: "error",
                        title: "İskonto Tutarını Az Girdiniz!"
                    });
                }

                selectedStocks[0].iskonto_amount = value

                setSummary()
                $('.iskonto_amount_cont').html(value.toLocaleString('tr-TR', {
                    style: 'currency',
                    currency: 'TRY'
                }) + ' ₺')

            })
            $('.campaing_select').change('on', function () {
                var value = $(this).val();
                var kdvHaricFiyat = $(this).find('option:selected').data('kdvharicfiyat');
                var fiyat = $(this).find('option:selected').data('fiyat');
                var max_iskonto = $(this).find('option:selected').data('maxiskonto');
                selectedStocks[0].kdvHaricFiyat = kdvHaricFiyat;
                $('.service_price').val(fiyat)
                $('.list_price').val(fiyat)

                $('.iskonto_amount_hizmet').attr('max', max_iskonto)
                var iskonto_value = $('.iskonto_amount_hizmet').val();
                var max = $('.iskonto_amount_hizmet').attr('max')
                if (parseInt(iskonto_value) > parseInt(max)) {
                    iskonto_value = max
                    $('.iskonto_amount_hizmet').val(parseInt(max));
                }
                selectedStocks[0].iskonto_amount = parseInt(iskonto_value)
                $('.iskonto_amount_cont').html(iskonto_value.toLocaleString('tr-TR', {
                    style: 'currency',
                    currency: 'TRY'
                }) + ' ₺')
                setSummary()
            })
            setSummary()
            if (typeof Toast === 'undefined') {
                var Toast = Swal.mixin({
                    toast: true,
                    position: "top-end",
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.onmouseenter = Swal.stopTimer;
                        toast.onmouseleave = Swal.resumeTimer;
                    }
                });
            }

            "use strict"
            $(".select2").select2({
                placeholder: "Ara.."
            });

            $('.add-payment').on('click', function () {
                var now = new Date();
                var timestamp = now.getTime();
                let html = '<div class="row odeme_tr_' + timestamp + '"><div class="col-md-3 ">'
                html += '<div class="form-group">'
                html += '<label>Ödeme Türü</label>'
                html += '<select name="odeme_tip[]" required data-timestamp="' + timestamp + '" id="odeme_tip_' + timestamp + '" class="form-control-sm form-control odeme_tip_select">'
                html += '<option value="">Lütfen Seçim Yapınız</option>'
                html += '<option value="kredi_karti">Kredi Kartı</option>'
                html += '<option value="nakit">Nakit</option>'
                html += '<option value="banka">Banka</option>'
                html += '<option value="acik_hesap">Açık Hesap</option>'
                html += '</select>'
                html += '</div>'
                html += '</div>'
                html += '<div class="col-md-3">'
                html += '<div class="form-group">'
                html += '<label>Hesap</label>'
                html += '<select name="odeme_hesap[]" id="odeme_hesap_' + timestamp + '" class="form-control-sm form-control">'
                html += '<option value="0">Seçilmedi</option>'
                html += ' </select>'
                html += '</div>'
                html += '</div>'
                html += '<div class="col-md-3">'
                html += '<div class="form-group">'
                html += '<label>Tutar</label>'
                html += '<input type="text" name="odeme_tutar[]" id="odeme_tutar_' + timestamp + '"  class="form-control form-control-sm odeme_tutar_input">'
                html += '</div>'
                html += '</div>'
                html += '<div class="col-md-3">'
                html += '<div class="form-group text-center">'
                html += '<button type="button" data-timestamp="' + timestamp + '" class="btn btn-danger btn-sm deleteOdemeColumn mt-4" style="margin-right: 5px;"><i class="fa fa-trash"></i></button><button type="button" data-id="' + timestamp + '" class="btn mt-4 start_pay_' + timestamp + ' btn-sm startPay btn-success" style="display:none;">Ödeme</button>'
                html += '</div>'
                html += '</div></div>'
                $('.payments').append(html)
                setTimeout(function () {
                    $('select[data-timestamp="' + timestamp + '"]').trigger('change')
                }, 500)

                $(".select2").select2({
                    placeholder: "Ara.."
                });

                $('.odeme_tip_select').change(function () {
                    var value = $(this).val();
                    var timestamp = $(this).attr('data-timestamp');
                    if (value == "kredi_karti") {
                        $('.start_pay_' + timestamp).show();
                    } else {
                        $('.start_pay_' + timestamp).hide();
                    }
                })
                $('.deleteOdemeColumn').click(function () {
                    var timestamp = $(this).attr('data-timestamp');
                    $('.odeme_tr_' + timestamp).remove();
                    var total_payment = 0
                    $.each($('input[name="odeme_tutar[]"]'), function (index, item) {
                        total_payment += parseFloat($(item).val().replace(/[^0-9,-]+/g, "").replace(',', '.'))
                    });
                    $('.payment-total span').html(total_payment.toLocaleString('tr-TR', {
                        style: 'currency',
                        currency: 'TRY'
                    }))
                })
                $('.odeme_tutar_input').blur(function () {
                    $.each($('input[name="odeme_tutar[]"]'), function (index, item) {
                        var value = $(item).val();
                        if (value !== '' && /^\d+$/.test(value)) {
                            value = parseFloat(value.replace(/[^0-9,-]+/g, "").replace(',', '.'))
                            $(item).val(value.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}));
                        }
                    })
                });

                $(document).on("click", ".startPay", function () {
                    var id = $(this).attr("data-id");
                    var type = $("#odeme_tip_" + id).val();
                    if (type == "kredi_karti") {
                        var amount = parseFloat($("#odeme_tutar_" + id).val().replace(/[^0-9,-]+/g, "").replace(',', '.'))
                        var uuid = $('input[name="uuid"]').val();
                        var route = "{{ url("startpay") }}";
                        var url = route + "/" + id + "?amount=" + amount + "&uuid=" + uuid;
                        $.get(url, function (data) {
                            $("#paymentModal .modal-body").html(data);
                            $("#paymentModal").modal("show");
                            $('#paymentSubmit').html(amount+"₺ Ödeme Al")
                        });
                    } else {
                        Toast.fire({
                            icon: "error",
                            title: "Sadece Kredi Kartı İle Ödeme Alınabilir!"
                        });
                        return false;
                    }
                });

                $(document).on("click", "#paymentSubmit", function () {
                    var data = $('#paynetForm').serializeArray();
                    var url = $("#paynetForm").attr("action");
                    $.post(url, data).then(res => {
                        if (res.status == "ok") {
                            Toast.fire({
                                icon: "success",
                                title: res.message
                            });
                        } else if (res.status == "3d") {
                            var iframe = $('<iframe>', {
                                src: res.url,
                                frameborder: 0,
                                scrolling: 'auto',
                                width: '100%',
                                height: '420px'
                            });
                            // İframe'i sayfaya ekle
                            $("#paymentModal .modal-body").html("").append(iframe);
                            $("#paymentModal").modal("show");

                            iframe.on('load', function () {
                                var iframeUrl = $(this).contents().get(0).location.href;
                                if (iframeUrl == "{{ url('/login') }}") {
                                    $("#paymentModal .modal-body").html("");
                                    $("#paymentModal").modal("hide");
                                    var url2 = "{{ url('paynetStatus') }}";
                                    const data2 = {
                                        "payment_session_id": res.payment_session_id,
                                        "payment_token_id": res.payment_token_id
                                    };
                                    $.post(url2, data2).then(res2 => {
                                        if (res2.status == "ok") {
                                            Toast.fire({
                                                icon: "success",
                                                title: res2.message
                                            });
                                        } else {
                                            Toast.fire({
                                                icon: "error",
                                                title: res2.message
                                            });
                                        }
                                    });
                                }

                            });
                            return false;
                            //$(location).attr('href', res.url)
                        } else {
                            Toast.fire({
                                icon: "error",
                                title: res.message
                            });
                        }
                    });
                    return false;
                });

                $('.odeme_tutar_input').change('on', function () {
                    var total_payment = 0
                    $.each($('input[name="odeme_tutar[]"]'), function (index, item) {
                        total_payment += parseFloat($(item).val().replace(/[^0-9,-]+/g, "").replace(',', '.'))
                    });
                    $('.payment-total span').html(total_payment.toLocaleString('tr-TR', {
                        style: 'currency',
                        currency: 'TRY'
                    }))
                })

                $('.odeme_tutar_input').on('input', function () {
                    var total_payment = 0;

                    $.each($('input[name="odeme_tutar[]"]'), function (index, item) {
                        var value = $(item).val();

                        if (value !== '' && !/^\d+(\.?\d{0,2})?$/.test(value)) {
                            var numericValue = parseFloat(value.replace(/[^0-9,-]+/g, "").replace(',', '.'));
                            if (!isNaN(numericValue)) {
                                var formattedValue = numericValue.toLocaleString('tr-TR', {
                                    style: 'currency',
                                    currency: 'TRY'
                                });
                                value = formattedValue;
                                $(item).val(formattedValue);
                            }
                        }
                        total_payment += parseFloat(value.replace(/[^0-9,-]+/g, "").replace(',', '.'));
                    });

                    var formattedTotalPayment = total_payment.toLocaleString('tr-TR', {
                        style: 'currency',
                        currency: 'TRY'
                    });
                    $('.payment-total span').html(formattedTotalPayment);
                });
            })

            document.addEventListener("DOMContentLoaded", (event) => {

                let selectedStocksHtml = ''
                @foreach($newDB->getStocks as $expertiseStock)
                selectedStocks.push({
                    'id': '{{ $expertiseStock->stock_id }}',
                    'ad': '{{ $expertiseStock->getStock->ad }}',
                    'kdv': {{ $expertiseStock->getStock->kdv ?? 0 }},
                    'iskonto_amount': 0,
                    'kdvHaricFiyat': {{ (($expertiseStock->liste_fiyati) / (1 + $expertiseStock->getStock->kdv / 100)) }},
                })
                $('.selectedStocks').html('')

                selectedStocksHtml += '<tr>'
                selectedStocksHtml += '<td>{{ $expertiseStock->getStock->ad }}</td>'
                {{--selectedStocksHtml += '<td>%{{ $expertiseStock->getStock->kdv ?? 0 }}</td>'--}}
                    selectedStocksHtml += '<td>{{ ($expertiseStock->liste_fiyati / (1 + $expertiseStock->getStock->kdv / 100)) ?? 0 }}₺</td>'
                selectedStocksHtml += '</tr>'

                @endforeach
                $('.selectedStocks').html(selectedStocksHtml)
                setSummary()
                $('.search-ruhsat-sahibi-button').on('click', function () {
                    let $val = $('.search-ruhsat-sahibi').val();
                    $('.filter-results-new.ruhsat-sahibi').html('')
                    $('.filter-results-new.satici').html('')
                    $('.filter-results-new.alici').html('')
                    if ($val.length > 2) {
                        $.ajax({
                            url: "{{ route('api.getCustomers') }}",
                            type: "post",
                            data: {
                                '_token': '{{ csrf_token() }}',
                                'search': $val,
                                'type': $('input[name="ruhsat_search_type"]:checked').val()
                            },
                            success: function (response) {
                                $('.find-ruhsat-sahibi').css('display', 'block')
                                if (response.items.length > 0) {
                                    let html = ''
                                    $.each(response.items, function (index, item) {
                                        let tcVergiNo = item.vergi_no || item.tc_no;
                                        let cep = item.cep;
                                        let mask;
                                        let mask2;
                                        if (tcVergiNo) {
                                            let first4 = tcVergiNo.substring(0, 4);
                                            let tcVergi = tcVergiNo.substring(4, tcVergiNo.length - 4).replace(/\d/g, "*");
                                            mask = first4 + tcVergi
                                        }
                                        if (cep) {
                                            let first4x = cep.substring(0, 2);
                                            let last5x = cep.substring(cep.length - 4);

                                            let stars = cep.substring(0, cep.length - 4).replace(/\d/g, "*");
                                            mask2 = stars + last5x
                                        }

                                        html += '<tr class="filter-item" ' +
                                            'data-id="' + item.id + '" ' +
                                            'data-branchId="' + item.branch_id + '" ' +
                                            'data-unvan="' + item.unvan + '" ' +
                                            'data-type="' + item.type + '" ' +
                                            'data-telefon="' + (item.telefon ? item.telefon : item.cep) + '" ' +
                                            'data-kod="' + item.cari_kod + '" ' +
                                            'data-tc="' + item.tc + '">'
                                        html += '<td>' + (item.cari_kod || '') + '</td>'
                                        html += '<td>' + item.unvan + '</td>'
                                        html += '<td>' + item.branch_name + '</td>'
                                        html += '<td>' + (mask2 || '') + '</td>'
                                        html += '<td>' + (mask || '') + '</td>'
                                        html += '<td>' + (item.plusCardNo || '') + '</td>'
                                        html += '</tr>'
                                    })
                                    $('.filter-results-new.ruhsat-sahibi').html(html)

                                    let addCustomerHtml = "<a style=\'font-size:.9rem\' class=\"btn btn-sm btn-danger open-add-customer-ruhsat-page\" href=\"{{ route('customers.create',['customer'=>'ruhsat','return_url'=>url()->current(),'type'=>'bireysel']) }}&unvan=" + $('.search-ruhsat-sahibi').val() + "\">Yeni Bireysel Kayıt</a><br>"
                                    addCustomerHtml += "<a style=\'font-size:.9rem\' class=\"btn btn-sm btn-danger open-add-customer-ruhsat-page\" href=\"{{ route('customers.create',['customer'=>'ruhsat','return_url'=>url()->current(),'type'=>'kurumsal']) }}&unvan=" + $('.search-ruhsat-sahibi').val() + "\">Yeni Kurumsal Kayıt</a>"
                                    $('.open-add-customer-ruhsat-page').on('click', function (e) {
                                        e.preventDefault()
                                        saveAndGo(e.target.href)
                                    })
                                    $('.modal-footer.cari').html(addCustomerHtml)
                                    $('#searchCustomerModal').modal('toggle')

                                    $('.filter-results-new.ruhsat-sahibi .filter-item').on('click', function () {
                                        let redirect = true;
                                        if ($(this).data('branchid') != 1)
                                            redirect = false;
                                        if($(this).data('branchid') != {{ auth()->user()->branch_id }})
                                            redirect = false;
                                        if (redirect === true){
                                            window.location.href = "{{ route('customers.create') }}?unvan="+$(this).data('unvan')+"&value="+$(this).data('telefon')+"tc="+$(this).data('tc')+"&type="+$(this).data('type');
                                        }

                                        $('input[name=cari_kod]').val($(this).data('kod'))
                                        $('input[name=cari_unvan]').val($(this).data('unvan'))
                                        $('input[name=cari_id]').val($(this).data('id'))
                                        $('.ruhsat-sahibi-buton').html("Ruhsat Sahibi - (" + $(this).data('unvan') + " - " + $(this).data('telefon') + ")").css('background-color','#06e328')
                                        $('.search-ruhsat-sahibi').removeAttr('required')
                                        if ($('input[name="satici_ayni"]')[0].checked) {
                                            $('input[name=satici_kod]').val($(this).data('kod'))
                                            $('input[name=satici_unvan]').val($(this).data('unvan'))
                                            $('input[name=satici_id]').val($(this).data('id'))
                                            $('.satici-buton').html("Satıcı Bilgileri - (" + $(this).data('unvan') + " - " + $(this).data('telefon') + ")").css('background-color','#06e328')
                                            $('.search-satici').removeAttr('required')
                                        }
                                        if ($('input[name="alici_ayni"]')[0].checked) {
                                            $('input[name=alici_kod]').val($(this).data('kod'))
                                            $('input[name=alici_unvan]').val($(this).data('unvan'))
                                            $('input[name=alici_id]').val($(this).data('id'))
                                            $('.alici-buton').html("Alıcı Bilgileri - (" + $(this).data('unvan') + " - " + $(this).data('telefon') + ")").css('background-color','#06e328')
                                            $('.search-alici').removeAttr('required')
                                        }
                                        $('.find-ruhsat-sahibi').css('display', 'none')
                                        $('.search-ruhsat-sahibi').val('')


                                        if ($('input[name=alici_ayni]').is(':checked')) {
                                            getPlusCard()
                                        }

                                        $('#searchCustomerModal').modal('toggle')
                                    })
                                } else {
                                    let addCustomerHtml = 'Hiçbir Kayıt Bulunamadı!'
                                    @if(in_array('*', $userRoles) || in_array('add_customer', $userRoles))
                                        addCustomerHtml += "<br><a style=\'font-size:.9rem\' class=\"text-danger open-add-customer-ruhsat-page\" target=\"_blank\" href=\"{{ route('customers.create',['customer'=>'ruhsat','return_url'=>url()->current(),'type'=>'bireysel']) }}&unvan=" + $('.search-ruhsat-sahibi').val() + "\">Yeni Bireysel Kayıt</a><br>"
                                    addCustomerHtml += "<a style=\'font-size:.9rem\' class=\"text-danger open-add-customer-ruhsat-page\" target=\"_blank\" href=\"{{ route('customers.create',['customer'=>'ruhsat','return_url'=>url()->current(),'type'=>'kurumsal']) }}&unvan=" + $('.search-ruhsat-sahibi').val() + "\">Yeni Kurumsal Kayıt</a>"
                                    @endif
                                    $('.find-ruhsat-sahibi').html(addCustomerHtml)
                                    $('.open-add-customer-ruhsat-page').on('click', function (e) {
                                        e.preventDefault()
                                        saveAndGo(e.target.href)
                                    })
                                }
                            }
                        });
                    } else {
                        $('.find-ruhsat-sahibi').css('display', 'none')
                    }
                })

                $('.search-satici-button').on('click', function () {
                    let $val = $('.search-satici').val();
                    $('.filter-results-new.ruhsat-sahibi').html('')
                    $('.filter-results-new.satici').html('')
                    $('.filter-results-new.alici').html('')
                    if ($val.length > 2) {
                        $.ajax({
                            url: "{{ route('api.getCustomers') }}",
                            type: "post",
                            data: {
                                '_token': '{{ csrf_token() }}',
                                'search': $val,
                                'type': $('input[name="satici_search_type"]:checked').val()
                            },
                            success: function (response) {
                                $('.find-satici').css('display', 'block')
                                if (response.items.length > 0) {
                                    let html = ''
                                    $.each(response.items, function (index, item) {
                                        let tcVergiNo = item.vergi_no || item.tc_no;
                                        let cep = item.cep;
                                        let mask;
                                        let mask2;
                                        if (tcVergiNo) {
                                            let first4 = tcVergiNo.substring(0, 4);
                                            let tcVergi = tcVergiNo.substring(4, tcVergiNo.length - 4).replace(/\d/g, "*");
                                            mask = first4 + tcVergi
                                        }
                                        if (cep) {
                                            let last5x = cep.substring(cep.length - 4);

                                            let stars = cep.substring(0, cep.length - 4).replace(/\d/g, "*");
                                            mask2 = stars + last5x
                                        }

                                        html += '<tr class="filter-item" ' +
                                            'data-id="' + item.id + '" ' +
                                            'data-branchId="' + item.branch_id + '" ' +
                                            'data-unvan="' + item.unvan + '" ' +
                                            'data-type="' + item.type + '" ' +
                                            'data-telefon="' + (item.telefon ? item.telefon : item.cep) + '" ' +
                                            'data-kod="' + item.cari_kod + '" ' +
                                            'data-tc="' + item.tc + '">'
                                        html += '<td>' + (item.cari_kod || '') + '</td>'
                                        html += '<td>' + item.unvan + '</td>'
                                        html += '<td>' + item.branch_name + '</td>'
                                        html += '<td>' + (mask2 || '') + '</td>'
                                        html += '<td>' + (mask || '') + '</td>'
                                        html += '<td>' + (item.plusCardNo || '') + '</td>'
                                        html += '</tr>'
                                    })
                                    $('.filter-results-new.satici').html(html)

                                    @if(in_array('*', $userRoles) || in_array('add_customer', $userRoles))
                                    let addCustomerHtml = "<br><a style=\'font-size:.9rem\' class=\"text-danger open-add-customer-satici-page\" target=\"_blank\" href=\"{{ route('customers.create',['customer'=>'satici','return_url'=>url()->current(),'type'=>'bireysel','muhasebe_type'=>'satici']) }}&unvan=" + $('.search-satici').val() + "\">Yeni Bireysel Kayıt</a><br>"
                                    addCustomerHtml += "<a style=\'font-size:.9rem\' class=\"text-danger open-add-customer-satici-page\" target=\"_blank\" href=\"{{ route('customers.create',['customer'=>'satici','return_url'=>url()->current(),'type'=>'kurumsal','muhasebe_type'=>'satici']) }}&unvan=" + $('.search-satici').val() + "\">Yeni Kurumsal Kayıt</a>"
                                    @endif
                                    $('.open-add-customer-satici-page').on('click', function (e) {
                                        e.preventDefault()
                                        saveAndGo(e.target.href)
                                    })
                                    $('.modal-footer.cari').html(addCustomerHtml)

                                    $('#searchCustomerModal').modal('toggle')
                                    $('.filter-results-new.satici .filter-item').on('click', function () {
                                        let redirect = true;
                                        if ($(this).data('branchid') != 1)
                                            redirect = false;
                                        if($(this).data('branchid') != {{ auth()->user()->branch_id }})
                                            redirect = false;
                                        if (redirect === true){
                                            window.location.href = "{{ route('customers.create') }}?unvan="+$(this).data('unvan')+"&value="+$(this).data('telefon')+"tc="+$(this).data('tc')+"&type="+$(this).data('type');
                                        }

                                        $('input[name=satici_kod]').val($(this).data('kod'))
                                        $('input[name=satici_unvan]').val($(this).data('unvan'))
                                        $('input[name=satici_id]').val($(this).data('id'))
                                        $('.find-satici').css('display', 'none')
                                        $('.search-satici').val('')
                                        $('.satici-buton').html("Satıcı Bilgileri - (" + $(this).data('unvan') + " - " + $(this).data('telefon') + ")").css('background-color','#06e328')
                                        $('.search-satici').removeAttr('required')
                                        $('#searchCustomerModal').modal('toggle')
                                    })
                                } else {
                                    let addCustomerHtml = 'Hiçbir Kayıt Bulunamadı!'
                                    @if(in_array('*', $userRoles) || in_array('add_customer', $userRoles))
                                        addCustomerHtml += "<br><a style=\'font-size:.9rem\' class=\"text-danger open-add-customer-satici-page\" href=\"{{ route('customers.create',['customer'=>'satici','return_url'=>url()->current(),'type'=>'bireysel','muhasebe_type'=>'satici']) }}&unvan=" + $('.search-satici').val() + "\">Yeni Bireysel Kayıt</a><br>"
                                    addCustomerHtml += "<a style=\'font-size:.9rem\' class=\"text-danger open-add-customer-satici-page\" href=\"{{ route('customers.create',['customer'=>'satici','return_url'=>url()->current(),'type'=>'kurumsal','muhasebe_type'=>'satici']) }}&unvan=" + $('.search-satici').val() + "\">Yeni Kurumsal Kayıt</a>"
                                    @endif
                                    $('.find-satici').html(addCustomerHtml)

                                    $('.open-add-customer-satici-page').on('click', function (e) {
                                        e.preventDefault()
                                        saveAndGo(e.target.href)
                                    })
                                }
                            }
                        });
                    } else {
                        $('.find-satici').css('display', 'none')
                    }
                })

                $('.search-alici-button').on('click', function () {
                    let $val = $('.search-alici').val();
                    $('.filter-results-new.ruhsat-sahibi').html('')
                    $('.filter-results-new.satici').html('')
                    $('.filter-results-new.alici').html('')
                    if ($val.length > 2) {
                        $.ajax({
                            url: "{{ route('api.getCustomers') }}",
                            type: "post",
                            data: {
                                '_token': '{{ csrf_token() }}',
                                'search': $val,
                                'type': $('input[name="alici_search_type"]:checked').val()
                            },
                            success: function (response) {
                                $('.find-alici').css('display', 'block')
                                if (response.items.length > 0) {
                                    let html = ''
                                    $.each(response.items, function (index, item) {
                                        html += '<tr class="filter-item" ' +
                                            'data-id="' + item.id + '" ' +
                                            'data-branchId="' + item.branch_id + '" ' +
                                            'data-unvan="' + item.unvan + '" ' +
                                            'data-type="' + item.type + '" ' +
                                            'data-telefon="' + (item.telefon ? item.telefon : item.cep) + '" ' +
                                            'data-kod="' + item.cari_kod + '" ' +
                                            'data-tc="' + item.tc + '">'
                                        html += '<td>' + (item.cari_kod || '') + '</td>'
                                        html += '<td>' + item.unvan + '</td>'
                                        html += '<td>' + item.branch_name + '</td>'
                                        html += '<td>' + (item.maskedPhone || '') + '</td>'
                                        html += '<td>' + (item.maskedTcVergiNo || '') + '</td>'
                                        html += '<td>' + (item.plusCardNo || '') + '</td>'
                                        html += '</tr>'
                                    })
                                    $('.filter-results-new.alici').html(html)
                                    $('#searchCustomerModal').modal('toggle')

                                    @if(in_array('*', $userRoles) || in_array('add_customer', $userRoles))
                                    let addCustomerHtml = "<br><a style=\'font-size:.9rem\' class=\"text-danger open-add-customer-alici-page\" target=\"_blank\" href=\"{{ route('customers.create',['customer'=>'alici','return_url'=>url()->current(),'type'=>'bireysel','muhasebe_type'=>'alici']) }}&unvan=" + $('.search-alici').val() + "\">Yeni Bireysel Kayıt</a><br>"
                                    addCustomerHtml += "<a style=\'font-size:.9rem\' class=\"text-danger open-add-customer-alici-page\" target=\"_blank\" href=\"{{ route('customers.create',['customer'=>'alici','return_url'=>url()->current(),'type'=>'kurumsal','muhasebe_type'=>'alici']) }}&unvan=" + $('.search-alici').val() + "\">Yeni Kurumsal Kayıt</a>"
                                    @endif
                                    $('.open-add-customer-alici-page').on('click', function (e) {
                                        e.preventDefault()
                                        saveAndGo(e.target.href)
                                    })
                                    $('.modal-footer.cari').html(addCustomerHtml)

                                    $('.filter-results-new.alici .filter-item').on('click', function () {
                                        let redirect = true;
                                        if ($(this).data('branchid') != 1)
                                            redirect = false;
                                        if($(this).data('branchid') != {{ auth()->user()->branch_id }})
                                            redirect = false;
                                        if (redirect === true){
                                            window.location.href = "{{ route('customers.create') }}?unvan="+$(this).data('unvan')+"&value="+$(this).data('telefon')+"tc="+$(this).data('tc')+"&type="+$(this).data('type');
                                        }
                                        $('input[name=alici_kod]').val($(this).data('kod'))
                                        $('input[name=alici_unvan]').val($(this).data('unvan'))
                                        $('input[name=alici_id]').val($(this).data('id'))
                                        $('.find-alici').css('display', 'none')
                                        $('.search-alici').val('')
                                        $('.alici-buton').html("Alıcı Bilgileri - (" + $(this).data('unvan') + " - " + $(this).data('telefon') + ")").css('background-color','#06e328')
                                        $('.search-alici').removeAttr('required')
                                        $('#searchCustomerModal').modal('toggle')
                                        getPlusCard()
                                    })
                                } else {
                                    let addCustomerHtml = 'Hiçbir Kayıt Bulunamadı!'
                                    @if(in_array('*', $userRoles) || in_array('add_customer', $userRoles))
                                        addCustomerHtml += "<br><a style=\'font-size:.9rem\' class=\"text-danger open-add-customer-alici-page\" href=\"{{ route('customers.create',['customer'=>'alici','return_url'=>url()->current(),'type'=>'bireysel','muhasebe_type'=>'alici']) }}&unvan=" + $('.search-alici').val() + "\">Yeni Bireysel Kayıt</a><br>"
                                    addCustomerHtml += "<a style=\'font-size:.9rem\' class=\"text-danger open-add-customer-alici-page\" href=\"{{ route('customers.create',['customer'=>'alici','return_url'=>url()->current(),'type'=>'kurumsal','muhasebe_type'=>'alici']) }}&unvan=" + $('.search-alici').val() + "\">Yeni Kurumsal Kayıt</a>"
                                    @endif
                                    $('.find-alici').html(addCustomerHtml)
                                    $('.open-add-customer-alici-page').on('click', function (e) {
                                        e.preventDefault()
                                        saveAndGo(e.target.href)
                                    })
                                }
                            }
                        });
                    } else {
                        $('.find-alici').css('display', 'none')
                    }
                })

                $('.search-car-button').on('click', function () {
                    let $val = $('.search-car').val();
                    $('.find-car-new').html('')
                    if ($val.length > 2) {
                        $.ajax({
                            url: "{{ route('api.getCars') }}",
                            type: "post",
                            data: {'_token': '{{ csrf_token() }}', 'search': $val},
                            success: function (response) {
                                $('#searchCarModal').modal('toggle');
                                $('.son-ekspertiz-raporlari').html('')
                                if (response.items.length > 0) {
                                    let html = ''
                                    $.each(response.items, function (index, item) {
                                        html += '<tr class="filter-item" ' +
                                            'data-id="' + item.id + '" ' +
                                            'data-plaka="' + item.plaka + '" ' +
                                            'data-sase="' + (item.sase_no) + '" ' +
                                            'data-arac_marka_model="' + item.marka_model + '"' +
                                            'data-km="' + item.km + '" data-kmtype ="' + item.km_type + '">'
                                        html += '<td>' + (item.plaka) + '</td>'
                                        html += '<td>' + item.sase_no + '</td>'
                                        html += '<td>' + item.car_group + '</td>'
                                        html += '<td>' + item.marka + '</td>'
                                        html += '<td>' + item.model + '</td>'
                                        html += '<td>' + item.case_type + '</td>'
                                        html += '<td>' + item.fuel_type + '</td>'
                                        html += '<td>' + item.gear_type + '</td>'
                                        html += '<td>' + item.model_yili + '</td>'
                                        html += '</tr>'
                                    })
                                    $('.find-car-new').html(html);


                                    $('.find-car-new .filter-item').on('click', function () {
                                        $('input[name=arac_plaka]').val($(this).data('plaka'));
                                        $('input[name=arac_sase]').val($(this).data('sase'));
                                        $('input[name=arac_marka_model]').val($(this).data('arac_marka_model'));
                                        $('input[name=car_id]').val($(this).data('id'));
                                        $('input[name=km]').val($(this).data('km'));
                                        $('select[name=km_type]').val($(this).data('kmtype'));
                                        $('.find-car').css('display', 'none');
                                        $('.search-car').val('');
                                        $('.car-buton').html("Araç Bilgileri - (" + $(this).data('plaka') + " - " + $(this).data('sase') + ")").css('background-color','#06e328');
                                        $('.search-car').removeAttr('required');
                                        let $id = $(this).data('id');
                                        $('#searchCarModal').modal('toggle');
                                        $.ajax({
                                            url: "{{ route('api.getCarExpertises') }}",
                                            type: "post",
                                            data: {'_token': '{{ csrf_token() }}', 'id': $id},
                                            success: function (response) {
                                                if (response.items.length > 0) {
                                                    $('button[name=save_type]').attr('data-printPreviousReport', 1)
                                                } else {
                                                    $('button[name=save_type]').attr('data-printPreviousReport', 2)
                                                }
                                                let html = '';
                                                $.each(response.items, function (index, item) {
                                                    html += '<tr>'
                                                    html += '<td>' + item.belge_tarihi + '</td>'
                                                    html += '<td>' + item.belge_no + '</td>'
                                                    html += '<td>' + item.sube + '</td>'
                                                    html += '<td>' + item.plaka + '</td>'

                                                    // Son veriye goprint sınıfını eklemek için kontrol ekleyelim
                                                    var goprintClass = (index === 0) ? ' goprint' : '';
                                                    var goprintAtag = (index === 0) ? '?goprint=1' : '';

                                                    html += '<td><a class="btn btn-success' + goprintClass + '" target="_blank" uuid="' + item.uuid + '" href="/ekspertiz-raporu/' + item.uuid + goprintAtag + '">Belge</a> </td>'
                                                    html += '</tr>'
                                                })


                                                $('.son-ekspertiz-raporlari').html(html)
                                            }
                                        });
                                    })
                                } else {
                                    let addCarHtml = 'Hiçbir Kayıt Bulunamadı!'
                                    @if(in_array('*', $userRoles) || in_array('add_car', $userRoles))
                                        addCarHtml += "<br><a class=\"btn btn-sm btn-danger open-add-car-page\" href=\"{{ route('cars.create',['return_url'=>url()->current()]) }}&plaka=" + $('.search-car').val() + "\">Yeni Kayıt</a><br>"
                                    @endif
                                    $('.modal-footer.arc').html(addCarHtml)
                                    $('.open-add-car-page').on('click', function (e) {
                                        e.preventDefault()
                                        saveAndGo(e.target.href)
                                    })
                                }
                            }
                        });
                    } else {
                        $('.find-car').css('display', 'none')
                    }
                })

                $('select.find-stock').on('change', function () {
                    let $ad = $('select.find-stock option:selected').data('ad')
                    let $id = $('select.find-stock option:selected').data('id')
                    let $kdv = $('select.find-stock option:selected').data('kdv')
                    let $sorguHizmeti = $('select.find-stock option:selected').data('sorguhizmeti')
                    let $yolYardimi = $('select.find-stock option:selected').data('yolyardimi')
                    let $fiyat = $('select.find-stock option:selected').data('fiyat')
                    let $kdvHaricFiyat = $('select.find-stock option:selected').data('kdvharicfiyat')
                    let $maxIskonto = $('select.find-stock option:selected').data('maxiskonto')
                    let $normalpayment = $('select.find-stock option:selected').data('normalpayment')
                    let $pluscart = $('select.find-stock option:selected').data('pluscartpayment')
                    if ($normalpayment == 1) {
                        $('.normal_payment_tab').removeClass('disabled')
                    } else {
                        $('.normal_payment_tab').addClass('disabled')
                        $('.normal_payment_tab').removeClass('active')
                        $('#nav-normal-odeme').removeClass('show')
                        $('#nav-normal-odeme').removeClass('active')
                        $('#nav-plus-kart').addClass('show');
                        $('#nav-plus-kart').addClass('active');
                    }
                    if ($pluscart == 1) {
                        $('.pluscart_payment_tab').removeClass('disabled')
                    } else {
                        $('.pluscart_payment_tab').addClass('disabled')
                        $('.pluscart_payment_tab').removeClass('active')
                        $('.normal_payment_tab').addClass('active')
                        $('#nav-normal-odeme').addClass('show')
                        $('#nav-normal-odeme').addClass('active')
                        $('#nav-plus-kart').removeClass('show');
                        $('#nav-plus-kart').removeClass('active');
                    }


                    $('select.find-stock option').attr('disabled', false)
                    $('select.find-stock option:selected').attr('disabled', true)
                    var campaing_html = '';
                    $.ajax({
                        url: "{{ route('api.campaingPrices') }}",
                        type: "post",
                        data: {
                            _token: '{{@csrf_token()}}',
                            stock_id: $id
                        },
                        success: function (response) {
                            if (response.success == "true") {
                                var campaigns = response.campaigns
                                $.each(campaigns, function (index, element) {
                                    campaing_html += '<option data-kdvharicfiyat="' + element.kdv_haric_fiyat + '" data-fiyat="' + element.kdv_dahil_fiyat + '" data-maxiskonto="' + element.max_iskonto_amount + '" value="' + element.id + '">' + element.name + '</option>';
                                })


                                let html = '<div class="row"><div class="col-md-4">' +
                                    '<div class="form-group">' +
                                    '<label>Hizmet</label>' +
                                    '<input type="text" class="form-control form-control-sm" readonly value="' + $ad + '">' +
                                    '<input type="hidden" class="form-control form-control-sm" name="stock_id[]" value="' + $id + '">' +
                                    '</div>' +
                                    ' </div>' +
                                    '<div class="col-md-4">' +
                                    '<div class="form-group">' +
                                    '<label>Geçerli Kampanya</label>' +
                                    '<select name="campaign_id[]" class="campaing_select select2 js-states">' +
                                    '<option value="0">Seçilmedi</option>' +
                                    campaing_html +
                                    '</select>' +
                                    '</div>' +
                                    '</div>' +
                                    '<div class="col-md-4">' +
                                    '<div class="form-group"><label>Sorgu Hizmeti</label>' +
                                    '<input type="text" class="form-control form-control-sm" name="sorgu_hizmeti[]" value="' + $sorguHizmeti + '"></div></div>' +
                                    '<div class="col-md-3"><div class="form-group"><label>Yol Yardımı</label>' +
                                    '<input type="text" class="form-control form-control-sm" name="yol_yardimi[]" value="' + $yolYardimi + '"></div>' +
                                    '</div><div class="col-md-3"><div class="form-group"><label>İskonto Tutarı</label>' +
                                    '<input type="text" name="iskonto_amount[]" min="0" max="' + $maxIskonto + '" class="form-control form-control-sm iskonto_amount_hizmet"  value="0"></div>' +
                                    '</div><div class="col-md-3"><div class="form-group"><label>Hizmet Tutarı</label><input type="text" class="form-control form-control-sm service_price" name="hizmet_tutari[]" value="' + $fiyat + '" ></div>' +
                                    '</div><div class="col-md-3"><div class="form-group"><label>Liste Fiyatı</label><input type="text" class="form-control form-control-sm list_price" name="liste_fiyati[]" value="' + $fiyat + '" ></div></div>' +
                                    '<div class="col-md-12 text-end"><button type="button" class="btn btn-danger btn-sm mt-4 remove-service" data-id="' + $id + '">Sil</button></div><hr></div>'

                                $('.services').html(html)
                                $(".select2").select2({
                                    placeholder: "Ara.."
                                });

                                $('select.find-stock').val(0)

                                $('.remove-service').on('click', function () {
                                    $(this).parent().parent().remove()
                                    let id = $(this).data('id')
                                    let index = selectedStocks.findIndex(obj => obj.id === id);

                                    if (index !== -1) {
                                        selectedStocks.splice(index, 1);
                                    }
                                    setSummary()
                                    $('select.find-stock option[value=' + id + ']').removeAttr('disabled')
                                })

                                selectedStocks = [];
                                selectedStocks.push({
                                    'id': $id,
                                    'ad': $ad,
                                    'kdv': $kdv,
                                    'iskonto_amount': 0,
                                    'kdvHaricFiyat': $kdvHaricFiyat,
                                })

                                $('.selectedStocks').html('')
                                let selectedStocksHtml = ''
                                selectedStocks.forEach(function (item, index) {
                                    selectedStocksHtml += '<tr>'
                                    selectedStocksHtml += '<td>' + item.ad + '</td>'
                                    // selectedStocksHtml += '<td>%' + item.kdv + '</td>'
                                    selectedStocksHtml += '<td>' + item.kdvHaricFiyat + '₺</td>'
                                    selectedStocksHtml += '</tr>'
                                })
                                $('.selectedStocks').html(selectedStocksHtml)

                                if ($id == 1 || $id == 68 || $id == 191) {
                                    $('select[name=branch_id]').removeAttr('disabled')
                                } else {
                                    @if(auth()->user()->type != 'admin')
                                    $('select[name=branch_id]').attr('disabled', '')
                                    @endif
                                }
                                setSummary()

                                $('.iskonto_amount_hizmet').change('on', function () {
                                    var value = $(this).val();
                                    var max = $(this).attr('max')
                                    var min = $(this).attr('min')

                                    if (parseInt(value) > parseInt(max)) {
                                        value = max
                                        $(this).val(parseInt(max));

                                        Toast.fire({
                                            icon: "error",
                                            title: "İskonto Tutarını Fazla Girdiniz!"
                                        });
                                    }

                                    if (parseInt(value) < parseInt(min)) {
                                        value = min
                                        $(this).val(parseInt(min));
                                        Toast.fire({
                                            icon: "error",
                                            title: "İskonto Tutarını Az Girdiniz!"
                                        });
                                    }

                                    selectedStocks[0].iskonto_amount = value

                                    setSummary()
                                    $('.iskonto_amount_cont').html(value.toLocaleString('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY'
                                    }) + ' ₺')
                                })
                                $('.campaing_select').change('on', function () {
                                    var value = $(this).val();
                                    if (value !== undefined && value != '' && value > 0) {
                                        var kdvHaricFiyat = $(this).find('option:selected').data('kdvharicfiyat');
                                        var fiyat = $(this).find('option:selected').data('fiyat');
                                        var max_iskonto = $(this).find('option:selected').data('maxiskonto');
                                        selectedStocks[0].kdvHaricFiyat = kdvHaricFiyat;
                                        $('.service_price').val(fiyat)
                                        $('.list_price').val(fiyat)

                                        $('.iskonto_amount_hizmet').attr('max', max_iskonto)
                                        var iskonto_value = $('.iskonto_amount_hizmet').val();
                                        var max = $('.iskonto_amount_hizmet').attr('max')
                                        if (parseInt(iskonto_value) > parseInt(max)) {
                                            iskonto_value = max
                                            $('.iskonto_amount_hizmet').val(parseInt(max));
                                        }
                                        selectedStocks[0].iskonto_amount = parseInt(iskonto_value)
                                    } else {
                                        selectedStocks[0].kdvHaricFiyat = $kdvHaricFiyat;
                                        $('.service_price').val($fiyat)
                                        $('.list_price').val($fiyat)


                                        $('.iskonto_amount_hizmet').attr('max', $maxIskonto)
                                        var iskonto_value = $('.iskonto_amount_hizmet').val();
                                        var max = $('.iskonto_amount_hizmet').attr('max')
                                        if (parseInt(iskonto_value) > parseInt(max)) {
                                            iskonto_value = max
                                            $('.iskonto_amount_hizmet').val(parseInt(max));
                                        }

                                        selectedStocks[0].iskonto_amount = parseInt(iskonto_value)
                                    }
                                    $('.iskonto_amount_cont').html(iskonto_value.toLocaleString('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY'
                                    }) + ' ₺')
                                    setSummary()
                                })

                                getPlusCard()
                            } else {
                                Toast.fire({
                                    icon: "error",
                                    title: "Bir Hata Oluştu Lütfen Sayfayı Yenileyin!"
                                });
                            }
                        },
                    });


                })
            });


            $('button[name="save_type"]').on('click', function (e) {
                e.preventDefault()
                if (selectedStocks.length == 0) {
                    Toast.fire({
                        icon: "error",
                        title: "Hizmet Seçilmedi!"
                    });
                    return false;
                }
                console.log($('input[name=car_id]').val());
                if ($('input[name=car_id]').val() == '') {
                    Toast.fire({
                        icon: "error",
                        title: "Araç Seçilmedi!"
                    });
                    return false;
                }
                var payment_type = $('input[name=payment_type]').val()
                if(payment_type == 'sozlesme'){
                    if ($('input[name="contract_status"]').val() !== '1'){
                        Toast.fire({
                            icon: "error",
                            title: "Sözleşme Kontrol Ediniz!"
                        });
                        return false
                    }
                    $('select[name="odeme_tip[]"]').removeAttr('required')
                }
                else if (payment_type != 'plus_kart') {
                    let totalPayment = 0;
                    $.each($('.odeme_tutar_input'), function (index, item) {
                        totalPayment += parseFloat($(item).val().replace(/[^0-9,-]+/g, "").replace(',', '.'))
                    });
                    if (totalPayment.toLocaleString('tr-TR', {
                        style: 'currency',
                        currency: 'TRY'
                    }) < allTotal.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'})) {
                        Toast.fire({
                            icon: "error",
                            title: "Ödeme Tutarı Eksik Girildi!"
                        });
                        return false;
                    } else if (totalPayment.toLocaleString('tr-TR', {
                        style: 'currency',
                        currency: 'TRY'
                    }) > allTotal.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'})) {
                        Toast.fire({
                            icon: "error",
                            title: "Ödeme Tutarı Fazla Girildi!"
                        });
                        return false;
                    }
                } else {
                    var plus_card_payment_type = $('input[name=plus_card_payment_type]:checked').val();
                    var plus_kart_id = $('input[name=plus_kart_id]:checked').val();
                    if (plus_card_payment_type === undefined) {
                        /* Toast.fire({
                             icon: "error",
                             title: "Plus Card Kullanım Tipi Seçilmedi!"
                         });
                         return false; */
                    } else if (plus_kart_id === undefined) {
                        /*Toast.fire({
                            icon: "error",
                            title: "Plus Card  Seçilmedi!"
                        });
                        return false;*/
                    }

                    let $plusCardSmsVerified = $('input[name="plus_card_sms_verified"]').val();
                    if ($plusCardSmsVerified == '0'){
                        Toast.fire({
                            icon: "error",
                            title: "Sms Doğrulanmadı"
                        });
                        $('button[name="save_type"]').removeAttr('disabled');
                        $('#plusCardSmsVerification').modal('toggle');
                        return false;
                    }
                }

                $(this).unbind('click').click();
            })
        })();

        $('.plus-card-sms-verification-button').on('click',function (){
            if ($('input[name="plus_card_sms"]').val() == '{{ $plusCardSmsCodeCep }}'){
                $('input[name="plus_card_sms_verified"]').val(1);
                $('input[name="plus_card_sms_verified_type"]').val('cep');
                Toast.fire({
                    icon: "success",
                    title: "Sms Doğrulandı"
                });
                $('#plusCardSmsVerification').modal('toggle');
            }else if($('input[name="plus_card_sms"]').val() == '{{ $plusCardSmsCodeTelefon }}'){
                $('input[name="plus_card_sms_verified"]').val(1);
                $('input[name="plus_card_sms_verified_type"]').val('telefon');
                Toast.fire({
                    icon: "success",
                    title: "Sms Doğrulandı"
                });
                $('#plusCardSmsVerification').modal('toggle');
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Sms Doğrulanamadı!"
                });
            }
        });

        $('.plus-card-sms-verification-send-button').on('click',function (){
            let $plusCardID = $('input[name=plus_kart_id]:checked').val();
            let $this = $(this);
            $($this).attr('disabled','true').html('Tekrar Göndermek İçin 30 Saniye Bekleyiniz');
            setTimeout(function (){
                $($this).removeAttr('disabled').html('SMS Kodu Gönder');
            },1000 * 30)
            $.ajax({
                url: "{{ route('plusCardSmsVerification') }}",
                type: "post",
                data: {
                    '_token': '{{ csrf_token() }}',
                    'plus_card_id': $plusCardID,
                    'sms_code_cep':'{{ $plusCardSmsCodeCep }}',
                    'sms_code_telefon':'{{ $plusCardSmsCodeTelefon }}'
                },
                success: function (response) {
                    if (response.success == 'false'){
                        Toast.fire({
                            icon: "error",
                            title: response.message
                        });
                    }else{
                        $('input[name="plus_card_sms_cep"]').val(response.plusCardCustomerCep);
                        $('input[name="plus_card_sms_telefon"]').val(response.plusCardCustomerTelefon);
                        Toast.fire({
                            icon: "success",
                            title: response.message
                        });
                    }
                }
            });
        });

        if(document.querySelector('input[name="plus_card_sms"]')){
            const maskTel = IMask(document.querySelector('input[name="plus_card_sms"]'), {
                mask: 'UMR-00-00'
            });
        }

        function setSummary() {
            let total = 0;
            let iskonto_total = 0;
            $('.kdv-item').addClass('d-none')
            $('.kdv-value-0', '.kdv-value-1', '.kdv-value-8', '.kdv-value-18', '.kdv-value-20').html('0₺')
            let kdv0 = 0;
            let kdv1 = 0;
            let kdv8 = 0;
            let kdv18 = 0;
            let kdv20 = 0;
            let selectedStocksHtml = ''
            $('.selectedStocks').html('')
            selectedStocks.forEach(function (item) {
                total += item.kdvHaricFiyat

                let kdv = item.kdv
                $('.kdv-' + kdv).removeClass('d-none')
                if (kdv == 20)
                    kdv20 += (item.kdvHaricFiyat) * kdv / 100;
                else if (kdv == 18)
                    kdv18 += (item.kdvHaricFiyat) * kdv / 100;
                else if (kdv == 8)
                    kdv8 += (item.kdvHaricFiyat) * kdv / 100;
                else if (kdv == 1)
                    kdv1 += (item.kdvHaricFiyat) * kdv / 100;

                iskonto_total += item.iskonto_amount
                selectedStocksHtml += '<tr>'
                selectedStocksHtml += '<td>' + item.ad + '</td>'
                // selectedStocksHtml += '<td>%' + item.kdv + '</td>'
                selectedStocksHtml += '<td>' + item.kdvHaricFiyat.toLocaleString('tr-TR', {
                    style: 'currency',
                    currency: 'TRY'
                }) + '₺</td>'
                selectedStocksHtml += '</tr>'
            })
            $('.selectedStocks').html(selectedStocksHtml)
            $('.kdv-value-0').html(kdv0.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}) + '₺')
            $('.kdv-value-1').html(kdv1.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}) + '₺')
            $('.kdv-value-8').html(kdv8.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}) + '₺')
            $('.kdv-value-18').html(kdv18.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}) + '₺')
            $('.kdv-value-20').html(kdv20.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}) + '₺')
            $('.subTotal').html(total.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}) + '₺')
            total += kdv0
            total += kdv1
            total += kdv8
            total += kdv18
            total += kdv20
            total = (total - iskonto_total)
            $('.allTotal').html(total.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}) + '₺')
            allTotal = total
        }

        function getPlusCard() {
            var stock_id = $('input[name="stock_id[]"]').val()
            var customer_id = $('input[name=alici_id]').val()
            $.ajax({
                url: "{{ route('api.getCustomerPlusCards') }}",
                type: "post",
                data: {
                    '_token': '{{ csrf_token() }}',
                    'stock_id': stock_id,
                    customer_id: customer_id,
                    servis_bayi: "{{$newDB->kayit_branch_id ?? null}}",
                },
                success: function (response) {
                    html = '';
                    if (response.items && typeof response.items === 'object' && Object.keys(response.items).length > 0) {
                        var html = '';
                        var html2 = '';
                        var total_credits = 0
                        var total_points = 0
                        $.each(response.items, function (index, item) {
                            html += '<tr>'
                            html += '<td>' + item.no + '</td>'
                            html += '<td>' + item.system_id + '</td>'
                            html += '<td>' + item.kredi + '</td>'
                            html += '<td>' + item.puan + '</td>'
                            html += '<td></td>'
                            html2 += '<tr>'
                            html2 += '<td>' + item.no + '</td>'
                            html2 += '<td>' + item.system_id + '</td>'
                            html2 += '<td>' + item.kredi + '</td>'
                            html2 += '<td>' + item.puan + '</td>'
                            html2 += '</tr>'


                            if (parseInt(item.kredi) == 0 && parseInt(item.puan) == 0) {
                                @if(in_array('*', $userRoles) || in_array('edit_plus_card', $userRoles))
                                    html += '<td><a href="/plus-cards/' + item.id + '/edit?return_url={{url()->current()}} "  class="btn btn-sm btn-success add_balance_plus_cards">Bakiye Yükle</a></td>'
                                @else
                                    html += '<td>Bakiye Yok.</td>'
                                @endif
                            } else {
                                html += '<td><input type="radio" name="plus_kart_id"  data-credits="' + item.kredi + '" data-points = "' + item.puan + '" value="' + item.card_id + '"></td>'
                            }
                            html += '</tr>'

                            total_credits = total_credits + item.kredi
                            total_points = total_points + item.puan
                        })
                        if (total_credits == 0) {
                            $('#plus_kredi').attr('disabled', '')
                        } else {
                            $('#plus_kredi').removeAttr('disabled')
                        }
                        if (total_points == 0) {
                            $('#plus_puan').attr('disabled', '')
                        } else {
                            $('#plus_puan').removeAttr('disabled')
                        }
                        $('.alici-plus-kartlar').html(html)
                        $('.alici-plus-kartlar_2').html(html2)

                    } else {
                        @if(in_array('*', $userRoles) || in_array('add_plus_card', $userRoles))
                            html += '<a href="{{ route('plus-cards.create') }}" class="btn btn-sm btn-danger">{{ \App\Models\Menu::where('key','add_plus_card')->first()->value }}</a>'
                        @endif
                        $('.alici-plus-kartlar').html(html)
                        $('.alici-plus-kartlar_2').html(html)
                    }
                }
            });
        }

        //Satıcı ile Cari Aynı
        $('input[name="satici_ayni"]').on('change', function () {
            if (this.checked) {
                $('input[name=satici_kod]').val($('input[name=cari_kod]').val())
                $('input[name=satici_unvan]').val($('input[name=cari_unvan]').val())
                $('input[name=satici_id]').val($('input[name=cari_id]').val())
                $('.saticiBilgiler').css('display', 'none')
                $('.satici-buton').html("Satıcı Bilgileri - " + $('input[name=cari_unvan]').val()).css('background-color','#06e328')
            } else {
                $('.saticiBilgiler').css('display', 'block')
                $('input[name=satici_kod]').val('')
                $('input[name=satici_unvan]').val('')
                $('input[name=satici_id]').val('')
                $('.satici-buton').html("Satıcı Bilgileri").css('background-color','#e30614')
            }
        })
        //Alıcı ile Cari Aynı
        $('input[name="alici_ayni"]').on('change', function () {
            if (this.checked) {
                $('input[name=alici_kod]').val($('input[name=cari_kod]').val())
                $('input[name=alici_unvan]').val($('input[name=cari_unvan]').val())
                $('input[name=alici_id]').val($('input[name=cari_id]').val())
                $('.aliciBilgiler').css('display', 'none')
                $('.alici-buton').html("Alıcı Bilgileri - " + $('input[name=cari_unvan]').val()).css('background-color','#06e328')
            } else {
                $('.aliciBilgiler').css('display', 'block')
                $('input[name=alici_kod]').val('')
                $('input[name=alici_unvan]').val('')
                $('input[name=alici_id]').val('')
                $('.alici-buton').html("Alıcı Bilgileri").css('background-color','#e30614')
            }
            getPlusCard()
        })

        $('.remove-service').on('click', function () {
            $(this).parent().parent().remove()
            let id = $(this).data('id')
            let index = selectedStocks.findIndex(obj => obj.id === id);

            if (index !== -1) {
                selectedStocks.splice(index, 1);
            }
            setSummary()
            $('select.find-stock option[value=' + id + ']').removeAttr('disabled')
        })

        function saveAndGo(url) {
            window.location.href = url
        }

        function getPaymentType(normalpayment = null, pluscart = null) {
            if (normalpayment == 1) {
                $('.normal_payment_tab').removeClass('disabled')
            } else {
                $('.normal_payment_tab').addClass('disabled')
                $('.normal_payment_tab').removeClass('active')
                $('#nav-normal-odeme').removeClass('show')
                $('#nav-normal-odeme').removeClass('active')
                $('#nav-plus-kart').addClass('show');
                $('#nav-plus-kart').addClass('active');
            }
            if (pluscart == 1) {
                $('.pluscart_payment_tab').removeClass('disabled')
            } else {
                $('.pluscart_payment_tab').addClass('disabled')
                $('.pluscart_payment_tab').removeClass('active')
                $('.normal_payment_tab').addClass('active')
                $('#nav-normal-odeme').addClass('show')
                $('#nav-normal-odeme').addClass('active')
                $('#nav-plus-kart').removeClass('show');
                $('#nav-plus-kart').removeClass('active');
            }
        }



        @foreach($newDB->getStocks as $expertiseStock)
        @if(isset($expertiseStock->getStock->active_normal_payment) && isset($expertiseStock->getStock->active_plus_cart))
        getPaymentType({{$expertiseStock->getStock->active_normal_payment}}, {{$expertiseStock->getStock->active_plus_cart}})
        @endif
        @endforeach

    </script>
@endpush
