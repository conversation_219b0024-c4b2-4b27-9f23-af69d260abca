@extends('pages.build')
@section('title','Alıcı Sözleşme')
@push('css')
    <style>
        table{
            border: 3px solid #000 !important;
        }
        .table-bordered>:not(caption)>*>*{
            border-width: 1px;
        }
        .table td, .table th{
            padding: .15rem;
        }
        .table td, .table th{
            line-height: 1rem;
        }
        @media print{
            body{
                visibility: hidden;
            }

            #divRaporEkrani{
                visibility: visible;
            }
        }
    </style>
@endpush
@section('content')
    <table id="divRaporEkrani" class="table table-bordered">
        <tbody>
            <tr>
                <td style="width: 33%;text-align: center">
                    <img src="/assets/isemri_logo.png" style="width: 100%">
                    <h5 style="color: darkorange">444 5 417</h5>
                </td>
                <td style="width: 33%" class="text-center">
                    <b> EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>@if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11')<br><br> <b>İŞ EMRİ</b> @endif
                </td>
                <td colspan="2" style="width: 33%">
                    <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                    <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['belge_tarihi'])->format('d.m.Y H:i:s') }}</h5>
                </td>
            </tr>
            <tr>
                <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
            </tr>
            <tr>
                <td>Plaka</td>
                <td>{{ $expertise['plaka'] }}</td>
                <td>Şase No</td>
                <td>{{ $expertise['sase_no'] }}</td>
            </tr>
            <tr>
                <td>Marka Model</td>
                <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                <td>Araç KM</td>
                <td>{{ $expertise['km'] }}</td>
            </tr>
            <tr>
                <td>Model Yılı</td>
                <td>{{ $expertise['model_yili'] }}</td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td colspan="2"><b>Alıcı Bilgileri</b></td>
                <td colspan="2"><b>Satıcı Bilgileri</b></td>
            </tr>
            <tr>
                <td>Adı Soyadı</td>
                <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                <td>Adı Soyadı</td>
                <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
            </tr>
            <tr>
                <td>Firma Unvanı</td>
                <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                <td>Firma Unvanı</td>
                <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
            </tr>
            <tr>
                <td>Adres</td>
                <td>{{ $expertise['fullAddressAlici'] }}</td>
                <td>Adres</td>
                <td>{{ $expertise['fullAddressSatici'] }}</td>
            </tr>
            <tr>
                <td>Telefon</td>
                <td>{{ $expertise['telefonAlici'] }}</td>
                <td>Telefon</td>
                <td>{{ $expertise['telefonSatici'] }}</td>
            </tr>
            <tr>
                <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                <td colspan="2">
                @foreach($expertise['getStocks'] as $expertiseStock)
                    {{ $expertiseStock['ad'] }},
                @endforeach</td>
            </tr>
            <tr>
                <td colspan="4"><b>Tarafların sorumlulukları ve bilgilendirme</b></td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 1) @endif Umran Bağımsız Araç Ekspertiz Merkezleri, aracın görünen kusurları ve o anki durumu ile ilgili bilgilendirme yapar. Satıcı ve üçüncü şahıslar tarafından gizlenmiş kusurların tespit edilememesinden ve sonrasında ortaya çıkacak problemlerden dolayı Umran Bağımsız Araç Ekspertiz Merkezleri sorumlu tutulamaz.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 2) @endif Servislerimizde ekspertiz kontrollerinde aracın hiçbir yerinde sökme takma işlemi yapılmamaktadır. Boyasız göçük düzeltme tespitleri yapılamamaktadır. Durum ve arıza tespiti için özel test gerektiren parçaların kontrolü yapılmamaktadır (Motor, Motor Bloğu, Kompresyon, sızdırmazlık vb.).
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 3) @endif Bayilerimizde Kilometre orijinalliği tespitleri yapılmamaktadır.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 4) @endif Servislerimizde Airbag (hava yastığı) kontrollerinde herhangi bir sökme takma işlemi yapılmamaktadır. Kontroller cihazla yapılmaktadır.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 5) @endif Yalnızca sökme takma neticesinde anlaşılabilecek arızaların tespit edilememesinden dolayı şirketimizin herhangi bir sorumluluğu bulunmamaktadır.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 6) @endif Aracın çalıntı olması ve ya yasal olmayan şase numara aktarımı işlemleri kontrol edilmemekte olup Umran Bağımsız Araç Ekspertiz Merkezleri'ni bağlayıcı hukuki sorumluluğu yoktur.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 7) @endif Plastik parçalar, Far, Stop Lambaları, Sis Lambalarındaki değişim ve Cam değişimleri, Silecek lastikleri kontrol edilmemektedir. Cam, ayna ve koltuk ısıtıcıları (({{ \Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-12 13:00:00' ? 'rezistansları' : 'rezinstansları' }})) kontrol edilmemektedir. Koltuk hafızası ve konfor elektriği kontrol edilmemektedir, fitil sızdırmazlıkları, kapı kilitleri, araç kumandaları ve anahtar, opsiyonel özellikler (Araç Paketleri), araç orijinalliğine uymayan yazılım ve parçalar (Modifiye Araçlar), tekerlek uygunluğu ve orijinalliği kontrol edilememektedir.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 8) @endif Araç karoseri üzerindeki kaplamalar, şeffaf folyo, reklam, araç giydirme vb. bulunan araçlarda boya ile ilgili kontroller yapılamamaktadır.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 9) @endif Ekspertizimizle ilgili problemlerimizde, araca herhangi bir müdahale (Tamirat) yaptırmadan aracı görmemiz gerekir. Aksi takdirde dışarıda yaptırdığınız müdahaleler Umran Bağımsız Araç Ekspertiz Merkezleri sorumluluk alanını kapsamaz.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 10) @endif Kaporta Boya değerlendirmesinin itiraz süresi 72 saat, Mekanik ile ilgili raporumuzun itiraz süresi 24 saattir.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 11) @endif Umran Bağımsız Araç Ekspertiz Merkezleri verilen ekspertiz raporunda yanlışlık yapılması halinde, Karayolları Motorlu Araçlar Zorunlu Mali Sorumluluk Sigortası Genel Şartlarının Ek-1’inde yer alan “Değer kaybı hesaplaması” hükümleri çerçevesinde müşteriye değer kaybı ödemesi yapmayı kabul ve taahhüt eder. Hem motor-mekanik hem kaporta-boya ekspertiz raporu için herhalükarda gerekli durumlarda yapılması taahhüt edilen değer kaybı ödemesi ve/veya parça onarım bedeli ödemesi aracın piyasa ve kasko değerine bakılmaksızın azami 100.000 TL'ye kadar olan tutarlar ile sınırlıdır.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 12) @endif Müşteri İşbu iş emri ile almış olduğu ekspertiz raporunun Umran Bağımsız Araç Ekspertiz Merkezlerine ait {{ \Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-12 13:00:00' ? 'web' : 'Web' }} sitesinde yayınlanmasını ve yine web sitesi üzerinden bir başkasına satışını kabul etmiş sayılır.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 13) @endif Gerekli görülmesi halinde araçlara yol testi yapılmaktadır. Cihaz ile Testlerin yapılamaması durumunda yol testi yapılabilmektedir.
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 14) @endif Umran Plus Otomotiv A.Ş. ve program ortaklarının sms ve e-mail yoluyla kampanya, ürün ve hizmetleri hakkında bilgilendirme yapmasına izin veriyorum.
                </td>
                <td>
                    Evet
                </td>
                <td>
                    Hayır
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 15) @endif Servislerimizde araçların periyodik bakım tespiti yapılmamaktadır. Bakımların zamanında yapılmamasından kaynaklı, ekspertiz işlemi sırasında ve sonrasında ortaya çıkabilecek problemlerden dolayı Umran Bağımsız Araç Ekspertiz Merkezleri sorumlu tutulamaz.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 16) @endif CO2 Kaçak Testiyle ilgili hizmet sonuçları test sıvısı üzerinden yorumlanmaktadır. İçeriğiyle ilgili olarak Umran Bağımsız Araç Ekspertiz Merkezlerinin sorumluluğu bulunmadığı gibi herhangi bir garanti yükümlülüğü taahhüdü de bulunmamaktadır.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 17) @endif Alıcı/satıcı beyanına göre ekspertiz talebi yapılan araç bilgilerine özel üretilmiş işbu ekspertiz raporunun tüm hukuki ve cezai sorumluluğu BAYİ'ye aittir. Umran Plus Otomotiv San. ve Tic. A.Ş.'nin bu hususta herhangi bir hukuki ve cezai bir sorumluluğu bulunmamaktadır.
                </td>
            </tr>
            <tr>
                <td colspan="4">
                    @if(\Carbon\Carbon::parse($expertise['belge_tarihi']) >= '2024-12-11') 18) @endif Umran Bağımsız Araç Ekspertiz Merkezleri; mikron boya işlemi uygulanmış araç parçaları için yaptığı kaporta-boya tespitleri hakkında herhangi bir garanti sağlamamaktadır. Bu hususta ortaya çıkabilecek durumlardan dolayı Umran Bağımsız Araç Ekspertiz Merkezleri’nin herhangi bir sorumluluğu ve taahhütü bulunmamaktadır.
                </td>
            </tr>
            <tr class="text-center">
                <td style="width: 33%">
                    Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                    <b>{{!empty($authUser->getBranch->unvan) ? $authUser->getBranch->unvan : ''}}</b>

                </td>
                <td style="width: 33%">
                    Alıcı <br><br><b>{{ $expertise['getAlici'] }}</b>
                </td>
                <td style="width: 33%" colspan="2">
                    Satıcı <br><br><b>{{ $expertise['getSatici'] }}</b>
                </td>
            </tr>
        </tbody>
    </table>
    <div class="col-12 d-flex justify-content-center">
        {{--        <button class="btn btn-danger m-3" onclick="window.print()"><i class="fa fa-print"></i> Yazdır</button>--}}
        <button class="btn btn-danger m-3" onclick="ExportPdf()"><i class="fe bi-file-earmark-pdf"></i> PDF Olarak Kaydet</button>
    </div>
@endsection
@push('js')
    <script src="/assets/js/html2pdf.bundle.js"></script>
    <script>
        function ExportPdf() {
            // Get the element.
            var element = document.getElementById('divRaporEkrani');
            // Generate the PDF.
            html2pdf().from(element).set({
                margin: 1,
                filename: '{{ $expertise['uuid'] }}.pdf',
                html2canvas: { scale: 1.5 },
                jsPDF: { orientation: 'portrait', unit: 'cm', format: 'a4', compressPDF: true }
            }).save();
        }
    </script>

@endpush
