@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        .orijinal{
            background-color: #96ff7e !important;
        }
        .boyali{
            background-color: #fffd7e !important;
        }
        .degisen{
            background-color: #ff7e7e !important;
        }
        .duz{
            background-color: #7eceff !important;
        }
    </style>
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Ekspertiz - Kaporta')
@section('content')
    @include('pages.expertise.tabs')
    <form id="mainForm" enctype="multipart/form-data">@csrf
        <input type="hidden" name="expertise_id" value="{{ $_GET['expertise_id'] }}">
        <input type="hidden" name="type" value="{{ $_GET['type'] }}">
        <div class="row">
            <div class="col-md-10">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item" style="position: sticky;top: 1rem;z-index: 2">
                        <h2 class="accordion-header" id="belgeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#belge" aria-expanded="true"
                                    aria-controls="belge">
                                Belge
                            </button>
                        </h2>
                        <div id="belge" class="accordion-collapse collapse show" aria-labelledby="belgeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-1">
                                        <label class="form-label">Personel</label>
                                        <input type="text" class="form-control form-control-sm" name="kaporta_kontrol_user" @if($expertise['kaporta_kontrol_user'] > 0) disabled value="{{ $expertise['kaporta_kontrol_user'] }}" @else  @endif placeholder="Personel ID">
                                    </div>
                                    <div class="col-md-1">
                                        <label>Plaka</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['plaka'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>Şase</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['sase_no'] }}">
                                    </div>
{{--                                    <div class="col-md-2">--}}
{{--                                        <label>Hizmet</label>--}}
{{--                                        <input disabled class="form-control form-control-sm" value="@foreach($expertise['getStocks'] as $expertiseStock) {{ $expertiseStock['ad'] }}, @endforeach">--}}
{{--                                    </div>--}}
                                    <div class="col-md-2">
                                        <label>Tarih</label>
                                        <input type="datetime-local" class="form-control form-control-sm" readonly name="date" value="{{ $expertise['date'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>No</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['belge_no'] }}">
                                    </div>
                                    <div class="col-md-1">
                                        <label>KM</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['km'] }}">
                                    </div>
                                    <div class="col-md-1">
                                        <label>Model Yılı</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['car_model_year'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>Model</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['car_model'] }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item" >
                        <h2 class="accordion-header" id="kaportaSonuclariHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#kaportaSonuclari" aria-expanded="true"
                                    aria-controls="kaportaSonuclari">
                                Kaporta Sonuçları
                            </button>
                        </h2>
                        <div id="kaportaSonuclari" class="accordion-collapse collapse show" aria-labelledby="kaportaSonuclariHeading">
                            <div class="accordion-body" style="overflow: auto;max-width: 100%;max-height: 500px">
                                <table id="stickyTable" class="table table-hover table-striped">
                                    <thead style="position: sticky;top: -13px;z-index:1;">
                                    <tr  class="text-center">
                                        <th>Başlık</th>
                                        <th>Orijinal</th>
                                        <th>Boyalı</th>
                                        <th>Değişen</th>
                                        <th>Düz</th>
                                        <th>Notlar</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($expertise['getBodyworks'] as $key => $bodywork)
                                        <tr class="text-center kaporta-checkboxes kaporta_check_tr{{$key}}">
                                            <td>{{ $bodywork['title'] }}</td>
                                            <td @if($bodywork['orijinal'] == 1) class="orijinal" @endif><input type="checkbox" onclick="kaporta_check_list('orijinal','{{$key}}')" key="{{$key}}" name="{{ $key }}[]" @if($bodywork['orijinal'] == 1) checked @endif value="orijinal" @if($expertise['is_closed']) disabled @endif></td>
                                            <td @if($bodywork['boyali'] == 1) class="boyali" @endif><input type="checkbox" onclick="kaporta_check_list('boyali','{{$key}}')" key="{{$key}}" name="{{ $key }}[]" @if($bodywork['boyali'] == 1) checked @endif value="boyali" @if($expertise['is_closed']) disabled @endif></td>
                                            <td @if($bodywork['degisen'] == 1) class="degisen" @endif><input type="checkbox" onclick="kaporta_check_list('degisen','{{$key}}')" key="{{$key}}" name="{{ $key }}[]" @if($bodywork['degisen'] == 1) checked @endif value="degisen" @if($expertise['is_closed']) disabled @endif></td>
                                            <td @if($bodywork['duz']== 1) class="duz" @endif><input type="checkbox" onclick="$(this).parent().addClass('duz')" name="{{ $key }}[]" key="{{$key}}" @if($bodywork['duz'] == 1) checked @endif value="duz" @if($expertise['is_closed']) disabled @endif></td>
                                            <td><input type="text" class="form-control note required_input_{{$key}}" maxlength="35" @if($bodywork['orijinal'] != 1 && $bodywork['boyali'] != 1 && $bodywork['degisen'] != 1 && $bodywork['duz'] != 1) required="" @endif name="{{ $key }}[]" value="{{ $bodywork['note'] }}" placeholder="Notlar" @if($expertise['is_closed']) disabled @endif></td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                    <tfoot>
                                    @if(!$expertise['is_closed'])
                                        <tr class="text-center">
                                            <td colspan="6"><button type="button" onclick="$('.kaporta-checkboxes td input[type=\'checkbox\']').prop('checked', false);$('.kaporta-checkboxes td').removeClass(['boyali','orijinal','degisen','duz'])" class="btn btn-danger btn-sm">Sıfırla</button> </td>
                                        </tr>
                                    @endif
                                    </tfoot>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item" >
                        <h2 class="accordion-header" id="kaportaSonuclariHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#kaportaSonuclari" aria-expanded="true"
                                    aria-controls="kaportaSonuclari">
                                Kaporta Hatalı Bölgeler
                            </button>
                        </h2>
                        <div id="kaportaSonuclari" class="accordion-collapse collapse show" aria-labelledby="kaportaSonuclariHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-10">
                                       <!-- @if(file_exists(public_path($expertise['bodywork_image'])))
                                            <img id="myImage" src="{{ $expertise['bodywork_image'] }}" class="img-fluid">
                                        @else
                                            <img id="myImage" src="{{$expertise['case_image']}}" class="img-fluid">
                                        @endif -->
                                        <img id="myImage" src="{{ $expertise['bodywork_image'] }}">

                                        @if(!$expertise['is_closed'])
                                            <canvas id="canvas"></canvas>
                                        @endif
                                    </div>
                                    <div class="col-md-2">
                                        @if(!auth('customer')->check() && !$expertise['is_closed'])
                                            <div class="form-group mt-2">
                                                <button type="button" class="btn" onclick="$('input[value=\'yellow\']').click()" style="background: #ffe403;padding: 3px;color: #000;">Çizik</button>
                                                <input class="d-none" type="radio" form="colorForm" name="color" checked value="yellow">
                                            </div>
                                            <div class="form-group">
                                                <button type="button" class="btn" onclick="$('input[value=\'blue\']').click()" style="background: #0077ff;padding: 3px;color: #fff;">Ezik</button>
                                                <input class="d-none" type="radio" form="colorForm" name="color" value="blue">
                                            </div>
                                            <div class="form-group">
                                                <button type="button" class="btn" onclick="$('input[value=\'red\']').click()" style="background: #ff0000;padding: 3px;color: #fff;">Kırık</button>
                                                <input class="d-none" type="radio" form="colorForm" name="color" value="red">
                                            </div>
                                            <div class="form-group">
                                                <button type="button" class="btn" onclick="$('input[value=\'green\']').click()" style="background: #6faa27;padding: 3px;color: #fff;">Onarım</button>
                                                <input class="d-none" type="radio" form="colorForm" name="color" value="green">
                                            </div>
                                            <button type="button" class="btn btn-sm btn-dark" id="undoButton">Geri</button>
                                            <button type="button" class="btn btn-sm btn-dark" id="redoButton">İleri</button>
                                            <button type="button" class="btn btn-sm btn-danger mt-3" id="resetButton">Görsel Sıfırla</button>
                                            @if($expertise['created_at'] > '2024-05-28 00:00:00')
                                                <button type="button" class="btn btn-sm btn-success mt-3" id="saveImageButton">Görsel Kaydet</button>
                                            @endif

                                        @endif

                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="notlarHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#notlar" aria-expanded="true"
                                    aria-controls="notlar">
                                Notlar (Boş Veriler Silinir)
                            </button>
                        </h2>
                        <div id="notlar" class="accordion-collapse collapse show" aria-labelledby="notlarHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-12 notes">
                                        <div class="row">
                                            <div class="col-md-5" style="position: relative">
                                                <div class="loader-body note_add_loader"  style="display: none">
                                                    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
                                                </div>
                                                <div class="col-md-12 p-0 bg-success text-white text-center rounded-top">
                                                    Ekli Notlar
                                                </div>
                                                @php
                                                    $selectedNotesArray = array();
                                                @endphp
                                                <select class="form-control rounded-0 rounded-bottom" name="not[]" id="selected_notes" multiple id="" style="height: 165px;">
                                                    @foreach($expertise['getBodyworkNotes'] as $note)
                                                        @php
                                                            array_push($selectedNotesArray,$note['note'])
                                                        @endphp
                                                        <option value="{{$note['id']}}">{{$note['note']}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-2 text-center d-flex flex-column justify-content-center align-items-center">
                                                <button type="button" class="col-12 btn delete_selected_button @if(!$expertise['is_closed']) btn-danger @else border btn-disabled @endif btn-sm" @if($expertise['is_closed']) disabled @endif>
                                                    Not Sil
                                                </button>
                                                <button onclick="$('#notesModal').modal('toggle')" type="button" class="col-12 btn  btn-sm mt-1 @if(!$expertise['is_closed']) btn-info @else border btn-disabled @endif " @if($expertise['is_closed']) disabled @endif>
                                                    Not Tanımla
                                                </button>
                                                <button type="button" class="col-12 btn add_note_select @if(!$expertise['is_closed']) btn-success @else border btn-disabled @endif btn-sm mt-1" @if($expertise['is_closed']) disabled @endif>
                                                    Not Taşı
                                                </button>
                                            </div>
                                            <div class="col-md-5" style="position: relative">
                                                <div class="loader-body note_add_loader"  style="display: none">
                                                    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
                                                </div>
                                                <input class="form-control mb-1 pt-1 pb-1" placeholder="Hazır Notlar İçinde Ara..." type="text"  id="search_input">
                                                <select class="form-control" name="all_note[]" id="all_notes" multiple id="" style="height: 150px;">
                                                    @foreach($expertise['allNotes'] as $notes)
                                                        @if(!in_array($notes->note,$selectedNotesArray))
                                                            <option ondblclick="addNoteSelect(['{{$notes->note}}'],1)" value="{{$notes->note}}">{{$notes->note}}</option>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-2">
                @include('pages.expertise.right_tabs')
            </div>
            @if(!$expertise['is_closed'])
                <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                    @if($authUser->user_role_group_id != 34)
                        <button type="button" class="btn btn-info mt-3 mb-3 only_save"><i class="fa fa-save"></i> Kaydet</button>
                    @endif
                </div>
            @endif
        </div>
        <input type="hidden" name="close" @if($expertise['is_closed'] == 1) value="1" @else value="0" @endif>
    </form>
    <form id="colorForm">
    </form>

    <div class="modal fade" id="notesModal" tabindex="-1"
         aria-labelledby="notesModal" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Not Tanımla
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Not</small></label>
                            <input type="text" class="form-control" name="bodywork_notes_modal">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger bodywork_modal_save_button" >Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <img id="originalImage" src="/storage/{{ \App\Models\CarCaseType::where('id',\App\Models\Car::where('id',\App\Models\Expertise::where('uuid',$expertise['uuid'])->first()?->car_id)->first()?->car_case_type_id)->first()?->image }}" class="img-fluid d-none">
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <!-- Select2 Cdn -->
    <script src="/assets/js/select2.min.js"></script>
    <script>
        isTabActive = true;
        var canvasEdited = false;
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                isTabActive = true;
            } else {
                isTabActive = false;
            }
        });

        var imageVersion = $('#originalImage').attr('src').replace("/storage/car_case_type/","").replace(".jpg","");

        $('.kaporta-checkboxes td').click(function(e) {
            var $checkbox = $(this).find('input[type="checkbox"]');
            if ($checkbox.length > 0 && e.target !== $checkbox[0]) {
                // Tıklanan alan bir checkbox değilse ve checkbox varsa, checkbox'u tıklayalım
                $checkbox[0].click();
            }
        });

        $('#saveImageButton').on('click', function () {
            let array = [];
            let xArray = $('input[name="coordinates_x[]"]');
            let yArray = $('input[name="coordinates_y[]"]');
            let colorArray = $('input[name="coordinates_color[]"]');

            for (let i = 0; i < xArray.length; i++) {
                let x = $(xArray[i]).val();
                let y = $(yArray[i]).val();
                let color = $(colorArray[i]).val();
                array.push({ x, y, color });
            }

            $.ajax({
                url: "{{ route('api.addBodyworkCoordinate') }}",
                type: "post",
                data: {
                    coordinates: JSON.stringify(array),
                    imageVersion: imageVersion,
                    _token: "{{ @csrf_token() }}",
                    expertise_uuid: $('input[name=expertise_id]').val(),
                    image_client_width:$('#myImage').innerWidth(),
                    image_client_height:$('#myImage').innerHeight()
                },
                success: function (response) {
                    $('.only_save').removeAttr('disabled');
                    canvasEdited = false;
                },
                error: function (error) {
                    // Hata olunca yapılacak işlemler
                    console.log(error);
                }
            });
        });


        (function (){
            "use strict"
            $(".select2").select2({
                placeholder: "Ara.."
            });
        })();
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
        function addNoteSelect(notes,type = 1){

            $('.note_add_loader').show();
            $.ajax({
                url: "{{ route('updateExpertiseNote') }}",
                type: "post",
                data: {
                    selectedNotes : JSON.stringify(notes),
                    type:'bodywork',
                    _token:"{{ @csrf_token() }}",
                    expertise_id:$('input[name=expertise_id]').val(),
                },
                success: function (response) {
                    if(response.success){
                        $('#notesModal').modal('hide')
                        Toast.fire({
                            icon: "success",
                            title: "Not Kayıt Edildi."
                        });
                        if(type == 1){
                            $("#all_notes option:selected").each(function () {
                                $(this).remove();
                            });
                        }

                        var option_text = '';
                        $.each(response.ExperBodyworkNote, function(key, value) {
                            option_text += '<option ondblclick="addNoteSelect([\"'+value['note']+'\"],1)" value="' + value['id'] + '">' + value['note'] + '</option>';
                            console.log(option_text)
                        });
                        $('#selected_notes').html(option_text)
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Not Eklenirken Bir Hata Oluştu"
                        });
                    }
                    $('.note_add_loader').hide();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        }

        function kaporta_check_list(key){
            var selectedValues = $('.kaporta_check_tr' + key + ' input[type=checkbox]:checked').map(function () {
                return $(this).val();
            }).get();
            if(selectedValues.indexOf('orijinal') !== -1){
                $('.kaporta_check_tr'+key).find('td:eq(1)').addClass('orijinal');

                $('.kaporta_check_tr'+key).find('td:eq(2),td:eq(3)').removeClass();
                $('.kaporta_check_tr' + key + ' input[type=checkbox]:checked').not('[value="orijinal"]').not('[value="duz"]').prop('checked', false);

            }else{
                $('.kaporta_check_tr'+key).find('td:eq(1)').removeClass();
            }

            if(selectedValues.indexOf('boyali') !== -1){
                $('.kaporta_check_tr'+key).find('td:eq(1)').removeClass();
                $('.kaporta_check_tr'+key).find('td:eq(2)').addClass('boyali');
                $('.kaporta_check_tr' + key + ' input[type=checkbox][value="orijinal"]:checked').prop('checked', false);
            }else{
                $('.kaporta_check_tr'+key).find(' td:eq(2)').removeClass();
            }

            if(selectedValues.indexOf('degisen') !== -1){
                $('.kaporta_check_tr'+key).find('td:eq(1)').removeClass();
                $('.kaporta_check_tr'+key).find('td:eq(3)').addClass('degisen');
                $('.kaporta_check_tr' + key + ' input[type=checkbox][value="orijinal"]:checked').prop('checked', false);
            }else{
                $('.kaporta_check_tr'+key).find(' td:eq(3)').removeClass();
            }
            if(selectedValues.indexOf('duz') !== -1){
                $('.kaporta_check_tr'+key).find('td:eq(4)').addClass('degisen');
            }else{
                $('.kaporta_check_tr'+key).find(' td:eq(4)').removeClass();
            }


        }
        $('input[type="checkbox"]').on('change', function() {
            var $checkbox = $(this);
            var $row = $checkbox.closest('tr');
            var checkedValues = $('input[name="' + $checkbox.attr('key') + '[]"]:checked').map(function(){
                return this.value;
            }).get();
            console.log(checkedValues.length)
            if(checkedValues.length > 0){
                $('.required_input_'+$checkbox.attr('key')).removeAttr('required','')

            }else{
                $('.required_input_'+$checkbox.attr('key')).attr('required','')
            }

            // Orijinal checkbox kontrolü
            if ($checkbox.val() === 'orijinal' && $checkbox.prop('checked')) {
                $row.find('[value="boyali"], [value="degisen"]').prop('checked', false);
            }

            // Boyalı checkbox kontrolü
            if ($checkbox.val() === 'boyali' && $checkbox.prop('checked')) {
                $row.find('[value="orijinal"]').prop('checked', false);
            }

            // Değişen checkbox kontrolü
            if ($checkbox.val() === 'degisen' && $checkbox.prop('checked')) {
                $row.find('[value="orijinal"]').prop('checked', false);
            }

            kaporta_check_list($checkbox.attr('key'));
        });
        $('.add-note').on('click',function (){
            let $val = $('.search-note').val();
            if ($val){
                $('.notes').append('<input class="form-control" name="not[]" form="mainForm" value="'+$val+'">')
                $('.search-note').val('');
                $('.find-note').css('display','none')
            }
        })


        @if(!auth('customer')->check())
        document.addEventListener('DOMContentLoaded', function () {
            var canvas = document.getElementById('canvas');
            var ctx = canvas.getContext('2d');
            var image = document.getElementById('myImage');
            var drawingStack = [];
            var currentStep = -1;

            @if($expertise['coordinates'] != [])
                @foreach(json_decode($expertise['coordinates']['coordinates'],true) as $expertiseCoordinate)
                    drawingStack.push({ x: {{ $expertiseCoordinate['x'] }}, y: {{ $expertiseCoordinate['y'] }}, color: '{{ __('arrays.bodywork_colors')[$expertiseCoordinate['color']] }}' });
                    currentStep++;
                @endforeach
            @endif
                    console.log(image.width,image.clientWidth,$('#myImage').innerWidth())
            canvas.width = image.naturalWidth;
            canvas.height = image.naturalHeight;

            // Resmi canvas'a çiz
            ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

            if (ctx.getImageData(0, 0, canvas.width, canvas.height).data.every(value => value === 0)) {
                Toast.fire({
                    icon: "error",
                    title: "Kaporta Resmi Çizilemedi! Sayfa Yenileniyor!"
                });
                setTimeout(function (){
                    window.location.reload()
                },1000)
            }

            image.style.display = 'none'

            function draw() {
                @if($expertise['created_at'] > '2024-05-28 00:00:00')
                $('input[name="coordinates_x[]"]').remove()
                $('input[name="coordinates_y[]"]').remove()
                $('input[name="coordinates_color[]"]').remove()
                @endif

                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

                let scaleX = 1, scaleY = 1;
                @if($expertise['coordinates'] != [])

                    if ({{ $expertise['coordinates']['image_client_width'] }} < canvas.width) {
                        scaleX = {{ $expertise['coordinates']['image_client_width'] }} / canvas.width;
                        scaleY = {{ $expertise['coordinates']['image_client_height'] }} / canvas.height;
                    } else {
                        scaleX = canvas.width / {{ $expertise['coordinates']['image_client_width'] }};
                        scaleY = canvas.height / {{ $expertise['coordinates']['image_client_height'] }};
                    }
                @endif



                for (var i = 0; i <= currentStep; i++) {
                    var step = drawingStack[i];
                    // Koordinatları ölçekleyin
                    var scaledX = step.x * scaleX;
                    var scaledY = step.y * scaleY;
                    ctx.fillStyle = step.color;
                    ctx.fillRect(scaledX, scaledY, 12, 12);
                    @if($expertise['created_at'] > '2024-05-28 00:00:00')
                        let clr = 4;
                        if(step.color === 'yellow')
                            clr = 1;
                        else if(step.color === 'blue')
                            clr = 2;
                        else if(step.color === 'red')
                            clr = 3;
                        $('#mainForm').append("<input type='hidden' name='coordinates_x[]' value='"+scaledX+"'/>")
                        $('#mainForm').append("<input type='hidden' name='coordinates_y[]' value='"+scaledY+"'/>")
                        $('#mainForm').append("<input type='hidden' name='coordinates_color[]' value='"+clr+"'/>")
                    @endif

                }
            }
            draw();
            canvas.addEventListener('click', function (e) {
                var x = (e.clientX - 7) - canvas.getBoundingClientRect().left;
                var y = (e.clientY - 7) - canvas.getBoundingClientRect().top;

                // 10x10 piksel boyutunda bir kare çiz
                var color = document.querySelector('input[name="color"]:checked').value;

                // Clear redo steps
                drawingStack = drawingStack.slice(0, currentStep + 1);
                console.log(x,y)
                // Save the current state
                drawingStack.push({ x: x, y: y, color: color });
                currentStep++;

                $('.only_save').attr('disabled','true');
                canvasEdited = true;

                // Draw on the canvas
                draw();
                @if($expertise['created_at'] < '2024-05-28 00:00:00')
                    if ($('#mainForm input[name="canvas"]').length > 0)
                        $('#mainForm input[name="canvas"]').remove()
                    $('#mainForm').append('<input type="hidden" name="canvas" value="' + canvas.toDataURL() + '"/>')
                @endif
            });

            document.getElementById('undoButton').addEventListener('click', function () {
                if (currentStep >= 0) {
                    currentStep--;
                    draw();
                }
            });

            document.getElementById('redoButton').addEventListener('click', function () {
                if (currentStep < drawingStack.length - 1) {
                    currentStep++;
                    draw();
                }
            });
            @if($expertise['created_at'] < '2024-05-28 00:00:00')
                $('#mainForm').submit(function (event) {
                    event.preventDefault();
                    $('#mainForm').append('<input type="hidden" name="canvas" value="' + canvas.toDataURL() + '"/>')

                    $('#mainForm').unbind('submit').submit()
                });
            @endif
            var expertisesIndexRoute = "{{ route('expertises.index') }}";
            setInterval(function (){
                if (isTabActive) {
                    $.ajax({
                        url: "{{ route('updateExpertise') }}",
                        type: "post",
                        data: $('#mainForm').serialize(),
                        success: function (response) {
                            // window.location.href = expertisesIndexRoute;
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                        }
                    });
                }
            },30 * 1000)
            $('.only_save').click(function(){
                let close = 1;
                $('#mainForm [required]').each(function() {
                    var fieldValue = $(this).val();
                    if (fieldValue === '') {
                        close = 0;
                    }
                });
                if (canvasEdited){
                    Toast.fire({
                        icon: "error",
                        title: "Görseli Kaydetmediniz!",
                    });
                    return false;
                }
                $('input[name="close"]').val(close)
                @if($expertise['created_at'] < '2024-05-28 00:00:00')
                    $('#mainForm').append('<input type="hidden" name="canvas" value="' + canvas.toDataURL() + '"/>')
                @endif
                let checkboxes = validateCheckboxes();
                console.log(checkboxes);
                if(checkboxes)
                {
                    $.ajax({
                        url: "{{ route('updateExpertise') }}",
                        type: "post",
                        data: $('#mainForm').serialize(),
                        success: function (response) {
                            console.log(response);
                            if (response.success) {
                                Toast.fire({
                                    icon: response.type ? response.type : 'success',
                                    title: "Bilgiler Kayıt Edildi." + (response.message ? response.message : '')
                                }).then(function(){
                                    if(response.return_url != ''){
                                        window.location.href = response.return_url;
                                    }
                                });
                            } else {
                                Toast.fire({
                                    icon: "error",
                                    title: "İşlem Başarısız!",
                                });
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                        }
                    });
                }
                else{
                    Toast.fire({
                        icon: "error",
                        title: "Tüm Alanları Doldurunuz!",
                    });
                }

            });

            document.getElementById('resetButton').addEventListener('click', function () {
                image = document.getElementById('originalImage')
                // Resmi canvas'a çiz
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

                // Reset drawing stack and current step
                drawingStack = [];
                currentStep = -1;
            });
        });
        @endif


        @if(auth('customer')->check())
        $('input').attr('disabled','true').removeAttr('required')
        $('select').attr('disabled','true').removeAttr('required')
        $('textarea').attr('disabled','true').removeAttr('required')
        $("button, input[type='submit']").filter(function() {
            return $(this).attr('type') !== 'button';
        }).remove();
        $('form').attr('action','')
        $('.add-note').remove()
        @endif

        @if(!auth('customer')->check() && $expertise['kaporta_kontrol_user'] > 0 && $expertise['kaporta_kontrol_user'] != $authUser->id)
        alert('Bu İşlem Başka Kullanıcıya Ait!')
        @endif

        $('.search-note').on('keyup',function (){
            let $val = $(this).val();
            if ($(this).val().length > 2){
                $.ajax({
                    url: "{{ route('api.getNotes') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','search':$val,'key':'bodywork'} ,
                    success: function (response) {
                        $('.find-note').css('display','block')
                        if (response.items.length > 0){
                            let html = ''
                            $.each(response.items,function (index,item){
                                html += '<div class="filter-item" data-message="'+item.note+'">'+item.note+'</div>'
                            })
                            $('.find-note').html(html)

                            $('.find-note .filter-item').on('click',function (){
                                $('.notes').append('<input class="form-control" name="not[]" form="mainForm" value="'+$(this).data('message')+'">')
                            })
                        }
                    }
                });
            }else{
                $('.find-note').css('display','none')
            }
        })



        /*Notes New Version Start*/
        $("#search_input").on("input", function () {
            var searchText = $(this).val().toLowerCase();
            $("#all_notes option").each(function () {
                var optionText = $(this).text().toLowerCase();
                $(this).toggle(optionText.indexOf(searchText) > -1);
            });
        });
        $('.add_note_select').click(function (){
            var selectedOptions = $("#all_notes").val();
            if(selectedOptions.length > 0){
                addNoteSelect(selectedOptions)
            }
        })

        $('.bodywork_modal_save_button').click(function(){
            var array_notes = [$('input[name=bodywork_notes_modal]').val()]
            addNoteSelect(array_notes,2)
            $('#notesModal').modal('hide')
        })

        $('.delete_selected_button').click(function(){
            var selected_notes = $('#selected_notes').val();
            if(selected_notes.length > 0){
                Swal.fire({
                    title: "Emin misiniz?",
                    text: "Seçili Notlar Silinecek",
                    icon: "error",
                    showCancelButton: true,
                    confirmButtonColor: "#d33",
                    cancelButtonColor: "#3085d6",
                    confirmButtonText: "Evet, Sil!",
                    cancelButtonText: "Hayır, Silme!"
                }).then((result) => {
                    $('.note_add_loader').show();
                    if (result.isConfirmed) {
                        $.ajax({
                            url: "{{ route('deleteExpertiseNote') }}",
                            type: "post",
                            data: {
                                selectedNotes : JSON.stringify(selected_notes),
                                type:'bodywork',
                                _token:"{{ @csrf_token() }}",
                                expertise_id:$('input[name=expertise_id]').val(),
                            },
                            success: function (response) {
                                if(response.success){

                                    var option_text = '';
                                    $.each(response.selectedNote, function(key, value) {
                                        option_text += '<option ondblclick="addNoteSelect([\"'+value+'\"],1)" value="' + value + '">' + value + '</option>';
                                    });
                                    $('#all_notes').html(option_text)


                                    var option_text_select = '';
                                    $.each(response.ExperBodyworkNote, function(key, value) {
                                        option_text_select += '<option ondblclick="addNoteSelect([\"'+value+'\"],1)" value="' + value.id + '">' + value.note + '</option>';
                                    });
                                    $('#selected_notes').html(option_text_select)
                                    Toast.fire({
                                        icon: "success",
                                        title: "Notlar Silindi"
                                    });
                                }else{
                                    Toast.fire({
                                        icon: "error",
                                        title: "Not Eklenirken Bir Hata Oluştu"
                                    });
                                }
                                $('.note_add_loader').hide();
                            },
                            error: function(jqXHR, textStatus, errorThrown) {
                                console.log(textStatus, errorThrown);
                            }
                        });
                    }
                });
            }

        })

        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
        function validateCheckboxes() {
            var rows = document.querySelectorAll('.kaporta-checkboxes');
            var isValid = false;

            for (var i = 0; i < rows.length; i++) {
                var checkboxes = rows[i].querySelectorAll('input[type="checkbox"]');
                var note = rows[i].querySelector('input[type="text"]');

                var checked = false;

                for (var j = 0; j < checkboxes.length; j++) {
                    if (checkboxes[j].checked) {
                        checked = true;
                        break;
                    }
                }

                if (!checked && note.value.length === 0) {
                    isValid = false;
                    break;
                } else {
                    isValid = true;
                }
            }

            return isValid;
        }

        /*Notes New Version finish*/
    </script>
@endpush
