@extends('pages.build')
@section('title','Taslak <PERSON>')
@push('css')
    <style>
        .filtreli{
            display: none;
        }
        th{
            background-color: #cecece57 !important;
            font-weight: bold;
        }
        .kaporta {
            width: auto;
        }
        .box{
            width: 25px;
            height: 25px;
            border-radius: 50%;
            border: 1px solid;
        }
        .inside{
            position: absolute;
            z-index: 1;
        }
        .green{
            background-color: lawngreen
        }
        .orange{
            background-color: #fc7100
        }
        .yellow {
            -webkit-print-color-adjust: exact; /* Safari & Chrome */
            print-color-adjust: exact; /* Firefox & Edge */
            background-color: yellow !important;
        }
        .red {
            background-color: red
        }
        .purple {
            background-color: purple
        }
        .transparent{
            background-color: rgba(255,255,255,0)
        }
        .orj:after{
            content: 'ORJ.';
            position: absolute;
            left: 2rem;
            font-weight: 700;
        }
        .table-col-6{
            padding: .35rem !important;
        }
        .table td, .table th{
            padding: .35rem !important;
            font-size: .513rem;
            font-weight: bold;
        }

        @media print {
            @page { margin: 0; }
            body * {
                visibility: hidden;
            }
            .printable * {
                visibility: visible;
            }
            .printable {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                margin: 0;
                padding: 0;
            }
            .non-printable, .print {
                visibility: hidden !important;
                display: none !important;
            }
            .container {
                width: 100%;
                margin: 0;
                padding: 0;
                left: 0;
            }
            .green {
                -webkit-print-color-adjust: exact; /* Safari & Chrome */
                print-color-adjust: exact; /* Firefox & Edge */
                background-color: lawngreen !important;
            }
            .orange {
                -webkit-print-color-adjust: exact; /* Safari & Chrome */
                print-color-adjust: exact; /* Firefox & Edge */
                background-color: #fc7100 !important;
            }
            .yellow {
                -webkit-print-color-adjust: exact; /* Safari & Chrome */
                print-color-adjust: exact; /* Firefox & Edge */
                background-color: yellow !important;
            }
            .red {
                -webkit-print-color-adjust: exact; /* Safari & Chrome */
                print-color-adjust: exact; /* Firefox & Edge */
                background-color: red !important;
            }
            .purple {
                -webkit-print-color-adjust: exact; /* Safari & Chrome */
                print-color-adjust: exact; /* Firefox & Edge */
                background-color: purple !important;
                
            }
            .bodywork-table {
                flex-wrap: nowrap !important;
                margin: 0;
                padding: 0;
            }
            .bodywork-table-left {
                margin: 0 !important;
                padding: 0 !important;
                float: left !important;
                max-width: 50%;
            }
            .bodywork-table-right {
                margin: 0 !important;
                padding: 0 !important;
                float: right !important;
                max-width: 50%;
            }
            .filtreli {
                display: none;
            }
            th{
                background-color: #cecece57 !important;
                font-weight: bold;
            }
            .kaporta {
                width: auto;
            }
            .box{
                width: 25px;
                height: 25px;
                border-radius: 50%;
                border: 1px solid;
            }
            .inside{
                position: absolute;
                z-index: 1;
            }
            .transparent {
                background-color: rgba(255,255,255,0)
            }
            .orj:after{
                content: 'ORJ.';
                position: absolute;
                left: 2rem;
                font-weight: 700;
            }
            .table-col-6{
                padding: .35rem !important;
            }
            .table td, .table th{
                padding: .35rem !important;
                font-size: .513rem;
                font-weight: bold;
            }
            .card {
                border: 0 !important;
                box-shadow: 0 !important;
                margin: 0 !important;
            }
            .card-body {
                margin-left: 0 !important;
                margin-right: 0 !important;
            }
            .no-margin {
                margin: 0 !important;
            }
            .no-padding {
                padding: 0 !important;
            }
        }
    </style>
@endpush
@section('content')
<div class="row row-sm printable">
    <div class="col-lg-12 col-md-12 no-margin no-padding">
        <div class="card custom-card">
            <div class="card-header d-flex justify-content-end non-printable"> 
                <div class="btn-list"> 
                    <button type="button" class="btn ripple btn-info mb-1" onclick="javascript:window.print();"><i class="fe fe-printer me-1"></i> Yazdır</button> 
                </div> 
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-invoice table-bordered">
                        <tbody>
                            <tr class="table-col-6">
                                <td style="width: 20%">Bayi:</td>
                                <td style="width: 40%"><b>{{ $expertise['getBranch'] }}</b></td>
                                <td style="width: 10%">Marka / Model:</td>
                                <td style="width: 20%">{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                                <td style="width: 10%">KM:</td>
                                <td style="width: 20%"><b>{{ number_format($expertise['km'] ?? 0,0,'.','.') }}</b></td>
                            </tr>
                            <tr class="table-col-6">
                                <td style="width: 20%">Belge No:</td>
                                <td style="width: 40%"><b class="text-danger">{{ $expertise['belge_no'] }}</b></td>
                                <td style="width: 10%">Model Yılı:</td>
                                <td style="width: 20%"><b>{{ $expertise['model_yili'] }}</b></td>
                                <td style="width: 10%">Motor Hacmi:</td>
                                <td style="width: 20%">
                                    <b>
                                        @if(is_numeric($expertise['motor_hacmi']))
                                            {{ number_format($expertise['motor_hacmi'] ?? 0,0,'.','.') }}
                                        @else
                                            {{$expertise['motor_hacmi']}}
                                        @endif
                                    </b>
                                </td>
                            </tr>
                            <tr class="table-col-6">
                                <td style="width: 20%">Giriş Tarihi ve Saati:</td>
                                <td style="width: 40%"><b>{{ $expertise['created_at'] }}</b></td>
                                <td style="width: 10%">Şase No:</td>
                                <td class="filtresiz" style="width: 20%"><b>
                                        {{ $expertise['sase_no'] }}
                                    </b>
                                </td>
                                <td class="filtreli" style="width: 20%"><b>
                                        {{ !empty($expertise['sase_no']) ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                                    </b>
                                </td>
                                <td style="width: 10%">Yakıt Tipi:</td>
                                <td style="width: 20%"><b>{{ $expertise['getFuel'] }}</b></td>
                            </tr>
                            <tr class="table-col-6">
                                <td style="width: 20%">Çıkış Tarihi ve Saati:</td>
                                <td style="width: 40%"><b>{{ $expertise['cikis_tarihi'] }}</b></td>
                                <td style="width: 10%">Plaka:</td>
                                <td class="filtresiz" style="width: 20%"><b>
                                        {{ $expertise['plaka'] }}
                                    </b>
                                </td>
                                <td class="filtreli" style="width: 20%"><b>
                                        {{ !empty($expertise['plaka']) ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                                    </b>
                                </td>
                                <td style="width: 10%">Vites Tipi:</td>
                                <td style="width: 20%"><b>{{ $expertise['getGear'] }}</b></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="table-responsive mt-3">
                    <div class="container">
                        <div class="row">
                            <div class="col-2 mt-3">
                                <div><div class="box green"></div>ORJİNAL PARÇA</div>
                                <div><div class="box yellow"></div>BOYALI PARÇA</div>
                                <div><div class="box red"></div>DEĞİŞEN PARÇA</div>
                                <div><div class="box purple"></div>DÜZELTME</div>
                                <div><div class="box transparent"></div>SÖKÜLÜP TAKILMIŞ</div>
                            </div>
                            <div class="col-10">
                                <img id="myImage" class="kaporta non-printable" src="{{ $expertise['bodywork_open_image'] }}">
                                <canvas class="kaporta" id="canvas"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row row-sm mb-5 bodywork-table justify-content-start mt-3">
                    <div class="col-md-6 bodywork-table-left table-responsive">
                        <table class="table table-bordered">
                            <thead>
                            <tr class="text-center">
                                <th width="45%">PARÇA ADI</th>
                                <th width="10%">ORJ.</th>
                                <th width="10%">BOY.</th>
                                <th width="10%">DEĞ.</th>
                                <th width="10%">DÜZ.</th>
                                <th width="15%">AÇIKLAMA</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($bodyworks1 as $key => $bodywork)
                                <tr>
                                    <td>{{ $bodywork }}</td>
                                    <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                    <div class="col-md-6 bodywork-table-right table-responsive">
                        <table class="table table-bordered">
                            <thead>
                            <tr class="text-center">
                                <th>PARÇA ADI</th>
                                <th>ORJ.</th>
                                <th>BOY.</th>
                                <th>DEĞ.</th>
                                <th>DÜZ.</th>
                                <th>AÇIKLAMA</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach($bodyworks2 as $key => $bodywork)
                                <tr>
                                    <td>{{ $bodywork }}</td>
                                    <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-end non-printable"> 
                <div class="btn-list"> 
                    <button type="button" class="btn ripple btn-info mb-1" onclick="javascript:window.print();"><i class="fe fe-printer me-1"></i> Yazdır</button> 
                </div> 
            </div>
        </div>
    </div>
</div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script>
        isTabActive = true;
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                isTabActive = true;
            } else {
                isTabActive = false;
            }
        });
        document.addEventListener('DOMContentLoaded', function () {
            var canvas = document.getElementById('canvas');
            var ctx = canvas.getContext('2d');
            var image = document.getElementById('myImage');
            var drawingStack = [];
            var currentStep = -1;

            @if($expertise['coordinates'] != [])
                @foreach($expertise['coordinates'] as $expertiseCoordinate)
                    @foreach($expertiseCoordinate['coordinates'] as $bodyworkCoordinate)
                        drawingStack.push({ 
                            x: {{ $bodyworkCoordinate['x'] }}, 
                            y: {{ $bodyworkCoordinate['y'] }}, 
                            color: '{{ $bodyworkCoordinate['color'] }}',
                            title: '{{ $bodyworkCoordinate['title'] }}'
                        });

                        currentStep++;
                    @endforeach
                @endforeach
            @endif
            console.log(image.width,image.clientWidth, $('#myImage').innerWidth())
            
            // canvas.width = image.naturalWidth;
            // canvas.height = image.naturalHeight;

            canvas.width = 600;
            canvas.height = 660;
            // ctx.rotate(90);
            // Resmi canvas'a çiz
            ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

            if (ctx.getImageData(0, 0, canvas.width, canvas.height).data.every(value => value === 0)) {
                Toast.fire({
                    icon: "error",
                    title: "Kaporta Resmi Çizilemedi! Sayfa Yenileniyor!"
                });
                setTimeout(function (){
                    window.location.reload()
                },1000)
            }

            image.style.display = 'none'

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

                for (var i = 0; i <= currentStep; i++) {
                    var step = drawingStack[i];

                    // Draw title
                    ctx.fillStyle = 'black';
                    ctx.font = "14px Arial";
                    ctx.fillText(step.title, step.x + 15, step.y + 5);

                    // Draw circle
                    ctx.fillStyle = step.color;
                    ctx.beginPath();
                    ctx.arc(step.x, step.y, 10, 0, Math.PI * 2);
                    ctx.fill();
                    ctx.strokeStyle = 'black'; // Kenarlık rengi
                    ctx.lineWidth = 1; // Kenarlık kalınlığı
                    ctx.stroke(); // Kenarlık çizimi
                }
            }
            draw();
            canvas.addEventListener('click', function (e) {
                var x = e.clientX - canvas.getBoundingClientRect().left;
                var y = e.clientY - canvas.getBoundingClientRect().top;

                var color = document.querySelector('input[name="color"]:checked').value;

                drawingStack = drawingStack.slice(0, currentStep + 1);
                drawingStack.push({ x: x, y: y, color: color });
                currentStep++;
                draw();
            });


            document.getElementById('undoButton').addEventListener('click', function () {
                if (currentStep >= 0) {
                    currentStep--;
                    draw();
                }
            });

            document.getElementById('redoButton').addEventListener('click', function () {
                if (currentStep < drawingStack.length - 1) {
                    currentStep++;
                    draw();
                }
            });

            $('#mainForm').submit(function (event) {
                event.preventDefault();
                $('#mainForm').append('<input type="hidden" name="canvas" value="' + canvas.toDataURL() + '"/>')

                $('#mainForm').unbind('submit').submit()
            });


            setInterval(function (){
                if(isTabActive){
                $.ajax({
                    url: "{{ route('updateExpertise') }}",
                    type: "post",
                    data: $('#mainForm').serialize(),
                    success: function (response) {},
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
                }

            },30 * 1000)
            $('.only_save').click(function(){
                let close = 1;
                $('#mainForm [required]').each(function() {
                    var fieldValue = $(this).val();
                    if (fieldValue === '') {
                        close = 0;
                    }
                });
                $('input[name="close"]').val(close)

                $('#mainForm').append('<input type="hidden" name="canvas" value="' + canvas.toDataURL() + '"/>')
                $.ajax({
                    url: "{{ route('updateExpertise') }}",
                    type: "post",
                    data: $('#mainForm').serialize(),
                    success: function (response) {
                        Toast.fire({
                            icon: "success",
                            title: "Bilgiler Kayıt Edildi."
                        });
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            });
        });
    </script>
@endpush
