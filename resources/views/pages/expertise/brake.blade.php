@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Ekspertiz - Fren')
@section('content')
    @include('pages.expertise.tabs')
    <form id="mainForm" method="post" action="{{ route('updateExpertise') }}" enctype="multipart/form-data">@csrf
        <input type="hidden" name="expertise_id" value="{{ $_GET['expertise_id'] }}">
        <input type="hidden" name="type" value="{{ $_GET['type'] }}">
        <div class="row">
            <div class="col-md-10">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item" style="position: sticky;top: 1rem;z-index: 2">
                        <h2 class="accordion-header" id="belgeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#belge" aria-expanded="true"
                                    aria-controls="belge">
                                Belge
                            </button>
                        </h2>
                        <div id="belge" class="accordion-collapse collapse show" aria-labelledby="belgeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-1">
                                        <label class="form-label">Personel</label>
                                        <input type="text" class="form-control form-control-sm" name="fren_kontrol_user" @if($expertise['fren_kontrol_user'] > 0) disabled value="{{ $expertise['fren_kontrol_user'] }}" @else @endif placeholder="Personel ID">
                                    </div>
                                    <div class="col-md-1">
                                        <label>Plaka</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['plaka'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>Şase</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['sase_no'] }}">
                                    </div>
{{--                                    <div class="col-md-2">--}}
{{--                                        <label>Hizmet</label>--}}
{{--                                        <input disabled class="form-control form-control-sm" value="@foreach($expertise['getStocks'] as $expertiseStock) {{ $expertiseStock['ad'] }}, @endforeach">--}}
{{--                                    </div>--}}
                                    <div class="col-md-2">
                                        <label>Tarih</label>
                                        <input type="datetime-local" class="form-control form-control-sm" readonly name="date" value="{{ $expertise['date'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>No</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['belge_no'] }}">
                                    </div>
                                    <div class="col-md-1">
                                        <label>KM</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['km'] }}">
                                    </div>
                                    <div class="col-md-1">
                                        <label>Model Yılı</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['car_model_year'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>Model</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['car_model'] }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="yanalKaymaHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#yanalKayma" aria-expanded="true"
                                    aria-controls="yanalKayma">
                                Yanal Kayma
                            </button>
                        </h2>
                        <div id="yanalKayma" class="accordion-collapse collapse show" aria-labelledby="yanalKaymaHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col">
                                        <label>Ön</label>
                                        <input class="form-control form-control-sm" required @if($expertise['is_closed'] || $expertise['editable'] == 0) disabled @endif name="yanal_kayma_on" value="{{ !is_null($expertise['yanal_kayma_on']) ? number_format((float)$expertise['yanal_kayma_on'], 2, ',', '.') : '' }}">
                                    </div>
                                    <div class="col">
                                        <label>Arka</label>
                                        <input class="form-control form-control-sm" required @if($expertise['is_closed'] || $expertise['editable'] == 0) disabled @endif name="yanal_kayma_arka" value="{{ !is_null($expertise['yanal_kayma_arka']) ? number_format((float)$expertise['yanal_kayma_arka'],2,',','.') : '' }}">
                                    </div>
                                    <div class="col mt-4">
                                        <button type="button" class="getDate btn btn-danger" style="display: none">Test Sonuçlarını Aktar</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="frenHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#fren" aria-expanded="true"
                                    aria-controls="fren">
                                Fren
                            </button>
                        </h2>
                        <div id="fren" class="accordion-collapse collapse show" aria-labelledby="frenHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Maksimum Kuvvet</label>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label>Sol Ön</label>
                                                    <input type="text" class="form-control form-control-sm" name="max_kuvvet_on_1" @if($expertise['is_closed'] || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['max_kuvvet_on_1']) ? number_format((float)$expertise['max_kuvvet_on_1'], 2, ',', '.') : '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label>Sağ Ön</label>
                                                    <input type="text" class="form-control form-control-sm" name="max_kuvvet_on_2" @if($expertise['is_closed'] || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['max_kuvvet_on_2']) ? number_format((float)$expertise['max_kuvvet_on_2'],2,',', '.') : '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label>Sol Arka</label>
                                                    <input type="text" class="form-control form-control-sm" name="max_kuvvet_arka_1" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['max_kuvvet_arka_1']) ? number_format((float)$expertise['max_kuvvet_arka_1'], 2, ',', '.') : '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label>Sağ Arka</label>
                                                    <input type="text" class="form-control form-control-sm" name="max_kuvvet_arka_2" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['max_kuvvet_arka_2']) ? number_format((float)$expertise['max_kuvvet_arka_2'], 2, ',', '.') : '' }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Dengesizlik Oranı</label>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <label>Ön</label>
                                                    <input type="text" class="form-control form-control-sm" name="dengesizlik_orani_on" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['dengesizlik_orani_on']) ? number_format((float)$expertise['dengesizlik_orani_on'], 2, ',', '.') : '' }}">
                                                </div>
                                                <div class="col-md-12">
                                                    <label>Arka</label>
                                                    <input type="text" class="form-control form-control-sm" name="dengesizlik_orani_arka" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['dengesizlik_orani_arka']) ? number_format((float)$expertise['dengesizlik_orani_arka'], 2, ',', '.') : '' }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label>Yalpa Oranı</label>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label>Sol Ön</label>
                                                    <input type="text" class="form-control form-control-sm" name="yalpa_orani_on_1" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['yalpa_orani_on_1']) ? number_format((float)$expertise['yalpa_orani_on_1'], 2, ',', '.') : '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label>Sağ Ön</label>
                                                    <input type="text" class="form-control form-control-sm" name="yalpa_orani_on_2" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['yalpa_orani_on_2']) ? number_format((float)$expertise['yalpa_orani_on_2'], 2, ',', '.') : '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label>Sol Arka</label>
                                                    <input type="text" class="form-control form-control-sm" name="yalpa_orani_arka_1" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['yalpa_orani_arka_1']) ? number_format((float)$expertise['yalpa_orani_arka_1'], 2, ',', '.') : '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label>Sağ Arka</label>
                                                    <input type="text" class="form-control form-control-sm" name="yalpa_orani_arka_2" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['yalpa_orani_arka_2']) ? number_format((float)$expertise['yalpa_orani_arka_2'], 2, ',', '.') : '' }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="suspansiyonHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#suspansiyon" aria-expanded="true"
                                    aria-controls="suspansiyon">
                                Süspansiyon
                            </button>
                        </h2>
                        <div id="suspansiyon" class="accordion-collapse collapse show" aria-labelledby="suspansiyonHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label>Sol Ön</label>
                                                    <input type="text" class="form-control form-control-sm" name="suspansiyon_on_1" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['suspansiyon_on_1']) ? number_format((float)$expertise['suspansiyon_on_1'], 2, ',', '.') : '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label>Sağ Ön</label>
                                                    <input type="text" class="form-control form-control-sm" name="suspansiyon_on_2" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['suspansiyon_on_2']) ? number_format((float)$expertise['suspansiyon_on_2'], 2, ',', '.') : '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label>Sol Arka</label>
                                                    <input type="text" class="form-control form-control-sm" name="suspansiyon_arka_1" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['suspansiyon_arka_1']) ? number_format((float)$expertise['suspansiyon_arka_1'], 2, ',', '.') : '' }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label>Sağ Arka</label>
                                                    <input type="text" class="form-control form-control-sm" name="suspansiyon_arka_2" @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @else required @endif value="{{ !is_null($expertise['suspansiyon_arka_2']) ? number_format((float)$expertise['suspansiyon_arka_2'], 2, ',', '.') : '' }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="elFreniHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#elFreni" aria-expanded="true"
                                    aria-controls="elFreni">
                                El Freni
                            </button>
                        </h2>
                        <div id="elFreni" class="accordion-collapse collapse show" aria-labelledby="elFreniHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label>El Freni Dengesizlik Oranı</label>
                                        <input class="form-control form-control-sm" required @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @endif name="el_freni_dengesizlik_orani" value="{{ !is_null($expertise['el_freni_dengesizlik_orani']) ? number_format((float)$expertise['el_freni_dengesizlik_orani'], 2, ',', '.') : '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label>El Freni Kuvvet Sol</label>
                                        <input class="form-control form-control-sm" required @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @endif name="el_freni_kuvvet_a" value="{{ !is_null($expertise['el_freni_kuvvet_a']) ? number_format((float)$expertise['el_freni_kuvvet_a'], 2, ',', '.') : '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label>El Freni Kuvvet Sağ</label>
                                        <input class="form-control form-control-sm" required @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @endif name="el_freni_kuvvet_b" value="{{ !is_null($expertise['el_freni_kuvvet_b']) ? number_format((float)$expertise['el_freni_kuvvet_b'], 2, ',', '.') : '' }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="dingilHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#dingil" aria-expanded="true"
                                    aria-controls="dingil">
                                Dingil
                            </button>
                        </h2>
                        <div id="dingil" class="accordion-collapse collapse show" aria-labelledby="dingilHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label>Ön Dingil Boşta Sürtünme Sol</label>
                                        <input class="form-control form-control-sm" required @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @endif name="on_dingil_bosta_a" value="{{ !is_null($expertise['on_dingil_bosta_a']) ? number_format((float)$expertise['on_dingil_bosta_a'], 2, ',', '.') : '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label>Ön Dingil Boşta Sürtünme Sağ</label>
                                        <input class="form-control form-control-sm" required @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @endif name="on_dingil_bosta_b" value="{{ !is_null($expertise['on_dingil_bosta_b']) ? number_format((float)$expertise['on_dingil_bosta_b'], 2, ',', '.') : '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label>Arka Dingil Boşta Sürtünme Sol</label>
                                        <input class="form-control form-control-sm" required @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @endif name="arka_dingil_bosta_a" value="{{ !is_null($expertise['arka_dingil_bosta_a']) ? number_format((float)$expertise['arka_dingil_bosta_a'], 2, ',', '.') : '' }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label>Arka Dingil Boşta Sürtünme Sağ</label>
                                        <input class="form-control form-control-sm" required @if($expertise['is_closed']  || $expertise['editable'] == 0) disabled @endif name="arka_dingil_bosta_b" value="{{ !is_null($expertise['arka_dingil_bosta_b']) ? number_format((float)$expertise['arka_dingil_bosta_b'], 2, ',', '.') : '' }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="notlarHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#notlar" aria-expanded="true"
                                    aria-controls="notlar">
                                Notlar (Boş Veriler Silinir)
                            </button>
                        </h2>
                        <div id="notlar" class="accordion-collapse collapse show" aria-labelledby="notlarHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-12 notes">
                                        <div class="row">
                                            <div class="col-md-5" style="position: relative">
                                                <div class="loader-body note_add_loader"  style="display: none">
                                                    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
                                                </div>
                                                <div class="col-md-12 p-0 bg-success text-white text-center rounded-top">
                                                    Ekli Notlar
                                                </div>
                                                @php
                                                 $selectedNotesArray = array();
                                                @endphp
                                                <select class="form-control rounded-0 rounded-bottom" name="not[]" id="selected_notes" multiple id="" style="height: 165px;">
                                                    @foreach($expertise['getNotes'] as $note)
                                                        @php
                                                        array_push($selectedNotesArray,$note['note'])
                                                        @endphp
                                                        <option value="{{$note['id']}}">{{$note['note']}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-2 text-center d-flex flex-column justify-content-center align-items-center">
                                                <button type="button" class="col-12 btn delete_selected_button @if(!$expertise['is_closed']) btn-danger @else border btn-disabled @endif btn-sm" @if($expertise['is_closed']) disabled @endif>
                                                    Not Sil
                                                </button>
                                                <button onclick="$('#notesModal').modal('toggle')" type="button" class="col-12 btn  btn-sm mt-1 @if(!$expertise['is_closed']) btn-info @else border btn-disabled @endif " @if($expertise['is_closed']) disabled @endif>
                                                    Not Tanımla
                                                </button>
                                                <button type="button" class="col-12 btn add_note_select @if(!$expertise['is_closed']) btn-success @else border btn-disabled @endif btn-sm mt-1" @if($expertise['is_closed']) disabled @endif>
                                                    Not Taşı
                                                </button>
                                            </div>
                                            <div class="col-md-5" style="position: relative">
                                                <div class="loader-body note_add_loader"  style="display: none">
                                                    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
                                                </div>
                                                <input class="form-control mb-1 pt-1 pb-1" placeholder="Hazır Notlar İçinde Ara..." type="text"  id="search_input">
                                                <select class="form-control form-control-sm" name="all_note[]" id="all_notes" multiple id="" style="height: 150px;">
                                                    @foreach($expertise['allNotes'] as $notes)
                                                        @if(!in_array($notes->note,$selectedNotesArray))
                                                            <option ondblclick="addNoteSelect(['{{$notes->note}}'],1)" value="{{$notes->note}}">{{$notes->note}}</option>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-2">
                @include('pages.expertise.right_tabs')
            </div>
            @if(!$expertise['is_closed'])
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12 ">

                <button type="button" class="btn btn-info mt-3 mb-3 only_save"><i class="fa fa-save"></i> Kaydet</button>
            </div>
            @endif
        </div>
        <input type="hidden" name="close" @if($expertise['is_closed'] == 1) value="1" @else value="0" @endif>
    </form>


    <div class="modal fade" id="notesModal" tabindex="-1"
         aria-labelledby="notesModal" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Not Tanımla
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Not</small></label>
                            <input type="text" class="form-control form-control-sm" name="brake_notes_modal">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger brake_modal_save_button" >Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <!-- Select2 Cdn -->
    <script src="/assets/js/select2.min.js"></script>
    <script>
        isTabActive = true;
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                isTabActive = true;
            } else {
                isTabActive = false;
            }
        });
        var warning_count = 1;
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
        function addNoteSelect(notes,type){
            $('.note_add_loader').show();
            $.ajax({
                url: "{{ route('updateExpertiseNote') }}",
                type: "post",
                data: {
                    selectedNotes : JSON.stringify(notes),
                    type:'brake',
                    _token:"{{ @csrf_token() }}",
                    expertise_id:$('input[name=expertise_id]').val(),
                },
                success: function (response) {
                    if(response.success){
                        $('#notesModal').modal('hide')
                        Toast.fire({
                            icon: "success",
                            title: "Not Kayıt Edildi."
                        });
                        if(type == 1){
                            $("#all_notes option:selected").each(function () {
                                $(this).remove();
                            });
                        }

                        var option_text = '';
                        $.each(response.ExperBrakeNote, function(key, value) {
                            option_text += '<option ondblclick="addNoteSelect([\"'+value['note']+'\"],1)" value="' + value['id'] + '">' + value['note'] + '</option>';
                            console.log(option_text)
                        });
                        $('#selected_notes').html(option_text)
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Not Eklenirken Bir Hata Oluştu"
                        });
                    }
                    $('.note_add_loader').hide();
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        }

        (function (){
            "use strict"
            $(".select2").select2({
                placeholder: "Ara.."
            });
            $('.only_save').click(function(){
                var empty = 0;
                var max_kuvvet_on_1 = $('input[name=max_kuvvet_on_1]').val()
                var max_kuvvet_on_2 = $('input[name=max_kuvvet_on_2]').val()
                var dengesizlik_orani_on = $('input[name=dengesizlik_orani_on]').val()
                var yalpa_orani_on_1 = $('input[name=yalpa_orani_on_1]').val()
                var yalpa_orani_on_2 = $('input[name=yalpa_orani_on_2]').val()
                var max_kuvvet_arka_1 = $('input[name=max_kuvvet_arka_1]').val()
                var max_kuvvet_arka_2 = $('input[name=max_kuvvet_arka_2]').val()
                var dengesizlik_orani_arka = $('input[name=dengesizlik_orani_arka]').val()
                var el_freni_dengesizlik_orani = $('input[name=el_freni_dengesizlik_orani]').val()
                var yalpa_orani_arka_1 = $('input[name=yalpa_orani_arka_1]').val()
                var yalpa_orani_arka_2 = $('input[name=yalpa_orani_arka_2]').val()

                if (
                    max_kuvvet_on_1 == 0 &&
                    max_kuvvet_on_2 == 0 &&
                    dengesizlik_orani_on == 0 &&
                    yalpa_orani_on_1 == 0 &&
                    yalpa_orani_on_2 == 0 &&
                    max_kuvvet_arka_1 == 0 &&
                    max_kuvvet_arka_2 == 0 &&
                    dengesizlik_orani_arka == 0 &&
                    el_freni_dengesizlik_orani == 0 &&
                    yalpa_orani_arka_1 == 0 &&
                    yalpa_orani_arka_2 == 0
                ){
                    empty = 1;
                }

                //süspansiyon
                var suspansiyon_on_1 = $('input[name=suspansiyon_on_1]').val()
                var suspansiyon_on_2 = $('input[name=suspansiyon_on_2]').val()
                var suspansiyon_arka_1 = $('input[name=suspansiyon_arka_1]').val()
                var suspansiyon_arka_2 = $('input[name=suspansiyon_arka_2]').val()

                if (
                    suspansiyon_on_1 == 0 &&
                    suspansiyon_on_2 == 0 &&
                    suspansiyon_arka_1 == 0 &&
                    suspansiyon_arka_2 == 0
                ){
                    empty = 1;
                }

                if(empty == 1 && warning_count == 1){
                    Toast.fire({
                        icon: "error",
                        title: "Cihazdan Okunan Değeri Manuel Giriniz."
                    });
                    warning_count = warning_count + 1;
                    $('html, body').animate({
                        scrollTop: $('#yanalKayma').offset().top
                    }, 1000);
                }else if(empty == 1 && warning_count > 1){
                    Toast.fire({
                        icon: "error",
                        title: "Otomatik Değer Okunmadı"
                    });
                    warning_count = warning_count + 1;
                    $('html, body').animate({
                        scrollTop: $('#yanalKayma').offset().top
                    }, 1000);

                }else{
                    let close = 1;
                    $('#mainForm [required]').each(function() {
                        var fieldValue = $(this).val();
                        if (fieldValue === '') {
                            close = 0;
                        }
                    });
                    $('input[name="close"]').val(close)

                    $.ajax({
                        url: "{{ route('updateExpertise') }}",
                        type: "post",
                        data: $('#mainForm').serialize(),
                        success: function (response) {
                            if (response.success == 'false' || response.success == false) {
                                Toast.fire({
                                    icon: response.type ? response.type : 'success',
                                    title:response.message
                                });
                            }else{
                                Toast.fire({
                                    icon: "success",
                                    title: "Bilgiler Kayıt Edildi."
                                }).then(function(){
                                    if(response.return_url != ''){
                                        window.location.href = response.return_url;
                                    }
                                });
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                        }
                    });
                }

            });
            setInterval(function (){
                if(isTabActive){
                    $.ajax({
                        url: "{{ route('updateExpertise') }}",
                        type: "post",
                        data: $('#mainForm').serialize(),
                        success: function (response) {

                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                        }
                    });
                }
            },30 * 1000)


            $(document).on("click",".getDate",function (){
                //var url = "{{ url("listDriveFile") }}/"+"{{ $expertise['plaka'] }}"
                $(this).html("Yükleniyor...").prop('disabled', true);
                var url = "{{ url("listDriveFile") }}/"+"33P987"
                $.get(url).then(data => {
                    var fren1Archive = data.firen_data;
                    var fren2Archive = data.firen_data2;
                    var fren1 = fren1Archive.split("#");
                    var fren2 = fren2Archive.split("#");
                    $("input[name='yanal_kayma_on']").val(fren1[0]);
                    $("input[name='yanal_kayma_arka']").val(fren2[0]);
                    $(this).html("Test Sonuçlarını Aktar").prop('disabled', false);
                });
            });


            /*Notes New Version Start*/
            $("#search_input").on("input", function () {
                var searchText = $(this).val().toLowerCase();
                $("#all_notes option").each(function () {
                    var optionText = $(this).text().toLowerCase();
                    $(this).toggle(optionText.indexOf(searchText) > -1);
                });
            });
            $('.add_note_select').click(function (){
                var selectedOptions = $("#all_notes").val();
                if(selectedOptions.length > 0){
                    addNoteSelect(selectedOptions)
                }
            })

            $('.brake_modal_save_button').click(function(){
                var array_notes = [$('input[name=brake_notes_modal]').val()]
                addNoteSelect(array_notes,2)
                $('#notesModal').modal('hide')
            })

            $('.delete_selected_button').click(function(){
                var selected_notes = $('#selected_notes').val();
                if(selected_notes.length > 0){
                    Swal.fire({
                        title: "Emin misiniz?",
                        text: "Seçili Notlar Silinecek",
                        icon: "error",
                        showCancelButton: true,
                        confirmButtonColor: "#d33",
                        cancelButtonColor: "#3085d6",
                        confirmButtonText: "Evet, Sil!",
                        cancelButtonText: "Hayır, Silme!"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            $('.note_add_loader').show();
                            $.ajax({
                                url: "{{ route('deleteExpertiseNote') }}",
                                type: "post",
                                data: {
                                    selectedNotes : JSON.stringify(selected_notes),
                                    type:'brake',
                                    _token:"{{ @csrf_token() }}",
                                    expertise_id:$('input[name=expertise_id]').val(),
                                },
                                success: function (response) {
                                    if(response.success){

                                        var option_text = '';
                                        $.each(response.selectedNote, function(key, value) {
                                            option_text += '<option ondblclick="addNoteSelect([\"'+value+'\"],1)" value="' + value + '">' + value + '</option>';
                                        });
                                        $('#all_notes').html(option_text)


                                        var option_text_select = '';
                                        $.each(response.ExperBrakeNote, function(key, value) {
                                            option_text_select += '<option ondblclick="addNoteSelect([\"'+value+'\"],1)" value="' + value.id + '">' + value.note + '</option>';
                                        });
                                        $('#selected_notes').html(option_text_select)
                                        Toast.fire({
                                            icon: "success",
                                            title: "Notlar Silindi"
                                        });
                                        $('.note_add_loader').hide();
                                    }else{
                                        Toast.fire({
                                            icon: "error",
                                            title: "Not Eklenirken Bir Hata Oluştu"
                                        });
                                        $('.note_add_loader').hide();
                                    }

                                },
                                error: function(jqXHR, textStatus, errorThrown) {
                                    console.log(textStatus, errorThrown);
                                }
                            });
                        }
                    });
                }

            })

            if (typeof Toast === 'undefined') {
                var Toast = Swal.mixin({
                    toast: true,
                    position: "top-end",
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.onmouseenter = Swal.stopTimer;
                        toast.onmouseleave = Swal.resumeTimer;
                    }
                });
            }

            /*Notes New Version finish*/
        })();
        @if(auth('customer')->check())
        $('input').attr('disabled','true').removeAttr('required')
        $('select').attr('disabled','true').removeAttr('required')
        $('textarea').attr('disabled','true').removeAttr('required')
        $("button, input[type='submit']").filter(function() {
            return $(this).attr('type') !== 'button';
        }).remove();
        $('form').attr('action','')
        $('.add-note').remove()
        @endif


        @if(!auth('customer')->check() && $expertise['fren_kontrol_user'] > 0 && $expertise['fren_kontrol_user'] != $authUser->id)
        alert('Bu İşlem Başka Kullanıcıya Ait!')
        @endif
    </script>
@endpush
