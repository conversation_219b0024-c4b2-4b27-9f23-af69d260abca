@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Ekspertiz - Araç')
@section('content')
    @include('pages.expertise.tabs')
    @php
      $readonly_input = '';
      if($authUser->user_role_group_id == 33 || $authUser->user_role_group_id == 34){
          $readonly_input = "readonly=''";
      }
    @endphp
    <form id="mainForm" method="post" action="{{ route('updateExpertise') }}">@csrf
        <input type="hidden" name="expertise_id" value="{{ $_GET['expertise_id'] }}">
        <input type="hidden" name="type" value="{{ $_GET['type'] }}">
        <input type="hidden" name="car_id" value="{{ $expertise['car_id'] }}">
        <div class="row">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header justify-content-between">
                        <div class="card-title">
                            Araç Bilgileri
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Personel ID</label>
                                <input type="text" class="form-control" name="arac_kontrol_user" @if($expertise['is_closed'] == 1) disabled @endif value="{{ $expertise['arac_kontrol_user'] }}" {{$readonly_input}} placeholder="Personel ID">
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Plaka</label>
                                <input type="text" class="form-control" required name="plaka" @if($expertise['is_closed'] == 1) disabled @endif value="{{ $expertise['plaka'] }}" {{$readonly_input}} placeholder="Plaka">
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Şase Numarası</label>
                                <input type="text" class="form-control" required name="sase_no" @if($expertise['is_closed'] == 1) disabled @endif value="{{ $expertise['sase_no'] }}" placeholder="Şase Numarası">
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Araç KM.</label>
                                <input type="number" min="0" class="form-control" name="km" value="{{ $expertise['km'] }}" @if($expertise['is_closed'] == 1) disabled @endif placeholder="Araç KM." required>
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Km / Mil.</label>
                                <select name="km_type" class="form-control" required="" id="km_type">
                                    <option value="">Lütfen Seçim Yapınız.</option>
                                    <option @if($expertise['km_type'] == 1) selected="" @endif value="1">Km</option>
                                    <option @if($expertise['km_type'] == 2) selected="" @endif value="2">Mil</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header justify-content-between">
                        <div class="card-title">
                            Marka ve Model
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @if(!$expertise['is_closed'])
                                <div class="col-xl-12">
                                    <div class="form-group mt-2">
                                        <label class="form-label">Model Ara <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control search-group" @if(!isset($expertise['car_group_model_id'])) required @endif maxlength="17" {{$readonly_input}} placeholder="Aramak için yazınız (En az 3 kelime)">
                                        <div class="filter-results find-car"></div>
                                    </div>
                                </div>
                                <input type="hidden" name="car_group_model_id" value="{{ $expertise['car_group_model_id'] ?? '' }}">
                            @endif
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Tipi</label>
                                <input class="form-control" name="car_group_tip" disabled value="{{ $expertise['car_group'] }}">
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Marka</label>
                                <input class="form-control" name="car_group_marka" disabled value="{{ $expertise['car_marka'] }}">
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Model</label>
                                <input class="form-control" required name="car_group_model" disabled value="{{ $expertise['car_model'] }}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header justify-content-between">
                        <div class="card-title">
                            Araç Özellikleri
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Kasa Tipi</label>
                                @if($expertise['is_closed'] == 1)
                                    <input disabled class="form-control" value="{{ $expertise['car_case_type'] }}" {{$readonly_input}}>
                                @else
                                    <select required name="car_case_type_id" class="form-control" {{$readonly_input}}>
                                        @foreach(\App\Models\CarCaseType::where('status',1)->get() as $carCaseType)
                                            <option @if($carCaseType->id == $expertise['car_case_type_id']) selected @endif value="{{ $carCaseType->id }}">{{ $carCaseType->name }}</option>
                                        @endforeach
                                    </select>
                                @endif
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Yakıt Tipi</label>
                                @if($expertise['is_closed'] == 1)
                                    <input disabled class="form-control" value="{{ $expertise['car_fuel'] }}">
                                @else
                                    <select required name="car_fuels_id" class="form-control" {{$readonly_input}}>
                                        @foreach(\App\Models\CarFuel::where('status',1)->get() as $carFuel)
                                            <option @if($carFuel->id == $expertise['car_fuels_id']) selected @endif value="{{ $carFuel->id }}">{{ $carFuel->name }}</option>
                                        @endforeach
                                    </select>
                                @endif
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Vites Tipi</label>
                                @if($expertise['is_closed'] == 1)
                                    <input disabled class="form-control" value="{{ $expertise['car_gear'] }}">
                                @else
                                    <select required name="car_gears_id" class="form-control" {{$readonly_input}}>
                                        @foreach(\App\Models\CarGear::where('status',1)->get() as $carGear)
                                            <option @if($carGear->id == $expertise['car_gears_id']) selected @endif value="{{ $carGear->id }}">{{ $carGear->name }}</option>
                                        @endforeach
                                    </select>
                                @endif
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Çekişi</label>
                                <select required name="cekis" @if($expertise['is_closed'] == 1) disabled @endif class="form-control" {{$readonly_input}}>
                                    <option @if($expertise['cekis'] == 0) selected @endif value="0">Seçilmedi</option>
                                    <option @if($expertise['cekis'] == '4x2') selected @endif value="4x2">4x2</option>
                                    <option @if($expertise['cekis'] == '4x4') selected @endif value="4x4">4x4</option>
                                </select>
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Motor Hacmi</label>
                                <input type="text" class="form-control" @if($expertise['is_closed'] == 1) disabled @endif name="motor_hacmi" {{$readonly_input}} value="{{ $expertise['motor_hacmi']}}" placeholder="Motor Hacmi" required>
                            </div>
                            <div class="col-xl-12 mt-2">
                                <label class="form-label">Model Yılı</label>
                                <select name="model_yili" @if($expertise['is_closed'] == 1) disabled @endif class="form-control">
                                    @for($i = 1970;$i <= now()->year;$i++)
                                        <option @if($expertise['model_yili'] == $i) selected @endif {{$readonly_input}} value="{{ $i }}">{{ $i }}</option>
                                    @endfor
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                @include('pages.expertise.right_tabs')
            </div>
            @if(!$expertise['is_closed'])
                <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                    <button type="button" class="btn btn-info mt-3 mb-3 only_save"><i class="fa fa-save"></i> Kaydet</button>

                </div>
            @endif

        </div>
        <input type="hidden" name="close" @if($expertise['is_closed'] == 1) value="1" @else value="0" @endif>
    </form>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script>
        @if(!$expertise['is_closed'])
            $('.search-group').on('keyup',function (){
                let $val = $(this).val();
                if ($(this).val().length > 1){
                    $.ajax({
                        url: "{{ route('api.getCarModels') }}",
                        type: "post",
                        data: {'_token':'{{ csrf_token() }}','search':$val} ,
                        success: function (response) {
                            $('.find-car').css('display','block')
                            if (response.items.length > 0){
                                let html = ''
                                $.each(response.items,function (index,item){
                                    html += '<div class="filter-item" data-id="'+item.id+'" data-name="'+item.name+'" data-parent="'+item.parent+'" data-parent2="'+item.parent2+'" data-nameonly="'+item.name_only_name+'">'+item.name+'</div>'
                                })
                                $('.find-car').html(html)

                                $('.find-car .filter-item').on('click',function (){
                                    $('input[name=car_group_model_id]').val($(this).data('id'))
                                    $('input[name=car_group_model]').val($(this).data('nameonly'))
                                    $('input[name=car_group_marka]').val($(this).data('parent'))
                                    $('input[name=car_group_tip]').val($(this).data('parent2'))
                                    $('.find-car').css('display','none')
                                    $('.search-group').val('')
                                    $('.search-group').removeAttr('required')
                                })

                            }else{
                                $('.find-car').html("Hiçbir Kayıt Bulunamadı! <a class='text-danger' href='{{ route('cars.create') }}'>Yeni Kayıt Ekle</a>")
                            }
                        }
                    });
                }else{
                    $('.find-car').css('display','none')
                }
            })
            $('.only_save').click(function(){
                let close = 1;
                $('#mainForm [required]').each(function() {
                    var fieldValue = $(this).val();
                    if (fieldValue === '') {
                        close = 0;
                    }
                });
                $('input[name="close"]').val(close)

                $.ajax({
                    url: "{{ route('updateExpertise') }}",
                    type: "post",
                    data: $('#mainForm').serialize(),
                    success: function (response) {
                        Toast.fire({
                            icon: response.type ? response.type : 'success',
                            title: "Bilgiler Kayıt Edildi." + (response.message ? response.message : '')
                        }).then(function(){
                            if(response.return_url != ''){
                                window.location.href = response.return_url;
                            }
                        });
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            });
            if (typeof Toast === 'undefined') {
                var Toast = Swal.mixin({
                    toast: true,
                    position: "top-end",
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.onmouseenter = Swal.stopTimer;
                        toast.onmouseleave = Swal.resumeTimer;
                    }
                });
            }
        @endif

        $('#mainForm').on('submit',function (e){
            e.preventDefault()
            if ($('input[name="sase_no"]').val().length !== 17){
                Toast.fire({
                    icon: "error",
                    title: "Şase No 17 Karakter Olmalıdır."
                });
                return false;
            }
            $('#mainForm').unbind('submit').submit()

        })

        @if(auth('customer')->check())
            $('input').attr('disabled','true').removeAttr('required')
            $('select').attr('disabled','true').removeAttr('required')
            $('textarea').attr('disabled','true').removeAttr('required')
            $("button, input[type='submit']").filter(function() {
                return $(this).attr('type') !== 'button';
            }).remove();
            $('form').attr('action','')
        @endif

        @if(!auth('customer')->check() && $expertise['arac_kontrol_user'] > 0 && $expertise['arac_kontrol_user'] != $authUser->id)
            alert('Bu İşlem Başka Kullanıcıya Ait!')
        @endif
    </script>
@endpush
