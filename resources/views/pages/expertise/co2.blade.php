@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Ekspertiz - co2')
@section('content')
    @include('pages.expertise.tabs')
    <form id="mainForm" method="post" action="{{ route('updateExpertise') }}" enctype="multipart/form-data">@csrf
        <input type="hidden" name="expertise_id" value="{{ $_GET['expertise_id'] }}">
        <input type="hidden" name="type" value="{{ $_GET['type'] }}">
        <div class="row">
            <div class="col-md-10">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item" style="position: sticky;top: 1rem;z-index: 2">
                        <table class="table table-hover">
                            <thead style="position: sticky;top: -13px;z-index:1;">
                            <tr>
                                <th>Başlık</th>
                                <th>Durum</th>
                                <th>Açıklama</th>
                            </tr>
                            </thead>
                            <tbody>
                                <td>{{__('arrays.co2_kacak_testi')}}</td>
                                <td>
                                    <select required="" name="co2_kacak_testi[]" @if($expertise['is_closed']) disabled @endif data-key="co2_kacak_testi" class="form-control secenek" >
                                        <option value="">Lütfen Seçim Yapınız</option>
                                        @foreach(__('arrays.sub_controls_and_engines_answer')['co2_kacak_testi'] as $sub_controls_anwer)
                                            <option @if(!empty($expertise['expertise_co2']) && $expertise['expertise_co2']->key == $sub_controls_anwer) selected="" @endif   value="{{ $sub_controls_anwer }}">{{ ucfirst(str_replace('_',' ',$sub_controls_anwer)) }}</option>
                                        @endforeach
                                    </select>
                                </td>
                                <td><input class="form-control note-item co_kacak_note" data-title="{{__('arrays.co2_kacak_testi')}}" @if($expertise['is_closed']) disabled @endif data-key="co2_kacak_testi" name="co2_kacak_testi[]" value="{{$expertise['expertise_co2']->note ?? ''}}" placeholder="Notlar" @if(!empty($expertise['expertise_co2']) && $expertise['expertise_co2']->key == "sorunlu") required="" @endif  readonly=""></td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="notlarHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#notlar" aria-expanded="true"
                                    aria-controls="notlar">
                                Notlar
                            </button>
                        </h2>
                        <div id="notlar" class="accordion-collapse collapse show" aria-labelledby="notlarHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-12 notes">
                                        <div class="row">
                                            <div class="col-md-5" style="position:relative;">
                                                <div class="loader-body note_add_loader"  style="display: none">
                                                    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
                                                </div>
                                                <div class="col-md-12 p-0 bg-success text-white text-center rounded-top">
                                                    Ekli Notlar
                                                </div>
                                                @php
                                                    $selectedNotesArray = array();
                                                @endphp
                                                <select class="form-control rounded-0 rounded-bottom" name="not[]" id="selected_notes" multiple id="" style="height: 165px;">
                                                    @foreach($expertise['getCo2Note'] as $note)
                                                        @php
                                                            array_push($selectedNotesArray,$note['note'])
                                                        @endphp
                                                        <option value="{{$note['id']}}">{{$note['note']}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-2 text-center d-flex flex-column justify-content-center align-items-center">
                                                <button type="button" class="col-12 btn delete_selected_button @if(!$expertise['is_closed']) btn-danger @else border btn-disabled @endif btn-sm" @if($expertise['is_closed']) disabled @endif>
                                                    Not Sil
                                                </button>
                                                <button onclick="$('#notesModal').modal('toggle')" type="button" class="col-12 btn  btn-sm mt-1 @if(!$expertise['is_closed']) btn-info @else border btn-disabled @endif " @if($expertise['is_closed']) disabled @endif>
                                                    Not Tanımla
                                                </button>
                                                <button type="button" class="col-12 btn add_note_select @if(!$expertise['is_closed']) btn-success @else border btn-disabled @endif btn-sm mt-1" @if($expertise['is_closed']) disabled @endif>
                                                    Not Taşı
                                                </button>
                                            </div>
                                            <div class="col-md-5" style="position:relative;">
                                                <div class="loader-body note_add_loader"  style="display: none">
                                                    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
                                                </div>
                                                <input class="form-control mb-1 pt-1 pb-1" placeholder="Hazır Notlar İçinde Ara..." type="text"  id="search_input">
                                                <select class="form-control" name="all_note[]" id="all_notes" multiple id="" style="height: 150px;">
                                                    @foreach($expertise['allNotes'] as $notes)
                                                        @if(!in_array($notes->note,$selectedNotesArray))
                                                            <option ondblclick="addNoteSelect(['{{$notes->note}}'],1)" value="{{$notes->note}}">{{$notes->note}}</option>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-2">
                @include('pages.expertise.right_tabs')
            </div>
            @if(!$expertise['is_closed'])
                <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                    <button type="button" class="btn btn-info mt-3 mb-3 only_save"><i class="fa fa-save"></i> Kaydet</button>

                </div>
            @endif
        </div>
        <input type="hidden" name="close" @if($expertise['is_closed'] == 1) value="1" @else value="0" @endif>
    </form>
    <div class="modal fade" id="notesModal" tabindex="-1"
         aria-labelledby="notesModal" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Not Tanımla
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Not</small></label>
                            <input type="text" class="form-control" name="co2_notes_modal">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger co2_modal_save_button" >Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <!-- Select2 Cdn -->
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(document).ready(function(){
            $('.secenek').change(function(){
                var value = $(this).val();
                if(value == "sorunlu"){
                    $('.co_kacak_note').attr('required', 'required');
                } else {
                    $('.co_kacak_note').removeAttr('required');
                }
            });
        });
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
        function addNoteSelect(notes,type = 1){
            $('.note_add_loader').show()
            $.ajax({
                url: "{{ route('updateExpertiseNote') }}",
                type: "post",
                data: {
                    selectedNotes : JSON.stringify(notes),
                    type:'co2',
                    _token:"{{ @csrf_token() }}",
                    expertise_id:$('input[name=expertise_id]').val(),
                },
                success: function (response) {
                    if(response.success){
                        $('#notesModal').modal('hide')
                        Toast.fire({
                            icon: "success",
                            title: "Not Kayıt Edildi."
                        });
                        if(type == 1){
                            $("#all_notes option:selected").each(function () {
                                $(this).remove();
                            });
                        }

                        var option_text = '';
                        $.each(response.ExperCo2Note, function(key, value) {
                            option_text += '<option ondblclick="addNoteSelect([\"'+value['note']+'\"],1)" value="' + value['id'] + '">' + value['note'] + '</option>';
                        });
                        $('#selected_notes').html(option_text)
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Not Eklenirken Bir Hata Oluştu"
                        });
                    }
                    $('.note_add_loader').hide()
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        }
        (function (){

            $('.only_save').click(function(){
                var formData = new FormData($('#mainForm')[0]);

                let close = 1;
                $('#mainForm [required]').each(function() {
                    var fieldValue = $(this).val();
                    if (fieldValue === '') {
                        close = 0;
                    }
                });
                $('input[name="close"]').val(close)

                $.ajax({
                    url: "{{ route('updateExpertise') }}",
                    type: "POST",
                    data: formData,
                    processData: false, // processData'yi false olarak ayarlayın
                    contentType: false, // contentType'yi false olarak ayarlayın
                    success: function (response) {
                        if(response.success != 'false'){
                            Toast.fire({
                                icon: response.type ? response.type : 'success',
                                title: "Bilgiler Kayıt Edildi."
                            }).then(function(){
                                if(response.return_url != ''){
                                    window.location.href = response.return_url;
                                }
                            });
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "CO2 Kaçak Testi	Boş Bırakılamaz."
                            });
                        }

                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            });
            "use strict"

            $('.add-note').on('click',function (){
                let $val = $('.search-note').val();
                if ($val){
                    $('.notes').append('<input class="form-control" name="not[]" form="mainForm" value="'+$val+'">')
                    $('.search-note').val('');
                    $('.find-note').css('display','none')
                }
            })



            $('.search-note').on('keyup',function (){
                let $val = $(this).val();
                if ($(this).val().length > 2){
                    $.ajax({
                        url: "{{ route('api.getNotes') }}",
                        type: "post",
                        data: {'_token':'{{ csrf_token() }}','search':$val,'key':'co2'} ,
                        success: function (response) {
                            $('.find-note').css('display','block')
                            if (response.items.length > 0){
                                let html = ''
                                $.each(response.items,function (index,item){
                                    html += '<div class="filter-item" data-message="'+item.note+'">'+item.note+'</div>'
                                })
                                $('.find-note').html(html)

                                $('.find-note .filter-item').on('click',function (){
                                    $('.notes').append('<input class="form-control" name="not[]" form="mainForm" value="'+$(this).data('message')+'">')
                                })
                            }
                        }
                    });
                }else{
                    $('.find-note').css('display','none')
                }
            })

            @if(auth('customer')->check())
            $('input').attr('disabled','true').removeAttr('required')
            $('select').attr('disabled','true').removeAttr('required')
            $('textarea').attr('disabled','true').removeAttr('required')
            $("button, input[type='submit']").filter(function() {
                return $(this).attr('type') !== 'button';
            }).remove();
            $('form').attr('action','')
            $('.add-note').remove()
            @endif

            @if(!auth('customer')->check() && $expertise['co2_kontrol_user'] > 0 && $expertise['co2_kontrol_user'] != $authUser->id)
            alert('Bu İşlem Başka Kullanıcıya Ait!')
            @endif


            /*Notes New Version Start*/
            $("#search_input").on("input", function () {
                var searchText = $(this).val().toLowerCase();
                $("#all_notes option").each(function () {
                    var optionText = $(this).text().toLowerCase();
                    $(this).toggle(optionText.indexOf(searchText) > -1);
                });
            });
            $('.add_note_select').click(function (){
                var selectedOptions = $("#all_notes").val();
                if(selectedOptions.length > 0){
                    addNoteSelect(selectedOptions)
                }
            })

            $('.co2_modal_save_button').click(function(){
                var array_notes = [$('input[name=co2_notes_modal]').val()]
                addNoteSelect(array_notes,2)
                $('#notesModal').modal('hide')

            })

            $('.delete_selected_button').click(function(){
                var selected_notes = $('#selected_notes').val();
                if(selected_notes.length > 0){
                    Swal.fire({
                        title: "Emin misiniz?",
                        text: "Seçili Notlar Silinecek",
                        icon: "error",
                        showCancelButton: true,
                        confirmButtonColor: "#d33",
                        cancelButtonColor: "#3085d6",
                        confirmButtonText: "Evet, Sil!",
                        cancelButtonText: "Hayır, Silme!"
                    }).then((result) => {
                        $('.note_add_loader').show()
                        if (result.isConfirmed) {
                            $.ajax({
                                url: "{{ route('deleteExpertiseNote') }}",
                                type: "post",
                                data: {
                                    selectedNotes : JSON.stringify(selected_notes),
                                    type:'co2',
                                    _token:"{{ @csrf_token() }}",
                                    expertise_id:$('input[name=expertise_id]').val(),
                                },
                                success: function (response) {
                                    if(response.success){

                                        var option_text = '';
                                        $.each(response.selectedNote, function(key, value) {
                                            option_text += '<option ondblclick="addNoteSelect([\"'+value+'\"],1)" value="' + value + '">' + value + '</option>';
                                        });
                                        $('#all_notes').html(option_text)


                                        var option_text_select = '';
                                        $.each(response.Experco2Note, function(key, value) {
                                            option_text_select += '<option ondblclick="addNoteSelect([\"'+value+'\"],1)" value="' + value.id + '">' + value.note + '</option>';
                                        });
                                        $('#selected_notes').html(option_text_select)
                                        Toast.fire({
                                            icon: "success",
                                            title: "Notlar Silindi"
                                        });
                                    }else{
                                        Toast.fire({
                                            icon: "error",
                                            title: "Not Eklenirken Bir Hata Oluştu"
                                        });
                                    }
                                    $('.note_add_loader').hide()
                                },
                                error: function(jqXHR, textStatus, errorThrown) {
                                    console.log(textStatus, errorThrown);
                                }
                            });
                        }
                    });
                }

            })



            /*Notes New Version finish*/
        })();

    </script>
@endpush
