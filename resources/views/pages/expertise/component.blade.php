@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Komponentler')
@section('content')
    @include('pages.expertise.tabs')
    <form id="mainForm" method="post" action="{{ route('updateExpertise') }}" enctype="multipart/form-data">@csrf
        <input type="hidden" name="expertise_id" value="{{ $_GET['expertise_id'] }}">
        <input type="hidden" name="type" value="{{ $_GET['type'] }}">
        <div class="row">
            <div class="col-md-10">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item" style="position: sticky;top: 1rem;z-index: 2">
                        <h2 class="accordion-header" id="belgeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#belge" aria-expanded="true"
                                    aria-controls="belge">
                                Belge
                            </button>
                        </h2>
                        <div id="belge" class="accordion-collapse collapse show" aria-labelledby="belgeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-1">
                                        <label class="form-label">Personel<span class="text-danger">*</span></label>
                                        <input type="text" class="form-control form-control-sm" name="komponent_kontrol_user" @if($expertise['komponent_kontrol_user'] > 0) disabled value="{{ $expertise['komponent_kontrol_user'] }}" @else  @endif placeholder="Personel ID">
                                    </div>
                                    <div class="col-md-1">
                                        <label>Plaka</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['plaka'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>Şase</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['sase_no'] }}">
                                    </div>
{{--                                    <div class="col-md-2">--}}
{{--                                        <label>Hizmet</label>--}}
{{--                                        <input disabled class="form-control form-control-sm" value="@foreach($expertise['getStocks'] as $expertiseStock) {{ $expertiseStock['ad'] }}, @endforeach">--}}
{{--                                    </div>--}}
                                    <div class="col-md-2">
                                        <label>Tarih</label>
                                        <input type="datetime-local" class="form-control form-control-sm" readonly name="date" value="{{ $expertise['date'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>No</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['belge_no'] }}">
                                    </div>
                                    <div class="col-md-1">
                                        <label>KM</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['km'] }}">
                                    </div>
                                    <div class="col-md-1">
                                        <label>Model Yılı</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['car_model_year'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>Model</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['car_model'] }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="kaportaSonuclariHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#kaportaSonuclari" aria-expanded="true"
                                    aria-controls="kaportaSonuclari">
                                Komponentler
                            </button>
                        </h2>
                        <div id="kaportaSonuclari" class="accordion-collapse collapse show" aria-labelledby="kaportaSonuclariHeading">
                            <div class="accordion-body" style="overflow: auto;max-width: 100%;max-height: 400px;">
                                <table class="table table-hover">
                                    <thead style="position: sticky;top: -13px;z-index:1;">
                                    <tr>
                                        <th>Başlık</th>
                                        <th>Durum</th>
                                        <th>Açıklama</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @php
                                    $sanziman_answer = null;
                                    @endphp
                                    @foreach($expertise['getComponents'] as $key => $bodywork)
                                        <tr>
                                            <td>{{ $bodywork['title'] }}</td>
                                            <td>
                                                @php
                                                $disabled_search = null;
                                                if($key == "sanziman_vites_gecisleri_ve_ses_kontrolu"){
                                                    $sanziman_answer = $bodywork['answer'];
                                                }
                                                @endphp
                                                @if($bodywork['title'] == 'Şanzıman' || $bodywork['title'] == 'Diferansiyel' || $bodywork['title'] == 'Tork Konvertörü' )
                                                    <input type = "hidden" class="{{$key}}" name="{{ $key }}[]" maxlength="35" value="{{$bodywork['answer']}}">
                                                    @php
                                                        if($bodywork['answer'] == "test_yapılmadı" && $sanziman_answer == $bodywork['answer']){
                                                            $disabled_search="disabled=''";
                                                        }
                                                    @endphp
                                                @else
                                                    @php
                                                        $disabled_search=null;
                                                    @endphp
                                                @endif
                                                <select {{$disabled_search}} @if($expertise['is_closed']) disabled @endif name="{{ $key }}[]" class="form-control form-control-sm form-item" data-key="{{ $key }}" onchange="optionChangeNot('{{$key}}')" required="">
                                                    <option value="">Lütfen seçim yapınız.</option>
                                                    @foreach(__('arrays.components_answer')[$key] as $compantent_anwer)
                                                        <option @if($bodywork['answer'] == $compantent_anwer) selected @endif   value="{{ $compantent_anwer }}">{{ ucfirst(str_replace('_',' ',$compantent_anwer)) }}</option>
                                                    @endforeach
                                                </select>
                                            </td>
                                            <td>
                                                @if($bodywork['title'] == 'Şanzıman' || $bodywork['title'] == 'Diferansiyel' || $bodywork['title'] == 'Tork Konvertörü')
                                                    <input type="text" class="form-control form-control-sm" maxlength="35" @if($bodywork['answer'] == "yok") readonly="" @endif @if($expertise['is_closed']) disabled @endif name="{{ $key }}[]" data-key="{{ $key }}" value="{{ $bodywork['note'] }}" placeholder="Notlar">
                                                @else
                                                    <input type="text" class="form-control form-control-sm" maxlength="35"  @if($bodywork['answer'] == "yok") readonly="" @endif @if($expertise['is_closed']) disabled @endif name="{{ $key }}[]" data-key="{{ $key }}" value="{{ $bodywork['note'] }}" placeholder="Notlar">
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="notlarHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#dyno" aria-expanded="true"
                                    aria-controls="dyno">
                                Dyno Bilgileri
                            </button>
                        </h2>
                        <div id="dyno" class="accordion-collapse collapse show" aria-labelledby="notlarHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-12 yol-testi-fields">
                                        <div class="row">
                                            <div class="col-md-3 d-flex flex-column justify-content-center align-items-start">
                                                <label for="">Ölçülen Max. Motor Gücü [kW]</label>
                                            </div>
                                            <div class="col-md-3 mt-1">
                                                <input name="measured_kw" @if($expertise['is_closed']) disabled @else required @endif value="{{$expertise['dyno_data']['measured_kw'] ?? ''}}" type="text" class="form-control form-control-sm">
                                            </div>
                                            <div class="col-md-3 mt-1">
                                                <div class="row">
                                                    <div class="col-10">
                                                        <input name="measured_hp" @if($expertise['is_closed']) disabled @else required @endif value="{{$expertise['dyno_data']['measured_hp'] ?? ''}}" type="text" class="form-control form-control-sm" >
                                                    </div>
                                                    <div class="col-1">
                                                        HP
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 mt-1 d-flex justify-content-center align-items-center">
                                                <div class="form-group">
                                                    <label for="yol_testi">Yol Testi</label>
                                                    <input type="checkbox" name="yol_testi" id="yol_testi" value="1">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12 mt-2 yol-testi-fields">
                                        <div class="row">
                                            <div class="col-md-3 d-flex flex-column justify-content-center align-items-start">
                                                <label for="">Hesaplan Max. Motor Gücü [kW]</label>
                                            </div>
                                            <div class="col-md-3 mt-1">
                                                <input name="calculated_kw" @if($expertise['is_closed']) disabled @else required @endif value="{{$expertise['dyno_data']['calculated_kw'] ?? ''}}"  type="text" class="form-control form-control-sm">
                                            </div>
                                            <div class="col-md-3 mt-1">
                                                <div class="row">
                                                    <div class="col-10">
                                                        <input name="calculated_hp" @if($expertise['is_closed']) disabled @else required @endif value="{{$expertise['dyno_data']['calculated_hp'] ?? ''}}"  type="text" class="form-control form-control-sm">
                                                    </div>
                                                    <div class="col-2">HP</div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 mt-1">
                                                <div class="row">
                                                    <div class="col-10">
                                                        <input name="calculated_rpm" @if($expertise['is_closed']) disabled @else required @endif value="{{$expertise['dyno_data']['calculated_rpm'] ?? ''}}"  type="text" class="form-control form-control-sm">
                                                    </div>
                                                    <div class="col-2">rpm</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-12 mt-2 yol-testi-fields">
                                        <div class="row">
                                            <div class="col-md-3 d-flex flex-column justify-content-center align-items-start">
                                                <label for="">Aktarma Organlarındaki Kayıp Güç</label>
                                            </div>
                                            <div class="col-md-3 mt-1">
                                                <input name="transfer_kw" type="text" @if($expertise['is_closed']) disabled @else required @endif value="{{$expertise['dyno_data']['transfer_kw'] ?? ''}}"  class="form-control form-control-sm">
                                            </div>
                                            <div class="col-md-3 mt-1">
                                                <div class="row">
                                                    <div class="col-10">
                                                        <input name="transfer_hp" @if($expertise['is_closed']) disabled @else required @endif value="{{$expertise['dyno_data']['transfer_hp'] ?? ''}}"  type="text" class="form-control form-control-sm">
                                                    </div>
                                                    <div class="col-2">HP</div>
                                                </div>
                                            </div>
                                            <div class="col-md-3 mt-1">
                                                <div class="row">
                                                    <div class="col-10">
                                                        <input name="transfer_rpm" @if($expertise['is_closed']) disabled @else required @endif value="{{$expertise['dyno_data']['transfer_rpm'] ?? ''}}"  type="text" class="form-control form-control-sm">
                                                    </div>
                                                    <div class="col-2">km/h</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="notlarHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#notlar" aria-expanded="true"
                                    aria-controls="notlar">
                                Notlar (Boş Veriler Silinir)
                            </button>
                        </h2>
                        <div id="notlar" class="accordion-collapse collapse show" aria-labelledby="notlarHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-12 notes">
                                        <div class="row">
                                            <div class="col-md-5" style="position:relative;">
                                                <div class="loader-body note_add_loader"  style="display: none">
                                                    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
                                                </div>
                                                <div class="col-md-12 p-0 bg-success text-white text-center rounded-top">
                                                    Ekli Notlar
                                                </div>
                                                @php
                                                    $selectedNotesArray = array();
                                                @endphp
                                                <select class="form-control rounded-0 rounded-bottom" name="not[]" id="selected_notes" multiple id="" style="height: 165px;">
                                                    @foreach($expertise['getComponentNotes'] as $note)
                                                        @php
                                                            array_push($selectedNotesArray,$note['note'])
                                                        @endphp
                                                        <option value="{{$note['id']}}">{{$note['note']}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-2 text-center d-flex flex-column justify-content-center align-items-center">
                                                <button type="button" class="col-12 btn delete_selected_button @if(!$expertise['is_closed']) btn-danger @else border btn-disabled @endif btn-sm" @if($expertise['is_closed']) disabled @endif>
                                                    Not Sil
                                                </button>
                                                <button onclick="$('#notesModal').modal('toggle')" type="button" class="col-12 btn  btn-sm mt-1 @if(!$expertise['is_closed']) btn-info @else border btn-disabled @endif " @if($expertise['is_closed']) disabled @endif>
                                                    Not Tanımla
                                                </button>
                                                <button type="button" class="col-12 btn add_note_select @if(!$expertise['is_closed']) btn-success @else border btn-disabled @endif btn-sm mt-1" @if($expertise['is_closed']) disabled @endif>
                                                    Not Taşı
                                                </button>
                                            </div>
                                            <div class="col-md-5" style="position:relative;">
                                                <div class="loader-body note_add_loader"  style="display: none">
                                                    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
                                                </div>
                                                <input class="form-control mb-1 pt-1 pb-1" placeholder="Hazır Notlar İçinde Ara..." type="text"  id="search_input">
                                                <select class="form-control form-control-sm" name="all_note[]" id="all_notes" multiple id="" style="height: 150px;">
                                                    @foreach($expertise['allNotes'] as $notes)
                                                        @if(!in_array($notes->note,$selectedNotesArray))
                                                            <option ondblclick="addNoteSelect(['{{$notes->note}}'],1)" value="{{$notes->note}}">{{$notes->note}}</option>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-2">
                @include('pages.expertise.right_tabs')
            </div>
            @if(!$expertise['is_closed'])
                <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">

                    <button type="button" class="btn btn-info mt-3 mb-3 only_save"><i class="fa fa-save"></i> Kaydet</button>

                </div>
            @endif
        </div>
        <input type="hidden" name="close" @if($expertise['is_closed'] == 1) value="1" @else value="0" @endif>
    </form>
    <div class="modal fade" id="notesModal" tabindex="-1"
         aria-labelledby="notesModal" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Not Tanımla
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Not</small></label>
                            <input type="text" class="form-control form-control-sm" name="component_notes_modal">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger component_modal_save_button" >Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <!-- Select2 Cdn -->
    <script src="/assets/js/select2.min.js"></script>
    <script>
        isTabActive = true;
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                isTabActive = true;
            } else {
                isTabActive = false;
            }
        });
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
        function addNoteSelect(notes,type = 1){
            $('.note_add_loader').show()
            $.ajax({
                url: "{{ route('updateExpertiseNote') }}",
                type: "post",
                data: {
                    selectedNotes : JSON.stringify(notes),
                    type:'component',
                    _token:"{{ @csrf_token() }}",
                    expertise_id:$('input[name=expertise_id]').val(),
                },
                success: function (response) {
                    if(response.success){
                        $('#notesModal').modal('hide')
                        Toast.fire({
                            icon: "success",
                            title: "Not Kayıt Edildi."
                        });
                        if(type == 1){
                            $("#all_notes option:selected").each(function () {
                                $(this).remove();
                            });
                        }

                        var option_text = '';

                        $.each(response.ExperComponentNote, function(key, value) {
                            option_text += '<option ondblclick="addNoteSelect([\"'+value['note']+'\"],1)" value="' + value['id'] + '">' + value['note'] + '</option>';
                            console.log(option_text)
                        });
                        $('#selected_notes').html(option_text)
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Not Eklenirken Bir Hata Oluştu"
                        });
                    }
                    $('.note_add_loader').hide()
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        }
        (function (){
            "use strict"
            // $(".select2").select2({
            //     placeholder: "Ara.."
            // });

            $('#mainForm').on('submit',function (e){
                e.preventDefault()
                if ($('select[name="sanziman"]').val() !== 'test_yapılmadı' && $('select[name="diferansiyel"]').val() === 'test_yapılmadı'){
                    Toast.fire({
                        icon: "error",
                        title: "Şanzıman ve Diferansiyel Seçenekleri Uyuşmuyor!"
                    });
                    return false;
                }

                $('#mainForm').unbind('submit').submit()
            })

            $('.add-note').on('click',function (){
                let $val = $('.search-note').val();
                if ($val){
                    $('.notes').append('<input class="form-control form-control-sm" name="not[]" form="mainForm" value="'+$val+'">')
                    $('.search-note').val('');
                    $('.find-note').css('display','none')
                }
            })

            setInterval(function (){
                if(isTabActive){
                    $.ajax({
                        url: "{{ route('updateExpertise') }}",
                        type: "post",
                        data: $('#mainForm').serialize(),
                        success: function (response) {},
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                        }
                    });
                }

            },30 * 1000)
            $('.only_save').click(function(){
                if ($('select[name="sanziman"]').val() !== 'test_yapılmadı' && $('select[name="diferansiyel"]').val() === 'test_yapılmadı'){
                    Toast.fire({
                        icon: "error",
                        title: "Şanzıman ve Diferansiyel Seçenekleri Uyuşmuyor!"
                    });
                    return false;
                }
                let close = 1;
                $('#mainForm [required]').each(function() {
                    var fieldValue = $(this).val();
                    if (fieldValue === '') {
                        close = 0;
                    }
                });
                $('input[name="close"]').val(close)
                $.ajax({
                    url: "{{ route('updateExpertise') }}",
                    type: "post",
                    data: $('#mainForm').serialize(),
                    success: function (response) {
                        Toast.fire({
                            icon: response.type ? response.type : 'success',
                            title: "Bilgiler Kayıt Edildi." + (response.message ? response.message : '')
                        }).then(function(){
                            if(response.return_url != ''){
                                window.location.href = response.return_url;
                            }
                        });
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            });

            $('.search-note').on('keyup',function (){
                let $val = $(this).val();
                if ($(this).val().length > 2){
                    $.ajax({
                        url: "{{ route('api.getNotes') }}",
                        type: "post",
                        data: {'_token':'{{ csrf_token() }}','search':$val,'key':'component'} ,
                        success: function (response) {
                            $('.find-note').css('display','block')
                            if (response.items.length > 0){
                                let html = ''
                                $.each(response.items,function (index,item){
                                    html += '<div class="filter-item" data-message="'+item.note+'">'+item.note+'</div>'
                                })
                                $('.find-note').html(html)

                                $('.find-note .filter-item').on('click',function (){
                                    $('.notes').append('<input class="form-control form-control-sm" name="not[]" form="mainForm" value="'+$(this).data('message')+'">')
                                })
                            }
                        }
                    });
                }else{
                    $('.find-note').css('display','none')
                }
            })

            @if(auth('customer')->check())
            $('input').attr('disabled','true').removeAttr('required')
            $('select').attr('disabled','true').removeAttr('required')
            $('textarea').attr('disabled','true').removeAttr('required')
            $("button, input[type='submit']").filter(function() {
                return $(this).attr('type') !== 'button';
            }).remove();
            $('form').attr('action','')
            $('.add-note').remove()
            @endif

            @if(!auth('customer')->check() && $expertise['komponent_kontrol_user'] > 0 && $expertise['komponent_kontrol_user'] != $authUser->id)
            alert('Bu İşlem Başka Kullanıcıya Ait!')
            @endif

            /*Notes New Version Start*/
            $("#search_input").on("input", function () {
                var searchText = $(this).val().toLowerCase();
                $("#all_notes option").each(function () {
                    var optionText = $(this).text().toLowerCase();
                    $(this).toggle(optionText.indexOf(searchText) > -1);
                });
            });
            $('.add_note_select').click(function (){
                var selectedOptions = $("#all_notes").val();
                if(selectedOptions.length > 0){
                    addNoteSelect(selectedOptions)
                }
            })

            $('.component_modal_save_button').click(function(){
                var array_notes = [$('input[name=component_notes_modal]').val()]
                addNoteSelect(array_notes,2)
                $('#notesModal').modal('hide')
            })

            $('.delete_selected_button').click(function(){
                var selected_notes = $('#selected_notes').val();
                if(selected_notes.length > 0){
                    Swal.fire({
                        title: "Emin misiniz?",
                        text: "Seçili Notlar Silinecek",
                        icon: "error",
                        showCancelButton: true,
                        confirmButtonColor: "#d33",
                        cancelButtonColor: "#3085d6",
                        confirmButtonText: "Evet, Sil!",
                        cancelButtonText: "Hayır, Silme!"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            $('.note_add_loader').show()
                            $.ajax({
                                url: "{{ route('deleteExpertiseNote') }}",
                                type: "post",
                                data: {
                                    selectedNotes : JSON.stringify(selected_notes),
                                    type:'component',
                                    _token:"{{ @csrf_token() }}",
                                    expertise_id:$('input[name=expertise_id]').val(),
                                },
                                success: function (response) {
                                    if(response.success){

                                        var option_text = '';
                                        $.each(response.selectedNote, function(key, value) {
                                            option_text += '<option ondblclick="addNoteSelect([\"'+value+'\"],1)" value="' + value + '">' + value + '</option>';
                                        });
                                        $('#all_notes').html(option_text)


                                        var option_text_select = '';
                                        $.each(response.ExperComponentNote, function(key, value) {
                                            option_text_select += '<option ondblclick="addNoteSelect([\"'+value+'\"],1)" value="' + value.id + '">' + value.note + '</option>';
                                        });
                                        $('#selected_notes').html(option_text_select)
                                        Toast.fire({
                                            icon: "success",
                                            title: "Notlar Silindi"
                                        });
                                    }else{
                                        Toast.fire({
                                            icon: "error",
                                            title: "Not Eklenirken Bir Hata Oluştu"
                                        });
                                    }
                                    $('.note_add_loader').hide()
                                },
                                error: function(jqXHR, textStatus, errorThrown) {
                                    console.log(textStatus, errorThrown);
                                }
                            });
                        }
                    });
                }

            })


            /*Notes New Version finish*/

        })();
        $(document).ready(function(){
            $('select[name="sanziman[]"]').attr('readonly','true')
        })
        function optionChangeNot(type){
          if(type == "sanziman_vites_gecisleri_ve_ses_kontrolu"){
              if($('select[name="'+type+'[]"]').val() == "iyi"){
                  $('select[name="tork_konventoru[]"]').val('iyi').trigger('change');
                  $('select[name="sanziman[]"]').val('iyi').trigger('change');

                  $('select[name="diferansiyel[]"]').val('').trigger('change');
                  $('select[name="motor[]"]').val('').trigger('change');

                  $('select[name="tork_konventoru[]"]').attr('readonly','true')
                  $('select[name="diferansiyel[]"]').attr('readonly','true')
                  $('select[name="motor[]"]').attr('readonly','true')

              }
              if($('select[name="'+type+'[]"]').val() == "kötü"){
                  $('select[name="tork_konventoru[]"]').val('orta').trigger('change');
                  $('select[name="sanziman[]"]').val('kötü').trigger('change');

                  $('select[name="diferansiyel[]"]').val('').trigger('change');
                  $('select[name="motor[]"]').val('').trigger('change');


                  $('select[name="tork_konventoru[]"]').attr('readonly','true')
                  $('select[name="diferansiyel[]"]').attr('readonly','true')
                  $('select[name="motor[]"]').attr('readonly','true')

              }
              if($('select[name="'+type+'[]"]').val() == "test_yapılmadı"){
                  $('select[name="tork_konventoru[]"]').val('test_yapılmadı').trigger('change');
                  $('select[name="sanziman[]"]').val('test_yapılmadı').trigger('change');
                  $('select[name="diferansiyel[]"]').val('test_yapılmadı').trigger('change');
                  $('select[name="motor[]"]').val('test_yapılmadı').trigger('change');




                  $('select[name="tork_konventoru[]"]').attr('readonly','true')
                  $('select[name="diferansiyel[]"]').attr('readonly','true')

              }
              if($('select[name="'+type+'[]"]').val() == "orta"){
                  $('select[name="tork_konventoru[]"]').val('orta').trigger('change');
                  $('select[name="sanziman[]"]').val('orta').trigger('change');

                  $('select[name="diferansiyel[]"]').val('').trigger('change');
                  $('select[name="motor[]"]').val('').trigger('change');

                  $('select[name="tork_konventoru[]"]').attr('readonly','true')
                  $('select[name="diferansiyel[]"]').attr('readonly','true')
                  $('select[name="motor[]"]').attr('readonly','true')


              }
              if($('select[name="'+type+'[]"]').val() == "hafif"){
                  $('select[name="tork_konventoru[]"]').val('iyi').trigger('change');
                  $('select[name="sanziman[]"]').val('iyi').trigger('change');

                  $('select[name="diferansiyel[]"]').val('').trigger('change');
                  $('select[name="motor[]"]').val('').trigger('change');

                  $('select[name="tork_konventoru[]"]').attr('readonly','true')
                  $('select[name="diferansiyel[]"]').attr('readonly','true')
                  $('select[name="motor[]"]').attr('readonly','true')
              }


          }
          if(type == "diferansiyel"){
              if($('select[name="'+type+'[]"]').val() == "orta" || $('select[name="'+type+'[]"]').val() == "kötü"){
                  $('input[name="'+type+'[]"]').attr("required","required");
              }else{
                  $('input[name="'+type+'[]"]').removeAttr("required");
              }
          }
          if(type == "tork_konventoru" || type == "dortceker_kontrolu"){
              if($('select[name="'+type+'[]"]').val() == "yok"){
                  $('input[name="'+type+'[]"]').val('Bu özellik bulunmamaktadır.')
                  $('input[name="'+type+'[]"]').attr('readonly','')
              }else{
                  $('input[name="'+type+'[]"]').val('')
                  $('input[name="'+type+'[]"]').removeAttr('readonly','')
              }
          }

          $('.'+type).val($('select[name="'+type+'[]"]').val())
            triggerFormItem(type)
        }

        $('.form-item').on('change',function (){
            let $key = $(this).data('key')
            let $value = $(this).val()
            let $noteInput = $('input[data-key="'+$key+'"]')
            $noteInput.val('')
            triggerFormItem($key,$value)
        })

        function triggerFormItem($key,$value){
            let $noteInput = $('input[data-key="'+$key+'"]')
            if ($key === 'sanziman_vites_gecisleri_ve_ses_kontrolu'){
                if ($value === 'iyi' || $value === 'test_yapılmadı' || $value === ''){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                    if ($value === 'test_yapılmadı'){
                        $('select[data-key="motor"]').val("test_yapılmadı")
                        triggerFormItem("motor","test_yapılmadı")
                        $('select[data-key="diferansiyel"]').val("test_yapılmadı")
                        triggerFormItem("diferansiyel","test_yapılmadı")
                        $('select[data-key="sanziman"]').val("test_yapılmadı").attr('readonly','true')
                        triggerFormItem("sanziman","test_yapılmadı")
                        $('select[data-key="tork_konventoru"]').val("test_yapılmadı").attr('disabled','true')
                        $('input[name="tork_konventoru[]"][type="hidden"]').val("test_yapılmadı")
                        triggerFormItem("tork_konventoru","test_yapılmadı")
                    }else if($value === 'iyi'){
                        $('select[data-key="sanziman"]').val("iyi").attr('readonly','true')
                        triggerFormItem("sanziman","iyi")
                        $('select[data-key="tork_konventoru"]').val("iyi").removeAttr('readonly').removeAttr('disabled')
                        triggerFormItem("tork_konventoru","iyi")
                    }
                }else{
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                    if($value === 'kötü'){
                        $('select[data-key="sanziman"]').val("kötü").attr('readonly','true')
                        triggerFormItem("sanziman","kötü")
                        $('select[data-key="tork_konventoru"]').val("orta").removeAttr('readonly').removeAttr('disabled')
                        triggerFormItem("tork_konventoru","orta")
                    }else if($value === 'orta'){
                        $('select[data-key="sanziman"]').val("orta").attr('readonly','true')
                        triggerFormItem("sanziman","orta")
                        $('select[data-key="tork_konventoru"]').val("orta").removeAttr('readonly').removeAttr('disabled')
                        triggerFormItem("tork_konventoru","orta")
                    }else if($value === 'hafif'){
                        $('select[data-key="sanziman"]').val("iyi").attr('readonly','true')
                        triggerFormItem("sanziman","iyi")
                        $('select[data-key="tork_konventoru"]').val("iyi").removeAttr('readonly').removeAttr('disabled')
                        triggerFormItem("tork_konventoru","iyi")
                    }
                }
            }else if ($key === 'dortceker_kontrolu'){
                if ($value === 'iyi' || $value === 'yok' || $value === 'test_yapılmadı' || $value === ''){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                    if($value === 'yok'){
                        $noteInput.val("Araçta Bu Özellik Bulunmamaktadır.")
                    }else if($value === 'test_yapılmadı'){
                        $noteInput.val("Test Yapılmadı")
                    }
                }else{
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }


            }else if ($key === 'motor'){
                $noteInput.attr('readonly','true').removeAttr('required')
            }else if ($key === 'sanziman'){
                $('select[data-key="sanziman"]').attr('disabled','true')
                $('input[type="hidden"][name="sanziman[]"]').val($value)
                $noteInput.attr('readonly','true').removeAttr('required')
            }else if ($key === 'diferansiyel'){
                if ($value === 'iyi' || $value === ''){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else{
                    $('select[data-key="diferansiyel"]').removeAttr('disabled').removeAttr('readonly')
                    if($value !== 'test_yapılmadı'){

                        $('input[name="diferansiyel[]"][type="hidden"]').val($value)
                        $noteInput.removeAttr('readonly').attr('required','true') // açıklama zorunlu
                    }else{
                        var sanzimandata = $('select[data-key=sanziman_vites_gecisleri_ve_ses_kontrolu]').val();
                        if(sanzimandata != "test_yapılmadı"){

                        }else{
                            // $('select[data-key="diferansiyel"]').attr('disabled','true').removeAttr('readonly','true')
                        }

                        $('input[name="diferansiyel[]"][type="hidden"]').val($value)
                        $('input[name="diferansiyel[]"][type="text"]').val("")
                        $noteInput.attr('readonly','true').removeAttr('required') // açıklama zorunlu
                    }

                }
            }else if ($key === 'tork_konventoru'){
                if ($value === "yok")
                    $noteInput.val("Araçta Bu Özellik Bulunmamaktadır.")
                $noteInput.attr('readonly','true').removeAttr('required')
            }
        }
    </script>
    <script>
        document.getElementById('yol_testi').addEventListener('change', function() {
            const yolTestiFields = document.querySelectorAll('.yol-testi-fields input');

            if (this.checked) {
                yolTestiFields.forEach(field => field.removeAttribute('required'));
            } else {
                yolTestiFields.forEach(field => field.setAttribute('required', 'required'));
            }
        });
    </script>
@endpush
