@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@push('message') ({{ \Carbon\Carbon::make($expertise['belge_tarihi'])->format('d.m.Y H:i:s') }}) ({{ $expertise['belge_no'] }})@endpush
@section('title','Kayıt Detay')
@section('content')
    @include('pages.expertise.tabs')
    <form id="mainForm" method="post" action="{{ route('expertises.update',$expertise['uuid']) }}" enctype="multipart/form-data">@csrf @method('put')
        <input type="hidden" name="uuid" value="{{ $expertise['uuid'] }}">
        <input type="hidden" name="return_url" value="{{ url()->previous() }}">
        <div class="row">
            <div class="col-md-6">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="cariBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#cariBilgiler" aria-expanded="true"
                                    aria-controls="cariBilgiler">
                                Ruhsat Bilgileri
                            </button>
                        </h2>
                        <div id="cariBilgiler" class="accordion-collapse collapse show" aria-labelledby="cariBilgilerHeading">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Kod</label>
                                        </div>
                                        <div class="col-md-10">
                                            <input disabled class="form-control" name="cari_kod" value="{{ $expertise['cariKod'] }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Unvan</label>
                                        </div>
                                        <div class="col-md-10">
                                            <input disabled class="form-control" name="cari_unvan" value="{{ $expertise['cariUnvan'] }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Nereden Ulaştınız</label>
                                        </div>
                                        <div class="col-md-10">
                                            <input disabled class="form-control" name="cari_unvan" value="{{ $expertise['nereden_ulastiniz'] }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="saticiBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#saticiBilgiler" aria-expanded="true"
                                    aria-controls="saticiBilgiler">
                                Satıcı Bilgileri
                            </button>
                        </h2>
                        <div id="saticiBilgiler" class="accordion-collapse collapse show" aria-labelledby="saticiBilgilerHeading">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Kod</label>
                                        </div>
                                        <div class="col-md-10">
                                            <input disabled class="form-control" name="satici_kod" value="{{ $expertise['saticiKod'] }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Unvan</label>
                                        </div>
                                        <div class="col-md-10">
                                            <input disabled class="form-control" name="satici_unvan" value="{{ $expertise['saticiUnvan']  }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aliciBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aliciBilgiler" aria-expanded="true"
                                    aria-controls="aliciBilgiler">
                                Alıcı Bilgileri
                            </button>
                        </h2>
                        <div id="aliciBilgiler" class="accordion-collapse collapse show" aria-labelledby="aliciBilgilerHeading">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Kod</label>
                                        </div>
                                        <div class="col-md-10">
                                            <input disabled class="form-control" name="alici_kod" value="{{ $expertise['aliciKod'] }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Unvan</label>
                                        </div>
                                        <div class="col-md-10">
                                            <input disabled class="form-control" name="alici_unvan" value="{{ $expertise['aliciUnvan'] }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgiler" aria-expanded="true"
                                    aria-controls="aracBilgiler">
                                Araç Bilgileri
                            </button>
                        </h2>
                        <div id="aracBilgiler" class="accordion-collapse collapse show" aria-labelledby="aracBilgilerHeading">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Plaka</label>
                                        </div>
                                        <div class="col-md-10">
                                            <input disabled class="form-control" name="arac_plaka" value="{{ $expertise['plaka'] }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row align-items-center">
                                        <div class="col-md-2">
                                            <label>Şase</label>
                                        </div>
                                        <div class="col-md-10">
                                            <input disabled class="form-control" name="arac_sase" value="{{ $expertise['sase_no'] }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="belgeBilgileriHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#belgeBilgileri" aria-expanded="true"
                                    aria-controls="belgeBilgileri">
                                Belge Bilgileri
                            </button>
                        </h2>
                        <div id="belgeBilgileri" class="accordion-collapse collapse show" aria-labelledby="belgeBilgileriHeading">
                            <div class="accordion-body">
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Belge Tarihi
                                        </div>
                                        <div class="col-md-9">
                                            <input type="datetime-local" disabled class="form-control" name="belge_tarihi" value="{{ $expertise['belge_tarihi'] }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Belge No
                                        </div>
                                        <div class="col-md-9">
                                            <input type="text" class="form-control" disabled name="belge_no" value="{{ $expertise['belge_no'] }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Belge Özel Kodu
                                        </div>
                                        <div class="col-md-9">
                                            <select disabled name="belge_ozel_kodu" class="select2">
                                                <option @if($expertise['belge_ozel_kodu'] == 1) selected @endif value="1">Onaylı</option>
                                                <option @if($expertise['belge_ozel_kodu'] == 0) selected @endif value="0">Hayır</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Sigorta Teklifi Ver
                                        </div>
                                        <div class="col-md-9">
                                            <select disabled name="sigorta_teklif_ver" class="select2">
                                                <option @if($expertise['sigorta_teklif_ver'] == 1) selected @endif value="1">Evet</option>
                                                <option @if($expertise['sigorta_teklif_ver'] == 0) selected @endif value="0">Hayır</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Yayın Yasağı
                                        </div>
                                        <div class="col-md-9">
                                            <select disabled name="yayin_yasagi" class="select2">
                                                <option @if($expertise['yayin_yasagi'] == 1) selected @endif value="1">Evet</option>
                                                <option @if($expertise['yayin_yasagi'] == 0) selected @endif value="0">Hayır</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Kayıt Bayi
                                        </div>
                                        <div class="col-md-9">
                                            <input disabled type="text" class="form-control" name="belge_no" value="{{ $expertise['kayitBranch'] }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Servis Bayi
                                        </div>
                                        <div class="col-md-9">
                                            <input disabled type="text" class="form-control" name="belge_no" value="{{ $expertise['branch'] }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Durum
                                        </div>
                                        <div class="col-md-9">
                                            <select name="status" class="select2">
                                                <option @if($expertise['status'] == 1) selected @endif value="1">Aktif</option>
                                                <option @if($expertise['status'] == 0) selected @endif value="0">Pasif</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div class="row">
                                        <div class="col-md-3">
                                            Çıkış Tarihi
                                        </div>
                                        <div class="col-md-9">
                                            <input disabled type="datetime-local" class="form-control" name="cikis_tarihi" value="{{ $expertise['cikis_tarihi'] }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="hizmetBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#hizmetBilgiler" aria-expanded="false"
                                    aria-controls="hizmetBilgiler">
                                Hizmet Bilgileri
                            </button>
                        </h2>
                        <div id="hizmetBilgiler" class="accordion-collapse collapse show" data-bs-parent="#accordionExample" aria-labelledby="hizmetBilgilerHeading">
                            <div class="accordion-body row">
                                <div class="services row">
                                    @foreach($expertise['getStocks'] as $expertiseStock)
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>Hizmet</label>
                                                <input type="text" class="form-control" disabled value="{{ $expertiseStock['ad'] }}">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>Geçerli Kampanya</label>
                                                <select disabled name="campaign_id[]" class="select2 js-states">
                                                    <option value="0">Seçilmedi</option>
                                                    @foreach($campaigns as $campaign)
                                                        <option @if($expertiseStock['campaign_id'] == $campaign->id) selected @endif value="{{ $campaign->id }}">{{ $campaign->name }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>Sorgu Hizmeti</label>
                                                <select disabled name="sorgu_hizmeti[]" class="select2 js-states">
                                                    <option value="1">Var</option>
                                                    <option @if($expertiseStock['sorgu_hizmet'] == 0) selected @endif value="0">Yok</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Yol Yardımı</label>
                                                <select disabled name="yol_yardimi[]" class="select2 js-states">
                                                    <option value="1">Var</option>
                                                    <option @if($expertiseStock['yol_yardimi'] == 0) selected @endif value="0">Yok</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>İskonto Tutarı</label>
                                                <input type="text" disabled class="form-control" value="{{ !empty($expertiseStock['iskonto_amount']) ? $expertiseStock['iskonto_amount']:0  }}" name="hizmet_tutari[]">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Hizmet Tutarı</label>
                                                <input type="text" disabled class="form-control" value="{{ $expertiseStock['hizmet_tutari'] }}" name="hizmet_tutari[]">
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Liste Fiyatı</label>
                                                <input type="text" disabled class="form-control" value="{{ $expertiseStock['liste_fiyati'] }}" name="liste_fiyati[]">
                                            </div>
                                        </div>
                                        <hr>
                                    @endforeach
                                </div>
                                <div class="row payments">
                                    @php $total_amount = 0 @endphp
                                    @foreach($expertise['getPayments'] as $expertisePayment)
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <div class="row align-items-center">
                                                    <div class="col-md-4">
                                                        <label>
                                                            @if($expertisePayment['type'] != "plus_kart" && !empty($expertisePayment['type']))
                                                                {{ __('arrays.payment_types')[$expertisePayment['type']] }} Hesap
                                                            @else
                                                                Plus Card No
                                                            @endif
                                                        </label>
                                                    </div>
                                                    <div class="col-md-8">
                                                        @if($expertisePayment['type'] != "plus_kart")
                                                            <select disabled class="select2" name="odeme_hesap[]">
                                                                <option value="0">Seçilmedi</option>
                                                            </select>
                                                        @else
                                                            <select disabled class="form-control" name="odeme_hesap[]">
                                                                <option value="{{$expertisePayment['plus_card_id']->id}}">{{$expertisePayment['plus_card_id']->no}}</option>
                                                            </select>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <div class="row align-items-center">
                                                    <div class="col-md-4">
                                                        @if($expertisePayment['type'] != "plus_kart")
                                                            {{ __('arrays.payment_types')[$expertisePayment['type']] }}
                                                        @else
                                                            @if(!empty($expertisePayment['plus_card_odeme_id']->balance_type) && $expertisePayment['plus_card_odeme_id']->balance_type == 'credits')
                                                                <label>{{ __('arrays.payment_types')[$expertisePayment['type']] }} Harcanan Puan</label>
                                                            @else
                                                                <label>{{ __('arrays.payment_types')[$expertisePayment['type']] }} @if(!empty($expertisePayment['plus_card_odeme_id']->balance_type) && $expertisePayment['plus_card_odeme_id']->balance_type == 'credits') Harcanan Puan @else Harcanan Kredi @endif</label>

                                                            @endif
                                                        @endif

                                                    </div>
                                                    <div class="col-md-8">
                                                        @if($expertisePayment['type'] != 'plus_kart')
                                                            <input disabled type="text" name="odeme_tutar[]" value="{{ $expertisePayment['amount'] }}" class="form-control">
                                                        @else
                                                            <input disabled type="text" name="odeme_tutar[]" value="1" class="form-control">

                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @php
                                            $total_amount +=$expertisePayment['amount'];
                                        @endphp
                                    @endforeach
                                    <div class="col-12 mt-3 text-end">
                                        <span>Toplam Ödeme Miktarı : {{$total_amount}}₺</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3" name="save_type">Kaydet</button>
            </div>
        </div>
    </form>
    @if($expertise['is_new'] == 1 && $expertise['arac_kontrol'] && $expertise['fren_kontrol'] && $expertise['kaporta_kontrol'] && $expertise['diagnostic_kontrol'] && $expertise['ic_kontrol'] && $expertise['lastik_jant_kontrol'] && $expertise['alt_motor_kontrol'] && $expertise['komponent_kontrol'])
        @if($expertise['audio_url'] == '/storage/')
            <button class="btn btn-danger btn-sm" id="startRecord">Kaydı Başlat</button>
            <button class="btn btn-danger btn-sm" id="stopRecord" disabled>Kaydı Durdur</button>
        @endif
        <audio id="audio" src="{{ $expertise['audio_url'] }}" controls></audio>
    @endif

@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <!-- Select2 Cdn -->
    <script src="/assets/js/select2.min.js"></script>
    <script>
        (function (){
            "use strict"
            $(".select2").select2({
                placeholder: "Ara.."
            });
        })();

        $('button[name="save_type"]').on('click', function (e) {
            e.preventDefault()

            var payment_type = $('input[name=payment_type]').val()
            if (payment_type != 'plus_kart') {
                let totalPayment = 0;
                $.each($('.odeme_tutar_input'), function (index, item) {
                    totalPayment += parseFloat($(item).val().replace(/[^0-9,-]+/g, "").replace(',', '.'))
                });
                if (totalPayment.toLocaleString('tr-TR', {
                    style: 'currency',
                    currency: 'TRY'
                }) < allTotal.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'})) {
                    Toast.fire({
                        icon: "error",
                        title: "Ödeme Tutarı Eksik Girildi!"
                    });
                    return false;
                } else if (totalPayment.toLocaleString('tr-TR', {
                    style: 'currency',
                    currency: 'TRY'
                }) > allTotal.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'})) {
                    Toast.fire({
                        icon: "error",
                        title: "Ödeme Tutarı Fazla Girildi!"
                    });
                    return false;
                }
            } else {
                var plus_card_payment_type = $('input[name=plus_card_payment_type]:checked').val();
                var plus_kart_id = $('input[name=plus_kart_id]:checked').val();
                if (plus_card_payment_type === undefined) {
                    Toast.fire({
                        icon: "error",
                        title: "Plus Card Kullanım Tipi Seçilmedi!"
                    });
                    return false;
                } else if (plus_kart_id === undefined) {
                    Toast.fire({
                        icon: "error",
                        title: "Plus Card Seçilmedi!"
                    });
                    return false;
                }

            }
        })

        let mediaRecorder;
        let audioChunks = [];

        document.getElementById("startRecord").addEventListener("click", async () => {
            // Kullanıcının mikrofonuna erişim sağlama
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder = new MediaRecorder(stream);

            // Ses verileri geldikçe bu verileri bir diziye ekleyin
            mediaRecorder.ondataavailable = event => {
                audioChunks.push(event.data);
            };

            // Kayıt durduğunda ses verilerini birleştirip audio elementinde oynat
            mediaRecorder.onstop = () => {
                const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = document.getElementById("audio");
                audio.src = audioUrl;

                const formData = new FormData();
                formData.append("audioFile", audioBlob, "recording.wav");
                formData.append("_token", '{{ csrf_token() }}');

                $.ajax({
                    url: '{{ route('voiceRecord') }}', // Sunucu tarafı işlemci URL'si
                    type: 'POST',
                    data: formData,
                    processData: false,  // jQuery'nin data'yı işlemesini engelle
                    contentType: false,  // İçerik tipi başlığını jQuery'nin ayarlamasını engelle
                    success: function(data) {
                        $('#mainForm').append('<input type="hidden" name="audio_url" value="'+data.audio_url+'"/>')
                    },
                    error: function(error) {
                        console.error('Error:', error);
                    }
                });

                audioChunks = [];
            };

            // Kaydı başlat ve düğmeleri güncelle
            mediaRecorder.start();
            document.getElementById("startRecord").disabled = true;
            document.getElementById("stopRecord").disabled = false;
        });

        document.getElementById("stopRecord").addEventListener("click", () => {
            // Kaydı durdur ve düğmeleri güncelle
            mediaRecorder.stop();
            document.getElementById("startRecord").disabled = false;
            document.getElementById("stopRecord").disabled = true;
        });




    </script>
@endpush
