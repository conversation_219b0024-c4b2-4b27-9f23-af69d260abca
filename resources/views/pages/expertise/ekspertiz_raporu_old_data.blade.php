@extends('pages.build')
@section('title','Ekspertiz Raporu')
@push('css')
    <style>
        table{
            border: 1px solid #000 !important;
        }
        th{
            background-color: #cecece57 !important
        }
        .page-break{
            page-break-after: always;
        }
        .table-bordered>:not(caption)>*>*{
            border-width: 1px;
        }
        .table td, .table th{
            padding: .35rem;
        }
        .table-col-6 td{
            width: 16.667% !important;
            border: 1px solid;
        }
        @media print{
            body{
                visibility: hidden;
            }

            #divRaporEkrani{
                visibility: visible;
            }
        }
    </style>
@endpush
@section('content')
    @php

        $electrical_values = array();
    @endphp
    @if($authUser->type == "admin")
        <div id="divRaporEkrani">
            <div class="table-responsive">
                <table class="table">
                    <tbody>
                    <tr>
                        <td colspan="2" style="width: 33%;text-align: center">
                            <img src="/assets/isemri_logo.png" style="width: 100%">
                            <h5 style="color: darkorange">444 5 417</h5>
                        </td>
                        <td colspan="3" style="width: 33%" class="text-center">
                            <b>2. EL ARAÇ EKSPERTİZ RAPORU</b><br>
                            Bağımsız Oto Ekspertiz Merkezi<br> www.umranoto.com<br><br>
                            <b>{{ $expertise['getBranch'] }}</b>
                        </td>
                        <td colspan="1" class="text-center" style="width: 25%">
                            <b>TSE-HYB Belge No:</b><h5 class="text-danger">{{ $expertise['tse_kodu'] }}</h5>
                        </td>
                        <td colspan=""  style="width: 25%;text-align:center;">
                            {!! $expertise['qr'] !!}
                        </td>
                    </tr>
                    <tr class="table-col-6">
                        <td colspan="1">Bayi:</td>
                        <td colspan="2"><b>{{ $expertise['getBranch'] }}</b></td>
                        <td colspan="1">Marka/Model:</td>
                        <td colspan="1">{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                        <td colspan="1">KM:</td>
                        <td colspan="1"><b>{{ number_format($expertise['km'] ?? 0,0,'.','.') }}</b></td>
                    </tr>
                    <tr class="table-col-6">
                        <td colspan="1">Belge No:</td>
                        <td colspan="2"><b class="text-danger">{{ $expertise['belge_no'] }}</b></td>
                        <td colspan="1">Model Yılı:</td>
                        <td colspan="1"><b>{{ $expertise['model_yili'] }}</b></td>
                        <td colspan="1">Motor Hacmi:</td>
                        <td colspan="1"><b>{{ number_format($expertise['motor_hacmi'] ?? 0,0,'.','.') }}</b></td>
                    </tr>
                    <tr class="table-col-6">
                        <td colspan="1">Giriş Tarihi ve Saati:</td>
                        <td colspan="2"><b>{{ $expertise['created_at'] }}</b></td>
                        <td colspan="1">Şase Numarası:</td>
                        <td colspan="1"><b>
                                {{ $expertise['sase_no'] }}
                            </b>
                        </td>
                        <td colspan="1">Yakıt Tipi:</td>
                        <td colspan="1"><b>{{ $expertise['getFuel'] }}</b></td>
                    </tr>
                    <tr class="table-col-6">
                        <td colspan="1">Çıkış Tarihi ve Saati:</td>
                        <td colspan="2"><b>{{ $expertise['cikis_tarihi'] }}</b></td>
                        <td colspan="1">Plaka:</td>
                        <td colspan="1"><b>
                                {{ $expertise['plaka'] }}
                            </b></td>
                        <td colspan="1">Vites Tipi:</td>
                        <td colspan="1"><b>{{ $expertise['getGear'] }}</b></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row">
                <div class="col-md-6 table-responsive">
                    <table class="table table-bordered mt-3">
                        <thead>
                        <tr>
                            <th>PARÇA ADI</th>
                            <th>ORJ.</th>
                            <th>BOY.</th>
                            <th>DEĞ.</th>
                            <th>DÜZ.</th>
                            <th>AÇIKLAMA</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($bodyworks1 as $key => $bodywork)
                            <tr>
                                <td><b>{{ $bodywork }}</b></td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6 table-responsive">
                    <table class="table table-bordered mt-3">
                        <thead>
                        <tr>
                            <th>PARÇA ADI</th>
                            <th>ORJ.</th>
                            <th>BOY.</th>
                            <th>DEĞ.</th>
                            <th>DÜZ.</th>
                            <th>AÇIKLAMA</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($bodyworks2 as $key => $bodywork)
                            <tr>
                                <td><b>{{ $bodywork }}</b></td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6 table-responsive">
                    <img src="{{ $expertise['bodywork_image'] }}" class="img-fluid">
                </div>
                <div class="col-md-6 table-responsive">
                    <table class="table table-bordered mt-3">
                        <thead>
                        <tr>
                            <th>PARÇA ADI</th>
                            <th>ORJ.</th>
                            <th>BOY.</th>
                            <th>DEĞ.</th>
                            <th>DÜZ.</th>
                            <th>AÇIKLAMA</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($bodyworks3 as $key => $bodywork)
                            <tr>
                                <td><b>{{ $bodywork }}</b></td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                            </tr>
                        @endforeach
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="6">
                                Tamponlar plastik aksam olduğundan boya ve değişim değerlendirilmesi yapılmamıştır. ORJ: Orijinal Parça DÜZ: Düzeltme ve/veya macun işlemi uygulanmış parça ST : Sökülüp takılmış parça DEĞ: Değişmiş Parça
                                (ST:Kendi üzerindeki ya da aynı model araçtan alınan birebir parça)
                            </td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
                <div class="col-md-12 table-responsive">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th colspan="4" class="text-center">NOTLAR</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php
                            $td_count = 10;
                            if($td_count < count($expertise['allNotes'][1])){
                                $td_count = count($expertise['allNotes'][1]);
                            }

                            foreach($expertise['allNotes'][1] as $key => $notes_array){
                                $expertise['allNotes'][1][$key] = '[KPR] '.$notes_array;
                            }

                            // Toplam eleman sayısını bul
                            $totalCount = count($expertise['allNotes'][1]);




                            $firstArraykpr = array_slice($expertise['allNotes'][1], 0, ceil($td_count));
                            $secondArraykpr = array_splice($expertise['allNotes'][1], ceil($td_count));
                        @endphp
                        @for ($i = 0; $i < $td_count; $i++)
                            <tr>
                                <td class="col-6" @if(empty($firstArraykpr[$i])) style="padding:15px;" @endif>{{!empty($firstArraykpr[$i]) ? $firstArraykpr[$i]:''}} </td>
                                <td class="col-6" @if(empty($firstArraykpr[$i])) style="padding:15px;" @endif> {{!empty($secondArraykpr[$i]) ? $secondArraykpr[$i] : ''}} </td>
                            </tr>
                        @endfor
                        </tbody>
                    </table>
                    <table class="table table-bordered">

                        <tbody>
                        <tr>
                            <td style="background-color: #ff1000;color: #fff;text-align: center;font-weight: bolder" colspan="4">SERVİSLERİMİZDEN ALINAN EKSPERTİZ RAPORLARINA www.umranoto.com ADRESİNDEN ULAŞABİLİRSİNİZ.</td>
                        </tr>
                        <tr>
                            <td colspan="4">
                                Umran Oto Ekspertiz Merkezleri tüm dünyadaki ekspertiz hizmetleri gibi aracın o anki durumunu analiz eder ve bilgi verir. Ekspertiz yaptığı aracın kesinlikle ileride arıza ve problem çıkarmayacağını garanti edemez. Aracın o anki görülen hataları kontrol edilerek raporlanır. Kaporta boya değerlendirmesine itiraz 72 saat, motor ile ilgili değerlendirmeye itiraz 24 saattir. Bayilerimizde kilometre orijinalliği tespiti yapılmamaktadır. Elektronik arşiv süresi 5 yıldır.
                            </td>
                        </tr>
                        <tr class="text-center">
                            <td>
                                KAPORTA BOYA TEKNİKERİ
                            </td>
                            <td>
                                SERVİS MÜDÜRÜ
                            </td>
                            <td>
                                MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['kvkk_durum'] == "true" && !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : $expertise['getAlici'] }}</b>
                            </td>
                            <td>
                                MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['kvkk_durum'] == "true" && !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : $expertise['getSatici'] }}</b>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-between" style="font-size: 1.1rem">
                        <span><span class="text-danger">KBR</span> 1/1 Örnek Mh. Fehmi Tokay Cad. No:18 B ATAŞEHİR İSTANBUL</span>
                        <span>ÜMRANİYE V.D. 0910564940</span>
                    </div>
                </div>
            </div>
            <div class="page-break"></div>
            <div class="table-responsive">
                <table class="table">
                    <tbody>
                    <tr>
                        <td colspan="2" style="width: 33%;text-align: center">
                            <img src="/assets/isemri_logo.png" style="width: 100%">
                            <h5 style="color: darkorange">444 5 417</h5>
                        </td>
                        <td colspan="3" style="width: 33%" class="text-center">
                            <b>2. EL ARAÇ EKSPERTİZ RAPORU</b><br>
                            Bağımsız Oto Ekspertiz Merkezi<br> www.umranoto.com<br><br>
                            <b>{{ $expertise['getBranch'] }}</b>
                        </td>
                        <td colspan="1" class="text-center" style="width: 25%">
                            <b>TSE-HYB Belge No:</b><h5 class="text-danger">{{ $expertise['tse_kodu'] }}</h5>
                        </td>
                        <td colspan=""  style="width: 25%;text-align:center;">
                            {!! $expertise['qr'] !!}
                        </td>
                    </tr>
                    <tr class="table-col-6">
                        <td colspan="1">Bayi:</td>
                        <td colspan="2"><b>{{ $expertise['getBranch'] }}</b></td>
                        <td colspan="1">Marka/Model:</td>
                        <td colspan="1">{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                        <td colspan="1">KM:</td>
                        <td colspan="1"><b>{{ number_format($expertise['km'] ?? 0,0,'.','.') }}</b></td>
                    </tr>
                    <tr class="table-col-6">
                        <td colspan="1">Belge No:</td>
                        <td colspan="2"><b class="text-danger">{{ $expertise['belge_no'] }}</b></td>
                        <td colspan="1">Model Yılı:</td>
                        <td colspan="1"><b>{{ $expertise['model_yili'] }}</b></td>
                        <td colspan="1">Motor Hacmi:</td>
                        <td colspan="1"><b>{{ number_format($expertise['motor_hacmi'] ?? 0,0,'.','.') }}</b></td>
                    </tr>
                    <tr class="table-col-6">
                        <td colspan="1">Giriş Tarihi ve Saati:</td>
                        <td colspan="2"><b>{{ $expertise['created_at'] }}</b></td>
                        <td colspan="1">Şase Numarası:</td>
                        <td colspan="1"><b>
                                {{ $expertise['kvkk_durum'] == "true" && !empty($expertise['sase_no']) ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : $expertise['sase_no'] }}
                            </b>
                        </td>
                        <td colspan="1">Yakıt Tipi:</td>
                        <td colspan="1"><b>{{ $expertise['getFuel'] }}</b></td>
                    </tr>
                    <tr class="table-col-6">
                        <td colspan="1">Çıkış Tarihi ve Saati:</td>
                        <td colspan="2"><b>{{ $expertise['cikis_tarihi'] }}</b></td>
                        <td colspan="1">Plaka:</td>
                        <td colspan="1"><b>
                                {{ $expertise['kvkk_durum'] == "true" && !empty($expertise['plaka']) ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : $expertise['plaka'] }}
                            </b></td>
                        <td colspan="1">Vites Tipi:</td>
                        <td colspan="1"><b>{{ $expertise['getGear'] }}</b></td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="row">
                <div class="col-md-6 table-responsive">
                    <table class="table table-bordered mt-3">
                        <thead>
                        <tr>
                            <th>MOTOR BÖLÜMÜ</th>
                            <th>DURUMU</th>
                            <th>AÇIKLAMA</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($engines1 as $key => $subControlAndEngine1)
                            @if(($key == "co2_kacak_testi" && $expertise['co2_kacak_testi'] == true) || $key != "co2_kacak_testi")
                                <tr>
                                    <td><b>{{ $subControlAndEngine1 }}</b></td>
                                    <td class="text-center">
                                        @if($expertise['getSubControlsAndEngines'][$key]['answer'] == "Turbo Yok" || $expertise['getSubControlsAndEngines'][$key]['answer'] == "turbo_yok" || $expertise['getSubControlsAndEngines'][$key]['answer'] == "deger_giriniz")
                                            --
                                            @el
                                            {{ucfirst($expertise['getSubControlsAndEngines'][$key]['answer'])}}
                                        @endif
                                    </td>
                                    <td>{!! $expertise['getSubControlsAndEngines'][$key]['note'] !!}</td>
                                </tr>
                            @endif
                        @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6 table-responsive">
                    <table class="table table-bordered mt-3">
                        <thead>
                        <tr>
                            <th>İÇ KONTROLLER</th>
                            <th>DURUM</th>
                            <th>AÇIKLAMA</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach(__('arrays.internal_controls') as $key => $internalControl)
                            <tr>
                                <td><b>{{ $internalControl }}</b></td>
                                <td class="text-center">{!! $expertise['getInternalControls'][$key]['answer'] != 'yok' ? ucfirst($expertise['getInternalControls'][$key]['answer']) : '---' !!}</td>
                                <td>{!! $expertise['getInternalControls'][$key]['note'] !!}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6 table-responsive">
                    <table class="table table-bordered mt-3">
                        <thead>
                        <tr>
                            <th>ALT KONTROLLER</th>
                            <th>DURUM</th>
                            <th>AÇIKLAMA</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($engines2 as $key => $engine2)
                            @if($engine2 != 'Elektrik Sistemi')

                                @if($key == 'fren_sistemi_kontrolu')

                                    <tr>
                                        <td><b>{{__('arrays.components')['sanziman_vites_gecisleri_ve_ses_kontrolu']}} </b></td>
                                        <td class="text-center">
                                            @if($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "yok" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Yok")
                                                ----
                                            @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "iyi" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "İyi")
                                                İyi
                                            @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "hafif" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Hafif")
                                                Hafif
                                            @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "test yapılmadı" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Test Yapılmadı")
                                                Test Yapılmadı
                                            @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "kötü" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Kötü")
                                                Kötü
                                            @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "orta" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Orta")
                                                Orta
                                            @endif
                                        </td>
                                        <td>{{$expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['note']}}</td>
                                    </tr>
                                @endif
                                <tr>
                                    <td><b>{{ $engine2 }}</b></td>
                                    <td class="text-center">
                                        {{ ucfirst($expertise['getSubControlsAndEngines'][$key]['answer']) }}
                                    </td>
                                    <td>{!! $expertise['getSubControlsAndEngines'][$key]['note'] !!}</td>
                                </tr>
                            @else
                                @php
                                    $electrical_values[0] = [
                                        'name' => $engine2,
                                        'answer' => $expertise['getSubControlsAndEngines'][$key]['answer'],
                                        'note' => $expertise['getSubControlsAndEngines'][$key]['note'],
                                    ];
                                @endphp
                            @endif

                        @endforeach
                        <tr>
                            <td><b>{{__('arrays.components')['dortceker_kontrolu']}}</b></td>
                            <td class="text-center">
                                @if($expertise['getComponents']['dortceker_kontrolu']['answer'] == "yok" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Yok")
                                    ----
                                @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "iyi" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "İyi")
                                    İyi
                                @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "test yapılmadı" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Test Yapılmadı")
                                    Test Yapılmadı
                                @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "kötü" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Kötü")
                                    Kötü
                                @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "orta" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Orta")
                                    Orta
                                @endif
                            </td>
                            <td>{{$expertise['getComponents']['dortceker_kontrolu']['note']}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-md-6 table-responsive">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">LASTİK VE JANTLAR</th>
                            <th class="text-center">DURUM</th>
                            <th class="text-center">DİŞ(mm)</th>
                            <th class="text-center">BASINÇ</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach(__('arrays.tire_and_rims') as $key => $tire)
                            <tr>
                                <td>{{ $tire }}</td>
                                <td >
                                    @if($expertise['getTireAndRims'][$key]['sorunlu_mu'] != 1)
                                        Sorunsuz
                                    @else
                                        {{$expertise['getTireAndRims'][$key]['note']}}
                                    @endif

                                </td>
                                <td class="text-center">{!! !empty($expertise['getTireAndRims'][$key]['dis']) ? number_format($expertise['getTireAndRims'][$key]['dis'], 1) : ''; !!}</td>
                                <td class="text-center">{!! $expertise['getTireAndRims'][$key]['basinc'] !!}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>

                    <table class="table table-bordered mt-3">
                        <thead>
                        <tr>
                            <th>DİAGNOSTİK CİHAZ KONTROL SONUÇLARI</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php
                            $td_count = 4;
                            if(count($expertise['allNotes'][3]) > $td_count){
                                $td_count = count($expertise['allNotes'][3]);
                            }

                            foreach($expertise['allNotes'][3] as $key => $notes_array){
                                $expertise['allNotes'][3][$key] = '[DIAG] '.$notes_array;
                            }
                        @endphp
                        @for ($i = 0; $i < $td_count; $i++)
                            <tr>

                                <td  @if(empty($expertise['allNotes'][3][$i])) style="padding:15px;" @endif>{{!empty($expertise['allNotes'][3][$i]) ? $expertise['allNotes'][3][$i] : ''}}</td>
                            </tr>
                        @endfor
                        </tbody>
                    </table>
                </div>
                <div class="col-md-12 table-responsive mt-3">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th colspan="4" class="text-center">NOTLAR</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php
                            $td_count = 10;
                            $td_count_2 = (count($expertise['allNotes'][4]) + count($expertise['allNotes'][5])+ count($expertise['allNotes'][6]) + count($expertise['allNotes'][7]) + count($expertise['allNotes'][2])) / 2;
                            if($td_count_2 > 10){
                                $td_count = $td_count_2;
                            }

                            foreach($expertise['allNotes'][4] as $key => $notes_array){
                                $expertise['allNotes'][4][$key] = '[ICK] '.$notes_array;
                            }
                            foreach($expertise['allNotes'][5] as $key => $notes_array){
                                $expertise['allNotes'][5][$key] = '[LAJ] '.$notes_array;
                            }
                            foreach($expertise['allNotes'][6] as $key => $notes_array){
                                $expertise['allNotes'][6][$key] = '[ALTK] '.$notes_array;
                            }
                            foreach($expertise['allNotes'][7] as $key => $notes_array){
                                $expertise['allNotes'][7][$key] = '[ALTK] '.$notes_array;
                            }
                            foreach($expertise['allNotes'][2] as $key => $notes_array){
                                $expertise['allNotes'][2][$key] = '[FRN] '.$notes_array;
                            }

                            $mergedArray = array_merge(
                                $expertise['allNotes'][2    ],
                                $expertise['allNotes'][4],
                                $expertise['allNotes'][5],
                                $expertise['allNotes'][6],
                                $expertise['allNotes'][7]
                            );

                            // Toplam eleman sayısını bul

                            $firstArray = array_slice($mergedArray, 0, ceil($td_count));
                            $secondArray = array_splice($mergedArray, ceil($td_count));

                        @endphp
                        @for ($i = 0; $i < $td_count; $i++)
                            <tr>
                                <td class="col-6" @if(empty($firstArray[$i])) style="padding:15px;" @endif>{{!empty($firstArray[$i]) ? $firstArray[$i]:''}} </td>
                                <td @if(empty($firstArray[$i])) style="padding:15px;" @endif> {{!empty($secondArray[$i]) ? $secondArray[$i] : ''}} </td>
                            </tr>
                        @endfor

                        </tbody>
                    </table>
                </div>
                <div class="col-12 table-responsive">
                    <table class="table table-bordered">
                        <tbody>
                        <tr>
                            <td>
                                <p>1* Ekspertiz işlemlerinde triger seti kontrolü yapılmamaktadır.</p>
                                <p>2* Akü ölçümü ekspertiz işlemi anındaki değerlere göre yapılmaktadır. Sonraki kullanımdan kaynaklanan sorunlarda firmamızın herhangi bir garantisi yoktur.</p>
                                <p>3* Kumanda kontrol düğmeleri kontrolü; Far düğmeleri, Sinyal kolu, silecek kolu, elektrikli ayna(ısıtma hariç), cam düğmeleri, bağaj, depo kapağı düğmesi, cam perdeleri, tavan ve sunroof düğmesi kontrollerini kapsamaktadır. Bunların dışında kalan kumanda düğmeleri kontrol edilmemektedir.</p>
                                <p>4* Farlarda yalnızca kırık, çatlak ve yanıp yanmadığının kontrolü yapılmaktadır. Orijinallik, xenon ve su alma testi yapılmamaktadır.</p>
                            </td>
                            <td colspan="3">
                                @php
                                    $hafif_open = 2;
                                     foreach(__('arrays.components') as $key => $component){
                                         if($expertise['getComponents'][$key]['answer'] == "hafif" || $expertise['getComponents'][$key]['answer'] == "Hafif"){
                                             if($expertise['getComponents'][$key]['title'] != 'Şanzıman Vites Geçişleri ve Ses Kont.' && $expertise['getComponents'][$key]['title'] != 'Dörtçeker Kontrolü'	){
                                                  $hafif_open = 1;
                                             }

                                         }
                                     }

                                @endphp
                                <table class="table table-bordered">
                                    <thead>
                                    <tr>
                                        <th>KOMPONENTLER</th>
                                        <th>İYİ</th>
                                        <th>ORTA</th>
                                        <th>KÖTÜ</th>
                                        <th>Açıklama </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach(__('arrays.components') as $key => $component)
                                        @if($key != "dortceker_kontrolu" && $key != 'sanziman_vites_gecisleri_ve_ses_kontrolu')
                                            <tr>
                                                <td>{{ $component }}</td>
                                                @if($expertise['getComponents'][$key]['answer'] == 'test_yapılmadı' || $expertise['getComponents'][$key]['answer'] == 'Test Yapılmadı')
                                                    <td colspan="5" style="text-align: center"><span class="test-yapilmadi fw-semibold">Test Yapılmadı</span></td>
                                                @elseif($expertise['getComponents'][$key]['answer'] == 'yok' || $expertise['getComponents'][$key]['answer'] == 'Yok')
                                                    <td colspan="5" style="text-align: center"><span class="test-yapilmadi fw-semibold">Araçta Bu Özellik Bulunmamaktadır.</span></td>
                                                @else
                                                    <td>{!! $expertise['getComponents'][$key]['answer'] == 'iyi' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                                    <td>{!! $expertise['getComponents'][$key]['answer'] == 'orta' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                                    <td>{!! $expertise['getComponents'][$key]['answer'] == 'kötü' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                                    <td>
                                                        {{$expertise['getComponents'][$key]['note']}}
                                                    </td>
                                                @endif

                                            </tr>
                                        @endif
                                    @endforeach
                                    <tr>
                                        <td>{{$electrical_values[0]['name']}} </td>
                                        @if($electrical_values[0]['answer'] == "test-yapilmadi" || $electrical_values[0]['answer'] == "Test Yapılmadı")
                                            <td colspan="5" style="text-align: center"><span class="test-yapilmadi fw-semibold">Test Yapılmadı</span></td>
                                        @elseif($expertise['getComponents'][$key]['answer'] == 'yok' || $expertise['getComponents'][$key]['answer'] == 'Yok')
                                            <td colspan="5" style="text-align: center"><span class="test-yapilmadi fw-semibold">Araçta Bu Özellik Bulunmamaktadır.</span></td>
                                        @endif

                                        <td>{!! $electrical_values[0]['answer'] == 'iyi' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                        <td>{!! $electrical_values[0]['answer'] == 'orta' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                        <td>{!! $electrical_values[0]['answer'] == 'kötü' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                        <td>{{$electrical_values[0]['note']}}</td>
                                    </tr>
                                    </tbody>
                                </table>

                                <div class="d-flex justify-content-between" style="font-size: 1.1rem">
                                    <span><span class="text-danger">KBR</span> 1/1 Örnek Mh. Fehmi Tokay Cad. No:18 B ATAŞEHİR İSTANBUL</span>
                                    <span>ÜMRANİYE V.D. 0910564940</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td style="background-color: #ff1000;color: #fff;text-align: center;font-weight: bolder" colspan="4">SERVİSLERİMİZDEN ALINAN EKSPERTİZ RAPORLARINA www.umranoto.com ADRESİNDEN ULAŞABİLİRSİNİZ.</td>
                        </tr>
                        <tr>
                            <td colspan="4">
                                Umran Oto Ekspertiz Merkezleri tüm dünyadaki ekspertiz hizmetleri gibi aracın o anki durumunu analiz eder ve bilgi verir. Ekspertiz yaptığı aracın kesinlikle ileride arıza ve problem çıkarmayacağını garanti edemez. Aracın o anki görülen hataları kontrol edilerek raporlanır. Kaporta boya değerlendirmesine itiraz 72 saat, motor ile ilgili değerlendirmeye itiraz 24 saattir. Bayilerimizde kilometre orijinalliği tespiti yapılmamaktadır. Elektronik arşiv süresi 5 yıldır.
                            </td>
                        </tr>
                        <tr class="text-center">
                            <td>
                                KAPORTA BOYA TEKNİKERİ
                            </td>
                            <td>
                                SERVİS MÜDÜRÜ
                            </td>
                            <td>
                                MÜŞTERİ (ALICI)<br><br><b>{{ $expertise['getSatici'] }}</b>
                            </td>
                            <td>
                                MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    @endif
    <div id="divRaporEkrani_fitreli" @if($authUser->type == "admin") style="display: none;" @endif>
        <div class="table-responsive">
            <table class="table">
                <tbody>
                <tr>
                    <td colspan="2" style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 100%">
                        <h5 style="color: darkorange">444 5 417</h5>
                    </td>
                    <td colspan="3" style="width: 33%" class="text-center">
                        <b>2. EL ARAÇ EKSPERTİZ RAPORU</b><br>
                        Bağımsız Oto Ekspertiz Merkezi<br> www.umranoto.com<br><br>
                        <b>{{ $expertise['getBranch'] }}</b>
                    </td>
                    <td colspan="1" class="text-center" style="width: 25%">
                        <b>TSE-HYB Belge No:</b><h5 class="text-danger">{{ $expertise['tse_kodu'] }}</h5>
                    </td>
                    <td colspan=""  style="width: 25%;text-align:center;">
                        {!! $expertise['qr'] !!}
                    </td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Bayi:</td>
                    <td colspan="2"><b>{{ $expertise['getBranch'] }}</b></td>
                    <td colspan="1">Marka/Model:</td>
                    <td colspan="1">{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td colspan="1">KM:</td>
                    <td colspan="1"><b>{{ number_format($expertise['km'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Belge No:</td>
                    <td colspan="2"><b class="text-danger">{{ $expertise['belge_no'] }}</b></td>
                    <td colspan="1">Model Yılı:</td>
                    <td colspan="1"><b>{{ $expertise['model_yili'] }}</b></td>
                    <td colspan="1">Motor Hacmi:</td>
                    <td colspan="1"><b>{{ number_format($expertise['motor_hacmi'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Giriş Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['created_at'] }}</b></td>
                    <td colspan="1">Şase Numarası:</td>
                    <td colspan="1"><b>
                            {{ $expertise['kvkk_durum'] == "true" && !empty($expertise['sase_no']) ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td colspan="1">Yakıt Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getFuel'] }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Çıkış Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['cikis_tarihi'] }}</b></td>
                    <td colspan="1">Plaka:</td>
                    <td colspan="1"><b>
                            {{ $expertise['kvkk_durum'] == "true" && !empty($expertise['plaka']) ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : $expertise['plaka'] }}
                        </b></td>
                    <td colspan="1">Vites Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getGear'] }}</b></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="row">
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>PARÇA ADI</th>
                        <th>ORJ.</th>
                        <th>BOY.</th>
                        <th>DEĞ.</th>
                        <th>DÜZ.</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($bodyworks1 as $key => $bodywork)
                        <tr>
                            <td><b>{{ $bodywork }}</b></td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>PARÇA ADI</th>
                        <th>ORJ.</th>
                        <th>BOY.</th>
                        <th>DEĞ.</th>
                        <th>DÜZ.</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($bodyworks2 as $key => $bodywork)
                        <tr>
                            <td><b>{{ $bodywork }}</b></td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
            <div class="col-md-6 table-responsive">
                <img src="{{ $expertise['bodywork_image'] }}" class="img-fluid">
            </div>
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>PARÇA ADI</th>
                        <th>ORJ.</th>
                        <th>BOY.</th>
                        <th>DEĞ.</th>
                        <th>DÜZ.</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($bodyworks3 as $key => $bodywork)
                        <tr>
                            <td><b>{{ $bodywork }}</b></td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td class="text-center">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                            <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                        </tr>
                    @endforeach
                    </tbody>
                    <tfoot>
                    <tr>
                        <td colspan="6">
                            Tamponlar plastik aksam olduğundan boya ve değişim değerlendirilmesi yapılmamıştır. ORJ: Orijinal Parça DÜZ: Düzeltme ve/veya macun işlemi uygulanmış parça ST : Sökülüp takılmış parça DEĞ: Değişmiş Parça
                            (ST:Kendi üzerindeki ya da aynı model araçtan alınan birebir parça)
                        </td>
                    </tr>
                    </tfoot>
                </table>
            </div>
            <div class="col-md-12 table-responsive">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th colspan="4" class="text-center">NOTLAR</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php
                        $td_count = 10;
                        if($td_count < count($expertise['allNotes'][1])){
                            $td_count = count($expertise['allNotes'][1]);
                        }

                        foreach($expertise['allNotes'][1] as $key => $notes_array){
                            $expertise['allNotes'][1][$key] = '[KPR] '.$notes_array;
                        }

                        // Toplam eleman sayısını bul
                        $totalCount = count($expertise['allNotes'][1]);




                        $firstArraykpr = array_slice($expertise['allNotes'][1], 0, ceil($td_count));
                        $secondArraykpr = array_splice($expertise['allNotes'][1], ceil($td_count));
                    @endphp
                    @for ($i = 0; $i < $td_count; $i++)
                        <tr>
                            <td class="col-6" @if(empty($firstArraykpr[$i])) style="padding:15px;" @endif>{{!empty($firstArraykpr[$i]) ? $firstArraykpr[$i]:''}} </td>
                            <td class="col-6" @if(empty($firstArraykpr[$i])) style="padding:15px;" @endif> {{!empty($secondArraykpr[$i]) ? $secondArraykpr[$i] : ''}} </td>
                        </tr>
                    @endfor
                    </tbody>
                </table>
                <table class="table table-bordered">

                    <tbody>
                    <tr>
                        <td style="background-color: #ff1000;color: #fff;text-align: center;font-weight: bolder" colspan="4">SERVİSLERİMİZDEN ALINAN EKSPERTİZ RAPORLARINA www.umranoto.com ADRESİNDEN ULAŞABİLİRSİNİZ.</td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            Umran Oto Ekspertiz Merkezleri tüm dünyadaki ekspertiz hizmetleri gibi aracın o anki durumunu analiz eder ve bilgi verir. Ekspertiz yaptığı aracın kesinlikle ileride arıza ve problem çıkarmayacağını garanti edemez. Aracın o anki görülen hataları kontrol edilerek raporlanır. Kaporta boya değerlendirmesine itiraz 72 saat, motor ile ilgili değerlendirmeye itiraz 24 saattir. Bayilerimizde kilometre orijinalliği tespiti yapılmamaktadır. Elektronik arşiv süresi 5 yıldır.
                        </td>
                    </tr>
                    <tr class="text-center">
                        <td>
                            KAPORTA BOYA TEKNİKERİ
                        </td>
                        <td>
                            SERVİS MÜDÜRÜ
                        </td>
                        <td>
                            MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['kvkk_durum'] == "true" && !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : $expertise['getAlici'] }}</b>
                        </td>
                        <td>
                            MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['kvkk_durum'] == "true" && !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : $expertise['getSatici'] }}</b>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div class="d-flex justify-content-between" style="font-size: 1.1rem">
                    <span><span class="text-danger">KBR</span> 1/1 Örnek Mh. Fehmi Tokay Cad. No:18 B ATAŞEHİR İSTANBUL</span>
                    <span>ÜMRANİYE V.D. 0910564940</span>
                </div>
            </div>
        </div>
        <div class="page-break"></div>
        <div class="table-responsive">
            <table class="table">
                <tbody>
                <tr>
                    <td colspan="2" style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 100%">
                        <h5 style="color: darkorange">444 5 417</h5>
                    </td>
                    <td colspan="3" style="width: 33%" class="text-center">
                        <b>2. EL ARAÇ EKSPERTİZ RAPORU</b><br>
                        Bağımsız Oto Ekspertiz Merkezi<br> www.umranoto.com<br><br>
                        <b>{{ $expertise['getBranch'] }}</b>
                    </td>
                    <td colspan="1" class="text-center" style="width: 25%">
                        <b>TSE-HYB Belge No:</b><h5 class="text-danger">{{ $expertise['tse_kodu'] }}</h5>
                    </td>
                    <td colspan=""  style="width: 25%;text-align:center;">
                        {!! $expertise['qr'] !!}
                    </td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Bayi:</td>
                    <td colspan="2"><b>{{ $expertise['getBranch'] }}</b></td>
                    <td colspan="1">Marka/Model:</td>
                    <td colspan="1">{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td colspan="1">KM:</td>
                    <td colspan="1"><b>{{ number_format($expertise['km'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Belge No:</td>
                    <td colspan="2"><b class="text-danger">{{ $expertise['belge_no'] }}</b></td>
                    <td colspan="1">Model Yılı:</td>
                    <td colspan="1"><b>{{ $expertise['model_yili'] }}</b></td>
                    <td colspan="1">Motor Hacmi:</td>
                    <td colspan="1"><b>{{ number_format($expertise['motor_hacmi'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Giriş Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['created_at'] }}</b></td>
                    <td colspan="1">Şase Numarası:</td>
                    <td colspan="1"><b>
                            {{ $expertise['kvkk_durum'] == "true" && !empty($expertise['sase_no']) ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td colspan="1">Yakıt Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getFuel'] }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Çıkış Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['cikis_tarihi'] }}</b></td>
                    <td colspan="1">Plaka:</td>
                    <td colspan="1"><b>
                            {{ $expertise['kvkk_durum'] == "true" && !empty($expertise['plaka']) ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : $expertise['plaka'] }}
                        </b></td>
                    <td colspan="1">Vites Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getGear'] }}</b></td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="row">
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>MOTOR BÖLÜMÜ</th>
                        <th>DURUMU</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($engines1 as $key => $subControlAndEngine1)
                        @if(($key == "co2_kacak_testi" && $expertise['co2_kacak_testi'] == true) || $key != "co2_kacak_testi")
                            <tr>
                                <td><b>{{ $subControlAndEngine1 }}</b></td>
                                <td class="text-center">
                                    @if($expertise['getSubControlsAndEngines'][$key]['answer'] == "Turbo Yok" || $expertise['getSubControlsAndEngines'][$key]['answer'] == "turbo_yok" || $expertise['getSubControlsAndEngines'][$key]['answer'] == "deger_giriniz")
                                        --
                                    @el
                                        {{ucfirst($expertise['getSubControlsAndEngines'][$key]['answer'])}}
                                    @endif
                                </td>
                                <td>{!! $expertise['getSubControlsAndEngines'][$key]['note'] !!}</td>
                            </tr>
                        @endif
                    @endforeach
                    </tbody>
                </table>
            </div>
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>İÇ KONTROLLER</th>
                        <th>DURUM</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach(__('arrays.internal_controls') as $key => $internalControl)
                        <tr>
                            <td><b>{{ $internalControl }}</b></td>
                            <td class="text-center">{!! $expertise['getInternalControls'][$key]['answer'] != 'yok' ? ucfirst($expertise['getInternalControls'][$key]['answer']) : '---' !!}</td>
                            <td>{!! $expertise['getInternalControls'][$key]['note'] !!}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>ALT KONTROLLER</th>
                        <th>DURUM</th>
                        <th>AÇIKLAMA</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($engines2 as $key => $engine2)
                        @if($engine2 != 'Elektrik Sistemi')

                            @if($key == 'fren_sistemi_kontrolu')

                                <tr>
                                    <td><b>{{__('arrays.components')['sanziman_vites_gecisleri_ve_ses_kontrolu']}} </b></td>
                                    <td class="text-center">
                                        @if($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "yok" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Yok")
                                            ----
                                        @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "iyi" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "İyi")
                                            İyi
                                        @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "hafif" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Hafif")
                                            Hafif
                                        @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "test yapılmadı" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Test Yapılmadı")
                                            Test Yapılmadı
                                        @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "kötü" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Kötü")
                                            Kötü
                                        @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "orta" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Orta")
                                            Orta
                                        @endif
                                    </td>
                                    <td>{{$expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['note']}}</td>
                                </tr>
                            @endif
                            <tr>
                                <td><b>{{ $engine2 }}</b></td>
                                <td class="text-center">
                                    {{ ucfirst($expertise['getSubControlsAndEngines'][$key]['answer']) }}
                                </td>
                                <td>{!! $expertise['getSubControlsAndEngines'][$key]['note'] !!}</td>
                            </tr>
                        @else
                            @php
                                $electrical_values[0] = [
                                    'name' => $engine2,
                                    'answer' => $expertise['getSubControlsAndEngines'][$key]['answer'],
                                    'note' => $expertise['getSubControlsAndEngines'][$key]['note'],
                                ];
                            @endphp
                        @endif

                    @endforeach
                    <tr>
                        <td><b>{{__('arrays.components')['dortceker_kontrolu']}}</b></td>
                        <td class="text-center">
                            @if($expertise['getComponents']['dortceker_kontrolu']['answer'] == "yok" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Yok")
                                ----
                            @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "iyi" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "İyi")
                                İyi
                            @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "test yapılmadı" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Test Yapılmadı")
                                Test Yapılmadı
                            @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "kötü" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Kötü")
                                Kötü
                            @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "orta" || $expertise['getComponents']['dortceker_kontrolu']['answer'] == "Orta")
                                Orta
                            @endif
                        </td>
                        <td>{{$expertise['getComponents']['dortceker_kontrolu']['note']}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
            <div class="col-md-6 table-responsive">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th class="text-center">LASTİK VE JANTLAR</th>
                        <th class="text-center">DURUM</th>
                        <th class="text-center">DİŞ(mm)</th>
                        <th class="text-center">BASINÇ</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach(__('arrays.tire_and_rims') as $key => $tire)
                        <tr>
                            <td>{{ $tire }}</td>
                            <td >
                                @if($expertise['getTireAndRims'][$key]['sorunlu_mu'] != 1)
                                    Sorunsuz
                                @else
                                    {{$expertise['getTireAndRims'][$key]['note']}}
                                @endif

                            </td>
                            <td class="text-center">{!! !empty($expertise['getTireAndRims'][$key]['dis']) ? number_format($expertise['getTireAndRims'][$key]['dis'], 1) : ''; !!}</td>
                            <td class="text-center">{!! $expertise['getTireAndRims'][$key]['basinc'] !!}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>

                <table class="table table-bordered mt-3">
                    <thead>
                    <tr>
                        <th>DİAGNOSTİK CİHAZ KONTROL SONUÇLARI</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php
                        $td_count = 4;
                        if(count($expertise['allNotes'][3]) > $td_count){
                            $td_count = count($expertise['allNotes'][3]);
                        }

                        foreach($expertise['allNotes'][3] as $key => $notes_array){
                            $expertise['allNotes'][3][$key] = '[DIAG] '.$notes_array;
                        }
                    @endphp
                    @for ($i = 0; $i < $td_count; $i++)
                        <tr>

                            <td  @if(empty($expertise['allNotes'][3][$i])) style="padding:15px;" @endif>{{!empty($expertise['allNotes'][3][$i]) ? $expertise['allNotes'][3][$i] : ''}}</td>
                        </tr>
                    @endfor
                    </tbody>
                </table>
            </div>
            <div class="col-md-12 table-responsive mt-3">
                <table class="table table-bordered">
                    <thead>
                    <tr>
                        <th colspan="4" class="text-center">NOTLAR</th>
                    </tr>
                    </thead>
                    <tbody>
                    @php
                        $td_count = 10;
                        $td_count_2 = (count($expertise['allNotes'][4]) + count($expertise['allNotes'][5])+ count($expertise['allNotes'][6]) + count($expertise['allNotes'][7]) + count($expertise['allNotes'][2])) / 2;
                        if($td_count_2 > 10){
                            $td_count = $td_count_2;
                        }

                        foreach($expertise['allNotes'][4] as $key => $notes_array){
                            $expertise['allNotes'][4][$key] = '[ICK] '.$notes_array;
                        }
                        foreach($expertise['allNotes'][5] as $key => $notes_array){
                            $expertise['allNotes'][5][$key] = '[LAJ] '.$notes_array;
                        }
                        foreach($expertise['allNotes'][6] as $key => $notes_array){
                            $expertise['allNotes'][6][$key] = '[ALTK] '.$notes_array;
                        }
                        foreach($expertise['allNotes'][7] as $key => $notes_array){
                            $expertise['allNotes'][7][$key] = '[ALTK] '.$notes_array;
                        }
                        foreach($expertise['allNotes'][2] as $key => $notes_array){
                            $expertise['allNotes'][2][$key] = '[FRN] '.$notes_array;
                        }

                        $mergedArray = array_merge(
                            $expertise['allNotes'][2    ],
                            $expertise['allNotes'][4],
                            $expertise['allNotes'][5],
                            $expertise['allNotes'][6],
                            $expertise['allNotes'][7]
                        );

                        // Toplam eleman sayısını bul

                        $firstArray = array_slice($mergedArray, 0, ceil($td_count));
                        $secondArray = array_splice($mergedArray, ceil($td_count));

                    @endphp
                    @for ($i = 0; $i < $td_count; $i++)
                        <tr>
                            <td class="col-6" @if(empty($firstArray[$i])) style="padding:15px;" @endif>{{!empty($firstArray[$i]) ? $firstArray[$i]:''}} </td>
                            <td @if(empty($firstArray[$i])) style="padding:15px;" @endif> {{!empty($secondArray[$i]) ? $secondArray[$i] : ''}} </td>
                        </tr>
                    @endfor

                    </tbody>
                </table>
            </div>
            <div class="col-12 table-responsive">
                <table class="table table-bordered">
                    <tbody>
                    <tr>
                        <td>
                            <p>1* Ekspertiz işlemlerinde triger seti kontrolü yapılmamaktadır.</p>
                            <p>2* Akü ölçümü ekspertiz işlemi anındaki değerlere göre yapılmaktadır. Sonraki kullanımdan kaynaklanan sorunlarda firmamızın herhangi bir garantisi yoktur.</p>
                            <p>3* Kumanda kontrol düğmeleri kontrolü; Far düğmeleri, Sinyal kolu, silecek kolu, elektrikli ayna(ısıtma hariç), cam düğmeleri, bağaj, depo kapağı düğmesi, cam perdeleri, tavan ve sunroof düğmesi kontrollerini kapsamaktadır. Bunların dışında kalan kumanda düğmeleri kontrol edilmemektedir.</p>
                            <p>4* Farlarda yalnızca kırık, çatlak ve yanıp yanmadığının kontrolü yapılmaktadır. Orijinallik, xenon ve su alma testi yapılmamaktadır.</p>
                        </td>
                        <td colspan="3">
                            @php
                                $hafif_open = 2;
                                 foreach(__('arrays.components') as $key => $component){
                                     if($expertise['getComponents'][$key]['answer'] == "hafif" || $expertise['getComponents'][$key]['answer'] == "Hafif"){
                                         if($expertise['getComponents'][$key]['title'] != 'Şanzıman Vites Geçişleri ve Ses Kont.' && $expertise['getComponents'][$key]['title'] != 'Dörtçeker Kontrolü'	){
                                              $hafif_open = 1;
                                         }

                                     }
                                 }

                            @endphp
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th>KOMPONENTLER</th>
                                    <th>İYİ</th>
                                    <th>ORTA</th>
                                    <th>KÖTÜ</th>
                                    <th>Açıklama </th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach(__('arrays.components') as $key => $component)
                                    @if($key != "dortceker_kontrolu" && $key != 'sanziman_vites_gecisleri_ve_ses_kontrolu')
                                        <tr>
                                            <td>{{ $component }}</td>
                                            @if($expertise['getComponents'][$key]['answer'] == 'test_yapılmadı' || $expertise['getComponents'][$key]['answer'] == 'Test Yapılmadı')
                                                <td colspan="5" style="text-align: center"><span class="test-yapilmadi fw-semibold">Test Yapılmadı</span></td>
                                            @elseif($expertise['getComponents'][$key]['answer'] == 'yok' || $expertise['getComponents'][$key]['answer'] == 'Yok')
                                                <td colspan="5" style="text-align: center"><span class="test-yapilmadi fw-semibold">Araçta Bu Özellik Bulunmamaktadır.</span></td>
                                            @else
                                                <td>{!! $expertise['getComponents'][$key]['answer'] == 'iyi' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                                <td>{!! $expertise['getComponents'][$key]['answer'] == 'orta' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                                <td>{!! $expertise['getComponents'][$key]['answer'] == 'kötü' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                                <td>
                                                    {{$expertise['getComponents'][$key]['note']}}
                                                </td>
                                            @endif

                                        </tr>
                                    @endif
                                @endforeach
                                <tr>
                                    <td>{{$electrical_values[0]['name']}} </td>
                                    @if($electrical_values[0]['answer'] == "test-yapilmadi" || $electrical_values[0]['answer'] == "Test Yapılmadı")
                                        <td colspan="5" style="text-align: center"><span class="test-yapilmadi fw-semibold">Test Yapılmadı</span></td>
                                    @elseif($expertise['getComponents'][$key]['answer'] == 'yok' || $expertise['getComponents'][$key]['answer'] == 'Yok')
                                        <td colspan="5" style="text-align: center"><span class="test-yapilmadi fw-semibold">Araçta Bu Özellik Bulunmamaktadır.</span></td>
                                    @endif

                                    <td>{!! $electrical_values[0]['answer'] == 'iyi' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! $electrical_values[0]['answer'] == 'orta' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! $electrical_values[0]['answer'] == 'kötü' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{{$electrical_values[0]['note']}}</td>
                                </tr>
                                </tbody>
                            </table>

                            <div class="d-flex justify-content-between" style="font-size: 1.1rem">
                                <span><span class="text-danger">KBR</span> 1/1 Örnek Mh. Fehmi Tokay Cad. No:18 B ATAŞEHİR İSTANBUL</span>
                                <span>ÜMRANİYE V.D. 0910564940</span>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td style="background-color: #ff1000;color: #fff;text-align: center;font-weight: bolder" colspan="4">SERVİSLERİMİZDEN ALINAN EKSPERTİZ RAPORLARINA www.umranoto.com ADRESİNDEN ULAŞABİLİRSİNİZ.</td>
                    </tr>
                    <tr>
                        <td colspan="4">
                            Umran Oto Ekspertiz Merkezleri tüm dünyadaki ekspertiz hizmetleri gibi aracın o anki durumunu analiz eder ve bilgi verir. Ekspertiz yaptığı aracın kesinlikle ileride arıza ve problem çıkarmayacağını garanti edemez. Aracın o anki görülen hataları kontrol edilerek raporlanır. Kaporta boya değerlendirmesine itiraz 72 saat, motor ile ilgili değerlendirmeye itiraz 24 saattir. Bayilerimizde kilometre orijinalliği tespiti yapılmamaktadır. Elektronik arşiv süresi 5 yıldır.
                        </td>
                    </tr>
                    <tr class="text-center">
                        <td>
                            KAPORTA BOYA TEKNİKERİ
                        </td>
                        <td>
                            SERVİS MÜDÜRÜ
                        </td>
                        <td>
                            MÜŞTERİ (ALICI)<br><br><b>{{ $expertise['kvkk_durum'] == "true" && !empty($expertise['getAlici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : $expertise['getAlici'] }}</b>
                        </td>
                        <td>
                            MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['kvkk_durum'] == "true" && !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : $expertise['getSatici'] }}</b>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>

        </div>
    </div>


    <div class="col-12 d-flex justify-content-center">
        {{--        <button class="btn btn-danger m-3" onclick="window.print()"><i class="fa fa-print"></i> Yazdır</button>--}}
        <button class="btn btn-danger m-3" onclick="ExportPdf()"><i class="fe bi-file-earmark-pdf"></i> Yazdır</button>
        @if(!empty($expertise['is_it_over']) && $expertise['is_it_over'] == true)
            <button class="btn btn-success m-3 ftp_ok" ><i class="fe bi-file-earmark-pdf"></i> FTP'e Gönder</button>
        @endif
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <script src="/assets/js/html2pdf.bundle.js"></script>
    <script>
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });

        $(document).ready(function(){
            @if(!empty($_GET['goprint']) && $_GET['goprint'] == 1)
            $('html, body').animate({scrollTop: $(document).height()}, 'slow');
            @endif
        })
        function ExportPdf() {
            @if(!empty($_GET['goprint']) && $_GET['goprint'] == 1 && !empty($_GET['refuuid']))
            $.ajax({
                url: "{{ route('api.createOldExpertisDowland') }}",
                type: "post",
                data: {
                    _token:'{{csrf_token()}}',
                    ref_uuid:'{{$_GET['refuuid']}}',
                    uuid:'{{$expertise['uuid']}}'
                },
                success: function (response) {
                    if(response.success == "true"){

                        Toast.fire({
                            icon: "success",
                            title: "Bir önceki sayfaya dönüp kaldığınız yerden devam edebilirsiniz.",
                        });
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Bir Hata Oluştu Lütfen Sayfayı Yenileyin!"
                        });
                    }
                },
            });
            @else
            @endif

            window.print()


        }
        $('.ftp_ok').click(function(){
            var thisbtn = $(this)
            $(this).attr('disabled','')
            var data_html = "<style>"+$('style').html()+"</style>"+$('#divRaporEkrani').html();
            var maskes_data_html ="<style>"+$('style').html()+"</style>"+$('#divRaporEkrani_fitreli').html();
            $.ajax({
                url: "{{ route('api.expertiseFtpOk') }}",
                type: "post",
                data: {
                    _token:'{{csrf_token()}}',
                    uuid:'{{$expertise['uuid']}}',
                    html_data:data_html,
                    maskes_data_html:maskes_data_html,
                },
                success: function (response) {
                    if(response.success){
                        thisbtn.remove()
                        Toast.fire({
                            icon: "success",
                            title: "Ekspertiz Ftp'e gönderildi",
                        });
                        window.location = "/expertises";
                    }else{
                        thisbtn.removeAttr('disabled')
                        Toast.fire({
                            icon: "error",
                            title: "Bir Hata Oluştu Sayfayı Lütfen Yenileyin",
                        });
                    }

                },
            });
        })
    </script>

@endpush
