@extends('pages.build')
@section('title',$expertise['plaka'] . '-' . $expertise['belge_no'])
@push('css')
    <style id="page">
        table{
            border: .9px solid rgba(0, 0, 0, 0.59) !important;
        }
        /*.result{*/
        /*    height: 620px;*/
        /*}*/
        .row>*{
            padding: 0;
        }
        th{
            background-color: #cecece57 !important
        }
        .page-break{
            page-break-after: always;
        }
        .table-bordered>:not(caption)>*>*{
            border-width: .5px;
        }
        .table td, .table th{
            padding: .35rem;
        }
        .table-col-6 td{
            width: 16.667% !important;
            border: 1px solid;
        }
        .imza{
            line-height: 2 !important;
        }
        .sonuc-sayfa-title{
            width: 100%;
            background: rgb(238,134,10);
            background: linear-gradient(90deg, rgba(238,134,10,1) 50%, rgba(255,255,255,1) 100%);
        }
        .sonuc-sayfa-title td{
            padding: 1rem;
        }
        .sonuc-sayfa-title td h4{
            margin-bottom: 0;
        }
        .sonuc-sayfa-bg{
            background: rgb(153,153,153);
            background: linear-gradient(0deg, rgba(153,153,153,1) 40%, rgba(255,255,255,1) 100%);
            padding: 2rem;
            border: 1px solid;
        }
        .color-bar {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            justify-content: end;
        }
        .line {
            width: 100px;
            height: 10px;
            margin: 0 10px;
        }
        .green {
            background-color: green;
        }
        .yellow {
            background-color: yellow;
        }
        .red {
            background-color: red;
        }
        .text {
            font-size: 16px;
            font-weight: bold;
        }
        .sonuc-logo{
            flex: 1;
            text-align: center;
            padding: 0.6rem 0.2rem;
            border: 1px solid red;
            border-radius: 1.5rem;
            background: #fff;
        }
        .sonuc-logo img{
            margin-top: 2rem;
        }
        .tutunma-box{
            background-color: #faf7e6;
            padding: 4px 15px;
            font-size: 1rem;
            box-shadow: 5px 6px #99989887;
            border: 1px solid #000;
            width: 4rem;
        }
        .tutunma-item{
            position: relative;
            text-align: center;
        }
        .tutunma-item p{
            margin-bottom: 0;
        }
        .sonuc-header{
            display: flex;
        }
        .sonuc-logo{
            flex: 1;
        }
        .sonuc-first-tab,.sonuc-third-tab{
            display: flex;
            justify-content: space-between;
            margin: 0.3rem 1rem;
        }
        .sonuc-info{
            flex: 7;
        }
        .sonuc-second-tab{
            background: rgb(238,134,10);
            background: linear-gradient(90deg, rgba(238,134,10,1) 50%, rgba(255,255,255,1) 100%);
        }
        .sonuc-second-tab span{
            padding: 1rem;
            font-size: 1.32rem;
        }
        .sonuc-third-tab .sonuc-first-tab-item{
            flex: 1;
        }
        .rapor-item-tr td:nth-child(1),.rapor-item-tr td:nth-child(3){
            width:48%;
            text-wrap:nowrap;
        }
        .rapor-item-tr td:nth-child(2){
            width:4% !important;
            text-wrap:nowrap;
        }
        .table td, .table th{
            padding: 0.25rem;
            font-size: .513rem;
            line-height: 1 !important;
        }
        .table thead tr th{
            font-size:.75rem;
        }
        .rapor{
            padding-top: 1rem;
        }
        .is-emri-sayfa .table td,.is-emri-sayfa .table th{
            padding: 0.35rem;
            font-size: .613rem;
            line-height: .9 !important;
        }
        #divRaporEkrani .rapor{
            margin: .4cm .5cm;
        }
        .imza{
            padding:1.6rem !important;
            line-height: 3 !important;
        }
        p{
            margin-bottom:.5rem;
        }
        .not-item.not-var{
            line-height: 1.3 !important;
            padding: .25rem
        }
        .not-item.not-yok{
            padding: .55rem;
            line-height: 3 !important;
        }
    </style>
    <style id="print">
        @media print{
            @if(!env('APP_LOCAL'))
                #divOnGosterim{
                display: none;
            }
            @else
                #divOnGosterim .rapor{
                margin: .4cm .5cm;
            }
            @endif
            .page-break{
                page-break-after: always;
            }
            .breadcrumb-header,.print-buttons{
                display: none !important;
            }
            .main-content{
                padding: 0;
            }
            .app-content{
                margin-block-start:unset;
                margin-inline-start:unset;
            }
            body{
                visibility: hidden;
                -webkit-print-color-adjust: exact;
            }

            .main-container div{
                visibility: visible;
            }

            .main-container button{
                visibility: hidden !important;
            }
            .print_bg{
                background-color:  #ff1000;
            }
            @page paysage{
                size: landscape;
            }
            .yatay{
                page: paysage;
            }
            .sonuc-sayfa-bg:nth-last-child{
                page-break-after: unset;
            }

        }
    </style>
@endpush
@section('content')
    <div id="divOnGosterim">
        <div class="row is-emri is-emri-sayfa rapor">
            <table class="table table-bordered" style="height: 950px">
                <tbody>
                <tr>
                    <td style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 100%">
                        <h5 style="color: darkorange">444 5 417</h5>
                    </td>
                    <td style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b> @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11')<br><br> <b>İŞ EMRİ</b> @endif
                    </td>
                    <td colspan="2" style="width: 33%">
                        <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                        <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                    </td>
                </tr>
                <tr>
                    <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                </tr>
                <tr>
                    <td>Plaka</td>
                    <td class="filtresiz"><b>
                            {{ $expertise['plaka'] }}
                        </b>
                    </td>
                    <td class="filtreli"><b>
                            {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                        </b>
                    </td>
                    <td>Şase No</td>
                    <td class="filtresiz"><b>
                            {{ $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td class="filtreli"><b>
                            {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                        </b>
                    </td>
                </tr>
                <tr>
                    <td>Marka Model</td>
                    <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                    <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                </tr>
                <tr>
                    <td>Model Yılı</td>
                    <td>{{ $expertise['model_yili'] }}</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2"><b>Alıcı Bilgileri</b></td>
                    <td colspan="2"><b>Satıcı Bilgileri</b></td>
                </tr>
                 <tr>
                    <td>Adı Soyadı</td>
                    <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                    <td>Adı Soyadı</td>
                    <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                </tr>
                <tr>
                    <td>Firma Unvanı</td>
                    <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                    <td>Firma Unvanı</td>
                    <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                </tr>
                <tr>
                    <td>Adres</td>
                    <td>{{ $expertise['fullAddressAlici'] }}</td>
                    <td>Adres</td>
                    <td>{{ $expertise['fullAddressSatici'] }}</td>
                </tr>
                <tr>
                    <td>Telefon</td>
                    <td>{{ $expertise['getAliciTel'] }}</td>
                    <td>Telefon</td>
                    <td>{{ $expertise['getSaticiTel'] }}</td>
                </tr>
                <tr>
                    <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                    <td colspan="2">
                        @foreach($expertise['getStocks'] as $expertiseStock)
                            {{ $expertiseStock['ad'] }},
                        @endforeach</td>
                </tr>

                <tr class="result">
                    <td colspan="4" id="hasarSonucu">
                        @if($logs)
                            @php
                                $hasar = json_decode($logs->hasar);
                            @endphp
                            @if($hasar != null)
                                <a href="{{ $hasar->data->imageurl ?? '' }}" target="_blank"><p class="text-center">{{ $hasar->data->imageurl ?? '' }}</p></a>
                                <p class="text-center">{{ $hasar->data->result ?? '' }}</p>
                                <img src="{{ $hasar->data->imageurl ?? '' }}" height="400px">
                            @endif
                        @endif
                    </td>
                </tr>
                <tr><td colspan="4"><p>{{ $settings->hasar_sorgu_mesaji }}</p></td></tr>
                <tr class="text-center">
                    <td class="imza" style="width: 33%">
                        Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                        <b>{{!empty($authUser->getBranch->unvan) ? $authUser->getBranch->unvan : ''}}</b>

                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                    </td>
                    <td class="filtreli imza">
                        MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                    </td>
                    <td class="imza filtreli">
                        MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="page-break"></div>
        <div class="row is-emri is-emri-sayfa rapor">
            <table class="table table-bordered" style="height: 950px">
                <tbody>
                <tr>
                    <td style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 100%">
                        <h5 style="color: darkorange">444 5 417</h5>
                    </td>
                    <td style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                    </td>
                    <td colspan="2" style="width: 33%">
                        <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                        <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                    </td>
                </tr>
                <tr>
                    <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                </tr>
                 <tr>
                    <td>Plaka</td>
                    <td class="filtresiz" colspan="1"><b>
                            {{ $expertise['plaka'] }}
                        </b>
                    </td>
                    <td class="filtreli" colspan="1"><b>
                            {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                        </b>
                    </td>
                    <td>Şase No</td>
                    <td class="filtresiz" colspan="1"><b>
                            {{ $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td class="filtreli" colspan="1"><b>
                            {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                        </b>
                    </td>
                </tr>
                <tr>
                    <td>Marka Model</td>
                    <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                    <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                </tr>
                <tr>
                    <td>Model Yılı</td>
                    <td>{{ $expertise['model_yili'] }}</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2"><b>Alıcı Bilgileri</b></td>
                    <td colspan="2"><b>Satıcı Bilgileri</b></td>
                </tr>
                <tr>
                    <td>Adı Soyadı</td>
                    <td class="filtresiz">@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                    <td class="filtreli">@if($expertise['getAliciType'] == 'bireysel') {{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }} @endif</td>
                    <td>Adı Soyadı</td>
                    <td class="filtresiz">@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                    <td class="filtreli">@if($expertise['getSaticiType'] == 'bireysel') {{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }} @endif</td>
                </tr>
                <tr>
                    <td>Firma Unvanı</td>
                    <td class="filtresiz">@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                    <td class="filtreli">@if($expertise['getAliciType'] == 'kurumsal') {{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }} @endif</td>
                    <td>Firma Unvanı</td>
                    <td class="filtresiz">@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                    <td class="filtreli">@if($expertise['getSaticiType'] == 'kurumsal') {{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }} @endif</td>
                </tr>
                <tr>
                    <td>Adres</td>
                    <td class="filtresiz">{{ $expertise['fullAddressAlici'] }}</td>
                    <td class="filtreli">{{ !empty($expertise['fullAddressAlici']) ? substr($expertise['fullAddressAlici'], 0, 2) . str_repeat('*', (strlen($expertise['fullAddressAlici']) - 3)) . substr($expertise['fullAddressAlici'], -1) : '' }}</td>
                    <td>Adres</td>
                    <td class="filtresiz">{{ $expertise['fullAddressSatici'] }}</td>
                    <td class="filtreli">{{ !empty($expertise['fullAddressSatici']) ? substr($expertise['fullAddressSatici'], 0, 2) . str_repeat('*', (strlen($expertise['fullAddressSatici']) - 3)) . substr($expertise['fullAddressSatici'], -1) : '' }}</td>
                </tr>
                <tr>
                    <td>Telefon</td>
                    <td class="filtresiz">{{ $expertise['getAliciTel'] }}</td>
                    <td class="filtreli">{{ !empty($expertise['getAliciTel']) && strlen($expertise['getAliciTel']) > 3 ? substr($expertise['getAliciTel'], 0, 2) . str_repeat('*', (strlen($expertise['getAliciTel']) - 3)) . substr($expertise['getAliciTel'], -1) : '' }}</td>
                    <td>Telefon</td>
                    <td class="filtresiz">{{ $expertise['getSaticiTel'] }}</td>
                    <td class="filtreli">{{ !empty($expertise['getSaticiTel']) ? substr($expertise['getSaticiTel'], 0, 2) . str_repeat('*', (strlen($expertise['getSaticiTel']) - 3)) . substr($expertise['getSaticiTel'], -1) : '' }}</td>
                </tr>
                <tr>
                    <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                    <td colspan="2">
                        @foreach($expertise['getStocks'] as $expertiseStock)
                            {{ $expertiseStock['ad'] }},
                        @endforeach</td>
                </tr>
                <tr class="result">
                    <td colspan="4" style="padding: 2rem 0 9rem 0;">
                        @if($logs)
                            @php
                                $kilometre = json_decode($logs->kilometre);
                            @endphp
                            @if($kilometre != null)
                                @if($kilometre->data->imageurl)
                                    <a href="{{ $kilometre->data->imageurl ?? '' }}" target="_blank"><p class="text-center">{{ $kilometre->data->imageurl }}</p></a>
                                    <img src="{{ $kilometre->data->imageurl ?? '' }}" width="100%">
                                @elseif($kilometre->data->kilometerQueryDetails)
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                            <tr>
                                                <th>KM Tarih</th>
                                                <th>KM</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            @foreach($kilometre->data->kilometerQueryDetails as $kmDetail)
                                                <tr>
                                                    <td>{{ \Carbon\Carbon::parse($kmDetail->kmTarih)->translatedFormat('d F Y') }}</td>
                                                    <td>{{ $kmDetail->km }}</td>
                                                </tr>
                                            @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                @endif
                            @endif
                        @endif
                    </td>
                </tr>
                <tr><td colspan="4"><p>{{ $settings->kilometre_sorgu_mesaji }}</p></td></tr>
                <tr class="text-center">
                    <td class="imza" style="width: 33%">
                        Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                        <b>{{!empty($authUser->getBranch->unvan) ? $authUser->getBranch->unvan : ''}}</b>
                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                    </td>
                    <td class="filtreli imza">
                        MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                    </td>
                    <td class="imza filtreli">
                        MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="page-break"></div>
        <div class="row is-emri is-emri-sayfa rapor">
            <table class="table table-bordered" style="height: 950px">
                <tbody>
                <tr>
                    <td style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 100%">
                        <h5 style="color: darkorange">444 5 417</h5>
                    </td>
                    <td style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                    </td>
                    <td colspan="2" style="width: 33%">
                        <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                        <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                    </td>
                </tr>
                <tr>
                    <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                </tr>
                 <tr>
                    <td>Plaka</td>
                    <td class="filtresiz" colspan="1"><b>
                            {{ $expertise['plaka'] }}
                        </b>
                    </td>
                    <td class="filtreli" colspan="1"><b>
                            {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                        </b>
                    </td>
                    <td>Şase No</td>
                    <td class="filtresiz" colspan="1"><b>
                            {{ $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td class="filtreli" colspan="1"><b>
                            {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                        </b>
                    </td>
                </tr>
                <tr>
                    <td>Marka Model</td>
                    <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                    <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                </tr>
                <tr>
                    <td>Model Yılı</td>
                    <td>{{ $expertise['model_yili'] }}</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2"><b>Alıcı Bilgileri</b></td>
                    <td colspan="2"><b>Satıcı Bilgileri</b></td>
                </tr>
                 <tr>
                    <td>Adı Soyadı</td>
                    <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                    <td>Adı Soyadı</td>
                    <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                </tr>
                <tr>
                    <td>Firma Unvanı</td>
                    <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                    <td>Firma Unvanı</td>
                    <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                </tr>
                <tr>
                    <td>Adres</td>
                    <td>{{ $expertise['fullAddressAlici'] }}</td>
                    <td>Adres</td>
                    <td>{{ $expertise['fullAddressSatici'] }}</td>
                </tr>
                <tr>
                    <td>Telefon</td>
                    <td>{{ $expertise['getAliciTel'] }}</td>
                    <td>Telefon</td>
                    <td>{{ $expertise['getSaticiTel'] }}</td>
                </tr>
                <tr>
                    <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                    <td colspan="2">
                        @foreach($expertise['getStocks'] as $expertiseStock)
                            {{ $expertiseStock['ad'] }},
                        @endforeach</td>
                </tr>
                <tr class="result">
                    <td colspan="4" @if($logs && json_decode($logs->borc) != null) style="padding: 2rem 0 14rem 0;" @else style="padding: 18rem 0 18rem 0;" @endif>
                        @if($logs)
                            @php
                                $borc = json_decode($logs->borc);
                            @endphp
                            @if($borc != null)
                                <table class="table">
                                    <tbody>
                                        <tr>
                                            <th>Tip</th>
                                            <th>Vade</th>
                                            <th>Borç</th>
                                            <th>G. Faizi</th>
                                            <th>Toplam</th>
                                        </tr>
                                        @foreach($borc->data->vehicletax->data as $brc)
                                            <tr>
                                                <td>{{ $brc->expirydate }}</td>
                                                <td>{{ $brc->installment }}</td>
                                                <td>{{ $brc->loanamount }}₺</td>
                                                <td>{{ $brc->delayamount }}₺</td>
                                                <td>{{ $brc->totalamount }}₺</td>
                                            </tr>
                                        @endforeach
                                        <tr>
                                            <td>Avrupa Otoyolu Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->avrupa ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->avrupa ?? '' }}₺</td>
                                        </tr>
                                        <tr>
                                            <td>HGS / OGS Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->hgsogs ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->hgsogs ?? '' }}₺</td>
                                        </tr>
                                        <tr>
                                            <td>Anadolu Otoyolu Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->anadolu ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->anadolu ?? '' }}₺</td>
                                        </tr>
                                        <tr>
                                            <td>Avrasya Tüneli Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->avrasya ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->avrasya ?? '' }}₺</td>
                                        </tr>
                                        <tr>
                                            <td>Kuzey Çevre Otoyolu + Yavuz Sultan Selim Köprü Geçiş Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->yssKuzeyCevre ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->yssKuzeyCevre ?? '' }}₺</td>
                                        </tr>
                                        <tr>
                                            <td>Gebze-İzmir Otoyolu + Osmangazi Köprü Geçiş Cezaları</td>
                                            <td>1</td>
                                            <td>{{ $borc->data->roadfines->data[0]->gebzeOrhangaziIzmir ?? '' }}₺</td>
                                            <td>0 ₺</td>
                                            <td>{{ $borc->data->roadfines->data[0]->gebzeOrhangaziIzmir ?? '' }}₺</td>
                                        </tr>
                                    </tbody>
                                </table>
                            @endif
                        @endif
                    </td>
                </tr>
                <tr><td colspan="4"><p>{{ $settings->borc_sorgu_mesaji }}</p></td></tr>
                <tr class="text-center">
                    <td class="imza" style="width: 33%">
                        Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                        <b>{{!empty($authUser->getBranch->unvan) ? $authUser->getBranch->unvan : ''}}</b>

                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                    </td>
                    <td class="filtreli imza">
                        MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                    </td>
                    <td class="imza filtreli">
                        MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="page-break"></div>
        <div class="row is-emri is-emri-sayfa rapor">
            <table class="table table-bordered" style="height: 950px">
                <tbody>
                <tr>
                    <td style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 100%">
                        <h5 style="color: darkorange">444 5 417</h5>
                    </td>
                    <td style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                    </td>
                    <td colspan="2" style="width: 33%">
                        <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                        <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                    </td>
                </tr>
                <tr>
                    <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                </tr>
                 <tr>
                    <td>Plaka</td>
                    <td class="filtresiz" colspan="1"><b>
                            {{ $expertise['plaka'] }}
                        </b>
                    </td>
                    <td class="filtreli" colspan="1"><b>
                            {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                        </b>
                    </td>
                    <td>Şase No</td>
                    <td class="filtresiz" colspan="1"><b>
                            {{ $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td class="filtreli" colspan="1"><b>
                            {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                        </b>
                    </td>
                </tr>
                <tr>
                    <td>Marka Model</td>
                    <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                    <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                </tr>
                <tr>
                    <td>Model Yılı</td>
                    <td>{{ $expertise['model_yili'] }}</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2"><b>Alıcı Bilgileri</b></td>
                    <td colspan="2"><b>Satıcı Bilgileri</b></td>
                </tr>
                 <tr>
                    <td>Adı Soyadı</td>
                    <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                    <td>Adı Soyadı</td>
                    <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                </tr>
                <tr>
                    <td>Firma Unvanı</td>
                    <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                    <td>Firma Unvanı</td>
                    <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                </tr>
                <tr>
                    <td>Adres</td>
                    <td>{{ $expertise['fullAddressAlici'] }}</td>
                    <td>Adres</td>
                    <td>{{ $expertise['fullAddressSatici'] }}</td>
                </tr>
                <tr>
                    <td>Telefon</td>
                    <td>{{ $expertise['getAliciTel'] }}</td>
                    <td>Telefon</td>
                    <td>{{ $expertise['getSaticiTel'] }}</td>
                </tr>
                <tr>
                    <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                    <td colspan="2">
                        @foreach($expertise['getStocks'] as $expertiseStock)
                            {{ $expertiseStock['ad'] }},
                        @endforeach</td>
                </tr>
                <tr class="result">
                    <td colspan="4" style="padding: 2rem 0 6rem 0;">
                        @if($logs)
                            @php
                                $ruhsat = json_decode($logs->ruhsat);
                            @endphp
                            <table class="table">
                                <tr><th>Seri Numarası</th><td>{{ $ruhsat->data->serialnumber ?? '' }}</td></tr>
                                <tr><th>Plaka</th><td>{{ $ruhsat->data->plate ?? '' }}</td></tr>
                                <tr><th>Şasi</th><td>{{ $ruhsat->data->chassis ?? '' }}</td></tr>
                                <tr><th>Motor Numarası</th><td>{{ $ruhsat->data->enginenumber ?? '' }}</td></tr>
                                <tr><th>Model Yılı</th><td>{{ $ruhsat->data->modelyear ?? '' }}</td></tr>
                                <tr><th>Marka</th><td>{{ $ruhsat->data->brand ?? '' }}</td></tr>
                                <tr><th>Model</th><td>{{ $ruhsat->data->modelname ?? '' }}</td></tr>
                                <tr><th>Yakıt Tipi</th><td>{{ $ruhsat->data->fueltype ?? '' }}</td></tr>
                                <tr><th>Kayıt Tarihi</th><td>{{ $ruhsat->data->registerdate ?? '' }}</td></tr>
                                <tr><th>Kayıt Başlangıç Tarihi</th><td>{{ $ruhsat->data->registerdateFrom ?? '' }}</td></tr>
                                <tr><th>Yolcu Sayısı</th><td>{{ $ruhsat->data->passengercount ?? '' }}</td></tr>
                                <tr><th>Kimlik Numarası</th><td>{{ $ruhsat->data->idnumber ?? '' }}</td></tr>
                                <tr><th>Adı Soyadı </th><td>{{ $ruhsat->data->surname ?? '' }}</td></tr>
                                <tr><th>Renk </th><td>{{ $ruhsat->data->color ?? '' }}</td></tr>
                                <tr><th>Motor Kapasitesi </th><td>{{ $ruhsat->data->enginecapacity ?? '' }}</td></tr>
                                <tr><th>Motor Gücü </th><td>{{ $ruhsat->data->enginepower ?? '' }}</td></tr>
                                <tr><th>Sakınca Durumu </th><td>{{ $ruhsat->data->sakincadurumu ?? '' }}</td></tr>
                                <tr><th>Kasko Değeri </th><td>{{ $ruhsat->data->kaskodegeri ?? '' }}</td></tr>
                            </table>
                        @endif
                    </td>
                </tr>
                <tr><td colspan="4"><p>{{ $settings->arac_detay_sorgu_mesaji }}</p></td></tr>
                <tr class="text-center">
                    <td class="imza" style="width: 33%">
                        Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                        <b>{{!empty($authUser->getBranch->unvan) ? $authUser->getBranch->unvan : ''}}</b>

                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                    </td>
                    <td class="filtreli imza">
                        MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                    </td>
                    <td class="imza filtreli">
                        MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="page-break"></div>
        <div class="row is-emri is-emri-sayfa rapor">
            <table class="table table-bordered" style="height: 950px">
                <tbody>
                <tr>
                    <td style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 100%">
                        <h5 style="color: darkorange">444 5 417</h5>
                    </td>
                    <td style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                    </td>
                    <td colspan="2" style="width: 33%">
                        <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                        <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                    </td>
                </tr>
                <tr>
                    <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                </tr>
                 <tr>
                    <td>Plaka</td>
                    <td class="filtresiz" colspan="1"><b>
                            {{ $expertise['plaka'] }}
                        </b>
                    </td>
                    <td class="filtreli" colspan="1"><b>
                            {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                        </b>
                    </td>
                    <td>Şase No</td>
                    <td class="filtresiz" colspan="1"><b>
                            {{ $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td class="filtreli" colspan="1"><b>
                            {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                        </b>
                    </td>
                </tr>
                <tr>
                    <td>Marka Model</td>
                    <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                    <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                </tr>
                <tr>
                    <td>Model Yılı</td>
                    <td>{{ $expertise['model_yili'] }}</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2"><b>Alıcı Bilgileri</b></td>
                    <td colspan="2"><b>Satıcı Bilgileri</b></td>
                </tr>
                 <tr>
                    <td>Adı Soyadı</td>
                    <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                    <td>Adı Soyadı</td>
                    <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                </tr>
                <tr>
                    <td>Firma Unvanı</td>
                    <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                    <td>Firma Unvanı</td>
                    <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                </tr>
                <tr>
                    <td>Adres</td>
                    <td>{{ $expertise['fullAddressAlici'] }}</td>
                    <td>Adres</td>
                    <td>{{ $expertise['fullAddressSatici'] }}</td>
                </tr>
                <tr>
                    <td>Telefon</td>
                    <td>{{ $expertise['getAliciTel'] }}</td>
                    <td>Telefon</td>
                    <td>{{ $expertise['getSaticiTel'] }}</td>
                </tr>
                <tr>
                    <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                    <td colspan="2">
                        @foreach($expertise['getStocks'] as $expertiseStock)
                            {{ $expertiseStock['ad'] }},
                        @endforeach</td>
                </tr>
                <tr class="result">
                    <td colspan="4" @if($logs && json_decode($logs->degisen) != null) style="padding: 2rem 0 14rem 0;" @else style="padding: 9rem 0 9rem 0;" @endif>
                        @if($logs)
                            @php
                                $degisenler = json_decode($logs->degisen);
                            @endphp
                            @if(is_array($degisenler))
                                @foreach($degisenler as $detail)
                                    <img src="{{ $detail?->data?->imageurl }}" width="100%">
                                @endforeach
                            @else
                                <img src="{{ $degisenler?->data?->imageurl }}" width="100%">
                            @endif
                        @endif
                    </td>
                </tr>
                <tr><td colspan="4"><p>{{ $settings->degisen_sorgu_mesaji }}</p></td></tr>
                <tr class="text-center">
                    <td class="imza" style="width: 33%">
                        Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                        <b>{{!empty($expertise['getBranch']) ? $expertise['getBranch'] : ''}}</b>

                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                    </td>
                    <td class="filtreli imza">
                        MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                    </td>
                    <td class="imza filtreli">
                        MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <div class="page-break"></div>
        <div class="row is-emri is-emri-sayfa rapor">
            <table class="table table-bordered" style="height: 950px">
                <tbody>
                <tr>
                    <td style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 100%">
                        <h5 style="color: darkorange">444 5 417</h5>
                    </td>
                    <td style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>
                    </td>
                    <td colspan="2" style="width: 33%">
                        <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                        <b>Tarih:</b><h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                    </td>
                </tr>
                <tr>
                    <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
                </tr>
                 <tr>
                    <td>Plaka</td>
                    <td class="filtresiz" colspan="1"><b>
                            {{ $expertise['plaka'] }}
                        </b>
                    </td>
                    <td class="filtreli" colspan="1"><b>
                            {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . str_repeat('*', (strlen($expertise['plaka']) - 3)) . substr($expertise['plaka'], -1) : '' }}
                        </b>
                    </td>
                    <td>Şase No</td>
                    <td class="filtresiz" colspan="1"><b>
                            {{ $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td class="filtreli" colspan="1"><b>
                            {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . str_repeat('*', (strlen($expertise['sase_no']) - 3)) . substr($expertise['sase_no'], -1) : '' }}
                        </b>
                    </td>
                </tr>
                <tr>
                    <td>Marka Model</td>
                    <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
                    <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
                </tr>
                <tr>
                    <td>Model Yılı</td>
                    <td>{{ $expertise['model_yili'] }}</td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="2"><b>Alıcı Bilgileri</b></td>
                    <td colspan="2"><b>Satıcı Bilgileri</b></td>
                </tr>
                 <tr>
                    <td>Adı Soyadı</td>
                    <td>@if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif</td>
                    <td>Adı Soyadı</td>
                    <td>@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
                </tr>
                <tr>
                    <td>Firma Unvanı</td>
                    <td>@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
                    <td>Firma Unvanı</td>
                    <td>@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
                </tr>
                <tr>
                    <td>Adres</td>
                    <td>{{ $expertise['fullAddressAlici'] }}</td>
                    <td>Adres</td>
                    <td>{{ $expertise['fullAddressSatici'] }}</td>
                </tr>
                <tr>
                    <td>Telefon</td>
                    <td>{{ $expertise['getAliciTel'] }}</td>
                    <td>Telefon</td>
                    <td>{{ $expertise['getSaticiTel'] }}</td>
                </tr>
                <tr>
                    <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
                    <td colspan="2">
                        @foreach($expertise['getStocks'] as $expertiseStock)
                            {{ $expertiseStock['ad'] }},
                        @endforeach</td>
                </tr>
                <tr class="result">
                    <td colspan="4"  @if($logs && json_decode($logs->degisen) != null) style="padding: 2rem 0 14rem 0;" @else style="padding: 10rem 0 10rem 0;" @endif>
                        @if($logs)
                            @php
                                $degisenler = json_decode($logs->degisen);
                            @endphp
                            @if(is_array($degisenler))
                                @foreach($degisenler as $detail)
                                    <img src="{{ $detail?->data?->imageurl }}" width="100%">
                                @endforeach
                            @else
                                <img src="{{ $degisenler?->data?->imageurl }}" width="100%">
                            @endif
                        @endif
                    </td>
                </tr>
                <tr><td colspan="4"><p>{{ $settings->ruhsat_sorgu_mesaji }}</p></td></tr>
                <tr class="text-center">
                    <td class="imza" style="width: 33%">
                        Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi<br><br>
                        <b>{{!empty($authUser->getBranch->unvan) ? $authUser->getBranch->unvan : ''}}</b>

                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (ALICI) <br><br><b>{{ $expertise['getAlici'] }}</b>
                    </td>
                    <td class="filtreli imza">
                        MÜŞTERİ (ALICI) <br><br><b>{{ !empty($expertise['getAlici']) ? substr($expertise['getAlici'], 0, 2) . str_repeat('*', (strlen($expertise['getAlici']) - 3)) . substr($expertise['getAlici'], -1) : '' }}</b>
                    </td>
                    <td class="imza filtresiz">
                        MÜŞTERİ (SATICI) <br><br><b>{{ $expertise['getSatici'] }}</b>
                    </td>
                    <td class="imza filtreli">
                        MÜŞTERİ (SATICI) <br><br><b>{{ !empty($expertise['getSatici']) ? substr($expertise['getSatici'], 0, 2) . str_repeat('*', (strlen($expertise['getSatici']) - 3)) . substr($expertise['getSatici'], -1) : '' }}</b>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div id="divRaporEkrani"></div>
    @if(!isset($_GET['process']))
        <div class="col-12 d-flex justify-content-center print-buttons">
            {{--        <button class="btn btn-danger m-3" onclick="window.print()"><i class="fa fa-print"></i> Yazdır</button>--}}
            @if($authUser->type == 'admin' || $expertise['employee_downloaded'] == 0)
                @if($expertise['kvkk_durum'] == "false" || $authUser->type == 'admin')
                    <a href="{{ url()->current() }}?process=print&type=filtresiz" target="_blank" class="btn btn-danger m-3"><i class="fe bi-file-earmark-pdf"></i>Filtresiz Yazdır</a>
                @endif
                <a href="{{ url()->current() }}?process=print&type=filtreli" class="btn btn-danger m-3" target="_blank"><i class="fe bi-file-earmark-pdf"></i>Filtreli Yazdır </a>
            @endif
        </div>
    @endif

@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <script src="/assets/js/html2pdf.bundle.js"></script>
    <script>
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });

        $(document).ready(function(){
            @if(!empty($_GET['goprint']) && $_GET['goprint'] == 1)
            $('html, body').animate({scrollTop: $(document).height()}, 'slow');
            @endif
            @if($authUser->type == 'admin')
            $('.filtreli').addClass('d-none') // Filtreli Gizle
            @else
            $('.filtresiz').addClass('d-none') // Filtresiz Gizle
            @endif

            setTimeout(function (){
                @if(isset($_GET['process']))
                @if($_GET['type'] == 'filtreli')
                ExportPdf('filtreli')
                @else
                ExportPdf('filtresiz')
                @endif
                @endif
            },1000)
        })
        function ExportPdf(type = "filtresiz",page = "rapor") {
            if(type == "filtreli"){
                $('.filtreli').removeClass('d-none')
                $('.filtresiz').addClass('d-none')
            }else{
                $('.filtreli').addClass('d-none')
                $('.filtresiz').removeClass('d-none')
            }

            $('.is-emri-sayfa').clone().removeClass('is-emri-sayfa').appendTo("#divRaporEkrani");
            window.print()
            $("#divRaporEkrani").html('')
        }
    </script>

@endpush
