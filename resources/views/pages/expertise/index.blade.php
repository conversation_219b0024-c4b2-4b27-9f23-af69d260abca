@extends('pages.build')
@section('title','Ekspertiz')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        table#responsiveDataTable tr td:nth-child(3) {
            padding: 0px;
            text-align: center;
        }
        .status_span{
            float: left;
            width: 100%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
        }
        .dataTables_scrollBody {
            min-height: 300px;
        }
    </style>
@endpush
@section('excel')
    @if(in_array('*', $userRoles) || in_array('download_excel_expertise', $userRoles))
        <a href="/excel/ekspertiz?search={{ $_GET['search'] ?? '' }}&start_date={{ $_GET['start_date'] ?? '' }}&end_date={{ $_GET['end_date'] ?? '' }}&belge_no={{ $_GET['belge_no'] ?? '' }}&uuid={{ $_GET['uuid'] ?? '' }}&payment_type={{ $_GET['payment_type'] ?? '' }}&&branch_id={{ $_GET['branch_id'] ?? '' }}&&type={{ $_GET['type'] ?? '' }}&expertise_campaing={{ $_GET['expertise_campaing'] ?? '' }}&customer_id={{ $_GET['customer_id'] ?? '' }}" class="btn btn-sm btn-success">Excel Olarak İndir <i class="fa fa-file-excel"></i> </a>
    @endif
    @if(in_array('*', $userRoles) || in_array('upload_excel_expertise', $userRoles))
        <a href="{{ route('importExcel', ['type' => 'expertise']) }}" class="btn btn-sm btn-danger">Excelden Aktar <i class="fa fa-file-excel"></i></a>
    @endif
@endsection
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form id="getSavedFilter" method="get" action="{{ route('expertises.index') }}"></form>
                <form method="get" action="{{ route('expertises.index') }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>
                                        <small>
                                            <button class="btn btn-sm btn-success mt-1" name="type" value="customer">Cari Ara</button>
                                            <button class="btn btn-sm btn-success mt-1" name="type" value="plaka">Plaka Ara</button>
                                            <button class="btn btn-sm btn-success mt-1" name="type" value="sase">Şase Ara</button>
                                        </small>
                                    </label>
                                    <input type="text" class="form-control form-control-sm" name="search" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->search :  ($_GET['search'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label><small>Cari</small></label>
                                    <select name="customer_id" class="form-control select2 customer-search">
                                        @if(!empty($customer)))
                                            <option value="{{ $customer->id }}" selected>{{ $customer->unvan }}</option>
                                        @endif
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2 mt-1">
                                <div class="form-group">
                                    <label><small>Belge Tarihi Başlangıç</small></label>
                                    <input type="date" class="form-control form-control-sm" name="start_date" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->belge_tarihi_baslangic : $filters['startDate'] }}">
                                </div>
                            </div>
                            <div class="col-md-2 mt-1">
                                <div class="form-group">
                                    <label><small>Belge Tarihi Bitiş</small></label>
                                    <input type="date" class="form-control form-control-sm" name="end_date" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->belge_tarihi_bitis : $filters['endDate'] }}">
                                </div>
                            </div>
                            <div class="col-md-2 mt-1">
                                <div class="form-group">
                                    <label><small>Sözleşme No</small></label>
                                    <input type="text" class="form-control form-control-sm" name="contract_no" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->contract_no :  ($_GET['contract_no'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2 mt-1">
                                <div class="form-group">
                                    <label><small>Belge No</small></label>
                                    <input type="text" class="form-control form-control-sm" name="belge_no" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->belge_no :  ($_GET['belge_no'] ?? '') }}">
                                </div>
                            </div>
                            @if(in_array($authUser->user_role_group_id,[30,39]))
                                <div class="col-md-2 mt-1">
                                    <div class="form-group">
                                        <label><small>ID-UQ</small></label>
                                        <input type="text" class="form-control form-control-sm" name="uuid" value="{{ $_GET['uuid'] ?? '' }}">
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label><small>Ödeme Türü</small></label>
                                    <select class="form-control form-control-sm" name="payment_type">
                                        <option value="all">Tümü</option>
                                        <option @if(isset($_GET['payment_type']) && $_GET['payment_type'] == "kredi_karti") selected="" @endif value="kredi_karti">Kredi Kartı</option>
                                        <option @if(isset($_GET['payment_type']) && $_GET['payment_type'] == "nakit") selected="" @endif value="nakit">Nakit</option>
                                        <option @if(isset($_GET['payment_type']) && $_GET['payment_type'] == "banka") selected="" @endif value="banka">Banka</option>
                                        <option @if(isset($_GET['payment_type']) && $_GET['payment_type'] == "acik_hesap") selected="" @endif value="acik_hesap">Açık Hesap</option>
                                        <option @if(isset($_GET['payment_type']) && $_GET['payment_type'] == "plus_kart") selected="" @endif value="plus_kart">Plus Card</option>
                                        <option @if(isset($_GET['payment_type']) && $_GET['payment_type'] == "sozlesme") selected="" @endif value="sozlesme">Kurumsal Satış</option>
                                    </select>
                                </div>
                            </div>
                            @if(count($authUser->getBranchIds()) > 1)
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label><small>Bayi</small></label>
                                        <select class="form-control select2" name="branch_id">
                                            @if($authUser->type == 'admin')
                                                <option value="all">Hepsi</option>
                                                @foreach(\App\Models\Branch::whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                                    <option @if((isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id)) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                                @endforeach
                                            @else
                                                @foreach(\App\Models\Branch::where('status',1)->whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                                    <option @if((isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id)) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                                @endforeach
                                            @endif

                                        </select>
                                    </div>
                                </div>
                            @endif
                            @if($authUser->type == 'admin' || $authUser->zone_id > 0)
                                <div class="col-md-3">
                                    <label class="form-label">Bölge</label>
                                    <select name="zone_id" class="form-control form-control-sm">
                                        <option value="">Hepsi</option>
                                        @foreach($zones as $zone)
                                            <option @if(!empty($filters['zone_id']) && $filters['zone_id'] == $zone->id) selected @endif value="{{ $zone->id }}">{{ $zone->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            @endif
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label for="expertise_campaing"><small>Seçili Kampanya</small></label>
                                    <select name="expertise_campaing" class="form-control select2" id="expertise_campaing">
                                        <option value="all">Hepsi</option>
                                        @foreach(\App\Models\Campaign::where('status', 1)->get() as $campaing)
                                            <option @if(!empty($_GET['expertise_campaing']) && $_GET['expertise_campaing'] == $campaing->id) selected="" @endif value="{{$campaing->id}}">{{$campaing->name}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label><small>Kayıtlı Aramalar</small></label>
                                    <select form="getSavedFilter" onchange="$('#getSavedFilter').submit()" class="form-control select2" name="saved_filter_id">
                                        <option value="0">Seçilmedi</option>
                                        @foreach($savedFilters as $savedFilter)
                                            <option @if(isset($_GET['saved_filter_id']) && $_GET['saved_filter_id'] == $savedFilter->id) selected @endif value="{{ $savedFilter->id }}">{{ $savedFilter->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-2">
                                <button class="btn btn-sm btn-success mt-4" name="type" value="customer">Listele</button>
                                <button onclick="$('#saveFilter').modal('toggle')" type="button" class="btn btn-sm btn-danger mt-4" >Aramayı Kaydet</button>
                            </div>
                            @if(in_array('*', $userRoles) || in_array('list_googleseo', $userRoles))
                            <div class="col-xl-10 col-10 d-flex flex-column justify-content-end align-items-end">
                                <div class="btn-group" role="group">
                                    @if(!empty($google_button_1))
                                        <a href="{{ $google_button_1 }}" type="button" class="btn btn-outline-primary">
                                            <i class="fa bi-google"></i> Google Reklam Raporu
                                        </a>
                                    @endif
                                    @if(!empty($google_button_2))
                                        <a href="{{ $google_button_2 }}" type="button" class="btn btn-outline-primary">
                                            <i class="fa bi-google"></i> Bayi Reklam Detay
                                        </a>
                                    @endif
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    @if(in_array('*', $userRoles) || in_array('add_expertise', $userRoles))
                        <a href="{{ route('expertises.create') }}" class="nav-link">{{ \App\Models\Menu::where('key','add_expertise')->first()->value }}</a>
                    @endif
                    @if(in_array('*', $userRoles) || in_array('list_expertise', $userRoles))
                        <a href="{{ route('ekspertizIslemleri') }}" class="nav-link">Ekspertiz İşlemleri</a>
                    @endif
                    @if(in_array('*', $userRoles) || in_array('list_note', $userRoles))
                        <a href="{{ route('notes.index') }}" class="nav-link">{{ \App\Models\Menu::where('key','list_note')->first()->value }}</a>
                    @endif

                    <a href="{{ route('query.index') }}" class="nav-link">Hasar Sorguları</a>
                </nav>
                <div class="card-body">
                    @if($authUser->type == 'admin')
                        <div style="display: flex">
                            <div style="width: 20px;height: 20px;background-color: #e2626b"></div> ArabaSorgula.com
                        </div>
                        <div style="display: flex">
                            <div style="width: 20px;height: 20px;background-color: #fecc16"></div> Otosorgu.com
                        </div>
                    @endif
                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr>
                            <th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th data-visibility="true" data-key="1" >İşlemler</th>
                            <th data-visibility="true" data-key="2" >Durum</th>
                            <th data-visibility="true" data-key="3">Belge Tarihi</th>
                            <th data-visibility="false" data-key="4">Kayıt Yeri</th>
                            <th data-visibility="true" data-key="5">Bayi</th>
                            <th data-visibility="true" data-key="6" >Müşteri Unvanı</th>
                            <th data-visibility="true" data-key="7" >Satıcı Unvanı</th>
                            <th data-visibility="true" data-key="8" >Plaka</th>
                            <th data-visibility="true" data-key="9" >Şase No</th>
                            <th data-visibility="true" data-key="10" >Araç KM</th>
                            <th data-visibility="false" data-key="11" >Model Yılı</th>
                            <th data-visibility="false" data-key="12" >Ağırlık</th>
                            <th data-visibility="false" data-key="13" >Çekişi</th>
                            <th data-visibility="true" data-key="14" >Hizmet</th>
                            <th data-visibility="true" data-key="15">Kampanya</th>
                            <th data-visibility="true" data-key="16">Ödeme Tipi</th>
                            <th data-visibility="true" data-key="17">Hizmet Tutarı</th>
                            <th data-visibility="true" data-key="18">Liste Fiyat</th>
                            <th data-visibility="true" data-key="19">İskonto Tutarı</th>
                            <th data-visibility="true" data-key="20">Hasar Sorgu Tutarı</th>
                            <th data-visibility="true" data-key="21">Kilometre Sorgu Tutarı</th>
                            <th data-visibility="true" data-key="22">Borç Sorgu Tutarı</th>
                            <th data-visibility="true" data-key="23">Detay Sorgu Tutarı</th>
                            <th data-visibility="true" data-key="24">Değişen Sorgu Tutarı</th>
                            <th data-visibility="true" data-key="25">Ruhsat Sorgu Tutarı</th>
                            <th data-visibility="true" data-key="26">Çıkış Tarihi</th>
                            <th data-visibility="true" data-key="27">Belge No</th>
                            <th data-visibility="false" data-key="28">Belge Özel Kod</th>
                            <th data-visibility="false" data-key="29">Bize Nerden Ulaştınız</th>
                            <th data-visibility="true" data-key="30">Yayın Yasağı</th>
                            <th data-visibility="false" data-key="31">Sigorta Teklif Ver</th>
                            <th data-visibility="false" data-key="32">Yol Yardımı</th>
                            <th data-visibility="true" data-key="33">Hasar Sorgulama</th>
                            <th data-visibility="true" data-key="34">Ses Kaydı</th>
                            <th data-visibility="false" data-key="35">Sözleşme Var mı?</th>
                            <th data-visibility="false" data-key="36">Sözleşme Sahibi</th>
                            <th data-visibility="false" data-key="35">Sil</th>
                        </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="saveFilter" tabindex="-1"
         aria-labelledby="saveFilter" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Filtre Şablonu Oluştur
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Şablon Adı</small></label>
                            <input type="text" class="form-control" name="saved_filter_name">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger save-filter">Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(".select2").select2({
            placeholder: "Ara.."
        });

        $('.customer-search').select2({
            language: {
                inputTooShort: function () {
                    return "Daha fazla karakter giriniz...";
                },
                noResults: function () {
                    return "Sonuç bulunamadı";
                },
                searching: function () {
                    return "Aranıyor...";
                }
            },
            placeholder: "Cari arayınız...",
            allowClear: true,
            minimumInputLength: 3,
            maximumInputLength: 20,
            minimumResultsForSearch: 20,
            ajax: {
                url: "{{ route('getCustomersForSelect') }}",
                dataType: 'json',
                delay: 250,
                type: "POST",
                data: function (params) {
                    return {
                        unvan: params.term,
                        page: params.page || 1,
                        _token: '{{ csrf_token() }}'
                    };
                },
                processResults: function (data, params) {
                    return {
                        results: data.items,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                }
            },
        });

        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({
                "ordering": false, // Disable sorting
                "dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                "processing": true,
                "serverSide": true,
                "searching":false,
                "ajax": {
                    "url": "{{ route('getExpertisesForAjax') }}",
                    "dataType": "json",
                    "type": "POST",
                    "data":{
                        _token: "{{csrf_token()}}",
                        @if(!empty($_GET['search']))
                        'search' : "{{$_GET['search']}}",
                        @endif

                        @if(!empty($_GET['start_date']))
                        'start_date':'{{$_GET['start_date']}}',
                        @endif

                        @if(!empty($_GET['end_date']))
                        'end_date':'{{$_GET['end_date']}}',
                        @endif

                        @if(!empty($_GET['belge_no']))
                        'belge_no':'{{$_GET['belge_no']}}',
                        @endif

                        @if(!empty($_GET['contract_no']))
                            'contract_no':'{{$_GET['contract_no']}}',
                        @endif

                        @if(!empty($_GET['contract_id']))
                            'contract_id':'{{$_GET['contract_id']}}',
                        @endif

                        @if(!empty($_GET['uuid']))
                        'uuid':'{{$_GET['uuid']}}',
                        @endif

                        @if(!empty($_GET['payment_type']))
                            'payment_type':'{{$_GET['payment_type']}}',
                        @endif

                        @if(!empty($_GET['branch_id']))
                        'branch_id':'{{$_GET['branch_id']}}',
                        @endif

                        @if(!empty($_GET['type']))
                        'type':'{{$_GET['type']}}',
                        @endif

                        @if(!empty($_GET['customer_id']))
                        'customer_id':'{{$_GET['customer_id']}}',
                        @endif

                        @if(!empty($filters['zone_id']))
                        'zone_id':'{{$filters['zone_id']}}',
                        @endif

                        @if(!empty($_GET['expertise_campaing']))
                        'expertise_campaing':'{{$_GET['expertise_campaing']}}',
                        @endif
                    },
                    "dataSrc":'data'
                },
                "columns": [
                    { "data": 'x','defaultContent': '' },
                    { "data": 'button_edit' },
                    { "data": 'durum'},
                    { "data": 'belge_tarihi'},
                    { "data": 'kayitBranch'},
                    { "data": 'branch' },
                    { "data": 'aliciUnvan' },
                    { "data": 'cariUnvan' },
                    { "data": 'plaka'},
                    { "data": 'sase_no' },
                    { "data": 'km' },
                    { "data": 'model_yili', },
                    { "data": 'net_agirlik', },
                    { "data": 'cekis' },
                    { "data": 'getStocks'},
                    { "data": 'campaign'},
                    { "data": 'payment_type'},
                    { "data": 'hizmetTutari'},
                    { "data": 'listeFiyati'},
                    { "data": 'iskonto_amount'},
                    { "data": 'hasar_sorgulama_tutari'},
                    { "data": 'kilometre_sorgulama_tutari'},
                    { "data": 'borc_sorgulama_tutari'},
                    { "data": 'details_sorgulama_tutari'},
                    { "data": 'degisen_sorgulama_tutari'},
                    { "data": 'ruhsat_sorgulama_tutari'},
                    { "data": 'cikis_tarihi' },
                    { "data": 'belge_no' },
                    { "data": 'belge_ozel_kodu' },
                    { "data": 'nereden_ulastiniz'},
                    { "data": 'yayin_yasagi' },
                    { "data": 'sigorta_teklif_ver'},
                    { "data": 'yol_yardim'},
                    { "data": 'hasar_sorgulama' },
                    { "data": 'audio_url' },
                    { "data": 'sozlesme_var_mi' },
                    { "data": 'sozlesme_sahibi' },
                    { "data": 'delete' },

                ],
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_expertise', $userRoles)) <a href='{{ route("expertises.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Tablo İçerisinde Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_expertise', $userRoles)) <a href='{{ route("expertises.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });
            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    var checked = "";
                    if(column.getAttribute('data-visibility') != "false"){
                        checked ="checked=''";
                    }
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" '+checked+' id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"


                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)
            columns.forEach(function(column) {
                if(column.getAttribute('data-visibility') == "false"){
                    console.log(column.cellIndex)
                    if (column.cellIndex > 0) {
                        let hide_column = responsiveDatatable.column(column);
                        hide_column.visible(false)
                        console.log(column);

                    }
                }
            })

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })

            @if(isset($_GET['sozlesme']) && $_GET['sozlesme'] != '' && !session('error'))
            window.open('{{ route('aliciSozlesme',$_GET['sozlesme']) }}','_blank');
            @endif
        });
        addEventListener("DOMContentLoaded", (event) => {
            $('html').attr('data-toggled','close')
        });

        if (!Swal.isVisible()) {
            const Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }


        $('.save-filter').on('click',function (){
            if ($('input[name="saved_filter_name"]').val() != ''){
                $.ajax({
                    url: "{{ route('saveFilters') }}",
                    type: "post",
                    data: {
                        '_token':'{{ csrf_token() }}',
                        'name':$('input[name="saved_filter_name"]').val(),
                        'start_date':$('input[name="start_date"]').val(),
                        'end_date':$('input[name="end_date"]').val(),
                        'belge_no':$('input[name="belge_no"]').val(),
                        'search':$('input[name="search"]').val(),
                        'branch_id':$('select[name="branch_id"]').val(),
                        'type':'expertise'
                    } ,
                    success: function (response) {

                        if (response.success == 'true'){
                            Toast.fire({
                                icon: "success",
                                title: "Şablon Kayıt Edildi"
                            });

                            $('select[name="saved_filter_id"]').append('<option value="'+response.id+'" selected="selected">'+response.name+'</option>');
                            $('#getSavedFilter').submit()
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "Şablon Kayıt Edilemedi"
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Şablon Adı Girilmedi"
                });
            }
        })
    </script>
@endpush
