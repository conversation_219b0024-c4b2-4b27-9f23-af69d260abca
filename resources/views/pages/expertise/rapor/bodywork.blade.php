<div class="row kaporta-boya rapor dikey">
    <div class="table-responsive">
        <table class="table">
            <tbody>
                <tr>
                    <td colspan="2" style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 65%">
                        <h6 style="color: darkorange">0850 354 54 17 / 0216 302 54 17</h6>
                        <b style="font-size: .5rem">Rapor Basım Tarihi / Saati / Sırası:</b>
                        <h8 style="color: #333335;"> {{ now()->format('d.m.Y') }} / {{ now()->format('H:i') }} / {{ $expertise['download_count'] }}</h8>
                    </td>
                    <td colspan="3" style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU</b><br>
                        Bağ<PERSON>msız Oto Ekspertiz Merkezi<br> www.umranoto.com<br><br>
                        <b>{{ $expertise['getBranch'] }}</b>
                        @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') <br><br> <b>KAPORTA & BOYA</b> @endif
                    </td>
                    <td colspan="1" class="text-center" style="width: 20%">
                        <img src="/assets/images/hyb.png" style="width: 35%"><br>
                        <b>TSE-HYB Belge No:</b>
                        <h6 class="text-danger">{{ $expertise['tse_kodu'] }}</h6>
                    </td>
                    <td colspan="" style="width: 30%;text-align:center;padding-right: 0;">
                        {!! $expertise['qr'] !!}
                        <h5 class="company-phone-right">444 54 17</h5>
                    </td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Bayi:</td>
                    <td colspan="2"><b>{{ $expertise['getBranch'] }}</b></td>
                    <td colspan="1">Marka/Model:</td>
                    <td colspan="1">{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td colspan="1">@if($expertise['km_type'] == 1) KM @else Mil @endif:</td>
                    <td colspan="1"><b>{{ number_format($expertise['km'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Belge No:</td>
                    <td colspan="2"><b class="text-danger">{{ $expertise['belge_no'] }}</b></td>
                    <td colspan="1">Model Yılı:</td>
                    <td colspan="1"><b>{{ $expertise['model_yili'] }}</b></td>
                    <td colspan="1">Motor Hacmi:</td>
                    <td colspan="1"><b>
                            @if(is_numeric($expertise['motor_hacmi']))
                            {{ number_format($expertise['motor_hacmi'] ?? 0,0,'.','.') }}
                            @else
                            {{$expertise['motor_hacmi']}}
                            @endif

                        </b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Giriş Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['created_at'] }}</b></td>
                    <td colspan="1">Şase Numarası:</td>
                    <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                            {{ $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                            {{ !empty($expertise['sase_no'])  && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . '*****' . substr($expertise['sase_no'], -1) : '' }}
                        </b>
                    </td>
                    <td colspan="1">Yakıt Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getFuel'] }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Çıkış Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['cikis_tarihi'] }}</b></td>
                    <td colspan="1">Plaka:</td>
                    <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                            {{ $expertise['plaka'] }}
                        </b>
                    </td>
                    <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                            {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . '*****' . substr($expertise['plaka'], -1) : '' }}
                        </b>
                    </td>
                    <td colspan="1">Vites Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getGear'] }}</b></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="col-md-6 table-responsive">
        <table class="table table-bordered mt-2">
            <thead>
                <tr class="text-center">
                    <th style="text-wrap: nowrap">PARÇA ADI</th>
                    <th>ORJ.</th>
                    <th>BOY.</th>
                    <th>DEĞ.</th>
                    <th>DÜZ.</th>
                    <th>AÇIKLAMA</th>
                </tr>
            </thead>
            <tbody>
                @foreach($bodyworks1 as $key => $bodywork)
                <tr>
                    <td style="text-wrap: nowrap">{{ $bodywork }}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="col-md-6 table-responsive">
        <table class="table table-bordered mt-2">
            <thead>
                <tr class="text-center">
                    <th style="text-wrap: nowrap">PARÇA ADI</th>
                    <th>ORJ.</th>
                    <th>BOY.</th>
                    <th>DEĞ.</th>
                    <th>DÜZ.</th>
                    <th>AÇIKLAMA</th>
                </tr>
            </thead>
            <tbody>
                @foreach($bodyworks2 as $key => $bodywork)
                <tr>
                    <td style="text-wrap: nowrap">{{ $bodywork }}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    <div class="col-md-6 mt-3 table-responsive">
        @if($expertise['coordinates'] != [])
        <canvas id="canvas"></canvas>
        <img id="myImage" src="/storage/car_case_type/{{ $expertise['coordinates']['image_url'] }}.jpg" class="img-fluid kaporta-gorsel">
        @else
        <img src="{{ $expertise['bodywork_image'] }}" class="img-fluid kaporta-gorsel">
        @endif
    </div>
    <div class="col-md-6 mt-3 table-responsive">
        <table class="table table-bordered mt-2">
            <thead>
                <tr class="text-center">
                    <th style="text-wrap: nowrap">PARÇA ADI</th>
                    <th>ORJ.</th>
                    <th>BOY.</th>
                    <th>DEĞ.</th>
                    <th>DÜZ.</th>
                    <th>AÇIKLAMA</th>
                </tr>
            </thead>
            <tbody>
                @foreach($bodyworks3 as $key => $bodywork)
                <tr>
                    <td style="text-wrap: nowrap">{{ $bodywork }}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['orijinal'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['boyali'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['degisen'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td class="text-center" style="width: 1%">{!! $expertise['getBodyworks'][$key]['duz'] == 1 ? '<i class="fa fa-check"></i>' : '' !!}</td>
                    <td>{!! $expertise['getBodyworks'][$key]['note'] !!}</td>
                </tr>
                @endforeach
            </tbody>
            <tfoot>
                <tr>
                    <td colspan="6">
                        Tamponlar plastik aksam olduğundan boya ve değişim değerlendirilmesi yapılmamıştır.<br>
                        <b>ORJ:</b> Orijinal Parça <b style="margin-left: 1rem">DÜZ:</b> Düzeltme ve/veya macun işlemi uygulanmış parça <br>
                        <b>ST:</b> Sökülüp takılmış parça <b style="margin-left: 1rem">DEĞ:</b> Değişmiş Parça<br>
                        (ST:Kendi üzerindeki ya da aynı model araçtan alınan birebir parça)
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
    <div class="col-md-12 table-responsive mt-3">
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th colspan="4" class="text-center">NOTLAR</th>
                </tr>
            </thead>
            <tbody>
                @php
                $td_count = 10;
                if($td_count < count($expertise['allNotes'][1])){
                    $td_count=count($expertise['allNotes'][1]);
                    }

                    foreach($expertise['allNotes'][1] as $key=> $notes_array){
                    $expertise['allNotes'][1][$key] = '[KPR] '.$notes_array;
                    }

                    // Toplam eleman sayısını bul
                    $totalCount = count($expertise['allNotes'][1]);




                    $firstArraykpr = array_slice($expertise['allNotes'][1], 0, ceil($td_count));
                    $secondArraykpr = array_splice($expertise['allNotes'][1], ceil($td_count));
                    @endphp
                    @for ($i = 0; $i < $td_count; $i++)
                        <tr>
                        <td class="col-6 not-item @if(!empty($firstArraykpr[$i])) not-var @else not-yok @endif">{{!empty($firstArraykpr[$i]) ? $firstArraykpr[$i]:''}} </td>
                        <td class="col-6 not-item @if(!empty($secondArraykpr[$i])) not-var @else not-yok @endif"> {{!empty($secondArraykpr[$i]) ? $secondArraykpr[$i] : ''}} </td>
                        </tr>
                        @endfor
            </tbody>
        </table>
        <table class="table table-bordered">

            <tbody>
                <tr>
                    <td class="print_bg" style="background-color: #ff1000;color: #fff;text-align: center;font-weight: bolder" colspan="4">SERVİSLERİMİZDEN ALINAN EKSPERTİZ RAPORLARINA WWW.UMRANOTO.COM ADRESİNDEN ULAŞABİLİRSİNİZ.</td>
                </tr>
                <tr>
                    <td colspan="4">
                        Umran Oto Ekspertiz Merkezleri tüm dünyadaki ekspertiz hizmetleri gibi aracın o anki durumunu analiz eder ve bilgi verir. Ekspertiz yaptığı aracın kesinlikle ileride arıza ve problem çıkarmayacağını garanti edemez. Aracın o anki görülen hataları kontrol edilerek raporlanır. Kaporta boya değerlendirmesine itiraz 72 saat, motor ile ilgili değerlendirmeye itiraz 24 saattir. Bayilerimizde kilometre orijinalliği tespiti yapılmamaktadır. Elektronik arşiv süresi 5 yıldır.
                    </td>
                </tr>
                <tr class="text-center">
                    <td class="imza">
                        KAPORTA BOYA TEKNİKERİ
                        @if($expertise['bodywork_expert'])
                        <br><br>
                        <b>{{ $expertise['bodywork_expert'] }}</b>
                        @endif
                    </td>
                    <td class="imza">
                        SERVİS MÜDÜRÜ
                        @if($expertise['service_manager'])
                        <br><br>
                        <b>{{ $expertise['service_manager'] }}</b>
                        @endif
                    </td>
                    @include('pages.expertise.rapor.customer_buyer')
                    @include('pages.expertise.rapor.customer_seller')
                </tr>
            </tbody>
        </table>
        <div class="d-flex justify-content-between" style="font-size: .7rem">
            <span><span class="text-danger">KBR</span> {{ $expertise['bayiAdres'] }}</span>
            <span>{{ $expertise['bayiVergiDairesi'] }} VERGİ DAİRESİ {{ $expertise['bayiVergiNo'] }} / MERSİS : {{ $expertise['bayiMersis'] }}</span>
        </div>
    </div>
</div>
