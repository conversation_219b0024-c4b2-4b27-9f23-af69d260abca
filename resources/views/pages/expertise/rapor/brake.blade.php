<div class="row fren-testi yatay rapor sonuc-sayfa-bg">
    <div class="sonuc-header">
        <div class="sonuc-logo">
            <img src="/assets/isemri_logo.png">
            <h6 style="color: darkorange">0850 354 54 17 / 0216 302 54 17</h6>
            <h6 class="text-danger">www.umranoto.com</h6>
            <b style="font-size: .5rem">Rapor Basım Tarihi / Saati / Sırası:</b>
            <h8 style="color: #333335;"> {{ now()->format('d.m.Y') }} / {{ now()->format('H:i') }} / {{ $expertise['download_count'] }}</h8>
        </div>
        <div class="sonuc-info">
            <div class="sonuc-first-tab">
                <div class="sonuc-first-tab-item">
                    <b>Plaka</b><br>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['plaka'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . '*****' . substr($expertise['plaka'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Model Yılı</b><br>
                    {{ $expertise['model_yili'] }}
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Şase No</b><br>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['sase_no'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['sase_no'])  && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . '*****' . substr($expertise['sase_no'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Yetkili Hizmet Yeri</b><br>
                    {{ $expertise['getBranch'] }}
                </div>
            </div>
            <div class="sonuc-second-tab">
                <span style="color: #fff">Fren Testi Sonuç Sayfası</span>
                <span>UMRAN Bağımsız Araç Ekspertiz Merkezleri</span>
            </div>
            <div class="sonuc-third-tab">
                <div class="sonuc-first-tab-item">
                    <b>Marka:</b>
                    {{ $expertise['getMarka'] }} {{ $expertise['getModel'] }}
                </div>
                <div class="sonuc-first-tab-item">
                    <b>@if($expertise['km_type'] == 1) KM @else Mil @endif:</b>
                    {{ number_format($expertise['km'] ?? 0,0,'.','.') }}
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Belge No:</b>
                    <span style="font-size: 1.4rem" class="text-danger">{{ $expertise['belge_no'] }}</span>
                </div>
            </div>
            <div class="sonuc-third-tab">
                <div class="sonuc-first-tab-item">
                    <b>Müşteri:</b>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['getAlici'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['getAlici']) && strlen($expertise['getAlici']) > 3 ? substr($expertise['getAlici'], 0, 2) . '*****' . substr($expertise['getAlici'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <b>GSM:</b>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['getAliciTel'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['getAliciTel'])  && strlen($expertise['getAliciTel']) > 3 ? substr($expertise['getAliciTel'], 0, 2) . '*****' . substr($expertise['getAliciTel'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <div style="background-color: lightgoldenrodyellow;padding: 3px 7px"><i class="fa fa-clock"></i> {{ $expertise['created_at'] }}</div>
                </div>
            </div>
        </div>

    </div>
    <div class="row" style="    background-color: #f8f8f8;">
        <div class="offset-3 col-5">
            <div class="row">
                <div class="col-12 text-center">
                    <span><b>Ön Fren Dengesizliği</b> : <span @if((int)$expertise['on_fren_dengesizlik_orani'] <=30) class="text-success-rapor" @else class="text-danger-rapor" @endif style="font-size: 2rem">%{{ $expertise['on_fren_dengesizlik_orani'] }}</span></span>
                </div>
                <div class="col-3" style="display: flex;flex-direction: column;justify-content: space-between;align-items: end;">
                    <div>
                        <p>Maksimum</p>
                        <div class="tutunma-box">{{ $expertise['fren_max_a'] }}kN</div>
                    </div>
                    <div>
                        <p>Maksimum</p>
                        <div class="tutunma-box">{{ $expertise['fren_max_b'] }}kN</div>
                    </div>
                </div>
                <div class="col-6 text-center">
                    <img src="/assets/arac_alti.png" style="width: 75%" class="img-fluid">
                </div>
                <div class="col-3" style="display: flex;flex-direction: column;justify-content: space-between;align-items: start;">
                    <div>
                        <p>Maksimum</p>
                        <div class="tutunma-box">{{ $expertise['fren_max_c'] }}kN</div>
                    </div>
                    <div>
                        <p>Maksimum</p>
                        <div class="tutunma-box">{{ $expertise['fren_max_d'] }}kN</div>
                    </div>
                </div>
                <div class="col-12 text-center">
                    <span><b>Arka Fren Dengesizliği</b> : <span @if((int)$expertise['arka_fren_dengesizlik_orani'] <=30) class="text-success-rapor" @else class="text-danger-rapor" @endif style="font-size: 2rem">%{{ $expertise['arka_fren_dengesizlik_orani'] }}</span></span>
                    <br>
                    <div style="background-color: #f8f8f8;padding: 1rem">
                        <div class="color-bar" style="justify-content: center">
                            <div class="text">%0</div>
                            <div class="line green"></div>
                            <div class="text">%30</div>
                            <div class="line red"></div>
                            <div class="text">%100</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-4 d-flex" style="align-items: center">
            <div class="text-center">
                <h6 style="border-bottom: 1px solid">Ön Dingil Boşta Sürtünme</h6>
                <div class="row">
                    <div class="col-6">
                        SOL: {{ $expertise['fren_on_bosta_surtunme_a'] }}kN
                    </div>
                    <div class="col-6">
                        SAĞ: {{ $expertise['fren_on_bosta_surtunme_b'] }}kN
                    </div>
                </div>
                <h6 style="margin-top: 1rem;border-bottom: 1px solid">Ön Dingil Yalpa</h6>
                <div class="row">
                    <div class="col-6">
                        SOL: {{ $expertise['fren_on_yalpa_a'] }}%
                    </div>
                    <div class="col-6">
                        SAĞ: {{ $expertise['fren_on_yalpa_b'] }}%
                    </div>
                </div>
                <h6 style="margin-top: 1rem;border-bottom: 1px solid">Arka Dingil Boşta Sürtünme</h6>
                <div class="row">
                    <div class="col-6">
                        SOL: {{ $expertise['fren_arka_bosta_surtunme_a'] }}kN
                    </div>
                    <div class="col-6">
                        SAĞ: {{ $expertise['fren_arka_bosta_surtunme_b'] }}kN
                    </div>
                </div>
                <h6 style="margin-top: 1rem;border-bottom: 1px solid">Arka Dingil Yalpa</h6>
                <div class="row">
                    <div class="col-6">
                        SOL: {{ $expertise['fren_arka_yalpa_a'] }}%
                    </div>
                    <div class="col-6">
                        SAĞ: {{ $expertise['fren_arka_yalpa_b'] }}%
                    </div>
                </div>
                <h6 style="margin-top: 1rem;border-bottom: 1px solid">El Freni Kuvvetleri</h6>
                <div class="row">
                    <div class="col-6">
                        SOL : {{ $expertise['fren_el_freni_a'] }}kN
                    </div>
                    <div class="col-6">
                        SAĞ : {{ $expertise['fren_el_freni_b'] }}kN
                    </div>
                    <div class="col-12 mt-5">
                        El Freni Dengesizliği <span @if((int)$expertise['fren_el_freni_dengesizlik'] <=30) class="text-success-rapor" @else class="text-danger-rapor" @endif style="font-size: 2rem">% {{ $expertise['fren_el_freni_dengesizlik'] }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="d-flex justify-content-between" style="font-size: .7rem">
        <span><span class="text-danger">KBR</span> {{ $expertise['bayiAdres'] }}</span>
        <span>{{ $expertise['bayiVergiDairesi'] }} VERGİ DAİRESİ {{ $expertise['bayiVergiNo'] }} / MERSİS : {{ $expertise['bayiMersis'] }}</span>
    </div>
</div>