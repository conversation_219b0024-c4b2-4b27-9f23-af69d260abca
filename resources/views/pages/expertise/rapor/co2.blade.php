<div class="row conta rapor dikey">
    <div class="table-responsive">
        <table class="table">
            <tbody>
                <tr>
                    <td colspan="2" style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 65%">
                        <h6 style="color: darkorange">0850 354 54 17 / 0216 302 54 17</h6>
                        <b style="font-size: .5rem">Rapor Basım Tarihi / Saati / Sırası:</b>
                        <h8 style="color: #333335;"> {{ now()->format('d.m.Y') }} / {{ now()->format('H:i') }} / {{ $expertise['download_count'] }}</h8>
                    </td>
                    <td colspan="3" style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU</b><br>
                        Bağ<PERSON>ms<PERSON>z Oto Ekspertiz Merkezi<br> www.umranoto.com<br><br>
                        <b>{{ $expertise['getBranch'] }}</b>
                    </td>
                    <td colspan="1" class="text-center" style="width: 20%">
                        <img src="/assets/images/hyb.png" style="width: 35%"><br>
                        <b>TSE-HYB Belge No:</b>
                        <h6 class="text-danger">{{ $expertise['tse_kodu'] }}</h6>
                    </td>
                    <td colspan="" style="width: 30%;text-align:center;padding-right: 0;">
                        {!! $expertise['qr'] !!}
                        <h5 class="company-phone-right">444 54 17</h5>
                    </td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Bayi:</td>
                    <td colspan="2"><b>{{ $expertise['getBranch'] }}</b></td>
                    <td colspan="1">Marka/Model:</td>
                    <td colspan="1">{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td colspan="1">@if($expertise['km_type'] == 1) KM @else Mil @endif:</td>
                    <td colspan="1"><b>{{ number_format($expertise['km'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Belge No:</td>
                    <td colspan="2"><b class="text-danger">{{ $expertise['belge_no'] }}</b></td>
                    <td colspan="1">Model Yılı:</td>
                    <td colspan="1"><b>{{ $expertise['model_yili'] }}</b></td>
                    <td colspan="1">Motor Hacmi:</td>
                    <td colspan="1"><b>
                            @if(is_numeric($expertise['motor_hacmi']))
                            {{ number_format($expertise['motor_hacmi'] ?? 0,0,'.','.') }}
                            @else
                            {{$expertise['motor_hacmi']}}
                            @endif

                        </b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Giriş Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['created_at'] }}</b></td>
                    <td colspan="1">Şase Numarası:</td>
                    <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                            {{ $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                            {{ !empty($expertise['sase_no'])  && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . '*****' . substr($expertise['sase_no'], -1) : '' }}
                        </b>
                    </td>
                    <td colspan="1">Yakıt Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getFuel'] }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Çıkış Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['cikis_tarihi'] }}</b></td>
                    <td colspan="1">Plaka:</td>
                    <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                            {{ $expertise['plaka'] }}
                        </b>
                    </td>
                    <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                            {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . '*****' . substr($expertise['plaka'], -1) : '' }}
                        </b>
                    </td>
                    <td colspan="1">Vites Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getGear'] }}</b></td>
                </tr>
            </tbody>
        </table>
    </div>


    <div class="row my-5">
        <div class="col-6 text-center">
            <img src="/assets/motor.png" style="width: 60%" class="img-fluid">
        </div>
        <div class="col-6 text-center">
            <div class="row">
                <div class="col-2">
                    <img src="/assets/conta_1.png" style="width: 85%" class="img-fluid">
                    <span>Sorunsuz</span>
                </div>
                <div class="col-2">
                </div>
                <div class="col-2">
                    <img src="/assets/conta_2.png" style="width: 85%" class="img-fluid">
                    <span class="text-danger">Sorunlu</span>
                </div>
                <div class="col-2">
                    <img src="/assets/conta_3.png" style="width: 85%" class="img-fluid">
                    <span class="text-danger">Sorunlu</span>
                </div>
                <div class="col-2">
                    <img src="/assets/conta_4.png" style="width: 85%" class="img-fluid">
                    <span class="text-danger">Sorunlu</span>
                </div>
                <div class="col-2">
                    <img src="/assets/conta_5.png" style="width: 85%" class="img-fluid">
                    <span class="text-danger">Sorunlu</span>
                </div>
            </div>
        </div>
        <div class="col-12 text-center my-5">
            <h4>CO2 TESTİ SONUCU</h4>
            <h2 style="color: #0a53be">{{ $expertise['co2Sonuc'] }}</h2>
            @if($expertise['co2Sonuc'] == 'Sorunlu')
            <i class="fa fa-times fa-2x" style="color: #0a53be"></i>
            @else
            <i class="fa fa-check fa-2x" style="color: #0a53be"></i>
            @endif

            @if($expertise['co2not'] != "Yok" && $expertise['only_co_kacak'] == 1)
            <p>
                {{$expertise['co2not'] ?? ''}}
            </p>
            @endif
            @if(!empty($expertise['co2Note']))
            @foreach($expertise['co2Note'] as $co2)
            <p>
                {{$co2->note}}
            </p>
            @endforeach
            @endif
        </div>
    </div>
    <div class="col-12">
        <p style="font-size: .4rem">
            *CO2 testinde aracın yanma merkezi ve soğutma sistemleri arasında meydana gelebilecek kaçaklar tespit edilir.<br>
            *CO2 testiyle ilgili hizmet sonuçları test sıvısı üzerinden yorumlanmaktadır. İçeriğiyle ilgili olarak Umran Bağımsız Araç Ekspertiz Merkezlerinin sorumluluğu bulunmadığı gibi herhangi bir garanti yükümlülüğü taahhüdü de bulunmamaktadır.<br>
            *Umran Bağımsız Araç Ekspertiz Merkezleri, aracın görünen kusurlan ve o anki durumu ile ilgili bilgilendirme yapar. Satıcı ve üçüncü şahıslar tarafindan gizlenmiş kusurların tespit edilememesinden ve sonrasında ortaya çıkacak problemlerden dolayı Umran Bağımsız Araç Ekspertiz Merkezleri sorumlu tutulamaz.<br>
            *Yalnızca sökme takma neticesinde anlaşılabilecek anzaların tespit edilememesinden dolayı şirketimizin herhangi bir sorumluluğu bulunmamaktadır.<br>
            *Aracın çalıntı olması ve ya yasal olmayan şase numara aktarımı işlemleri kontrol edilmemekte olup Umran Bağımsız Araç Ekspertiz Merkezleri'ni bağlayıcı hukuki sorumluluğu yoktur.<br>
            *Alia/satıcı beyanına göre CO2 raporu talebi yapılan araç bilgilerine özel üretilmiş işbu CO2 raporunun tüm hukuki ve cezai sorumluluğu BAYİ'ye aittir. Umran Plus Otomotiv San. ve Tic. A.Ş.'nin bu hususta herhangi bir hukuki ve cezai bir sorumluluğu bulunmamaktadır.
        </p>
    </div>

    <div class="col-md-12 table-responsive">
        <table class="table table-bordered">

            <tbody>
                <tr>
                    <td class="print_bg" style="background-color: #ff1000;color: #fff;text-align: center;font-weight: bolder" colspan="4">SERVİSLERİMİZDEN ALINAN EKSPERTİZ RAPORLARINA WWW.UMRANOTO.COM ADRESİNDEN ULAŞABİLİRSİNİZ.</td>
                </tr>
                <tr>
                    <td colspan="4">
                        Umran Oto Ekspertiz Merkezleri tüm dünyadaki ekspertiz hizmetleri gibi aracın o anki durumunu analiz eder ve bilgi verir. Ekspertiz yaptığı aracın kesinlikle ileride arıza ve problem çıkarmayacağını garanti edemez. Aracın o anki görülen hataları kontrol edilerek raporlanır. Kaporta boya değerlendirmesine itiraz 72 saat, motor ile ilgili değerlendirmeye itiraz 24 saattir. Bayilerimizde kilometre orijinalliği tespiti yapılmamaktadır. Elektronik arşiv süresi 5 yıldır.
                    </td>
                </tr>
                <tr class="text-center">
                    <td class="imza">
                        MOTOR MEKANİK TEKNİKERİ
                        @if($expertise['mechanic_experts'])
                        <br><br>
                        @foreach ($expertise['mechanic_experts'] as $mechanicExpert)
                        <b>{{ $mechanicExpert }}</b><br>
                        @endforeach
                        @endif
                    </td>
                    <td class="imza">
                        SERVİS MÜDÜRÜ
                        @if($expertise['service_manager'])
                        <br><br>
                        <b>{{ $expertise['service_manager'] }}</b>
                        @endif
                    </td>
                    @include('pages.expertise.rapor.customer_buyer')
                    @include('pages.expertise.rapor.customer_seller')
                </tr>
            </tbody>
        </table>
        <div class="d-flex justify-content-between" style="font-size: .7rem">
            <span><span class="text-danger">KBR</span> {{ $expertise['bayiAdres'] }}</span>
            <span>{{ $expertise['bayiVergiDairesi'] }} VERGİ DAİRESİ {{ $expertise['bayiVergiNo'] }} / MERSİS : {{ $expertise['bayiMersis'] }}</span>
        </div>
    </div>
</div>
