<style id="page">
    .text-success-rapor{
        color:#008001!important;
    }
    .text-warning-rapor{
        color:#ffff00!important;
    }
    .text-warning-rapor{
        color:#ff0000!important;
    }

    table{
        border: .9px solid rgba(0, 0, 0, 0.59) !important;
    }
    .row>*{
        padding: 0;
    }
    th{
        background-color: #cecece57 !important
    }
    .page-break{
        page-break-after: always;
    }
    .table-bordered>:not(caption)>*>*{
        border-width: .5px;
    }
    .table td, .table th{
        padding: .35rem;
    }
    .table-col-6 td{
        width: 16.667% !important;
        border: 1px solid;
    }
    .imza{
        line-height: 2 !important;
    }
    .sonuc-sayfa-title{
        width: 100%;
        background: rgb(238,134,10);
        background: linear-gradient(90deg, rgba(238,134,10,1) 50%, rgba(255,255,255,1) 100%);
    }
    .sonuc-sayfa-title td{
        padding: 1rem;
    }
    .sonuc-sayfa-title td h4{
        margin-bottom: 0;
    }
    .sonuc-sayfa-bg{
        background: rgb(153,153,153);
        background: linear-gradient(0deg, rgba(153,153,153,1) 40%, rgba(255,255,255,1) 100%);
        padding: 2rem;
        border: 1px solid;
    }
    .color-bar {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        justify-content: end;
    }
    .line {
        width: 100px;
        height: 10px;
        margin: 0 10px;
    }
    .green {
        background-color: green;
    }
    .yellow {
        background-color: yellow;
    }
    .red {
        background-color: red;
    }
    .text {
        font-size: 16px;
        font-weight: bold;
    }
    .sonuc-logo{
        flex: 1;
        text-align: center;
        padding: 0.6rem 0.2rem;
        border: 1px solid red;
        border-radius: 1.5rem;
        background: #fff;
    }
    .sonuc-logo img{
        margin-top: 2rem;
    }
    .tutunma-box{
        background-color: #faf7e6;
        padding: 4px 15px;
        font-size: 1rem;
        box-shadow: 5px 6px #99989887;
        border: 1px solid #000;
        width: 4rem;
    }
    .tutunma-item{
        position: relative;
        text-align: center;
    }
    .tutunma-item p{
        margin-bottom: 0;
    }
    .sonuc-header{
        display: flex;
    }
    .sonuc-logo{
        flex: 1;
    }
    .sonuc-first-tab,.sonuc-third-tab{
        display: flex;
        justify-content: space-between;
        margin: 0.3rem 1rem;
    }
    .sonuc-info{
        flex: 7;
    }
    .sonuc-second-tab{
        background: rgb(238,134,10);
        background: linear-gradient(90deg, rgba(238,134,10,1) 50%, rgba(255,255,255,1) 100%);
    }
    .sonuc-second-tab span{
        padding: 1rem;
        font-size: 1.32rem;
    }
    .sonuc-third-tab .sonuc-first-tab-item{
        flex: 1;
    }
    .rapor-item-tr td:nth-child(1),.rapor-item-tr td:nth-child(3){
        width:48%;
        text-wrap:nowrap;
    }
    .rapor-item-tr td:nth-child(2){
        width:4% !important;
        text-wrap:nowrap;
    }
    .table td, .table th{
        padding: 0.25rem;
        font-size: .513rem;
        line-height: 1 !important;
    }
    .table thead tr th{
        font-size:.75rem;
    }
    .rapor{
        padding-top: 1rem;
    }
    .is-emri-sayfa .table td,.is-emri-sayfa .table th{
        padding: 0.35rem;
        font-size: .613rem;
        line-height: .9 !important;
    }
    #divRaporEkrani .rapor{
        margin: .4cm .5cm;
    }
    .imza{
        padding:1.6rem !important;
        line-height: 3 !important;
    }
    p{
        margin-bottom:.5rem;
    }
    .not-item.not-var{
        line-height: 1.3 !important;
        padding: .25rem
    }
    .not-item.not-yok{
        padding: .49rem;
        line-height: 3 !important;
    }
    canvas{
        max-width: 100%;
    }
    .company-phone-right {
        color: darkorange;
        writing-mode: vertical-rl;
        transform: rotate(180deg);
        padding: 0;
        margin: 0;
        float: right;

        /* Safari ve bazı eski Edge sürümleri için */
        -webkit-writing-mode: vertical-rl;
        -ms-writing-mode: tb-rl;
    }
</style>
<style id="print">
    @media print{
        .table-responsive{
            overflow: hidden;
        }
        .audio_div{
            display: none;
        }
        @if(!env('APP_LOCAL'))
        #divOnGosterim{
            display: none;
        }
        @else
        #divOnGosterim .rapor{
            margin: .4cm .5cm;
        }
        @endif
            .page-break{
            page-break-after: always;
        }
        .breadcrumb-header,.print-buttons{
            display: none !important;
        }
        .main-content{
            padding: 0;
        }
        .app-content{
            margin-block-start:unset;
            margin-inline-start:unset;
        }
        body{
            visibility: hidden;
            -webkit-print-color-adjust: exact;
        }

        .main-container div{
            visibility: visible;
        }

        .main-container button{
            visibility: hidden !important;
        }
        .print_bg{
            background-color:  #ff1000;
        }
        @page paysage{
            size: landscape;
        }
        .yatay{
            page: paysage;
        }
        .sonuc-sayfa-bg:nth-last-child{
            page-break-after: unset;
        }
        .audio_div{
            visibility: hidden;
        }

    }
</style>
<div class="row is-emri is-emri-sayfa rapor">
    <table class="table table-bordered">
        <tbody>
        <tr>
            <td style="width: 30%;text-align: center">
                <img src="/assets/isemri_logo.png" style="width: 100%">
                <h6 style="color: darkorange">0850 354 54 17 / 0216 302 54 17</h6>
            </td>
            <td style="width: 30%;font-size: .813rem;" class="text-center">
                <b>ARAÇ EKSPERTİZ RAPORU <br> Bağımsız Oto Ekspertiz Merkezi <br> www.umranoto.com</b>@if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11')<br><br> <b>İŞ EMRİ</b> @endif
            </td>
            <td style="width: 25%">
                <b>Belge No:</b><h4 class="text-danger">{{ $expertise['belge_no'] }}</h4>
                <b>Tarih:</b>
                <h5 class="text-danger">{{ \Carbon\Carbon::make($expertise['created_at'])->format('d.m.Y H:i:s') }}</h5>
                <b style="font-size: .5rem">Rapor Basım Tarihi / Saati / Sırası:</b>
                <h8 style="color: #333335;"> {{ now()->format('d.m.Y') }} / {{ now()->format('H:i') }} / {{ $expertise['download_count'] }}</h8>
            </td>
            <td style="width: 15%; text-align:center; padding-right: 0;">
                {!! $expertise['qr'] !!}
                <h5 class="company-phone-right">444 54 17</h5>
            </td>
        </tr>
        <tr>
            <td colspan="4"><b>Alıcı / Satıcı beyanına göre ekspertiz talebi yapılan araç bilgileri</b></td>
        </tr>
        <tr>
            <td>Plaka</td>
            <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                    {{ $expertise['plaka'] }}
                </b>
            </td>
            <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                    {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . '*****' . substr($expertise['plaka'], -1) : '' }}
                </b>
            </td>
            <td>Şase No</td>
            <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                    {{ $expertise['sase_no'] }}
                </b>
            </td>
            <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                    {{ !empty($expertise['sase_no']) && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . '*****' . substr($expertise['sase_no'], -1) : '' }}
                </b>
            </td>
        </tr>
        <tr>
            <td>Marka Model</td>
            <td>{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
            <td>Araç @if($expertise['km_type'] == 1) KM @else Mil @endif</td>
            <td>{{ number_format($expertise['km'], 0, '', '.') }}</td>
        </tr>
        <tr>
            <td>Model Yılı</td>
            <td>{{ $expertise['model_yili'] }}</td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td colspan="2"><b>Alıcı Bilgileri</b></td>
            <td colspan="2"><b>Satıcı Bilgileri</b></td>
        </tr>
        <tr>
            <td>Adı Soyadı</td>
            <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                @if($expertise['getAliciType'] == 'bireysel') {{ $expertise['getAlici'] }} @endif
            </td>
            <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                @if($expertise['getAliciType'] == 'bireysel') {{ !empty($expertise['getAlici']) && strlen($expertise['getAlici']) > 3 ? mb_substr($expertise['getAlici'], 0, 2, "UTF-8") . '*****' . mb_substr($expertise['getAlici'], -1, null, "UTF-8") : '' }} @endif
            </td>
            <td>Adı Soyadı</td>
            <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">@if($expertise['getSaticiType'] == 'bireysel') {{ $expertise['getSatici'] }} @endif</td>
            <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">@if($expertise['getSaticiType'] == 'bireysel') {{ !empty($expertise['getSatici']) && strlen($expertise['getSatici']) > 3 ? mb_substr($expertise['getSatici'], 0, 2, "UTF-8") . '*****' . mb_substr($expertise['getSatici'], -1, null, "UTF-8") : '' }} @endif</td>
        </tr>
        <tr>
            <td>Firma Unvanı</td>
            <td style="font-size: .3rem" class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">@if($expertise['getAliciType'] == 'kurumsal') {{ $expertise['getAlici'] }} @endif</td>
            <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">@if($expertise['getAliciType'] == 'kurumsal') {{ !empty($expertise['getAlici']) && strlen($expertise['getAlici']) > 3 ? mb_substr($expertise['getAlici'], 0, 2, "UTF-8") . '*****' . mb_substr($expertise['getAlici'], -1, null, "UTF-8") : '' }} @endif</td>
            <td>Firma Unvanı</td>
            <td style="font-size: .3rem" class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">@if($expertise['getSaticiType'] == 'kurumsal') {{ $expertise['getSatici'] }} @endif</td>
            <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">@if($expertise['getSaticiType'] == 'kurumsal') {{ !empty($expertise['getSatici']) && strlen($expertise['getSatici']) > 3 ? mb_substr($expertise['getSatici'], 0, 2, "UTF-8") . '*****' . mb_substr($expertise['getSatici'], -1, null, "UTF-8") : '' }} @endif</td>
        </tr>
        <tr>
            <td>@if($expertise['getAliciType'] == 'kurumsal' && strlen($expertise['getAliciVergiNo']) == 11) ESBİS - TCKN @elseif($expertise['getAliciType'] == 'kurumsal' && strlen($expertise['getAliciVergiNo']) == 10) MERSİS NO - YETKİ BELGE NO @else TCKN @endif</td>
            <td>
                @if($expertise['getAliciType'] == 'kurumsal' && strlen($expertise['getAliciVergiNo']) == 11)
                    {{ $expertise['getAliciEsbis'] }} - {{ !empty($expertise['getAliciTc']) && strlen($expertise['getAliciTc']) > 3 ? substr($expertise['getAliciTc'], 0, 2) . '*****' . substr($expertise['getAliciTc'], -1) : '' }}
                @elseif($expertise['getAliciType'] == 'kurumsal' && strlen($expertise['getAliciVergiNo']) == 10)
                    {{ $expertise['getAliciMersis'] }} - {{ $expertise['getAliciYetkiBelgeNo'] }}
                @else
                    {{ !empty($expertise['getAliciTc']) && strlen($expertise['getAliciTc']) > 3 ? substr($expertise['getAliciTc'], 0, 2) . '*****' . substr($expertise['getAliciTc'], -1) : '' }}
                @endif
            </td>
            <td>@if($expertise['getSaticiType'] == 'kurumsal' && strlen($expertise['getSaticiVergiNo']) == 11) ESBİS - TCKN @elseif($expertise['getSaticiType'] == 'kurumsal' && strlen($expertise['getSaticiVergiNo']) == 10) MERSİS NO - YETKİ BELGE NO @else TCKN @endif</td>
            <td>
                @if($expertise['getSaticiType'] == 'kurumsal' && strlen($expertise['getSaticiVergiNo']) == 11)
                    {{ $expertise['getSaticiEsbis'] }} - {{ !empty($expertise['getSaticiTc']) && strlen($expertise['getSaticiTc']) > 3 ? substr($expertise['getSaticiTc'], 0, 2) . '*****' . substr($expertise['getSaticiTc'], -1) : '' }}
                @elseif($expertise['getSaticiType'] == 'kurumsal' && strlen($expertise['getSaticiVergiNo']) == 10)
                    {{ $expertise['getSaticiMersis'] }} - {{ $expertise['getSaticiYetkiBelgeNo'] }}
                @else
                    {{ !empty($expertise['getSaticiTc']) && strlen($expertise['getSaticiTc']) > 3 ? substr($expertise['getSaticiTc'], 0, 2) . '*****' . substr($expertise['getSaticiTc'], -1) : '' }}
                @endif
            </td>
        </tr>
        <tr>
            <td>Adres</td>
            <td style="font-size: .5rem" class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                {{ $expertise['fullAddressAlici'] }}
            </td>
            <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                {{ !empty($expertise['fullAddressAlici']) ? substr($expertise['fullAddressAlici'], 0, 2) . '*****' . substr($expertise['fullAddressAlici'], -1) : '' }}
            </td>
            <td>Adres</td>
            <td style="font-size: .5rem" class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                {{ $expertise['fullAddressSatici'] }}
            </td>
            <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                {{ !empty($expertise['fullAddressSatici']) ? substr($expertise['fullAddressSatici'], 0, 2) . '*****' . substr($expertise['fullAddressSatici'], -1) : '' }}
            </td>
        </tr>
        <tr>
            <td>Telefon</td>
            <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['getAliciTel'] }}</td>
            <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['getAliciTel'])  && strlen($expertise['getAliciTel']) > 3 ? substr($expertise['getAliciTel'], 0, 2) . '*****' . substr($expertise['getAliciTel'], -1) : '' }}</td>
            <td>Telefon</td>
            <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['getSaticiTel'] }}</td>
            <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                <?php
                $saticiTel = $expertise['getSaticiTel'] ?? '';
                $telLength = strlen($saticiTel);
                if ($telLength > 3) {
                    $maskedTel = substr($saticiTel, 0, 2) . '*****' . substr($saticiTel, -1);
                } else {
                    $maskedTel = $saticiTel; // Telefon numarası 3 karakterden küçükse maskeleme yapmadan kullan
                }
                ?>
                {{$maskedTel}}
            </td>
        </tr>
        <tr>
            <td colspan="2"><b>Talep Edilen Ekspertiz Paketi</b></td>
            <td colspan="2">
                <b>
                @foreach($expertise['getStocks'] as $expertiseStockIndex => $expertiseStock)
                    {{ $expertiseStock['ad'] }} @if($expertiseStockIndex > 0) , @endif
                    @if(!empty($expertiseStock['campaign_name'])) {{ '/' . $expertiseStock['campaign_name'] ?? '' }} @endif
                @endforeach
                / {{ $expertise['payment_type'] }}
                </b>
            </td>
        </tr>
        <tr>
            <td colspan="4"><b>Tarafların sorumlulukları ve bilgilendirme</b></td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 1) @endif Umran Bağımsız Araç Ekspertiz Merkezleri, aracın görünen kusurları ve o anki durumu ile ilgili bilgilendirme yapar. Satıcı ve üçüncü şahıslar tarafından gizlenmiş kusurların tespit edilememesinden ve sonrasında ortaya çıkacak problemlerden dolayı Umran Bağımsız Araç Ekspertiz Merkezleri sorumlu tutulamaz.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 2) @endif Servislerimizde ekspertiz kontrollerinde aracın hiçbir yerinde sökme takma işlemi yapılmamaktadır. Boyasız göçük düzeltme tespitleri yapılamamaktadır. Durum ve arıza tespiti için özel test gerektiren parçaların kontrolü yapılmamaktadır (Motor, Motor Bloğu, Kompresyon, sızdırmazlık vb.).
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 3) @endif Bayilerimizde Kilometre orijinalliği tespitleri yapılmamaktadır.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 4) @endif Servislerimizde Airbag (hava yastığı) kontrollerinde herhangi bir sökme takma işlemi yapılmamaktadır. Kontroller cihazla yapılmaktadır.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 5) @endif Yalnızca sökme takma neticesinde anlaşılabilecek arızaların tespit edilememesinden dolayı şirketimizin herhangi bir sorumluluğu bulunmamaktadır.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 6) @endif Aracın çalıntı olması ve ya yasal olmayan şase numara aktarımı işlemleri kontrol edilmemekte olup Umran Bağımsız Araç Ekspertiz Merkezleri'ni bağlayıcı hukuki sorumluluğu yoktur.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 7) @endif Plastik parçalar, Far, Stop Lambaları, Sis Lambalarındaki değişim ve Cam değişimleri, Silecek lastikleri kontrol edilmemektedir. Cam, ayna ve koltuk ısıtıcıları ({{ \Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-12 13:00:00' ? 'rezistansları' : 'rezinstansları' }}) kontrol edilmemektedir. Koltuk hafızası ve konfor elektriği kontrol edilmemektedir, fitil sızdırmazlıkları, kapı kilitleri, araç kumandaları ve anahtar, opsiyonel özellikler (Araç Paketleri), araç orijinalliğine uymayan yazılım ve parçalar (Modifiye Araçlar), tekerlek uygunluğu ve orijinalliği kontrol edilememektedir.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 8) @endif Araç karoseri üzerindeki kaplamalar, şeffaf folyo, reklam, araç giydirme vb. bulunan araçlarda boya ile ilgili kontroller yapılamamaktadır.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 9) @endif Ekspertizimizle ilgili problemlerimizde, araca herhangi bir müdahale (Tamirat) yaptırmadan aracı görmemiz gerekir. Aksi takdirde dışarıda yaptırdığınız müdahaleler Umran Bağımsız Araç Ekspertiz Merkezleri sorumluluk alanını kapsamaz.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 10) @endif Kaporta Boya değerlendirmesinin itiraz süresi 72 saat, Mekanik ile ilgili raporumuzun itiraz süresi 24 saattir.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 11) @endif Umran Bağımsız Araç Ekspertiz Merkezleri verilen ekspertiz raporunda yanlışlık yapılması halinde, Karayolları Motorlu Araçlar Zorunlu Mali Sorumluluk Sigortası Genel Şartlarının Ek-1’inde yer alan “Değer kaybı hesaplaması” hükümleri çerçevesinde müşteriye değer kaybı ödemesi yapmayı kabul ve taahhüt eder. Hem motor-mekanik hem kaporta-boya ekspertiz raporu için herhalükarda gerekli durumlarda yapılması taahhüt edilen değer kaybı ödemesi ve/veya parça onarım bedeli ödemesi aracın piyasa ve kasko değerine bakılmaksızın azami 100.000 TL'ye kadar olan tutarlar ile sınırlıdır.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 12) @endif Müşteri İşbu iş emri ile almış olduğu ekspertiz raporunun Umran Bağımsız Araç Ekspertiz Merkezlerine ait {{ \Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-12 13:00:00' ? 'web' : 'Web' }} sitesinde yayınlanmasını ve yine web sitesi üzerinden bir başkasına satışını kabul etmiş sayılır.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 13) @endif Gerekli görülmesi halinde araçlara yol testi yapılmaktadır. Cihaz ile Testlerin yapılamaması durumunda yol testi yapılabilmektedir.
            </td>
        </tr>
        <tr>
            <td colspan="2">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 14) @endif Umran Plus Otomotiv A.Ş. ve program ortaklarının sms ve e-mail yoluyla kampanya, ürün ve hizmetleri hakkında bilgilendirme yapmasına izin veriyorum.
            </td>
            <td>
                Evet
            </td>
            <td>
                Hayır
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 15) @endif Servislerimizde araçların periyodik bakım tespiti yapılmamaktadır. Bakımların zamanında yapılmamasından kaynaklı, ekspertiz işlemi sırasında ve sonrasında ortaya çıkabilecek problemlerden dolayı Umran Bağımsız Araç Ekspertiz Merkezleri sorumlu tutulamaz.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 16) @endif CO2 Kaçak Testiyle ilgili hizmet sonuçları test sıvısı üzerinden yorumlanmaktadır. İçeriğiyle ilgili olarak Umran Bağımsız Araç Ekspertiz Merkezlerinin sorumluluğu bulunmadığı gibi herhangi bir garanti yükümlülüğü taahhüdü de bulunmamaktadır.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 17) @endif Alıcı/satıcı beyanına göre ekspertiz talebi yapılan araç bilgilerine özel üretilmiş işbu ekspertiz raporunun tüm hukuki ve cezai sorumluluğu BAYİ'ye aittir. Umran Plus Otomotiv San. ve Tic. A.Ş.'nin bu hususta herhangi bir hukuki ve cezai bir sorumluluğu bulunmamaktadır.
            </td>
        </tr>
        <tr>
            <td colspan="4">
                @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') 18) @endif Umran Bağımsız Araç Ekspertiz Merkezleri; mikron boya işlemi uygulanmış araç parçaları için yaptığı kaporta-boya tespitleri hakkında herhangi bir garanti sağlamamaktadır. Bu hususta ortaya çıkabilecek durumlardan dolayı Umran Bağımsız Araç Ekspertiz Merkezleri’nin herhangi bir sorumluluğu ve taahhütü bulunmamaktadır.
            </td>
        </tr>
        <tr class="text-center">
            <td class="imza" style="width: 33%">
                Umran Bağımsız Araç Ekspertiz <br> Merkezleri Bayisi
                <br><br>
                <b>{{!empty($expertise['getBranch']) ? $expertise['getBranch'] : ''}}</b>
            </td>
            @include('pages.expertise.rapor.customer_buyer')
            @include('pages.expertise.rapor.customer_seller')
            <td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
                @if(isset($expertise['creator_expert']['name']))
                    {{ $expertise['creator_expert']['role_name'] }}
                    <br><br>
                    <b>{{ $expertise['creator_expert']['name'] }}</b>
                @endif
            </td>
            <td class="imza filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
                @if(isset($expertise['creator_expert']['name']))
                    {{ $expertise['creator_expert']['role_name'] }}
                    <br><br>
                    <b>{{ $expertise['creator_expert']['name'] }}</b>
                @endif
            </td>
        </tr>
        </tbody>
    </table>
    <div class="d-flex justify-content-between" style="font-size: .7rem">
        <span><span class="text-danger">KBR</span> {{ $expertise['bayiAdres'] }}</span>
        <span>{{ $expertise['bayiVergiDairesi'] }} VERGİ DAİRESİ {{ $expertise['bayiVergiNo'] }} / MERSİS : {{ $expertise['bayiMersis'] }}</span>
    </div>
</div>
<div class="page-break"></div>
@if($expertise['only_co_kacak'] == 2 || $expertise['is_old'])
    @if($expertise['hasBodywork'])
        @include('pages.expertise.rapor.bodywork')
        <div class="page-break"></div>
    @endif
    @if($expertise['hasSubControls'])
        @include('pages.expertise.rapor.sub_controls_and_engine')
        <div class="page-break"></div>
    @endif
    @if($expertise['hasBrake'])
        @include('pages.expertise.rapor.side_slipping')
        <div class="page-break"></div>
        @include('pages.expertise.rapor.brake')
        <div class="page-break"></div>
    @endif
    @if($expertise['hasSubControls'])
        @include('pages.expertise.rapor.suspension')
        <div class="page-break"></div>
    @endif
@endif
@if($expertise['co2Sonuc'] != 'Yok' || $expertise['only_co_kacak'] == 1)
    @include('pages.expertise.rapor.co2')
@endif
