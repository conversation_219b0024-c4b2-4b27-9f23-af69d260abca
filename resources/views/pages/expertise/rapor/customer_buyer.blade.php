<td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
    MÜŞTERİ (ALICI)
    <br><br>
    @if($expertise['getAliciType'] == 'kurumsal' && strlen($expertise['getAliciVergiNo']) == 11)
        (TCKN / ESBIS)<br><br>
        {{ !empty($expertise['getAliciTc']) && strlen($expertise['getAliciTc']) > 3 ? substr($expertise['getAliciTc'], 0, 2) . '*****' . substr($expertise['getAliciTc'], -1) : '' }}
        / {{ $expertise['getAliciEsbis'] }}
    @elseif($expertise['getAliciType'] == 'kurumsal' && strlen($expertise['getAliciVergiNo']) == 10)
        (VKN / MERSIS)<br><br>
        {{ $expertise['getAliciVergiNo'] }} / {{ $expertise['getAliciMersis'] }}
        <br><br>Yetki Belge No: {{ $expertise['getAliciYetkiBelgeNo'] }}
    @else
        (TCKN)<br><br>
        {{ !empty($expertise['getAliciTc']) && strlen($expertise['getAliciTc']) > 3 ? substr($expertise['getAliciTc'], 0, 2) . '*****' . substr($expertise['getAliciTc'], -1) : '' }}
    @endif
    <br><br>
    <b @if(strlen($expertise['getAlici']) > 50 && \Carbon\Carbon::make($expertise['created_at'])->greaterThan(\Carbon\Carbon::parse('2024-10-21 10:12:00'))) style="font-size: .4rem" @endif>
        {{ $expertise['getAlici'] }}
    </b>
</td>
<td class="filtreli imza @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
    MÜŞTERİ (ALICI)
    <br><br>
    @if($expertise['getAliciType'] == 'kurumsal' && strlen($expertise['getAliciVergiNo']) == 11)
        (TCKN / ESBIS)<br><br>
        {{ !empty($expertise['getAliciTc']) && strlen($expertise['getAliciTc']) > 3 ? substr($expertise['getAliciTc'], 0, 2) . '*****' . substr($expertise['getAliciTc'], -1) : '' }}
        / {{ $expertise['getAliciEsbis'] }}
    @elseif($expertise['getAliciType'] == 'kurumsal' && strlen($expertise['getAliciVergiNo']) == 10)
        (VKN / MERSIS)<br><br>
        {{ $expertise['getAliciVergiNo'] }} / {{ $expertise['getAliciMersis'] }}
        <br><br>Yetki Belge No: {{ $expertise['getAliciYetkiBelgeNo'] }}
    @else
        (TCKN)<br><br>
        {{ !empty($expertise['getAliciTc']) && strlen($expertise['getAliciTc']) > 3 ? substr($expertise['getAliciTc'], 0, 2) . '*****' . substr($expertise['getAliciTc'], -1) : '' }}
    @endif
    <br><br>
    <b @if(strlen($expertise['getAlici']) > 50 && \Carbon\Carbon::make($expertise['created_at'])->greaterThan(\Carbon\Carbon::parse('2024-10-21 10:12:00'))) style="font-size: .4rem" @endif>
        {{ !empty($expertise['getAlici']) && strlen($expertise['getAlici']) > 3 ? mb_substr($expertise['getAlici'], 0, 2, "UTF-8") . '*****' . mb_substr($expertise['getAlici'], -1, null, "UTF-8") : '' }}
    </b>
</td>
