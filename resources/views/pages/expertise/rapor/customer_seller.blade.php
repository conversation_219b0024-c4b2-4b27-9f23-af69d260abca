<td class="imza filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">
    MÜŞTERİ (SATICI)
    <br><br>
    @if($expertise['getSaticiType'] == 'kurumsal' && strlen($expertise['getSaticiVergiNo']) == 11)
        (TCKN / ESBIS)<br><br>
        {{ !empty($expertise['getSaticiTc']) && strlen($expertise['getSaticiTc']) > 3 ? substr($expertise['getSaticiTc'], 0, 2) . '*****' . substr($expertise['getSaticiTc'], -1) : '' }}
        / {{ $expertise['getSaticiEsbis'] }}
    @elseif($expertise['getSaticiType'] == 'kurumsal' && strlen($expertise['getSaticiVergiNo']) == 10)
        (VKN / MERSIS)<br><br>
        {{ $expertise['getSaticiVergiNo'] }} / {{ $expertise['getSaticiMersis'] }}
        <br><br>Yetki <PERSON>ge No: {{ $expertise['getSaticiYetkiBelgeNo'] }}
    @else
        (TCKN)<br><br>
        {{ !empty($expertise['getSaticiTc']) && strlen($expertise['getSaticiTc']) > 3 ? substr($expertise['getSaticiTc'], 0, 2) . '*****' . substr($expertise['getSaticiTc'], -1) : '' }}
    @endif
    <br><br>
    <b @if(strlen($expertise['getSatici']) > 50 && \Carbon\Carbon::make($expertise['created_at'])->greaterThan(\Carbon\Carbon::parse('2024-10-21 10:12:00'))) style="font-size: .4rem" @endif>
        {{ $expertise['getSatici'] }}
    </b>
</td>
<td class="imza filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">
    MÜŞTERİ (SATICI)
    <br><br>
    @if($expertise['getSaticiType'] == 'kurumsal' && strlen($expertise['getSaticiVergiNo']) == 11)
        (TCKN / ESBIS)<br><br>
        {{ !empty($expertise['getSaticiTc']) && strlen($expertise['getSaticiTc']) > 3 ? substr($expertise['getSaticiTc'], 0, 2) . '*****' . substr($expertise['getSaticiTc'], -1) : '' }}
        / {{ $expertise['getSaticiEsbis'] }}
    @elseif($expertise['getSaticiType'] == 'kurumsal' && strlen($expertise['getSaticiVergiNo']) == 10)
        (VKN / MERSIS)<br><br>
        {{ $expertise['getSaticiVergiNo'] }} / {{ $expertise['getSaticiMersis'] }}
        <br><br>Yetki Belge No: {{ $expertise['getSaticiYetkiBelgeNo'] }}
    @else
        (TCKN)<br><br>
        {{ !empty($expertise['getSaticiTc']) && strlen($expertise['getSaticiTc']) > 3 ? substr($expertise['getSaticiTc'], 0, 2) . '*****' . substr($expertise['getSaticiTc'], -1) : '' }}
    @endif
    <br><br>
    <b @if(strlen($expertise['getSatici']) > 50 && \Carbon\Carbon::make($expertise['created_at'])->greaterThan(\Carbon\Carbon::parse('2024-10-21 10:12:00'))) style="font-size: .4rem" @endif>
        {{ !empty($expertise['getSatici']) && strlen($expertise['getSatici']) > 3 ? mb_substr($expertise['getSatici'], 0, 2, "UTF-8") . '*****' . mb_substr($expertise['getSatici'], -1, null, "UTF-8") : '' }}
    </b>
</td>
