<div class="row motor-mekanik rapor dikey">
    <div class="table-responsive">
        <table class="table">
            <tbody>
                <tr>
                    <td colspan="2" style="width: 33%;text-align: center">
                        <img src="/assets/isemri_logo.png" style="width: 65%">
                        <h6 style="color: darkorange">0850 354 54 17 / 0216 302 54 17</h6>
                        <b style="font-size: .5rem">Rapor Basım Tarihi / Saati / Sırası:</b>
                        <h8 style="color: #333335;"> {{ now()->format('d.m.Y') }} / {{ now()->format('H:i') }} / {{ $expertise['download_count'] }}</h8>
                    </td>
                    <td colspan="3" style="width: 33%;font-size: .813rem;" class="text-center">
                        <b>ARAÇ EKSPERTİZ RAPORU</b><br>
                        Bağımsız Oto Ekspertiz Merkezi<br> www.umranoto.com<br><br>
                        <b>{{ $expertise['getBranch'] }}</b>
                        @if(\Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-11') <br><br> <b>MEKANİK & ELEKTRİK</b> @endif
                    </td>
                    <td colspan="1" class="text-center" style="width: 20%">
                        <img src="/assets/images/hyb.png" style="width: 35%"><br>
                        <b>TSE-HYB Belge No:</b>
                        <h6 class="text-danger">{{ $expertise['tse_kodu'] }}</h6>
                    </td>
                    <td colspan="" style="width: 30%;text-align:center;padding-right: 0;">
                        {!! $expertise['qr'] !!}
                        <h5 class="company-phone-right">444 54 17</h5>
                    </td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Bayi:</td>
                    <td colspan="2"><b>{{ $expertise['getBranch'] }}</b></td>
                    <td colspan="1">Marka/Model:</td>
                    <td colspan="1">{{ $expertise['getMarka'] ? $expertise['getMarka'] . ' ' . $expertise['getModel'] : '' }}</td>
                    <td colspan="1">@if($expertise['km_type'] == 1) KM @else Mil @endif:</td>
                    <td colspan="1"><b>{{ number_format($expertise['km'] ?? 0,0,'.','.') }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Belge No:</td>
                    <td colspan="2"><b class="text-danger">{{ $expertise['belge_no'] }}</b></td>
                    <td colspan="1">Model Yılı:</td>
                    <td colspan="1"><b>{{ $expertise['model_yili'] }}</b></td>
                    <td colspan="1">Motor Hacmi:</td>
                    <td colspan="1"><b>
                            @if(is_numeric($expertise['motor_hacmi']))
                            {{ number_format($expertise['motor_hacmi'] ?? 0,0,'.','.') }}
                            @else
                            {{$expertise['motor_hacmi']}}
                            @endif
                        </b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Giriş Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['created_at'] }}</b></td>
                    <td colspan="1">Şase Numarası:</td>
                    <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                            {{ $expertise['sase_no'] }}
                        </b>
                    </td>
                    <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                            {{ !empty($expertise['sase_no'])  && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . '*****' . substr($expertise['sase_no'], -1) : '' }}
                        </b>
                    </td>
                    <td colspan="1">Yakıt Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getFuel'] }}</b></td>
                </tr>
                <tr class="table-col-6">
                    <td colspan="1">Çıkış Tarihi ve Saati:</td>
                    <td colspan="2"><b>{{ $expertise['cikis_tarihi'] }}</b></td>
                    <td colspan="1">Plaka:</td>
                    <td class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif" colspan="1"><b>
                            {{ $expertise['plaka'] }}
                        </b>
                    </td>
                    <td class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif" colspan="1"><b>
                            {{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . '*****' . substr($expertise['plaka'], -1) : '' }}
                        </b>
                    </td>
                    <td colspan="1">Vites Tipi:</td>
                    <td colspan="1"><b>{{ $expertise['getGear'] }}</b></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="col-md-6 table-responsive">
        <table class="table table-bordered mt-3">
            <thead>
            <tr>
                <th>
                    @if($expertise['getFuel'] == 'Elektrik')
                        ÜST KONTROLLER
                    @else
                        MOTOR BÖLÜMÜ
                    @endif
                </th>
                <th>DURUMU</th>
                <th>AÇIKLAMA</th>
            </tr>
            </thead>
            <tbody>
            @foreach($engines1 as $key => $subControlAndEngine1)
                @if($key == "co2_kacak_testi")
                    @if(empty($expertise['co2_kacak_testi']))
                        {{--
                                    <tr class="rapor-item-tr">
                                    <td>{{ $subControlAndEngine1 }}</td>
                        <td class="text-center" colspan="2"><span style="color: #0a53be">Bu Kontrol Satın Alınmamıştır</span></td>
                        </tr>
                        --}}
                    @else
                        <tr class="rapor-item-tr">
                            <td>{{ $subControlAndEngine1 }}</td>
                            <td class="text-center">
                                {{$expertise['co2']->key ?? ''}}
                            </td>
                            <td> {{$expertise['co2']->note ?? ''}}</td>
                        </tr>
                    @endif
                @else
                    <tr class="rapor-item-tr">
                        <td>{{ $subControlAndEngine1 }}</td>
                        <td class="text-center">
                            @if($expertise['getSubControlsAndEngines'][$key]['answer'] == "turbo_yok" || $expertise['getSubControlsAndEngines'][$key]['answer'] == "deger_giriniz")
                                --
                            @else
                                {{ucfirst($expertise['getSubControlsAndEngines'][$key]['answer'])}}
                            @endif
                        </td>
                        <td>{!! $expertise['getSubControlsAndEngines'][$key]['note'] !!}</td>
                    </tr>
                @endif
            @endforeach
            </tbody>
        </table>
    </div>
    <div class="col-md-6 table-responsive">
        <table class="table table-bordered mt-3">
            <thead>
            <tr>
                <th>İÇ KONTROLLER</th>
                <th>DURUM</th>
                <th>AÇIKLAMA</th>
            </tr>
            </thead>
            <tbody>
            @foreach(__('arrays.internal_controls') as $key => $internalControl)
                <tr class="rapor-item-tr">
                    <td>{{ $internalControl }}</td>
                    <td class="text-center">{!! $expertise['getInternalControls'][$key]['answer'] != 'yok' ? ucfirst($expertise['getInternalControls'][$key]['answer']) : '---' !!}</td>
                    <td>{!! $expertise['getInternalControls'][$key]['note'] !!}</td>
                </tr>
            @endforeach
            </tbody>
        </table>
    </div>
    <div class="col-md-6 table-responsive">
        <table class="table table-bordered">
            <thead>
            <tr>
                <th>ALT KONTROLLER</th>
                <th>DURUM</th>
                <th>AÇIKLAMA</th>
            </tr>
            </thead>
            <tbody>
            @foreach($engines2 as $key => $engine2)
                @if($engine2 != 'Elektrik Sistemi')
                    @if($key == 'fren_sistemi_kontrolu')
                        <tr class="rapor-item-tr">
                            <td>{{__('arrays.components')['sanziman_vites_gecisleri_ve_ses_kontrolu']}}</td>
                            <td class="text-center">
                                @if($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "yok")
                                    ----
                                @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "iyi")
                                    İyi
                                @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "test_yapılmadı")
                                    Test Yapılmadı
                                @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "kötü")
                                    Kötü
                                @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "orta")
                                    Orta
                                @elseif($expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['answer'] == "hafif")
                                    Hafif
                                @endif
                            </td>
                            <td>{{$expertise['getComponents']['sanziman_vites_gecisleri_ve_ses_kontrolu']['note']}}</td>
                        </tr>
                    @endif

                    <tr class="rapor-item-tr">
                        <td>{{ $engine2 }}</td>
                        <td class="text-center">
                            {{ ucfirst($expertise['getSubControlsAndEngines'][$key]['answer']) }}
                        </td>
                        <td>{!! $expertise['getSubControlsAndEngines'][$key]['note'] !!}</td>
                    </tr>
                @else
                    @php
                        $electrical_values[0] = [
                        'name' => $engine2,
                        'answer' => $expertise['getSubControlsAndEngines'][$key]['answer'],
                        'note' => $expertise['getSubControlsAndEngines'][$key]['note'],
                        ];
                    @endphp
                @endif
            @endforeach

            <tr class="rapor-item-tr">
                <td>{{__('arrays.components')['dortceker_kontrolu']}}</td>
                <td class="text-center">
                    @if($expertise['getComponents']['dortceker_kontrolu']['answer'] == "yok")
                        ----
                    @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "iyi")
                        İyi
                    @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "test_yapılmadı")
                        Test Yapılmadı
                    @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "kötü")
                        Kötü
                    @elseif($expertise['getComponents']['dortceker_kontrolu']['answer'] == "orta")
                        Orta
                    @endif
                </td>
                <td>{{$expertise['getComponents']['dortceker_kontrolu']['note']}}</td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="col-md-6 table-responsive">
        <table class="table table-bordered">
            <thead>
            <tr>
                <th class="text-center">LASTİK VE JANTLAR</th>
                <th class="text-center">DURUM</th>
                <th class="text-center">DİŞ(mm)</th>
                <th class="text-center">BASINÇ</th>
            </tr>
            </thead>
            <tbody>
            @foreach(__('arrays.tire_and_rims') as $key => $tire)
                <tr class="rapor-item-tr">
                    <td>{{ $tire }}</td>
                    <td>
                        @if($expertise['getTireAndRims'][$key]['sorunlu_mu'] != 1)
                            Kontrol Edildi
                        @else
                            {{$expertise['getTireAndRims'][$key]['note']}}
                        @endif
                    </td>
                    <td class="text-center">{!! !empty($expertise['getTireAndRims'][$key]['dis']) ? number_format($expertise['getTireAndRims'][$key]['dis'], 1) : ''; !!}</td>
                    <td class="text-center">{!! $expertise['getTireAndRims'][$key]['basinc'] !!}</td>
                </tr>
            @endforeach
            </tbody>
        </table>
        <table class="table table-bordered">
            <thead>
            <tr>
                <th>DİAGNOSTİK CİHAZ KONTROL SONUÇLARI</th>
            </tr>
            </thead>
            <tbody>
            @php
                $td_count = 4;
                if(count($expertise['allNotes'][3]) > $td_count){
                $td_count = count($expertise['allNotes'][3]);
                }

                foreach($expertise['allNotes'][3] as $key => $notes_array){
                $expertise['allNotes'][3][$key] = '[DIAG] '.$notes_array;
                }
            @endphp
            @for ($i = 0; $i < $td_count; $i++)
                <tr>

                    <td @if(empty($expertise['allNotes'][3][$i])) style="padding:.6rem;" @endif>{{!empty($expertise['allNotes'][3][$i]) ? $expertise['allNotes'][3][$i] : ''}}</td>
                </tr>
            @endfor
            </tbody>
        </table>
    </div>
    <div class="col-md-12 table-responsive">
        <table class="table table-bordered">
            <thead>
            <tr>
                <th colspan="4" class="text-center">NOTLAR</th>
            </tr>
            </thead>
            <tbody>

            @php
                $td_count = 10;
                $td_count_2 = (count($expertise['allNotes'][4]) + count($expertise['allNotes'][5])+ count($expertise['allNotes'][6]) + count($expertise['allNotes'][7]) + count($expertise['allNotes'][2])) / 2;

                if($td_count_2 > 10){
                    $td_count = $td_count_2;
                }

                foreach($expertise['allNotes'][4] as $key => $notes_array){
                    $expertise['allNotes'][4][$key] = '[ICK] '.$notes_array;
                }
                foreach($expertise['allNotes'][5] as $key => $notes_array){
                    $expertise['allNotes'][5][$key] = '[LAJ] '.$notes_array;
                }
                foreach($expertise['allNotes'][6] as $key => $notes_array){
                    $expertise['allNotes'][6][$key] = '[KOM] '.$notes_array;
                }
                foreach($expertise['allNotes'][7] as $key => $notes_array){
                    $expertise['allNotes'][7][$key] = '[KOM] '.$notes_array;
                }
                foreach($expertise['allNotes'][2] as $key => $notes_array){
                    $expertise['allNotes'][2][$key] = '[FRN] '.$notes_array;
                }
                $expertise['allNotes'][8] = array();
                foreach(__('arrays.components') as $key => $component){
                    if($key != "dortceker_kontrolu" && $key != 'sanziman_vites_gecisleri_ve_ses_kontrolu' && !empty($expertise['getComponents'][$key]['note'])){
                        $expertise['allNotes'][8][$key] = "[KOM]".$expertise['getComponents'][$key]['note'];
                    }
                }

                if(!empty($electrical_values[0]['note'])){
                    $expertise['allNotes'][8][]="[KOM]".$electrical_values[0]['note'];
                }

                $mergedArray = array_merge(
                    $expertise['allNotes'][2],
                    $expertise['allNotes'][4],
                    $expertise['allNotes'][5],
                    $expertise['allNotes'][6],
                    $expertise['allNotes'][7],
                    $expertise['allNotes'][8],
                );

                // Toplam eleman sayısını bul

                $firstArray = array_slice($mergedArray, 0, ceil($td_count));
                $secondArray = array_splice($mergedArray, ceil($td_count));
            @endphp

            @if(!empty($expertise['co2Note']))
                @foreach($expertise['co2Note'] as $co2)
                    <tr>
                        <td class="col-6 not-item not-var">{{$co2->note}}</td>
                        <td class="not-item not-yok"></td>
                    </tr>
                @endforeach
            @endif
            @for ($i = 0; $i < $td_count; $i++)
                <tr>
                    <td class="col-6 not-item @if(!empty($firstArray[$i])) not-var @else not-yok @endif">{{!empty($firstArray[$i]) ? $firstArray[$i]:''}} </td>
                    <td class="not-item @if(!empty($secondArray[$i])) not-var @else not-yok @endif"> {{!empty($secondArray[$i]) ? $secondArray[$i] : ''}} </td>
                </tr>
            @endfor
            </tbody>
        </table>
    </div>
    <div class="col-12 table-responsive">
        <table class="table table-bordered">
            <tbody>
                <tr>
                    <td style="width: 70%">
                        <p>1* Ekspertiz işlemlerinde triger seti kontrolü yapılmamaktadır.</p>
                        <p>2* Akü ölçümü ekspertiz işlemi anındaki değerlere göre yapılmaktadır. Sonraki kullanımdan kaynaklanan sorunlarda firmamızın herhangi bir garantisi yoktur.</p>
                        <p>3* Kumanda kontrol düğmeleri kontrolü; Far düğmeleri, Sinyal kolu, silecek kolu, elektrikli ayna(ısıtma hariç), cam düğmeleri, bağaj, depo kapağı düğmesi, cam perdeleri, tavan ve sunroof düğmesi kontrollerini kapsamaktadır. Bunların dışında kalan kumanda düğmeleri kontrol edilmemektedir.</p>
                        <p>4* Farlarda yalnızca kırık, çatlak ve yanıp yanmadığının kontrolü yapılmaktadır. Orijinallik, xenon ve su alma testi yapılmamaktadır.</p>
                        @if($expertise['getFuel'] == 'Elektrik' || $expertise['getFuel'] == 'Hybrid ( Benzin - Elektrik )' || $expertise['getFuel'] == 'Hybrid ( Dizel - Elektrik )')
                            <p>5 Batarya sağlığı (SOH) değerleri cihaz ile kontrol edilmektedir. Araç üreticisinin, üniversal test cihazlarında SOH değerini görüntüleme onayı vermediği araçların SOH değeri cihazda görülemediğinden, bu konuda bir tespit yapılamamaktadır. Bu tespit ancak yetkili servislerde yaptırılabilir.</p>
                        @endif
                    </td>
                    <td colspan="1">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>KOMPONENTLER</th>
                                    <th>İYİ</th>
                                    <th>ORTA</th>
                                    <th>KÖTÜ</th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach(__('arrays.components') as $key => $component)
                                @if(!empty($key) && $key != "dortceker_kontrolu" && $key != 'sanziman_vites_gecisleri_ve_ses_kontrolu')
                                <tr>
                                    <td>{{ $key == 'tork_konventoru' && \Carbon\Carbon::parse($expertise['created_at']) >= '2024-12-12 13:00:00' ? 'Tork Konvertörü' : $component }}</td>
                                    @if($expertise['getComponents'][$key]['answer'] == 'test_yapılmadı')
                                    <td colspan="4" style="text-align: center"><span class="test-yapilmadi fw-semibold">Test Yapılmadı</span></td>
                                    @elseif($expertise['getComponents'][$key]['answer'] == "yok")
                                    <td colspan="4" style="text-align: center"><span class="test-yapilmadi fw-semibold">Araçta Bu Özellik Bulunmamaktadır.</span></td>
                                    @else
                                    <td>{!! mb_strtolower($expertise['getComponents'][$key]['answer']) == 'iyi' || $expertise['getComponents'][$key]['answer'] == 'İyi' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! mb_strtolower($expertise['getComponents'][$key]['answer']) == 'orta' || $expertise['getComponents'][$key]['answer'] == 'Orta' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! mb_strtolower($expertise['getComponents'][$key]['answer']) == 'kötü' || $expertise['getComponents'][$key]['answer'] == 'Kötü' ? '<i class="fa fa-check"></i>' : '' !!}</td>


                                    @endif

                                </tr>
                                @endif
                                @endforeach
                                <tr>
                                    <td>{{$electrical_values[0]['name']}} </td>
                                    @if($electrical_values[0]['answer'] == "test_yapilmadi")
                                    <td colspan="5" style="text-align: center"><span class="test-yapilmadi fw-semibold">Test Yapılmadı</span></td>
                                    @elseif($electrical_values[0]['answer'] == "yok")
                                    <td colspan="5" style="text-align: center"><span class="test-yapilmadi fw-semibold">Araçta Bu Özellik Bulunmamaktadır.</span></td>
                                    @endif
                                    <td>{!! $electrical_values[0]['answer'] == 'iyi' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! $electrical_values[0]['answer'] == 'orta' ? '<i class="fa fa-check"></i>' : '' !!}</td>
                                    <td>{!! $electrical_values[0]['answer'] == 'kötü' ? '<i class="fa fa-check"></i>' : '' !!}</td>

                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td class="print_bg" style="background-color: #ff1000;color: #fff;text-align: center;font-weight: bolder" colspan="4">SERVİSLERİMİZDEN ALINAN EKSPERTİZ RAPORLARINA WWW.UMRANOTO.COM ADRESİNDEN ULAŞABİLİRSİNİZ.</td>
                </tr>
                <tr>
                    <td colspan="4">
                        Umran Oto Ekspertiz Merkezleri tüm dünyadaki ekspertiz hizmetleri gibi aracın o anki durumunu analiz eder ve bilgi verir. Ekspertiz yaptığı aracın kesinlikle ileride arıza ve problem çıkarmayacağını garanti edemez. Aracın o anki görülen hataları kontrol edilerek raporlanır. Kaporta boya değerlendirmesine itiraz 72 saat, motor ile ilgili değerlendirmeye itiraz 24 saattir. Bayilerimizde kilometre orijinalliği tespiti yapılmamaktadır. Elektronik arşiv süresi 5 yıldır.
                    </td>
                </tr>
            </tbody>
        </table>
        <table class="table table-bordered">
            <tbody>
            <tr class="text-center">
                <td class="imza">
                    MOTOR MEKANİK TEKNİKERİ
                    @if($expertise['mechanic_experts'])
                        <br><br>
                        @foreach ($expertise['mechanic_experts'] as $mechanicExpert)
                            <b>{{ $mechanicExpert }}</b><br>
                        @endforeach
                    @endif
                </td>
                <td class="imza">
                    SERVİS MÜDÜRÜ
                    @if($expertise['service_manager'])
                        <br><br>
                        <b>{{ $expertise['service_manager'] }}</b>
                    @endif
                </td>
                @include('pages.expertise.rapor.customer_buyer')
                @include('pages.expertise.rapor.customer_seller')
            </tr>
            </tbody>
        </table>
        <div class="d-flex justify-content-between" style="font-size: .7rem">
            <span><span class="text-danger">KBR</span> {{ $expertise['bayiAdres'] }}</span>
            <span>{{ $expertise['bayiVergiDairesi'] }} VERGİ DAİRESİ {{ $expertise['bayiVergiNo'] }} / MERSİS : {{ $expertise['bayiMersis'] }}</span>
        </div>
    </div>
</div>
