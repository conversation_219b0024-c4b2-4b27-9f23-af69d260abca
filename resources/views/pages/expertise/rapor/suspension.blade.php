<div class="row suspansiyon yatay rapor sonuc-sayfa-bg">
    <div class="sonuc-header">
        <div class="sonuc-logo">
            <img src="/assets/isemri_logo.png">
            <h6 style="color: darkorange">0850 354 54 17 / 0216 302 54 17</h6>
            <h6 class="text-danger">www.umranoto.com</h6>
            <b style="font-size: .5rem">Rapor Basım Tarihi / Saati / Sırası:</b>
            <h8 style="color: #333335;"> {{ now()->format('d.m.Y') }} / {{ now()->format('H:i') }} / {{ $expertise['download_count'] }}</h8>
        </div>
        <div class="sonuc-info">
            <div class="sonuc-first-tab">
                <div class="sonuc-first-tab-item">
                    <b>Plaka</b><br>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['plaka'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . '*****' . substr($expertise['plaka'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Model Yılı</b><br>
                    {{ $expertise['model_yili'] }}
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Şase No</b><br>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['sase_no'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['sase_no'])  && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . '*****' . substr($expertise['sase_no'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Yetkili Hizmet Yeri</b><br>
                    {{ $expertise['getBranch'] }}
                </div>
            </div>
            <div class="sonuc-second-tab">
                <span style="color: #fff">Süspansiyon Testi Sonuç Sayfası</span>
                <span>UMRAN Bağımsız Araç Ekspertiz Merkezleri</span>
            </div>
            <div class="sonuc-third-tab">
                <div class="sonuc-first-tab-item">
                    <b>Marka:</b>
                    {{ $expertise['getMarka'] }} {{ $expertise['getModel'] }}
                </div>
                <div class="sonuc-first-tab-item">
                    <b>@if($expertise['km_type'] == 1) KM @else Mil @endif:</b>
                    {{ number_format($expertise['km'] ?? 0,0,'.','.') }}
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Belge No:</b>
                    <span style="font-size: 1.4rem" class="text-danger">{{ $expertise['belge_no'] }}</span>
                </div>
            </div>
            <div class="sonuc-third-tab">
                <div class="sonuc-first-tab-item">
                    <b>Müşteri:</b>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['getAlici'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['getAlici']) && strlen($expertise['getAlici']) > 3 ? substr($expertise['getAlici'], 0, 2) . '*****' . substr($expertise['getAlici'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <b>GSM:</b>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['getAliciTel'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['getAliciTel'])  && strlen($expertise['getAliciTel']) > 3 ? substr($expertise['getAliciTel'], 0, 2) . '*****' . substr($expertise['getAliciTel'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <div style="background-color: lightgoldenrodyellow;padding: 3px 7px"><i class="fa fa-clock"></i> {{ $expertise['created_at'] }}</div>
                </div>
            </div>
        </div>
    </div>
    <div class="row" style="background: #f8f8f8">
        <div class="offset-3 col-6">
            <div class="row">
                <div class="col-3" style="display: flex;flex-direction: column;justify-content: space-between;">
                    <div>
                        <p>Min. Tutunma</p>
                        <div class="tutunma-box @if((int)$expertise['suspansiyon_min_tutunma_a'] <= 40) text-danger @else text-success-rapor @endif">%{{ $expertise['suspansiyon_min_tutunma_a'] }}</div>
                        <p><b>( Hz)</b></p>
                    </div>
                    <div>
                        <p>Min. Tutunma</p>
                        <div class="tutunma-box @if((int)$expertise['suspansiyon_min_tutunma_b'] <= 40) text-danger @else text-success-rapor @endif">%{{ $expertise['suspansiyon_min_tutunma_b'] }}</div>
                        <p><b>( Hz)</b></p>
                    </div>
                </div>
                <div class="col-6">
                    <img src="/assets/arac_alti.png" style="width: 75%" class="img-fluid">
                </div>
                <div class="col-3" style="display: flex;flex-direction: column;justify-content: space-between;">
                    <div>
                        <p>Min. Tutunma</p>
                        <div class="tutunma-box @if((int)$expertise['suspansiyon_min_tutunma_c'] <= 40) text-danger @else text-success-rapor @endif">%{{ $expertise['suspansiyon_min_tutunma_c'] }}</div>
                        <p><b>( Hz)</b></p>
                    </div>
                    <div>
                        <p>Min. Tutunma</p>
                        <div class="tutunma-box @if((int)$expertise['suspansiyon_min_tutunma_d'] <= 40) text-danger @else text-success-rapor @endif">%{{ $expertise['suspansiyon_min_tutunma_d'] }}</div>
                        <p><b>( Hz)</b></p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 text-end">
            <div class="color-bar" style="float: right">
                <div class="text">%0</div>
                <div class="line red"></div>
                <div class="text">%40</div>
                <div class="line green"></div>
                <div class="text">%100</div>
            </div>
        </div>
    </div>
    <div class="d-flex justify-content-between" style="font-size: .7rem">
        <span><span class="text-danger">KBR</span> {{ $expertise['bayiAdres'] }}</span>
        <span>{{ $expertise['bayiVergiDairesi'] }} VERGİ DAİRESİ {{ $expertise['bayiVergiNo'] }} / MERSİS : {{ $expertise['bayiMersis'] }}</span>
    </div>
</div>