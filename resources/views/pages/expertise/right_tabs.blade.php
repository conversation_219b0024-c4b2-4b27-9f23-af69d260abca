<div class="card" style="position: sticky; top: 3rem; z-index: 2;">
    <div class="card-body">
        <div class="form-group">
            <label>Plaka</label>
            <input class="form-control-sm form-control" value="{{ $expertise['plaka'] }}" disabled name="plaka">
        </div>
        <div class="form-group">
            <label>Şase No</label>
            <input class="form-control-sm form-control" value="{{ $expertise['sase_no'] }}" disabled name="sase_no">
        </div>
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('car', $expertise['unlock_tabs'])))
        <a href="{{ route('expertise_details',['type'=>'car','expertise_id'=>$expertise['uuid']]) }}" class="btn btn-block arac-button mt-2 @if($expertise['islemler']['arac']) btn-success @else btn-danger @endif">Araç</a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('brake', $expertise['unlock_tabs'])))
        <a href="{{ route('expertise_details',['type'=>'brake','expertise_id'=>$expertise['uuid']]) }}" class="btn btn-block fren-button mt-2 @if($expertise['islemler']['fren']) btn-success @else btn-danger @endif">Fren</a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('bodywork', $expertise['unlock_tabs'])))
        <a href="{{ route('expertise_details',['type'=>'bodywork','expertise_id'=>$expertise['uuid']]) }}" class="btn btn-block kaporta-button mt-2 @if($expertise['islemler']['kaporta']) btn-success @else btn-danger @endif">Kaporta</a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('diagnostic', $expertise['unlock_tabs'])))
        <a href="{{ route('expertise_details',['type'=>'diagnostic','expertise_id'=>$expertise['uuid']]) }}" class="btn btn-block diagnostic-button mt-2 @if($expertise['islemler']['diagnostic']) btn-success @else btn-danger @endif">Diagnostic</a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('internal_control', $expertise['unlock_tabs'])))
        <a href="{{ route('expertise_details',['type'=>'internal_control','expertise_id'=>$expertise['uuid']]) }}" class="btn btn-block ic-kontrol-button mt-2 @if($expertise['islemler']['ic']) btn-success @else btn-danger @endif">İç Kontroller</a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('tire_and_rim', $expertise['unlock_tabs'])))
        <a href="{{ route('expertise_details',['type'=>'tire_and_rim','expertise_id'=>$expertise['uuid']]) }}" class="btn btn-block lastik-button mt-2 @if($expertise['islemler']['lastik']) btn-success @else btn-danger @endif">Lastik ve Jant</a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('sub_control_and_engine', $expertise['unlock_tabs'])))
        <a href="{{ route('expertise_details',['type'=>'sub_controls_and_engine','expertise_id'=>$expertise['uuid']]) }}" class="btn btn-block alt-kontrol-button mt-2 @if($expertise['islemler']['motor']) btn-success @else btn-danger @endif">Alt Kontroller-Motor</a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('component', $expertise['unlock_tabs'])))
        <a href="{{ route('expertise_details',['type'=>'component','expertise_id'=>$expertise['uuid']]) }}" class="btn btn-block komponent-button mt-2 @if($expertise['islemler']['komponent']) btn-success @else btn-danger @endif">Komponentler</a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('co2', $expertise['unlock_tabs'])))
        <a href="{{ route('expertise_details', ['type'=>'co2', 'expertise_id' => $expertise['uuid']]) }}" class="btn btn-block co2-button mt-2 @if($expertise['islemler']['co2']) btn-success @else btn-danger @endif">{{__('arrays.co2_kacak_testi')}}</a>
        @endif
    </div>
</div>