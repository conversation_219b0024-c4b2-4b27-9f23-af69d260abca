@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        @media screen and (max-width: 500px) {
            .note-item{
                min-width: 300px;
            }
        }
    </style>
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title', $pageTitle)
@section('content')
    @include('pages.expertise.tabs')
    <form id="mainForm" method="post" action="{{ route('updateExpertise') }}" enctype="multipart/form-data">@csrf
        <input type="hidden" name="expertise_id" value="{{ $_GET['expertise_id'] }}">
        <input type="hidden" name="type" value="{{ $_GET['type'] }}">
        <div class="row">
            <div class="col-md-10">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item"  style="position: sticky;top: 1rem;z-index: 2">
                        <h2 class="accordion-header" id="belgeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#belge" aria-expanded="true"
                                    aria-controls="belge">
                                Belge
                            </button>
                        </h2>
                        <div id="belge" class="accordion-collapse collapse show" aria-labelledby="belgeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-1">
                                        <label class="form-label">Personel<span class="text-danger">*</span></label>
                                        <input type="text" class="form-control form-control-sm" name="alt_motor_kontrol_user" @if($expertise['alt_motor_kontrol_user'] > 0) disabled value="{{ $expertise['alt_motor_kontrol_user'] }}" @else  @endif placeholder="Personel ID">
                                    </div>
                                    <div class="col-md-1">
                                        <label>Plaka</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['plaka'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>Şase</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['sase_no'] }}">
                                    </div>
{{--                                    <div class="col-md-2">--}}
{{--                                        <label>Hizmet</label>--}}
{{--                                        <input disabled class="form-control form-control-sm" value="@foreach($expertise['getStocks'] as $expertiseStock) {{ $expertiseStock['ad'] }}, @endforeach">--}}
{{--                                    </div>--}}
                                    <div class="col-md-2">
                                        <label>Tarih</label>
                                        <input type="datetime-local" class="form-control form-control-sm" readonly name="date" value="{{ $expertise['date'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>No</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['belge_no'] }}">
                                    </div>
                                    <div class="col-md-1">
                                        <label>KM</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['km'] }}">
                                    </div>
                                    <div class="col-md-1">
                                        <label>Model Yılı</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['car_model_year'] }}">
                                    </div>
                                    <div class="col-md-2">
                                        <label>Model</label>
                                        <input disabled class="form-control form-control-sm" value="{{ $expertise['car_model'] }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="kaportaSonuclariHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#kaportaSonuclari" aria-expanded="true"
                                    aria-controls="kaportaSonuclari">
                                Alt Kontroller ve Motor
                            </button>
                        </h2>
                        <div id="kaportaSonuclari" class="accordion-collapse collapse show" aria-labelledby="kaportaSonuclariHeading">
                            <div class="accordion-body" style="overflow: auto;max-width: 100%;max-height: 400px;">
                                <table class="table table-hover">
                                    <thead style="position: sticky;top: -13px;z-index:1;">
                                    <tr>
                                        <th>Başlık</th>
                                        <th>Durum</th>
                                        <th>Açıklama</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($expertise['getSubControlsAndEngines'] as $key => $bodywork)
                                        @if($key != "co2_kacak_testi")
                                            <tr>
                                                <td>{{ $bodywork['title'] }}</td>
                                                <td>
                                                    <select required="" name="{{ $key }}[]" @if($expertise['is_closed']) disabled @endif data-key="{{ $key }}" data-title="{{ $bodywork['title'] }}" class="form-control secenek" onchange="optionChangeNot('{{$key}}')">
                                                        <option value="">Lütfen Seçim Yapınız</option>
                                                        @foreach(__('arrays.sub_controls_and_engines_answer')[$key] as $sub_controls_anwer)
                                                            <option @if($bodywork['answer'] == $sub_controls_anwer) selected @endif  value="{{ $sub_controls_anwer }}">{{ ucfirst(str_replace('_',' ',$sub_controls_anwer)) }}</option>
                                                        @endforeach
                                                    </select>
                                                </td>
                                                <td><input maxlength="35" class="form-control note-item" data-title="{{ $bodywork['title'] }}" @if($expertise['is_closed']) disabled @endif data-key="{{ $key }}" name="{{ $key }}[]" value="{{ $bodywork['note'] }}" placeholder="Notlar"></td>
                                            </tr>
                                        @endif
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="notlarHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#notlar" aria-expanded="true"
                                    aria-controls="notlar">
                                Notlar (Boş Veriler Silinir)
                            </button>
                        </h2>
                        <div id="notlar" class="accordion-collapse collapse show" aria-labelledby="notlarHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-12 notes">
                                        <div class="row">
                                            <div class="col-md-5" style="position:relative;">
                                                <div class="loader-body note_add_loader"  style="display: none">
                                                    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
                                                </div>
                                                <div class="col-md-12 p-0 bg-success text-white text-center rounded-top">
                                                    Ekli Notlar
                                                </div>
                                                @php
                                                    $selectedNotesArray = array();
                                                @endphp
                                                <select class="form-control rounded-0 rounded-bottom" name="not[]" id="selected_notes" multiple id="" style="height: 165px;">
                                                    @foreach($expertise['getSubControlsAndEngineNotes'] as $note)
                                                        @php
                                                            array_push($selectedNotesArray,$note['note'])
                                                        @endphp
                                                        <option value="{{$note['id']}}">{{$note['note']}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <div class="col-md-2 text-center d-flex flex-column justify-content-center align-items-center">
                                                <button type="button" class="col-12 btn delete_selected_button @if(!$expertise['is_closed']) btn-danger @else border btn-disabled @endif btn-sm" @if($expertise['is_closed']) disabled @endif>
                                                    Not Sil
                                                </button>
                                                <button onclick="$('#notesModal').modal('toggle')" type="button" class="col-12 btn  btn-sm mt-1 @if(!$expertise['is_closed']) btn-info @else border btn-disabled @endif " @if($expertise['is_closed']) disabled @endif>
                                                    Not Tanımla
                                                </button>
                                                <button type="button" class="col-12 btn add_note_select @if(!$expertise['is_closed']) btn-success @else border btn-disabled @endif btn-sm mt-1" @if($expertise['is_closed']) disabled @endif>
                                                    Not Taşı
                                                </button>
                                            </div>
                                            <div class="col-md-5" style="position:relative;">
                                                <div class="loader-body note_add_loader"  style="display: none">
                                                    <div class="custom-loader " style="width: 50px;height: 50px;"></div>
                                                </div>
                                                <input class="form-control mb-1 pt-1 pb-1" placeholder="Hazır Notlar İçinde Ara..." type="text"  id="search_input">
                                                <select class="form-control" name="all_note[]" id="all_notes" multiple id="" style="height: 150px;">
                                                    @foreach($expertise['allNotes'] as $notes)
                                                        @if(!in_array($notes->note,$selectedNotesArray))
                                                            <option ondblclick="addNoteSelect(['{{$notes->note}}'],1)" value="{{$notes->note}}">{{$notes->note}}</option>
                                                        @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-2">
                @include('pages.expertise.right_tabs')
            </div>
            @if(!$expertise['is_closed'])
                <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">

                    @if($authUser->user_role_group_id != 33)
                        <button type="button" class="btn btn-info mt-3 mb-3 only_save"><i class="fa fa-save"></i> Kaydet</button>
                    @endif

                </div>
            @endif
        </div>
        <input type="hidden" name="close" @if($expertise['is_closed'] == 1) value="1" @else value="0" @endif>
    </form>
    <div class="modal fade" id="notesModal" tabindex="-1"
         aria-labelledby="notesModal" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Not Tanımla
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Not</small></label>
                            <input type="text" class="form-control" name="sub_controls_and_engine_notes_modal">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger sub_controls_and_engine_modal_save_button" >Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <!-- Select2 Cdn -->
    <script src="/assets/js/select2.min.js"></script>
    <script>
        isTabActive = true;
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                isTabActive = true;
            } else {
                isTabActive = false;
            }
        });
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
        function addNoteSelect(notes,type = 1){
            $('.note_add_loader').show()

            $.ajax({
                url: "{{ route('updateExpertiseNote') }}",
                type: "post",
                data: {
                    selectedNotes : JSON.stringify(notes),
                    type:'sub_controls_and_engine',
                    _token:"{{ @csrf_token() }}",
                    expertise_id:$('input[name=expertise_id]').val(),
                },
                success: function (response) {
                    if(response.success){
                        $('#notesModal').modal('hide')
                        Toast.fire({
                            icon: "success",
                            title: "Not Kayıt Edildi."
                        });
                        if(type == 1){
                            $("#all_notes option:selected").each(function () {
                                $(this).remove();
                            });
                        }

                        var option_text = '';

                        $.each(response.ExperSubControlsAndEngineNote, function(key, value) {
                            option_text += '<option ondblclick="addNoteSelect([\"'+value['note']+'\"],1)" value="' + value['id'] + '">' + value['note'] + '</option>';
                            console.log(option_text)
                        });
                        $('#selected_notes').html(option_text)
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Not Eklenirken Bir Hata Oluştu"
                        });
                    }
                    $('.note_add_loader').hide()
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        }
        (function (){
            "use strict"
            $(".select2").select2({
                placeholder: "Ara.."
            });

            $('.add-note').on('click',function (){
                let $val = $('.search-note').val();
                if ($val){
                    $('.notes').append('<input class="form-control" name="not[]" form="mainForm" value="'+$val+'">')
                    $('.search-note').val('');
                    $('.find-note').css('display','none')
                }
            })

            setInterval(function (){
                if(isTabActive){
                    $.ajax({
                        url: "{{ route('updateExpertise') }}",
                        type: "post",
                        data: $('#mainForm').serialize(),
                        success: function (response) {

                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                        }
                    });
                }

            },30 * 1000)
            $('.only_save').click(function(){
                let status = true
                $('.secenek').each(function(){
                    if ($(this).val() === ''){
                        Toast.fire({
                            icon: "error",
                            title: $(this).data('title') + ' için seçim yapmadınız!'
                        });
                        status = false;
                        return false; // Stop the loop if a selection is not made
                    }
                });

                $.each($('.note-item'),function (index,item){
                    if ($(this)[0].required === true && $(this).val().length < 3){
                        status = false;
                        Toast.fire({
                            icon: "error",
                            title: $(item).data('title') + ' için not en az 3 karakter olmalıdır!'
                        });
                        return false;
                    }
                })
                let close = 1;
                $('#mainForm [required]').each(function() {
                    var fieldValue = $(this).val();
                    if (fieldValue === '') {
                        close = 0;
                    }
                });
                $('input[name="close"]').val(close)
                if (status === true){
                    $.ajax({
                        url: "{{ route('updateExpertise') }}",
                        type: "post",
                        data: $('#mainForm').serialize(),
                        success: function (response) {
                            Toast.fire({
                                icon: response.type ? response.type : 'success',
                                title: "Bilgiler Kayıt Edildi."
                            }).then(function(){
                                if(response.return_url != ''){
                                    window.location.href = response.return_url;
                                }
                            });
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                        }
                    });
                }
            });

            $('.search-note').on('keyup',function (){
                let $val = $(this).val();
                if ($(this).val().length > 2){
                    $.ajax({
                        url: "{{ route('api.getNotes') }}",
                        type: "post",
                        data: {'_token':'{{ csrf_token() }}','search':$val,'key':'sub_control'} ,
                        success: function (response) {
                            $('.find-note').css('display','block')
                            if (response.items.length > 0){
                                let html = ''
                                $.each(response.items,function (index,item){
                                    html += '<div class="filter-item" data-message="'+item.note+'">'+item.note+'</div>'
                                })
                                $('.find-note').html(html)

                                $('.find-note .filter-item').on('click',function (){
                                    $('.notes').append('<input class="form-control" name="not[]" form="mainForm" value="'+$(this).data('message')+'">')
                                })
                            }
                        }
                    });
                }else{
                    $('.find-note').css('display','none')
                }
            })

            @if(auth('customer')->check())
            $('input').attr('disabled','true').removeAttr('required')
            $('select').attr('disabled','true').removeAttr('required')
            $('textarea').attr('disabled','true').removeAttr('required')
            $("button, input[type='submit']").filter(function() {
                return $(this).attr('type') !== 'button';
            }).remove();
            $('form').attr('action','')
            $('.add-note').remove()
            @endif

            @if(!auth('customer')->check() && $expertise['alt_motor_kontrol_user'] > 0 && $expertise['alt_motor_kontrol_user'] != $authUser->id)
            alert('Bu İşlem Başka Kullanıcıya Ait!')
            @endif

            /*Notes New Version Start*/
            $("#search_input").on("input", function () {
                var searchText = $(this).val().toLowerCase();
                $("#all_notes option").each(function () {
                    var optionText = $(this).text().toLowerCase();
                    $(this).toggle(optionText.indexOf(searchText) > -1);
                });
            });
            $('.add_note_select').click(function (){
                var selectedOptions = $("#all_notes").val();
                if(selectedOptions.length > 0){
                    addNoteSelect(selectedOptions)
                }
            })

            $('.sub_controls_and_engine_modal_save_button').click(function(){
                var array_notes = [$('input[name=sub_controls_and_engine_notes_modal]').val()]
                addNoteSelect(array_notes,2)
                $('#notesModal').modal('hide')

            })

            $('.delete_selected_button').click(function(){
                var selected_notes = $('#selected_notes').val();
                if(selected_notes.length > 0){
                    Swal.fire({
                        title: "Emin misiniz?",
                        text: "Seçili Notlar Silinecek",
                        icon: "error",
                        showCancelButton: true,
                        confirmButtonColor: "#d33",
                        cancelButtonColor: "#3085d6",
                        confirmButtonText: "Evet, Sil!",
                        cancelButtonText: "Hayır, Silme!"
                    }).then((result) => {
                        if (result.isConfirmed) {
                            $('.note_add_loader').show()
                            $.ajax({
                                url: "{{ route('deleteExpertiseNote') }}",
                                type: "post",
                                data: {
                                    selectedNotes : JSON.stringify(selected_notes),
                                    type:'sub_controls_and_engine',
                                    _token:"{{ @csrf_token() }}",
                                    expertise_id:$('input[name=expertise_id]').val(),
                                },
                                success: function (response) {
                                    if(response.success){

                                        var option_text = '';
                                        $.each(response.selectedNote, function(key, value) {
                                            option_text += '<option ondblclick="addNoteSelect([\"'+value+'\"],1)" value="' + value + '">' + value + '</option>';
                                        });
                                        $('#all_notes').html(option_text)


                                        var option_text_select = '';
                                        $.each(response.ExperSubControlsAndEngineNote, function(key, value) {
                                            option_text_select += '<option ondblclick="addNoteSelect([\"'+value+'\"],1)" value="' + value.id + '">' + value.note + '</option>';
                                        });
                                        $('#selected_notes').html(option_text_select)
                                        Toast.fire({
                                            icon: "success",
                                            title: "Notlar Silindi"
                                        });
                                    }else{
                                        Toast.fire({
                                            icon: "error",
                                            title: "Not Eklenirken Bir Hata Oluştu"
                                        });
                                    }
                                    $('.note_add_loader').hide()
                                },
                                error: function(jqXHR, textStatus, errorThrown) {
                                    console.log(textStatus, errorThrown);
                                }
                            });
                        }
                    });
                }

            })

            if (typeof Toast === 'undefined') {
                var Toast = Swal.mixin({
                    toast: true,
                    position: "top-end",
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.onmouseenter = Swal.stopTimer;
                        toast.onmouseleave = Swal.resumeTimer;
                    }
                });
            }
            /*Notes New Version finish*/
        })();
        @if($expertise['is_closed'] != 1)
        $(document).ready(function(){
            let getfunctionarray = [
                'turbo_ve_intercooler_kacak_kontrolu',
                'gozle_gorunur_kayislar',
                'diferansiyel_yag_kacak_kontrolu',
                'egzoz_sistemi',
                'motor_muhafaza_ve_alt_bakalitler',
                'elektrik_sistemi',
                'sanziman_yag_kacak_kontrolu',
                'fren_sistemi_kontrolu',
            ]
            for (let i = 0; i < getfunctionarray.length; i++) {
                optionChangeNot(getfunctionarray[i],'1');
            }
        })
        @endif
        function optionChangeNot(type,intype = null){
            let getfunctionarray = [
                'turbo_ve_intercooler_kacak_kontrolu',
                'gozle_gorunur_kayislar',
                'diferansiyel_yag_kacak_kontrolu',
                'egzoz_sistemi',
                'motor_muhafaza_ve_alt_bakalitler',
                'elektrik_sistemi',
                'sanziman_yag_kacak_kontrolu',
                'fren_sistemi_kontrolu',
            ]
            if(getfunctionarray.includes(type)){
                noInputSelect(type,intype)
            }

        }
        function noInputSelect(type,intype = null){
            if((type != 'turbo_ve_intercooler_kacak_kontrolu' && $('select[name="'+type+'[]"]').val() == "yok") || (type == 'turbo_ve_intercooler_kacak_kontrolu' && $('select[name="'+type+'[]"]').val() == "turbo_yok")){
                if (type == 'turbo_ve_intercooler_kacak_kontrolu'){
                    $('input[name="'+type+'[]"]').val('Araçta Bu özellik bulunmamaktadır.')
                    $('input[name="'+type+'[]"]').attr('readonly','')
                }else if(type != 'diferansiyel_yag_kacak_kontrolu'){
                    // $('input[name="'+type+'[]"]').val('Bu özellik bulunmamaktadır.')
                    $('input[name="'+type+'[]"]').attr('readonly','')
                }else{
                    if(intype != '1'){
                        $('input[name="'+type+'[]"]').val('')
                    }
                    $('input[name="'+type+'[]"]').removeAttr('readonly')
                    $('input[name="'+type+'[]"]').removeAttr("required");
                }

                console.log($(this))
                if (type == 'motor_yag_kacak_kontrolu'){
                    console.log($(this))
                }

            }
            else if($('select[name="'+type+'[]"]').val() == "sorunlu"){
                console.log(type,"kayislar");
                if(type == "gozle_gorunur_kayislar"){
                    $('input[name="'+type+'[]"]').val('Kayış seti ve gergi bilyası kontrol edilmeli.')
                   $('input[name="'+type+'[]"]').attr('readonly','');
                }else{
                    if(intype != '1'){
                        $('input[name="'+type+'[]"]').val('')
                    }

                    $('input[name="'+type+'[]"]').removeAttr('readonly');
                }

                $('input[name="'+type+'[]"]').attr("required","required");

            }else if((type == "diferansiyel_yag_kacak_kontrolu" || type == "elektrik_sistemi" ||  type == "sanziman_yag_kacak_kontrolu" ||  type == "fren_sistemi_kontrolu" ) && $('select[name="'+type+'[]"]').val() == "kötü"){
                if(intype != '1'){
                    $('input[name="'+type+'[]"]').val('')
                }

                $('input[name="'+type+'[]"]').removeAttr('readonly');
                $('input[name="'+type+'[]"]').attr("required","required");
            }else{
                if(intype != '1'){
                    $('input[name="'+type+'[]"]').val('')
                }
                $('input[name="'+type+'[]"]').removeAttr('readonly')
                $('input[name="'+type+'[]"]').removeAttr("required");
            }
        }

        $('#mainForm select').on('change',function (){
            let $key = $(this).data('key')
            let $value = $(this).val()
            let $noteInput = $('input[data-key="'+$key+'"]')
            $noteInput.val('')
            if ($key === 'motor_yag_kacak_kontrolu'){
                if ($value === 'yok' || $value === 'hafif' || $value === ''){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else{
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }
            }else if ($key === 'motor_su_kacak_kontrolu'){
                if ($value === 'yok' || $value === ''){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else{
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }
            }else if ($key === 'turbo_ve_intercooler_kacak_kontrolu'){
                if ($value === 'yok' || $value === '' || $value === 'turbo_yok' || $value === 'hafif'){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                    if ($value === 'turbo_yok')
                        $noteInput.val("Araçta Bu özellik bulunmamaktadır.")
                }else{
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }
            }else if ($key === 'motor_ufleme_kontrolu'){
                $noteInput.attr('readonly','true').removeAttr('required')
            }else if ($key === 'gozle_gorunur_kayislar'){
                if ($value === 'sorunlu'){
                    $noteInput.val("Kayış Seti ve Gergi Bilyası Kontrol Edilmeli.").attr('readonly','true')
                }else{
                    $noteInput.val("").attr('readonly','true').removeAttr('required')
                }
            }else if ($key === 'motor_yag_seviye_kontrolu'){
                $noteInput.attr('readonly','true').removeAttr('required') // açıklama yazılamaz
            }else if ($key === 'motor_sogutma_suyu_seviyesi'){
                $noteInput.attr('readonly','true').removeAttr('required') // açıklama yazılamaz
            }else if ($key === 'diferansiyel_yag_kacak_kontrolu'){
                if ($value === 'yok' || $value === '' || $value === 'hafif'){
                    $noteInput.attr('readonly','true').removeAttr('required') // açıklama yazılamaz
                }else if($value === 'orta'){
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }else{
                    $noteInput.removeAttr('readonly').attr('required','true') // açıklama zorunlu
                }
            }else if ($key === 'sanziman_yag_kacak_kontrolu'){
                if ($value === 'yok' || $value === 'hafif' || $value === ''){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else{
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }
            }else if ($key === 'fren_sistemi_kontrolu'){
                if ($value === '' || $value === 'iyi'){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else{
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }
            }else if ($key === 'direksiyon_sistemi_kontrolu'){
                if ($value === '' || $value === 'iyi'){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else{
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }
            }else if ($key === 'on_duzen_ve_arka_duzen_kontrolu'){
                if ($value === '' || $value === 'iyi' || $value === 'orta'){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else{
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }
            }else if ($key === 'suspansiyon_sistemi_kontrolu'){
                if ($value === '' || $value === 'iyi'){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else{
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }
            }else if ($key === 'egzoz_sistemi'){
                if ($value === '' || $value === 'sorunsuz' || $value === 'modifiyeli'){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else{
                    $noteInput.removeAttr('readonly').attr('required','true') // açıklama zorunlu
                }
            }else if ($key === 'motor_muhafaza_ve_alt_bakalitler'){
                if ($value === '' || $value === 'sorunsuz'){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else{
                    $noteInput.removeAttr('readonly').attr('required','true') // açıklama zorunlu
                }
            }else if ($key === 'elektrik_sistemi'){
                if ($value === '' || $value === 'iyi'){ // açıklama yazılamaz
                    $noteInput.attr('readonly','true').removeAttr('required')
                }else if ($value === 'orta'){
                    $noteInput.removeAttr('readonly').removeAttr('required') // açıklama yazılabilir
                }else{
                    $noteInput.removeAttr('readonly').attr('required','true') // açıklama zorunlu
                }
            }
        })
    </script>
@endpush
