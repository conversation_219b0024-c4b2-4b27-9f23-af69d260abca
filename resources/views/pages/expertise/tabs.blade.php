@push('css')
    <style>
        .nav-pills .nav-link {
            border-radius: unset;
        }
        .nav.nav-style-1 .nav-link.active:after{
            content: '\2190';
        }
    </style>
@endpush
<nav class="nav nav-style-1 nav-pills mb-3" role="tablist">

    <a href="{{ url()->previous() }}"
       class="nav-link"><PERSON><PERSON><PERSON></a>
    <a href="{{ route('expertises.edit',$expertise['uuid']) }}"
       class="nav-link @if(url()->current() == route('expertises.edit',$expertise['uuid'])) active @endif">Detaylar</a>

    @if(in_array($authUser->user_role_group_id,[30,31,32,33,34,35,37,39,40,42,44,45,47]))
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('car', $expertise['unlock_tabs'])))
            <a href="{{ route('expertise_details',['type'=>'car','expertise_id'=>$expertise['uuid']]) }}"
               class="nav-link @if(isset($_GET['type']) && $_GET['type'] == 'car') active @endif @if(isset($expertise['islemler']) && $expertise['islemler']['arac']) completed @else not-completed @endif">
                Araç
            </a>
        @endif
    @endif
    @if(in_array($authUser->user_role_group_id,[30,33,39,40,44]) )
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('bodywork', $expertise['unlock_tabs'])))
            <a href="{{ route('expertise_details',['type'=>'bodywork','expertise_id'=>$expertise['uuid']]) }}"
               class="nav-link @if(isset($_GET['type']) && $_GET['type'] == 'bodywork') active @endif @if(isset($expertise['islemler']) && $expertise['islemler']['kaporta']) completed @else not-completed @endif">
                Kaporta
            </a>
        @endif
    @endif
    @if(in_array($authUser->user_role_group_id,[30,34,39,40,45]))
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('brake', $expertise['unlock_tabs'])))
            <a href="{{ route('expertise_details',['type'=>'brake','expertise_id'=>$expertise['uuid']]) }}"
               class="nav-link @if(isset($_GET['type']) && $_GET['type'] == 'brake') active @endif @if(isset($expertise['islemler']) && $expertise['islemler']['fren']) completed @else not-completed @endif">
                Fren
            </a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('sub_control_and_engine', $expertise['unlock_tabs'])))
            <a href="{{ route('expertise_details',['type'=>'sub_controls_and_engine','expertise_id'=>$expertise['uuid']]) }}"
               class="nav-link @if(isset($_GET['type']) && $_GET['type'] == 'sub_controls_and_engine') active @endif @if(isset($expertise['islemler']) && $expertise['islemler']['motor']) completed @else not-completed @endif">
                Alt Kontroller ve Motor
            </a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('internal_control', $expertise['unlock_tabs'])))
            <a href="{{ route('expertise_details',['type'=>'internal_control','expertise_id'=>$expertise['uuid']]) }}"
               class="nav-link @if(isset($_GET['type']) && $_GET['type'] == 'internal_control') active @endif @if(isset($expertise['islemler']) && $expertise['islemler']['ic']) completed @else not-completed @endif">
                İç Kontroller
            </a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('tire_and_rim', $expertise['unlock_tabs'])))
            <a href="{{ route('expertise_details',['type'=>'tire_and_rim','expertise_id'=>$expertise['uuid']]) }}"
               class="nav-link @if(isset($_GET['type']) && $_GET['type'] == 'tire_and_rim') active @endif @if(isset($expertise['islemler']) && $expertise['islemler']['lastik']) completed @else not-completed @endif">
                Lastik ve Jant
            </a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('diagnostic', $expertise['unlock_tabs'])))
            <a href="{{ route('expertise_details',['type'=>'diagnostic','expertise_id'=>$expertise['uuid']]) }}"
               class="nav-link @if(isset($_GET['type']) && $_GET['type'] == 'diagnostic') active @endif @if(isset($expertise['islemler']) && $expertise['islemler']['diagnostic']) completed @else not-completed @endif">
                Diagnostic
            </a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('component', $expertise['unlock_tabs'])))
            <a href="{{ route('expertise_details',['type'=>'component','expertise_id'=>$expertise['uuid']]) }}"
               class="nav-link @if(isset($_GET['type']) && $_GET['type'] == 'component') active @endif @if(isset($expertise['islemler']) && $expertise['islemler']['komponent']) completed @else not-completed @endif">
                Komponent
            </a>
        @endif
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('co2', $expertise['unlock_tabs'])))
            <a href="{{ route('expertise_details',['type'=>'co2','expertise_id'=>$expertise['uuid']]) }}"
               class="nav-link @if(isset($_GET['type']) && $_GET['type'] == 'co2') active @endif @if(isset($expertise['islemler']) && $expertise['islemler']['co2']) completed @else not-completed @endif">
                {{__('arrays.co2_kacak_testi')}}
            </a>
        @endif
    @endif
    @if(in_array($authUser->user_role_group_id,[30,31,32,34,35,37,39,40,44,45,47]))
        @if(!isset($expertise['unlock_tabs']) || (isset($expertise['unlock_tabs']) && in_array('query', $expertise['unlock_tabs'])))
        <a href="{{ route('expertise_details',['type'=>'query','expertise_id'=>$expertise['uuid']]) }}" class="nav-link">
            Sorgulama
        </a>
        @endif
    @endif

    @if(!auth('customer')->check())
        <a href="{{ route('aliciSozlesme',$expertise['uuid']) }}/" class="nav-link">İş Emri Formu</a>
        <a href="{{ route('ekspertizRaporu',$expertise['uuid']) }}/" class="nav-link">Ekspertiz Raporu</a>
    @endif

</nav>
