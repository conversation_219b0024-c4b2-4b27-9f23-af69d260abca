@extends('pages.build')
@section('title','Excelden Aktar')
@section('content')
    <div class="row">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <form method="post" action="{{ route('importExcelPost') }}" enctype="multipart/form-data">@csrf
                        <div class="form-group">
                            <label>İçerik</label>
                            <select name="type" class="form-control">
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'expertise') selected @endif value="expertise">Ekspertiz</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'branch') selected @endif value="branch">Bayi</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'campaign') selected @endif value="campaign"><PERSON><PERSON><PERSON><PERSON></option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'car_case_type') selected @endif value="car_case_type"><PERSON><PERSON>ü<PERSON></option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'car_fuel') selected @endif value="car_fuel">Araç Yakıt Türü</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'car_gear') selected @endif value="car_gear">Araç Vites Türü</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'car_group') selected @endif value="car_group">Araç Grubu</option>
                                {{--                                <option @if(isset($_GET['type']) && $_GET['type'] == 'car') selected @endif value="car">Araç</option>--}}
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'customer_group') selected @endif value="customer_group">Cari Grubu</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'customer') selected @endif value="customer">Cari</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'customer_type') selected @endif value="customer_type">Cari Türü</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'stock_group') selected @endif value="stock_group">Stok Grubu</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'stock') selected @endif value="stock">Stok</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'stock_type') selected @endif value="stock_type">Stok Türü</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'stock_unit') selected @endif value="stock_unit">Stok Birimi</option>
                                <option @if(isset($_GET['type']) && $_GET['type'] == 'user') selected @endif value="user">Personel</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Excel Dosyası <span class="text-danger">(Max 2MB Dosya Boyutu)</span></label>
                            <input type="file" class="form-control" name="file" required accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel">
                        </div>
                        <button class="btn btn-success mt-3">Kaydet</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection
