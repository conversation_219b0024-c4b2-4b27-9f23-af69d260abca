@extends('pages.build')
@section('title','<PERSON> Sayfa')
@if(auth('customer')->check())
    @push('main-content-css') style="background:#e30614;height:100vh;padding:0 !important" @endpush
@endif
@push('css')
    <style>
        .list-item{
            width: 50%;
            border: 1px solid #fff;
            border-top: unset;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            cursor: pointer;
        }
        .list-item i{
            font-size: 2.5rem;
        }
        .list-item span{
            text-align: end;
            font-size: 1rem;
            font-family: Poppins,sans-serif !important;
        }
        .list-item:nth-child(1),.list-item:nth-child(3),.list-item:nth-child(5){
            border-left: unset;
        }
        .list-item:nth-child(2),.list-item:nth-child(4),.list-item:nth-child(6){
            border-right: unset;
        }
        .slider-item{
            background-size: cover;
            height: 250px;
            background-position: top;
            margin-top: 6vh;
        }
    </style>
@endpush
@section('content')
    @if(!auth('customer')->check())
{{--        <div class="row">--}}
{{--            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">--}}
{{--                <div class="card">--}}
{{--                    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">--}}
{{--                        <a href="{{ route('index') }}" class="nav-link active">{{ \App\Models\Menu::where('key','home')->first()->value }}</a>--}}
{{--                        @if(!auth('customer')->check() && $authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','list_expertise')->first())--}}
{{--                            <a href="{{ route('expertises.index') }}" class="nav-link">{{ \App\Models\Menu::where('key','expertise')->first()->value }}</a>--}}
{{--                        @endif--}}
{{--                        @if(!auth('customer')->check() && $authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','list_stock')->first())--}}
{{--                            <a href="{{ route('stocks.index') }}" class="nav-link">{{ \App\Models\Menu::where('key','stock_management')->first()->value }}</a>--}}
{{--                        @endif--}}
{{--                        @if(!auth('customer')->check() && $authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','list_customer')->first())--}}
{{--                            <a href="{{ route('customers.index') }}" class="nav-link">{{ \App\Models\Menu::where('key','customer_management')->first()->value }}</a>--}}
{{--                        @endif--}}
{{--                        @if(!auth('customer')->check() && $authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','list_branch')->first())--}}
{{--                            <a href="{{ route('branches.index') }}" class="nav-link">{{ \App\Models\Menu::where('key','branch_management')->first()->value }}</a>--}}
{{--                        @endif--}}
{{--                        @if(!auth('customer')->check() && $authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','list_car')->first())--}}
{{--                            <a href="{{ route('cars.index') }}" class="nav-link">{{ \App\Models\Menu::where('key','car_management')->first()->value }}</a>--}}
{{--                        @endif--}}
{{--                        @if(!auth('customer')->check() && $authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','list_user')->first())--}}
{{--                            <a href="{{ route('users.index') }}" class="nav-link">{{ \App\Models\Menu::where('key','user_management')->first()->value }}</a>--}}
{{--                        @endif--}}
{{--                        @if(!auth('customer')->check() && $authUser->type == 'admin')--}}
{{--                            <a href="{{ route('kvkkOnay') }}" class="nav-link">KVKK Onay</a>--}}
{{--                            <a href="{{ route('menus.index') }}" class="nav-link">Menü Başlıkları</a>--}}
{{--                            <a href="{{ route('logs.index') }}" class="nav-link">Log Kayıtları</a>--}}
{{--                        @endif--}}
{{--                    </nav>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}

        <livewire:dashboard />

        <div class="row">
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <div class="card">
                    <div class="card-body">
                        <div>
                            <h6>Başarısız Girişler</h6>
                            <ul style="list-style-type:disc">
                                @foreach(\App\Models\Log::where('content_id',$authUser->telephone)->where('content_type','false_login')->get()->reverse()->take(10) as $falseLogin)
                                    <li>{{ $falseLogin->created_at->format('d.m.Y H:i:s') }} - {{ $falseLogin->ip }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @else
        <div style="background: #e30614">
            <div id="carouselExampleIndicators" class="carousel slide" data-bs-ride="carousel">
                <div class="carousel-inner">
                    @foreach(\App\Models\Slider::where('place','customer')->get() as $index => $slider)
                        <div class="carousel-item @if($index == 0) active @endif">
                            <div class="slider-item" style="background-image: url('/storage/{{ $slider->image }}')"></div>
{{--                            <img src="/storage/{{ $slider->image }}" class="d-block w-100" alt="...">--}}
                        </div>
                    @endforeach
                </div>
            </div>
            <div class="row" style="margin-right: 0;margin-left: 0">
                <a class="list-item" href="{{ route('nearbyBranches') }}">
                    <i class="fe fe-map"></i>
                    <span>EN YAKIN <br> UMRAN</span>
                </a>
                <a class="list-item" href="{{ route('customer.plusCard') }}">
                    <span style="background-color: #fff;border-radius: 50%;padding: 5px;width: 2rem;height: 2rem;color: #000;text-align: center">{{ $credit }}</span>
                    <span>PLUS CARD <br> BAKİYENİZ</span>
                </a>
                <a class="list-item" href="{{ route('customer.cars') }}">
                    <span style="background-color: #fff;border-radius: 50%;padding: 5px;width: 2rem;height: 2rem;color: #000;text-align: center">0</span>
                    <span>İŞLEM <br> HACMİ</span>
                </a>
                <a class="list-item" href="{{ route('customer.query') }}">
                    <i class="ri-car-line"></i>
                    <span>ARAÇ <br> SORGULAMA</span>
                </a>
                <a class="list-item" href="{{ route('customer.booking') }}">
                    <i class="ri-calendar-line"></i>
                    <span>RANDEVU <br> AL</span>
                </a>
                <a class="list-item" href="{{ route('complaints.index') }}">
                    <i class="ri-customer-service-2-line"></i>
                    <span>ÇÖZÜM <br> MERKEZİ</span>
                </a>
            </div>
            <div style="display: flex;align-items: center;">
                <img src="/assets/logo-bg.png" alt="logo" class="desktop-logo">
            </div>
        </div>

    @endif



@endsection
