@extends('pages.build')
@section('title','<PERSON>g <PERSON>')
@push('css')
   <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form id="filterForm" method="get" action="{{ route('logs.index') }}">
                    <input type="hidden" name="step" value="{{ $step }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col">
                                <div class="form-group">
                                    <label>Başlangıç <PERSON></label>
                                    <input type="date" class="form-control" name="start_date" value="{{ $startDate }}">
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label>Bitiş Tarihi</label>
                                    <input type="date" class="form-control" name="end_date" value="{{ $endDate }}">
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label>IP Adresi</label>
                                    <input type="text" class="form-control" name="ip" value="{{ $_GET['ip'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label>Kullanıcı</label>
                                    <select class="form-control select2" name="user_id">
                                        <option value="">Tümü</option>
                                        @foreach(\App\Models\User::all() as $filterUser)
                                            <option @if(isset($_GET['user_id']) && $_GET['user_id'] == $filterUser->id) selected @endif value="{{ $filterUser->id }}">{{ $filterUser->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col">
                                <div class="form-group">
                                    <label>Kayıt Getir</label>
                                    <select class="form-control" name="take">
                                        <option value="10">10</option>
                                        <option @if($take == 25) selected @endif value="25">25</option>
                                        <option @if($take == 50) selected @endif value="50">50</option>
                                        <option @if($take == 100) selected @endif value="100">100</option>
                                    </select>
                                </div>
                            </div>
                            @if($authUser->type == 'admin')
                                <div class="col">
                                    <div class="form-group">
                                        <label>Bayi</label>
                                        <select class="form-control select2" name="branch_id">
                                            <option value="">Tümü</option>
                                            @foreach(\App\Models\Branch::where('status',1)->get() as $filterBranch)
                                                <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col mt-4">
                                <button class="btn btn-success">Listele</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <div class="card-body table-responsive">

                    <table class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr>
                            <th>Tarih</th>
                            <th>Açıklama</th>
                            <th>IP Adresi</th>
                            <th>Kullanıcı</th>
                            <th>Bayi</th>
                        </tr>
                        </thead>
                        <tbody>
                        @if($items != [])
                            @foreach($items as $key => $item)
                                <tr>
                                    <td>{{ $item->created_at->format('d.m.Y H:i:s') }}</td>
                                    <td>{{ $item->description }}</td>
                                    <td>{{ $item->ip }}</td>
                                    <td>{{ $item->getUser ? $item->getUser->name : '' }}</td>
                                    <td>{{ $item->getBranch ? $item->getBranch->unvan : '' }}</td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td class="text-center text-danger" colspan="5"><b>Listeleme Yapınız!</b></td>
                            </tr>
                        @endif
                        </tbody>
                        @if(count($items) > 0)
                            <tfoot>
                            <tr>
                                <td colspan="5" align="right">
                                    @if(isset($_GET['step']) && $_GET['step'] > 0)
                                        <a onclick="previousRecords()" class="btn btn-sm btn-danger">Önceki {{ $take }} Kayıt</a>
                                    @endif
                                    @if($more)
                                        <a onclick="nextRecords()" class="btn btn-sm btn-danger">Sonraki {{ $take }} Kayıt</a>
                                    @endif
                                </td>
                            </tr>
                            </tfoot>
                        @endif

                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <!-- Datatables Cdn -->
       <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(".select2").select2({
            placeholder: "Ara.."
        });
        function previousRecords(){
            let $step = $('input[name="step"]').val();
            $('input[name="step"]').val(parseInt($step) - 1)
            $('#filterForm').submit()
        }
        function nextRecords(){
            let $step = $('input[name="step"]').val();
            $('input[name="step"]').val(parseInt($step) + 1)
            $('#filterForm').submit()
        }

        $('select[name="take"]').on('change',function (){
            $('input[name="step"]').val(0)
        })
        $(function (e) {
            'use strict';
            $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });
        });
    </script>
@endpush
