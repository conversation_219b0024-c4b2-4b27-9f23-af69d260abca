<!DOCTYPE html>
<html lang="{{ config('app.locale') }}">
<head>

    <meta charset="UTF-8">
    <meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=0'>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>Giriş Yap</title>


    <!-- Favicon -->
    <link rel="icon" href="/assets/logo-icon.png" type="image/x-icon">

    <!-- Main Theme Js -->
    <script src="/assets/js/authentication-main.js"></script>

    <!-- Bootstrap Css -->
    <link id="style" href="/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet" >

    <!-- Style Css -->
    <link href="/assets/css/styles.min.css" rel="stylesheet" >

    <!-- Icons Css -->
    <link href="/assets/css/icons.min.css" rel="stylesheet" >
    <link rel="stylesheet" type="text/css" href="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
    <style>
        .card-sigin{
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .bg-image{
            background-image: url('/assets/umran_giris.jpeg');
            background-position: top left;
            background-size: cover;
            background-repeat: no-repeat;
            height: 100vh;
        }
        @media screen and (max-width: 426px) {
            .bg-image{
                height: 40vh;
            }
            .card-sigin{
                height: 70vh;
            }
        }
        .show-password,.show-password2{
            font-size: 1.5rem;
            position: relative;
            top: -2.1rem;
            float: inline-end;
            right: 1rem;
            cursor: pointer;
        }

        @media screen and (max-width: 500px) {
            .card-sigin-main{
                padding-left: 0;
            }
            .mobile-bottom{
                display: block !important;
                background: #e30614;
                width: 100%;
                height: 15vh;
                position: fixed;
                bottom: 0;
                left: 0;
            }
            .slider-area{
                display: none;
            }
        }
    </style>
</head>
<body class="ltr error-page1 bg-danger">

<div class="square-box">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div>
<div class="page" >
    <div class="page-single">
        <div class="container-fluid ">
            <div class="row">
                <div class="col-xl-8 slider-area slick" style="padding-left: 0;padding-right: 0">
                    @foreach(\App\Models\Slider::where('place','panel')->get() as $slider)
{{--                        <img src="/storage/{{$slider->image}}" class="img-fluid">--}}
                        <div class="bg-image" style="background-image: url('/storage/{{$slider->image}}"></div>
                    @endforeach
                    <img src="/assets/umran_giris.jpeg" class="img-fluid">
{{--                    <div class="bg-image"></div>--}}
                </div>
                <div class="col-xl-4 card-sigin-main my-auto  justify-content-center" style="padding-top: 0;padding-right: 0;padding-left: 0">

                    <div class="card-sigin" style="border-radius: 0;@if(isset($_GET['type']) && ($_GET['type'] == 'customer' || $_GET['type'] == 'sms') ) align-items: start @endif">
                        <div class="main-card-signin d-md-flex">
                            @if(!isset($_GET['type']) || $_GET['type'] != 'customer' || $_GET['type'] != 'sms')
{{--                                <nav class="nav nav-style-1 nav-pills mb-3 justify-content-center" role="tablist">--}}
{{--                                    <a class="nav-link active" href="{{ route('loginPage') }}">Personel Girişi</a>--}}
{{--                                    --}}{{--                                        <a class="nav-link @if(isset($_GET['type']) && ($_GET['type'] == 'customer' || $_GET['type'] == 'sms') ) active @endif" href="{{ route('landing') }}" >Müşteri Girişi</a>--}}
{{--                                    --}}{{--                                    <a class="nav-link @if(isset($_GET['type']) && ($_GET['type'] == 'customer' || $_GET['type'] == 'sms') ) active @endif" href="{{ route('loginPage',['type'=>'customer']) }}" >Müşteri Girişi</a>--}}
{{--                                </nav>--}}
                            @endif
                            <div class="tab-content">
                                <div class="tab-pane show active text-muted" id="nav-temel" role="tabpanel">
                                    <div class="wd-100p">
                                        <h1 style="font-size: 4.5rem" class="text-center">{{ now()->format('H:i') }}</h1>
                                        <h4 class="text-center">{{ now()->format('d.m.Y') . ' ' . \Carbon\Carbon::now()->locale('tr')->dayName }}</h4>
                                        <div class="d-flex justify-content-center"><a href="/"><img src="/assets/umram4.webp" class="sign-favicon ht-80" alt="logo"></a></div>
                                        @if(isset($_GET['type']) && ($_GET['type'] == 'customer' || $_GET['type'] == 'sms') )
                                            <div class="d-flex align-items-center justify-content-center" style="flex-direction: column">
                                                <h6 style="color: #373737c4">BAĞIMSIZ ARAÇ EKSPERTİZ MERKEZLERİ</h6>
                                                <h4 style="color: #ff6b00">444 5 417</h4>
                                            </div>
                                        @endif
                                        <div class="main-signup-header">
                                            {{--                                                <h1 style="color:#e12222 !important;">Hoşgeldiniz</h1>--}}
                                            {{--                                                <h5 >Lütfen Giriş Yapın</h5>--}}

                                            <form action="{{ route('loginPost') }}" method="post">@csrf
                                                <input type="hidden" name="type" value="{{ $_GET['type'] ?? 'employee' }}">
                                                <input type="hidden" name="return_url" value="{{ $_GET['return_url'] ?? '' }}">
                                                @if(isset($_GET['type']) && $_GET['type'] == 'customer')
                                                    <div class="form-group text-center">
{{--                                                        <label>Telefon Numarası</label>--}}
                                                        <input type="hidden" name="return_url" value="{{$_GET['return_url'] ?? ''}}">
                                                        <input class="form-control" placeholder="+90___ ___ __ __" style="border: unset;border-bottom: 2px solid #ff1000;border-radius: 0" name="telefon" required type="text">
                                                        <p style="color: #ff6b00 !important;">Onay Mesajı Gönderilecektir.</p>
                                                    </div>
{{--                                                    <div class="mobile-bottom" style="display: none">--}}
{{--                                                        <img src="/assets/kindpng_1725076.png" style="    width: 35%;--}}
{{--    position: fixed;--}}
{{--    bottom: 0;--}}
{{--}">--}}
{{--                                                    </div>--}}
                                                    <button class="btn btn-danger btn-block">Giriş Yap</button>
                                                @elseif(isset($_GET['type']) && $_GET['type'] == 'sms')
                                                    <input type="hidden" name="login_token" value="{{ $_GET['login_token'] ?? '' }}">
                                                    <div class="form-group text-center">
                                                        <input type="hidden" name="return_url" value="{{$_GET['return_url'] ?? ''}}">
                                                        <input class="form-control" placeholder="UMR-XX-XX" name="login_sms" value="UMR-" required type="text">
                                                        <p class="sms-kalan" style="color: #ff6b00 !important;">Telefonunuza gelen onay kodunu 180 saniye içerisinde girmeniz gerekir.</p>
                                                    </div>
{{--                                                    <div class="mobile-bottom" style="display: none">--}}
{{--                                                        <img src="/assets/hispanic-portrait-clean-slim-natural.png" style="    width: 40%;--}}
{{--    position: fixed;--}}
{{--    bottom: 0;--}}
{{--    left: 7rem;--}}
{{--}">--}}

{{--                                                    </div>--}}
                                                    <button class="btn btn-danger btn-block">Giriş Yap</button>
                                                @elseif(isset($qr))
                                                    @php
                                                        $login_user_data = \App\Models\User::where('telephone',str_replace(['(',')',' '],'',$_GET['telephone']))
                                                        //->orWhere('second_telephone',str_replace(['(',')',' '],'',$_GET['telephone']))
                                                        ->where('status',1)
                                                         ->first();
                                                    @endphp
                                                    @if(!empty($login_user_data) && !empty($login_user_data->first_login) && $login_user_data->first_login == 1)
                                                        <div class="form-group">
                                                            <label>Yeni Şifre </label>
                                                            <input class="form-control" placeholder="Şifre" required id="newPassword" name="password" type="password">
                                                        </div>
                                                        <p id="hatalar" class="text-danger"></p>
                                                        <div class="form-group">
                                                            <label>Şifre Tekrar</label>
                                                            <input class="form-control" placeholder="Şifre" required name="password_again" type="password">
                                                        </div>
                                                    @else
                                                        <div class="form-group">
                                                            <label>Şifre </label>
                                                            <input class="form-control login-password" placeholder="Şifre" required name="password" type="password">
                                                            <i class="fe fe-eye-off show-password"></i>
                                                        </div>
                                                        <p><a class="text-danger" href="{{ route('forgotPassword') }}">Şifrenizi mi unuttunuz?</a> </p>

                                                    @endif
                                                    <button id="kaydetButonu" class="btn btn-danger btn-block">Giriş Yap</button>
                                                @elseif(isset($_GET['step']) && $_GET['step'] == 'password')
                                                    @php
                                                        $login_user_data = \App\Models\User::where('login_token',$_GET['login_token'])
                                                        ->where('status',1)
                                                         ->first();
                                                    @endphp
                                                    @if(!empty($login_user_data) && !empty($login_user_data->first_login) && $login_user_data->first_login == 1)
                                                        <div class="form-group">
                                                            <label>Yeni Şifre </label>
                                                            <input class="form-control" placeholder="Şifre" required id="newPassword" name="password" type="password">
                                                        </div>
                                                        <p id="hatalar" class="text-danger"></p>
                                                        <div class="form-group">
                                                            <label>Şifre Tekrar</label>
                                                            <input class="form-control" placeholder="Şifre" required name="password_again" type="password">
                                                        </div>
                                                    @else
                                                        <div class="form-group">
                                                            <label>Şifre </label>
                                                            <input class="form-control login-password2" placeholder="Şifre" required name="password" type="password">
                                                            <i class="fe fe-eye-off show-password2"></i>
                                                        </div>
                                                        <p><a class="text-danger" href="{{ route('forgotPassword') }}">Şifrenizi mi unuttunuz?</a> </p>

                                                    @endif
                                                <input type="hidden" name="type" value="check_password">
                                                    <input type="hidden" name="login_token" value="{{ $_GET['login_token'] ?? '' }}">
                                                    <button id="kaydetButonu" class="btn btn-danger btn-block">Giriş Yap</button>
                                                @elseif(isset($_GET['telephone']) && $_GET['telephone'] != '' && isset($_GET['login_token']) && $_GET['login_token'] != '')
                                                    <div class="form-group">
                                                        <label>SMS Kodu</label>
                                                        <input class="form-control" placeholder="UMR-XX-XX" name="login_sms" value="UMR-" required type="text">
                                                    </div>
                                                    <button class="btn btn-danger btn-block">Giriş Yap</button>
                                                    <input type="hidden" name="type" value="check_user_sms">
                                                    <input type="hidden" name="telephone" value="{{ $_GET['telephone'] }}">
                                                    <input type="hidden" name="login_token" value="{{ $_GET['login_token'] ?? '' }}">
                                                @else
                                                    @if(session('info'))
                                                        <p class="text-danger">{{ session('info') }} <a href="{{ route('loginPage') }}" class="text-info">Sayfayı Yenile</a> </p>
                                                    @endif
                                                    <div class="form-group">
                                                        <label>Telefon Numarası</label>
                                                        <input class="form-control mask-tel" placeholder="(0XXX) XXX XX XX" name="telephone" required type="text">
                                                    </div>
                                                        <input type="hidden" name="type" value="send_user_sms">
                                                    <button class="btn btn-danger btn-block">Giriş Yap</button>
{{--                                                    <div class="d-flex mt-3">--}}
{{--                                                        <a href="#"><img style="width: 150px;margin-top: 3px" src="/assets/google-play-badge.png"></a>--}}
{{--                                                        <a href="#"><img style="width: 150px;margin-top: 3px" src="/assets/appstore.png"></a>--}}
{{--                                                    </div>--}}
                                                @endif
                                            </form>
                                            <form id="mailForm" action="{{ route('loginPage') }}"></form>
                                            <h6 class="mb-4 mt-2 fs-11">IP Adresiniz : {{ request()->ip() }}</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                @if(!isset($_GET['type']) || ($_GET['type'] != 'customer' && $_GET['type'] != 'sms'))
                    <footer class="footer bg-white text-center" style="padding-inline-start: 7rem;position: absolute;bottom: 0">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="tradingview-widget-container">
                                    <div class="tradingview-widget-container__widget"></div>
                                    <script type="text/javascript" src="https://s3.tradingview.com/external-embedding/embed-widget-ticker-tape.js" async>
                                        {
                                            "symbols": [
                                            {
                                                "description": "",
                                                "proName": "FX:USDTRY"
                                            },
                                            {
                                                "description": "",
                                                "proName": "FX:EURTRY"
                                            },
                                            {
                                                "description": "",
                                                "proName": "CAPITALCOM:GBPTRY"
                                            }
                                        ],
                                            "showSymbolLogo": true,
                                            "isTransparent": true,
                                            "displayMode": "regular",
                                            "colorTheme": "light",
                                            "locale": "tr"
                                        }
                                    </script>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div id="ww_734c029c0717f" v='1.3' loc='auto' a='{"t":"ticker","lang":"tr","sl_lpl":1,"ids":[],"font":"Arial","sl_ics":"one","sl_sot":"celsius","cl_bkg":"#FFFFFF00","cl_font":"#000000","cl_cloud":"#d4d4d4","cl_persp":"#2196F3","cl_sun":"#FFC107","cl_moon":"#FFC107","cl_thund":"#FF5722"}'>Daha fazla hava durumu tahmini: <a href="https://oneweather.org/tr/ankara/30_days/" id="ww_734c029c0717f_u" target="_blank">Ankarada 30 günlük hava durumu</a></div><script async src="https://app2.weatherwidget.org/js/?id=ww_734c029c0717f"></script>
                            </div>
                        </div>
                    </footer>
                @endif
            </div>
        </div>

    </div>
</div>


<script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="/assets/js/<EMAIL>"></script>
<script src="/assets/js/imask.js"></script>
<script src="/assets/js/jquery_3.6.1.min.js"></script>
<script type="text/javascript">
    $(document).ready(function(){
        $('.slick').slick({
            arrows: false,
            autoplay: true,
            autoplaySpeed: 2000,
        });
    });
</script>
<script>

    if (document.querySelector('.show-password')){
        document.querySelector('.show-password').addEventListener('click',function (){
            var input = document.querySelector('.login-password')
            var showPasswordButton = document.querySelector('.show-password')
            if (input.type == 'text'){
                input.type = 'password'
                showPasswordButton.classList.remove('fe-eye')
                showPasswordButton.classList.add('fe-eye-off')
            }else{
                input.type = 'text'
                showPasswordButton.classList.add('fe-eye')
                showPasswordButton.classList.remove('fe-eye-off')
            }
        })
    }


    if (document.querySelector('.show-password2')){
        document.querySelector('.show-password2').addEventListener('click',function (){
            var input = document.querySelector('.login-password2')
            var showPasswordButton = document.querySelector('.show-password2')
            if (input.type == 'text'){
                input.type = 'password'
                showPasswordButton.classList.remove('fe-eye')
                showPasswordButton.classList.add('fe-eye-off')
            }else{
                input.type = 'text'
                showPasswordButton.classList.add('fe-eye')
                showPasswordButton.classList.remove('fe-eye-off')
            }
        })
    }


    if(document.querySelector('input[name="telefon"]')){
        const maskTel = IMask(document.querySelector('input[name="telefon"]'), {
            mask: '(0000) 000 0000'
        });
    }

    if(document.querySelector('input[name="login_sms"]')){
        const maskTel = IMask(document.querySelector('input[name="login_sms"]'), {
            mask: 'UMR-00-00'
        });
    }

    if(document.querySelector('input[name="totp_code"]')){
        const maskTel = IMask(document.querySelector('input[name="totp_code"]'), {
            mask: '000 000'
        });
    }

    @if(isset($_GET['type']) && $_GET['type'] == 'sms')
        let countdownTime = 180;

        // Her saniye geri sayımı güncelleyecek bir fonksiyon oluşturun
        function updateCountdown() {
            // Süreyi ekranda gösterin (burada bir örnekleme yapabilirsiniz)
            document.querySelector('.sms-kalan').innerHTML = "Telefonunuza gelen onay kodunu " + countdownTime + " saniye içerisinde girmeniz gerekir."

            // Geri sayımı azaltın
            countdownTime--;

            // Eğer süre 0'a ulaşırsa, sayacı durdurun
            if (countdownTime < 0) {
                clearInterval(intervalId);
                window.location.href = '{{ route('loginPage',['type'=>'customer']) }}'
            }
        }

        // setInterval fonksiyonuyla her saniye 'updateCountdown' fonksiyonunu çalıştırın
        let intervalId = setInterval(updateCountdown, 1000);
    @endif


    @if(session('success'))
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });
    Toast.fire({
        icon: "success",
        title: "{{ session('success') }}"
    });
    @endif
    @if(session('error'))
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });
    Toast.fire({
        icon: "error",
        title: "{{ session('error') }}"
    });
    @endif

    document.addEventListener('DOMContentLoaded', function () {
        var newPassword = document.getElementById('newPassword');
        var passwordAgain = document.getElementsByName('password_again')[0];
        var hatalar = document.getElementById('hatalar');
        var kaydetButonu = document.getElementById('kaydetButonu');

        function sifreKontrol() {
            var sifre = newPassword.value;
            var sifreTekrar = passwordAgain.value;

            if (sifre.length < 6) {
                hatalar.textContent = "Şifre en az 6 karakter uzunluğunda olmalıdır.";
                kaydetButonu.disabled = true;
                return;
            }

            // Regex kontrolleri
            var buyukHarf = /[A-Z]/;
            var kucukHarf = /[a-z]/;
            var rakam = /[0-9]/;
            var ozelKarakter = /[^A-Za-z0-9]/;

            if (!buyukHarf.test(sifre) || !kucukHarf.test(sifre) || !rakam.test(sifre) || !ozelKarakter.test(sifre)) {
                hatalar.textContent = "Şifre büyük harf, küçük harf, rakam ve özel karakter içermelidir.";
                kaydetButonu.disabled = true;
                return;
            }

            // Şifre eşleşme kontrolü
            if (sifre !== sifreTekrar) {
                hatalar.textContent = "Şifreler eşleşmiyor.";
                kaydetButonu.disabled = true;
                return;
            }

            // Eğer tüm kontroller geçildiyse
            hatalar.textContent = "";
            kaydetButonu.disabled = false;
        }

        newPassword.addEventListener('input', sifreKontrol);
        passwordAgain.addEventListener('input', sifreKontrol);
    });



    // document.querySelector("#newPassword").addEventListener('keyup',function (){
    //     var buyukHarf = /[A-Z]/g;
    //     var kucukHarf = /[a-z]/g;
    //     var rakam = /[0-9]/g;
    //     var ozelKarakter = /[^A-Za-z0-9]/g;
    //
    //     var zorlukKriterleri = [
    //         { regex: buyukHarf, mesaj: "En az bir büyük harf içermelidir." },
    //         { regex: kucukHarf, mesaj: "En az bir küçük harf içermelidir." },
    //         { regex: rakam, mesaj: "En az bir rakam içermelidir." },
    //         { regex: ozelKarakter, mesaj: "En az bir özel karakter içermelidir." }
    //     ];
    //
    //     var hatalar = zorlukKriterleri.filter(kriter => !document.querySelector("#newPassword").value.match(kriter.regex)).map(kriter => kriter.mesaj);
    //     let html = ''
    //     hatalar.forEach(function (index,item){
    //         html += index +"<br>"
    //     })
    //     document.querySelector("#hatalar").innerHTML = html
    //     return hatalar.length === 0 ? "Şifre geçerli." : hatalar.join("\n");
    // })
    //
    // document.querySelector("input[name='password_again']").addEventListener('keyup',function (){
    //     if (document.querySelector("input[name='password_again']").value == document.querySelector("input[name='password']"))
    //         document.querySelector("button").attributes.type = 'submit'
    //     else
    //         document.querySelector("button").attributes.type = 'button'
    // })

    if(document.querySelector('.mask-tel')){
        document.querySelectorAll('.mask-tel').forEach(function (item,index){
            const maskTel = IMask(item, {
                mask: '(0000) 000 0000'
            });
        })
    }

    document.querySelector('input[name="telephone"]').addEventListener('click',function (){
        let length = this.value.length
        if (length == 0){
            this.value = 0
        }
    })
    document.querySelector('input[name="telephone"]').addEventListener('keyup',function (){
        let length = this.value.length
        if (length == 0){
            this.value = 0
        }
    })

</script>
<script type="text/javascript" src="//cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>
</body>
</html>
