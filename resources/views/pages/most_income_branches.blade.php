@extends('pages.build')
@section('title',$startDate->translatedFormat('d F Y') . ' - ' . $endDate->translatedFormat('d F Y') . ' <PERSON><PERSON>h Arası Bayi Raporu')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-body">

                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr>
                            <th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th>Bayi</th>
                            <th>Bireysel</th>
                            <th>Plus Card</th>
                            <th>Toplam Ciro</th>
                            <th>İL</th>
                            <th>BÖLGE</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($sortedResults as $key => $item)
                            <tr>
                                <td>{{ $key + 1 }}</td>
                                <td>{{ $item['kisa_ad'] }}</td>
                                <td>
                                    ₺{{ number_format($item['bireysel_amount'] ?? 0, 0, ',', '.') }}
                                    @php
                                        $bireyselDiff = $item['bireysel_diff'] ?? 0;
                                        $bireyselIcon = $bireyselDiff >= 0 ? 'bx bx-caret-up' : 'bx bx-caret-down';
                                        $bireyselColor = $bireyselDiff >= 0 ? 'success' : 'danger';
                                    @endphp
                                    <i class="{{ $bireyselIcon }} mx-1 text-{{ $bireyselColor }} small"></i>
                                    <small class="fw-semibold text-{{ $bireyselColor }}">
                                        ₺{{ number_format(abs($bireyselDiff), 0, ',', '.') }}
                                    </small>
                                </td>
                                <td>
                                    ₺{{ number_format($item['plus_card_amount'] ?? 0, 0, ',', '.') }}
                                    @php
                                        $plusDiff = $item['plus_diff'] ?? 0;
                                        $plusIcon = $plusDiff >= 0 ? 'bx bx-caret-up' : 'bx bx-caret-down';
                                        $plusColor = $plusDiff >= 0 ? 'success' : 'danger';
                                    @endphp
                                    <i class="{{ $plusIcon }} mx-1 text-{{ $plusColor }} small"></i>
                                    <small class="fw-semibold text-{{ $plusColor }}">
                                        ₺{{ number_format(abs($plusDiff), 0, ',', '.') }}
                                    </small>
                                </td>
                                <td>
                                    ₺{{ number_format($item['total_amount'] ?? 0, 0, ',', '.') }}
                                    @php
                                        $ciroDiff = $item['total_diff'] ?? 0;
                                        $ciroIcon = $ciroDiff >= 0 ? 'bx bx-caret-up' : 'bx bx-caret-down';
                                        $ciroColor = $ciroDiff >= 0 ? 'success' : 'danger';
                                    @endphp
                                    <i class="{{ $ciroIcon }} mx-1 text-{{ $ciroColor }} small"></i>
                                    <small class="fw-semibold text-{{ $ciroColor }}">
                                        ₺{{ number_format(abs($ciroDiff), 0, ',', '.') }}
                                    </small>
                                </td>
                                <td>{{ $item['il_name'] ?? '' }}</td>
                                <td>{{ $item['zone_name'] ?? '' }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

    <script>
        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })
        });
    </script>
@endpush
