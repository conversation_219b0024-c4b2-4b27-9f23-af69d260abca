@extends('pages.build')

@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>

        span.select2-container.select2-container--default.select2-container--open {
            z-index: 9999;
        }
        .paynetj-button{
            display: none;
        }
    </style>
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Plus Card Detay')
@section('content')
    <input type="hidden" value="{{$empty_user_information}}" name="empty_user_information">
    @if((in_array('*', $userRoles) || in_array('edit_plus_card', $userRoles) || in_array('credits_plus_card', $userRoles)))
     <form method="post" action="{{ route('plus-cards.update',$plusCard) }}">@csrf @method('put')
    @endif
        <div class="row">
            <div class="col-md-4">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="temelBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#temelBilgiler" aria-expanded="true"
                                    aria-controls="temelBilgiler">
                                        Kart Bilgileri =>
                                        {{ $plusCard->getCustomer ? formatTelephoneNumber($plusCard->getCustomer->telefon) : 'Telefon Bulunamadı' }}
                            </button>
                        </h2>
                        <div id="temelBilgiler" class="accordion-collapse collapse show" aria-labelledby="temelBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kart ID</label>
                                        <input type="text" class="form-control  form-control-sm" {{ $authUser->type != 'admin' ? 'readonly' : 'required' }}  name="system_id" placeholder="Kart ID için kartı okutunuz " value="{{ $plusCard->system_id }}">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kart Numarası<span class="text-danger">*</span></label>
                                        <input type="text" class="form-control form-control-sm" @if($authUser->type != 'admin') readonly @endif  name="no" placeholder="Kart numarası" value="{{ $plusCard->no }}" required>
                                    </div>
                                    <div class="col-xl-12">
                                        <div class="form-group mt-2">
                                            <label class="form-label">Bayi<span class="text-danger">*</span></label>
                                            <input type="hidden" name="branch_id" value="{{ $plusCard->branch_id }}">
                                            <input type="text" class="form-control form-control-sm search-branch" required @if($authUser->type != 'admin') readonly @endif value="{{ $plusCard->getBranch ? $plusCard->getBranch->unvan : '' }}" placeholder="Aramak için yazınız (En az 3 kelime)">
                                            <div class="filter-results find-branch"></div>
                                        </div>
                                    </div>
                                    <div class="col-xl-12">
                                        <div class="col-12">
                                            <label class="form-label">Cari<span class="text-danger">*</span></label>

                                        </div>
                                        <div class="row">
                                            <div class="form-group mt-2 col-10">
                                                <div class="d-flex">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="customer_search_type"
                                                               value="cep" id="saticiSearchTypeGsm">
                                                        <label class="form-check-label" for="saticiSearchTypeGsm">
                                                            GSM
                                                        </label>
                                                    </div>
                                                    <div class="form-check mx-3">
                                                        <input class="form-check-input" type="radio" name="customer_search_type"
                                                               value="cari_kod" id="saticiSearchTypePlusCard">
                                                        <label class="form-check-label" for="saticiSearchTypePlusCard">
                                                            Yeni Cari Kod
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" checked type="radio"
                                                               name="customer_search_type" value="unvan" id="saticiSearchTypeUnvan">
                                                        <label class="form-check-label" for="saticiSearchTypeUnvan">
                                                            Unvan
                                                        </label>
                                                    </div>
                                                </div>
                                            <input type="hidden" name="customer_id" value="{{ $plusCard->customer_id }}">
                                            <input type="text" class="form-control form-control-sm search-customer" value="{{ $plusCard->getCustomer ? $plusCard->getCustomer->unvan : '' }}" placeholder="Aramak için yazınız (En az 3 kelime)">
                                            <div class="filter-results find-customer"></div>
                                        </div>
                                            <div class="col-2">
                                                <button type="button" class="btn  btn-sm btn-primary customer-search mt-3">Ara</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kart ID</label>
                                        <input type="text" class="form-control  form-control-sm" {{ $authUser->type != 'admin' ? 'readonly' : 'required' }}  name="system_id" placeholder="Kart ID için kartı okutunuz " value="{{ $plusCard->system_id }}">
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Son Geçerlilik Tarihi</label>
                                        <select name="valid_date_if" class="form-control form-control-sm mb-2" id="valid_date_if">
                                            <option @if($plusCard->valid_date_if == 1 || old('valid_date_if') == 1) selected @endif value="1">Son Geçerlilik Tarihi Uygulansın</option>

                                            @if($plusCard->valid_date_if == 0 || $authUser->type == "admin")
                                                <option @if($plusCard->valid_date_if == 0 || old('valid_date_if') === 0) selected @endif value="0">Son Geçerlilik Tarihi Uygulanmasın</option>
                                            @endif
                                        </select>
                                        <div class="valid_date_input" @if($plusCard->valid_date_if == 0)  style="display: none" @endif>
                                            <input type="date" class="form-control form-control-sm" name="valid_date" value="{{ !empty($plusCard->valid_date) ? $plusCard->valid_date : now()->addYear()->format('Y-m-d') }}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 mt-2 col-xl-12 col-xs-12 col-sm-12">
                    @if((in_array('*', $userRoles) || in_array('edit_plus_card', $userRoles) || in_array('credits_plus_card', $userRoles)))
                        <button class="btn btn-success btn-sm mb-3 saveButton">Kaydet</button>
                        @if($balance_add_disabled == 0)
                            <button type="button" class="btn btn-sm btn-primary mb-3 plusCardAddBalance">Bakiye Yükle</button>
                        @endif
                    @endif
                    <button type="button" class="btn btn-sm btn-warning mb-3 plus_card_balance_detail_upload">Hesap Haraketlerini Gör</button>
                </div>
            </div>

            <div class="col-md-8">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="paketHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#paket" aria-expanded="true"
                                    aria-controls="paket">
                                Yüklemeler
                            </button>
                        </h2>
                        <div id="temelBilgiler" class="accordion-collapse collapse show" aria-labelledby="paketHeading">
                            <div class="accordion-body">
                                <table class="table table-bordered table-sm table-striped table-responsive">
                                    <thead>
                                        <tr>
                                            <th>Tanım Adı</th>
                                            <th>Yük.</th>
                                            <th>Düş.</th>
                                            <th>Kul.</th>
                                            <th>Kalan</th>
                                            <th>Tür</th>
                                            <th>Yük. Yapılan Şube</th>
                                            <th>Bayi</th>
                                            <th>Yük. Tarihi</th>
                                            <th>Son K. Tarihi</th>
                                            <th>Yük.Birim Fiyatı</th>
                                            <th>Açıklama</th>
                                            <th>Ödeme</th>
                                            <th>İşlemler</th>
                                            <th>Bakiye Düş</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($plusCard->getBalance as $balance)
                                            <tr class="have_plus_card_stock_{{$balance->id}}">
                                                <td>@if($balance->definitions_id == 0 && $balance->devir_miktar > 0) Devir @else {{$balance->getDefinitions->definition_name ?? ''}} @endif</td>
                                                <td class="balance{{ $balance->id }}">@if($balance->balance_type == "credits") {{$balance->credi}} @else {{$balance->puan}} @endif</td>
                                                <td>{{ $balance->getRemoves->sum('amount') }}</td>
                                                <td>{{ $balance->getUsages->count() }}</td>
                                                <td>{{ ($balance->balance_type == "credits" ? $balance->credi : $balance->puan) - $balance->getRemoves->sum('amount') - $balance->getUsages->count() }}</td>
                                                <td>@if($balance->balance_type == "credits") Kredi @else Puan @endif</td>
                                                <td>
                                                    @if($balance->getUser == null)
                                                        {{ '' }}
                                                    @elseif($balance->getUser->user_role_group_id == 30)
                                                        {{ $balance->getUser->branches->firstWhere('id', 26)->kisa_ad ?? '' }};
                                                    @else
                                                        {{ $balance->getUser->branches->first()->kisa_ad ?? '' }}
                                                    @endif
                                                </td>
                                                <td>{{!empty($balance->getBranche->kisa_ad) ? $balance->getBranche->kisa_ad : 'Hepsi'}}</td>
                                                <td>{{$balance->created_at}}</td>
                                                <td>{{$balance->valid_date}}</td>
                                                <td>{{number_format((float)$balance->unit_price, 2, ',', '.')}}₺</td>
                                                <td>{{$balance->devir_aciklama}}</td>
                                                <td>{{number_format((float)$balance->odenen_kdv_dahil_fiyat, 2, ',', '.')}}₺</td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-warning plus_card_balance_detail_upload "  data-id="{{$balance->id}}">
                                                        <i class="fa fa-eye"></i>
                                                    </button>
                                                    @if($authUser->type == "admin")
                                                        <button type="button" class="btn btn-sm btn-danger have_plus_card_price_delete" data-id="{{$balance->id}}">
                                                            <i class="fa fa-trash"></i>
                                                        </button>
                                                    @endif
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-danger remove-point-credit btn-sm" data-amount="1" data-id="{{ $balance->id }}">-1</button>
                                                    <button type="button" class="btn btn-danger remove-point-credit btn-sm" data-amount="5" data-id="{{ $balance->id }}">-5</button>
                                                    <button type="button" class="btn btn-danger remove-point-credit btn-sm" data-amount="10" data-id="{{ $balance->id }}">-10</button>
                                                </td>
                                            </tr>
                                      @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @if((in_array('*', $userRoles) || in_array('edit_plus_card', $userRoles) || in_array('credits_plus_card', $userRoles)))
    </form>
    @endif
    <div class="modal fade" id="plusCardBalance" tabindex="-1"
         aria-labelledby="plusCardBalance" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Harcamalar
                    </h6>

                    @if(in_array('*', $userRoles) || in_array('download_excel_plus_card', $userRoles) || $authUser->type == 'admin')
                        <a target="_blank" href="{{ route('plusCardCustomerExport', ['id' => $plusCard->id]) }}" class="btn btn-success btn-sm plus-card-excel" data-pluscardid="{{ $plusCard->id }}" style="margin-left: 5px">
                            <i class="fa fa-file-excel"></i> Excel Çıktı Al
                        </a>
                    @endif

                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body balance_details_modal_body" style="max-height: 500px;overflow-y: scroll">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">Alınan Hizmet</th>
                                <th scope="col">Hizmet Tarihi</th>
                                <th scope="col">Hizmet Bayisi</th>
                                <th scope="col">Muhasebe Kodu</th>
                            </tr>
                            </thead>
                            <tbody>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="plusCardAddBalance" tabindex="-1"
         aria-labelledby="plusCardAddBalance" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Bakiye Yükle
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body " style="max-height: 500px;overflow-y: scroll">
                    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                        <a class="nav-link active" data-bs-toggle="tab" role="tab" href="#credits_and_points" aria-selected="true" @if($authUser->type == 'admin') onclick="$('.submit_balance_form').removeClass('d-none');$('.submit_kredi_devir_form').addClass('d-none');$('.submit_puan_devir_form').addClass('d-none')" @endif>Kredi & Puan Yükle</a>
                        @if(in_array($authUser->id,[224,847,848]))
                            <a class="nav-link" data-bs-toggle="tab" role="tab" href="#krediDevir" aria-selected="false" onclick="$('.submit_balance_form').addClass('d-none');$('.submit_puan_devir_form').addClass('d-none');$('.submit_kredi_devir_form').removeClass('d-none');">Kredi Yükle</a>
                            <a class="nav-link" data-bs-toggle="tab" role="tab" href="#puanDevir" aria-selected="false" onclick="$('.submit_balance_form').addClass('d-none');$('.submit_puan_devir_form').removeClass('d-none');$('.submit_kredi_devir_form').addClass('d-none');">Puan Yükle</a>
                        @endif
                    </nav>
                    <div class="tab-content">
                        <div class="tab-pane show active text-muted" id="credits_and_points" role="tabpanel">
                            <form method="post" id="balance_form" action="{{route('plus_cards_balance')}}">@csrf
{{--                                <input type="hidden" name="return_url" value="{{ url()->full() }}">--}}
                                <input type="hidden" name="add_comission_amount" value="true">
                                <input type="hidden" name="no_instalment" value="false">
                                <input type="hidden" name="tds_required" value="true">
                                <input type="hidden" name="ratio_code" value="">
                                <input type="hidden" name="installments" value="">
                                <input type="hidden" name="reference_no" value="">
                                <input type="hidden" name="plus_card_definition_id" value="">
                                <input type="hidden" name="plus_card_id" value="{{ $plusCard->id }}">
                                <input type="hidden" name="campaign_id" value="">
                                <script
                                    id="paynetScript"
                                    class="paynet-button"
                                    type="text/javascript"
                                    src="https://pj.paynet.com.tr/public/js/paynet.min.js"
                                    data-key="pbk_rPMxYmDP2b8nhT2aZuhxQqyNZoqg"
                                    data-amount=""
                                    data-name="Plus Card Satın Al"
                                    data-image="/assets/umram4.webp"
                                    data-agent="{{ $authUser->getBranch->paynet_code ?? "1697" }}"
                                    data-no_instalment="false"
                                    data-tds_required="true"
                                    data-form="#balance_form"
                                    data-add_commission_amount="true"
                                    data-reference_no=""
                                    data-description=""
                                >
                                </script>

                                <input type="hidden" name="return_url" value="@if(!empty($_GET['return_url'])) {{$_GET['return_url']}} @else {{ url()->full() }} @endif">
                                <input type="hidden" name="plus_card_id" value="{{$plusCard->id}}">
                                <input type="hidden" name="balance_stock_id"  class="balance_stock_id" value="">
                                <input type="hidden" name="delayed_id" value="{{$plus_card_agreement->delayed_id ?? ''}}">
                                <div class="col-12">
                                    <div class="row">
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label for="credits_balance">Kredi Yükle</label>
                                                <input type="radio" checked="" name="balance_type" value="credits">
                                            </div>
                                            @if(in_array('*', $userRoles) || in_array('points_plus_card', $userRoles))
                                                <div class="form-group">
                                                    <label for="credits_balance">Puan Yükle</label>
                                                    <input type="radio" name="balance_type" value="points">
                                                </div>
                                            @endif
                                        </div>
                                    </div>


                                    <div class="col-12 credits-tabs">
                                        <div class="row">
                                            <div class="col-lg-8 col-md-8 col-xl-8 col-12">
                                                <div class="row">
                                                    <div class="form-group">
                                                        <label for="">Kredi Tipi</label>
                                                        <select name="plus_cards_definitions" class="form-control form-control-sm select2" required="" id="plus_cards_definitions">
                                                            <option value="">Lütfen Seçim Yapınız</option>
                                                            @foreach($plusCardDefinitions->where('show_in_view',1) as $pcd)
                                                                <option data-stockid="{{$pcd->stock_id}}" data-quantity="{{$pcd->unit_quantity}}" data-price="{{$pcd->unit_price}}" data-commissionrate="{{$pcd->commission}}" data-invoicecode="{{$pcd->definition_invoice_code}}" value="{{$pcd->id}}">{{$pcd->definition_name}}</option>
                                                            @endforeach
                                                            @if($authUser->user_role_group_id == 39)
                                                                @foreach($plusCardDefinitions->where('show_in_view',0) as $pcd)
                                                                    <option data-stockid="{{$pcd->stock_id}}" data-quantity="{{$pcd->unit_quantity}}" data-price="{{$pcd->unit_price}}" data-commissionrate="{{$pcd->commission}}" data-invoicecode="{{$pcd->definition_invoice_code}}" value="{{$pcd->id}}">{{$pcd->definition_name}}</option>
                                                                @endforeach
                                                            @endif
                                                        </select>
                                                    </div>
                                                    <div class="form-group col-xl-3 col-12">
                                                        <label for="">Kredi Miktarı</label>
                                                        @if($authUser->isAdmin())
                                                            <input type="number" min="1" name="unit_quantity" class="unit_quantity form-control-sm form-control" @if($authUser->type != "admin") readonly @endif value="">
                                                        @else
                                                            <input type="number" min="5" step="5" name="unit_quantity" class="unit_quantity form-control-sm form-control" value="">
                                                        @endif
                                                    </div>
                                                    <div class="form-group col-xl-3 col-12">
                                                        <label for="">Kredi Fiyatı</label>
                                                        <input type="text" name="unit_price" class="credits_price form-control-sm form-control" readonly  value="">
                                                    </div>
                                                    <div class="form-group col-xl-3 col-12">
                                                        <label for="">Kredi Tutarı</label>
                                                        <input type="text" name="credits_amount" class="credits_amount form-control-sm form-control" readonly  value="">
                                                    </div>
                                                    <div class="form-group col-xl-3 col-12">
                                                        <label for="">Komisyon</label>
                                                        <input type="text" name="commission" class="commission form-control-sm form-control" readonly  value="">
                                                    </div>
                                                    @if($campaigns->isNotEmpty())
                                                        <div class="form-group">
                                                            <label for="">Kullanılabilir Kampanyalar</label>
                                                            <select name="plus_card_campaigns" class="form-control form-control-sm" required="" id="plus_card_campaigns">
                                                                <option value="">Lütfen Seçim Yapınız</option>
                                                                @foreach($campaigns as $campaign)
                                                                    <option value="{{$campaign->id}}">{{$campaign->title}}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                    @endif
                                                    <div class="col-14 form-group">
                                                        <label for="">Ödeme Tipi</label>
                                                        <select name="payment_type" class="form-control form-control-sm" id="payment_type">
{{--                                                            <option selected="" value="nakit">Nakit</option>--}}
{{--                                                            <option value="banka">Banka</option>--}}
                                                            <option value="kredi_karti">Kredi Kartı</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-xl-4 col-12 form-group">
                                                        <label for="">Hesap Kod</label>
                                                        <input type="text" class="form-control form-control-sm invoice_code" readonly name="invoice_code" >
                                                    </div>
                                                    <div class="col-xl-4 col-12 form-group">
                                                        <label for="">Hesap Adı</label>
                                                        <select name="case" class="form-control form-control-sm" id="case">
                                                            <option value="">Lütfen Seçim Yapınız</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-xl-4 col-12 form-group">
                                                        <label for="">Tahsilat Tutarı</label>
                                                        <input type="text" name="payment_amount" readonly="" class="form-control form-control-sm payment_amount">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-4 col-md-4 col-xl-4 col-12">
                                                <div class="row">
                                                    <div class="col-12">
                                                        <label for="">Kullanım Yetkisi</label>
                                                        <select name="valid_branch" class="select2" id="valid_branch">
                                                            <option value="0">Hepsi</option>
                                                            @foreach($branches as $b)
                                                                <option value="{{$b->id}}">{{$b->unvan}}</option>
                                                            @endforeach
                                                        </select>
                                                    </div>
                                                    <div class="col-12 mt-3">
                                                        <label for="">Son Kullanma Tarihi</label>
                                                        <input type="date" name="valid_date" class="form-control credits-valid-date form-control-sm" value="">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @if(in_array('*', $userRoles) || in_array('points_plus_card', $userRoles))
                                        <div class="col-12 points-tabs" style="display: none">
                                            <div class="row">
                                                <div class="col-xl-3 col-12">
                                                    <label for="">Puan Geçerlilik Bayi</label>
                                                    <select name="point_branche" class="select2" id="point_branche">
                                                        <option value="">Lütfen Seçim Yapın</option>
                                                        @foreach($branches as $b)
                                                            <option value="{{$b->id}}">{{$b->unvan}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-xl-3 col-12">
                                                    <label for="">Kredi Tipi</label>
                                                    <select name="plus_cards_definitions_point" class="form-control form-control-sm select2" required="" id="plus_cards_definitions">
                                                        <option value="">Lütfen Seçim Yapınız</option>
                                                        @foreach($plusCardDefinitions->where('show_in_view',1) as $pcd)
                                                            <option data-stockid="{{$pcd->stock_id}}" data-quantity="{{$pcd->unit_quantity}}" data-price="{{$pcd->unit_price}}" data-commissionrate="{{$pcd->commission}}" data-invoicecode="{{$pcd->definition_invoice_code}}" value="{{$pcd->id}}">{{$pcd->definition_name}}</option>
                                                        @endforeach
                                                        @if($authUser->user_role_group_id == 39)
                                                            @foreach($plusCardDefinitions->where('show_in_view',0) as $pcd)
                                                                <option data-stockid="{{$pcd->stock_id}}" data-quantity="{{$pcd->unit_quantity}}" data-price="{{$pcd->unit_price}}" data-commissionrate="{{$pcd->commission}}" data-invoicecode="{{$pcd->definition_invoice_code}}" value="{{$pcd->id}}">{{$pcd->definition_name}}</option>
                                                            @endforeach
                                                        @endif
                                                    </select>
                                                </div>
                                                <div class="col-xl-3 col-12">
                                                    <label for="">Puan Geçerli Hizmet</label>
                                                    <select name="point_stocks_id" class="select2" id="point_stocks_id">
                                                        <option value="">Lütfen Seçim Yapın</option>
                                                        @foreach($stocks as $s)
                                                            <option value="{{$s->id}}">{{$s->ad}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-xl-3 col-12">
                                                    <label for="">Puan Geçerlilik Tarih</label>
                                                    <input type="date" class="form-control form-control-sm" name="point_valid_date">
                                                </div>
                                                <div class="col-xl-3 col-12">
                                                    <label for="">Puan </label>
                                                    <input type="number" class="form-control form-control-sm" name="point_quantity">
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </form>
                        </div>
                        @if(in_array($authUser->id,[224,847,848]))
                            <div class="tab-pane text-muted" id="krediDevir" role="tabpanel">
                                <form method="post" id="krediDevirForm" action="{{route('plus_cards_balance')}}">@csrf
                                    <input type="hidden" name="return_url" value="@if(!empty($_GET['return_url'])) {{$_GET['return_url']}} @endif">
                                    <input type="hidden" name="plus_card_id" value="{{$plusCard->id}}">
                                    <input type="hidden" name="balance_stock_id"  class="balance_stock_id" value="">
                                    <input type="hidden" name="balance_type" value="devir_credits">
                                    <div class="col-12">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-lg-8 col-md-8 col-xl-8 col-12">
                                                    <div class="row">
                                                        <div class="form-group col-xl-3 col-12">
                                                            <label for="">Kredi Miktarı</label>
                                                            <input type="number" min="1" name="devir_unit_quantity" class="form-control form-control-sm" value="">
                                                        </div>
                                                        <div class="form-group col-xl-3 col-12">
                                                            <label for="">Kredi Fiyatı</label>
                                                            <input type="text" name="devir_unit_price" class="form-control form-control-sm"  value="">
                                                        </div>
                                                        <div class="form-group col-xl-3 col-12">
                                                            <label for="">Kredi Tutarı</label>
                                                            <input type="text" name="devir_credits_amount" class="form-control form-control-sm" readonly  value="">
                                                        </div>
                                                        <div class="form-group col-xl-3 col-12">
                                                            <label for="">Komisyon</label>
                                                            <input type="text" name="devir_commission" class="form-control form-control-sm"  value="0">
                                                        </div>
                                                        <div class="col-14 form-group">
                                                            <label for="">Ödeme Tipi</label>
                                                            <select name="devir_payment_type" class="form-control form-control-sm" id="devir_payment_type">
                                                                <option selected="" value="nakit">Nakit</option>
                                                                <option value="banka">Banka</option>
                                                                <option value="kredi_karti">Kredi Kartı</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-xl-4 col-12 form-group">
                                                            <label for="">Hesap Kod</label>
                                                            <input type="text" class="form-control form-control-sm" readonly name="devir_invoice_code" >
                                                        </div>
                                                        <div class="col-xl-4 col-12 form-group">
                                                            <label for="">Hesap Adı</label>
                                                            <select name="devir_case" class="form-control form-control-sm" id="case">
                                                                <option value="">Lütfen Seçim Yapınız</option>
                                                            </select>
                                                        </div>
                                                        <div class="col-xl-4 col-12 form-group">
                                                            <label for="">Tahsilat Tutarı</label>
                                                            <input type="text" name="devir_payment_amount" readonly="" class="form-control form-control-sm payment_amount">
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-lg-4 col-md-4 col-xl-4 col-12">
                                                    <div class="row">
                                                        <div class="col-12">
                                                            <label for="">Kullanım Yetkisi</label>
                                                            <select name="devir_valid_branch" class="select2" id="valid_branch">
                                                                <option value="0">Hepsi</option>
                                                                @foreach($branches as $b)
                                                                    <option value="{{$b->id}}">{{$b->unvan}}</option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                        <div class="col-12 mt-3">
                                                            <label for="">Son Kullanma Tarihi</label>
                                                            <input type="date" name="devir_valid_date" class="form-control form-control-sm" value="">
                                                        </div>
                                                        <div class="col-12 d-none mt-3">
                                                            <label for="">Devir Tarihi</label>
                                                            <input type="date" name="devir_tarih" class="form-control form-control-sm" value="">
                                                        </div>
                                                        <div class="col-12 mt-3">
                                                            <label for="devir_aciklama">Açıklaması</label>
                                                            <textarea class="form-control" id="devir_aciklama" name="devir_aciklama" required></textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="tab-pane text-muted" id="puanDevir" role="tabpanel">
                                <form method="post" id="puanDevirForm" action="{{route('plus_cards_balance')}}">@csrf
                                    <input type="hidden" name="return_url" value="@if(!empty($_GET['return_url'])) {{$_GET['return_url']}} @endif">
                                    <input type="hidden" name="plus_card_id" value="{{$plusCard->id}}">
                                    <input type="hidden" name="balance_stock_id"  class="balance_stock_id" value="">

                                    <input type="hidden" name="balance_type" value="devir_points">
                                    <div class="col-12">
                                        <div class="col-12 points-tabs">
                                            <div class="row">
                                                <div class="col-xl-2 col-12">
                                                    <label for="">Puan Geçerlilik Bayi</label>
                                                    <select name="devir_point_branche" class="select2" id="point_branche">
                                                        <option value="">Lütfen Seçim Yapın</option>
                                                        @foreach($branches as $b)
                                                            <option value="{{$b->id}}">{{$b->unvan}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-xl-2 col-12">
                                                    <label for="">Kredi Tipi</label>
                                                    <select name="devir_plus_cards_definitions_point" class="form-control select2" required="" id="plus_cards_definitions">
                                                        <option value="">Lütfen Seçim Yapınız</option>
                                                        @foreach($plusCardDefinitions as $pcd)
                                                            <option data-stockid="{{$pcd->stock_id}}" data-quantity="{{$pcd->unit_quantity}}" data-price="{{$pcd->unit_price}}" data-commissionrate="{{$pcd->commission}}" data-invoicecode="{{$pcd->definition_invoice_code}}" value="{{$pcd->id}}">{{$pcd->definition_name}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-xl-2 col-12">
                                                    <label for="">Puan Geçerli Hizmet</label>
                                                    <select name="point_stocks_id" class="select2" id="point_stocks_id">
                                                        <option value="">Lütfen Seçim Yapın</option>
                                                        @foreach($stocks as $s)
                                                            <option value="{{$s->id}}">{{$s->ad}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-xl-2 col-12">
                                                    <label for="">Puan Geçerlilik Tarih</label>
                                                    <input type="date" class="form-control form-control-sm" name="devir_point_valid_date">
                                                </div>
                                                <div class="col-xl-2 col-12">
                                                    <label for="">Puan </label>
                                                    <input type="number" class="form-control form-control-sm" name="devir_point_quantity">
                                                </div>
                                                <div class="col-xl-2 d-none col-12">
                                                    <label for="">Devir Tarihi</label>
                                                    <input type="date" name="devir_tarih" class="form-control form-control-sm" value="">
                                                </div>
                                                <div class="col-12 mt-3">
                                                    <label for="devir_aciklama">Açıklaması</label>
                                                    <textarea class="form-control" id="devir_aciklama" name="devir_aciklama" required></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        @endif
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success btn-sm submit_balance_form" >Kaydet</button>
                    @if(in_array($authUser->id,[224,847,848]))
                        <button type="submit" class="btn btn-success btn-sm d-none submit_kredi_devir_form" form="krediDevirForm">Kaydet</button>
                        <button type="submit" class="btn btn-success btn-sm d-none submit_puan_devir_form" form="puanDevirForm">Kaydet</button>
                    @endif
                    <button type="button" class="btn btn-danger btn-sm" data-bs-dismiss="modal" aria-label="Close">İptal Et</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="paymentModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 id="myModalLabel">Sözleşme Kabul</h3>
                </div>
                <div class="modal-body">
                    <div class="col-12 text-center">
                        <button type="button" class="btn btn-success btn-sm send_agreement">
                            Sözleşme Gönder
                        </button>
                        <button type="button" class="btn btn-warning btn-sm sendAgreementCheck">
                            Sözleşme Onay Kontrol Et
                        </button>
                        <button type="button" class="btn btn-primary btn-sm credi_card_payment_cancel">
                            İptal Et
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="paymentModalCard" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <form action="{{ url('paynet') }}" id="paynetForm" method="post">
                @csrf
                <input type="hidden" name="payment_type" value="2">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3 id="myModalLabel">Ödeme AL</h3>
                    </div>
                    <div class="modal-body">
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="modal fade" id="userInfoModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <!-- Modal body -->
                <div class="modal-header">
                    <h5>Kullanıcı Bilgileri</h5>
                </div>
                <div class="modal-body ">
                    <form class="row"  method="post" name="userInformationForm" id="userInformationForm" >@csrf
                        <input type="hidden" name="customer_id" value="{{!empty($plusCard->getCustomer) ? $plusCard->getCustomer->id : ''}}">
                        <div class="form-group col-xl-6">
                                <label for="">Mahalle</label>
                                <input type="text" placeholder="Mahalle" name="mahalle" class="form-control form-control-sm" required="" value="{{$plusCard->getCustomer->mahalle ?? ''}}">
                            </div>
                            <div class="form-group col-xl-6">
                                <label for="">Cadde</label>
                                <input type="text" placeholder="Cadde" name="cadde" class="form-control form-control-sm" required="" value="{{$plusCard->getCustomer->cadde ?? ''}}">
                            </div>
                            <div class="form-group col-xl-6">
                                <label for="">Sokak</label>
                                <input type="text" placeholder="Sokak" name="sokak" class="form-control form-control-sm" required="" value="{{$plusCard->getCustomer->sokak ?? ''}}">
                            </div>
                            <div class="form-group col-xl-6">
                                <label for="">Semt</label>
                                <input type="text" placeholder="Semt" name="semt" class="form-control form-control-sm" required="" value="{{$plusCard->getCustomer->semt ?? ''}}">
                            </div>
                            <div class="form-group col-xl-6">
                                <label for="">İl</label>
                                <select name="city_id" id="city_id" class="select2User" required="">
                                    <option value="">Lütfen Seçim Yapınız</option>
                                    @foreach($cities as $c)
                                        <option @if(!empty($plusCard->getCustomer) && $plusCard->getCustomer->il_kodu == $c->id) selected="" @endif value="{{$c->id}}">{{$c->title}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-group col-xl-6">
                                <label for="">İlçe</label>
                                <select name="ilce_id" id="ilce_id" class="select2User" required="">
                                    <option value="">Lütfen Seçim Yapınız</option>
                                    @foreach($counties as $c)
                                        <option @if(!empty($plusCard->getCustomer) && $plusCard->getCustomer->ilce_kodu == $c->id) selected="" @endif value="{{$c->ilce_id}}">{{$c->ilce_title}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-group col-xl-6">
                                <label for="">Telefon</label>
                                <input type="text" class="form-control form-control-sm" name="telefon" placeholder="Telefon" value="{{$plusCard->getCustomer->telefon ?? ''}}" required="">
                            </div>
                            <div class="form-group col-xl-6">
                                <label for="">Vergi Dairesi</label>
                                <input type="text" class="form-control form-control-sm" name="vergi_dairesi" placeholder="Vergi Dairesi" value="{{$plusCard->getCustomer->vergi_dairesi ?? ''}}" required="">
                            </div>
                            <div class="form-group col-xl-6">
                                <label for="">T.C/Vergi No</label>
                                <input type="text" class="form-control form-control-sm" name="tc_no" placeholder="T.C/Vergi No" value="{{$plusCard->getCustomer->vergi_no ?? ''}}" required="">
                            </div>
                            <div class="form-group col-xl-6">
                                <label for="">Mersis</label>
                                <input type="text" class="form-control form-control-sm" name="mersis_no" placeholder="Mersis" {{$plusCard->getCustomer->mersis ?? ''}}>
                            </div>
                            <div class="form-group col-xl-12 text-center">
                                <button type="submit" class="btn btn-sm btn-success">Kullanıcı Bilgileri Kaydet Ve Ödeme Adımına Geç</button>
                                <button type="button" class="btn btn-sm btn-danger cancel_user_information_update">İptal Et</button>
                            </div>
                    </form>
                </div>

            </div>
        </div>
    </div>

    <div class="modal fade" id="searchCustomerModal" tabindex="-1"
         aria-labelledby="searchCustomerModal" data-bs-keyboard="false"
         aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Bulunan Sonuçlar
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body" style="max-height: 300px;overflow: auto; padding-top: 0px;">
                    <table class="table table-striped table-responsive">
                        <thead style="position: sticky;top: 0">
                        <tr>
                            <th>Hesap Kodu</th>
                            <th>Ad Soyad</th>
                            <th>Bayi</th>
                            <th>Telefon</th>
                            <th>T.C/Vergi No</th>
                            <th>Plus Card</th>
                        </tr>
                        </thead>
                        <tbody class="filter-results-new">

                        </tbody>
                    </table>
                    <table id="header-fixed"></table>
                </div>
                <div class="modal-footer cari">

                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="searchUnusedCardModal" tabindex="-1"
         aria-labelledby="searchUnusedCardModal" data-bs-keyboard="false"
         aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Yeni Kart Tanımla
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body" style="max-height: 300px;overflow: auto; padding-top: 0px;">
                    <table class="table table-striped table-responsive" id="unused-card-table">
                        <thead style="position: sticky;top: 0">
                        <tr>
                            <th></th>
                            <th>Atanmamış Kart No</th>
                            <th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody class="unused-cards">

                        </tbody>
                    </table>
                    <p id="unused-card-note">Kart tanımlaması yapılmıştır. Lütfen yeni kartı müşterimize teslim ediniz.</p>
                </div>
                <div class="modal-footer">

                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/js/select2.min.js"></script>

    <script>
        $(document).ready(function(){
            function isValidCardNumber(cardNumber) {
                // Remove spaces before validation
                return /^04445417\d{4}\d{4}$/.test(cardNumber.replace(/\s/g, '')); // Validate format test() returns true or false
            }

            function isValidCardID(cardID) {
                return /^\d{7,12}$/.test(cardID); // Kart ID must be 7 digits and more
            }

            // Auto-format card number input (add spaces after every 4 digits)
            $('input[name="no"]').on('input', function() {
                let value = $(this).val().replace(/\D/g, ''); // Remove non-digits
                let formattedValue = value.replace(/(.{4})/g, '$1 ').trim(); // Add space after every 4 digits
                $(this).val(formattedValue);
            });

            //Validate when clicking "Kaydet or Bakiye Yükle" button
            // Note: card ID can be junk fake number sometimes
            $('.saveButton,.plusCardAddBalance').on('click', function (event) {
                // Check for valid card number and ID format
                let cardNumberInput = $('input[name="no"]');
                let cardNumber = cardNumberInput.val().trim();
                let cardIDInput = $('input[name="system_id"]');
                let cardID = cardIDInput.val().trim();

                if (!isValidCardID(cardID)) {
                    event.preventDefault(); // Stop form submission

                    Swal.fire({
                        title: "Geçersiz Kart ID Numarası!",
                        text: "Kart ID 7 ile 12 arası karakter uzunluğunda olmalıdır. Lütfen girdiğiniz kart ID alanını konrol ediniz veya yeni kart oluşturunuz.",
                        icon: "warning",
                        confirmButtonText: "Yeni Kart",
                        showCancelButton: true,
                        cancelButtonText: "İptal",
                    }).then((result) => {
                        cardIDInput.focus(); // Focus on the input field

                        if (result.isConfirmed) {
                            getUnusedCards();
                        }
                    })

                    return false; //  Stop further execution
                }

                if (!isValidCardNumber(cardNumber)) {
                    event.preventDefault(); // Stop form submission

                    Swal.fire({
                        title: "Geçersiz Kart Numarası!",
                        text: "Kart numarası 0444 5417 XXXX XXXX formatında olmalıdır. Lütfen doğru formatta giriniz.",
                        icon: "warning",
                        confirmButtonText: "Yeni Kart",
                        showCancelButton: true,
                        cancelButtonText: "İptal",
                    }).then((result) => {
                        cardNumberInput.focus(); // Focus on the input field

                        if (result.isConfirmed) {
                            getUnusedCards();
                        }
                    });

                    return false; //  Stop further execution
                }
                // If the clicked button is "Bakiye Yükle", open the modal only if validation passes
                if ($(this).hasClass('plusCardAddBalance')) {
                    $('#plusCardAddBalance').modal('show');
                }
                // If valid, allow form submission
            });

            @if($authUser->type == 'admin')
                $('input[name="devir_unit_quantity"]').on('change',function (){
                    let $quantity = parseFloat($('input[name="devir_unit_quantity"]').val());
                    let $price = parseFloat($('input[name="devir_unit_price"]').val());
                    if ($price && $quantity){
                        let credits_amount = $quantity * $price;
                        $('input[name="devir_credits_amount"]').val(credits_amount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }))
                    }
                })
                $('input[name="devir_unit_price"]').on('change',function (){
                    let $quantity = parseFloat($('input[name="devir_unit_quantity"]').val());
                    let $price = parseFloat($('input[name="devir_unit_price"]').val());
                    if ($price && $quantity){
                        let credits_amount = $quantity * $price;
                        $('input[name="devir_credits_amount"]').val(credits_amount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }))
                    }
                })

                $('.remove-point-credit').on('click',function (){
                    let $amount = $(this).data('amount')
                    let $id = $(this).data('id')
                    $.ajax({
                        type: 'POST',
                        url: '{{route('api.removePointOrCredits')}}',
                        data: {'_token':'{{ csrf_token() }}','amount':$amount,'id':$id},
                        success: function(response) {
                            let swalIcon;
                            if(response.success == 'false'){
                                swalIcon = 'error'
                            }else{
                                swalIcon = 'success'

                                setTimeout(function (){
                                    window.location.reload()
                                },1000)
                            }

                            Swal.fire({
                                text: response.message,
                                icon:swalIcon,
                            });


                        }
                    });
                })
            @endif

            if (typeof Toast === 'undefined') {
                var Toast = Swal.mixin({
                    toast: true,
                    position: "top-end",
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.onmouseenter = Swal.stopTimer;
                        toast.onmouseleave = Swal.resumeTimer;
                    }
                });
            }

            $('select[name="point_stocks_id"]').on('change',function (){
                let $val = $(this).val();
                $.ajax({
                    type: 'POST',
                    url: '{{route('api.getTowns')}}',
                    data: {
                        city_id:city_id,
                        _token:'{{csrf_token()}}',
                    },
                    success: function(response) {
                        console.log(response)
                        if(response.success){
                            var option_html = '';
                            $.each(response.items, function (index, item) {
                                option_html += '<option value="' + item.ilce_id + '">' + item.ilce_title + '</option>';
                            })

                            $('#ilce_id').html(option_html)
                            $('#ilce_id').trigger('change')
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error(xhr.responseText);
                    }
                });
            })

            $('#city_id').change(function(){
                var city_id = $(this).val();
                $.ajax({
                    type: 'POST',
                    url: '{{route('api.getTowns')}}',
                    data: {
                        city_id:city_id,
                        _token:'{{csrf_token()}}',
                    },
                    success: function(response) {
                        console.log(response)
                        if(response.success){
                            var option_html = '';
                            $.each(response.items, function (index, item) {
                                option_html += '<option value="' + item.ilce_id + '">' + item.ilce_title + '</option>';
                            })

                            $('#ilce_id').html(option_html)
                            $('#ilce_id').trigger('change')
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error(xhr.responseText);
                    }
                });
            });
            $('#userInformationForm').submit(function(event) {
                event.preventDefault();
                var formData = $(this).serialize();
                $.ajax({
                    type: 'POST',
                    url: '{{route('api.userInformationUpdate')}}',
                    data: formData,
                    success: function(response) {
                        if(response.success){
                            $('input[name=empty_user_information]').val(2)
                            $('#userInfoModal').modal('hide');
                            sendAgreement()
                            $('#paymentModal').modal('show');
                        }else{
                            Swal.fire({
                                title: "Uyarı!",
                                text: 'Bir Hata Oluştu!',
                                icon: "error",
                                confirmButtonText: "Tamam"

                            });
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error(xhr.responseText);
                    }
                });
            });
            $('.cancel_user_information_update').click(function(){
                $('#userInfoModal').modal('hide');
                $('#plusCardAddBalance').modal('show');
            })
            $('.send_agreement').click(function(){
                $(this).attr('disabled','')
                sendAgreement();
            });



            $('.sendAgreementCheck').click(function(){
                $(this).attr('disabled','')
                var delayed_id = $('input[name=delayed_id]').val();

                $.ajax({
                    type: 'POST',
                    url: '{{route('api.sendAgreementCheck')}}',
                    data: {
                        delayed_id:delayed_id,
                        plus_cards_definitions:$('select[name="plus_cards_definitions"]').val(),
                        campaign_id: $('input[name="campaign_id"]').val(),
                        plus_card_id: $('input[name="plus_card_id"]').val(),
                        quantity: $('input[name=unit_quantity]').val(),
                        _token:'{{csrf_token()}}',
                    },
                    success: function(response) {
                        if(response.success){
                            var payment_type = $('select[name=payment_type]').val();
                            var balance_type = $('input[name=balance_type]:checked').val();;
                            if(payment_type == "kredi_karti" && balance_type == 'credits'){
                                var payment_amount = response.pay;
                                // var unit_price_cleaned = payment_amount.replace(/[^\d.,-]/g, '');
                                // unit_price_cleaned = unit_price_cleaned.replace('.', '').replace(',', '.');
                                // unit_price_cleaned = unit_price_cleaned.replace('.', '');
                                // payment_amount = unit_price_cleaned;

                                document.querySelectorAll('.paynet-button').forEach(function (script){
                                    // Transfer data attributes from the button to the script
                                    script.setAttribute('data-amount', payment_amount);
                                    script.setAttribute('data-button_label', 'Bakiye Yükle');
                                    script.setAttribute('data-reference_no', {{ $plusCard->id }});
                                    script.setAttribute('data-description', 'Plus Card Bakiyesi Yüklüyorsunuz!');
                                })
                                document.querySelector('.paynetj-button').click();

                                {{--var uuid = $('input[name="uuid"]').val();--}}
                                {{--var route = "{{ url("startpay") }}";--}}
                                {{--var url = route+"/"+delayed_id+"?amount="+payment_amount+"&uuid="+delayed_id;--}}
                                {{--$.get( url, function( data ) {--}}

                                {{--    $('.sendAgreementCheck').removeAttr('disabled')--}}
                                {{--    $("#paymentModalCard .modal-body").html(data);--}}
                                {{--    $('#paymentModal').modal('hide');--}}
                                {{--    $('#paymentModalCard').modal('show');--}}
                                {{--});--}}
                            }else{
                                document.getElementById('balance_form').submit()
                            }

                        }else{
                            Swal.fire({
                                title: "Uyarı!",
                                text: 'Cari Sözleşmeyi Onaylamadı.',
                                icon: "error",
                                confirmButtonText: "Tamam"

                            });
                            $('.sendAgreementCheck').removeAttr('disabled')
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error(xhr.responseText);
                    }
                });
            })
        })
        function sendAgreement(){
            var definition_id = $('select[name="plus_cards_definitions"]').val();
            var delayed_id = $('input[name=delayed_id]').val();
            var plus_card_id = $('input[name=plus_card_id]').val()
            var customer_id = $('input[name=customer_id]').val()
            var unit_quantity = $('input[name=unit_quantity]').val()
            var payment_amount = $('input[name=payment_amount]').val()
            var unit_price_cleaned = payment_amount.replace(/[^\d.,-]/g, '');
            unit_price_cleaned = unit_price_cleaned.replace('.', '').replace(',', '.');
            var payment_amount = unit_price_cleaned;
            var valid_date = $('input[name=valid_date]').val()
            var payment_type = $('select[name=payment_type]').val()
            var campaign_id = $('input[name=campaign_id]').val();

            $.ajax({
                type: 'POST',
                url: '{{route('api.sendAgreement')}}',
                data: {
                    delayed_id:delayed_id,
                    _token:'{{csrf_token()}}',
                    plus_card_id: plus_card_id,
                    customer_id:customer_id,
                    unit_quantity:unit_quantity,
                    payment_amount:payment_amount,
                    valid_date:valid_date,
                    definition_id:definition_id,
                    payment_type:payment_type,
                    campaign_id: campaign_id
                },
                success: function(response) {

                    if(response.success){
                        $('input[name=delayed_id]').val(response.delayed_id)

                        Swal.fire({
                            title: "Uyarı!",
                            text: 'Sözleşme sms olarak gönderildi.',
                            icon: "success",
                            confirmButtonText: "Tamam"

                        });
                        $('.send_agreement').removeAttr('disabled')
                    }else{
                        Swal.fire({
                            title: "Uyarı!",
                            text: 'Tekrar Göndermek İçin 3 Dk Bekleyiniz.',
                            icon: "error",
                            confirmButtonText: "Tamam"

                        });
                        $('.send_agreement').removeAttr('disabled')
                    }
                },
                error: function(xhr, status, error) {
                    console.error(xhr.responseText);
                }
            });
        }
        $(".select2").select2({
            placeholder: "Ara..",
            dropdownParent: $('#plusCardAddBalance')
        });
        $(".select2User").select2({
            placeholder: "Ara..",
            dropdownParent: $('#userInfoModal')
        });
        $('.unit_quantity').change(function(){
            var unit = $(this).val();
            let definitionID = $('select[name=plus_cards_definitions] option:selected').val();
            var campaign_id = $('input[name=campaign_id]').val();

            $.ajax({
                url: "{{ route('api.checkDefinitionPrices') }}",
                type: "post",
                data: {
                    _token:"{{ @csrf_token() }}",
                    id:definitionID,
                    count:unit,
                    campaign_id: campaign_id
                },
                success: function (response) {
                    $('#plus_card_campaigns option:selected').prop('selected', false);

                    var commission_rate = $('select[name=plus_cards_definitions] option:selected').data('commissionrate');
                    if (response.success == 'true'){
                        $('input[name=unit_price]').val(response.price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }))
                    }else{
                        $('input[name=unit_price]').val($('select[name=plus_cards_definitions] option:selected').data('price').toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }))
                    }
                    var unit_price = $('input[name=unit_price]').val();
                    var unit_price_cleaned = unit_price.replace(/[^\d.,-]/g, '');
                    unit_price_cleaned = unit_price_cleaned.replace('.', '').replace(',', '.');
                    var credits_amount = parseFloat(unit) * parseFloat(unit_price_cleaned);
                    var commission_price = (credits_amount * parseInt(commission_rate)) / 100;

                    $('input[name=credits_amount]').val(credits_amount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }))
                    $('input[name=payment_amount]').val(credits_amount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }))
                    $('input[name=commission]').val(commission_price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }));
                    valid_date_calc(unit)
                }
            });




        })
        function valid_date_calc(unit){
            var valid_date = "";

            var today = new Date();
            if (unit < 50) {
                today.setMonth(today.getMonth() + 6);
            } else {
                today.setFullYear(today.getFullYear() + 1);
            }

            var year = today.getFullYear();
            var month = (today.getMonth() + 1).toString().padStart(2, '0'); // Ayı iki haneli hale getirme
            var day = today.getDate().toString().padStart(2, '0'); // Günü iki haneli hale getirme

            valid_date = year + '-' + month + '-' + day;

            $('input[name=valid_date]').val(valid_date);
            if ($("select[name=plus_cards_definitions] option:selected").val() === '10'){
                $('.credits-valid-date').val('{{ \Carbon\Carbon::now()->endOfMonth()->toDateString() }}')
            }
        }

        $('#valid_date_if').change(function () {
            var value = $('#valid_date_if').val();

            if (value == 1) {
                $('.valid_date_input').show();
            } else {
                $('.valid_date_input').hide();
            }
        });

        $('.credi_card_payment_cancel').click(function(){
            Swal.fire({
                title: "Emin misiniz?",
                text: "Kredi Kartı İle Ödeme İptal Edilecek.",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "İptal Et!",
                cancelButtonText: "Devam Et!"
            }).then((result) => {
                if (result.isConfirmed) {
                    $('#paymentModal').modal('hide')
                    $('#plusCardAddBalance').modal('show')
                }
            });
        });
        $('input[type=radio][name=balance_type]').change(function(){
            if($(this).val() == "credits"){
                $('.credits-tabs').show()
                $('.points-tabs').hide()
            }else{
                $('.credits-tabs').hide()
                $('.points-tabs').show()
            }
        })
        $('.submit_balance_form').click(function(){
            if ($('#plus_cards_definitions').val() == ''){
                Toast.fire({
                    icon: "error",
                    title: "Kredi tipi seçilmedi!"
                });
                return false;
            }
            $('input[name="plus_card_definition_id"]').val($('#plus_cards_definitions').val())
            Swal.fire({
                title: "Emin misiniz?",
                text: "Plus Card'a Bakiye Yüklenecek",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Evet, Yükle!",
                cancelButtonText: "Hayır, Yükleme!"
            }).then((result) => {
                if (result.isConfirmed) {
                    var balance_type = $('input[name=balance_type]').val()
                    var payment_type = $('#payment_type').val()
                    $('#plusCardAddBalance').modal('hide')
                    if($('input[name=empty_user_information]').val() == 2){
                        sendAgreement();
                        $('#paymentModal').modal('show')
                    }else{
                        $('#userInfoModal').modal('show')
                    }
                }
            });

        })
       $('#plus_cards_definitions').change(function(){
           var quantity = $("select[name=plus_cards_definitions] option:selected").data('quantity');
           var unit_price = $("select[name=plus_cards_definitions] option:selected").data('price');
           var commission_rate = $("select[name=plus_cards_definitions] option:selected").data('commissionrate');
           var invoice_code = $("select[name=plus_cards_definitions] option:selected").data('invoicecode');
           var stock_id = $("select[name=plus_cards_definitions] option:selected").data('stockid');
           var credits_amount = unit_price * quantity
           var commission_price = (credits_amount * commission_rate) / 100;
           $('.unit_quantity').val(quantity)
           $('.credits_price').val(unit_price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }))
           $('.credits_amount').val(credits_amount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }))
           $('.commission').val(commission_price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }))
           $('.invoice_code').val(invoice_code)
           $('.payment_amount').val(credits_amount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }))
           $('.balance_stock_id').val(stock_id);
           valid_date_calc(quantity)
       })

        $('#plus_card_campaigns').change(function () {
            var campaignId = $(this).val();
            var definitionId = $("select[name=plus_cards_definitions] option:selected").val();
            var plusCardId = $('input[name=plus_card_id]').val()
            var quantity = $('.unit_quantity').val();

            if (campaignId) {
                $.ajax({
                    url: '{{ route("pluscard.campaign.select") }}',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        campaign_id: campaignId,
                        definition_id: definitionId,
                        plus_card_id: plusCardId,
                        quantity: quantity
                    },
                    success: function (response) {
                        if (response.success === true) {
                            Toast.fire({
                                icon: "success",
                                title: response.message
                            });

                            $('.credits_price').val(response.discountedAmount.unitPrice.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}))
                            $('.credits_amount').val(response.discountedAmount.creditsAmount.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}))
                            $('.commission').val(response.discountedAmount.commissionPrice.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}))
                            $('.payment_amount').val(response.discountedAmount.creditsAmount.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'}))
                            $('input[name=campaign_id]').val(response.campaignId);
                        } else {
                            $('#plus_card_campaigns option:selected').prop('selected', false);

                            Toast.fire({
                                icon: "error",
                                title: response.message
                            });
                        }
                    },
                    error: function (xhr) {
                        $('#plus_card_campaigns option:selected').prop('selected', false);

                        Toast.fire({
                            icon: "error",
                            title: xhr.responseText
                        });
                    }
                });
            }
        });

        function stock_price_calc(type){
            var price = $("select[name=new_stock_new] option:selected").data('price')
            var credi = $('#new_credi').val();
            var result = parseInt(price) * parseInt(credi);
            result = result.toLocaleString('tr-TR');
            if(!isNaN(result)){
                $('.total_price_new').html(result.toLocaleString('tr-TR') + '₺')
            }else{
                $('.total_price_new').html('0₺')
            }
            if(type == "kredi"){
                $('#new_puan').val('0');
            }
            if(type == "puan"){
                $('#new_credi').val('0');
            }
        }
        function stock_price_calc_2(id){
            var price = $("#stocks_"+id+" option[value='" + id + "']").data('price')
            var credi = $('#stock_credi_'+id).val();
            console.log(price,credi)
            var result = parseInt(price) * parseInt(credi);
            result = result.toLocaleString('tr-TR');
            if(!isNaN(result)){
                $('.total_price_'+id).html(result.toLocaleString('tr-TR') + '₺')
            }else{
                $('.total_price_'+id).html('0₺')
            }

        }

        $('input[name="multiple"]').on('click',function (){
            if ($(this)[0].checked == true){
                $('.multi').removeClass('d-none')
            }else{
                $('.multi').addClass('d-none')
            }
        })

        $('.search-branch').on('keyup',function (){
            let $val = $(this).val();
            if ($(this).val().length > 2){
                $.ajax({
                    url: "{{ route('api.getBranchesForPlusCard') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','search':$val} ,
                    success: function (response) {
                        $('.find-branch').css('display','block')
                        if (response.items.length > 0){
                            let html = ''
                            $.each(response.items,function (index,item){
                                html += '<div class="filter-item" data-id="'+item.id+'" data-unvan="'+item.unvan+'">'+item.unvan+'</div>'
                            })
                            $('.find-branch').html(html)

                            $('.find-branch .filter-item').on('click',function (){
                                $('input[name=branch_id]').val($(this).data('id'))
                                $('.find-branch').css('display','none')
                                $('.search-branch').val($(this).data('unvan'))
                                $('.search-branch').removeAttr('required')
                            })

                        }else{
                            $('.find-branch').html("Hiçbir Kayıt Bulunamadı! <a class='text-danger' href='{{ route('branches.create') }}'>Yeni Kayıt Ekle</a>")
                        }
                    }
                });
            }else{
                $('.find-branch').css('display','none')
            }
        })

        $('.customer-search').click(function(){
            $('.customer-search').attr('disabled','disabled');
            let $val = $('.search-customer').val();
            $.ajax({
                url: "{{ route('api.getCustomersForPlusCard') }}",
                type: "post",
                data: {'_token':'{{ csrf_token() }}','search':$val,'search_type':$('input[name="customer_search_type"]:checked').val()} ,
                success: function (response) {
                    console.log(response.items.length);
                    if (response.items.length > 0) {
                        let html = ""
                        $.each(response.items, function (index, item) {
                            let tcVergiNo = item.vergi_no || item.tc_no;
                            let cep = item.cep;
                            let mask;
                            let mask2;
                            if (tcVergiNo) {
                                let first4 = tcVergiNo.substring(0, 4);
                                let tcVergi = tcVergiNo.substring(4, tcVergiNo.length - 4).replace(/\d/g, "*");
                                mask = first4 + tcVergi
                            }
                            if (cep) {
                                let first4x = cep.substring(0, 2);
                                let last5x = cep.substring(cep.length - 4);

                                let stars = cep.substring(0, cep.length - 4).replace(/\d/g, "*");
                                mask2 = stars + last5x
                            }

                            html += '<tr class="filter-item" ' +
                                'data-id="' + item.id + '" ' +
                                'data-unvan="' + item.unvan + '" ' +
                                'data-telefon="' + (item.telefon ? item.telefon : item.cep) + '" ' +
                                'data-kod="' + item.cari_kod + '" ' +
                                'data-tc="' + item.tc + '">'
                            html += '<td>' + (item.cari_kod || '') + '</td>'
                            html += '<td>' + item.unvan + '</td>'
                            html += '<td>' + item.branch_name + '</td>'
                            html += '<td>' + (mask2 || '') + '</td>'
                            html += '<td>' + (mask || '') + '</td>'
                            html += '<td>' + (item.plusCardNo || '') + '</td>'
                            html += '</tr>'
                        })
                        $('.filter-results-new').html(html)
                        let addCustomerHtml = "<a style=\'font-size:.9rem\' class=\"btn btn-sm btn-danger open-add-customer-ruhsat-page\" href=\"{{ route('customers.create',['customer'=>'ruhsat','return_url'=>url()->current(),'type'=>'bireysel']) }}&unvan=" + $('.search-customer').val() + "\">Yeni Bireysel Kayıt</a><br>"
                        addCustomerHtml += "<a style=\'font-size:.9rem\' class=\"btn btn-sm btn-danger open-add-customer-ruhsat-page\" href=\"{{ route('customers.create',['customer'=>'ruhsat','return_url'=>url()->current(),'type'=>'kurumsal']) }}&unvan=" + $('.search-customer').val() + "\">Yeni Kurumsal Kayıt</a>"
                        $('.modal-footer.cari').html(addCustomerHtml)
                        $('#searchCustomerModal').modal('show')
                        $('.filter-results-new .filter-item').on('click', function () {
                            $('input[name=customer_id]').val($(this).data('id'))
                            $('.search-customer').val($(this).data('unvan'));

                            $('#searchCustomerModal').modal('hide')
                        })
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Eşleşen Kayıt Bulunamadı!"
                        });
                    }
                    $('.customer-search').removeAttr('disabled');
                }
            });
        })

        $('.plus_card_balance_detail_upload').click(function(){
            var balance_id = $(this).attr('data-id');
            $.ajax({
                url: "{{ route('api.plusCardBalance') }}",
                type: "post",
                data: {
                    _token:"{{ @csrf_token() }}",
                    balance_id:balance_id,
                    card_id:'{{$plusCard->id}}',
                },
                success: function (response) {
                    if(response.success && response.balances.length != 0){
                        var html='<div class="accordion" id="accordionPanelsStayOpenExample"><div class="accordion-item">'
                        var isFirstIteration = true;
                        $.each(response.balances, function(key, value) {
                            var showClass = "collapsed collapse"
                            var buttonShowClass="collapsed"
                            if(isFirstIteration){
                                showClass = "collapse show"
                                buttonShowClass = ""
                                isFirstIteration=false
                            }
                            console.log(key)
                            html += '<h2 class="accordion-header" id="balances_id' + key + '"><button class="accordion-button '+buttonShowClass+'" type="button" data-bs-toggle="collapse" data-bs-target="#balances_tab_id' + key + '" aria-expanded="true" aria-controls="balances_tab_id' + key + '">' + value.name + '</button></h2>';
                            html += '<div id="balances_tab_id' + key + '" class="accordion-collapse '+showClass+'" aria-labelledby="balances_id' + key + '">';
                            html += '<div class="accordion-body"><div class="row"><div class="table-responsive"><table class="table">';
                            html += '<thead><tr>' +
                                '<th>YUK.ID</th>' +
                                '<th>Bayi</th>' +
                                '<th>Belge Tarihi</th>' +
                                '<th>Belge Numarası</th>' +
                                '<th>Son Kullanma Tarihi</th>' +
                                '<th>Tanım Adı</th>' +
                                '<th>Kupon Kodu</th>' +
                                '<th>Özel Bölge Kodu</th>' +
                                '<th>Kampanya Türü</th>' +
                                '<th>İşlem Tipi</th>' +
                                '<th>Miktar</th>' +
                                '<th>Fiyat</th>' +
                                '<th>Tutar</th>' +
                                '<th>Kullanım Tipi</th>' +
                                '</tr></thead>';
                            html += '<tbody>';
                            $.each(value.islemler, function(key2, islem_value) {
                                html += '<tr>';
                                html += '<td>' + islem_value.id + '</td>';
                                html += '<td>' + islem_value.buy_stock_service + '</td>';
                                html += '<td>' + islem_value.belge_tarihi + '</td>';
                                html += '<td><a target="_blank" class="text-danger" href="'+islem_value.expertise_url+'">' + islem_value.belge_no + '</a></td>';
                                html += '<td>' + islem_value.son_kullanma + '</td>';
                                html += '<td>' + islem_value.definition_name + '</td>';
                                html += '<td>' + islem_value.kupon_kodu + '</td>';
                                html += '<td>' + islem_value.ozel_bolge_kodu + '</td>';
                                html += '<td>' + islem_value.kampanya_turu + '</td>';
                                html += '<td>' + islem_value.islem_tipi + '</td>';
                                html += '<td>' + islem_value.unit + '</td>';
                                html += '<td>' + islem_value.price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) + '</td>';
                                html += '<td>' + islem_value.amount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) + '</td>';
                                html += '<td>' + islem_value.type + '</td>';
                                html += '</tr>';
                            });
                            html += '</tbody>';
                            html += '</table></div></div></div></div>';
                        });
                        html += '</div></div>';

                        $('.balance_details_modal_body').html(html);



                    }else{
                        $('.balance_details_modal_body').html('<center><h2>Bir Sonuç Bulunamadı</h2></center>')
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
            $('#plusCardBalance').modal('show');
        })
        $('.have_plus_card_price_delete').click(function(){
            var id = $(this).attr('data-id');
            var thisbtn = $(this)
            Swal.fire({
                title: "Emin misiniz?",
                text: "Seçili Notlar Silinecek",
                icon: "error",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Evet, Sil!",
                cancelButtonText: "Hayır, Silme!"
            }).then((result) => {
                if (result.isConfirmed) {
                    thisbtn.attr('disabled','')
                    $.ajax({
                        url: "{{ route('api.deletePlusCardBalance') }}",
                        type: "post",
                        data: {
                            _token:"{{ @csrf_token() }}",
                            plus_card_balance_id:id,
                        },
                        success: function (response) {
                            if(response.success){
                                $('.have_plus_card_stock_'+id).remove();
                                Swal.fire({
                                    title: "Uyarı!",
                                    text: 'Yükleme Silindi',
                                    icon: "success",
                                    confirmButtonText: "Tamam"

                                });
                            }else{
                                Swal.fire({
                                    title: "Uyarı!",
                                    text: response.message,
                                    icon: "error",
                                    confirmButtonText: "Tamam"

                                });
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                        }
                    });
                }
            });
        })
        $(document).on("click","#paymentSubmit",function(){
            var thisbtn = $(this);
            $(this).attr('disabled','');
            var data = $('#paynetForm').serializeArray();
            var url = $("#paynetForm").attr("action");
            $.post(url, data).then(res => {
                console.log(res)
                if(res.status == "ok"){
                    Swal.fire({
                        title: "Başarılı!",
                        text: "Bu işlem için daha önce ödeme alınmıştır. Lütfen 10 dk sonra tekrar deneyiniz.",
                        icon: "success",
                        confirmButtonText: "Tamam"

                    });
                    $('#paymentSubmit').removeAttr('disabled');
                    document.getElementById('balance_form').submit()
                }else if(res.status == "3d"){
                    var iframe = $('<iframe>', {
                        src: res.url,
                        frameborder: 0,
                        scrolling: 'auto',
                        width: '100%',
                        height: '420px',
                        'id':'payFrame'
                    });
                    // İframe'i sayfaya ekle
                    $("#paymentModalCard .modal-body").html("").append(iframe);
                    $("#paymentModalCard").modal("show");

                    iframe.on('load',function(){
                        var iframeUrl = '';
                        if(typeof $(this).contents().get(0) !== "undefined" && $(this).contents().get(0).location.href){
                            iframeUrl = $(this).contents().get(0).location.href;
                        }
                        // console.log(iframeUrl,1)
                        // console.log($(this).contents().get(0),2)
                        // console.log($(this).contents().get(0).html(),3)

                        if(iframeUrl == "{{ url('/login') }}" || iframeUrl.includes('uuid='+$('input[name="delayed_id"]').val())){
                            $("#paymentModalCard .modal-body").html("");
                            $("#paymentModalCard").modal("hide");
                            var url2 = "{{ url('paynetStatus') }}";
                            const data2 = {
                                "payment_session_id": res.payment_session_id,
                                "payment_token_id": res.payment_token_id
                            };

                            $.post(url2, data2).then(res2 => {
                                if(res2.status == "ok"){

                                    Swal.fire({
                                        title: "Başarılı!",
                                        text: res2.message,
                                        icon: "success",
                                        confirmButtonText: "Tamam"

                                    });
                                    $("#paymentModalCard").modal("hide");
                                    $("#paymentModal").modal("hide");
                                    document.getElementById('balance_form').submit()
                                }else{
                                    Swal.fire({
                                        title: "Uyarı!",
                                        text: res2.message,
                                        icon: "error",
                                        confirmButtonText: "Tamam"

                                    });
                                    $(this).removeAttr('disabled');
                                }
                            });
                        }
                    });
                    return false;
                    //$(location).attr('href', res.url)
                }else{
                    Swal.fire({
                        title: "Uyarı!",
                        text: res.message,
                        icon: "error",
                        confirmButtonText: "Tamam"

                    });
                }
            });
            return false;
        });

        function getUnusedCards()
        {
            $('#unused-card-note').hide();
            $('#unused-card-table').show();

            $.ajax({
                url: "{{ route('getPlusCardsForAjax') }}",
                type: "post",
                dataType: "json",
                data: {
                    _token: "{{ @csrf_token() }}",
                    saved_filter_status: 2,
                    unused_cards: true,
                    start: 0,
                    length: 10
                },
                success: function (response) {
                    if (response.recordsTotal > 0) {
                        $('#unused-card-table tbody').empty();

                        $.each(response.data, function(index, item) {
                            var newRow = `
                                <tr>
                                    <td></td>
                                    <td>${item.no}</td>
                                    <td><button data-no="${item.no}" data-system_id="${item.cleared_system_id}" class="btn btn-sm btn-danger assignCardBtn">Tanımla</button></td>
                                </tr>
                            `;

                            $('#unused-card-table tbody').append(newRow);
                        });

                        $('#searchUnusedCardModal').modal('show');
                    } else {
                        $('#searchUnusedCardModal').modal('hide');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        }

        $(document).on('click', '.assignCardBtn', function(e) {
            $('input[name="system_id"]').val($(this).data('system_id'));
            $('input[name="no"]').val($(this).data('no'));

            $('#unused-card-table').hide();
            $('#unused-card-note').show();

            $('.saveButton').click();

            $('#searchUnusedCardModal').modal('hide');
            $('#unused-card-note').hide();
        });

        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        if (!Swal.isVisible()) {
            const Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        @if(session('error') && session('redirect'))
            Swal.fire({
                title: "Uyarı",
                text: "{{ session('error') }}",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Yönlendir",
                cancelButtonText: "Bu Sayfada Kal"
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = '{{ session('redirect') }}'
                }
            });
        @endif
    </script>
@endpush
