@extends('pages.build')
@section('title','Plus Card')
@push('css')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
   <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush

@section('excel')
    @if(in_array('*', $userRoles) || in_array('download_excel_plus_card', $userRoles))
        <a href="/excel/plus_card?customer_name={{$_GET['customer_name'] ?? ''}}&customer_telephone={{$_GET['customer_telephone'] ?? ''}}&customer_code={{$_GET['customer_code'] ?? ''}}&customer_code_old={{$_GET['customer_code_old'] ?? ''}}&card_no={{$_GET['card_no'] ?? ''}}&branch_id={{$_GET['branch_id'] ?? ''}}&start_date={{$_GET['start_date'] ?? ''}}&end_date={{$_GET['end_date'] ?? ''}}&type=1" class="btn btn-sm btn-success">Tüm Veriler <i class="fa fa-file-excel"></i> </a>
        <a href="/excel/plus_card?customer_name={{$_GET['customer_name'] ?? ''}}&customer_telephone={{$_GET['customer_telephone'] ?? ''}}&customer_code={{$_GET['customer_code'] ?? ''}}&customer_code_old={{$_GET['customer_code_old'] ?? ''}}&card_no={{$_GET['card_no'] ?? ''}}&branch_id={{$_GET['branch_id'] ?? ''}}&start_date={{$_GET['start_date'] ?? ''}}&end_date={{$_GET['end_date'] ?? ''}}&type=2" class="btn btn-sm btn-success">Harcama <i class="fa fa-file-excel"></i> </a>
        <a href="/excel/plus_card?customer_name={{$_GET['customer_name'] ?? ''}}&customer_telephone={{$_GET['customer_telephone'] ?? ''}}&customer_code={{$_GET['customer_code'] ?? ''}}&customer_code_old={{$_GET['customer_code_old'] ?? ''}}&card_no={{$_GET['card_no'] ?? ''}}&branch_id={{$_GET['branch_id'] ?? ''}}&start_date={{$_GET['start_date'] ?? ''}}&end_date={{$_GET['end_date'] ?? ''}}&type=3" class="btn btn-sm btn-success">Yükleme <i class="fa fa-file-excel"></i> </a>
        <a href="/excel/plus_card?customer_name={{$_GET['customer_name'] ?? ''}}&customer_telephone={{$_GET['customer_telephone'] ?? ''}}&customer_code={{$_GET['customer_code'] ?? ''}}&customer_code_old={{$_GET['customer_code_old'] ?? ''}}&card_no={{$_GET['card_no'] ?? ''}}&branch_id={{$_GET['branch_id'] ?? ''}}&start_date={{$_GET['start_date'] ?? ''}}&end_date={{$_GET['end_date'] ?? ''}}&type=4" class="btn btn-sm btn-success">Devir <i class="fa fa-file-excel"></i> </a>
        <a href="/excel/plus_card?branch_id={{$_GET['branch_id'] ?? ''}}&type=not_used" class="btn btn-sm btn-success">Boştaki Kartlar <i class="fa fa-file-excel"></i> </a>
    @endif
@endsection
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form id="getSavedFilter" method="get" action="{{ route('plus-cards.index') }}"></form>
                <form method="get" action="{{ route('plus-cards.index') }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Cari Unvan</label>
                                    <input type="text" class="form-control form-control-sm" name="customer_name" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->customer_name : ($_GET['customer_name'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Cari Tel.</label>
                                    <input type="text" class="form-control form-control-sm" name="customer_telephone" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->customer_telephone : ($_GET['customer_telephone'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Cari Kod</label>
                                    <input type="text" class="form-control form-control-sm" name="customer_code" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->customer_code : ($_GET['customer_code'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Cari Eski Kod</label>
                                    <input type="text" class="form-control form-control-sm" name="customer_code_old" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->customer_code_old : ($_GET['customer_code_old'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Kart ID</label>
                                    <input type="text" class="form-control form-control-sm" name="system_id" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->system_id : ($_GET['system_id'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Kart No</label>
                                    <input type="text" class="form-control form-control-sm" name="card_no" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->card_no : ($_GET['card_no'] ?? '') }}">
                                </div>
                            </div>
                            @if(count($authUser->getBranchIds()) > 1)
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Bayi</label>
                                        <select class="form-control form-control-sm select2" name="branch_id">
                                            @if($authUser->type == 'admin')
                                                <option value="0">Tümü</option>
                                            @endif
                                            @foreach(\App\Models\Branch::where('status',1)->whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                                <option @if((isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id)) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label><small>Kayıtlı Şablonlar</small></label>
                                    <select form="getSavedFilter" onchange="$('#getSavedFilter').submit()" class="form-control form-control-sm" name="saved_filter_id">
                                        <option value="0">Seçilmedi</option>
                                        @foreach($savedFilters as $savedFilter)
                                            <option @if(isset($_GET['saved_filter_id']) && $_GET['saved_filter_id'] == $savedFilter->id) selected @endif value="{{ $savedFilter->id }}">{{ $savedFilter->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label><small>Durumu</small></label>
                                    <select class="form-control form-control-sm" name="saved_filter_status">
                                        <option value="">Hepsi</option>
                                        <option @if(!empty($_GET['saved_filter_status']) && $_GET['saved_filter_status'] == 1) selected="" @endif value="1">Atanmış</option>
                                        <option @if(!empty($_GET['saved_filter_status']) && $_GET['saved_filter_status'] == 2) selected="" @endif value="2">Atanmamış</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Başlangıç Tarihi</label>
                                    <input type="date" class="form-control form-control-sm" name="start_date" value="{{ $filters['startDate'] }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Bitiş Tarihi</label>
                                    <input type="date" class="form-control form-control-sm" name="end_date" value="{{ $filters['endDate'] }}">
                                </div>
                            </div>
                            <div class="col mt-2">
                                <button class="btn btn-success btn-sm mt-1">Listele</button>
                                <button onclick="$('#saveFilter').modal('toggle')" type="button" class="btn btn-sm btn-danger mt-1" >Kaydet</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    @if(in_array('*', $userRoles) || in_array('add_plus_card', $userRoles))
                        <a href="{{ route('plus-cards.create') }}" class="nav-link">{{ \App\Models\Menu::where('key', 'add_plus_card')->first()->value }}</a>
                    @endif

                </nav>
                <div class="card-body">

                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="saveFilter" tabindex="-1"
         aria-labelledby="saveFilter" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Filtre Şablonu Oluştur
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Şablon Adı</small></label>
                            <input type="text" class="form-control" name="saved_filter_name">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger save-filter">Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="plusCardBalance" tabindex="-1"
         aria-labelledby="plusCardBalance" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-xl modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Harcamalar
                    </h6>
                    @if(in_array('*', $userRoles) || in_array('download_excel_plus_card', $userRoles))
                    <button type="button" class="btn btn-success btn-sm plus-card-excel" style="margin-left: 5px"><i class="fa fa-file-excel"></i> Excel Çıktı Al</button>
                    @endif
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body balance_details_modal_body" style="max-height: 500px;overflow-y: scroll">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">Alınan Hizmet</th>
                                <th scope="col">Hizmet Tarihi</th>
                                <th scope="col">Hizmet Bayisi</th>
                                <th scope="col">Muhasebe Kodu</th>
                            </tr>
                            </thead>
                            <tbody>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
{{--    <script src="https://editor.datatables.net/extensions/Editor/js/dataTables.editor.js"></script>--}}
{{--    <script src="https://editor.datatables.net/extensions/Editor/js/editor.dataTables.js"></script>--}}

    <script>
        $(".select2").select2({
            placeholder: "Ara.."
        });
        $(function (e) {
            'use strict';
            var isAdmin = {{ $authUser->type == 'admin' ? 'true' : 'false' }};
            if (isAdmin) {
                columns = [
                    {
                        "data": 'x',
                        'defaultContent': '',
                        "title": `<span style="cursor:pointer;" onclick="$('#setColumns').modal('toggle')">Stn</span>`,
                        "width": "1%"
                    },
                    { "data": "system_id", "title": "Kart/Sistem ID" },
                    { "data": "no", "title": "Kart No" },
                    { "data": "deha_bakiye", "title": "Devir 23.03.24 Öncesi" },
                    { "data": "balance_credits", "title": "Kalan Kredi Bakiye" },
                    { "data": "balance_points", "title": "Kalan Puan Bakiye" },
                    { "data": "branch_name", "title": "Bayi" },
                    { "data": "customer_code", "title": "Müşteri Yeni Kod" },
                    { "data": "customer_code_old", "title": "Müşteri Eski Kod" },
                    { "data": "customer_name", "title": "Müşteri Unvan" },
                    { "data": "customer_telefon", "title": "Müşteri Telefon" },
                    { "data": "options", "title": "İşlemler" }
                ];
            } else {    // Reorder the columns for bayi users
                columns = [
                    { "data": "customer_telefon", "title": "MÜŞTERİ TELEFON" },
                    { "data": "customer_name", "title": "MÜŞTERİ ÜNVANI" },
                    { "data": "no", "title": "PLUS CARD  NO" },
                    { "data": "balance_credits", "title": "KREDİ" },
                    { "data": "balance_points", "title": "PUAN" },
                    { "data": "last_updated_date", "title": "SON İŞLEM TARİHİ"},
                    { "data": "customer_code", "title": "MÜŞTERİ YENİ KOD" },
                    { "data": "first_upload_date", "title": "İLK YÜKLEME TARİHİ" },
                    { "data": "branch_name", "title": "İLK YÜKLEME YAPILAN BAYİ" },
                    { "data": "system_id", "title": "KART/SİSTEM ID" },
                    { "data": "customer_code_old", "title": "Müşteri Eski Kod" },
                    { "data": "options", "title": "İşlemler" }
                ];
            }
            var pageLength, lengthMenu;
            if (isAdmin) {
                pageLength = 50;
                lengthMenu = [[50, 500, 1000], [50, 500, 1000]];
            } else {
                pageLength = 10;
                lengthMenu = [[10, 25, 50, 100], [10, 25, 50, 100]];
            }
            var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "ordering": !isAdmin,
                "aaSorting": [],
                "pageLength": pageLength,
                "lengthMenu": lengthMenu,
                "processing": true,
                "serverSide": true,
                "searching":false,
                "columns":columns,
                "ajax": {
                    "url": "{{ route('getPlusCardsForAjax') }}",
                    "dataType": "json",
                    "type": "POST",
                    "data":{
                        _token: "{{csrf_token()}}",
                        @if(!empty($_GET['customer_name']))
                        'customer_name': "{{$_GET['customer_name']}}",
                        @endif
                        @if(!empty($_GET['customer_telephone']))
                        'customer_telephone': "{{$_GET['customer_telephone']}}",
                        @endif
                        @if(!empty($_GET['created_at']))
                        'created_at': "{{$_GET['created_at']}}",
                        @endif
                        @if(!empty($_GET['system_id']))
                        'system_id': "{{$_GET['system_id']}}",
                        @endif
                        @if(!empty($_GET['customer_code']))
                        'customer_code': "{{$_GET['customer_code']}}",
                        @endif
                        @if(!empty($_GET['customer_code_old']))
                        'customer_code_old': "{{$_GET['customer_code_old']}}",
                        @endif
                        @if(!empty($_GET['card_no']))
                        'card_no': "{{$_GET['card_no']}}",
                        @endif
                        @if(!empty($_GET['branch_id']))
                        'branch_id': "{{$_GET['branch_id']}}",
                        @endif
                        @if(!empty($_GET['saved_filter_id']))
                        'saved_filter_id':"{{$_GET['saved_filter_id']}}",
                        @endif
                        @if(!empty($_GET['saved_filter_status']))
                        'saved_filter_status':"{{$_GET['saved_filter_status']}}",
                        @endif
                            @if(!empty($_GET['start_date']))
                        'start_date':"{{$_GET['start_date']}}",
                        @endif
                            @if(!empty($_GET['end_date']))
                        'end_date':"{{$_GET['end_date']}}",
                        @endif


                    },
                    "dataSrc":'data'
                },
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_plus_card', $userRoles)) <a href='{{ route("plus-cards.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_plus_card', $userRoles)) <a href='{{ route("plus-cards.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }, // 0-1 credit cell  colors
                "createdRow": function( row, data, dataIndex ) {
                    if (data.balance_credits == 0 && !isAdmin) {
                        $('td', row).eq(3).css({
                        'background-color': 'orangered',
                        'color': '#fff'
                    });
                    }
                    else if (data.balance_credits == 1 && !isAdmin) {
                        $('td', row).eq(3).css({
                            'background-color': '#edc072',
                            'color': '#fff'
                        });
                    }
                },
                drawCallback: function(settings) {
                    $('.deletePlusCard').click(function(){
                        var thisbtn = $(this)
                        Swal.fire({
                            title: "Emin misiniz?",
                            text: "Plus Cart Silinecek",
                            icon: "error",
                            showCancelButton: true,
                            confirmButtonColor: "#d33",
                            cancelButtonColor: "#3085d6",
                            confirmButtonText: "Evet, Sil!",
                            cancelButtonText: "Hayır, Silme!"
                        }).then((result) => {
                            if (result.isConfirmed) {
                                var id = $(this).attr('data-id')
                                $.ajax({
                                    url: "{{route('api.deletePlusCard')}}",
                                    type: "POST",
                                    data: {
                                        '_token':'{{ csrf_token() }}',
                                        'id':id
                                    } ,
                                    success: function (response) {

                                        if (response.success){
                                            responsiveDatatable.row(thisbtn.closest('tr')).remove().draw();
                                            Toast.fire({
                                                icon: "success",
                                                title: "Plus Card Silindi"
                                            });
                                        }else{
                                            Toast.fire({
                                                icon: "error",
                                                title: "Bir Hata Oluştu Yeniden Deneyiniz."
                                            });
                                        }
                                    },
                                    error: function(jqXHR, textStatus, errorThrown) {
                                        console.log(textStatus, errorThrown);
                                    }
                                });
                            }
                        });

                    })

                    $('.system_id').on('dblclick',function (){
                        let $this = $(this)
                        $($this).removeAttr('disabled')
                        let $plusCardId = $this.data('pluscard')

                        $(document).on('click', function(event) {
                            let target = $(event.target);
                            if (!target.closest($this).length) {
                                let newValue = $this.val().trim(); // Get input value and remove spaces
                                // Validation: Ensure it contains at least 7 digits and is numeric
                                if (!/^\d{7,12}$/.test(newValue)) {
                                    Toast.fire({
                                        icon: "error",
                                        title: "Geçersiz Kart ID! Kart ID 7 ile 12 arası karakter uzunluğunda olmalıdır"
                                    });
                                    $this.val(''); // Clear invalid input and disable it
                                    $this.attr('disabled', 'true');
                                    $(document).unbind('click');
                                    return;
                                }
                                $($this).attr('disabled','true')
                                $(document).unbind('click')
                                $.ajax({
                                    url: '{{ route('setPlusCardSystemID') }}',
                                    type: 'POST',
                                    dataType: 'json',
                                    data: {'_token':'{{ csrf_token() }}','plus_card_id':$plusCardId,'value':$this.val()},
                                    success: function(response) {
                                        let iconText
                                        if (response.success === 'true')
                                            iconText = "success";
                                        else
                                            iconText = "error";
                                        Toast.fire({
                                            icon: iconText,
                                            title: response.message
                                        });
                                    },
                                });
                            }
                        });

                    })
                    $('.plus_card_balance_detail_upload').click(function(){
                        var card_id = $(this).attr('data-id');
                        $('.plus-card-excel').attr('data-pluscardid',card_id)
                        $.ajax({
                            url: "{{ route('api.plusCardBalance') }}",
                            type: "post",
                            data: {
                                _token:"{{ @csrf_token() }}",
                                balance_id:'',
                                card_id:card_id,
                            },
                            success: function (response) {
                                if(response.success && response.balances.length != 0){
                                    var html='<div class="accordion" id="accordionPanelsStayOpenExample"><div class="accordion-item">'
                                    var isFirstIteration = true;
                                    $.each(response.balances, function(key, value) {
                                        var showClass = "collapsed collapse"
                                        var buttonShowClass="collapsed"
                                        if(isFirstIteration){
                                            showClass = "collapse show"
                                            buttonShowClass = ""
                                            isFirstIteration=false
                                        }
                                        console.log(key)
                                        html += '<h2 class="accordion-header" id="balances_id' + key + '"><button class="accordion-button '+buttonShowClass+'" type="button" data-bs-toggle="collapse" data-bs-target="#balances_tab_id' + key + '" aria-expanded="true" aria-controls="balances_tab_id' + key + '">' + value.name + '</button></h2>';
                                        html += '<div id="balances_tab_id' + key + '" class="accordion-collapse '+showClass+'" aria-labelledby="balances_id' + key + '">';
                                        html += '<div class="accordion-body"><div class="row"><div class="table-responsive"><table class="table">';
                                        html += '<thead><tr>' +
                                            '<th>YUK.ID</th>' +
                                            '<th>Bayi</th>' +
                                            '<th>Belge Tarihi</th>' +
                                            '<th>Belge Numarası</th>' +
                                            '<th>Son Kullanma Tarihi</th>' +
                                            '<th>Tanım Adı</th>' +
                                            '<th>Kupon Kodu</th>' +
                                            '<th>Özel Bölge Kodu</th>' +
                                            '<th>Kampanya Türü</th>' +
                                            '<th>İşlem Tipi</th>' +
                                            '<th>Miktar</th>' +
                                            '<th>Fiyat</th>' +
                                            '<th>Tutar</th>' +
                                            '<th>Kullanım Tipi</th>' +
                                            '</tr></thead>';
                                        html += '<tbody>';
                                        $.each(value.islemler, function(key2, islem_value) {
                                            html += '<tr>';
                                            html += '<td>' + islem_value.id + '</td>';
                                            html += '<td>' + islem_value.buy_stock_service + '</td>';
                                            html += '<td>' + islem_value.belge_tarihi + '</td>';
                                            html += '<td><a target="_blank" class="text-danger" href="'+islem_value.expertise_url+'">' + islem_value.belge_no + '</a></td>';
                                            html += '<td>' + islem_value.son_kullanma + '</td>';
                                            html += '<td>' + islem_value.definition_name + '</td>';
                                            html += '<td>' + islem_value.kupon_kodu + '</td>';
                                            html += '<td>' + islem_value.ozel_bolge_kodu + '</td>';
                                            html += '<td>' + islem_value.kampanya_turu + '</td>';
                                            html += '<td>' + islem_value.islem_tipi + '</td>';
                                            html += '<td>' + islem_value.unit + '</td>';
                                            html += '<td>' + islem_value.price.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) + '</td>';
                                            html += '<td>' + islem_value.amount.toLocaleString('tr-TR', { style: 'currency', currency: 'TRY' }) + '</td>';
                                            html += '<td>' + islem_value.type + '</td>';
                                            html += '</tr>';
                                        });
                                        html += '</tbody>';
                                        html += '</table></div></div></div></div>';
                                    });
                                    html += '</div></div>';

                                    $('.balance_details_modal_body').html(html);



                                }else{
                                    $('.balance_details_modal_body').html('<center><h2>Bir Sonuç Bulunamadı</h2></center>')
                                }

                            },
                            error: function(jqXHR, textStatus, errorThrown) {
                                console.log(textStatus, errorThrown);
                            }
                        });
                        $('#plusCardBalance').modal('show');
                    })
                }
            });


            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })


        });
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
        $('.save-filter').on('click',function (){
            if ($('input[name="saved_filter_name"]').val() != ''){
                $.ajax({
                    url: "{{ route('saveFilters') }}",
                    type: "post",
                    data: {
                        '_token':'{{ csrf_token() }}',
                        'name':$('input[name="saved_filter_name"]').val(),
                        'card_no':$('input[name="card_no"]').val(),
                        'branch_id':$('select[name="branch_id"]').val(),
                        'customer_code':$('input[name="customer_code"]').val(),
                        'customer_name':$('input[name="customer_name"]').val(),
                        'type':'plus_cards'
                    } ,
                    success: function (response) {

                        if (response.success == 'true'){
                            Toast.fire({
                                icon: "success",
                                title: "Şablon Kayıt Edildi"
                            });
                            $('select[name="saved_filter_id"]').append('<option value="'+response.id+'" selected="selected">'+response.name+'</option>');
                            $('#getSavedFilter').submit()
                        }else{
                            Toast.fire({
                                icon: "error",
                                title: "Şablon Kayıt Edilemedi"
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Şablon Adı Girilmedi"
                });
            }
        })
        $('.plus-card-excel').click(function(){
            var card_id = $(this).attr('data-pluscardid');

            window.open('/excel/plus_card_customer/'+card_id, '_blank');
        })
    </script>
@endpush
