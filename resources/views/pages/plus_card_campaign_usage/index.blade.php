@extends('pages.build')
@section('title','Plus Card')
@push('css')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
   <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush

@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form method="get" action="{{ route('plus-card-campaign-usages.index') }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Kart ID</label>
                                    <input type="text" class="form-control form-control-sm" name="system_id" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->system_id : ($_GET['system_id'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Kart No</label>
                                    <input type="text" class="form-control form-control-sm" name="card_no" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->card_no : ($_GET['card_no'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col mt-2">
                                <button class="btn btn-success btn-sm mt-1">Listele</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <div class="card-body">
                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr>
                            <th style="width:1%" onclick="$('#setColumns').modal('toggle')">Stn</th>
                            <th>Kart/Sistem ID</th>
                            <th>Kart No</th>
                            <th>Kampanya</th>
                            <th>Ekspertiz</th>
                            <th>Fiyat</th>
                            <th>İndirimli Fiyat</th>
                            <th>İndirim Miktarı</th>
                            <th>Kullanılma Tarihi</th>
                            <th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(".select2").select2({
            placeholder: "Ara.."
        });

        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "ordering": false, // Disable sorting
                "aaSorting": [],
                "pageLength": 10,
                "processing": true,
                "serverSide": true,
                "searching":true,
                "ajax": {
                    "url": "{{ route('getPlusCardCampaignUsagesForAjax') }}",
                    "dataType": "json",
                    "type": "POST",
                    "data":{
                        _token: "{{csrf_token()}}",
                        @if(!empty($_GET['system_id']))
                        'system_id': "{{$_GET['system_id']}}",
                        @endif
                        @if(!empty($_GET['card_no']))
                        'card_no': "{{$_GET['card_no']}}",
                        @endif
                    },
                    "dataSrc":'data'
                },
                "columns": [
                    { "data": 'x','defaultContent': '' },
                    { "data": "card_system_id", 'defaultContent': '' },
                    { "data": "card_no", 'defaultContent': '' },
                    { "data": "campaign" },
                    { "data": "expertise_id" },
                    { "data": "original_amount" },
                    { "data": "discounted_amount" },
                    { "data": "discount_value" },
                    { "data": "used_at" },
                    { "data": "actions" },
                ],
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı!",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı!",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                },
                drawCallback: function(settings) {
                    $('.deletePlusCard').click(function(){
                        var thisbtn = $(this)

                        var destroyUrl = "{{ route('plus-card-campaign-usages.destroy', ':id') }}";

                        Swal.fire({
                            title: "Emin misiniz?",
                            text: "Plus Card Kampanya Kullanımı Silinecek",
                            icon: "error",
                            showCancelButton: true,
                            confirmButtonColor: "#d33",
                            cancelButtonColor: "#3085d6",
                            confirmButtonText: "Evet, Sil!",
                            cancelButtonText: "Hayır, Silme!"
                        }).then((result) => {
                            if (result.isConfirmed) {
                                var id = $(this).attr('data-id')
                                var url = destroyUrl.replace(':id', id);

                                $.ajax({
                                    url: url,
                                    type: "DELETE",
                                    data: {
                                        '_token': '{{ csrf_token() }}',
                                        'id': id
                                    },
                                    success: function (response) {
                                        if (response.success) {
                                            responsiveDatatable.row(thisbtn.closest('tr')).remove().draw();

                                            Toast.fire({
                                                icon: "success",
                                                title: "Plus Card Kampanya Kullanımı Silindi"
                                            });
                                        } else {
                                            Toast.fire({
                                                icon: "error",
                                                title: "Bir Hata Oluştu Yeniden Deneyiniz."
                                            });
                                        }
                                    },
                                    error: function (jqXHR, textStatus, errorThrown) {
                                        console.log(textStatus, errorThrown);
                                    }
                                });
                            }
                        });
                    })
                }
            });


            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })
        });
    </script>
@endpush
