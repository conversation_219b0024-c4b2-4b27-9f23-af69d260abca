<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link id="style" href="/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet" >
    <title>Kontrol Listesi</title>
</head>
<body>
<!-- resources/views/check.blade.php -->

<table class="table table-bordered table-striped" border="1">
    <tr>
        <th>Hesap Adı</th>
        <th>Telefon</th>
        <th>Cep</th>
        <th>Hesap Kodu</th>
        <th>Kart ID</th>
        <th>Şube</th>
        <th>Kullanılan</th>
        <th>Kalan</th>
        <th>Açıklama</th>
        <th>Var/Yok</th>
        <th>Kaydet</th>
    </tr>
    @foreach ($items as $index => $item)
        <tr>
            <td>{{ $item->hesapadi }}</td>
            <td>{{ $item->telefon }}</td>
            <td>{{ $item->cep }}</td>
            <td>{{ $item->hesapkodu }}</td>
            <td>{{ $item->kartid }}</td>
            <td>{{ $item->sube }}</td>
            <td>{{ $item->kullanilanmiktar }}</td>
            <td>{{ $item->kalanmiktar }}</td>
            <td><input type="text" maxlength="1500" class="form-control-sm form-control" form="form{{ $item->id }}" name="aciklama" value="{{ $item->aciklama }}"></td>
            <td><select class="form-control-sm form-control" form="form{{ $item->id }}" name="varyok">
                    <option value=""></option>
                    <option @if($item->varyok == 'Var') selected @endif value="Var">Var</option>
                    <option @if($item->varyok == 'Yok') selected @endif value="Yok">Yok</option>
                </select></td>
            <td><button type="button" form="form{{ $item->id }}" class="btn btn-success save btn-sm">Kaydet</button> </td>
        </tr>
        <form id="form{{ $item->id }}" method="post" action="">@csrf <input type="hidden" name="id" value="{{$item->id}}"></form>
    @endforeach
</table>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function () {
        $('.save').on('click',function () {
            var id = $(this).attr('form');

            var form = $('#'+id)
            console.log(form)
            $.ajax({
                url: "{{ route('plusCardPointUpdate') }}",
                type: "post",
                data: form.serialize(),
                success: function (response) {
                    alert("Başarıyla Güncellendi");
                }
            });
        });
    });
</script>
</body>
</html>
