@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Anket Sorusu <PERSON>')
@section('content')
    <form id="mainForm" method="post" action="{{ route('pool-questions.store') }}">@csrf
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgi" aria-expanded="true"
                                    aria-controls="aracBilgi">
                                Temel Bilgiler
                            </button>
                        </h2>
                        <div id="aracBilgi" class="accordion-collapse collapse show" aria-labelledby="aracBilgiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Soru <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="question" value="{{ \Cache::has('pool_create') ? \Cache::get('pool_create')['question'] : '' }}" placeholder="Soru" required>
                                    </div>
                                    <div class="col-xl-12 mt-2 soru-turu">
                                        <label class="form-label">Cevap Türü</label>
                                        <select name="type" class="form-control">
                                            <option value="text">Kısa Metin</option>
                                            <option value="textarea">Uzun Metin</option>
                                            <option value="select">Seçenek Listesi</option>
                                            <option value="checkbox">Çoklu Seçim</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control">
                                            <option value="1">Aktif</option>
                                            <option value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
                <button type="button" class="btn btn-info add-option mb-3" style="display: none">Seçenek Ekle</button>
            </div>
        </div>
        <input type="hidden" name="session_name" value="pool_create">
    </form>
    @if(\Illuminate\Support\Facades\Cache::has('pool_create'))
        <div class="modal fade" id="deleteCacheModal" tabindex="-1"
             aria-labelledby="deleteCacheModal" data-bs-keyboard="false"
             aria-hidden="true">
            <!-- Scrollable modal -->
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="staticBackdropLabel2">Mevcut Kayıt Bulundu!
                        </h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Önceden oluşturduğunuz kayıt kapanmadı!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success"
                                data-bs-dismiss="modal">Kayıta Devam Et</button>
                        <button type="submit" form="deleteCache" class="btn btn-danger">Yeni Kayıt Oluştur</button>
                        <form id="deleteCache" method="post" action="{{ route('deleteCache') }}">@csrf<input type="hidden" name="type" value="pool_create"></form>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script>
        addEventListener("DOMContentLoaded", (event) => {
            @if(\Illuminate\Support\Facades\Cache::has('pool_create'))
            $('#deleteCacheModal').modal('toggle')
            @endif
        });
        $('.add-option').on('click',function (){
            $('.soru-turu').after('<div class="col-xl-12 mt-2 option"><label class="form-label">Seçenek (Boş Veri Silinir)</label><input type="text" class="form-control" name="option[]" placeholder="Seçenek (Boş Veri Silinir)"></div>')
        })

        $('select[name="type"]').on('change',function (){
            let $val = $(this).val()
            if ($val == 'select' || $val == 'checkbox'){
                $('.add-option').css('display','inline-block')
                $('.option').css('display','block')
            }else{
                $('.add-option').css('display','none')
                $('.option').css('display','none')
            }
        })

        setInterval(function (){
            $.ajax({
                url: "{{ route('saveToSession') }}",
                type: "post",
                data: $('#mainForm').serialize(),
                success: function (response) {

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        },30 * 1000)
    </script>
@endpush
