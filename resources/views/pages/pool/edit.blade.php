@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Anket Sorusu Düzenle')
@section('content')
    <form method="post" action="{{ route('pool-questions.update',$poolQuestion) }}">@csrf @method('put')
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgi" aria-expanded="true"
                                    aria-controls="aracBilgi">
                                Temel Bilgiler
                            </button>
                        </h2>
                        <div id="aracBilgi" class="accordion-collapse collapse show" aria-labelledby="aracBilgiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Soru <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="question" placeholder="Soru" value="{{ $poolQuestion->question }}" required>
                                    </div>
                                    <div class="col-xl-12 mt-2 soru-turu">
                                        <label class="form-label">Cevap Türü</label>
                                        <select name="type" class="form-control">
                                            <option value="text">Kısa Metin</option>
                                            <option @if($poolQuestion->type == 'textarea') selected @endif value="textarea">Uzun Metin</option>
                                            <option @if($poolQuestion->type == 'select') selected @endif value="select">Seçenek Listesi</option>
                                            <option @if($poolQuestion->type == 'checkbox') selected @endif value="checkbox">Çoklu Seçim</option>
                                        </select>
                                    </div>
                                    @foreach($poolQuestion->getOptions as $option)
                                        <div class="col-xl-12 mt-2 option">
                                            <label class="form-label">Seçenek (Boş Veri Silinir)</label>
                                            <input type="text" class="form-control" name="option[]" value="{{ $option->option }}" placeholder="Seçenek (Boş Veri Silinir)">
                                        </div>
                                    @endforeach
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control">
                                            <option value="1">Aktif</option>
                                            <option value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
                <button type="button" class="btn btn-info add-option mb-3" @if($poolQuestion->type == 'text' || $poolQuestion->type == 'textarea') style="display: none" @endif>Seçenek Ekle</button>
            </div>
        </div>
    </form>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script>
        $('.add-option').on('click',function (){
            $('.soru-turu').after('<div class="col-xl-12 mt-2 option"><label class="form-label">Seçenek (Boş Veri Silinir)</label><input type="text" class="form-control" name="option[]" placeholder="Seçenek (Boş Veri Silinir)"></div>')
        })

        $('select[name="type"]').on('change',function (){
            let $val = $(this).val()
            if ($val == 'select' || $val == 'checkbox'){
                $('.add-option').css('display','inline-block')
                $('.option').css('display','block')
            }else{
                $('.add-option').css('display','none')
                $('.option').css('display','none')
            }
        })
    </script>
@endpush
