@extends('pages.build')
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Anket')
@section('content')
    <form method="post" action="{{ route('pool-choises.store') }}">@csrf
        <input type="hidden" name="uuid" value="{{ $_GET['uuid'] ?? '' }}">
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgi" aria-expanded="true"
                                    aria-controls="aracBilgi">
                                Anket Soruları
                            </button>
                        </h2>
                        <div id="aracBilgi" class="accordion-collapse collapse show" aria-labelledby="aracBilgiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    @if(auth('customer')->check())
                                        @foreach($questions as $question)
                                            <div class="col-xl-12 mt-3">
                                                <label class="form-label">{{ $question->question }}</label>
                                                @if($question->type == 'text')
                                                    <input type="text" class="form-control" name="answer[{{ $question->id }}]" required>
                                                @elseif($question->type == 'textarea')
                                                    <textarea class="form-control" name="answer[{{ $question->id }}]" required></textarea>
                                                @elseif($question->type == 'select')
                                                    <select name="answer[{{ $question->id }}]" class="form-control">
                                                        @foreach($question->getOptions as $option)
                                                            <option value="{{ $option->option }}">{{ $option->option }}</option>
                                                        @endforeach
                                                    </select>
                                                @elseif($question->type == 'checkbox')
                                                    @foreach($question->getOptions as $option)<br>
                                                    <label class="form-label">{{ $option->option }}</label>
                                                    <input type="checkbox" name="answer[{{ $question->id }}][]" value="{{ $option->option }}">
                                                    @endforeach
                                                @endif
                                            </div>
                                        @endforeach
                                    @else
                                        @foreach(\App\Models\PoolChoise::where('expertise_uuid',$_GET['uuid'])->get() as $choise)
                                            <div class="col-xl-12 mt-3">
                                                <label class="form-label">{{ $choise->question }}</label>
                                                <input type="text" class="form-control" disabled value="{{ $choise->answer }}">
                                            </div>
                                        @endforeach
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
