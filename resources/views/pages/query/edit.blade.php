@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Not Ekle')
@section('content')
    <form method="post" action="{{ route('notes.update',$note) }}">@csrf @method('put')
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgi" aria-expanded="true"
                                    aria-controls="aracBilgi">
                                Temel Bilgiler
                            </button>
                        </h2>
                        <div id="aracBilgi" class="accordion-collapse collapse show" aria-labelledby="aracBilgiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Not <span class="text-danger">*</span></label>
                                        <textarea class="form-control" name="note" required>{{ $note->note }}</textarea>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kayıt Yeri</label>
                                        <select name="key" class="form-control">
                                            <option value="car">Araç Kontrol</option>
                                            <option @if($note->key == 'brake') selected @endif value="brake">Fren Kontrol</option>
                                            <option @if($note->key == 'bodywork') selected @endif value="bodywork">Kaporta Kontrol</option>
                                            <option @if($note->key == 'diagnostic') selected @endif value="diagnostic">Diagnostic Kontrol</option>
                                            <option @if($note->key == 'internal') selected @endif value="internal">İç Kontrol</option>
                                            <option @if($note->key == 'sub_control') selected @endif value="sub_control">Alt Kontroller ve Motor</option>
                                            <option @if($note->key == 'tire_and_rim') selected @endif value="tire_and_rim">Lastik ve Jant</option>
                                            <option @if($note->key == 'component') selected @endif value="component">Komponent</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
