@extends('pages.build')
@section('title','<PERSON>i Rapor')
@section('content')
    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
        <a href="{{ route('reports',['type'=>'expertise']) }}" class="nav-link">Ekspertiz Raporları</a>
        @if($authUser->type == 'admin' || $authUser->zone_id > 0)
            <a href="{{ route('reports',['type'=>'stock']) }}" class="nav-link">Stok Raporları</a>
            <a href="{{ route('reports',['type'=>'branch']) }}" class="nav-link active"><PERSON>i Raporları</a>
            @if($authUser->type == 'admin')
                <a href="{{ route('reports',['type'=>'query']) }}" class="nav-link">Hasar Raporları</a>
            @endif
        @endif
        <a href="{{ route('reports',['type'=>'customer']) }}" class="nav-link"><PERSON><PERSON></a>
        <a href="{{ route('reports',['type'=>'car']) }}" class="nav-link">Araç Raporları</a>
        <a href="{{ route('reports',['type'=>'user']) }}" class="nav-link">Personel Raporları</a>
        <a href="{{ route('reports',['type'=>'endofdays']) }}" class="nav-link">Gün Sonu Raporu</a>
    </nav>
    <form id="filterForm" method="get" action="{{ route('reports',['type'=>'branch']) }}">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>Başlangıç Tarihi</label>
                            <input type="date" class="form-control" name="start_date" value="{{ $filters['startDate'] }}">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>Bitiş Tarihi</label>
                            <input type="date" class="form-control" name="end_date" value="{{ $filters['endDate'] }}">
                        </div>
                    </div>
                    @if($authUser->type == 'admin' || $authUser->zone_id > 0)
                        <div class="col">
                            <div class="form-group">
                                <label>Bayi</label>
                                <select class="form-control select2" name="branch_id">
                                    @if($authUser->type == 'admin')
                                        @foreach(\App\Models\Branch::get() as $filterBranch)
                                            <option @if((!empty($selectedSavedFilter) && $selectedSavedFilter->branch_id == $filterBranch->id )||(isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id)) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                        @endforeach
                                    @else
                                        @foreach(\App\Models\ZoneBranch::where('zone_id',$authUser->zone_id)->get() as $zoneBranch)
                                            <option @if((!empty($selectedSavedFilter) && $selectedSavedFilter->branch_id == $zoneBranch->branch_id )||(isset($_GET['branch_id']) && $_GET['branch_id'] == $zoneBranch->branch_id)) selected @endif value="{{ $zoneBranch->branch_id }}">{{ \App\Models\Branch::where('id',$zoneBranch->branch_id)->first()->kisa_ad }}</option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                        </div>
                    @endif
                    <div class="col mt-4">
                        <button class="btn btn-success">Listele</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="row">
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image1">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12 ">Toplam Bayi</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalBranchCount }}</h4>
                                </div>
                                <p class="mb-0 fs-12 text-muted">Yeni Eklenen<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $newAddedBranchCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-success-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-user fs-16 text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image2">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Aktif Bayi Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $activeBranchCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-info-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-check fs-16 text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image3">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Pasif Bayi Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalBranchCount - $activeBranchCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-secondary-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-x-square fs-16 text-secondary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Hizmet Veren</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseBranch != null ? \App\Models\Branch::where('id',$mostExpertiseBranch)->first()->kisa_ad : '-' }} ({{ $mostExpertiseBranchExpertiseCount }})</h6>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseBranchExpertiseCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-4 col-md-4 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Cari Sahip</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostCustomerBranch != null ? \App\Models\Branch::where('id',$mostCustomerBranch)->first()->unvan : '-' }} ({{ $mostCustomerBranchCustomerCount }})</h6>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostCustomerBranchCustomerCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-4 col-md-4 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Az Hizmet Veren</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $lessExpertiseBranch != null ? \App\Models\Branch::where('id',$lessExpertiseBranch)->first()->unvan : '-' }} ({{ $lessExpertiseBranchExpertiseCount }})</h6>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $lessExpertiseBranchExpertiseCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-lg-4 col-md-4 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Az Cari Sahip</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $lessCustomerBranch != null ? \App\Models\Branch::where('id',$lessCustomerBranch)->first()->unvan : '-' }} ({{ $lessCustomerBranchCustomerCount }})</h6>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $lessCustomerBranchCustomerCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">İllere Göre Bayi Sayısı</div>
                </div>
                <div class="card-body">
                    <div id="stepline-chart"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Ekspertiz Sayıları</div>
                </div>
                <div class="card-body">
                    <div id="bar-basic"></div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/libs/apexcharts/apexcharts.min.js"></script>
    <script>
        var options = {
            series: [{
                data: [
                    @foreach($branchCountsByCity as $cityBranchCount)
                    {{ $cityBranchCount['branch_count'] }},
                    @endforeach
                ]
            }],
            labels:[
                @foreach($branchCountsByCity as $cityBranchCount)
                    '{{ $cityBranchCount['il'] }}',
                @endforeach
            ],
            chart: {
                type: 'line',
                height: 345
            },
            stroke: {
                curve: 'stepline',
            },
            grid: {
                borderColor: '#f2f5f7',
            },
            dataLabels: {
                enabled: false
            },
            colors: ["#38cab3"],
            markers: {
                hover: {
                    sizeOffset: 4
                }
            },
            xaxis: {
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-xaxis-label',
                    },
                }
            },
            yaxis: {
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-yaxis-label',
                    },
                }
            }
        };
        var chart2 = new ApexCharts(document.querySelector("#stepline-chart"), options);
        chart2.render();

        var options = {
            series: [{
                data: [
                    @foreach($expertises as $expertise)
                    {{ $expertise['count'] }},
                    @endforeach
                ]
            }],
            chart: {
                type: 'bar',
                height: 320
            },
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    horizontal: true,
                }
            },
            colors: ["#38cab3"],
            grid: {
                borderColor: '#f2f5f7',
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                categories:  [
                    @foreach($expertises as $expertise)
                        '{{ $expertise['start_date'] }} - {{ $expertise['end_date'] }}',
                    @endforeach
                ],
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-xaxis-label',
                    },
                }
            },
            yaxis: {
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-yaxis-label',
                    },
                }
            }
        };
        var chart = new ApexCharts(document.querySelector("#bar-basic"), options);
        chart.render();
    </script>
@endpush
