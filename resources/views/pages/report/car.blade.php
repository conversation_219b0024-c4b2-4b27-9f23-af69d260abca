@extends('pages.build')
@section('title','Araç Rapor')
@section('content')
    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
        <a href="{{ route('reports',['type'=>'expertise']) }}" class="nav-link">Ekspertiz Raporları</a>
        @if($authUser->type == 'admin' || $authUser->zone_id > 0)
            <a href="{{ route('reports',['type'=>'stock']) }}" class="nav-link">Stok Raporları</a>
            <a href="{{ route('reports',['type'=>'branch']) }}" class="nav-link"><PERSON><PERSON></a>
            @if($authUser->type == 'admin')
                <a href="{{ route('reports',['type'=>'query']) }}" class="nav-link">Hasar Raporları</a>
            @endif
        @endif
        <a href="{{ route('reports',['type'=>'customer']) }}" class="nav-link"><PERSON><PERSON></a>
        <a href="{{ route('reports',['type'=>'car']) }}" class="nav-link active">Araç Raporları</a>
        <a href="{{ route('reports',['type'=>'user']) }}" class="nav-link">Personel Raporları</a>
        <a href="{{ route('reports',['type'=>'endofdays']) }}" class="nav-link">Gün Sonu Raporu</a>
    </nav>
    <form id="filterForm" method="get" action="{{ route('reports',['type'=>'car']) }}">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>Başlangıç Tarihi</label>
                            <input type="date" class="form-control" name="start_date" value="{{ $filters['startDate'] }}">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>Bitiş Tarihi</label>
                            <input type="date" class="form-control" name="end_date" value="{{ $filters['endDate'] }}">
                        </div>
                    </div>
                    @if($authUser->type == 'admin')
                        <div class="col">
                            <div class="form-group">
                                <label>Bayi</label>
                                <select class="form-control select2" name="branch_id">
                                    @foreach(\App\Models\Branch::get() as $branch)
                                        <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $branch->id) selected @endif value="{{ $branch->id }}">{{ $branch->kisa_ad }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    @endif
                    <div class="col mt-4">
                        <button class="btn btn-success">Listele</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="row">
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image1">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12 ">Toplam Araç</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalCarCount }}</h4>
                                </div>
                                <p class="mb-0 fs-12 text-muted">Yeni Eklenen<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $newAddedCarCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-success-transparent text-center align-self-center overflow-hidden">
                            <i class="bx bx-car fs-16 text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image2">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Aktif Araç Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $activeCarCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-info-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-check fs-16 text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image3">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Pasif Araç Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalCarCount - $activeCarCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-secondary-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-x-square fs-16 text-secondary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Hizmet Alan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseCar != null ? \App\Models\Car::where('id',$mostExpertiseCar)->first()->plaka : '-' }}</h6>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseCarExpertiseCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Araç Sahibi Kişi</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostCustomerCar != null ? \App\Models\Customer::where('id',$mostCustomerCar->customer_id)->first()->unvan : '-' }}</h6>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold">{{ $mostCustomerCar->car_count ?? 0 }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Kasa Türüne Göre Araç Sayısı</div>
                </div>
                <div class="card-body">
                    <canvas id="case-type-pie" class="chartjs-chart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Yakıt Türüne Göre Araç Sayısı</div>
                </div>
                <div class="card-body">
                    <canvas id="fuel-pie" class="chartjs-chart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Vites Türüne Göre Araç Sayısı</div>
                </div>
                <div class="card-body">
                    <canvas id="gear-pie" class="chartjs-chart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">En Fazla Kayıtlı Grup</div>
                </div>
                <div class="card-body">
                    <canvas id="group-pie" class="chartjs-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/libs/apexcharts/apexcharts.min.js"></script>
    <script src="/assets/libs/chart.js/chart.min.js"></script>
    <script>
        const data2 = {
            labels: [
                @foreach($carCaseTypes as $carCaseType)
                    '{{ $carCaseType['name'] }}',
                @endforeach
            ],
            datasets: [{
                data: [
                    @foreach($carCaseTypes as $carCaseType)
                        '{{ $carCaseType['car_count'] }}',
                    @endforeach
                ],
                backgroundColor: [
                    @foreach($carCaseTypes as $carCaseType)
                        'rgb({{ rand(10,255) }},{{ rand(10,255) }},{{ rand(10,255) }})',
                    @endforeach
                ],
                hoverOffset: 4
            }]
        };
        const config3 = {
            type: 'pie',
            data: data2,
        };
        const myChart2 = new Chart(
            document.getElementById('case-type-pie'),
            config3
        );

        const dataFuel = {
            labels: [
                @foreach($carFuels as $carFuel)
                    '{{ $carFuel['name'] }}',
                @endforeach
            ],
            datasets: [{
                data: [
                    @foreach($carFuels as $carFuel)
                        '{{ $carFuel['car_count'] }}',
                    @endforeach
                ],
                backgroundColor: [
                    @foreach($carFuels as $carFuel)
                        'rgb({{ rand(10,255) }},{{ rand(10,255) }},{{ rand(10,255) }})',
                    @endforeach
                ],
                hoverOffset: 4
            }]
        };
        const configFuel = {
            type: 'pie',
            data: dataFuel,
        };
        const fuelPie = new Chart(
            document.getElementById('fuel-pie'),
            configFuel
        );

        const dataGear = {
            labels: [
                @foreach($carGears as $carGear)
                    '{{ $carGear['name'] }}',
                @endforeach
            ],
            datasets: [{
                data: [
                    @foreach($carGears as $carGear)
                        '{{ $carGear['car_count'] }}',
                    @endforeach
                ],
                backgroundColor: [
                    @foreach($carGears as $carGear)
                        'rgb({{ rand(10,255) }},{{ rand(10,255) }},{{ rand(10,255) }})',
                    @endforeach
                ],
                hoverOffset: 4
            }]
        };
        const configGear = {
            type: 'pie',
            data: dataGear,
        };
        const gearPie = new Chart(
            document.getElementById('gear-pie'),
            configGear
        );

        const dataGroup = {
            labels: [
                @foreach($carGroups as $carGroup)
                    '{{ $carGroup['group'] }}',
                @endforeach
            ],
            datasets: [{
                data: [
                    @foreach($carGroups as $carGroup)
                        '{{ $carGroup['count'] }}',
                    @endforeach
                ],
                backgroundColor: [
                    @foreach($carGroups as $carGroup)
                        'rgb({{ rand(10,255) }},{{ rand(10,255) }},{{ rand(10,255) }})',
                    @endforeach
                ],
                hoverOffset: 4
            }]
        };
        const configGroup = {
            type: 'pie',
            data: dataGroup,
        };
        const groupPie = new Chart(
            document.getElementById('group-pie'),
            configGroup
        );
    </script>
@endpush
