@extends('pages.build')
@section('title','Cari Rapor')
@section('content')
    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
        <a href="{{ route('reports',['type'=>'expertise']) }}" class="nav-link">Ekspertiz Raporları</a>
        @if($authUser->type == 'admin' || $authUser->zone_id > 0)
            <a href="{{ route('reports',['type'=>'stock']) }}" class="nav-link">Stok Raporları</a>
            <a href="{{ route('reports',['type'=>'branch']) }}" class="nav-link"><PERSON>i Raporları</a>
            @if($authUser->type == 'admin')
                <a href="{{ route('reports',['type'=>'query']) }}" class="nav-link">Hasar Raporları</a>
            @endif
        @endif
        <a href="{{ route('reports',['type'=>'customer']) }}" class="nav-link active"><PERSON><PERSON></a>
        <a href="{{ route('reports',['type'=>'car']) }}" class="nav-link">Araç Raporları</a>
        <a href="{{ route('reports',['type'=>'user']) }}" class="nav-link">Personel Raporları</a>
        <a href="{{ route('reports',['type'=>'endofdays']) }}" class="nav-link">Gün Sonu Raporu</a>
    </nav>
    <form id="filterForm" method="get" action="{{ route('reports',['type'=>'customer']) }}">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>Başlangıç Tarihi</label>
                            <input type="date" class="form-control" name="start_date" value="{{ $filters['startDate'] }}">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>Bitiş Tarihi</label>
                            <input type="date" class="form-control" name="end_date" value="{{ $filters['endDate'] }}">
                        </div>
                    </div>
                    @if($authUser->type == 'admin')
                        <div class="col">
                            <div class="form-group">
                                <label>Bayi</label>
                                <select class="form-control select2" name="branch_id">
                                    @foreach(\App\Models\Branch::get() as $filterBranch)
                                        <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    @endif
                    <div class="col mt-4">
                        <button class="btn btn-success">Listele</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="row">
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image1">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12 ">Toplam Cari</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalCustomerCount }}</h4>
                                </div>
                                <p class="mb-0 fs-12 text-muted">Yeni Eklenen<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $newAddedCustomerCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-success-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-user fs-16 text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image2">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Aktif Cari Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $activeCustomerCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-info-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-check fs-16 text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image3">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Pasif Cari Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalCustomerCount - $activeCustomerCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-secondary-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-x-square fs-16 text-secondary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Hizmet Alan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <h6 class="fs-20 fw-semibold mb-2">
                                    {{ $mostExpertiseCustomer != null && \App\Models\Customer::where('id', $mostExpertiseCustomer)->first() != null ? \App\Models\Customer::where('id', $mostExpertiseCustomer)->first()->unvan : '-' }}
                                </h6>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseCustomerExpertiseCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-warning-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-credit-card fs-16 text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">İl Dağılıma Göre Cariler</div>
                </div>
                <div class="card-body">
                    <div id="treemap-basic"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Cari Türüne Göre Dağılım</div>
                </div>
                <div class="card-body">
                    <div id="donut-simple"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Araç Sayısına Göre Dağılım</div>
                </div>
                <div class="card-body">
                    <div id="area-basic"></div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/libs/apexcharts/apexcharts.min.js"></script>
    <script>
        var options = {
            series: [
                {
                    data: [
                        @foreach($top15CustomerCountForCity as $top15CustomerCount)
                        {
                            x: '{{ $top15CustomerCount['sehir'] }}',
                            y: {{ $top15CustomerCount['count'] }}
                        },
                        @endforeach
                    ]
                }
            ],
            legend: {
                show: false
            },
            chart: {
                height: 350,
                type: 'treemap'
            },
            colors: ["#e12222"],
        };
        var chart = new ApexCharts(document.querySelector("#treemap-basic"), options);
        chart.render();

        var options = {
            series: [
                @foreach($customersByType as $type => $customerTypeCount)
                {{ $customerTypeCount }},
                @endforeach

            ],
            labels: [
                @foreach($customersByType as $type => $customerTypeCount)
                   ' {{ $type }}',
                @endforeach
            ],
            chart: {
                type: 'donut',
                height: 290
            },
            legend: {
                position: 'bottom'
            },
            colors: ["#53c2c2", "#fffd7e"],
            dataLabels: {
                dropShadow: {
                    enabled: false
                }
            },
        };
        var chart = new ApexCharts(document.querySelector("#donut-simple"), options);
        chart.render();

        var options = {
            series: [{
                name: "Kişi Sayısı",
                data: [
                    @foreach($carCountByCustomer as $carCount => $customerCount)
                        {{ $customerCount }},
                    @endforeach
                ]
            }],
            chart: {
                type: 'area',
                height: 320,
                zoom: {
                    enabled: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'straight',
            },
            grid: {
                borderColor: '#f2f5f7',
            },
            labels: [
                @foreach($carCountByCustomer as $carCount => $customerCount)
                    {{ $carCount }},
                @endforeach
            ],
            colors: ['#96ff7e'],
            xaxis: {
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-xaxis-label',
                    },
                }
            },
            yaxis: {
                opposite: true,
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-xaxis-label',
                    },
                }
            },
            legend: {
                horizontalAlign: 'left'
            }
        };
        var chart = new ApexCharts(document.querySelector("#area-basic"), options);
        chart.render();
    </script>
@endpush
