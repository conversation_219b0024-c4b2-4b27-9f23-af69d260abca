@extends('pages.build')
@section('title','Ekspertiz Rapor')
@section('content')
    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
        <a href="{{ route('reports',['type'=>'expertise']) }}" class="nav-link active">Ekspertiz Raporları</a>
        @if($authUser->type == 'admin' || $authUser->zone_id > 0)
            <a href="{{ route('reports',['type'=>'stock']) }}" class="nav-link">Stok Raporları</a>
            <a href="{{ route('reports',['type'=>'branch']) }}" class="nav-link">Bayi Raporları</a>
            @if($authUser->type == 'admin')
                <a href="{{ route('reports',['type'=>'query']) }}" class="nav-link">Hasar Raporları</a>
            @endif
        @endif
        <a href="{{ route('reports',['type'=>'customer']) }}" class="nav-link"><PERSON><PERSON></a>
        <a href="{{ route('reports',['type'=>'car']) }}" class="nav-link">Araç Raporları</a>
        <a href="{{ route('reports',['type'=>'user']) }}" class="nav-link">Personel Raporları</a>
        <a href="{{ route('reports',['type'=>'endofdays']) }}" class="nav-link">Gün Sonu Raporu</a>
    </nav>
    <form id="filterForm" method="get" action="{{ route('reports',['type'=>'expertise']) }}">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>Başlangıç Tarihi</label>
                            <input type="date" class="form-control" name="start_date" value="{{ $filters['startDate'] }}">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>Bitiş Tarihi</label>
                            <input type="date" class="form-control" name="end_date" value="{{ $filters['endDate'] }}">
                        </div>
                    </div>
                    @if($authUser->type == 'admin')
                        <div class="col">
                            <div class="form-group">
                                <label>Bayi</label>
                                <select class="form-control select2" name="branch_id">
                                    @foreach(\App\Models\Branch::all() as $filterBranch)
                                        <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    @endif
                    <div class="col mt-4">
                        <button class="btn btn-success">Listele</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="row">
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image1">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12 ">Toplam Ekspertiz</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalExpertiseCount }}</h4>
                                </div>
                                <p class="mb-0 fs-12 text-muted">Yeni Eklenen<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $newAddedExpertiseCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-primary-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-grid fs-16 text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image2">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Aktif Ekspertiz</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $activeExpertiseCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-info-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-check fs-16 text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image3">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Pasif Ekspertiz</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalExpertiseCount - $activeExpertiseCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-secondary-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-x-square fs-16 text-secondary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Kayıt Açan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseBranch != null ? $mostExpertiseBranch->kisa_ad : '-' }}</h4>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseBranchCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Az Kayıt Açan (0'dan farklı)</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $lessExpertiseBranch != null ? $lessExpertiseBranch->kisa_ad : '-' }}</h4>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $lessExpertiseBranchCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Ekspertiz Yaptıran Ruhsat Sahibi</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseCustomer != null ? ($mostExpertiseCustomer->unvan ?? $mostExpertiseCustomer->ad . ' ' . $mostExpertiseCustomer->soyad): '-' }}</h4>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseCustomerCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Ekspertiz Yaptıran Satıcı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseSatici != null ? ($mostExpertiseSatici->unvan ?? $mostExpertiseSatici->ad . ' ' . $mostExpertiseSatici->soyad): '-' }}</h4>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseSaticiCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Ekspertiz Yaptıran Alıcı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseAlici != null ? ($mostExpertiseAlici->unvan ?? $mostExpertiseAlici->ad . ' ' . $mostExpertiseAlici->soyad): '-' }}</h4>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseAliciCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Ekspertiz Yaptıran Araç</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseCar != null ? $mostExpertiseCar->plaka: '-' }}</h4>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseCarCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Müşteri Nereden Geldi</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseComeFrom }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ 0 }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Ödeme Türü</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">
                                        @if($mostExpertisePaymentType == 'nakit')
                                            Nakit
                                        @elseif($mostExpertisePaymentType == 'banka')
                                            Banka
                                        @elseif($mostExpertisePaymentType == 'kredi_karti')
                                            Kredi Kartı
                                        @elseif($mostExpertisePaymentType == 'acik_hesap')
                                            Açık Hesap
                                        @endif
                                    </h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseStock['count'] ?? 0 }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Satan Stok</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseStock['stock'] }}</h4>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseStockCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Ekspertiz Sayıları</div>
                </div>
                <div class="card-body">
                    <div id="bar-basic"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Tarih Aralığına Göre Ödeme Türleri</div>
                </div>
                <div class="card-body">
                    <div id="echart-stacked-line" class="echart-charts"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Tarih Aralığına Göre Kazanç</div>
                </div>
                <div class="card-body">
                    <div id="bar-full"></div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/libs/apexcharts/apexcharts.min.js"></script>
    <script src="/assets/libs/echarts/echarts.min.js"></script>
    <script>
        var options = {
            series: [{
                data: [
                    @foreach($expertises as $expertise)
                        {{ $expertise['count'] }},
                    @endforeach
                ]
            }],
            chart: {
                type: 'bar',
                height: 320
            },
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    horizontal: false,
                }
            },
            colors: ["#38cab3"],
            grid: {
                borderColor: '#f2f5f7',
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                categories:  [
                    @foreach($expertises as $expertise)
                        '{{ $expertise['start_date'] }} - {{ $expertise['end_date'] }}',
                    @endforeach
                ],
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-xaxis-label',
                    },
                }
            },
            yaxis: {
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-yaxis-label',
                    },
                }
            }
        };
        var chart = new ApexCharts(document.querySelector("#bar-basic"), options);
        chart.render();

        var dom = document.getElementById('echart-stacked-line');
        var myChart = echarts.init(dom, null, {
            renderer: 'canvas',
            useDirtyRect: false
        });
        var app = {};
        var option;
        option = {
            grid: {
                left: "0%",
                right: "0%",
                bottom: "0%",
                top: "10%",
                containLabel: true
            },
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: [
                    @foreach(__('arrays.payment_types') as $paymentType)
                        '{{ $paymentType }}',
                    @endforeach
                ],
                textStyle: {
                    color: '#777'
                }
            },
            toolbox: {
                feature: {
                    saveAsImage: {}
                }
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: [
                    @foreach($expertisePayments as $expertisePayment)
                        '{{ $expertisePayment['start_date'] }} - {{ $expertisePayment['end_date'] }}',
                    @endforeach
                ],
                axisLine: {
                    lineStyle: {
                        color: "#8c9097"
                    }
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    lineStyle: {
                        color: "#8c9097"
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: "rgba(142, 156, 173,0.1)"
                    }
                }
            },
            series: [
                @foreach(__('arrays.payment_types') as $paymentKey => $paymentType)
                {
                    name: '{{ $paymentType }}',
                    type: 'line',
                    data: [
                        @foreach($expertisePayments as $index => $expertisePayment)
                        {{ $expertisePayment['counts'][$paymentKey]['count'] }},
                        @endforeach

                    ]
                },
                @endforeach
            ],
            color: [
                @foreach(__('arrays.payment_types') as $paymentKey => $paymentType)
                    "{{ '#' . str_pad(dechex(mt_rand(0, 0xFFFFFF)), 6, '0', STR_PAD_LEFT) }}",
                @endforeach

            ]
        };
        if (option && typeof option === 'object') {
            myChart.setOption(option);
        }
        window.addEventListener('resize', myChart.resize);

        var options = {
            series: [
                @foreach(__('arrays.payment_types') as $paymentKey => $paymentType)
                    {
                        name: '{{ $paymentType }}',
                        data:[
                            @foreach($expertisePayments as $index => $expertisePayment)
                                {{ $expertisePayment['counts'][$paymentKey]['total'] }},
                            @endforeach

                        ]
                    },
                @endforeach
            ],
            chart: {
                type: 'bar',
                height: 320,
                stacked: true,
                stackType: '100%'
            },
            plotOptions: {
                bar: {
                    horizontal: true,
                },
            },
            stroke: {
                width: 1,
                colors: ['#fff']
            },
            colors: [
                @foreach(__('arrays.payment_types') as $paymentKey => $paymentType)
                    "{{ '#' . str_pad(dechex(mt_rand(0, 0xFFFFFF)), 6, '0', STR_PAD_LEFT) }}",
                @endforeach
            ],
            grid: {
                borderColor: '#f2f5f7',
            },
            xaxis: {
                categories: [
                    @foreach($expertisePayments as $expertisePayment)
                        '{{ $expertisePayment['start_date'] }} - {{ $expertisePayment['end_date'] }}',
                    @endforeach
                ],
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-xaxis-label',
                    },
                }
            },
            yaxis: {
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-yaxis-label',
                    },
                }
            },
            tooltip: {
                y: {
                    formatter: function (val) {
                        return val + "₺"
                    }
                }
            },
            fill: {
                opacity: 1

            },
            legend: {
                position: 'top',
                horizontalAlign: 'left',
                offsetX: 40
            }
        };
        var chart = new ApexCharts(document.querySelector("#bar-full"), options);
        chart.render();
    </script>
@endpush
