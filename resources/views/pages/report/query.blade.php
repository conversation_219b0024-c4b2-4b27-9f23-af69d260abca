@extends('pages.build')
@section('title','Hasar Rapor')
@push('css')
    <style>
        .arabasorgula{
            background-color: #e2626b !important;
        }
        .otosorgu{
            background-color: #fecc16 !important;
        }
    </style>
@endpush
@section('content')
    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
        <a href="{{ route('reports',['type'=>'expertise']) }}" class="nav-link">Ekspertiz Raporları</a>
        @if($authUser->type == 'admin' || $authUser->zone_id > 0)
            <a href="{{ route('reports',['type'=>'stock']) }}" class="nav-link">Stok Raporları</a>
            <a href="{{ route('reports',['type'=>'branch']) }}" class="nav-link">Bayi Raporları</a>
            @if($authUser->type == 'admin')
                <a href="{{ route('reports',['type'=>'query']) }}" class="nav-link active">Hasar Raporları</a>
            @endif
        @endif
        <a href="{{ route('reports',['type'=>'customer']) }}" class="nav-link">Cari Raporları</a>
        <a href="{{ route('reports',['type'=>'car']) }}" class="nav-link">Araç Raporları</a>
        <a href="{{ route('reports',['type'=>'user']) }}" class="nav-link">Personel Raporları</a>
        <a href="{{ route('reports',['type'=>'endofdays']) }}" class="nav-link">Gün Sonu Raporu</a>
    </nav>
    <form id="filterForm" method="get" action="{{ route('reports',['type'=>'query']) }}">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Başlangıç Tarihi</label>
                            <input type="date" class="form-control form-control-sm" name="start_date" value="{{ $filters['startDate'] }}">
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="form-group">
                            <label>Bitiş Tarihi</label>
                            <input type="date" class="form-control form-control-sm" name="end_date" value="{{ $filters['endDate'] }}">
                        </div>
                    </div>
                    @if($authUser->type == 'admin')
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Bayi</label>
                                <select class="form-control form-control-sm select2" name="branch_id">
                                    <option value="">Tümü</option>
                                    @foreach(\App\Models\Branch::get() as $branch)
                                        <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $branch->id) selected @endif value="{{ $branch->id }}">{{ $branch->kisa_ad }} ({{ $branch->kod }})</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Komisyon Ücretlerini Ekle</label>
                                <select class="form-control form-control-sm select2" name="add_commissions">
                                    <option value="0">Hayır</option>
                                    <option @if(isset($_GET['add_commissions']) && $_GET['add_commissions'] == 1) selected @endif value="1">Evet</option>
                                </select>
                            </div>
                        </div>
{{--                        <div class="col-md-2 d-none">--}}
{{--                            <div class="form-group">--}}
{{--                                <label>Hasar Sorgu Firma</label>--}}
{{--                                <select class="form-control form-control-sm select2" name="hasar_sorgu_firma">--}}
{{--                                    <option value="">Tümü</option>--}}
{{--                                    <option @if(isset($_GET['hasar_sorgu_firma']) && $_GET['hasar_sorgu_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>--}}
{{--                                    <option @if(isset($_GET['hasar_sorgu_firma']) && $_GET['hasar_sorgu_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>--}}
{{--                                </select>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="col-md-2 d-none">--}}
{{--                            <div class="form-group">--}}
{{--                                <label>Kilometre Sorgu Firma</label>--}}
{{--                                <select class="form-control form-control-sm select2" name="kilometre_sorgu_firma">--}}
{{--                                    <option value="">Tümü</option>--}}
{{--                                    <option @if(isset($_GET['kilometre_sorgu_firma']) && $_GET['kilometre_sorgu_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>--}}
{{--                                    <option @if(isset($_GET['kilometre_sorgu_firma']) && $_GET['kilometre_sorgu_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>--}}
{{--                                </select>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="col-md-2 d-none">--}}
{{--                            <div class="form-group">--}}
{{--                                <label>Borç Sorgu Firma</label>--}}
{{--                                <select class="form-control form-control-sm select2" name="borc_sorgu_firma">--}}
{{--                                    <option value="">Tümü</option>--}}
{{--                                    <option @if(isset($_GET['borc_sorgu_firma']) && $_GET['borc_sorgu_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>--}}
{{--                                    <option @if(isset($_GET['borc_sorgu_firma']) && $_GET['borc_sorgu_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>--}}
{{--                                </select>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="col-md-2 d-none">--}}
{{--                            <div class="form-group">--}}
{{--                                <label>Değisen Sorgu Firma</label>--}}
{{--                                <select class="form-control form-control-sm select2" name="degisen_sorgu_firma">--}}
{{--                                    <option value="">Tümü</option>--}}
{{--                                    <option @if(isset($_GET['degisen_sorgu_firma']) && $_GET['degisen_sorgu_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>--}}
{{--                                    <option @if(isset($_GET['degisen_sorgu_firma']) && $_GET['degisen_sorgu_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>--}}
{{--                                </select>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="col-md-2 d-none">--}}
{{--                            <div class="form-group">--}}
{{--                                <label>Ruhsat Sorgu Firma</label>--}}
{{--                                <select class="form-control form-control-sm select2" name="ruhsat_sorgu_firma">--}}
{{--                                    <option value="">Tümü</option>--}}
{{--                                    <option @if(isset($_GET['ruhsat_sorgu_firma']) && $_GET['ruhsat_sorgu_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>--}}
{{--                                    <option @if(isset($_GET['ruhsat_sorgu_firma']) && $_GET['ruhsat_sorgu_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>--}}
{{--                                </select>--}}
{{--                            </div>--}}
{{--                        </div>--}}
{{--                        <div class="col-md-2 d-none">--}}
{{--                            <div class="form-group">--}}
{{--                                <label>Detay Sorgu Firma</label>--}}
{{--                                <select class="form-control form-control-sm select2" name="detail_sorgu_firma">--}}
{{--                                    <option value="">Tümü</option>--}}
{{--                                    <option @if(isset($_GET['detail_sorgu_firma']) && $_GET['detail_sorgu_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>--}}
{{--                                    <option @if(isset($_GET['detail_sorgu_firma']) && $_GET['detail_sorgu_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>--}}
{{--                                </select>--}}
{{--                            </div>--}}
{{--                        </div>--}}
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Sorgu Firması</label>
                                <select class="form-control form-control-sm select2" name="sorgu_firma">
                                    <option value="">Tümü</option>
                                    <option @if(isset($_GET['sorgu_firma']) && $_GET['sorgu_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>
                                    <option @if(isset($_GET['sorgu_firma']) && $_GET['sorgu_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>
                                </select>
                            </div>
                        </div>
                    @endif
                    <div class="col mt-4">
                        <button class="btn btn-danger btn-sm">Listele</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card">
            <div class="card-body">
                @if($authUser->type == 'admin')
                    <div style="display: flex">
                        <div style="width: 20px;height: 20px;background-color: #e2626b"></div> ArabaSorgula.com
                    </div>
                    <div style="display: flex">
                        <div style="width: 20px;height: 20px;background-color: #fecc16"></div> Otosorgu.com
                    </div>
                @endif
                <div class="table-responsive">
                    <table class="table table-bordered mt-3">
                        <thead style="position: sticky;top: -13px;z-index:1;">
                        <tr class="text-center">
                            <th colspan="2">Bayi Bilgileri</th>
                            <th colspan="2">Borç Sorgu</th>
                            <th colspan="2">Hasar Sorgu</th>
                            <th colspan="2">Kilometre Sorgu</th>
                            <th colspan="2">Ruhsat Sorgu</th>
                            <th colspan="2">Değişen Sorgu</th>
                            <th colspan="2">Detay Sorgu</th>
                            <th rowspan="2">Toplam Tutar</th>
                        </tr>
                        <tr>
                            <th>Bayi Adı</th>
                            <th>Bayi Kodu</th>
                            <th>Adet</th>
                            <th>Tutar</th>
                            <th>Adet</th>
                            <th>Tutar</th>
                            <th>Adet</th>
                            <th>Tutar</th>
                            <th>Adet</th>
                            <th>Tutar</th>
                            <th>Adet</th>
                            <th>Tutar</th>
                            <th>Adet</th>
                            <th>Tutar</th>
                        </tr>
                        </thead>
                        <tbody>
                        @php
                            $totalBorcAdet = 0;
                            $totalBorcTutar = 0;
                            $totalHasarAdet = 0;
                            $totalHasarTutar = 0;
                            $totalKilometreAdet = 0;
                            $totalKilometreTutar = 0;
                            $totalRuhsatAdet = 0;
                            $totalRuhsatTutar = 0;
                            $totalDegisenAdet = 0;
                            $totalDegisenTutar = 0;
                            $totalDetayAdet = 0;
                            $totalDetayTutar = 0;
                            $totalTutar = 0;
                        @endphp
                        @foreach($branches as $branch)
                            @php
                                $totalBorcAdet += $branch['borcAdet'];
                                $totalBorcTutar += $branch['borcTutar'];
                                $totalHasarAdet += $branch['hasarAdet'];
                                $totalHasarTutar += $branch['hasarTutar'];
                                $totalKilometreAdet += $branch['kilometreAdet'];
                                $totalKilometreTutar += $branch['kilometreTutar'];
                                $totalRuhsatAdet += $branch['ruhsatAdet'];
                                $totalRuhsatTutar += $branch['ruhsatTutar'];
                                $totalDegisenAdet += $branch['degisenAdet'];
                                $totalDegisenTutar += $branch['degisenTutar'];
                                $totalDetayAdet += $branch['detayAdet'];
                                $totalDetayTutar += $branch['detayTutar'];
                                $branchTotalTutar = $branch['borcTutar'] + $branch['hasarTutar'] + $branch['kilometreTutar'] + $branch['ruhsatTutar'] + $branch['degisenTutar'] + $branch['detayTutar'];
                                $totalTutar += $branchTotalTutar;
                                $fields = ['borc','hasar','degisen','detail','kilometre','ruhsat'];
                            @endphp
                            <tr style="cursor: pointer" data-bs-toggle="collapse" data-bs-target="#details{{ $branch['id'] }}" aria-expanded="false" aria-controls="details{{ $branch['id'] }}">
                                <td>{{ $branch['kisa_ad'] }}</td>
                                <td>{{ $branch['kod'] }}</td>
                                <td>{{ $branch['borcAdet'] }}</td>
                                <td>{{ $branch['borcTutar'] }}₺</td>
                                <td>{{ $branch['hasarAdet'] }}</td>
                                <td>{{ $branch['hasarTutar'] }}₺</td>
                                <td>{{ $branch['kilometreAdet'] }}</td>
                                <td>{{ $branch['kilometreTutar'] }}₺</td>
                                <td>{{ $branch['ruhsatAdet'] }}</td>
                                <td>{{ $branch['ruhsatTutar'] }}₺</td>
                                <td>{{ $branch['degisenAdet'] }}</td>
                                <td>{{ $branch['degisenTutar'] }}₺</td>
                                <td>{{ $branch['detayAdet'] }}</td>
                                <td>{{ $branch['detayTutar'] }}₺</td>
                                <td>{{ $branch['borcTutar'] + $branch['hasarTutar'] + $branch['kilometreTutar'] + $branch['ruhsatTutar'] + $branch['degisenTutar'] + $branch['detayTutar'] }}₺</td>
                            </tr>
                            <tr class="collapse" id="details{{ $branch['id'] }}">
                                <td colspan="13">
                                    <div class="card card-body">
                                        <table class="table table-bordered">
                                            <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Sorgu Adı</th>
                                                <th>Sorgulanan Veri</th>
                                                <th>Tarih</th>
                                                <th>Tutar</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($fields as $field)
                                                    @foreach($branch[$field] as $query)
                                                        @php $item = json_decode($query->result ?? $query->$field,true);
                                                            $from = isset($item['from']) && $item['from'] == 'arabasorgula.com' ? 'arabasorgula' : 'otosorgu';
                                                        @endphp
                                                        <tr>
                                                            <td class="{{ $authUser->type == 'admin' ? $from : '' }}">{{ $item['data']['id'] ?? '' }}</td>
                                                            <td class="{{ $authUser->type == 'admin' ? $from : '' }}">{{ $item['data']['query'] ?? '' }}</td>
                                                            <td class="{{ $authUser->type == 'admin' ? $from : '' }}">{{ $item['data']['data'] ?? '' }}</td>
                                                            <td class="{{ $authUser->type == 'admin' ? $from : '' }}">{{ isset($item['data']['date']) ? \Carbon\Carbon::parse($item['data']['date'])->translatedFormat('d F Y') : '' }}</td>
                                                            <td class="{{ $authUser->type == 'admin' ? $from : '' }}">{{ $query[$field.'_komisyon_dahil_ucret'] }}₺</td>
                                                        </tr>

                                                    @endforeach
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                        <tfoot>
                        <tr>
                            <td><strong>Toplamlar</strong></td>
                            <td></td>
                            <td><strong>{{ $totalBorcAdet }}</strong></td>
                            <td><strong>{{ $totalBorcTutar }}₺</strong></td>
                            <td><strong>{{ $totalHasarAdet }}</strong></td>
                            <td><strong>{{ $totalHasarTutar }}₺</strong></td>
                            <td><strong>{{ $totalKilometreAdet }}</strong></td>
                            <td><strong>{{ $totalKilometreTutar }}₺</strong></td>
                            <td><strong>{{ $totalRuhsatAdet }}</strong></td>
                            <td><strong>{{ $totalRuhsatTutar }}₺</strong></td>
                            <td><strong>{{ $totalDegisenAdet }}</strong></td>
                            <td><strong>{{ $totalDegisenTutar }}₺</strong></td>
                            <td><strong>{{ $totalDetayAdet }}</strong></td>
                            <td><strong>{{ $totalDetayTutar }}₺</strong></td>
                            <td><strong>{{ $totalTutar }}₺</strong></td>
                        </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </form>

@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var collapseElements = document.querySelectorAll('.collapse');
            collapseElements.forEach(function(collapseElement) {
                collapseElement.addEventListener('show.bs.collapse', function () {
                    var tdElements = this.previousElementSibling.querySelectorAll('td');
                    tdElements.forEach(function(td) {
                        td.style.backgroundColor = '#f2f2f2';
                    });
                });
                collapseElement.addEventListener('hide.bs.collapse', function () {
                    var tdElements = this.previousElementSibling.querySelectorAll('td');
                    tdElements.forEach(function(td) {
                        td.style.backgroundColor = '';
                    });
                });
            });
        });
    </script>
@endpush
