@extends('pages.build')
@section('title','Stok Rapor')
@section('content')
    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
        <a href="{{ route('reports',['type'=>'expertise']) }}" class="nav-link">Ekspertiz Raporları</a>
        @if($authUser->type == 'admin' || $authUser->zone_id > 0)
            <a href="{{ route('reports',['type'=>'stock']) }}" class="nav-link active">Stok Raporları</a>
            <a href="{{ route('reports',['type'=>'branch']) }}" class="nav-link"><PERSON>i <PERSON>orlar<PERSON></a>
            @if($authUser->type == 'admin')
                <a href="{{ route('reports',['type'=>'query']) }}" class="nav-link">Hasar Raporları</a>
            @endif
        @endif
        <a href="{{ route('reports',['type'=>'customer']) }}" class="nav-link"><PERSON><PERSON></a>
        <a href="{{ route('reports',['type'=>'car']) }}" class="nav-link">Araç Raporları</a>
        <a href="{{ route('reports',['type'=>'user']) }}" class="nav-link">Personel Raporları</a>
        <a href="{{ route('reports',['type'=>'endofdays']) }}" class="nav-link">Gün Sonu Raporu</a>
    </nav>
    <form id="filterForm" method="get" action="{{ route('reports',['type'=>'stock']) }}">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>Başlangıç Tarihi</label>
                            <input type="date" class="form-control" name="start_date" value="{{ $filters['startDate'] }}">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>Bitiş Tarihi</label>
                            <input type="date" class="form-control" name="end_date" value="{{ $filters['endDate'] }}">
                        </div>
                    </div>
                    @if($authUser->type == 'admin')
                        <div class="col">
                            <div class="form-group">
                                <label>Bayi</label>
                                <select class="form-control select2" name="branch_id">
                                    @foreach(\App\Models\Branch::get() as $filterBranch)
                                        <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    @endif
                    <div class="col mt-4">
                        <button class="btn btn-success">Listele</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="row">
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image1">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12 ">Toplam Stok Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalStockCount }}</h4>
                                </div>
                                <p class="mb-0 fs-12 text-muted">Yeni Eklenen<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $newAddedStockCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-primary-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-grid fs-16 text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image2">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Aktif Stok Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $activeStockCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-info-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-check fs-16 text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image3">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Pasif Stok Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalStockCount - $activeStockCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-secondary-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-x-square fs-16 text-secondary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Satan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $mostUsedStockId != null ? \App\Models\Stock::where('id',$mostUsedStockId)->first()->ad : '-' }}</h4>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostUsedStockSellCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-warning-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-credit-card fs-16 text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">En Çok ve En Az Satan Stok Kartları</div>
                </div>
                <div class="card-body">
                    <div id="echart-step-line" class="echart-charts"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">En Çok Kazandıran 5 Stok</div>
                </div>
                <div class="card-body">
                    <div id="pie-monochrome"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">En Çok Satan 5 Stok</div>
                </div>
                <div class="card-body">
                    <div id="pie-monochrome2"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">En Az Satan 5 Stok</div>
                </div>
                <div class="card-body">
                    <div id="pie-monochrome3"></div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Stok Grubuna Göre Dağılım</div>
                </div>
                <div class="card-body">
                    <div id="bar-basic"></div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/libs/echarts/echarts.min.js"></script>
    <script src="/assets/libs/apexcharts/apexcharts.min.js"></script>
    <script>
        var dom = document.getElementById('echart-step-line');
        var myChart = echarts.init(dom, null, {
            renderer: 'canvas',
            useDirtyRect: false
        });
        var app = {};
        var option;
        option = {
            tooltip: {
                trigger: 'axis'
            },
            legend: {
                data: ['En Az Satan ({{ $lessUsedStockId != null ? \App\Models\Stock::where('id',$lessUsedStockId)->first()->ad : '-' }})', 'En Çok Satan ({{ $mostUsedStockId != null ? \App\Models\Stock::where('id',$mostUsedStockId)->first()->ad : '-' }})'],
                textStyle: {
                    color: '#777'
                }
            },
            grid: {
                left: '0%',
                right: '0%',
                bottom: '0%',
                top: '10%',
                containLabel: true
            },
            toolbox: {
                feature: {
                    saveAsImage: {}
                }
            },
            xAxis: {
                type: 'category',
                data: [
                    @foreach($mostSells as $mostSell)
                        '{{ $mostSell['start_date'] . '-' . $mostSell['end_date'] }}',
                    @endforeach
                ],
                axisLine: {
                    lineStyle: {
                        color: "#8c9097"
                    }
                }
            },
            yAxis: {
                type: 'value',
                axisLine: {
                    lineStyle: {
                        color: "#8c9097"
                    }
                },
                splitLine: {
                    lineStyle: {
                        color: "rgba(142, 156, 173,0.1)"
                    }
                }
            },
            series: [
                {
                    name: 'En Az Satan ({{ $lessUsedStockId != null ? \App\Models\Stock::where('id',$lessUsedStockId)->first()->ad : '-' }})',
                    type: 'line',
                    step: 'start',
                    data: [
                        @foreach($lessSells as $lessSell)
                            '{{ $lessSell['sell_count'] }}',
                        @endforeach
                    ],
                },
                {
                    name: 'En Çok Satan ({{ $mostUsedStockId != null ? \App\Models\Stock::where('id',$mostUsedStockId)->first()->ad : '-' }})',
                    type: 'line',
                    step: 'end',
                    data: [
                        @foreach($mostSells as $mostSell)
                            '{{ $mostSell['sell_count'] }}',
                        @endforeach
                    ],
                }
            ],
            color: ["#38cab3", "#4ec2f0", "#ffbd5a", "#4ec2f0", "#f34343"]
        };
        if (option && typeof option === 'object') {
            myChart.setOption(option);
        }
        window.addEventListener('resize', myChart.resize);


        var options = {
            series: [
                @foreach($top5SellStocks as $top5SellStock)
                    {{ (int)$top5SellStock['total_hizmet_tutari'] }},
                @endforeach
            ],
            chart: {
                height: '500',
                type: 'pie',
            },
            labels: [
                @foreach($top5SellStocks as $top5SellStock)
                    '{{ $top5SellStock['stock_name'] }}',
                @endforeach
            ],
            theme: {
                monochrome: {
                    enabled: true,
                    color: "#38caca",
                }
            },
            plotOptions: {
                pie: {
                    dataLabels: {
                        offset: -5
                    }
                }
            },
            dataLabels: {
                formatter(val, opts) {
                    console.log(val,opts)
                    const name = opts.w.globals.labels[opts.seriesIndex]
                    const total = opts.w.config.series[opts.seriesIndex]
                    return [name,'₺' + total ]
                },
                dropShadow: {
                    enabled: false
                }
            },
            legend: {
                show: false
            },
            responsive: [{
                breakpoint: [480],
                options: {
                    chart: {
                        height: '300'
                    },
                },
            }],
        };
        var chart = new ApexCharts(document.querySelector("#pie-monochrome"), options);
        chart.render();

        var options = {
            series: [
                @foreach($mostUsed5Stocks as $mostUsed5Stock)
                    {{ (int)$mostUsed5Stock['count'] }},
                @endforeach
            ],
            chart: {
                height: '500',
                type: 'pie',
            },
            labels: [
                @foreach($mostUsed5Stocks as $mostUsed5Stock)
                    '{{ $mostUsed5Stock['stock'] }}',
                @endforeach
            ],
            theme: {
                monochrome: {
                    enabled: true,
                    color: "#38ca5a",
                }
            },
            plotOptions: {
                pie: {
                    dataLabels: {
                        offset: -5
                    }
                }
            },
            dataLabels: {
                formatter(val, opts) {
                    console.log(val,opts)
                    const name = opts.w.globals.labels[opts.seriesIndex]
                    const total = opts.w.config.series[opts.seriesIndex]
                    return [name, total + ' Adet' ]
                },
                dropShadow: {
                    enabled: false
                }
            },
            legend: {
                show: false
            },
            responsive: [{
                breakpoint: [480],
                options: {
                    chart: {
                        height: '300'
                    },
                },
            }],
        };
        var chart = new ApexCharts(document.querySelector("#pie-monochrome2"), options);
        chart.render();

        var options = {
            series: [
                @foreach($leastUsed5Stocks as $leastUsed5Stock)
                    {{ (int)$leastUsed5Stock['count'] }},
                @endforeach
            ],
            chart: {
                height: '500',
                type: 'pie',
            },
            labels: [
                @foreach($leastUsed5Stocks as $leastUsed5Stock)
                    '{{ $leastUsed5Stock['stock'] }}',
                @endforeach
            ],
            theme: {
                monochrome: {
                    enabled: true,
                    color: "#ca3838",
                }
            },
            plotOptions: {
                pie: {
                    dataLabels: {
                        offset: -5
                    }
                }
            },
            dataLabels: {
                formatter(val, opts) {
                    console.log(val,opts)
                    const name = opts.w.globals.labels[opts.seriesIndex]
                    const total = opts.w.config.series[opts.seriesIndex]
                    return [name,total + ' Adet' ]
                },
                dropShadow: {
                    enabled: false
                }
            },
            legend: {
                show: false
            },
            responsive: [{
                breakpoint: [480],
                options: {
                    chart: {
                        height: '300'
                    },
                },
            }],
        };
        var chart = new ApexCharts(document.querySelector("#pie-monochrome3"), options);
        chart.render();

        var options = {
            series: [{
                data: [
                    @foreach($stockTypes as $stockType)
                      {{ $stockType->getStocks->count()  }},
                    @endforeach
                ]
            }],
            chart: {
                type: 'bar',
                height: 460
            },
            plotOptions: {
                bar: {
                    borderRadius: 4,
                    horizontal: true,
                }
            },
            colors: ["#38cab3"],
            grid: {
                borderColor: '#f2f5f7',
            },
            dataLabels: {
                enabled: false
            },
            xaxis: {
                categories: [
                    @foreach($stockTypes as $stockType)
                        '{{ $stockType->name  }}',
                    @endforeach
                ],
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-xaxis-label',
                    },
                }
            },
            yaxis: {
                labels: {
                    show: true,
                    style: {
                        colors: "#8c9097",
                        fontSize: '11px',
                        fontWeight: 600,
                        cssClass: 'apexcharts-yaxis-label',
                    },
                }
            }
        };
        var chart = new ApexCharts(document.querySelector("#bar-basic"), options);
        chart.render();
    </script>
@endpush
