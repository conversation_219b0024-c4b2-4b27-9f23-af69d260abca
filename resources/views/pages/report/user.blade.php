@extends('pages.build')
@section('title','Personel Rapor')
@section('content')
    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
        <a href="{{ route('reports',['type'=>'expertise']) }}" class="nav-link">Ekspertiz Raporları</a>
        @if($authUser->type == 'admin' || $authUser->zone_id > 0)
            <a href="{{ route('reports',['type'=>'stock']) }}" class="nav-link">Stok Raporları</a>
            <a href="{{ route('reports',['type'=>'branch']) }}" class="nav-link"><PERSON><PERSON></a>
            @if($authUser->type == 'admin')
                <a href="{{ route('reports',['type'=>'query']) }}" class="nav-link">Hasar Raporları</a>
            @endif
        @endif
        <a href="{{ route('reports',['type'=>'customer']) }}" class="nav-link"><PERSON><PERSON></a>
        <a href="{{ route('reports',['type'=>'car']) }}" class="nav-link">Araç Raporları</a>
        <a href="{{ route('reports',['type'=>'user']) }}" class="nav-link active">Personel Raporları</a>
        <a href="{{ route('reports',['type'=>'endofdays']) }}" class="nav-link">Gün Sonu Raporu</a>
    </nav>
    <form id="filterForm" method="get" action="{{ route('reports',['type'=>'user']) }}">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col">
                        <div class="form-group">
                            <label>Başlangıç Tarihi</label>
                            <input type="date" class="form-control" name="start_date" value="{{ $filters['startDate'] }}">
                        </div>
                    </div>
                    <div class="col">
                        <div class="form-group">
                            <label>Bitiş Tarihi</label>
                            <input type="date" class="form-control" name="end_date" value="{{ $filters['endDate'] }}">
                        </div>
                    </div>
                    @if($authUser->type == 'admin')
                        <div class="col">
                            <div class="form-group">
                                <label>Bayi</label>
                                <select class="form-control select2" name="branch_id">
                                    @foreach(\App\Models\Branch::get() as $branch)
                                        <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $branch->id) selected @endif value="{{ $branch->id }}">{{ $branch->kisa_ad }}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    @endif
                    <div class="col mt-4">
                        <button class="btn btn-success">Listele</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="row">
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image1">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12 ">Toplam Personel</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalPersonelCount }}</h4>
                                </div>
                                <p class="mb-0 fs-12 text-muted">Yeni Eklenen<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $newAddedPersonelCount }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-success-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-user fs-16 text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image2">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Aktif Personel Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $activePersonelCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-info-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-check fs-16 text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-lg-3 col-md-3 col-xs-12">
            <div class="card sales-card circle-image3">
                <div class="row">
                    <div class="col-8">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">Pasif Personel Kartı</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h4 class="fs-20 fw-semibold mb-2">{{ $totalPersonelCount - $activePersonelCount }}</h4>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted"><i class="bx bx-caret-down mx-2 text-danger"></i>
                                    <span class="text-danger fw-semibold"> </span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="circle-icon bg-secondary-transparent text-center align-self-center overflow-hidden">
                            <i class="fe fe-x-square fs-16 text-secondary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok İşlem Kaydı Bulunan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostLogPersonel != null ? \App\Models\User::where('id',$mostLogPersonel)->first()->name . ' ' . \App\Models\User::where('id',$mostLogPersonel)->first()->second_name . ' ' . \App\Models\User::where('id',$mostLogPersonel)->first()->surname : '-' }} ({{ $mostLogPersonelLogCount }})</h6>
                                </div>
                                <p class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostLogPersonelLogCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Ekspertiz Kaydı Açan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostOpenExpertisePersonel['isim'] }} ({{ $mostOpenExpertisePersonel->count ?? 0 }})</h6>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertisePersonelExpertiseCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Araç Kontrol Yapan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseCarUser['isim'] }} ({{ $mostExpertiseCarUser->car_count ?? 0 }})</h6>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseCarUserCarCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Fren Kontrol Yapan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseBrakeUser['isim'] }} ({{ $mostExpertiseBrakeUser->car_count ?? 0 }})</h6>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseBrakeUserBrakeCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Kaporta Kontrol Yapan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseBodyworkUser['isim'] }} ({{ $mostExpertiseBodyworkUser->car_count ?? 0 }})</h6>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseBodyworkUserBodyworkCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Diagnostic Kontrol Yapan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseDiagnosticUser['isim'] }} ({{ $mostExpertiseDiagnosticUser->car_count ?? 0 }})</h6>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseDiagnosticUserDiagnosticCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok İç Kontrol Yapan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseInternalUser['isim'] }} ({{ $mostExpertiseInternalUser->car_count ?? 0 }})</h6>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseInternalUserInternalCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Lastik ve Jant Kontrol Yapan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseTireAndRimUser['isim'] }} ({{ $mostExpertiseTireAndRimUser->car_count ?? 0 }})</h6>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseTireAndRimUserTireAndRimCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Alt Kontrol ve Motor Yapan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseSubControlUser['isim'] }} ({{ $mostExpertiseSubControlUser->car_count ?? 0 }})</h6>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseSubControlUserSubControlCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
            <div class="card sales-card circle-image4">
                <div class="row">
                    <div class="col-12">
                        <div class="ps-4 pt-4 pe-3 pb-4">
                            <div class="">
                                <h6 class="mb-2 fs-12">En Çok Komponent Kontrol Yapan</h6>
                            </div>
                            <div class="pb-0 mt-0">
                                <div class="d-flex">
                                    <h6 class="fs-20 fw-semibold mb-2">{{ $mostExpertiseComponentUser['isim'] }} ({{ $mostExpertiseComponentUser->car_count ?? 0 }})</h6>
                                </div>
                                <p style="visibility: hidden" class="mb-0 fs-12  text-muted">Seçili Tarihte<i class="bx bx-caret-up mx-2 text-success"></i>
                                    <span class="text-success fw-semibold"> +{{ $mostExpertiseComponentUserComponentCountSelectedDate }}</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @if($authUser->type == 'admin' || $authUser->zone_id > 0)
            <div class="col-xl-12">
                <div class="card custom-card">
                    <div class="card-header">
                        <div class="card-title">Bayiye Göre Personel Sayısı</div>
                    </div>
                    <div class="card-body">
                        <div id="stepline-chart"></div>
                    </div>
                </div>
            </div>
        @endif

        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">Kullanıcı Grubuna Göre Dağılım</div>
                </div>
                <div class="card-body">
                    <canvas id="group-pie" class="chartjs-chart"></canvas>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card custom-card">
                <div class="card-header">
                    <div class="card-title">İşe Başlama Yıllarına Göre Dağılım</div>
                </div>
                <div class="card-body">
                    <canvas id="year-pie" class="chartjs-chart"></canvas>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/libs/apexcharts/apexcharts.min.js"></script>
    <script src="/assets/libs/chart.js/chart.min.js"></script>

    <script>
        @if($authUser->type == 'admin' || $authUser->zone_id > 0)
            var options = {
                series: [{
                    data: [
                        @foreach($usersByBranches as $branchUser)
                            {{ $branchUser['count'] }},
                        @endforeach
                    ]
                }],
                labels:[
                    @foreach($usersByBranches as $branchUser)
                        '{{ $branchUser['branch'] }}',
                    @endforeach
                ],
                chart: {
                    type: 'line',
                    height: 345
                },
                stroke: {
                    curve: 'stepline',
                },
                grid: {
                    borderColor: '#f2f5f7',
                },
                dataLabels: {
                    enabled: false
                },
                colors: ["#38cab3"],
                markers: {
                    hover: {
                        sizeOffset: 4
                    }
                },
                xaxis: {
                    labels: {
                        show: true,
                        style: {
                            colors: "#8c9097",
                            fontSize: '11px',
                            fontWeight: 600,
                            cssClass: 'apexcharts-xaxis-label',
                        },
                    }
                },
                yaxis: {
                    labels: {
                        show: true,
                        style: {
                            colors: "#8c9097",
                            fontSize: '11px',
                            fontWeight: 600,
                            cssClass: 'apexcharts-yaxis-label',
                        },
                    }
                }
            };
            var chart2 = new ApexCharts(document.querySelector("#stepline-chart"), options);
            chart2.render();
        @endif


        const dataGroup = {
            labels: [
                @foreach($userRoleGroups as $userRoleGroup)
                    '{{ $userRoleGroup['name'] }}',
                @endforeach
            ],
            datasets: [{
                data: [
                    @foreach($userRoleGroups as $userRoleGroup)
                        '{{ $userRoleGroup['count'] }}',
                    @endforeach
                ],
                backgroundColor: [
                    @foreach($userRoleGroups as $userRoleGroup)
                        'rgb({{ rand(10,255) }},{{ rand(10,255) }},{{ rand(10,255) }})',
                    @endforeach
                ],
                hoverOffset: 4
            }]
        };
        const configGroup = {
            type: 'pie',
            data: dataGroup,
        };
        const groupPie = new Chart(
            document.getElementById('group-pie'),
            configGroup
        );

        const dataYear = {
            labels: [
                @foreach($usersByYears as $usersByYear)
                    '{{ $usersByYear['year'] }}',
                @endforeach
            ],
            datasets: [{
                data: [
                    @foreach($usersByYears as $usersByYear)
                        '{{ $usersByYear['count'] }}',
                    @endforeach
                ],
                backgroundColor: [
                    @foreach($usersByYears as $usersByYear)
                        'rgb({{ rand(10,255) }},{{ rand(10,255) }},{{ rand(10,255) }})',
                    @endforeach
                ],
                hoverOffset: 4
            }]
        };
        const configYear = {
            type: 'pie',
            data: dataYear,
        };
        const yearPie = new Chart(
            document.getElementById('year-pie'),
            configYear
        );
    </script>
@endpush
