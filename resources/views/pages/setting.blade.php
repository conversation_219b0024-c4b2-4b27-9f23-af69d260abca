@extends('pages.build')
@section('title','Ayarlar')
@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <form method="post" action="{{ route('settings.update',$settings) }}" enctype="multipart/form-data">@csrf @method('put')
                        <div class="example">
                            <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                                <a class="nav-link active" data-bs-toggle="tab" role="tab" href="#nav-netgsm" aria-selected="true">Netgsm</a>
                                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-iyzico" aria-selected="false">İyzico</a>
                                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-setting" aria-selected="false"><PERSON><PERSON><PERSON></a>
                                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-message" aria-selected="false">Uyarı Mesajları</a>
{{--                                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-commission-otosorgu" aria-selected="false">Otosorgu Komisyon Ücretleri</a>--}}
{{--                                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-commission-arabasorgula" aria-selected="false">Araba Sorgula Komisyon Ücretleri</a>--}}
                                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-commissions" aria-selected="false">Komisyon Ücretleri</a>
                            </nav>
                            <div class="tab-content">
                                <div class="tab-pane show active text-muted" id="nav-netgsm" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Durum</label>
                                            <select name="netgsm_active" class="form-control form-control-sm">
                                                <option value="1">Aktif</option>
                                                <option @if($settings->netgsm_active == 0) selected @endif value="0">Pasif</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Netgsm Kullanıcı Kodu</label>
                                            <input type="text" class="form-control form-control-sm" name="netgsm_usercode" placeholder="Netgsm Kullanıcı Kodu" value="{{ $settings->netgsm_usercode }}">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Netgsm Şifre</label>
                                            <input type="text" class="form-control form-control-sm" name="netgsm_password" placeholder="Netgsm Şifre" value="{{ $settings->netgsm_password }}">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Personel Netgsm Mesaj Başlığı</label>
                                            <input type="text" class="form-control form-control-sm" name="netgsm_msgheader" placeholder="Netgsm Mesaj Başlığı" value="{{ $settings->netgsm_msgheader }}">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Cari Netgsm Mesaj Başlığı </label>
                                            <input type="text" class="form-control form-control-sm" name="netgsm_msgheader2" placeholder="Netgsm Mesaj Başlığı" value="{{ $settings->netgsm_msgheader2 }}">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Plus card Netgsm Mesaj Başlığı </label>
                                            <input type="text" class="form-control form-control-sm" name="netgsm_msgheader3" placeholder="Netgsm Mesaj Başlığı" value="{{ $settings->netgsm_msgheader3 }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane text-muted" id="nav-iyzico" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Durum</label>
                                            <select name="iyzico_active" class="form-control form-control-sm">
                                                <option value="1">Aktif</option>
                                                <option @if($settings->iyzico_active == 0) selected @endif value="0">Pasif</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">İyzico API KEY</label>
                                            <input type="text" class="form-control form-control-sm" name="iyzico_api_key" placeholder="İyzico API KEY" value="{{ $settings->iyzico_api_key }}">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">İyzico SECRET KEY</label>
                                            <input type="text" class="form-control form-control-sm" name="iyzico_secret_key" placeholder="İyzico SECRET KEY" value="{{ $settings->iyzico_secret_key }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane text-muted" id="nav-setting" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Google Auth</label>
                                            <select name="google_auth" class="form-control form-control-sm">
                                                <option value="1">Aktif</option>
                                                <option @if($settings->google_auth == 0) selected @endif value="0">Pasif</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Rapor Satın Alma Ücreti</label>
                                            <input type="text" class="form-control form-control-sm" name="customer_buy_expertise_price" placeholder="Rapor Satın Alma Ücreti" value="{{ $settings->customer_buy_expertise_price }}">
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Hasar Sorgulama Nereden Yapılsın?</label>
                                            <select name="hasar_sorgu_firmasi" class="form-control form-control-sm">
                                                <option value="otosorgu">Oto Sorgu</option>
                                                <option @if($settings->hasar_sorgu_firmasi == 'araba_sorgula') selected @endif value="araba_sorgula">Araba Sorgula</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Kilometre Sorgulama Nereden Yapılsın?</label>
                                            <select name="kilometre_sorgu_firmasi" class="form-control form-control-sm">
                                                <option value="otosorgu">Oto Sorgu</option>
                                                <option @if($settings->kilometre_sorgu_firmasi == 'araba_sorgula') selected @endif value="araba_sorgula">Araba Sorgula</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Borç Sorgulama Nereden Yapılsın?</label>
                                            <select name="borc_sorgu_firmasi" class="form-control form-control-sm">
                                                <option value="otosorgu">Oto Sorgu</option>
{{--                                                <option @if($settings->borc_sorgu_firmasi == 'araba_sorgula') selected @endif value="araba_sorgula">Araba Sorgula</option>--}}
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Araç Detay Sorgulama Nereden Yapılsın?</label>
                                            <select name="arac_detay_sorgu_firmasi" class="form-control form-control-sm">
                                                <option value="otosorgu">Oto Sorgu</option>
                                                <option @if($settings->arac_detay_sorgu_firmasi == 'araba_sorgula') selected @endif value="araba_sorgula">Araba Sorgula</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Değişen Sorgulama Nereden Yapılsın?</label>
                                            <select name="degisen_sorgu_firmasi" class="form-control form-control-sm">
                                                <option value="otosorgu">Oto Sorgu</option>
                                                <option @if($settings->degisen_sorgu_firmasi == 'araba_sorgula') selected @endif value="araba_sorgula">Araba Sorgula</option>
                                            </select>
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">Ruhsat Sorgulama Nereden Yapılsın?</label>
                                            <select name="ruhsat_sorgu_firmasi" class="form-control form-control-sm">
                                                <option value="otosorgu">Oto Sorgu</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane text-muted" id="nav-message" role="tabpanel">
                                    <div class="row">
                                        <div class="col-xl-12 mt-2">
                                            <label class="form-label">Hasar Sorgu Mesajı</label>
                                            <textarea class="form-control form-control-sm" name="hasar_sorgu_mesaji">{{ $settings->hasar_sorgu_mesaji }}</textarea>
                                        </div>
                                        <div class="col-xl-12 mt-2">
                                            <label class="form-label">Kilometre Sorgu Mesajı</label>
                                            <textarea class="form-control form-control-sm" name="kilometre_sorgu_mesaji">{{ $settings->kilometre_sorgu_mesaji }}</textarea>
                                        </div>
                                        <div class="col-xl-12 mt-2">
                                            <label class="form-label">Borç Sorgu Mesajı</label>
                                            <textarea class="form-control form-control-sm" name="borc_sorgu_mesaji">{{ $settings->borc_sorgu_mesaji }}</textarea>
                                        </div>
                                        <div class="col-xl-12 mt-2">
                                            <label class="form-label">Araç Detay Sorgu Mesajı</label>
                                            <textarea class="form-control form-control-sm" name="arac_detay_sorgu_mesaji">{{ $settings->arac_detay_sorgu_mesaji }}</textarea>
                                        </div>
                                        <div class="col-xl-12 mt-2">
                                            <label class="form-label">Değişen Sorgu Mesajı</label>
                                            <textarea class="form-control form-control-sm" name="degisen_sorgu_mesaji">{{ $settings->degisen_sorgu_mesaji }}</textarea>
                                        </div>
                                        <div class="col-xl-12 mt-2">
                                            <label class="form-label">Ruhsat Sorgu Mesajı</label>
                                            <textarea class="form-control form-control-sm" name="ruhsat_sorgu_mesaji">{{ $settings->ruhsat_sorgu_mesaji }}</textarea>
                                        </div>
                                        <div class="col-xl-12 mt-2">
                                            <label class="form-label">Pasif Kullanıcı Hata Mesajı</label>
                                            <textarea class="form-control form-control-sm" name="passive_user_message">{{ $settings->passive_user_message }}</textarea>
                                        </div>
                                    </div>
                                </div>
                                <div class="tab-pane text-muted" id="nav-commission-otosorgu" role="tabpanel">
                                    <div class="row">
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Hasar Sorgu Komisyon Ücreti </label>--}}
{{--                                            <input class="form-control form-control-sm price" name="hasar_sorgu_komisyon" onkeyup="calculatePrice('hasar','')" value="{{ str_replace('.',',',$settings->hasar_sorgu_komisyon) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Otosorgu Ücreti </label>--}}
{{--                                            <input class="form-control form-control-sm price" name="hasar_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->hasar_sorgu_otosorgu) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ekspertiz Gözüken Fiyat</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="hasar_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->hasar_sorgu_sistem) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Kilometre Sorgu Komisyon Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="kilometre_sorgu_komisyon" onkeyup="calculatePrice('kilometre','')" value="{{ str_replace('.',',',$settings->kilometre_sorgu_komisyon) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Otosorgu Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="kilometre_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->kilometre_sorgu_otosorgu) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ekspertiz Gözüken Fiyat</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="kilometre_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->kilometre_sorgu_sistem) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Borç Sorgu Komisyon Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="borc_sorgu_komisyon" onkeyup="calculatePrice('borc','')" value="{{ str_replace('.',',',$settings->borc_sorgu_komisyon) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Otosorgu Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="borc_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->borc_sorgu_otosorgu) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ekspertiz Gözüken Fiyat</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="borc_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->borc_sorgu_sistem) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Araç Detay Sorgu Komisyon Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="detail_sorgu_komisyon" onkeyup="calculatePrice('detail','')" value="{{ str_replace('.',',',$settings->detail_sorgu_komisyon) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Otosorgu Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="detail_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->detail_sorgu_otosorgu) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ekspertiz Gözüken Fiyat</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="detail_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->detail_sorgu_sistem) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Değişen Sorgu Komisyon Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="degisen_sorgu_komisyon" onkeyup="calculatePrice('degisen','')" value="{{ str_replace('.',',',$settings->degisen_sorgu_komisyon) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Otosorgu Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="degisen_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->degisen_sorgu_otosorgu) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ekspertiz Gözüken Fiyat</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="degisen_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->degisen_sorgu_sistem) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ruhsat Sorgu Komisyon Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="ruhsat_sorgu_komisyon" onkeyup="calculatePrice('ruhsat','')" value="{{ str_replace('.',',',$settings->ruhsat_sorgu_komisyon) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Otosorgu Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="ruhsat_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->ruhsat_sorgu_otosorgu) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ekspertiz Gözüken Fiyat</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="ruhsat_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->ruhsat_sorgu_sistem) }}">--}}
{{--                                        </div>--}}
                                    </div>
                                </div>
                                <div class="tab-pane text-muted" id="nav-commission-arabasorgula" role="tabpanel">
                                    <div class="row">
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Hasar Sorgu Komisyon Ücreti </label>--}}
{{--                                            <input class="form-control form-control-sm price" name="hasar_sorgu_komisyon_arabasorgula" onkeyup="calculatePrice('hasar','_arabasorgula')" value="{{ str_replace('.',',',$settings->hasar_sorgu_komisyon_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Araba Sorgula Ücreti </label>--}}
{{--                                            <input class="form-control form-control-sm price" name="hasar_sorgu_arabasorgula" disabled value="{{ str_replace('.',',',$settings->hasar_sorgu_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ekspertiz Gözüken Fiyat</label>--}}
{{--                                            <input class="form-control form-control-sm price" readonly name="hasar_sorgu_sistem_arabasorgula" value="{{ str_replace('.',',',$settings->hasar_sorgu_komisyon_arabasorgula + $settings->hasar_sorgu_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Kilometre Sorgu Komisyon Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="kilometre_sorgu_komisyon_arabasorgula" onkeyup="calculatePrice('kilometre','_arabasorgula')" value="{{ str_replace('.',',',$settings->kilometre_sorgu_komisyon_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Araba Sorgula Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="kilometre_sorgu_arabasorgula" disabled value="{{ str_replace('.',',',$settings->kilometre_sorgu_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ekspertiz Gözüken Fiyat</label>--}}
{{--                                            <input class="form-control form-control-sm price" readonly name="kilometre_sorgu_sistem_arabasorgula" value="{{ str_replace('.',',',$settings->kilometre_sorgu_komisyon_arabasorgula + $settings->kilometre_sorgu_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Araç Detay Sorgu Komisyon Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="detail_sorgu_komisyon_arabasorgula" onkeyup="calculatePrice('detail','_arabasorgula')" value="{{ str_replace('.',',',$settings->detail_sorgu_komisyon_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Araba Sorgula Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="detail_sorgu_arabasorgula" disabled value="{{ str_replace('.',',',$settings->detail_sorgu_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ekspertiz Gözüken Fiyat</label>--}}
{{--                                            <input class="form-control form-control-sm price" readonly name="detail_sorgu_sistem_arabasorgula" value="{{ str_replace('.',',',$settings->detail_sorgu_komisyon_arabasorgula + $settings->detail_sorgu_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Değişen Sorgu Komisyon Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="degisen_sorgu_komisyon_arabasorgula" onkeyup="calculatePrice('degisen','_arabasorgula')" value="{{ str_replace('.',',',$settings->degisen_sorgu_komisyon_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Araba Sorgula Ücreti</label>--}}
{{--                                            <input class="form-control form-control-sm price" name="degisen_sorgu_arabasorgula" disabled value="{{ str_replace('.',',',$settings->degisen_sorgu_arabasorgula) }}">--}}
{{--                                        </div>--}}
{{--                                        <div class="col-xl-4 mt-2">--}}
{{--                                            <label class="form-label">Ekspertiz Gözüken Fiyat</label>--}}
{{--                                            <input class="form-control form-control-sm price" readonly name="degisen_sorgu_sistem_arabasorgula" value="{{ str_replace('.',',',$settings->degisen_sorgu_komisyon_arabasorgula + $settings->degisen_sorgu_arabasorgula) }}">--}}
{{--                                        </div>--}}
                                    </div>
                                </div>
                                <div class="tab-pane text-muted" id="nav-commissions" role="tabpanel">
                                    <div class="table-responsive">
                                        <table class="table table-bordered text-center">
                                            <thead>
                                            <tr>
                                                <th colspan="2" style="background-color: #D1F0D1;">Hasar Sorgulama Hizmetleri</th>
                                                <th colspan="3" style="background-color: #67B7D1;">Otosorgu</th>
                                                <th colspan="3" style="background-color: #A64CA6;">Araba Sorgula</th>
                                            </tr>
                                            <tr>
                                                <th>#</th>
                                                <th>Hizmet</th>
                                                <th>Ücreti</th>
                                                <th>Komisyon Ücreti</th>
                                                <th>Ekspertiz Gözüken Fiyat</th>
                                                <th>Ücreti</th>
                                                <th>Komisyon Ücreti</th>
                                                <th>Ekspertiz Gözüken Fiyat</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr>
                                                <td>1</td>
                                                <td>Hasar Sorgu Komisyon Ücreti</td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="hasar_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->hasar_sorgu_otosorgu) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="hasar_sorgu_komisyon" onkeyup="calculatePrice('hasar','')" value="{{ str_replace('.',',',$settings->hasar_sorgu_komisyon) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="hasar_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->hasar_sorgu_sistem) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="hasar_sorgu_arabasorgula" disabled value="{{ str_replace('.',',',$settings->hasar_sorgu_arabasorgula) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="hasar_sorgu_komisyon_arabasorgula" onkeyup="calculatePrice('hasar','_arabasorgula')" value="{{ str_replace('.',',',$settings->hasar_sorgu_komisyon_arabasorgula) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" readonly name="hasar_sorgu_sistem_arabasorgula" value="{{ str_replace('.',',',$settings->hasar_sorgu_komisyon_arabasorgula + $settings->hasar_sorgu_arabasorgula) }}">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>2</td>
                                                <td>Kilometre Sorgu Komisyon Ücreti</td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="kilometre_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->kilometre_sorgu_otosorgu) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="kilometre_sorgu_komisyon" onkeyup="calculatePrice('kilometre','')" value="{{ str_replace('.',',',$settings->kilometre_sorgu_komisyon) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="kilometre_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->kilometre_sorgu_sistem) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="kilometre_sorgu_arabasorgula" disabled value="{{ str_replace('.',',',$settings->kilometre_sorgu_arabasorgula) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="kilometre_sorgu_komisyon_arabasorgula" onkeyup="calculatePrice('kilometre','_arabasorgula')" value="{{ str_replace('.',',',$settings->kilometre_sorgu_komisyon_arabasorgula) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" readonly name="kilometre_sorgu_sistem_arabasorgula" value="{{ str_replace('.',',',$settings->kilometre_sorgu_komisyon_arabasorgula + $settings->kilometre_sorgu_arabasorgula) }}">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>3</td>
                                                <td>Borç Sorgu Komisyon Ücreti</td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="borc_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->borc_sorgu_otosorgu) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="borc_sorgu_komisyon" onkeyup="calculatePrice('borc','')" value="{{ str_replace('.',',',$settings->borc_sorgu_komisyon) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="borc_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->borc_sorgu_sistem) }}">
                                                </td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <td>4</td>
                                                <td>Araç Detay Sorgu Komisyon Ücreti</td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="detail_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->detail_sorgu_otosorgu) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="detail_sorgu_komisyon" onkeyup="calculatePrice('detail','')" value="{{ str_replace('.',',',$settings->detail_sorgu_komisyon) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="detail_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->detail_sorgu_sistem) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="detail_sorgu_arabasorgula" disabled value="{{ str_replace('.',',',$settings->detail_sorgu_arabasorgula) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="detail_sorgu_komisyon_arabasorgula" onkeyup="calculatePrice('detail','_arabasorgula')" value="{{ str_replace('.',',',$settings->detail_sorgu_komisyon_arabasorgula) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" readonly name="detail_sorgu_sistem_arabasorgula" value="{{ str_replace('.',',',$settings->detail_sorgu_komisyon_arabasorgula + $settings->detail_sorgu_arabasorgula) }}">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>5</td>
                                                <td>Değişen Sorgu Komisyon Ücreti</td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="degisen_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->degisen_sorgu_otosorgu) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="degisen_sorgu_komisyon" onkeyup="calculatePrice('degisen','')" value="{{ str_replace('.',',',$settings->degisen_sorgu_komisyon) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="degisen_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->degisen_sorgu_sistem) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="degisen_sorgu_arabasorgula" disabled value="{{ str_replace('.',',',$settings->degisen_sorgu_arabasorgula) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="degisen_sorgu_komisyon_arabasorgula" onkeyup="calculatePrice('degisen','_arabasorgula')" value="{{ str_replace('.',',',$settings->degisen_sorgu_komisyon_arabasorgula) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" readonly name="degisen_sorgu_sistem_arabasorgula" value="{{ str_replace('.',',',$settings->degisen_sorgu_komisyon_arabasorgula + $settings->degisen_sorgu_arabasorgula) }}">
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>6</td>
                                                <td>Ruhsat Sorgu Komisyon Ücreti</td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="ruhsat_sorgu_otosorgu" disabled value="{{ str_replace('.',',',$settings->ruhsat_sorgu_otosorgu) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="ruhsat_sorgu_komisyon" onkeyup="calculatePrice('ruhsat','')" value="{{ str_replace('.',',',$settings->ruhsat_sorgu_komisyon) }}">
                                                </td>
                                                <td>
                                                    <input class="form-control form-control-sm price" name="ruhsat_sorgu_sistem" readonly value="{{ str_replace('.',',',$settings->ruhsat_sorgu_sistem) }}">
                                                </td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            </tbody>

                                        </table>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                                <button class="btn btn-success mb-3">Kaydet</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script src="/assets/js/imask.js"></script>

    <script>
        if(document.querySelector('.price')){
            document.querySelectorAll('.price').forEach(function (item,index){
                const maskTel = IMask(item, {
                    mask: Number,  // numeric input mask
                    scale: 2,  // digits after point, 0 for integers
                    signed: false,  // disallow negative
                    thousandsSeparator: '.',  // any single char
                    padFractionalZeros: true,  // if true, then pads zeros at end to the length of scale
                    normalizeZeros: true,  // appends or removes zeros at ends
                    radix: ',',  // fractional separator
                    mapToRadix: [',']  // symbols to process as radix
                });
            })
        }

        function calculatePrice(name, postfix = null) {
            let $url = postfix ? '' : '_otosorgu';

            // Değerleri al ve virgülleri noktayla değiştir
            let komisyonValue = parseFloat(document.querySelector('input[name="' + name + '_sorgu_komisyon' + postfix + '"]').value.replace(',', '.') || 0);
            let sorguValue = parseFloat(document.querySelector('input[name="' + name + '_sorgu' + $url + postfix + '"]').value.replace(',', '.') || 0);

            // Toplamı hesapla
            let toplam = komisyonValue + sorguValue;

            // Ekspertiz Gözüken Fiyat alanına değeri yaz ve virgüller ile formatla
            document.querySelector('input[name="' + name + '_sorgu_sistem' + postfix + '"]').value = toplam.toFixed(2).replace('.', ',');

            // IMask ile kuruşları göster
            IMask(document.querySelector('input[name="' + name + '_sorgu_sistem' + postfix + '"]'), {
                mask: Number,
                scale: 2,
                signed: false,
                thousandsSeparator: '.',
                padFractionalZeros: true,
                normalizeZeros: true,
                radix: ',',
                mapToRadix: [',']
            });
        }


    </script>
@endsection
