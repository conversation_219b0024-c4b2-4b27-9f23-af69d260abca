@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Slider Düzenle')
@section('content')
    <form method="post" action="{{ route('sliders.update',$slider) }}" enctype="multipart/form-data">@csrf @method('put')
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgi" aria-expanded="true"
                                    aria-controls="aracBilgi">
                                <PERSON>mel Bilgiler
                            </button>
                        </h2>
                        <div id="aracBilgi" class="accordion-collapse collapse show" aria-labelledby="aracBilgiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12">
                                        <label class="form-label">Görsel <span class="text-danger">*</span></label>
                                        <input type="file" class="dropify" data-height="250" data-default-file="/storage/{{ $slider->image }}" name="image"/>
                                    </div>
                                    <div class="col-12">
                                        <label>Gösterim Yeri</label>
                                        <select name="place" class="form-control">
                                            <option value="panel">Personel Giriş</option>
                                            <option @if($slider->place == 'customer') selected @endif value="customer">Müşteri</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script src="/assets/js/dropify.min.js"></script>
    <script>
        $('.dropify').dropify({
            messages: {
                'default': 'Dosya Sürükle veya Tıkla',
                'replace': 'Dosya Sürükle veya Tıkla',
                'remove':  'Kaldır',
                'error':   'Bir Hata Ortaya Çıktı'
            }
        });
    </script>
@endpush
