@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Stok Grubu Düzenle')
@section('content')
    <form method="post" action="{{ route('stock-groups.update',$stockGroup) }}">@csrf @method('put')
        <div class="row">
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="alisMuhasebeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#alisMuhasebe" aria-expanded="true"
                                    aria-controls="alisMuhasebe">
                                Temel Bilgiler
                            </button>
                        </h2>
                        <div id="alisMuhasebe" class="accordion-collapse collapse show" aria-labelledby="alisMuhasebeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">İlgili Stok Cinsi</label>
                                        <select name="stock_type_id" class="form-control">
                                            <option value="0">Yok</option>
                                            @foreach($stockTypes as $item)
                                                <option @if($item->id == $stockGroup->stock_type_id) selected @endif value="{{ $item->id }}">{{ $item->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Üst Grup</label>
                                        <select name="parent_id" class="form-control">
                                            <option value="0">Yok</option>
                                            @foreach($stockGroups as $item)
                                                <option @if($stockGroup->parent_id == $item->id) selected @endif value="{{ $item->id }}">{{ $item->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kod<span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="kod" value="{{ $stockGroup->kod }}" placeholder="Kod" required>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Başlık<span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="name" value="{{ $stockGroup->name }}" placeholder="Başlık" required>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control">
                                            <option @if($stockGroup->status == 1) selected @endif value="1">Aktif</option>
                                            <option @if($stockGroup->status == 0) selected @endif value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
