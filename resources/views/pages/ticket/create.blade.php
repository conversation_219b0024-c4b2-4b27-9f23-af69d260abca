@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('title','Ticket Oluştur')
@section('content')
    <form method="post" action="{{ route('tickets.store') }}" enctype="multipart/form-data">@csrf
        <div class="row">
            <div class="col-md-7">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="aracBilgiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#aracBilgi" aria-expanded="true"
                                    aria-controls="aracBilgi">
                                İçerik
                            </button>
                        </h2>
                        <div id="aracBilgi" class="accordion-collapse collapse show" aria-labelledby="aracBilgiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Bayi</label>
                                        <select name="branch_id" class="form-control form-control-sm">
                                            @foreach($branches as $branch)
                                                <option @if($authUser->branch_id == $branch->id) selected @endif value="{{ $branch->id }}">{{ $branch->kisa_ad }}</option>
                                            @endforeach
                                        </select>
                                    </div>

                                    <div class="col-xl-6 @if($authUser->type != 'admin') d-none  @endif mt-2">
                                        <label class="form-label">İlgili Departman</label>
                                        <select name="to" class="form-control form-control-sm">
                                            <option selected value="yonetim">Yönetim</option>
                                            <option value="yazilim">Yazılım Ekibi</option>
                                        </select>
                                    </div>


                                    <div class="col-xl-3 mt-2">
                                        <label class="form-label">Personel</label>
                                        <select name="related_user" class="form-control select2">
                                            <option value="">Seçilmedi</option>
                                            @foreach($users as $user)
                                                <option value="{{ $user->id }}">{{ $user->fullName() }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-3 mt-2">
                                        <label class="form-label">Öncelik</label>
                                        <select name="priority" class="form-control form-control-sm">
                                            <option value="1">Düşük</option>
                                            <option value="2">Orta</option>
                                            <option value="3">Yüksek</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Konu<span class="text-danger">*</span></label>
                                        <input type="text" class="form-control form-control-sm" name="subject" placeholder="Konu" required>
                                    </div>

                                    <div class="col-xl-12 mt-2">
                                        <label>Mesajınız<span class="text-danger">*</span></label>
                                        <textarea class="form-control form-control-sm" name="message" required rows="7"></textarea>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control form-control-sm">
                                            <option value="1">Aktif</option>
                                            <option value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-5">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="imageHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#image" aria-expanded="true"
                                    aria-controls="image">
                                Görseller
                            </button>
                        </h2>
                        <div id="image" class="accordion-collapse collapse show" aria-labelledby="imageHeading">
                            <div class="accordion-body">
                                <button type="button" class="btn btn-sm btn-danger add-image">Görsel Ekle</button>
                                <div class="row images"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(".select2").select2({
            placeholder: "Ara.."
        });

        $('.add-image').click(function (){
           $('.images').append("<div class=\"col-xl-12 mt-2\"><label class=\"form-label\">Görsel Seçiniz</label><input type=\"file\" name=\"image[]\" class=\"form-control form-control-sm\" ></div>")
        });
    </script>
@endpush
