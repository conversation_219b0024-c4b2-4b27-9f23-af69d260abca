@extends('pages.build')
@section('title','Destek <PERSON>ay')
@push('css')
    <link rel="stylesheet" href="/assets/css/magnific-popup.css">
    <style>
        hr {
            border-top: #000000 solid;
        }
        .star-rating {
            display: flex;

            flex-direction: column;
        }

        .star-rating input[type="range"] {
            -webkit-appearance: none;
            width: 170px;
            background: transparent;
            margin-right: 10px;
        }

        .star-rating input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            height: 15px;
            width: 15px;
            background: gold;
            cursor: pointer;
            border-radius: 25%;
        }

        .star-rating input[type="range"]::-moz-range-thumb {
            height: 10px;
            width: 10px;
            background: gold;
            cursor: pointer;
            border-radius: 50%;
        }

        .stars {
            display: flex;
        }

        .stars .star {
            font-size: 2.5em;
            color: lightgray;
            transition: color 0.3s;
        }

        .stars .star.active {
            color: gold;
        }
    </style>
@endpush
@section('content')
    <div class="row row-sm mb-4">
        <div class="col-xl-12">
            <div class="row main-chart-wrapper">
                <div class="col-lg-12 col-xxl-8">
                    <div class="card">
                        <div class="card-body">
                            <b>{{ $ticket->subject }}</b><br>
                            <small>#{{ $ticket->uuid }}</small>
                            <p class="mt-2">Oluşturulma Tarihi : <b>{{ $ticket->created_at->translatedFormat('d F Y H:i') }}</b></p>
                        </div>
                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-6 d-flex" style="flex-direction: column">
                                    <span><b>{{ $ticket->getUser ? $ticket->getUser->fullName() : '' }}</b></span>
                                    <small>{{ $ticket->getUser ? ($ticket->getUser->telephone ?? $ticket->getUser->second_telephone) : '' }}</small>
                                </div>
                                <div class="col-md-6 d-flex justify-content-end">
                                    <i class="fe fe-user" style="font-size: 2rem"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    @if($ticket->status == 2 || $ticket->status == 3 || in_array($authUser->user_role_group_id,[30,38,39]))
                        <div class="accordion" id="accordionPanelsStayOpenExample">
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="aracBilgiHeading">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                            data-bs-target="#aracBilgi" aria-expanded="false"
                                            aria-controls="aracBilgi">
                                        Cevap Yaz
                                    </button>
                                </h2>
                                <div id="aracBilgi" class="accordion-collapse collapse" aria-labelledby="aracBilgiHeading">
                                    <div class="accordion-body">
                                        <div class="card answer">
                                            <div class="card-body">
                                                <div class="form-group">
                                                    <label>Mesajınız</label>
                                                    <textarea class="form-control" form="mainForm" @if($authUser->user_role_group_id != 30 && $authUser->user_role_group_id != 38 && $authUser->user_role_group_id != 39) required @endif name="message"></textarea>
                                                </div>
                                                @if($authUser->user_role_group_id == 30 || $authUser->user_role_group_id == 38)
                                                    <div class="form-group">
                                                        <label>Departman</label>
                                                        <select class="form-control"  form="mainForm" name="to">
                                                            <option value="yazilim">Yazılım Ekibi</option>
                                                            <option @if($ticket->to == 'yonetim') selected @endif value="yonetim">Yönetim</option>
                                                        </select>
                                                    </div>
                                                    <div class="form-group">
                                                        <label>Öncelik</label>
                                                        <select class="form-control"  form="mainForm" name="priority">
                                                            <option value="1">Düşük</option>
                                                            <option @if($ticket->priority == 2) selected @endif value="2">Orta</option>
                                                            <option @if($ticket->priority == 3) selected @endif value="3">Yüksek</option>
                                                        </select>
                                                    </div>
                                                @endif
                                                @if($ticket->status == 4)
                                                    <div class="star-rating">
                                                        <label>Değerlendirme Yap</label>
                                                        <div class="stars">
                                                            <span class="star">&#9733;</span>
                                                            <span class="star">&#9733;</span>
                                                            <span class="star">&#9733;</span>
                                                            <span class="star">&#9733;</span>
                                                            <span class="star">&#9733;</span>
                                                        </div>
                                                        <input type="range" id="starRange" form="mainForm" name="rating" min="1" max="5" step="1" value="{{ $ticket->rating ?? 5 }}" />
                                                    </div>
                                                @endif

                                                <form id="mainForm" method="post" action="{{ route('tickets.update',$ticket) }}">@csrf @method('put')
                                                    <input type="hidden" name="status" value="{{ $ticket->status }}">
                                                    <button class="btn btn-sm mt-3 btn-success">Gönder</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif


                    @foreach($ticket->getMessages->reverse() as $message)
                        <div class="card mt-3" @if($message->from == 'sube') style="border: 1px solid green" @elseif($message->from == 'yonetim') style="border: 1px solid deepskyblue" @else style="border: 1px solid orangered" @endif>
                            <div class="card-header">
                                <div class="d-flex justify-content-between" style="width: 100%">
                                    <div>
{{--                                        @if($message->from == 'sube')--}}
{{--                                            Bayi Personeli :--}}
{{--                                        @elseif($message->from == 'yonetim')--}}
{{--                                            Yönetim :--}}
{{--                                        @else--}}
{{--                                            Yazılım Ekibi :--}}
{{--                                        @endif--}}
                                            {{ $message->getUser?->getUserRoleGroup?->name }} :
                                        <b>{{ $message->getUser?->fullname() }}</b>
                                    </div>
                                    <div>
                                        <span>{{ $message->created_at->translatedFormat('d F Y H:i') }}</span>
                                    </div>
                                </div>
                            </div>
                            <hr>
                            <div class="card-body">
                                <p>{!! $message->message !!}</p>
                            </div>
                        </div>
                    @endforeach
                    @if($ticket->getImages->count() > 0)
                        <div class="card mt-3">
                            <div class="card-header">
                                Görseller
                            </div>
                            <hr>
                            <div class="card-body">
                                <div class="row parent-container">
                                    @foreach($ticket->getImages as $image)
                                        <div class="col-4">
                                            <a href="/storage/{{ $image->image }}" class="image-link">
                                                <img src="/storage/{{ $image->image }}" class="img-fluid">
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif

                </div>
                <div class="col-lg-12 col-xxl-4">
                    @if(in_array($authUser->user_role_group_id,[30,38,39]))
                        <div class="card">
                            <div class="card-body">
                                <div class="accordion" id="accordionPanelsStayOpenExample">
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="subeBilgilerHeading">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                                    data-bs-target="#subeBilgiler" aria-expanded="true"
                                                    aria-controls="subeBilgiler">
                                                Durum Ayarı
                                            </button>
                                        </h2>
                                        <div id="subeBilgiler" class="accordion-collapse collapse" aria-labelledby="subeBilgilerHeading">
                                            <div class="accordion-body">
                                                @foreach(__('arrays.ticket_statuses') as $key => $ticketStatus)
                                                    <button type="button" onclick="$('input[name=\'status\']').val({{ $key }});$('button[type=\'button\']').removeClass('btn-dark').addClass('btn-light');$(this).removeClass('btn-light').addClass('btn-dark')" class="btn @if($ticket->status == $key) btn-dark @else btn-light @endif btn-block mt-1">{{ $ticketStatus }}</button>
                                                @endforeach
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif
                    <div class="card overflow-hidden">
                        <div class="main-content-app">
                            <div class="card-body p-0 chat-main">
                                <div class="chat-user-details" id="chat-user-details">
                                    <div class="text-center border-bottom chat-image p-4 pb-0 mb-4 br-5 mt-3">
                                        <div class="main-img-user avatar-xl main-avatar mb-3 mx-auto">
                                            <a class="" href="#"><img alt="avatar" class="rounded-circle" src="{{ !auth('customer')->check() && $authUser->image ? '/storage/'.$authUser->image : '/assets/logo-icon.png' }}"></a>
                                        </div>
                                        <a href="#"><h5 class="mb-1">{{ auth('customer')->check() ? $ticket->related_user != null ? $ticket->getRelatedUser?->name : '-' : $ticket->getCustomer?->unvan }}</h5></a>
                                    </div>
                                    <div class="">
                                        <div class="px-4">
                                            <h6 class="mb-3">Sohbet Detayları :</h6>
                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">Konu</p>
                                                    <p class="fs-12 text-muted">{{ $ticket->subject }}</p>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">Mevcut Departman</p>
                                                    <p class="fs-12 text-muted">{{ $ticket->to == 'yazilim' ? 'Yazılım Ekibi' : 'Yönetim' }}</p>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">Öncelik</p>
                                                    <p class="fs-12 text-muted">@if($ticket->priority == 1) Düşük @elseif($ticket->priority == 2) Orta @else Yüksek @endif</p>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">İlgili Bayi</p>
                                                    <p class="fs-12 text-muted">{{ $ticket->getBranch ? $ticket->getBranch->kisa_ad : '' }}</p>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">İlgili Personel</p>
                                                    <p class="fs-12 text-muted">{{ $ticket->getRelatedUser?->fullName() }}</p>
                                                </div>
                                            </div>
                                            <div class="d-flex">
                                                <div class="ms-2">
                                                    <p class="fs-13 font-weight-semibold mb-0">Değerlendirme Puanı</p>
                                                    <p class="fs-12 text-muted">{{ $ticket->rating ?  $ticket->rating . '/5' : 'Yapılmamış' }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script src="/assets/js/jquery.magnific-popup.js"></script>
    <script>
        @if($ticket->status == 4)
        const starRange = document.getElementById('starRange');
        const stars = document.querySelectorAll('.star');

        function updateStars(value) {
            stars.forEach((star, index) => {
                if (index < value) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        starRange.addEventListener('input', (event) => {
            updateStars(event.target.value);
        });

        // Initial update based on default value
        updateStars(starRange.value);
        @endif

        $(document).ready(function() {
            $('.image-link').magnificPopup({
                type:'image',
                gallery: {
                    enabled: true
                },
            });
        });
    </script>
@endpush
