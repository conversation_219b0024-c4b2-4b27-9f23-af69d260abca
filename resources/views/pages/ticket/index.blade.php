@extends('pages.build')
@section('title','Ticket')
@push('css')
   <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        #chevron {
            position: relative;
            text-align: center;
            padding: 30px;
            margin-bottom: 6px;
            height: 100px;
            width: 100%;
            background: #e30614;
            border-bottom-left-radius: 50%;
            border-bottom-right-radius: 50%;
            color: #fff;
            font-size: 16px;
            box-shadow: 0 7px 10px 3px #b2b2b2;
        }
    </style>
@endpush
@section('content')
    @if(auth('customer')->check())
        <div id="chevron"><i class="ion-ios-chatbubbles" style="margin-right: 2rem"></i> İstek, Şikayet ve Önerileriniz için...</div>
    @endif
    <div class="row">
        <div class="col-xl-12">
            @if(auth()->check())
                <div class="card custom-card">
                    <form method="get" action="{{ route('tickets.index') }}">
                        <div class="card-body">
                            <div class="row">
                                @if($authUser->user_role_group_id != 39)
                                    <div class="col">
                                        <div class="form-group">
                                            <label>Departman</label>
                                            <select class="form-control form-control-sm" name="to">
                                                <option value="">Tümü</option>
                                                <option @if(isset($_GET['to']) && $_GET['to'] == 'yonetim') selected @endif value="yonetim">Yönetim</option>
                                                <option @if(isset($_GET['to']) && $_GET['to'] == 'yazilim') selected @endif value="yazilim">Yazılım Ekibi</option>
                                            </select>
                                        </div>
                                    </div>
                                @endif

                                <div class="col">
                                    <div class="form-group">
                                        <label>Öncelik</label>
                                        <select class="form-control form-control-sm" name="priority">
                                            <option value="">Tümü</option>
                                            <option @if(isset($_GET['priority']) && $_GET['priority'] == '1') selected @endif value="1">Düşük</option>
                                            <option @if(isset($_GET['priority']) && $_GET['priority'] == '2') selected @endif value="2">Orta</option>
                                            <option @if(isset($_GET['priority']) && $_GET['priority'] == '3') selected @endif value="3">Yüksek</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col">
                                    <div class="form-group">
                                        <label>Durum</label>
                                        <select class="form-control form-control-sm" name="status">
                                            <option value="">Tümü</option>
                                            @foreach(__('arrays.ticket_statuses') as $key => $ticketStatus)
                                                <option @if(isset($_GET['status']) && $_GET['status'] == $key) selected @endif value="{{ $key }}">{{ $ticketStatus }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                    <div class="col">
                                        <div class="form-group">
                                            <label>Baş. Tarihi</label>
                                            <input type="date" class="form-control form-control-sm" name="start_date" value="{{ $filters['startDate'] }}">
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="form-group">
                                            <label>Bit. Tarihi</label>
                                            <input type="date" class="form-control form-control-sm" name="end_date" value="{{ $filters['endDate'] }}">
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="form-group">
                                            <label>İlgili Personel</label>
                                            <select class="form-control form-control-sm select2" name="related_user">
                                                <option value="0">Tümü</option>
                                                @foreach($users as $user)
                                                    <option @if(isset($_GET['related_user']) && $_GET['related_user'] == $user->id) selected @endif value="{{ $user->id }}">{{ $user->fullName() }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                @if($authUser->user_role_group_id == 30 || $authUser->user_role_group_id == 38 || $authUser->user_role_group_id == 39 || count($authUser->getBranchIds()) > 1)
                                    <div class="col">
                                        <div class="form-group">
                                            <label>Bayi</label>
                                            <select class="form-control form-control-sm select2" name="branch_id">
                                                <option value="0">Tüm Bayiler</option>
                                                @if($authUser->user_role_group_id == 30 || $authUser->user_role_group_id == 38 || $authUser->user_role_group_id == 39)
                                                    @foreach(\App\Models\Branch::where('status',1)->get() as $filterBranch)
                                                        <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                                    @endforeach
                                                @else
                                                    @foreach(\App\Models\Branch::where('status',1)->whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                                        <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                                    @endforeach
                                                @endif
                                            </select>
                                        </div>
                                    </div>
                                @endif
                                <div class="col mt-4">
                                    <button class="btn btn-sm btn-success">Listele</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            @endif

            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    <a href="{{ route('tickets.create') }}" class="nav-link @if(auth('customer')->check()) btn-danger @endif">Yeni Talep Oluştur</a>
                </nav>

                <div class="card-body">

                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr><th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th>Konu</th>
                            <th>Mevcut Departman</th>
                            <th>İlgili Personel</th>
                            <th>Öncelik</th>
                            <th>Durum</th>
                            <th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($items->reverse() as $key => $item)
                            <tr>
                                <td></td>
                                <td>{{ mb_substr($item->subject,0,40,'UTF-8') }}..</td>
                                <td>{{ $item->to == 'yazilim' ? 'Yazılım Ekibi' : 'Yönetim' }}</td>
                                <td>{{ $item->getRelatedUser ? $item->getRelatedUser->fullName() : '' }}</td>
                                <td @if($item->priority == 1) style="background-color: yellow" @elseif($item->priority == 2) style="background-color: orange" @else style="background-color: red" @endif>@if($item->priority == 1) Düşük @elseif($item->priority == 2) Orta @else Yüksek @endif</td>
                                <td @if($item->status == 0) style="background-color: orangered" @elseif($item->status == 1) style="background-color: greenyellow" @else style="background-color: #2fffe7" @endif>{{ __('arrays.ticket_statuses')[$item->status] }}</td>
                                <td>
                                    @if(in_array('*', $userRoles) || in_array('edit_ticket', $userRoles))
                                        <a class="btn btn-primary btn-sm" href="{{ route('tickets.edit',$item->uuid) }}">Görüntüle</a>
                                    @endif
                                        @if(in_array('*', $userRoles) || in_array('delete_ticket', $userRoles))
                                        @if($item->user_id == $authUser->id || $authUser->type == 'admin')
                                            <button class="btn btn-danger btn-sm" type="button" onclick="checkBeforeDelete('deleteForm{{ $key }}')">Sil</button>
                                            <form method="post" action="{{ route('tickets.destroy',$item) }}" id="deleteForm{{ $key }}">@csrf @method('delete')</form>
                                        @endif
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <!-- Datatables Cdn -->
       <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>
        $(".select2").select2({
            placeholder: "Ara.."
        });
        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({
                "dom": '<"pull-left"f><"pull-right"l>rtip',
                responsive: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })
        });
    </script>
@endpush
