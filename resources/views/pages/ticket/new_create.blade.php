@extends('pages.build')
@section('title','Yeni Şikayet Oluştur')
@section('content')
    <div class="container ">
        <div class="card ">
            <div class="card-body ">
                <div class="row d-flex justify-content-around">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label>Telefon Numarası</label>
                            <input class="form-control telefon" name="telefon">
                        </div>
                    </div>
                    <div class="offset-md-2 col-md-2 mt-4"><button class="btn btn-danger">Sorgula</button> </div>
                    <div class="example">
                        <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                            <a class="nav-link active" data-bs-toggle="tab" role="tab" href="#nav-bireysel" onclick="change('bireysel')" aria-selected="true">Bireysel</a>
                            <a class="nav-link" data-bs-toggle="tab" role="tab" href="#nav-bireysel" onclick="change('kurumsal')" aria-selected="false">Kurumsal</a>
                        </nav>
                        <div class="tab-content">
                            <div class="tab-pane show active text-muted" id="nav-bireysel" role="tabpanel">
                                <form>
                                    <div class="row">
                                        <div class="col-md-12 kurumsal d-none">
                                            <div class="form-group">
                                                <label>Unvan</label>
                                                <input type="text" class="form-control kurumsal" name="unvan" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6 kurumsal d-none">
                                            <div class="form-group">
                                                <label>Vergi Dairesi</label>
                                                <input type="text" class="form-control kurumsal" name="vergi_dairesi" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6 kurumsal d-none">
                                            <div class="form-group">
                                                <label>Vergi No</label>
                                                <input type="text" class="form-control kurumsal" name="vergi_no" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6 bireysel">
                                            <div class="form-group">
                                                <label>Ad</label>
                                                <input type="text" class="form-control bireysel" name="ad" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6 bireysel">
                                            <div class="form-group">
                                                <label>Soyad</label>
                                                <input type="text" class="form-control bireysel" name="soyad" required>
                                            </div>
                                        </div>
                                        <div class="col-md-12 bireysel">
                                            <div class="form-group">
                                                <label>T.C. Kimlik No</label>
                                                <input type="text" class="form-control bireysel" name="tc" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>E-posta Adresi</label>
                                                <input type="email" class="form-control" name="email" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>Telefon Numarası</label>
                                                <input type="text" class="form-control telefon" name="telephone" required>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="form-group">
                                                <label>Bayi</label>
                                                <select name="branch_id" class="form-control select2">
                                                    @foreach($branches as $branch)
                                                        <option value="{{ $branch->id }}">{{ $branch->kisa_ad }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-12"></div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>İl</label>
                                                <select name="city_id" class="form-control select2">
                                                    @foreach($cities as $city)
                                                        <option value="{{ $city->id }}">{{ $city->title }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>İlçe</label>
                                                <select name="town_id" class="form-control select2">

                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Semt</label>
                                                <input type="text" class="form-control" name="semt" required>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label>Mahalle</label>
                                                <input type="text" class="form-control" name="mahalle" required>
                                            </div>
                                        </div>
                                        <div class="col-12 text-center">
                                            <button class="btn btn-danger btn-sm">Talep Girişi</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <script>
        addEventListener("DOMContentLoaded", (event) => {
            $('select[name="city_id"]').trigger('change')
        });

        $('select[name="city_id"]').on('change',function (){
            $('select[name="town_id"]').find('option').remove()
            if ($('select[name="city_id"]').val() > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$('select[name="city_id"]').val()} ,
                    success: function (response) {
                        $.each(response.items,function (index,item){
                            $('select[name="town_id"]').append("<option value="+item.ilce_id+">"+item.ilce_title+"</option>")
                        })
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }
        })

        function change(type){
            if (type == 'bireysel'){
                $('.kurumsal').addClass('d-none').removeAttr('required');
                $('.bireysel').removeClass('d-none').attr('required','true')
            }else{
                $('.kurumsal').removeClass('d-none').attr('required','true');
                $('.bireysel').addClass('d-none').removeAttr('required')
            }
        }
    </script>
@endpush
