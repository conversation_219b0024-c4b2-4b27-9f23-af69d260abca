@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> <a href="{{ url()->previous() }}" class="btn btn-danger btn-sm">Geri <PERSON>ö<PERSON></a> @endpush
@section('title','Personel Ekle')
@section('content')
    <form id="mainForm" method="post" action="{{ route('users.store') }}" enctype="multipart/form-data">@csrf
        <input type="hidden" name="return_url" value="{{ url()->previous() }}">
        <div class="row">
            <div class="col-md-12">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="kisiselBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#kisiselBilgiler" aria-expanded="true"
                                    aria-controls="kisiselBilgiler">
                                Kişisel Bilgiler
                            </button>
                        </h2>
                        <div id="kisiselBilgiler" class="accordion-collapse collapse show" aria-labelledby="kisiselBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-md-4 mt-3">
                                                <label>Ad<span class="text-danger">*</span></label>
                                                <input class="form-control form-control-sm" name="name" value="{{ isset($_GET['name']) && $_GET['name'] != '' ? $_GET['name'] : '' }}" required>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>İkinci Ad</label>
                                                <input class="form-control form-control-sm" name="second_name" value="{{ isset($_GET['second_name']) && $_GET['second_name'] != '' ? $_GET['second_name'] : '' }}">
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Soyad<span class="text-danger">*</span></label>
                                                <input class="form-control form-control-sm" name="surname" value="{{ isset($_GET['surname']) && $_GET['surname'] != '' ? $_GET['surname'] : '' }}" required>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>T.C. Kimlik No <span class="text-danger">*</span></label>
                                                <input class="form-control form-control-sm" name="tc" value="{{ isset($_GET['tc']) && $_GET['tc'] != '' ? $_GET['tc'] : '' }}" minlength="11" maxlength="11" required>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Doğum Tarihi</label>
                                                <input class="form-control form-control-sm" name="dogum_tarihi" oninput="tcNoDogrula()" type="date" value="{{ date('Y-m-d') }}">
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Cinsiyet</label>
                                                <select class="form-control form-control-sm" name="gender">
                                                    <option value="male">Erkek</option>
                                                    <option value="female">Kadın</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>E-posta Adresi</label>
                                                <input class="form-control form-control-sm" name="email" value="{{ isset($_GET['email']) && $_GET['email'] != '' ? $_GET['email'] : '' }}" type="email">
                                            </div>
                                            {{--                                            <div class="col-md-4 mt-3">--}}
                                            {{--                                                <label>Şifre<span class="text-danger">*</span></label>--}}
                                            {{--                                                <input class="form-control form-control-sm" name="password" required type="password">--}}
                                            {{--                                            </div>--}}
                                            {{--                                            <div class="col-md-4 mt-3">--}}
                                            {{--                                                <label>Şifre Tekrar<span class="text-danger">*</span></label>--}}
                                            {{--                                                <input class="form-control form-control-sm" name="password_again" required type="password">--}}
                                            {{--                                            </div>--}}
                                            <div class="col-md-4 mt-3">
                                                <label>Alt Beden</label>
                                                <select name="alt_beden" id="" class="form-control form-control-sm">
                                                    <option>Seçiniz</option>
                                                    <!-- @for($i = 28; $i <= 60; $i +=2)
                                                        <option value="{{ $i }}">{{ $i }}</option>
                                                    @endfor -->
                                                    <option value="XS">XS</option>
                                                    <option value="S">S</option>
                                                    <option value="M">M</option>
                                                    <option value="L">L</option>
                                                    <option value="XL">XL</option>
                                                    <option value="XXL">XXL</option>
                                                    <option value="XXXL">XXXL</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Üst Beden</label>
                                                <select name="ust_beden" id="" class="form-control form-control-sm">
                                                    <option>Seçiniz</option>
                                                    <option value="XS">XS</option>
                                                    <option value="S">S</option>
                                                    <option value="M">M</option>
                                                    <option value="L">L</option>
                                                    <option value="XL">XL</option>
                                                    <option value="XXL">XXL</option>
                                                    <option value="XXXL">XXXL</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Ayakkabı Numarası</label>
                                                <select name="ayakkabi_numarasi" id="" class="form-control form-control-sm">
                                                    <option>Seçiniz</option>
                                                    @for($i = 30; $i <= 50; $i++)
                                                        <option value="{{ $i }}">{{ $i }}</option>
                                                    @endfor
                                                </select>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label for="blood_group">Kan Grubu:</label>
                                                <select class="form-control form-control-sm" name="blood_group" id="blood_group">
                                                    <option value="A+">A+</option>
                                                    <option value="A-">A-</option>
                                                    <option value="B+">B+</option>
                                                    <option value="B-">B-</option>
                                                    <option value="AB+">AB+</option>
                                                    <option value="AB-">AB-</option>
                                                    <option value="O+">O+</option>
                                                    <option value="O-">O-</option>
                                                </select>
                                            </div>
                                            @if($authUser->type == 'admin')
                                                <div class="col-md-4 mt-3">
                                                    <label for="first_login">İlk Girişte Şifre Belirlesin</label>
                                                    <select class="form-control form-control-sm" name="first_login" id="first_login">
                                                        <option value=1>Evet</option>
                                                        <option value="0">Hayır</option>
                                                    </select>
                                                </div>
                                            @endif

                                            <div class="col-md-4 mt-3">
                                                <label>Telefon Numarası <span class="text-danger add-phone" style="cursor: pointer">Ekle</span></label>
                                                <input class="form-control form-control-sm mask-tel" name="telephone_login" value="{{ isset($_GET['telephone']) && $_GET['telephone'] != '' ? $_GET['telephone'] : '' }}">
                                            </div>
                                            <div class="col-md-4 mt-3 telefon">

                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Fotoğraf</label>
                                        <input type="file" class="dropify" data-height="250" name="image"/>
                                    </div>
                                    <div class="col-md-12 mt-3">
                                        <label>Not</label>
                                        <textarea class="form-control form-control-sm" name="comment"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="adresHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#adres" aria-expanded="true"
                                    aria-controls="adres">
                                Adres Bilgileri
                            </button>
                        </h2>
                        <div id="adres" class="accordion-collapse collapse show" aria-labelledby="adresHeading">
                            <div class="accordion-body">
                                <button type="button" class="btn btn-sm btn-danger my-3 add-address">Kayıt Ekle</button>
                                <div class="row addresses">
                                    <div class="col-md-3">
                                        <label>Başlık</label>
                                        <input class="form-control form-control-sm" name="address_title[]">
                                    </div>
                                    <div class="col-md-3">
                                        <label>Şehir</label>
                                        <select name="city[]" class="form-control select2">
                                            @foreach(\App\Models\City::all() as $city)
                                                <option value="{{ $city->id }}">{{ $city->title }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-6">
                                        <label>Adres</label>
                                        <input class="form-control form-control-sm" name="address[]">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="bolumHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#bolum" aria-expanded="true"
                                    aria-controls="bolum">
                                Çalışma Bilgileri
                            </button>
                        </h2>
                        <div id="bolum" class="accordion-collapse collapse show" aria-labelledby="bolumHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-4 mt-3">
                                        <label>Kullanıcı Rolü</label>
                                        <select name="user_role_group_id" class="select2">
                                            @foreach($userRoleGroups->where('id','!=',30) as $userRoleGroup)
                                                <option value="{{ $userRoleGroup->id }}">{{ $userRoleGroup->name }}</option>
                                            @endforeach
                                            @if($authUser->isAdmin())
                                                <option value="30">{{ $userRoleGroups->where('id', '=', 30)->first()->name }}</option>
                                            @endif
                                        </select>
                                    </div>
                                    @if($authUser->type == 'admin')
                                        <div class="col-md-4 mt-3">
                                            <label>Bayi</label>
                                            <select class="select2 user-branches" name="branch_id[]" multiple required>
                                                @foreach($branches as $branch)
                                                    <option value="{{ $branch->id }}">{{ $branch->unvan }}</option>
                                                @endforeach
                                            </select>
                                        </div>
{{--                                        <div class="col-md-4 mt-3">--}}
{{--                                            <label>Bölge</label>--}}
{{--                                            <select name="zone_id" class="select2">--}}
{{--                                                <option value="0">Yok</option>--}}
{{--                                                @foreach($zones as $zone)--}}
{{--                                                    <option value="{{ $zone->id }}">{{ $zone->name }}</option>--}}
{{--                                                @endforeach--}}
{{--                                            </select>--}}
{{--                                        </div>--}}
                                    @endif
                                    <div class="col-md-4 mt-3">
                                        <label>Departman</label>
                                        <select name="department" class="select2">
                                            @foreach(__('arrays.departments') as $departmentKey => $departmentName)
                                                <option value="{{ $departmentKey }}">{{ $departmentName }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Şirket Telefonu</label>
                                        <select class="select2" name="has_phone">
                                            <option value="1">Var</option>
                                            <option value="0">Yok</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Şirket Bilgisayarı</label>
                                        <select class="select2" name="has_computer">
                                            <option value="1">Var</option>
                                            <option value="0">Yok</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Şirket Aracı</label>
                                        <select class="select2" name="has_car">
                                            <option value="1">Var</option>
                                            <option value="0">Yok</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Şirket Belgeleri</label>
                                        <select class="select2" name="has_document">
                                            <option value="1">Var</option>
                                            <option value="0">Yok</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Yıllık Çalışma Süresi</label>
                                        <input class="form-control form-control-sm" name="yearly_work_time" type="number">
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>İşe Başlama Tarihi</label>
                                        <input class="form-control form-control-sm" name="work_start_date" type="date">
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Kalan Tatil Hakkı (Gün)</label>
                                        <input class="form-control form-control-sm" name="remain_holiday_date_count" type="number">
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Oturum Süresi (Saniye)</label>
                                        <input class="form-control form-control-sm" name="session_lifetime" value="600" type="number">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="documentsHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#documents" aria-expanded="true"
                                    aria-controls="documents">
                                Evraklar
                            </button>
                        </h2>
                        <div id="documents" class="accordion-collapse collapse show" aria-labelledby="documentsHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-3">
                                        <label class="form-label">Kimlik Fotokopisi</label>
                                        <input type="file" class="dropify" data-height="250" name="id_document"/>
                                    </div>
                                    <div class="col-xl-3">
                                        <label class="form-label">Adres Bilgileri</label>
                                        <input type="file" class="dropify" data-height="250" name="address_document"/>
                                    </div>
                                    <div class="col-xl-3">
                                        <label class="form-label">Sözleşme Bilgileri</label>
                                        <input type="file" class="dropify" data-height="250" name="agreement_document"/>
                                    </div>
                                    <div class="col-xl-3">
                                        <label class="form-label">Evlilik Bilgileri</label>
                                        <input type="file" class="dropify" data-height="250" name="marriage_document"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="emergencyHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#emergency" aria-expanded="true"
                                    aria-controls="emergency">
                                Kişi Yakınları
                            </button>
                        </h2>
                        <div id="emergency" class="accordion-collapse collapse show" aria-labelledby="emergencyHeading">
                            <div class="accordion-body">
                                <button type="button" class="btn btn-sm btn-danger mt-3 add-emergency">Kayıt Ekle</button>
                                <div class="row emergencies">
                                    <div class="col-md-3 mt-3">
                                        <label>Yakınlık</label>
                                        <input class="form-control form-control-sm" name="emergency_title[]">
                                    </div>
                                    <div class="col-md-3 mt-3">
                                        <label>Yakın Adı</label>
                                        <input class="form-control form-control-sm" name="emergency_name[]">
                                    </div>
                                    <div class="col-md-3 mt-3">
                                        <label>Yakın Telefon</label>
                                        <input class="form-control form-control-sm tel-mask" name="emergency_telephone[]">
                                    </div>
                                    <div class="col-md-3 mt-3">
                                        <label>Yakın Adres</label>
                                        <input class="form-control form-control-sm" name="emergency_address[]">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="bankaHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#banka" aria-expanded="true"
                                    aria-controls="banka">
                                Banka Bilgileri
                            </button>
                        </h2>
                        <div id="banka" class="accordion-collapse collapse show" aria-labelledby="bankaHeading">
                            <div class="accordion-body">
                                <div class="row bankalar">
                                    <div class="col-md-6 mt-3">
                                        <label>Banka Adı</label>
                                        <input class="form-control form-control-sm" name="banka_adi[]">
                                    </div>
                                    <div class="col-md-6 mt-3">
                                        <label>IBAN</label>
                                        <input class="form-control form-control-sm" name="iban[]">
                                    </div>
                                </div>
                                <button type="button" class="btn btn-sm btn-success banka-ekle">Banka Ekle</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button type="button" class="btn btn-success mt-3 mb-3 save_form">
                    <i class="fa fa-save"></i> Kaydet
                </button>
            </div>
            <div class="col-md-3 d-none tel-dogrula">
                <div class="form-group">
                    <label>Doğrulama Kodu</label>
                    <input class="form-control form-control-sm" type="hidden" name="old_verification_code" value="{{ rand(1000,9999) }}">
                    <input class="form-control form-control-sm" required minlength="4" maxlength="4" name="verification_code">
                </div>
            </div>
            <div class="col-md-2 d-none tel-dogrula">
                <button type="button" class="btn btn-danger mt-5 tel-verify-button btn-sm">Doğrula</button>
            </div>

        </div>
        <input type="hidden" name="session_name" value="user_create">
    </form>
    @if(\Illuminate\Support\Facades\Cache::has('user_create'))
        <div class="modal fade" id="deleteCacheModal" tabindex="-1"
             aria-labelledby="deleteCacheModal" data-bs-keyboard="false"
             aria-hidden="true">
            <!-- Scrollable modal -->
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="staticBackdropLabel2">Mevcut Kayıt Bulundu!
                        </h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Önceden oluşturduğunuz kayıt kapanmadı!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success"
                                data-bs-dismiss="modal">Kayıta Devam Et</button>
                        <button type="submit" form="deleteCache" class="btn btn-danger">Yeni Kayıt Oluştur</button>
                        <form id="deleteCache" method="post" action="{{ route('deleteCache') }}">@csrf<input type="hidden" name="type" value="user_create"></form>
                    </div>
                </div>
            </div>
        </div>
    @endif
    <div class="modal fade" id="phone_verified" tabindex="-1"
         aria-labelledby="phone_verified" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Telefon Numarası Doğrula
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body phone_list_modal">

                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-success send_verification_code"
                           >Doğrulama Kodu Gönder</button>
                    <button type="button" class="btn btn-danger" data-bs-dismiss="modal"
                            aria-label="Close">İptal Et</button>

                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')

    <!-- Select2 Cdn -->
    <script src="/assets/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/toastify-js"></script>

    <script>
        {{--addEventListener("DOMContentLoaded", (event) => {--}}
        {{--    @if(\Illuminate\Support\Facades\Cache::has('user_create'))--}}
        {{--    $('#deleteCacheModal').modal('toggle')--}}
        {{--    @endif--}}
        {{--});--}}
        (function (){
            "use strict"
            $(".select2").select2({
                placeholder: "Ara.."
            });
        })();
    </script>
    <script src="/assets/js/dropify.min.js"></script>
    <script>

        $(document).ready(function(){
            const Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
            $('.save_form').click(function(){
                var name = $('input[name=name]').val()
                var surname = $('input[name=surname]').val()
                var email = $('input[name=email]').val()
                var telephone = $('input[name=telephone_login]').val()
                var telephones = [];
                $('input[name="telephone[]"]').each(function() {
                    if($(this).val() != ''){
                        telephones.push($(this).val());
                    }

                });
                if(name == ''){
                    Toast.fire({
                        icon: "error",
                        title: "Personel Adı Eksik Girildi!"
                    });
                    return false;
                }
                if(surname == ''){
                    Toast.fire({
                        icon: "error",
                        title: "Personel Soyadı Eksik Girildi!"
                    });
                    return false;
                }
                // if(email == ''){
                //     Toast.fire({
                //         icon: "error",
                //         title: "Personel Email Eksik Girildi!"
                //     });
                //     return false;
                // }
                if(telephone == ''){
                    Toast.fire({
                        icon: "error",
                        title: "Personel Telefonu Eksik Girildi!"
                    });
                    return false;
                }
                html = "<div class='col-12'><div class='row'>";
                html += "<div class='form-group col-4'><input checked type='radio' id='send_sms_radio"+-1+"' name='sms_tel' value='"+telephone+"'> <label for='send_sms_radio"+-1+"'>"+telephone+"</label></div>"
                $.each(telephones,function (index,item){
                    // var checkClass = ""
                    // if(index == 0){
                    //     checkClass = "checked=''"
                    // }
                    html += "<div class='form-group col-4'><input type='radio' id='send_sms_radio"+index+"' name='sms_tel' value='"+item+"'> <label for='send_sms_radio"+index+"'>"+item+"</label></div>"
                })
                html += "</div></div>";
                $('.phone_list_modal').html(html)
                $('#phone_verified').modal('show')
            })
            $('.send_verification_code').click(function(){
                var sms_tel =  $('input[type=radio][name=sms_tel]:checked').val()
                var thisbtn = $(this)
                if(sms_tel !== undefined){
                    $.ajax({
                        url: "{{ route('api.smsVerification') }}",
                        type: "post",
                        data: {
                            _token:'{{@csrf_token()}}',
                            phone:sms_tel,
                            page:'user_create',
                        },
                        success: function (response) {
                            console.log(response);
                            if(response.success){
                                htmltext = '<div class="col-12"><input type="hidden" name="sms_verification_id" value="'+response.sms_verification_id+'"><input class="form-control form-control-sm" placeholder="UMR-XX-XX" name="login_sms"  value="'+response.code+'" required type="text"></div>';
                                htmltext += '<div class="col-12 mt-2"><button type="button" class="btn btn-sm btn-success use_code">Kodu Doğrula</button></div>'
                                $('.phone_list_modal').html(htmltext);
                                if(document.querySelector('input[name="login_sms"]')){
                                    const maskTel = IMask(document.querySelector('input[name="login_sms"]'), {
                                        mask: 'UMR-00-00'
                                    });
                                }

                                $('.use_code').click(function(){
                                    $.ajax({
                                        url: "{{ route('api.smsVerificationUse') }}",
                                        type: "post",
                                        data: {
                                            _token:'{{@csrf_token()}}',
                                            code:$('input[name=login_sms]').val(),
                                            sms_verification_id:$('input[name=sms_verification_id]').val(),
                                            page:'user_create',
                                        },
                                        success: function (response) {
                                            if(response.success){
                                              $('#mainForm').submit()
                                            }else{
                                                Toast.fire({
                                                    icon: "error",
                                                    title: "Kod Hatalı Tekrar Deneyiniz.!"
                                                });
                                            }
                                        },
                                        error: function(jqXHR, textStatus, errorThrown) {
                                            console.log(textStatus, errorThrown);
                                        }
                                    });
                                })
                            }else{
                                Toast.fire({
                                    icon: "error",
                                    title: "Tekrar kod almak için 3 dk bekleyiniz.!"
                                });
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.log(textStatus, errorThrown);
                        }
                    });
                }else{
                    Toast.fire({
                        icon: "error",
                        title: "Sms Gönderilecek Numarayı Seçiniz!"
                    });
                }
            })
        })

        $('.dropify').dropify({
            messages: {
                'default': 'Dosya Sürükle veya Tıkla',
                'replace': 'Dosya Sürükle veya Tıkla',
                'remove':  'Kaldır',
                'error':   'Bir Hata Ortaya Çıktı'
            }
        });

        $('.banka-ekle').on('click',function (){
            let html = '<div class="col-md-6 mt-3"><label>Banka Adı</label><input class="form-control form-control-sm" name="banka_adi[]"></div>'
            html += '<div class="col-md-6 mt-3"><label>IBAN</label><input class="form-control form-control-sm" name="iban[]"></div>'
            $('.bankalar').append(html)
        })

        addEventListener("DOMContentLoaded", (event) => {
            $('.add-phone').on('click',function (){
                $('.telefon').after("<div class=\"col-md-6 mt-3\"><label>Telefon Başlık</label> <input class=\"form-control\" name=\"telephone_title[]\"> </div><div class=\"col-md-6 mt-3\"><label>Telefon Numarası</label> <input class=\"form-control telephones\" name=\"telephone[]\"> </div>")
                document.querySelectorAll('input[name="telephone[]"]').forEach(function (item,index){
                    const maskTel2 = IMask(item, {
                        mask: '(*************'
                    });
                })
            })

            $('select[name="user_role_group_id"]').on('change', function () {
                let $val = $(this).val();

                $('.user-branches').val(null).removeAttr('multiple');

                $('.user-branches option[value="0"]').remove();

                if ($val == 30) {
                    $('.user-branches').append("<option value='0'>Tümü</option>").val(0);
                } else if ($val == 31 || $val == 44 || $val == 45 || $val == 47) {
                    $('.user-branches').attr('multiple', 'true');
                }

                $('.user-branches').select2();
            });

        });


        $('.add-emergency').on('click',function (){
            let html = '<div class="col-md-3 mt-3"><label>Yakınlık</label><input class="form-control form-control-sm" name="emergency_title[]"></div>'
            html += '<div class="col-md-3 mt-3"><label>Yakın Adı</label><input class="form-control form-control-sm" name="emergency_name[]"></div>'
            html += '<div class="col-md-3 mt-3"><label>Yakın Telefon</label><input class="form-control tel-mask" name="emergency_telephone[]"></div>'
            html += '<div class="col-md-3 mt-3"><label>Yakın Adres</label><input class="form-control form-control-sm" name="emergency_address[]"></div>'

            $('.emergencies').append(html)
        })
        $('.add-address').on('click',function (){
            let html = '<div class="col-md-3"><label>Başlık</label><input class="form-control form-control-sm" name="address_title[]"></div>'
            html += '<div class="col-md-3"><label>Şehir</label><select name="city[]" class="form-control select2">@foreach(\App\Models\City::all() as $city)<option value="{{ $city->id }}">{{ $city->title }}</option>@endforeach</select></div>'
            html += '<div class="col-md-6"><label>Adres</label><input class="form-control form-control-sm" name="address[]"></div>'
            $('.addresses').append(html)
            $(".select2").select2({
                placeholder: "Ara.."
            });
        })
        $('.sub-button').on('click',function (){
            if ($('input[name="telephone[]"]')[0].value.length == 14){
                $('.tel-dogrula').removeClass('d-none')
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Telefon Numarası Eksik Girildi!"
                });
            }
        })

        $('.tel-verify-button').on('click',function (){
            if ($('input[name="verification_code"]').val() == $('input[name="old_verification_code"]').val()){
                $('.sub-button').removeAttr('type')
                $('.sub-button').html("Kaydet")
                $('.tel-dogrula').addClass('d-none')
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Doğrulama Kodu Eşleşmiyor!"
                });
            }
        })

        setInterval(function (){
            $.ajax({
                url: "{{ route('saveToSession') }}",
                type: "post",
                data: $('#mainForm').serialize(),
                success: function (response) {

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        },30 * 1000)

        let tcTimeout = null; // Timeout ID'si

        // T.C. Kimlik numarası alanına yazıldığında tetiklenen olay
        $('input[name="tc"]').on('input', function() {
            clearTimeout(tcTimeout); // Önceki timeout'u temizle

            let $tc = $(this).val().replace(/[^0-9]/g, ''); // T.C. Kimlik numarasındaki harf ve özel karakterleri temizle, sadece sayıları al

            // Eğer 11 hane girildiyse doğrulama işlemini başlat
            if ($tc.length === 11) {
                // 1 saniye bekleyip doğrulama işlemini başlat
                tcTimeout = setTimeout(function() {
                    personalTcControl($tc); // T.C. numarasını doğrulayan fonksiyon
                }, 1000); // 1 saniye (1000 ms) bekle
            }
        });
        /*Düzeltme*/
        function personalTcControl(tc) {
            $.ajax({
                url: "{{ route('api.personalTcControl') }}", // Laravel'deki route
                type: "POST",
                data: {
                    '_token': '{{ csrf_token() }}', // CSRF token
                    'tc': tc // T.C. Kimlik numarası
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: "error",
                            title: "T.C. Kimlik No var!",
                            showConfirmButton: false,
                            timer: 3000
                        });
                    } else {
                        Swal.fire({
                            icon: "success",
                            title: "T.C. Kimlik No Yok!",
                            showConfirmButton: false,
                            timer: 3000
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Swal.fire({
                        icon: "error",
                        title: "Bir hata oluştu. Lütfen tekrar deneyin.",
                        showConfirmButton: true
                    });
                }
            });
        }
       /* $('input[name="tc"]').on('input', function() {
            let $tc = $(this).val(); // T.C. Kimlik numarasını al

            // Eğer 11 hane girildiyse doğrulama işlemini başlat
            if ($tc.length === 11) {
                tcNoDogrula(); // Doğrulama fonksiyonunu çalıştır
            }
        });

        function tcNoDogrula() {
            let $tc = $('input[name="tc"]').val();
            let $ad = $('input[name="name"]').val();
            let $ikinci_ad = $('input[name="second_name"]').val();
            let $soyad = $('input[name="surname"]').val();
            let $dogum_tarihi = $('input[name="dogum_tarihi"]').val();

            // Diğer alanları kontrol et
            if ($ad != '') {
                if ($soyad != '') {
                    if ($dogum_tarihi != '') {
                        $.ajax({
                            url: "{{ route('api.tcNoDogrula') }}",
                            type: "POST",
                            data: {
                                '_token': '{{ csrf_token() }}',
                                'tc': $tc,
                                'ad': $ad + ' ' + ($ikinci_ad || ''),
                                'soyad': $soyad,
                                'dogum_tarihi': $dogum_tarihi
                            },
                            success: function(response) {
                                if (response.success == 'true') {
                                    Swal.fire({
                                        icon: "success",
                                        title: "T.C. Kimlik No Doğrulandı!",
                                        showConfirmButton: false,
                                        timer: 3000
                                    });
                                } else if (response.success == 'false') {
                                    if (response.tc_uniq == 2) {
                                        Swal.fire({
                                            icon: "error",
                                            title: "Bu T.C. Kimlik No ile kayıtlı müşteri mevcut!",
                                            text: response.message,
                                            showConfirmButton: true
                                        });
                                    } else {
                                        Swal.fire({
                                            icon: "error",
                                            title: "T.C. Kimlik No Doğrulanamadı!",
                                            showConfirmButton: false,
                                            timer: 3000
                                        });
                                    }
                                }
                            }
                        });
                    } else {
                        Swal.fire({
                            icon: "error",
                            title: "Doğum Tarihi Zorunludur!",
                            showConfirmButton: false,
                            timer: 3000
                        });
                    }
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "Soyisim Zorunludur!",
                        showConfirmButton: false,
                        timer: 3000
                    });
                }
            } else {
                Swal.fire({
                    icon: "error",
                    title: "İsim Zorunludur!",
                    showConfirmButton: false,
                    timer: 3000
                });
            }
        }*/

        let phoneTimeout = null; // Timeout ID'si

        // Telefon numarası alanına yazıldığında tetiklenen olay
        $('input[name="telephone_login"]').on('input', function() {
            clearTimeout(phoneTimeout); // Önceki timeout'u temizle

            let phone = $(this).val().replace(/[^0-9]/g, ''); // Maskeyi temizle ve sadece rakamları al

            // Eğer telefon numarası 11 karakter ise kontrolü başlat
            if (phone.length === 11) {
                console.log('11 karaktere ulaşıldı, phoneControl fonksiyonu çağrılacak');
                phoneControl(phone); // Telefon numarasını doğrulayan fonksiyon
            }
        });

        function phoneControl(phone) {
            $.ajax({
                url: "{{ route('api.phoneControl') }}", // Laravel'deki route
                type: "POST",
                data: {
                    '_token': '{{ csrf_token() }}', // CSRF token
                    'telephone_login': phone // Temizlenmiş telefon numarası
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: "error",
                            title: "Telefon Numarası Var!",
                            showConfirmButton: false,
                            timer: 3000
                        });
                    } else {
                        Swal.fire({
                            icon: "success",
                            title: "Telefon Numarası Bulunamadı!",
                            showConfirmButton: false,
                            timer: 3000
                        });
                    }
                },
                error: function(xhr, status, error) {
                    Swal.fire({
                        icon: "error",
                        title: "Bir hata oluştu. Lütfen tekrar deneyin.",
                        showConfirmButton: true
                    });
                }
            });
        }



        document.getElementById('mainForm').addEventListener('submit', function(e) {
            // Select2 ile seçilen değeri al
            let selectedBranch = $('select[name="branch_id"]').val();

            // Eğer seçilen değer boş, "Bayi seçiniz" ya da 0 ise uyarı ver
            if (selectedBranch === '' || selectedBranch === 'Bayi seçiniz' || selectedBranch === '0') {
                e.preventDefault(); // Formun gönderilmesini engelle
                Swal.fire({
                    icon: 'error',
                    title: 'Hata',
                    text: 'Lütfen geçerli bir bayi seçiniz!',
                });
            }
        });
    </script>
@endpush
