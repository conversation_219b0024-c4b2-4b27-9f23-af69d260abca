@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> <a href="{{ url()->previous() }}" class="btn btn-danger btn-sm">Geri <PERSON>ö<PERSON></a> @endpush
@section('title','Personel Düzenle')
@section('content')
    <button type="button" class="btn btn-sm btn-danger" onclick="ExportPdf()"><i class="bi bi-printer"></i> Yazdır</button>
    <form id="mainForm" method="post" action="{{ route('users.update',$user) }}" enctype="multipart/form-data">@csrf @method('put')
        <input type="hidden" name="return_url" value="{{ url()->previous() }}">
        <div id="divRaporEkrani" class="row">
            <div class="col-md-12">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="kisiselBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#kisiselBilgiler" aria-expanded="true"
                                    aria-controls="kisiselBilgiler">
                                Kişisel Bilgiler
                            </button>
                        </h2>
                        <div id="kisiselBilgiler" class="accordion-collapse collapse show" aria-labelledby="kisiselBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-9">
                                        <div class="row">
                                            <div class="col-md-4 mt-3">
                                                <label>Ad<span class="text-danger">*</span></label>
                                                <input class="form-control form-control-sm" name="name" value="{{ $user->name }}" @if($authUser->type == 'admin') required @else disabled @endif >
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>İkinci Ad</label>
                                                <input class="form-control form-control-sm" name="second_name" @if($authUser->type != 'admin') disabled @endif value="{{ $user->second_name }}">
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Soyad<span class="text-danger">*</span></label>
                                                <input class="form-control form-control-sm" name="surname" value="{{ $user->surname }}" @if($authUser->type == 'admin') required @else disabled @endif>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>T.C. Kimlik No</label>
                                                <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="tc" value="{{ $user->tc }}">
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Doğum Tarihi</label>
                                                <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="dogum_tarihi" type="date" value="{{ $user->dogum_tarihi ?? date('Y-m-d') }}">
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Cinsiyet</label>
                                                <select class="select2" @if($authUser->type != 'admin') disabled @endif name="gender">
                                                    <option @if($user->gender == 'male') selected @endif value="male">Erkek</option>
                                                    <option @if($user->gender == 'female') selected @endif value="female">Kadın</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>E-posta Adresi<span class="text-danger">*</span></label>
                                                <input class="form-control form-control-sm" name="email" type="email" value="{{ $user->email }}" @if($authUser->type == 'admin') @else disabled @endif>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Alt Beden</label>
                                                <select name="alt_beden" @if($authUser->type == 'admin' ||  $authUser->user_role_group_id == 37  || $authUser->user_role_group_id==47 || $authUser->user_role_group_id==40 || $authUser->user_role_group_id==31)  @else disabled @endif id="" class="form-control form-control-sm">
                                                    <option>Seçiniz</option>
                                                    <!-- @for($i = 28; $i <= 60; $i +=2)
                                                        <option value="{{ $i }}" @selected(old('alt_beden', $user->alt_beden) == $i)>{{ $i }}</option>
                                                    @endfor -->
                                                    <option value="XS" @selected(old('alt_beden', $user->alt_beden) == 'XS')>XS</option>
                                                    <option value="S" @selected(old('alt_beden', $user->alt_beden) == 'S')>S</option>
                                                    <option value="M" @selected(old('alt_beden', $user->alt_beden) == 'M')>M</option>
                                                    <option value="L" @selected(old('alt_beden', $user->alt_beden) == 'L')>L</option>
                                                    <option value="XL" @selected(old('alt_beden', $user->alt_beden) == 'XL')>XL</option>
                                                    <option value="XXL" @selected(old('alt_beden', $user->alt_beden) == 'XXL')>XXL</option>
                                                    <option value="XXXL" @selected(old('alt_beden', $user->alt_beden) == 'XXXL')>XXXL</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Üst Beden</label>

                                                <select name="ust_beden" @if($authUser->type == 'admin' ||  $authUser->user_role_group_id == 37  || $authUser->user_role_group_id ==47 || $authUser->user_role_group_id==40 || $authUser->user_role_group_id==31)  @else disabled @endif id="" class="form-control form-control-sm">
                                                    <option>Seçiniz</option>
                                                    <option value="XS" @selected(old('ust_beden', $user->ust_beden) == 'XS')>XS</option>
                                                    <option value="S" @selected(old('ust_beden', $user->ust_beden) == 'S')>S</option>
                                                    <option value="M" @selected(old('ust_beden', $user->ust_beden) == 'M')>M</option>
                                                    <option value="L" @selected(old('ust_beden', $user->ust_beden) == 'L')>L</option>
                                                    <option value="XL" @selected(old('ust_beden', $user->ust_beden) == 'XL')>XL</option>
                                                    <option value="XXL" @selected(old('ust_beden', $user->ust_beden) == 'XXL')>XXL</option>
                                                    <option value="XXXL" @selected(old('ust_beden', $user->ust_beden) == 'XXXL')>XXXL</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label>Ayakkabı Numarası</label>
                                                <select name="ayakkabi_numarasi" @if($authUser->type == 'admin' ||  $authUser->user_role_group_id == 37  || $authUser->user_role_group_id==47 || $authUser->user_role_group_id==40 || $authUser->user_role_group_id==31)  @else disabled @endif id="" class="form-control form-control-sm">
                                                    <option>Seçiniz</option>
                                                    @for($i = 30; $i <= 50; $i++)
                                                        <option value="{{ $i }}" @selected(old('ayakkabi_numarasi', $user->ayakkabi_numarasi) == $i)>{{ $i }}</option>
                                                    @endfor
                                                </select>
                                            </div>
                                            <div class="col-md-4 mt-3">
                                                <label for="blood_group">Kan Grubu:</label>
                                                <select class="form-control select2" @if($authUser->type != 'admin') disabled @endif name="blood_group" id="blood_group">
                                                    <option value="A+">A+</option>
                                                    <option @if($user->blood_group == "A-") selected @endif value="A-">A-</option>
                                                    <option @if($user->blood_group == "B+") selected @endif value="B+">B+</option>
                                                    <option @if($user->blood_group == "B-") selected @endif value="B-">B-</option>
                                                    <option @if($user->blood_group == "AB+") selected @endif value="AB+">AB+</option>
                                                    <option @if($user->blood_group == "AB-") selected @endif value="AB-">AB-</option>
                                                    <option @if($user->blood_group == "O+") selected @endif value="O+">O+</option>
                                                    <option @if($user->blood_group == "O-") selected @endif value="O-">O-</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">Fotoğraf</label>
                                        <input type="file" class="dropify" data-height="250" @if($authUser->type != 'admin') disabled @endif data-default-file="/storage/{{ $user->image }}" name="image"/>
                                    </div>

                                    <div class="col-md-4 mt-3">
                                        <label>Şifre (Değiştirmeyecekseniz Boş Bırakın)</label>
                                        <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="password" type="password">
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Şifre Tekrar (Değiştirmeyecekseniz Boş Bırakın)</label>
                                        <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="password_again" type="password">
                                    </div>
                                    <div class="col-md-4 mt-3 telefon">
                                        <label>Telefon Numarası (Sistem Giriş)<span class="text-danger add-phone" style="cursor: pointer">Ekle</span> </label>
                                        <input class="form-control mask-tel form-control-sm" @if($authUser->type != 'admin') disabled @endif name="telephone_login" value="{{ $user->telephone }}">
                                    </div>
                                    @forelse($user->getPhones as $index => $userPhone)
                                        <div class="col-md-6 mt-3">
                                            <label>Telefon Başlık</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="telephone_title[]" value="{{ $userPhone->title }}">
                                        </div>
                                        <div class="col-md-4 mt-3">
                                            <label>Telefon Numarası</label>
                                            <input class="form-control form-control-sm"  @if($authUser->type != 'admin') disabled @endif name="telephone[]" value="{{ $userPhone->telephone }}">
                                        </div>
                                    @empty
                                        <div class="col-md-6 mt-3 d-none">
                                            <label>Telefon Başlık</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="telephone_title[]">
                                        </div>
                                        <div class="col-md-4 mt-3 telefon">
                                            <label>Telefon Numarası <span class="text-danger add-phone" style="cursor: pointer">Ekle</span></label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="telephone[]">
                                        </div>
                                    @endforelse
                                    <div class="col-md-12 mt-3">
                                        <label>Not</label>
                                        <textarea class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="comment">{{ $user->comment }}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="adresHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#adres" aria-expanded="true"
                                    aria-controls="adres">
                                Adres Bilgileri
                            </button>
                        </h2>
                        <div id="adres" class="accordion-collapse collapse show" aria-labelledby="adresHeading">
                            <div class="accordion-body">
                                <button type="button" class="btn btn-sm btn-danger my-3 add-address">Kayıt Ekle</button>
                                <div class="row addresses">
                                    @forelse($user->getAddresses as $userAddress)
                                        <div class="col-md-3">
                                            <label>Başlık</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="address_title[]" value="{{ $userAddress->title }}">
                                        </div>
                                        <div class="col-md-3">
                                            <label>Şehir</label>
                                            <select name="city[]" @if($authUser->type != 'admin') disabled @endif class="form-control select2">
                                                @foreach(\App\Models\City::all() as $city)
                                                    <option @if($userAddress->city_id == $city->id) selected @endif value="{{ $city->id }}">{{ $city->title }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label>Adres</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif value="{{ $userAddress->address }}" name="address[]">
                                        </div>
                                    @empty
                                        <div class="col-md-3">
                                            <label>Başlık</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="address_title[]">
                                        </div>
                                        <div class="col-md-3">
                                            <label>Şehir</label>
                                            <select name="city[]" @if($authUser->type != 'admin') disabled @endif class="form-control select2">
                                                @foreach(\App\Models\City::all() as $city)
                                                    <option value="{{ $city->id }}">{{ $city->title }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label>Adres</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="address[]">
                                        </div>
                                    @endforelse
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="bolumHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#bolum" aria-expanded="true"
                                    aria-controls="bolum">
                                Çalışma Bilgileri
                            </button>
                        </h2>
                        <div id="bolum" class="accordion-collapse collapse show" aria-labelledby="bolumHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-md-4 mt-3">
                                        <label>Kullanıcı Rolü</label>
                                        <select name="user_role_group_id" @if($authUser->type != 'admin') disabled @endif class="select2">
                                            @foreach($userRoleGroups->where('id','!=',30) as $userRoleGroup)
                                                <option @if($user->user_role_group_id == $userRoleGroup->id) selected @endif value="{{ $userRoleGroup->id }}">{{ $userRoleGroup->name }}</option>
                                            @endforeach
                                            @if($authUser->isAdmin())
                                                <option @if($user->user_role_group_id == '30') selected @endif value="30">{{ $userRoleGroups->where('id', '=', 30)->first()->name }}</option>
                                            @endif
                                        </select>
                                    </div>
                                    @if($authUser->type == 'admin')
{{--                                        <div class="col-md-4 mt-3">--}}
{{--                                            <label>Bölge</label>--}}
{{--                                            <select name="zone_id" @if($authUser->type != 'admin') disabled @endif class="select2">--}}
{{--                                                <option value="0">Yok</option>--}}
{{--                                                @foreach($zones as $zone)--}}
{{--                                                    <option @if($user->zone_id == $zone->id) selected @endif value="{{ $zone->id }}">{{ $zone->name }}</option>--}}
{{--                                                @endforeach--}}
{{--                                            </select>--}}
{{--                                        </div>--}}
                                        <div class="col-md-4 mt-3">
                                            <label>Bayi</label>
                                            <select class="select2 user-branches" @if($authUser->type != 'admin') disabled @endif name="branch_id[]" @if(in_array($user->user_role_group_id,[31,44,45,47])) multiple @endif>
                                                @foreach($branches as $branch)
                                                    <option @if($user->type != 'admin' && in_array($branch->id,$userBranchIds)) selected @endif value="{{ $branch->id }}">{{ $branch->unvan }}</option>
                                                @endforeach
                                                @if($user->type == 'admin')
                                                    <option selected value="0">Tümü</option>
                                                @endif
                                            </select>
                                        </div>
                                    @endif

                                    <div class="col-md-4 mt-3">
                                        <label>Departman</label>
                                        <select name="department" @if($authUser->type != 'admin') disabled @endif class="select2">
                                            @foreach(__('arrays.departments') as $departmentKey => $departmentName)
                                                <option @if($user->department == $departmentKey) selected @endif value="{{ $departmentKey }}">{{ $departmentName }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Şirket Telefonu</label>
                                        <select class="select2" @if($authUser->type != 'admin') disabled @endif name="has_phone">
                                            <option @if($user->has_phone == 1) selected @endif value="1">Var</option>
                                            <option @if($user->has_phone == 0) selected @endif value="0">Yok</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Şirket Bilgisayarı</label>
                                        <select class="select2" @if($authUser->type != 'admin') disabled @endif name="has_computer">
                                            <option @if($user->has_computer == 1) selected @endif value="1">Var</option>
                                            <option @if($user->has_computer == 0) selected @endif value="0">Yok</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Şirket Aracı</label>
                                        <select class="select2" @if($authUser->type != 'admin') disabled @endif name="has_car">
                                            <option @if($user->has_car == 1) selected @endif value="1">Var</option>
                                            <option @if($user->has_car == 0) selected @endif value="0">Yok</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Şirket Belgeleri</label>
                                        <select class="select2" @if($authUser->type != 'admin') disabled @endif name="has_document">
                                            <option @if($user->has_document == 1) selected @endif value="1">Var</option>
                                            <option @if($user->has_document == 0) selected @endif value="0">Yok</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Yıllık Çalışma Süresi</label>
                                        <input class="form-control form-control-sm" name="yearly_work_time" @if($authUser->type != 'admin') disabled @endif type="number" value="{{ $user->yearly_work_time }}">
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>İşe Başlama Tarihi</label>
                                        <input class="form-control form-control-sm" name="work_start_date" @if($authUser->type != 'admin') disabled @endif type="date" value="{{ $user->work_start_date }}">
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Kalan Tatil Hakkı (Gün)</label>
                                        <input class="form-control form-control-sm" name="remain_holiday_date_count" @if($authUser->type != 'admin') disabled @endif type="number" value="{{ $user->remain_holiday_date_count }}">
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Oturum Süresi (Saniye)</label>
                                        <input class="form-control form-control-sm" name="session_lifetime" @if($authUser->type != 'admin') disabled @endif value="{{ $user->session_lifetime }}" type="number">
                                    </div>
                                    <div class="col-md-4 mt-3">
                                        <label>Durum</label>
                                        <select class="select2" @if($authUser->type != 'admin') disabled @endif name="status">
                                            <option @if($user->status == 1) selected @endif value="1">Aktif</option>
                                            <option @if($user->status == 0) selected @endif value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="documentsHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#documents" aria-expanded="true"
                                    aria-controls="documents">
                                Evraklar
                            </button>
                        </h2>
                        <div id="documents" class="accordion-collapse collapse show" aria-labelledby="documentsHeading">
                            <div class="accordion-body">
                                <div class="row">

                                    <div class="col-xl-3">
                                        <label class="form-label">Kimlik Fotokopisi</label>
                                        <input type="file" class="dropify" @if($authUser->type != 'admin') disabled @endif data-height="250" data-default-file="/storage/{{ $user->id_document }}" name="id_document"/>
                                    </div>
                                    <div class="col-xl-3">
                                        <label class="form-label">Adres Bilgileri</label>
                                        <input type="file" class="dropify" @if($authUser->type != 'admin') disabled @endif data-height="250" data-default-file="/storage/{{ $user->address_document }}" name="address_document"/>
                                    </div>
                                    <div class="col-xl-3">
                                        <label class="form-label">Sözleşme Bilgileri</label>
                                        <input type="file" class="dropify" @if($authUser->type != 'admin') disabled @endif data-height="250" data-default-file="/storage/{{ $user->agreement_document }}" name="agreement_document"/>
                                    </div>
                                    <div class="col-xl-3">
                                        <label class="form-label">Evlilik Bilgileri</label>
                                        <input type="file" class="dropify" @if($authUser->type != 'admin') disabled @endif data-height="250" data-default-file="/storage/{{ $user->marriage_document }}" name="marriage_document"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="emergencyHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#emergency" aria-expanded="true"
                                    aria-controls="emergency">
                                Kişi Yakınları
                            </button>
                        </h2>
                        <div id="emergency" class="accordion-collapse collapse show" aria-labelledby="emergencyHeading">
                            <div class="accordion-body">
                                <button type="button" class="btn btn-sm btn-danger my-3 add-emergency">Kayıt Ekle</button>
                                <div class="row emergencies">
                                    @forelse($user->getEmergencies as $userEmergency)
                                        <div class="col-md-3 mt-3">
                                            <label>Yakınlık</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif value="{{ $userEmergency->title }}" name="emergency_title[]">
                                        </div>
                                        <div class="col-md-3 mt-3">
                                            <label>Yakın Adı</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif value="{{ $userEmergency->name }}" name="emergency_name[]">
                                        </div>
                                        <div class="col-md-3 mt-3">
                                            <label>Yakın Telefon</label>
                                            <input class="form-control tel-mask form-control-sm" @if($authUser->type != 'admin') disabled @endif value="{{ $userEmergency->telephone }}" name="emergency_telephone[]">
                                        </div>
                                        <div class="col-md-3 mt-3">
                                            <label>Yakın Adres</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif value="{{ $userEmergency->address }}" name="emergency_address[]">
                                        </div>
                                    @empty
                                        <div class="col-md-3 mt-3">
                                            <label>Yakınlık</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="emergency_title[]">
                                        </div>
                                        <div class="col-md-3 mt-3">
                                            <label>Yakın Adı</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="emergency_name[]">
                                        </div>
                                        <div class="col-md-3 mt-3">
                                            <label>Yakın Telefon</label>
                                            <input class="form-control tel-mask form-control-sm"  @if($authUser->type != 'admin') disabled @endif name="emergency_telephone[]">
                                        </div>
                                        <div class="col-md-3 mt-3">
                                            <label>Yakın Adres</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="emergency_address[]">
                                        </div>
                                    @endforelse

                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="bankaHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#banka" aria-expanded="true"
                                    aria-controls="banka">
                                Banka Bilgileri
                            </button>
                        </h2>
                        <div id="banka" class="accordion-collapse collapse show" aria-labelledby="bankaHeading">
                            <div class="accordion-body">
                                <div class="row bankalar">
                                    @foreach($user->getBanks as $bank)
                                        <div class="col-md-6 mt-3">
                                            <label>Banka Adı</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif value="{{ $bank->banka_adi }}" name="banka_adi[]">
                                        </div>
                                        <div class="col-md-6 mt-3">
                                            <label>IBAN</label>
                                            <input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif value="{{ $bank->iban }}" name="iban[]">
                                        </div>
                                    @endforeach
                                </div>
                                <button type="button" class="btn btn-sm btn-success banka-ekle">Banka Ekle</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush
@push('js')

    <!-- Select2 Cdn -->
    <script src="/assets/js/select2.min.js"></script>
    <script>
        (function (){
            "use strict"
            $(".select2").select2({
                placeholder: "Ara.."
            });
        })();
    </script>
    <script src="/assets/js/dropify.min.js"></script>
    <script>
        $('.dropify').dropify({
            messages: {
                'default': 'Dosya Sürükle veya Tıkla',
                'replace': 'Dosya Sürükle veya Tıkla',
                'remove':  'Kaldır',
                'error':   'Bir Hata Ortaya Çıktı'
            }
        });

        $('.banka-ekle').on('click',function (){
            let html = '<div class="col-md-6 mt-3"><label>Banka Adı</label><input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="banka_adi[]"></div>'
            html += '<div class="col-md-6 mt-3"><label>IBAN</label><input class="form-control form-control-sm" @if($authUser->type != 'admin') disabled @endif name="iban[]"></div>'
            $('.bankalar').append(html)
        })


        addEventListener("DOMContentLoaded", (event) => {
            $('.add-phone').on('click',function (){
                $('.telefon').after("<div class=\"col-md-6 mt-3\"><label>Telefon Başlık</label> <input class=\"form-control\" @if($authUser->type != 'admin') disabled @endif name=\"telephone_title[]\"> </div><div class=\"col-md-4 mt-3\"><label>Telefon Numarası</label> <input class=\"form-control\" name=\"telephone[]\"> </div>")
                document.querySelectorAll('input[name="telephone[]"]').forEach(function (item,index){
                    const maskTel2 = IMask(item, {
                        mask: '(*************'
                    });
                })
            })
        });

        $('.add-emergency').on('click',function (){
            let html = '<div class="col-md-3 mt-3"><label>Yakınlık</label><input @if($authUser->type != 'admin') disabled @endif class="form-control form-control-sm" name="emergency_title[]"></div>'
            html += '<div class="col-md-3 mt-3"><label>Yakın Adı</label><input @if($authUser->type != 'admin') disabled @endif class="form-control form-control-sm" name="emergency_name[]"></div>'
            html += '<div class="col-md-3 mt-3"><label>Yakın Telefon</label><input @if($authUser->type != 'admin') disabled @endif class="form-control tel-mask form-control-sm" name="emergency_telephone[]"></div>'
            html += '<div class="col-md-3 mt-3"><label>Yakın Adres</label><input @if($authUser->type != 'admin') disabled @endif class="form-control form-control-sm" name="emergency_address[]"></div>'

            $('.emergencies').append(html)
        })

        $('.add-address').on('click',function (){
            let html = '<div class="col-md-3"><label>Başlık</label><input @if($authUser->type != 'admin') disabled @endif class="form-control form-control-sm" name="address_title[]"></div>'
            html += '<div class="col-md-3"><label>Şehir</label><select @if($authUser->type != 'admin') disabled @endif name="city[]" class="form-control select2">@foreach(\App\Models\City::all() as $city)<option value="{{ $city->id }}">{{ $city->title }}</option>@endforeach</select></div>'
            html += '<div class="col-md-6"><label>Adres</label><input @if($authUser->type != 'admin') disabled @endif class="form-control form-control-sm" name="address[]"></div>'
            $('.addresses').append(html)
            $(".select2").select2({
                placeholder: "Ara.."
            });
        })
    </script>
    <script src="/assets/js/html2pdf.bundle.js"></script>
    <script>
        function ExportPdf() {
            // Get the element.
            var element = document.getElementById('divRaporEkrani');
            // Generate the PDF.
            html2pdf().from(element).set({
                margin: 1,
                filename: 'kullanici_{{ $user->id }}.pdf',
                html2canvas: { scale: 2,
                    onclone: function (documentClone) {
                        // Buttonları gizle
                        documentClone.querySelectorAll('button').forEach(function(button){
                            button.style.display = 'none';
                        });

                    }
                },
                jsPDF: { orientation: 'portrait', unit: 'cm', format: 'a4', compressPDF: true }
            }).save();
        }

        $('select[name="user_role_group_id"]').on('change', function () {
            let $val = $(this).val();

            $('.user-branches').val(null).removeAttr('multiple');

            $('.user-branches option[value="0"]').remove();

            if ($val == 30) {
                $('.user-branches').append("<option value='0'>Tümü</option>").val(0);
            } else if ($val == 31 || $val == 44 || $val == 45 || $val == 47) {
                $('.user-branches').attr('multiple', 'true');
            }

            $('.user-branches').select2();
        });
    </script>
@endpush
