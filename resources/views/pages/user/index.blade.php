@extends('pages.build')
@section('title','Personeller')
@push('css')
   <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
   <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('excel')
    @if(in_array('*', $userRoles) || in_array('download_excel_user', $userRoles))
        <a href="javascript:void(0);" class="btn btn-sm btn-success export-btn">Excel Olarak İndir <i class="fa fa-file-excel"></i> </a>
    @endif
    @if(in_array('*', $userRoles) || in_array('upload_excel_user', $userRoles))
        <a href="{{ route('importExcel',['type'=>'user']) }}" class="btn btn-sm btn-danger">Excelden Aktar <i class="fa fa-file-excel"></i> </a>
    @endif


@endsection
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form id="getSavedFilter" method="get" action="{{ route('users.index') }}"></form>
                <form method="get" action="{{ route('users.index') }}">
                    <div class="card-body">
                        <div class="row">

                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Ad</label>
                                    <input type="text" class="form-control form-control-sm" name="name" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->user_name : ($_GET['name'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>İkinci Ad</label>
                                    <input type="text" class="form-control form-control-sm" name="second_name" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->second_name : ($_GET['second_name'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Soyad</label>
                                    <input type="text" class="form-control form-control-sm" name="surname" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->surname : ($_GET['surname'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>E-posta Adresi</label>
                                    <input type="text" class="form-control form-control-sm" name="email" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->email : ($_GET['email'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>T.C. Kimlik No</label>
                                    <input type="text" class="form-control form-control-sm" name="tc" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->tc : ($_GET['tc'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Telefon Numarası</label>
                                    <input type="text" class="form-control form-control-sm" name="telephone" value="{{ $selectedSavedFilter != null ? $selectedSavedFilter->telephone : ($_GET['telephone'] ?? '') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Durum</label>
                                    <select class="form-control form-control-sm" name="status">
                                        <option value="">Hepsi</option>
                                        <option @if(isset($_GET['status']) && $_GET['status'] == '1') selected @endif value="1">Aktif</option>
                                        <option @if(isset($_GET['status']) && $_GET['status'] == '0') selected @endif value="0">Pasif</option>
                                        @if($authUser->type == 'admin')
                                            <option @if(isset($_GET['status']) && $_GET['status'] == '-1') selected @endif value="-1">Silinmiş</option>
                                        @endif
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Cinsiyet</label>
                                    <select class="form-control form-control-sm" name="gender">
                                        <option value="">Seçiniz</option>
                                        <option @if(isset($_GET['gender']) && $_GET['gender'] == 'male') selected @endif value="male">Erkek</option>
                                        <option @if(isset($_GET['gender']) && $_GET['gender'] == 'female') selected @endif value="female">Kadın</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <label>Alt Beden</label>
                                <select name="alt_beden" class="form-control form-control-sm">
                                  <option value="">Seçiniz</option>
                                    @for($i = 28; $i <= 60; $i +=2)
                                        <option @if(isset($_GET['alt_beden']) && $_GET['alt_beden'] == $i) selected @endif value="{{ $i }}">{{ $i }}</option>
                                    @endfor
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label>Ayakkabı Numarası</label>
                                <select name="ayakkabi_numarasi" id="" class="form-control form-control-sm">
                                  <option value="">Seçiniz</option>
                                    @for($i = 30; $i <= 50; $i++)
                                        <option value="{{ $i }}" @if(isset($_GET['ayakkabi_numarasi']) && $_GET['ayakkabi_numarasi'] == $i) selected @endif>{{ $i }}</option>
                                    @endfor
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="blood_group">Kan Grubu:</label>
                                <select class="form-control form-control-sm" name="blood_group" id="blood_group">
                                  <option value="">Seçiniz</option>
                                    <option @if(isset($_GET['blood_group']) && $_GET['blood_group'] == "A+") selected @endif value="A+">A+</option>
                                    <option @if(isset($_GET['blood_group']) && $_GET['blood_group'] == "A-") selected @endif value="A-">A-</option>
                                    <option @if(isset($_GET['blood_group']) && $_GET['blood_group'] == "B+") selected @endif value="B+">B+</option>
                                    <option @if(isset($_GET['blood_group']) && $_GET['blood_group'] == "B-") selected @endif value="B-">B-</option>
                                    <option @if(isset($_GET['blood_group']) && $_GET['blood_group'] == "AB+") selected @endif value="AB+">AB+</option>
                                    <option @if(isset($_GET['blood_group']) && $_GET['blood_group'] == "AB-") selected @endif value="AB-">AB-</option>
                                    <option @if(isset($_GET['blood_group']) && $_GET['blood_group'] == "O+") selected @endif value="O+">O+</option>
                                    <option @if(isset($_GET['blood_group']) && $_GET['blood_group'] == "O-") selected @endif value="O-">O-</option>
                                </select>
                            </div>

                            <div class="col-md-2">
                                <label>Görev</label>
                                <select name="user_role_group_id" class="form-control form-control-sm">
                                  <option value="">Seçiniz</option>
                                    @foreach($userRoleGroups as $userRoleGroup)
                                        <option @if(($selectedSavedFilter != null && $selectedSavedFilter->user_role_group_id == $userRoleGroup->id )|| (isset($_GET['user_role_group_id']) && $_GET['user_role_group_id'] == $userRoleGroup->id)) selected @endif value="{{ $userRoleGroup->id }}">{{ $userRoleGroup->name }}</option>
                                    @endforeach
                                </select>
                            </div>

                            @if(count($authUser->getBranchIds()) > 1)
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Bayi</label>
                                        <select class="form-control form-control-sm select2" name="branch_id">
                                            @if($authUser->type == 'admin')
                                                <option value="0">Tüm Bayiler</option>
                                            @endif
                                            @if($authUser->type == 'admin')
                                                @foreach(\App\Models\Branch::where('status',1)->get() as $filterBranch)
                                                    <option @if(($selectedSavedFilter != null && $selectedSavedFilter->branch_id == $filterBranch->id )|| (isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id)) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                                @endforeach
                                            @else
                                                @foreach(\App\Models\Branch::where('status',1)->whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                                    <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                                @endforeach
                                            @endif
                                        </select>
                                    </div>
                                </div>
                                @if($authUser->type == 'admin')
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Bölge</label>
                                        <select class="form-control form-control-sm select2" name="zone_id">
                                            <option value="0">Tüm Bölgeler</option>
                                            @foreach(\App\Models\Zone::where('status',1)->get() as $filterZone)
                                                <option @if(($selectedSavedFilter != null && $selectedSavedFilter->zone_id == $filterZone->id )|| (isset($_GET['zone_id']) && $_GET['zone_id'] == $filterZone->id)) selected @endif value="{{ $filterZone->id }}">{{ $filterZone->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                @endif
                            @endif
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label><small>Kayıtlı Aramalar</small></label>
                                    <select form="getSavedFilter" onchange="$('#getSavedFilter').submit()" class="form-control form-control-sm select2" name="saved_filter_id">
                                        <option value="0">Seçilmedi</option>
                                        @foreach($savedFilters as $savedFilter)
                                            <option @if(isset($_GET['saved_filter_id']) && $_GET['saved_filter_id'] == $savedFilter->id) selected @endif value="{{ $savedFilter->id }}">{{ $savedFilter->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col mt-4">
                                <button class="btn btn-sm mt-1 btn-success">Listele</button>
                                <button type="button" class="btn btn-sm btn-danger mt-1 save-filter-btn" >Aramayı Kaydet</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    @if(in_array('*', $userRoles) || in_array('add_user', $userRoles))
                        <a href="{{ route('users.create') }}" class="nav-link">Kayıt Ekle</a>
                    @endif
                    @if(in_array('*', $userRoles) || in_array('list_branch_category', $userRoles))
                        <a href="javascript:void(0);" class="nav-link norm-analyze-button">Norm Kadro Kontrol</a>
                    @endif
                </nav>
                <div class="card-body">

                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr><th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th>Ad Soyad</th>
                            <th>Bayi</th>
                            <th>Bayi Kategori</th>
                            <th>Bölge</th>
                            <th>Departman</th>
                            <th>Görev</th>
                            <th>E-posta</th>
                            <th>T.C. Kimlik No</th>
                            <th>Doğum Tarihi</th>
                            <th>Cinsiyet</th>
                            <th>Telefon</th>
                            <th>İkinici Telefon</th>
                            <th>İşe Başlama Tarihi</th>
                            <th>Durum</th>
                            <th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="saveFilter" tabindex="-1"
         aria-labelledby="saveFilter" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Filtre Şablonu Oluştur
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Şablon Adı</small></label>
                            <input type="text" class="form-control" name="saved_filter_name">
                        </div>
                        <button type="button" class="btn btn-sm btn-danger save-filter">Kaydet</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="normAnalyze"
         aria-labelledby="normAnalyze" data-bs-keyboard="false"
         aria-hidden="true">
        <!-- Scrollable modal -->
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title">Bayi Norm Kadro Analizi</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="col">
                        <div class="form-group">
                            <label><small>Bayi</small></label>
                            <select class="form-control form-control-sm select2" name="norm_branch_id">
                                <option value="0">Bayi Seçiniz</option>
                                @if($authUser->type == 'admin')
                                    @foreach(\App\Models\Branch::where('status',1)->get() as $filterBranch)
                                        <option value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                    @endforeach
                                @else
                                    @foreach(\App\Models\Branch::where('status',1)->whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                        <option value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th>Personel Görevleri</th>
                                    <th>Personel Sayısı</th>
                                    <th>Kategori Personel Sayısı</th>
                                    <th>Yeterli Personel</th>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                        <button type="button" class="btn btn-secondary mt-2" data-bs-dismiss="modal">Kapat</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
       <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>
        (function (){
            "use strict"
            $(".select2").select2({
                placeholder: "Ara.."
            });

            $("#normAnalyze .select2").select2({
                placeholder: "Ara..",
                dropdownParent: "#normAnalyze"
            });
        })();


        $(function() {
            'use strict';

            var responsiveDatatable = $('#responsiveDataTable').DataTable({
                "dom": '<"pull-left"f><"pull-right"l>rtip',
                "scrollX": true,
                "ordering": true,
                "aaSorting": [],
                "pageLength": 10,
                "processing": true,
                "serverSide": true,
                "searching": true,
                "ajax": {
                    "url": "{{ route('getUsersForAjax') }}",
                    "dataType": "json",
                    "type": "POST",
                    "data": function(d) {
                        d._token = "{{ csrf_token() }}";

                        @if (!empty($_GET['name']))
                            d.name = "{{ $_GET['name'] }}";
                        @endif

                        @if (!empty($_GET['second_name']))
                            d.second_name = "{{ $_GET['second_name'] }}";
                        @endif

                        @if (!empty($_GET['surname']))
                            d.surname = "{{ $_GET['surname'] }}";
                        @endif

                        @if (!empty($_GET['tc']))
                            d.tc = "{{ $_GET['tc'] }}";
                        @endif

                        @if (!empty($_GET['alt_beden']))
                            d.alt_beden = "{{ $_GET['alt_beden'] }}";
                        @endif

                        @if (!empty($_GET['ayakkabi_numarasi']))
                            d.ayakkabi_numarasi = "{{ $_GET['ayakkabi_numarasi'] }}";
                        @endif

                        @if (!empty($_GET['blood_group']))
                            d.blood_group = "{{ $_GET['blood_group'] }}";
                        @endif

                        @if (!empty($_GET['zone_id']))
                            d.zone_id = "{{ $_GET['zone_id'] }}";
                        @endif

                        @if (!empty($_GET['saved_filter_id']))
                            d.saved_filter_id = "{{ $_GET['saved_filter_id'] }}";
                        @endif

                        @if (!empty($_GET['email']))
                            d.eposta = "{{ $_GET['email'] }}";
                        @endif

                        @if (!empty($_GET['gender']))
                            d.gender = "{{ $_GET['gender'] }}";
                        @endif

                        @if (!empty($_GET['telephone']))
                            d.telephone = "{{ $_GET['telephone'] }}";
                        @endif

                        @if (!empty($_GET['branch_id']))
                            d.branch_id = "{{ $_GET['branch_id'] }}";
                        @endif

                        @if (!empty($_GET['status']) || (isset($_GET['status']) && $_GET['status'] == '0'))
                            d.status = "{{ $_GET['status'] }}";
                        @endif

                        @if (!empty($_GET['user_role_group_id']))
                            d.user_role_group_id = "{{ $_GET['user_role_group_id'] }}";
                        @endif

                        @if (!empty($_GET['start_date']))
                            d.start_date = "{{ $_GET['start_date'] }}";
                        @endif

                        @if (!empty($_GET['end_date']))
                            d.end_date = "{{ $_GET['end_date'] }}";
                        @endif
                    },
                    "dataSrc": "data"
                },
                "columns": [{
                        "data": "id",
                        "defaultContent": ""
                    },
                    {
                        "data": "name"
                    },
                    {
                        "data": "branch_name"
                    },
                    {
                        "data": "branch_category"
                    },
                    {
                        "data": "zone"
                    },
                    {
                        "data": "department"
                    },
                    {
                        "data": "position"
                    },
                    {
                        "data": "email"
                    },
                    {
                        "data": "tc"
                    },
                    {
                        "data": "dogum_tarihi"
                    },
                    {
                        "data": "gender"
                    },
                    {
                        "data": "telephone"
                    },
                    {
                        "data": "second_telephone"
                    },
                    {
                        "data": "work_start_date"
                    },
                    {
                        "data": "status"
                    },
                    {
                        "data": "actions"
                    }
                ],
                "language": {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı! @if (in_array('*', $userRoles) || in_array('add_user', $userRoles)) <a href='{{ route('users.create', ['name' => $_GET['name'] ?? '', 'second_name' => $_GET['second_name'] ?? '', 'surname' => $_GET['surname'] ?? '', 'telephone' => $_GET['telephone'] ?? '', 'tc' => $_GET['tc'] ?? '', 'email' => $_GET['email'] ?? '']) }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı! @if (in_array('*', $userRoles) || in_array('add_user', $userRoles)) <a href='{{ route('users.create', ['name' => $_GET['name'] ?? '', 'second_name' => $_GET['second_name'] ?? '', 'surname' => $_GET['surname'] ?? '', 'telephone' => $_GET['telephone'] ?? '', 'tc' => $_GET['tc'] ?? '', 'email' => $_GET['email'] ?? '']) }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ","
                },
                "createdRow": function(row, data, dataIndex) { // Handle Css if the user status pasif

                    if (data.status !== 'Aktif'){
                        $('td', row).eq(1).css({
                            'background-color': 'orangered',
                            'color': '#fff'
                        });
                    }
                }
            });
            $(document).on('click', '.deleteUser', function() {
                let userId = $(this).data('id');

                Swal.fire({
                    title: "Emin misiniz?",
                    text: "Bu kullanıcıyı silmek istiyor musunuz?",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonColor: "#d33",
                    cancelButtonColor: "#6c757d",
                    confirmButtonText: "Evet, Sil!",
                    cancelButtonText: "Hayır, Silme!"
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: '/users/' + userId,
                            type: 'POST',
                            data: {
                                _method: 'DELETE',
                                '_token': '{{ csrf_token() }}'
                            }, // Dont forget the token
                            headers: {
                                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                            },
                            success: function(response) {
                                Swal.fire({
                                    title: "Silindi!",
                                    text: "Kullanıcı başarıyla silindi.",
                                    icon: "success",
                                    timer: 2000,
                                    showConfirmButton: false
                                });

                                $('#responsiveDataTable').DataTable().ajax.reload(null, false);

                            },
                            error: function(xhr) {
                                Swal.fire({
                                    title: "Hata!",
                                    text: "Kullanıcı silinemedi.",
                                    icon: "error",
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                            }
                        });
                    }
                });
            });

            // Generate checkboxes dynamically
            // Open column settings modal
            $('#openColumnsModal').on('click', function() {
                $('#setColumns').modal('toggle');
                syncCheckboxesWithColumns();
            });

            // Generate checkboxes dynamically for column visibility
            var columns = responsiveDatatable.columns().header().toArray();
            let columnsHtml = '<div class="row">';

            // "Tümü" (Select All) checkbox
            columnsHtml += `
                <div class="col-xl-4 mt-2 form-check-sm">
                    <label class="form-label" for="column0"><i class="fe fe-corner-down-right"></i> Tümü</label>
                    <input type="checkbox" class="form-check-input column-show-hide" id="column0" value="all">
                </div>`;

            // Generate checkboxes for each column (excluding "Sütunlar" index 0)
            columns.forEach(function(column, index) {
                if (index !== 0) { // Skip "Sütunlar"
                    columnsHtml += `
                <div class="col-xl-4 mt-2 form-check-sm">
                    <label class="form-label" for="column${index}"><i class="fe fe-corner-down-right"></i> ${$(column).text()}</label>
                    <input type="checkbox" class="form-check-input column-show-hide" id="column${index}" value="${index}">
                </div>`;
                }
            });

            columnsHtml += "</div>";
            $('.datatable-columns').html(columnsHtml);

            // Load saved visibility settings from localStorage
            loadColumnVisibility();

            // Toggle column visibility and update checkboxes
            $('.column-show-hide').on('click', function() {
                let columnIndex = $(this).val();

                if (columnIndex === "all") { // If "Tümü" checkbox is clicked
                    let isChecked = $(this).prop('checked');

                    $('.column-show-hide').not('#column0').each(function() {
                        let colIndex = $(this).val();
                        $(this).prop('checked', isChecked);
                        responsiveDatatable.column(colIndex).visible(isChecked);
                    });

                } else { // Toggle individual column visibility (except "Sütunlar")
                    let column = responsiveDatatable.column(columnIndex);
                    let newState = !column.visible();
                    column.visible(newState);
                    $(this).prop('checked', newState);
                }

                // Save new visibility settings to localStorage
                saveColumnVisibility();
            });

            // Save column visibility settings to localStorage
            function saveColumnVisibility() {
                let columnVisibility = {};

                $('.column-show-hide').each(function() {
                    columnVisibility[$(this).val()] = $(this).prop('checked');
                });

                localStorage.setItem('columnVisibility', JSON.stringify(columnVisibility));
            }

            // Load column visibility settings from localStorage
            function loadColumnVisibility() {
                let columnVisibility = JSON.parse(localStorage.getItem('columnVisibility')) || {};

                $('.column-show-hide').each(function() {
                    let columnIndex = $(this).val();
                    let isVisible = columnVisibility[columnIndex] !== undefined ? columnVisibility[
                        columnIndex] : true;

                    responsiveDatatable.column(columnIndex).visible(isVisible);
                    $(this).prop('checked', isVisible);
                });

                // Sync "Tümü" checkbox
                syncCheckboxesWithColumns();
            }

            // Sync "Tümü" Checkbox
            function syncCheckboxesWithColumns() {
                let allChecked = $('.column-show-hide:not(#column0)').length === $(
                    '.column-show-hide:not(#column0):checked').length;
                $('#column0').prop('checked', allChecked);
            }

        });

        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        $('.save-filter').on('click', function () {
            if ($('input[name="saved_filter_name"]').val() != '') {
                $.ajax({
                    url: "{{ route('saveFilters') }}",
                    type: "post",
                    data: {
                        '_token':'{{ csrf_token() }}',
                        'filter_id': $('select[name="saved_filter_id"]').val(),
                        'name':$('input[name="saved_filter_name"]').val(),
                        'user_name':$('input[name="name"]').val(),
                        'second_name':$('input[name="second_name"]').val(),
                        'surname':$('input[name="surname"]').val(),
                        'email':$('input[name="email"]').val(),
                        'tc':$('input[name="tc"]').val(),
                        'telephone':$('input[name="telephone"]').val(),
                        'branch_id':$('select[name="branch_id"]').val(),
                        'type':'user'
                    } ,
                    success: function (response) {
                        if (response.success == 'true') {
                            Toast.fire({
                                icon: "success",
                                title: "Şablon Kayıt Edildi"
                            });
                            $('select[name="saved_filter_id"]').append('<option value="'+response.id+'" selected="selected">'+response.name+'</option>');
                            $('#getSavedFilter').submit()
                        } else {
                            Toast.fire({
                                icon: "error",
                                title: "Şablon Kayıt Edilemedi"
                            });
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }else{
                Toast.fire({
                    icon: "error",
                    title: "Şablon Adı Girilmedi"
                });
            }
        })

        $('.save-filter-btn').on('click', function () {
            let filterName = '';

            if (parseInt($('select[name="saved_filter_id"]').val()) > 0) {
                filterName = $('select[name="saved_filter_id"] option:selected').text();
            }

            $('input[name="saved_filter_name"]').val(filterName);

            $('#saveFilter').modal('toggle');
        });

        $('.norm-analyze-button').on('click', function () {
            $('#normAnalyze').modal('toggle');
        });

        $('#normAnalyze select').on('change', function () {
            if ($(this).val() == '0' || $(this).val() == '') {
                Toast.fire({
                    icon: "error",
                    title: 'Bayi Seçilmedi'
                });

                return false;
            }

            $.ajax({
                url: "{{ route('normAnalyze') }}",
                type: "post",
                data: {
                    '_token':'{{ csrf_token() }}',
                    'branch_id':$(this).val()
                },
                success: function (response) {
                    if (response.success) {
                        Toast.fire({
                            icon: "success",
                            title: "Analyze Başarılı"
                        });

                        $('#normAnalyze table tbody').empty();
                        let rows = '';
                        let group_grand_total = 0;
                        let limit_grand_total = 0;
                        let grand_total = 0;

                        response.data.users.forEach(user => {
                            if (user.total >= 0) {
                                var spanClass = 'class="badge bg-success"';
                                var rowClass = 'class="table-success"';
                            } else {
                                var spanClass = 'class="badge bg-danger"';
                                var rowClass = 'class="table-danger"';
                            }

                            rows += `
                                <tr ${rowClass}>
                                    <td>${user.user_group_name}</td>
                                    <td>${user.user_count}</td>
                                    <td>${user.limit}</td>
                                    <td><span ${spanClass}>${user.total}</span></td>
                                </tr>
                            `;

                            group_grand_total += user.user_count;
                            limit_grand_total += user.limit;
                            grand_total += user.total;
                        });

                        // Grand total row
                        rows += `
                                <tr class="table-info">
                                    <td><strong>Toplam: </strong></td>
                                    <td>${group_grand_total}</td>
                                    <td>${limit_grand_total}</td>
                                    <td><span class="badge bg-info">${grand_total}</span></td>
                                </tr>
                            `;

                        $('#normAnalyze table tbody').html(rows);
                    } else {
                        $('#normAnalyze table tbody').empty();

                        Toast.fire({
                            icon: "error",
                            title: response.message
                        });
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    Toast.fire({
                        icon: "error",
                        title: "Analyze Başarısız"
                    })
                }
            })
        });

        $('.export-btn').on('click', function () {
            let filterData = {
                'filter_id': $('select[name="saved_filter_id"]').val(),
                'filter_name': $('input[name="saved_filter_name"]').val(),
                'name': $('input[name="name"]').val(),
                'second_name': $('input[name="second_name"]').val(),
                'surname': $('input[name="surname"]').val(),
                'telephone': $('input[name="telephone"]').val(),
                'email': $('input[name="email"]').val(),
                'tc': $('input[name="tc"]').val(),
                'gender': $('select[name="gender"]').val(),
                'alt_beden': $('select[name="alt_beden"]').val(),
                'ayakkabi_numarasi': $('select[name="ayakkabi_numarasi"]').val(),
                'blood_group': $('select[name="blood_group"]').val(),
                'status': $('select[name="status"]').val(),
                'user_role_group_id': $('select[name="user_role_group_id"]').val(),
                'branch_id': $('select[name="branch_id"]').val(),
                'zone_id': $('select[name="zone_id"]').val(),
            };

            let queryString = $.param(filterData);

            window.location.href = "/excel/kullanicilar?" + queryString;
        });
    </script>
@endpush
