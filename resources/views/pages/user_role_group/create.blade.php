@extends('pages.build')
@section('title','<PERSON><PERSON> Ekle')
@push('css')
    <style>
        .form-check-md label{
            margin-inline-start: 0;
        }
    </style>
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('content')
    <form id="mainForm" method="post" action="{{ route('user-role-groups.store') }}">@csrf
        <div class="row">
            <div class="col-md-3">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="alisMuhasebeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#alisMuhasebe" aria-expanded="true"
                                    aria-controls="alisMuhasebe">
                                Grup Bilgileri
                            </button>
                        </h2>
                        <div id="alisMuhasebe" class="accordion-collapse collapse show" aria-labelledby="alisMuhasebeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Başlık<span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="name" value="{{ \Cache::has('user_role_group_create') ? \Cache::get('user_role_group_create')['name'] : '' }}" placeholder="Başlık" required>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control">
                                            <option value="1">Aktif</option>
                                            <option value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-9">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="yetkiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#yetki" aria-expanded="true"
                                    aria-controls="yetki">
                                Yetkiler
                            </button>
                        </h2>
                        <div id="yetki" class="accordion-collapse collapse show" aria-labelledby="yetkiHeading">
                            <div class="accordion-body">
                                <div class="example">
                                    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                                        @foreach($arr as $key => $items)
                                            <a class="nav-link @if(array_key_first($arr) == $key) active @endif" data-bs-toggle="tab" role="tab" href="#{{ $key }}"
                                               @if(array_key_first($arr) == $key) aria-selected="true" @else aria-selected="false" @endif>{{ $items[$key] }}</a>
                                        @endforeach
                                        @if($authUser->type == 'admin' || $authUser->id == 843)
                                            <a class="nav-link" data-bs-toggle="tab" role="tab" href="#other" aria-selected="false">Diğer</a>
                                        @endif
                                    </nav>
                                    <div class="tab-content">
                                        @foreach($arr as $key => $items)
                                        <div class="tab-pane @if(array_key_first($arr) == $key) show active @endif  text-muted" id="{{ $key }}" role="tabpanel">
                                            <div class="row">
                                                <div class="col-xl-12 mt-2 form-check-md">
                                                    <label class="form-label" for="{{ $key }}_management">{{ $items[$key] }} Yönetimi</label>
                                                    <input type="checkbox" class="form-check-input" id="{{ $key }}_management" style="border: 1px solid #0e0e0e">
                                                </div>
                                                @foreach($items as $subKey => $item)
                                                    <div class="col-xl-4 mt-2 form-check-sm">
                                                        <label class="form-label" for="add_{{ $subKey }}"><i class="fe fe-corner-down-right"></i>{{ $item }} Ekle</label>
                                                        <input type="checkbox" class="form-check-input sub" id="add_{{ $subKey }}" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'add_'.$subKey])->first()) checked @endif data-key="{{ $key }}" name="add_{{ $subKey }}" style="border: 1px solid #0e0e0e">
                                                    </div>
                                                    <div class="col-xl-4 mt-2 form-check-sm">
                                                        <label class="form-label" for="edit_{{ $subKey }}"><i class="fe fe-corner-down-right"></i> {{ $item }} Düzenle</label>
                                                        <input type="checkbox" class="form-check-input sub" id="edit_{{ $subKey }}" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'edit_'.$subKey])->first()) checked @endif data-key="{{ $key }}" name="edit_{{ $subKey }}" style="border: 1px solid #0e0e0e">
                                                    </div>

                                                    @if($subKey == "expertise")
                                                        <div class="col-xl-4 mt-2 form-check-sm">
                                                            <label class="form-label" for="ftp_{{ $subKey }}"><i class="fe fe-corner-down-right"></i> {{ $item }}  Kapat</label>
                                                            <input type="checkbox" class="form-check-input sub" data-key="{{ $key }}" id="ftp_{{ $subKey }}" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'ftp_'.$subKey])->first()) checked @endif name="ftp_{{ $subKey }}" style="border: 1px solid #0e0e0e">
                                                        </div>
                                                    @endif

                                                    @if($subKey == "plus-card")
                                                        <div class="col-xl-4 mt-2 form-check-sm">
                                                            <label class="form-label" for="points_{{ $subKey }}"><i class="fe fe-corner-down-right"></i> {{ $item }} Puan Yükle</label>
                                                            <input type="checkbox" class="form-check-input sub" data-key="{{ $key }}" id="points_{{ $subKey }}" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'points'.$subKey])->first()) checked @endif name="points_{{ $subKey }}" style="border: 1px solid #0e0e0e">
                                                        </div>
                                                    @endif
                                                    <div class="col-xl-4 mt-2 form-check-sm">
                                                        <label class="form-label" for="list_{{ $subKey }}"><i class="fe fe-corner-down-right"></i> {{ $item }} Listele</label>
                                                        <input type="checkbox" class="form-check-input sub" id="list_{{ $subKey }}" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'list_'.$subKey])->first()) checked @endif data-key="{{ $key }}" name="list_{{ $subKey }}" style="border: 1px solid #0e0e0e">
                                                    </div>
                                                    <div class="col-xl-4 mt-2 form-check-sm">
                                                        <label class="form-label" for="delete_{{ $subKey }}"><i class="fe fe-corner-down-right"></i> {{ $item }} Sil</label>
                                                        <input type="checkbox" class="form-check-input sub" id="delete_{{ $subKey }}" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'delete_'.$subKey])->first()) checked @endif data-key="{{ $key }}" name="delete_{{ $subKey }}" style="border: 1px solid #0e0e0e">
                                                    </div>
                                                    @if($authUser->type == 'admin' || $authUser->id == 843)
                                                        <div class="col-xl-4 mt-2 form-check-sm">
                                                            <label class="form-label" for="download_excel_{{ $subKey }}"><i class="fe fe-corner-down-right"></i> {{ $item }} Excel İndir</label>
                                                            <input type="checkbox" class="form-check-input sub" id="download_excel_{{ $subKey }}" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'download_excel_'.$subKey])->first()) checked @endif data-key="{{ $key }}" name="download_excel_{{ $subKey }}" style="border: 1px solid #0e0e0e">
                                                        </div>
                                                        <div class="col-xl-4 mt-2 form-check-sm">
                                                            <label class="form-label" for="upload_excel_{{ $subKey }}"><i class="fe fe-corner-down-right"></i> {{ $item }} Excel Yükle</label>
                                                            <input type="checkbox" class="form-check-input sub" id="upload_excel_{{ $subKey }}" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'upload_excel_'.$subKey])->first()) checked @endif data-key="{{ $key }}" name="upload_excel_{{ $subKey }}" style="border: 1px solid #0e0e0e">
                                                        </div>
                                                    @endif
                                                @endforeach

                                            </div>
                                        </div>
                                        @endforeach
                                        @if($authUser->type == 'admin' || $authUser->id == 843)
                                            <div class="tab-pane text-muted" id="other" role="tabpanel">
                                                <div class="row">
                                                    <div class="col-xl-4 mt-2 form-check-sm">
                                                        <label class="form-label" for="list_menu"><i class="fe fe-corner-down-right"></i>Menü Başlıkları</label>
                                                        <input type="checkbox" class="form-check-input" id="list_menu" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'list_menu'])->first()) checked @endif data-key="list_menu" name="list_menu" style="border: 1px solid #0e0e0e">
                                                    </div>
                                                    <div class="col-xl-4 mt-2 form-check-sm">
                                                        <label class="form-label" for="list_menu"><i class="fe fe-corner-down-right"></i>Randevular</label>
                                                        <input type="checkbox" class="form-check-input" id="list_booking" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'list_booking'])->first()) checked @endif data-key="list_booking" name="list_booking" style="border: 1px solid #0e0e0e">
                                                    </div>
                                                    <div class="col-xl-4 mt-2 form-check-sm">
                                                        <label class="form-label" for="edit_booking"><i class="fe fe-corner-down-right"></i>Randevu Detayı</label>
                                                        <input type="checkbox" class="form-check-input" id="edit_booking" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'edit_booking'])->first()) checked @endif data-key="edit_booking" name="edit_booking" style="border: 1px solid #0e0e0e">
                                                    </div>
                                                    <div class="col-xl-4 mt-2 form-check-sm">
                                                        <label class="form-label" for="list_log"><i class="fe fe-corner-down-right"></i>Log Kayıtları</label>
                                                        <input type="checkbox" class="form-check-input" id="list_log" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'list_log'])->first()) checked @endif data-key="list_log" name="list_log" style="border: 1px solid #0e0e0e">
                                                    </div>
                                                    <div class="col-xl-4 mt-2 form-check-sm">
                                                        <label class="form-label" for="list_telescope"><i class="fe fe-corner-down-right"></i>Telescope</label>
                                                        <input type="checkbox" class="form-check-input" id="list_telescope" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'list_telescope'])->first()) checked @endif data-key="list_telescope" name="list_telescope" style="border: 1px solid #0e0e0e">
                                                    </div>
                                                    <div class="col-xl-4 mt-2 form-check-sm">
                                                        <label class="form-label" for="list_settings"><i class="fe fe-corner-down-right"></i>Ayarlar</label>
                                                        <input type="checkbox" class="form-check-input" id="list_settings" @if(isset($_GET['user_group_id']) && (int)$_GET['user_group_id'] > 0 && \App\Models\UserRole::where(['group_id'=>(int)$_GET['user_group_id'],'key'=>'list_settings'])->first()) checked @endif data-key="list_settings" name="list_settings" style="border: 1px solid #0e0e0e">
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
        <input type="hidden" name="session_name" value="user_role_group_create">
    </form>
    @if(\Illuminate\Support\Facades\Cache::has('user_role_group_create'))
        <div class="modal fade" id="deleteCacheModal" tabindex="-1"
             aria-labelledby="deleteCacheModal" data-bs-keyboard="false"
             aria-hidden="true">
            <!-- Scrollable modal -->
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="staticBackdropLabel2">Mevcut Kayıt Bulundu!
                        </h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Önceden oluşturduğunuz kayıt kapanmadı!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success"
                                data-bs-dismiss="modal">Kayıta Devam Et</button>
                        <button type="submit" form="deleteCache" class="btn btn-danger">Yeni Kayıt Oluştur</button>
                        <form id="deleteCache" method="post" action="{{ route('deleteCache') }}">@csrf<input type="hidden" name="type" value="user_role_group_create"></form>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script>
        addEventListener("DOMContentLoaded", (event) => {
            @if(\Illuminate\Support\Facades\Cache::has('user_role_group_create'))
            $('#deleteCacheModal').modal('toggle')
            @endif
        });
        @foreach($arr as $key => $items)
        $('#{{ $key }}_management').on('change',function (){
            @foreach($items as $subKey => $item)
            $('#add_{{ $subKey }}')[0].checked = this.checked
            $('#edit_{{ $subKey }}')[0].checked = this.checked
            $('#list_{{ $subKey }}')[0].checked = this.checked
            $('#delete_{{ $subKey }}')[0].checked = this.checked
            $('#download_excel_{{ $subKey }}')[0].checked = this.checked
            $('#upload_excel_{{ $subKey }}')[0].checked = this.checked
            @endforeach
        })
        @endforeach

        $('.sub').on('click',function (){
            let all = document.querySelectorAll('.sub[data-key="'+$(this).data('key')+'"]');
            let allSelected = true;

            all.forEach(function(checkbox) {
                if (!checkbox.checked) {
                    allSelected = false;
                }
            });

            document.getElementById($(this).data('key')+'_management').checked = allSelected;
        })

        document.addEventListener("DOMContentLoaded", (event) => {
            $('.sub').trigger('click').trigger('click')
        });

        setInterval(function (){
            $.ajax({
                url: "{{ route('saveToSession') }}",
                type: "post",
                data: $('#mainForm').serialize(),
                success: function (response) {

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        },30 * 1000)
    </script>
@endpush
