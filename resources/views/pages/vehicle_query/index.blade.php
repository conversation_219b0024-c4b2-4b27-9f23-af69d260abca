@extends('pages.build')
@section('title','Hasar Sorguları')
@push('css')
   <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('excel')
    @if(in_array('*', $userRoles) || in_array('download_excel_expertise', $userRoles))
        <a href="/excel/query?unvan={{$_GET['unvan'] ?? ''}}&plaka={{$_GET['plaka'] ?? ''}}&sase_no={{$_GET['sase_no'] ?? ''}}&belge_no={{$_GET['belge_no'] ?? ''}}&branch_id={{$_GET['branch_id'] ?? ''}}&hasar={{$_GET['hasar'] ?? ''}}&kilometre={{$_GET['kilometre'] ?? ''}}&borc={{$_GET['borc'] ?? ''}}&detail={{$_GET['detail'] ?? ''}}&degisen={{$_GET['degisen'] ?? ''}}&ruhsat={{$_GET['ruhsat'] ?? ''}}&start_date={{$_GET['start_date'] ?? now()->format('Y-m-d')}}&end_date={{$_GET['end_date'] ?? now()->format('Y-m-d')}}" class="btn btn-sm btn-success">Excel Olarak İndir <i class="fa fa-file-excel"></i> </a>
    @endif
@endsection
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <form method="get" action="{{ route('vehicleQueries') }}">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Unvan</label>
                                    <input type="text" class="form-control form-control-sm" name="unvan" value="{{ $_GET['unvan'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Plaka</label>
                                    <input type="text" class="form-control form-control-sm" name="plaka" value="{{ $_GET['plaka'] ?? '' }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Şase No</label>
                                    <input type="text" class="form-control form-control-sm" name="sase_no" value="{{ $_GET['sase_no'] ?? '' }}">
                                </div>
                            </div>
{{--                            <div class="col-md-2">--}}
{{--                                <div class="form-group">--}}
{{--                                    <label>Belge</label>--}}
{{--                                    <input type="text" class="form-control form-control-sm" name="belge_no" value="{{ $_GET['belge_no'] ?? '' }}">--}}
{{--                                </div>--}}
{{--                            </div>--}}
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Başlangıç Tarihi</label>
                                    <input type="date" class="form-control form-control-sm" name="start_date" value="{{ $filters['startDate'] }}" max="{{ now()->format('Y-m-d') }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Bitiş Tarihi</label>
                                    <input type="date" class="form-control form-control-sm" name="end_date" value="{{ $filters['endDate'] }}" max="{{ now()->format('Y-m-d') }}">
                                </div>
                            </div>
                            @if(count($authUser->getBranchIds()) > 1)
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Bayi</label>
                                        <select class="form-control form-control-sm select2" name="branch_id">
                                            @if($authUser->type == 'admin')
                                                <option value="0">Tüm Bayiler</option>
                                            @endif
                                            @foreach(\App\Models\Branch::where('status',1)->whereIn('id',$authUser->getBranchIds())->get() as $filterBranch)
                                                <option @if(isset($_GET['branch_id']) && $_GET['branch_id'] == $filterBranch->id) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Hasar Sorgusu</label>
                                    <select class="form-control-sm form-control" name="hasar">
                                        <option value="">Tümü</option>
                                        <option @if(isset($_GET['hasar']) && $_GET['hasar'] == 1) selected @endif value="1">Yapıldı</option>
                                        <option @if(isset($_GET['hasar']) && $_GET['hasar'] == 0) selected @endif value="0">Yapılmadı</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Kilometre Sorgusu</label>
                                    <select class="form-control-sm form-control" name="kilometre">
                                         <option value="">Tümü</option>
                                        <option @if(isset($_GET['kilometre']) && $_GET['kilometre'] == 1) selected @endif value="1">Yapıldı</option>
                                        <option @if(isset($_GET['kilometre']) && $_GET['kilometre'] == 0) selected @endif value="0">Yapılmadı</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Borç Sorgusu</label>
                                    <select class="form-control-sm form-control" name="borc">
                                         <option value="">Tümü</option>
                                        <option @if(isset($_GET['borc']) && $_GET['borc'] == 1) selected @endif value="1">Yapıldı</option>
                                        <option @if(isset($_GET['borc']) && $_GET['borc'] == 0) selected @endif value="0">Yapılmadı</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Araç Detay Sorgusu</label>
                                    <select class="form-control-sm form-control" name="detail">
                                         <option value="">Tümü</option>
                                        <option @if(isset($_GET['detail']) && $_GET['detail'] == 1) selected @endif value="1">Yapıldı</option>
                                        <option @if(isset($_GET['detail']) && $_GET['detail'] == 0) selected @endif value="0">Yapılmadı</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Değişen Sorgusu</label>
                                    <select class="form-control-sm form-control" name="degisen">
                                         <option value="">Tümü</option>
                                        <option @if(isset($_GET['degisen']) && $_GET['degisen'] == 1) selected @endif value="1">Yapıldı</option>
                                        <option @if(isset($_GET['degisen']) && $_GET['degisen'] == 0) selected @endif value="0">Yapılmadı</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="form-group">
                                    <label>Ruhsat Sorgusu</label>
                                    <select class="form-control-sm form-control" name="ruhsat">
                                         <option value="">Tümü</option>
                                        <option @if(isset($_GET['ruhsat']) && $_GET['ruhsat'] == 1) selected @endif value="1">Yapıldı</option>
                                        <option @if(isset($_GET['ruhsat']) && $_GET['ruhsat'] == 0) selected @endif value="0">Yapılmadı</option>
                                    </select>
                                </div>
                            </div>
                            @if($authUser->type == 'admin')
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Hasar Sorgu Firma</label>
                                        <select class="form-control-sm form-control" name="hasar_firma">
                                            <option value="">Tümü</option>
                                            <option @if(isset($_GET['hasar_firma']) && $_GET['hasar_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>
                                            <option @if(isset($_GET['hasar_firma']) && $_GET['hasar_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Kilometre Sorgu Firma</label>
                                        <select class="form-control-sm form-control" name="kilometre_firma">
                                            <option value="">Tümü</option>
                                            <option @if(isset($_GET['kilometre_firma']) && $_GET['kilometre_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>
                                            <option @if(isset($_GET['kilometre_firma']) && $_GET['kilometre_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Borç Sorgu Firma</label>
                                        <select class="form-control-sm form-control" name="borc_firma">
                                            <option value="">Tümü</option>
                                            <option @if(isset($_GET['borc_firma']) && $_GET['borc_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>
                                            <option @if(isset($_GET['borc_firma']) && $_GET['borc_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Araç Detay Sorgu Firma</label>
                                        <select class="form-control-sm form-control" name="detail_firma">
                                            <option value="">Tümü</option>
                                            <option @if(isset($_GET['detail_firma']) && $_GET['detail_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>
                                            <option @if(isset($_GET['detail_firma']) && $_GET['detail_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Değişen Sorgu Firma</label>
                                        <select class="form-control-sm form-control" name="degisen_firma">
                                            <option value="">Tümü</option>
                                            <option @if(isset($_GET['degisen_firma']) && $_GET['degisen_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>
                                            <option @if(isset($_GET['degisen_firma']) && $_GET['degisen_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label>Ruhsat Sorgu Firma</label>
                                        <select class="form-control-sm form-control" name="ruhsat_firma">
                                            <option value="">Tümü</option>
                                            <option @if(isset($_GET['ruhsat_firma']) && $_GET['ruhsat_firma'] == 'otosorgu.com') selected @endif value="otosorgu.com">Otosorgu</option>
                                            <option @if(isset($_GET['ruhsat_firma']) && $_GET['ruhsat_firma'] == 'arabasorgula.com') selected @endif value="arabasorgula.com">Araba Sorgula</option>
                                        </select>
                                    </div>
                                </div>
                            @endif
                            <div class="col-md-2 mt-4">
                                <button class="btn btn-success btn-sm mt-1">Listele</button>
                                <button onclick="$('#saveFilter').modal('toggle')" type="button" class="btn btn-sm btn-danger mt-1" >Kaydet</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="card custom-card">
                <div class="card-body">
                    <div class="table-responsive">
                        @if($authUser->type == 'admin')
                            <div style="display: flex">
                                <div style="width: 20px;height: 20px;background-color: #e2626b"></div> ArabaSorgula.com
                            </div>
                            <div style="display: flex">
                                <div style="width: 20px;height: 20px;background-color: #fecc16"></div> Otosorgu.com
                            </div>
                            <div class="text-center">
                                <!-- <button id="addRow" class="btn btn-primary mb-2 data-table-btn" data-bs-toggle="modal" data-bs-target="#exampleModalScrollable">Yeni Sorgu</button> -->
                                <a href="{{ route('vehicleQueries.create') }}" class="btn btn-primary mb-2 data-table-btn">Yeni Sorgu</a>
                            </div>
                        @endif

                        <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                            <thead>
                                <tr>
                                    <th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                                    <th>Müşteri Adı</th>
                                    <th>Plaka</th>
                                    <th>Şase No</th>
                                    <th>Belge no</th>
                                    <th>Bayi</th>
                                    <th>Maliyet</th>
                                    <th>Hasar</th>
                                    <th>Kilometre</th>
                                    <th>Borç</th>
                                    <th>Araç Detay</th>
                                    <th>Degisen</th>
                                    <th>Ruhsat</th>
                                    <th>İşlem Tarihi</th>
                                    <th>İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->
    <div class="modal fade" id="exampleModalScrollable" tabindex="-1" aria-labelledby="exampleModalScrollableLabel" data-bs-keyboard="false" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel">Yeni sorgu yap</h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit...</p>
                    <br><br><br><br><br><br><br><br>
                    <p>Lorem ipsum dolor sit amet.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Save Changes</button>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

    <script>
        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({
                "dom": '<"pull-left"f><"pull-right"l>rtip',
                scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                "processing": true,
                "serverSide": true,
                "searching": false,
                "ajax": {
                    "url": "{{ route('vehicleQueries.ajax') }}",
                    "dataType": "json",
                    "type": "POST",
                    "data": {
                        _token: "{{csrf_token()}}",
                        @if(isset($_GET))
                            @foreach($_GET as $key => $value)
                                '{{ $key }}':"{{ $value }}",
                            @endforeach
                        @endif
{{--                        @if(isset($_GET['unvan']) && $_GET['unvan'] != '')--}}
{{--                        'unvan':"{{ $_GET['unvan'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['plaka']) && $_GET['plaka'] != '')--}}
{{--                        'plaka':"{{ $_GET['plaka'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['sase_no']) && $_GET['sase_no'] != '')--}}
{{--                        'sase_no':"{{ $_GET['sase_no'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['unvan']) && $_GET['unvan'] != '')--}}
{{--                        'unvan':"{{ $_GET['unvan'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['plaka']) && $_GET['plaka'] != '')--}}
{{--                        'plaka':"{{ $_GET['plaka'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['sase_no']) && $_GET['sase_no'] != '')--}}
{{--                        'sase_no':"{{ $_GET['sase_no'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['unvan']) && $_GET['unvan'] != '')--}}
{{--                        'unvan':"{{ $_GET['unvan'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['plaka']) && $_GET['plaka'] != '')--}}
{{--                        'plaka':"{{ $_GET['plaka'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['sase_no']) && $_GET['sase_no'] != '')--}}
{{--                        'sase_no':"{{ $_GET['sase_no'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['unvan']) && $_GET['unvan'] != '')--}}
{{--                        'unvan':"{{ $_GET['unvan'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['plaka']) && $_GET['plaka'] != '')--}}
{{--                        'plaka':"{{ $_GET['plaka'] }}",--}}
{{--                        @endif--}}
{{--                        @if(isset($_GET['sase_no']) && $_GET['sase_no'] != '')--}}
{{--                        'sase_no':"{{ $_GET['sase_no'] }}",--}}
{{--                        @endif--}}
                    },
                    "dataSrc":'data'
                },
                "columns": [
                    { "data": 'x','defaultContent': '' },
                    { "data": "alici" },
                    { "data": "plaka" },
                    { "data": "sase_no" },
                    { "data": "belge_no" },
                    { "data": "branch_name" },
                    { "data": "maliyet" },
                    { "data": "hasar" },
                    { "data": "kilometre" },
                    { "data": "borc" },
                    { "data": "detail" },
                    { "data": "degisen" },
                    { "data": "ruhsat" },
                    { "data": "tarih" },
                    { "data": "islem" },
                ],
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı! @if($authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','add_note')->first()) <a href='{{ route("notes.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı! @if($authUser->getUserRoleGroup && $authUser->getUserRoleGroup->getRoles->where('key','add_note')->first()) <a href='{{ route("notes.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })
        });
    </script>
@endpush
