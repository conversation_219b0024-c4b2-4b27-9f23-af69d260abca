<?php

use App\Models\Customer;
use App\Models\ExpertisePayment;
use App\Models\PlusCard;
use App\Models\PlusCardCrediAndPuanAdd;
use App\Models\Setting;
use App\Models\Ticket;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Controllers\Api\V1\ExpertiseReportController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/setNotificationToken',[\App\Http\Controllers\HelperController::class,'setNotificationToken']);

Route::middleware('userAuth')->group(function (){
    Route::get('/updateHizmetTutari',function (){
        $expertiseStocks = \App\Models\ExpertiseStock::all();
        foreach ($expertiseStocks as $expertiseStock){
            $expertiseStock->hizmet_tutari = $expertiseStock->liste_fiyati - $expertiseStock->iskonto_amount;
//            $expertiseStock->save();
        }
    });


    Route::get('/setTelDeletedUsers',function (){
        $users = \App\Models\User::where('status',-1)->get();
        foreach ($users as $user){
            $user->telephone = '-1'.$user->telephone;
            $user->second_telephone = '-1'.$user->second_telephone;
            $user->tc = '-1'.$user->tc;
            $user->save();
        }
    });

    Route::get('/getOldCustomerCodes', function () {
        $index = 0;
        $min = 150000 * $index;
        $max = 150000 * ($index+1);
        $count = 0;
        $customers = \Illuminate\Support\Facades\DB::select("SELECT kod,eski_cari_kod,unvan,ad FROM customers_yedek12 WHERE eski_cari_kod LIKE '%-%' AND id BETWEEN ".$min." AND ".$max." LIMIT 70000");
        foreach ($customers as $customer) {

            if (!empty($customer->kod)) {
                $kod = \Illuminate\Support\Facades\DB::connection('mysql2')
                    ->select("SELECT T200_hesapkodu FROM T200_HESPLAN WHERE T200_UQ = '".$customer->kod."'");

                if (!empty($kod) && !empty($kod[0]->T200_hesapkodu)) {
                    \Illuminate\Support\Facades\DB::update("UPDATE customers_yedek12 SET eski_cari_kod = '".$kod[0]->T200_hesapkodu."' WHERE kod = '".$customer->kod."'");
                    $count++;
                }
            }elseif (!empty($customer->eski_cari_kod)){

                $kod = \Illuminate\Support\Facades\DB::connection('mysql2')
                    ->select("SELECT T200_hesapkodu FROM T200_HESPLAN WHERE T200_UQ = '".$customer->eski_cari_kod."'");

                if (!empty($kod) && !empty($kod[0]->T200_hesapkodu)) {
                    $cariKod = $customer->eski_cari_kod;
                    \Illuminate\Support\Facades\DB::update("UPDATE customers_yedek12 SET eski_cari_kod = '".$kod[0]->T200_hesapkodu."' , kod = '".$cariKod."' WHERE eski_cari_kod = '".$customer->eski_cari_kod."'");
                    $count++;
                }
            }
        }
        echo $count;
    });


    Route::get('/acikHesapToPlusCard',function (){
        $expertises = \App\Models\Expertise::whereHas('getPayments',function ($query){
            return $query->where(['type'=>'acik_hesap']);
        })->whereDate('created_at','>=','2024-03-22')->get();
        foreach ($expertises as $expertise){

            foreach ($expertise->getAlici->getPlusCards as $getPlusCard){
                $plusCard = PlusCard::find($getPlusCard->id);
                if ($plusCard){
                    $expertiseStock = \App\Models\ExpertiseStock::where('expertise_id',$expertise->id)->first();

                    $balance = $plusCard->getTotalBalanceStock($expertiseStock->stock_id,$expertise->branch_id);
                    if($balance['points'] > 0){
                        $last_puan = PlusCardCrediAndPuanAdd::where('card_id',$plusCard->id)
                            ->where('puan_branche_id',$expertise->branch_id)
                            ->where('puan','>',0)
                            ->orderBy('id','asc')
                            ->where(function ($query) {
                                $query->whereNull('valid_date')  // Boşsa
                                ->orWhere('valid_date', '>=', '2024-03-22');  // Bugünden büyük veya eşitse
                            })
                            ->get();
                        if(!empty($last_puan)){
                            $odeme_id = 0;
                            foreach($last_puan as $lp){
                                $lps = ExpertisePayment::where('type','plus_kart')
                                    ->where('plus_card_id',$plusCard->id)
                                    ->where('plus_card_odeme_id',$lp->id)
                                    ->count();
                                if($lps < $lp->puan){
                                    $odeme_id = $lp->id;
                                    break;
                                }
                            }
                            if($odeme_id != 0){
                                $expertisePayment = ExpertisePayment::where('expertise_id',$expertise->id)->first();
                                if ($expertisePayment->type == 'acik_hesap'){
                                    $expertisePayment->type = 'plus_kart';
                                    $expertisePayment->plus_card_id = $plusCard->id;
                                    $expertisePayment->plus_card_odeme_id = $odeme_id;
                                    $expertisePayment->save();
                                }
                            }
                        }
                    }

                    if($balance['credits'] > 0){
                        $last_credits = PlusCardCrediAndPuanAdd::where('card_id',$plusCard->id)
                            ->where('credi','>',0)
                            ->orderBy('id','asc')
                            ->get();
                        $last_credits = $last_credits->filter(function ($query){
                            return  $query->whereNull('valid_date')  // Boşsa
                            ->orWhere('valid_date', '>=', '2024-03-22');  // Bugünden büyük veya eşitse
                        });
                        if(!empty($last_credits)){
                            $odeme_id = 0;
                            foreach($last_credits as $lp){
                                $lps = ExpertisePayment::where('type','plus_kart')
                                    ->where('plus_card_id',$plusCard->id)
                                    ->where('plus_card_odeme_id',$lp->id)
                                    ->count();
                                if($lps < $lp->credi){
                                    $odeme_id = $lp->id;
                                    break;
                                }
                            }

                            if($odeme_id != 0){
                                $expertisePayment = ExpertisePayment::where('expertise_id',$expertise->id)->first();
                                if ($expertisePayment->type == 'acik_hesap'){
                                    $expertisePayment->type = 'plus_kart';
                                    $expertisePayment->plus_card_id = $plusCard->id;
                                    $expertisePayment->plus_card_odeme_id = $odeme_id;
                                    $expertisePayment->save();
                                }
                            }

                        }
                    }
                }
            }
        }

    });


    Route::get('/deletedExpertisesRemovePayments',function (){
        $deletedExpertises = \App\Models\Expertise::onlyTrashed()->get();
        foreach ($deletedExpertises as $deletedExpertise){
            ExpertisePayment::where('expertise_id',$deletedExpertise->id)->delete();
        }
    });

    Route::get('/setCustomerNameToUpper',[\App\Http\Controllers\CustomerController::class,'setCustomerNameToUpper']);

    Route::get('/checkMatch',function (){
        $customersCodes = \App\Models\Customer::whereNull('eski_cari_kod')->whereNull('created_at')->whereNotNull('kod')->pluck('kod')->toArray();
        foreach ($customersCodes as $customerCode){
            $oldCustomer = \Illuminate\Support\Facades\DB::connection('mysql2')->select('SELECT * FROM T201_CARILER WHERE hesap_UQ = ?',[$customerCode]);
            if (!is_array($oldCustomer))
                echo "<span>$customerCode</span><br><br>";
        }
    });

    Route::get('/admin/checkClear',function (){
        if (auth()->check() && auth()->user()->type == 'admin')
            \Illuminate\Support\Facades\Artisan::call('cache:clear');

        return redirect(url()->previous());
    });

    Route::get('/plusCardsAgain',function (){
        \App\Jobs\TransferPlusCardsJob::dispatch()->onQueue('high');
        dd("başladı");
    });

    Route::get('/plusCardWalletsAgain',function (){
        //SELECT SUM(T404_miktar) AS toplam_miktar FROM `T404_KUPONTAKIP` WHERE `T404_kayittipi` = 1 AND (`T404_islemtipi` = 2 OR `T404_islemtipi` = 1) AND `T404_hesap_UQ` LIKE 'A0231DAF-FF57-4D95-8876-1A59284C306C';; Yükleme
        // SELECT SUM(T404_miktar) AS toplam_miktar FROM `T404_KUPONTAKIP` WHERE `T404_kayittipi` = 1 AND `T404_islemtipi` = -1 AND `T404_hesap_UQ` LIKE 'E5521762-4522-456F-BC68-697F12213908'; Harcama
    });

    Route::get('/plusCardAndKartNoCheck',function (){
        $kartNumaralari = \Illuminate\Support\Facades\DB::connection('mysql2')->select("SELECT * FROM T212_KARTNUMARALARI");
        foreach ($kartNumaralari as $kartNo){
            $plusCard = \Illuminate\Support\Facades\DB::connection('mysql')->select("SELECT * FROM plus_cards WHERE no = ?",[$kartNo->T212_kartno]);
            if (is_array($plusCard) && isset($plusCard[0]) && $plusCard[0]->customer_id != $kartNo->T212_musteri_UQ){
                $customer = \Illuminate\Support\Facades\DB::connection('mysql')->select("SELECT * FROM customers WHERE id = ?",[$plusCard[0]->customer_id]);
                if (!isset($customer[0]) || ($customer[0]->kod != $kartNo->T212_musteri_UQ)){
                    echo $kartNo->T212_kartno . ' => <span style="color: orangered">'.($customer[0]->kod ?? ''). '</span> => <span style="color: #396010">' .$kartNo->T212_musteri_UQ.'</span><br>';
                }
            }
//        else{
//            echo $kartNo->T212_kartno . ' yeni sistemde yok <br>';
//        }
        }
    });

    Route::get('/checkNewAndOldCardNumbers',function (){
        // Son önbellek tarihini al
        $lastCacheDate = \Illuminate\Support\Facades\Cache::get('lastCacheDate');
        \Illuminate\Support\Facades\Cache::forget('lastCacheDate');
// Eğer cache tarihi yoksa veya son önbellek tarihinden 10 dakika geçtiyse
        if (!$lastCacheDate || \Carbon\Carbon::now()->diffInMinutes($lastCacheDate) > 10) {
            // Cache tarihini güncelle
            \Illuminate\Support\Facades\Cache::put('lastCacheDate', \Carbon\Carbon::now());
            echo "<p>Son önbellek tarihi: " . now()->format('Y-m-d H:i:s') . "</p>";
            // Kodu önbelleğe al
            $cachedHtml = \Illuminate\Support\Facades\Cache::remember('cachedHtml', now()->addMinutes(10), function () {
                echo "<link id=\"style\" href=\"/assets/libs/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\" >";
                echo "<table class='table table-bordered table-striped'><thead>";
                echo "<tr style='position: sticky;top: 0;'><td colspan='2' style='background-color: aquamarine;text-align: center'>Eski Sistem</td><td colspan='4' style='background-color: aquamarine;text-align: center'>Eski Sistem</td><td colspan='4' style='background-color: aquamarine;text-align: center'>Yeni Sistem</td></tr>";
                echo "<tr style='position: sticky;top: 1.5rem;'><td>Kart No</td><td>Kart ID</td><td>Cari Bilgileri</td><td>Eski Kod</td><td>Yeni Kod</td><td>Telefon</td><td>Cari Bilgileri</td><td>Eski Kod</td><td>Yeni Kod</td><td>Telefon</td><td>Eski Kod Aynı mı?</td></tr>";
                echo "</thead><tbody>";

                \Illuminate\Support\Facades\DB::table('hlx_plus_try')->orderBy('id')->chunk(1000, function ($oldCards) {
                    foreach ($oldCards as $oldCard) {

                        $newCard = \App\Models\PlusCard::where('no', $oldCard->no)->whereNotNull('customer_id')->first();

                        if ($newCard && $newCard->customer_id != $oldCard->customer_id) {
                            $oldCardCustomer = \App\Models\Customer::where('id',$oldCard->customer_id)->first();
                            $newCardCustomer = \App\Models\Customer::where('id',$newCard->customer_id)->first();
                            echo "<tr>";
                            echo "<td>" . $oldCard->no . "</td><td>" . $oldCard->system_id . "</td><td style='border-left: 2px solid'>".($oldCardCustomer?->fullName ?? '')."</td><td>".($oldCardCustomer?->eski_cari_kod ?? '')."</td><td>".($oldCardCustomer?->cari_kod ?? '')."</td><td>".($oldCardCustomer?->phone() ?? '')."</td>";
                            echo "<td  style='border-left: 2px solid'>".($newCardCustomer?->fullName ?? '')."</td><td>".($newCardCustomer?->eski_cari_kod ?? '')."</td><td>".($newCardCustomer?->cari_kod ?? '')."</td><td>".($newCardCustomer?->phone() ?? '')."</td>";
                            echo "<td>".($oldCardCustomer && $newCardCustomer ? ($newCardCustomer->eski_cari_kod == $oldCardCustomer->eski_cari_kod ? 'Evet' : '') : '')."</td>";
                            echo "</tr>";
                        }
                    }
                });

                echo "</tbody></table>";
            });

        } else {
            echo "<p>Son önbellek tarihi: " . $lastCacheDate->format('Y-m-d H:i:s') . "</p>";
            // Cache tarihinden 10 dakikadan az bir zaman geçtiyse, önbellekten alınmış HTML'i kullan
            $cachedHtml = \Illuminate\Support\Facades\Cache::get('cachedHtml');
        }

// Önbelleklenmiş HTML'i görüntüle
        echo $cachedHtml;

// Kullanıcıya bilgi olarak son önbellek tarihini ver


    });


    Route::get('/newToOldPlusCardTransfer',function (){
        set_time_limit(0);
        $chk=100;
        \Illuminate\Support\Facades\DB::connection('mysql2')->table('plus_cards')->where('hlx_transfer',0)->orderBy('no')->chunk($chk,function($rs){
            $mevcutlar=\Illuminate\Support\Facades\DB::connection('mysql2')->table('hlx_plus')->whereIn('no',$rs->pluck('no'))->pluck('no');
            foreach($rs->whereNotIn('no',$mevcutlar)->all() as $r){
                \Illuminate\Support\Facades\DB::connection('mysql2')->table('hlx_plus')->insert([
                    'karttipi' => $r->karttipi,
                    'system_id' => $r->system_id,
                    'no' => $r->no,
                    'branch_id' => $r->branch_id,
                    'customer_id' => $r->customer_id,
                    'kredi' => $r->kredi,
                    'puan' => $r->puan,
                    'valid_date_if' => $r->valid_date_if,
                    'valid_date' => $r->valid_date,
                    'created_at' => $r->created_at,
                    'updated_at' => $r->updated_at,
                    'deleted_at' => $r->deleted_at,
                    'tlindtutar' => $r->tlindtutar,
                    'credits_update' => $r->credits_update,
                    'points_update' => $r->points_update,
                    'T212_ID' => null
                ]);
                $this->warn($r->no.' Eklendi');
            }
            //\Illuminate\Support\Facades\DB::connection('mysql2')->table('hlx_plus_new')->whereIn('no',$rs->pluck('no'))->update(['hlx_transfer'=>1]);
        });
    });

    Route::get('/plusCardNullCustomers',function (){
        \Illuminate\Support\Facades\DB::connection('mysql')->table('hlx_plus_try')
            ->whereNull('customer_id')
            ->orderBy('no')
            ->chunk(1000, function ($hlxPlusCards) {
                foreach ($hlxPlusCards as $hlxPlusCard) {
                    $plusCard = \Illuminate\Support\Facades\DB::connection('mysql')->table('plus_cards')
                        ->where('no', $hlxPlusCard->no)
                        ->whereNotNull('customer_id')
                        ->select('customer_id')
                        ->first();

                    if ($plusCard) {
                        \Illuminate\Support\Facades\DB::connection('mysql')->table('hlx_plus_try')
                            ->where('id', $hlxPlusCard->id)
                            ->update(['customer_id' => $plusCard->customer_id]);
                    }
                }
            });
    });

    Route::get('/plusCardSkipDeletedUsers',function (){
        $deletedPlusCards = \App\Models\PlusCard::onlyTrashed()->pluck('no');
        foreach ($deletedPlusCards as $deletedPlusCard){
            $notDeleted = \App\Models\PlusCard::where('no',$deletedPlusCard)->first();
            if ($notDeleted){
                $hlxPlusTry = \Illuminate\Support\Facades\DB::connection('mysql')->table('hlx_plus_try')->where('no',$deletedPlusCard)->update(['customer_id' => $notDeleted->customer_id]);
            }
        }
    });

    Route::get('/updateOldCariUuidToCode',function (){
        $index = 2;
        $skip = 250 * $index;
        $take = 250 * ($index+1);
        $customers = \App\Models\Customer::where('eski_cari_kod','LIKE','%-%')->skip($skip)->take($take)->get();
        foreach ($customers as $customer){
            $new = \DB::table('customers_yedek12')->where('id',$customer->id)->first();
            if ($new){
                $customer->eski_cari_kod = $new->eski_cari_kod;
                $customer->kod = $new->kod;
//                $customer->save();
            }
        }
    });

    Route::get('/TransferPlusCardWallets',function (){
        $plus_card_definitions = \App\Models\PlusCardsDefinitions::pluck('id', 'stok_uq');

        $kartNumaralari = DB::connection('mysql')
            ->table('hlx_plus_try')
            ->whereNotNull('customer_id')
            ->orderBy('no')
            ->chunk(100, function ($kartNumaralari) use ($plus_card_definitions) {
                $insertData = [];

                foreach ($kartNumaralari as $plusCard) {
                    $customer = Customer::where('id', $plusCard->customer_id)->select('kod')->first();

                    if ($customer) {
                        $yuklenenler = \DB::table('plus_card_credi_and_puan_add_new')->where('card_id', $plusCard->id)->pluck('T404_ID');

                        $yuklemeler = DB::connection('mysql2')
                            ->table('T404_KUPONTAKIP')
                            ->whereNotIn('T404_ID', $yuklenenler)
                            ->where(['T404_kayittipi' => 1, 'T404_islemtipi' => 2, 'T404_hesap_UQ' => $customer->kod])
                            ->get();

                        foreach ($yuklemeler as $yukleme) {
                            $harcamaAdeti = DB::connection('mysql2')
                                ->table('T404_KUPONTAKIP')
                                ->where(['T404_kayittipi' => 1, 'T404_islemtipi' => -1, 'T404_iliski_UQ' => $yukleme->T404_UQ])
                                ->count();

                            if ((int)$yukleme->T404_miktar - $harcamaAdeti > 0) {
                                $insertData[] = [
                                    'user_id' => 1,
                                    'stock_id' => 7,
                                    'definitions_id' => $plus_card_definitions[$yukleme->T404_stok_UQ] ?? null,
                                    'balance_type' => 'credits',
                                    'card_id' => $plusCard->id,
                                    'unit_price' => $yukleme->T404_fiyat,
                                    'commission' => $yukleme->T404_komisyon,
                                    'credits_amount' => $yukleme->T404_tutar,
                                    'credi' => (int)$yukleme->T404_miktar - $harcamaAdeti,
                                    'puan' => 0,
                                    'puan_branche_id' => 0,
                                    'odenen_kdv_dahil_fiyat' => $yukleme->T404_tutar,
                                    'payment_type' => 'yeni_sistem_bakiye_aktirimi',
                                    'case' => 0,
                                    'valid_date' => $yukleme->T404_sonkullanimtarihi,
                                    'devir_miktar' => null,
                                    'devir_tarih' => null,
                                    'created_at' => null,
                                    'updated_at' => null,
                                    'deleted_at' => null,
                                    'code_deleted' => 2,
                                    'delayed_id' => null,
                                    'T404_ID' => $yukleme->T404_ID,
                                ];
                            }
                        }
                    }
                }

                if (!empty($insertData)) {
                    \Illuminate\Support\Facades\DB::connection('mysql')
                        ->table('plus_card_credi_and_puan_add_new')
                        ->insert($insertData);
                }
            });

    });

    Route::get('/checkNewPlusCardWallets',function (){
        $hlxPlusCards =  DB::connection('mysql')
            ->table('hlx_plus_try')
            ->whereNotNull('customer_id')
            ->orderBy('no')
            ->get();
        echo "<link id=\"style\" href=\"/assets/libs/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\" >";
        echo "<table class='table table-bordered table-striped'>";
        echo "<tr style='position:sticky;top: 0;'>";
        echo "<th>No</th>";
        echo "<th>Sistem ID</th>";
        echo "<th>Müşteri Adı</th>";
        echo "<th>Eski Kod</th>";
        echo "<th>Yeni Kod</th>";
        echo "<th>Telefon</th>";
        echo "<th>Deha Kalan Kredi</th>";
        echo "<th>Deha Kalan Puan</th>";
        echo "<th>Umram Yüklenen Kredi</th>";
        echo "<th>Umram Yüklenen Puan</th>";
        echo "<th>Umram Harcamalar</th>";
        echo "<th>Kalan</th>";
        echo "</tr>";

        foreach ($hlxPlusCards as $hlxPlusCard){
            $customer = Customer::where('id',$hlxPlusCard->customer_id)->first();
            $eskiSistemKredi = DB::connection('mysql')
                ->table('plus_card_credi_and_puan_add_try')
                ->where('card_id',$hlxPlusCard->id)
                ->where('balance_type','credits')
                ->where('payment_type','yeni_sistem_bakiye_aktirimi')
                ->sum('credi');
            $eskiSistemPuan = DB::connection('mysql')
                ->table('plus_card_credi_and_puan_add_try')
                ->where('card_id',$hlxPlusCard->id)
                ->where('balance_type','points')
                ->where('payment_type','yeni_sistem_bakiye_aktirimi')
                ->sum('puan');

            $yeniSistemKredi = DB::connection('mysql')
                ->table('plus_card_credi_and_puan_add_try')
                ->where('card_id',$hlxPlusCard->id)
                ->where('balance_type','credits')
                ->where('payment_type','!=','yeni_sistem_bakiye_aktirimi')
                ->sum('credi');
            $yeniSistemPuan = DB::connection('mysql')
                ->table('plus_card_credi_and_puan_add_try')
                ->where('card_id',$hlxPlusCard->id)
                ->where('balance_type','points')
                ->where('payment_type','!=','yeni_sistem_bakiye_aktirimi')
                ->sum('puan');
            $harcamalar = DB::connection('mysql')
                ->table('expertise_payments_try')
                ->where('plus_card_id',$hlxPlusCard->id)
                ->count();
            echo "<tr>";
            echo "<td>" . $hlxPlusCard->no . "</td>";
            echo "<td>" . $hlxPlusCard->system_id . "</td>";
            $customerName = isset($customer) ? $customer->fullName : '';
            echo "<td>" . $customerName . "</td>";
            echo "<td>" . $customer?->eski_cari_kod . "</td>";
            echo "<td>" . $customer?->cari_kod . "</td>";
            echo "<td>" . $customer?->phone() . "</td>";
            echo "<td>" . $eskiSistemKredi . "</td>";
            echo "<td>" . $eskiSistemPuan . "</td>";
            echo "<td>" . $yeniSistemKredi . "</td>";
            echo "<td>" . $yeniSistemPuan . "</td>";
            echo "<td>" . $harcamalar . "</td>";
            echo "<td>" . ($eskiSistemKredi + $eskiSistemPuan + $yeniSistemKredi + $yeniSistemPuan - $harcamalar) . "</td>";
            echo "</tr>";
        }
    });

    Route::get('/paymentWithNewPlusCards',function (){
        set_time_limit(0);
        $payments =  DB::connection('mysql2')
            ->table('expertise_payments_try')
            ->where('type','acik_hesap')
            ->where('update_data',0)
            ->orderBy('id')
            ->chunk(100,function ($payments){
                foreach ($payments as $payment){
                    $expertise = DB::connection('mysql2')
                        ->table('expertises')
                        ->select('alici_id','branch_id')
                        ->where('id',$payment->expertise_id)
                        ->first();
                    if ($expertise){
                        $hlxPlusCard = DB::connection('mysql2')
                            ->table('hlx_plus_try')
                            ->where('customer_id',$expertise->alici_id)
                            ->first();
                        if ($hlxPlusCard){
                            $puanYuklemeler =  DB::connection('mysql2')
                                ->table('plus_card_credi_and_puan_add_try')
                                ->where('card_id',$hlxPlusCard->id)
                                ->where('puan_branche_id',$expertise->branch_id)
                                ->get();
                            $pay = 0;
                            foreach ($puanYuklemeler as $puanYukleme){
                                $puanHarcamaAdeti = DB::connection('mysql2')
                                    ->table('expertise_payments_try')
                                    ->where('plus_card_id',$hlxPlusCard->id)
                                    ->where('plus_card_odeme_id',$puanYukleme->id)
                                    ->count();
                                if ($puanHarcamaAdeti < $puanYukleme->puan){
                                    DB::connection('mysql2')
                                        ->table('expertise_payments_try')
                                        ->where('id',$payment->id)
                                        ->update([
                                            'amount'=>$puanYukleme->unit_price ?? 0,
                                            'plus_card_id'=>$hlxPlusCard->id,
                                            'plus_card_odeme_id'=>$puanYukleme->id,
                                            'update_data'=>1
                                        ]);
                                    $this->info($payment->id . " Ödeme Kaydedildi");
                                    $pay = 1;
                                    break;
                                }
                            }
                            if ($pay == 0){
                                $krediYuklemeler =  DB::connection('mysql2')
                                    ->table('plus_card_credi_and_puan_add_try')
                                    ->where('card_id',$hlxPlusCard->id)
                                    ->get();
                                foreach ($krediYuklemeler as $krediYukleme){
                                    $krediHarcamaAdeti = DB::connection('mysql2')
                                        ->table('expertise_payments_try')
                                        ->where('plus_card_id',$hlxPlusCard->id)
                                        ->where('plus_card_odeme_id',$krediYukleme->id)
                                        ->count();
                                    if ($krediHarcamaAdeti < $krediYukleme->credi){
                                        DB::connection('mysql2')
                                            ->table('expertise_payments_try')
                                            ->where('id',$payment->id)
                                            ->update([
                                                'amount'=>$krediYukleme->unit_price ?? 0,
                                                'plus_card_id'=>$hlxPlusCard->id,
                                                'plus_card_odeme_id'=>$krediYukleme->id,
                                                'update_data'=>1
                                            ]);
                                        $this->info($payment->id . " Ödeme Kaydedildi");
                                        $pay = 1;
                                        break;
                                    }
                                }
                            }
                            if ($pay == 0){
                                DB::connection('mysql2')
                                    ->table('expertise_payments_try')
                                    ->where('id',$payment->id)
                                    ->update(['update_data'=>4]);
                                $this->error($payment->id . " Ödeme Yapılamadı");
                            }
                        }else{
                            DB::connection('mysql2')
                                ->table('expertise_payments_try')
                                ->where('id',$payment->id)
                                ->update(['update_data'=>3]);
                            $this->warn($payment->id . " Kart Bulunamadı!");
                        }
                    }else{
                        DB::connection('mysql2')
                            ->table('expertise_payments_try')
                            ->where('id',$payment->id)
                            ->update(['update_data'=>2]);
                        $this->warn($payment->id . " Ekspertiz Bulunamadı!");
                    }

                }
            });
    });
});


Route::post('/plus-card-info',function (Request $request){
    dd($request->all());
});

Route::get('/get-branch/{dahili}',function ($dahili){
   $branch = \App\Models\Branch::where('yetkili_dahili',$dahili)->first();
   if ($branch){
       return response()->json([
           'success' => 'true',
           'telephone' => $branch->telefon,
           'wp_telephone' => $branch->whatsapp_telefon,
           'gsm' => $branch->gsm,
           'email' => $branch->email,
           'adres' => $branch->fullAddress,
           'yetkili' => $branch->yetkili_ad_soyad,
           'konum' => $branch->konum,
       ]);
   }else{
       return response()->json([
           'success' => 'false',
       ]);
   }
});

Route::get('/queryExport',function (){
    return Excel::download(new \App\Exports\QueryLogExport(), 'sorgular.xlsx');
});

Route::get('/ticketReport',function (){
    $todayStats = [];
    $allStats = [];
    $statuses = [
        0 => 'Olumsuz Sonuclandi',
        1 => 'Basariyla Cozuldu',
        2 => 'Islem Bekliyor',
        3 => 'Islemde',
        4 => 'Islem Yapildi',
        5 => ''
    ];

// Initialize stats arrays
    foreach ($statuses as $index => $status) {
        $todayStats[$index] = 0;
        $allStats[$index] = 0;
    }

// Get all tickets and filter them
    $tickets = Ticket::all();
    $today = now()->startOfDay();

    foreach ($tickets as $ticket) {
        $allStats[$ticket->status] += 1;
        if ($ticket->updated_at >= $today) {
            $todayStats[$ticket->status] += 1;
        }
    }

    $telephones = [
        '5512549933',
        '5071004968'
    ];
    $settings = Setting::first();

// Split statuses into two parts
    $statusesPart1 = array_slice($statuses, 0, ceil(count($statuses) / 2), true);
    $statusesPart2 = array_slice($statuses, ceil(count($statuses) / 2), null, true);

// Prepare the messages
    $todayMessagePart1 = now()->translatedFormat('d F Y') . " Umran Ticket istatistik:\n";
    $todayMessagePart2 = now()->translatedFormat('d F Y') . " Umran Ticket istatistik:\n";
    $allMessagePart1 = "Genel Umran Ticket istatistik:\n";
    $allMessagePart2 = "Genel Umran Ticket istatistik:\n";

    foreach ($statusesPart1 as $index => $status) {
        $todayMessagePart1 .= $status . "=" . $todayStats[$index] . "\n";
        $allMessagePart1 .= $status . "=" . $allStats[$index] . "\n";
    }

    foreach ($statusesPart2 as $index => $status) {
        $todayMessagePart2 .= $status . "=" . $todayStats[$index] . "\n";
        $allMessagePart2 .= $status . "=" . $allStats[$index] . "\n";
    }

// Send today's stats SMS part 1
    foreach ($telephones as $phone) {
        $sms = sendSmsOTP($settings->netgsm_usercode, $settings->netgsm_password, $settings->netgsm_msgheader2, $todayMessagePart1, $phone);
        Log::info($sms);
    }

// Send today's stats SMS part 2
    foreach ($telephones as $phone) {
        $sms = sendSmsOTP($settings->netgsm_usercode, $settings->netgsm_password, $settings->netgsm_msgheader2, $todayMessagePart2, $phone);
        Log::info($sms);
    }

// Send general stats SMS part 1
    foreach ($telephones as $phone) {
        $sms = sendSmsOTP($settings->netgsm_usercode, $settings->netgsm_password, $settings->netgsm_msgheader2, $allMessagePart1, $phone);
        Log::info($sms);
    }

// Send general stats SMS part 2
    foreach ($telephones as $phone) {
        $sms = sendSmsOTP($settings->netgsm_usercode, $settings->netgsm_password, $settings->netgsm_msgheader2, $allMessagePart2, $phone);
        Log::info($sms);
    }

    Log::info($todayMessagePart1);
    Log::info($todayMessagePart2);
    Log::info($allMessagePart1);
    Log::info($allMessagePart2);

});

Route::post('/create-token', function (Request $request) {
    if (is_null($request->telephone) || is_null($request->password))
        return response()->json(['success'=>'false','message'=>'Telefon veya Şifre Girmediniz'], 401);
    $user = \App\Models\User::where('telephone', $request->telephone)
        ->first();

    if (!$user)
        return response()->json(['success'=>'false','message'=>'Kullanıcı Bulunamadı!'], 401);

    if ($user->status != 1)
        return response()->json(['success'=>'false','message'=>'Kullanıcı Aktif Değil!'], 401);

    if (!\Illuminate\Support\Facades\Hash::check($request->password, $user->password)) {
        return response()->json(['success'=>'false','message' => 'Yanlış Şifre'], 401);
    }
    $userBranchCodes = \App\Models\Branch::whereIn('id',$user->getBranchIds())->where('status',1)->pluck('belge_kod')->toArray();
    // Token oluştur ve kullanıcıya döndür
    $token = $user->createToken('API Token')->plainTextToken;

    return response()->json(['success'=>'true','token' => $token,'userBranchCodes' => $userBranchCodes], 200);
});

Route::middleware(['apiAuth'])->group(function (){
    Route::post('/update-expertise-brake',function (Request $request){
        $cars = \App\Models\Car::where('plaka',str_replace(' ','',$request->plaka))
            ->where('sase_no',$request->sase_no)
            ->pluck('id')->toArray();
        if (count($cars) < 1)
            return response()->json(['success'=>'false','message'=>'Araç Bulunamadı']);

        $branch = \App\Models\Branch::where('belge_kod',$request->branch_code)->first();
        if (!$branch)
            return response()->json(['success'=>'false','message'=>'Bayi Bulunamadı']);

        $authUser = auth('sanctum')->user();
        $authUserBranchIds = $authUser->getBranchIds();
        if (!in_array($branch->id,$authUserBranchIds))
            return response()->json(['success'=>'false','message'=>'Bu Bayide Yetkiniz Bulunmamaktadır.']);

        $expertises = \App\Models\Expertise::where('sase_no',$request->sase_no)
            ->whereIn('car_id',$cars)
            ->whereDate('belge_tarihi',\Carbon\Carbon::parse($request->belge_tarihi)->format('Y-m-d'))
            ->where('status',1)
            ->where('branch_id',$branch->id)
            ->get();
        if (count($expertises) < 1)
            return response()->json(['success'=>'false','message'=>'Ekspertiz Bulunamadı']);
        foreach ($expertises as $expertise){
            if ($expertise && $expertise->ftp_ok != 1 && $expertise->getStockhasOne?->getStock?->brake == 1){
                $expertiseBrake = \App\Models\ExpertiseBrake::where('expertise_id',$expertise->id)->first();
                if (!$expertiseBrake)
                    $expertiseBrake = new \App\Models\ExpertiseBrake();
                $expertiseBrake->expertise_id = $expertise->id;
                $expertiseBrake->date = now();
                $expertiseBrake->yanal_kayma_on = str_replace(',','.',str_replace('.','',$request->yanal_kayma_on));
                $expertiseBrake->yanal_kayma_arka = str_replace(',','.',str_replace('.','',$request->yanal_kayma_arka));
                $expertiseBrake->max_kuvvet_on_1 = str_replace(',','.',str_replace('.','',$request->max_kuvvet_on_1));
                $expertiseBrake->max_kuvvet_on_2 = str_replace(',','.',str_replace('.','',$request->max_kuvvet_on_2));
                $expertiseBrake->max_kuvvet_arka_1 = str_replace(',','.',str_replace('.','',$request->max_kuvvet_arka_1));
                $expertiseBrake->max_kuvvet_arka_2 = str_replace(',','.',str_replace('.','',$request->max_kuvvet_arka_2));
                $expertiseBrake->dengesizlik_orani_on = str_replace(',','.',str_replace('.','',$request->dengesizlik_orani_on));
                $expertiseBrake->dengesizlik_orani_arka = str_replace(',','.',str_replace('.','',$request->dengesizlik_orani_arka));
                $expertiseBrake->yalpa_orani_on_1 = str_replace(',','.',str_replace('.','',$request->yalpa_orani_on_1));
                $expertiseBrake->yalpa_orani_on_2 = str_replace(',','.',str_replace('.','',$request->yalpa_orani_on_2));
                $expertiseBrake->yalpa_orani_arka_1 = str_replace(',','.',str_replace('.','',$request->yalpa_orani_arka_1));
                $expertiseBrake->yalpa_orani_arka_2 = str_replace(',','.',str_replace('.','',$request->yalpa_orani_arka_2));
                $expertiseBrake->suspansiyon_on_1 = str_replace(',','.',str_replace('.','',$request->suspansiyon_on_1));
                $expertiseBrake->suspansiyon_on_2 = str_replace(',','.',str_replace('.','',$request->suspansiyon_on_2));
                $expertiseBrake->suspansiyon_arka_1 = str_replace(',','.',str_replace('.','',$request->suspansiyon_arka_1));
                $expertiseBrake->suspansiyon_arka_2 = str_replace(',','.',str_replace('.','',$request->suspansiyon_arka_2));
                $expertiseBrake->on_dingil_bosta_a = str_replace(',','.',str_replace('.','',$request->on_dingil_bosta_a));
                $expertiseBrake->on_dingil_bosta_b = str_replace(',','.',str_replace('.','',$request->on_dingil_bosta_b));
                $expertiseBrake->arka_dingil_bosta_a = str_replace(',','.',str_replace('.','',$request->arka_dingil_bosta_a));
                $expertiseBrake->arka_dingil_bosta_b = str_replace(',','.',str_replace('.','',$request->arka_dingil_bosta_b));
                $expertiseBrake->el_freni_kuvvet_a = str_replace(',','.',str_replace('.','',$request->el_freni_kuvvet_a));
                $expertiseBrake->el_freni_kuvvet_b = str_replace(',','.',str_replace('.','',$request->el_freni_kuvvet_b));
                $expertiseBrake->el_freni_dengesizlik_orani = str_replace(',','.',str_replace('.','',$request->el_freni_dengesizlik_orani));
                $expertiseBrake->type = 'auto';
                $expertiseBrake->save();

                $expertise->fren_kontrol = 1;
                $expertise->fren_kontrol_user = $authUser->id;
                $expertise->save();
            }
        }
        return response()->json(['success'=>'true','message'=>'Kayıt Başarılı']);
    });

    Route::post('/update-expertise-dyno',function (Request $request){
        $cars = \App\Models\Car::where('plaka',str_replace(' ','',$request->plaka))
            ->where('sase_no',$request->sase_no)
            ->pluck('id')->toArray();
        if (count($cars) < 1)
            return response()->json(['success'=>'false','message'=>'Araç Bulunamadı']);

        $branch = \App\Models\Branch::where('belge_kod',$request->branch_code)->first();
        if (!$branch)
            return response()->json(['success'=>'false','message'=>'Bayi Bulunamadı']);

        $authUser = auth('sanctum')->user();
        $authUserBranchIds = $authUser->getBranchIds();
        if (!in_array($branch->id,$authUserBranchIds))
            return response()->json(['success'=>'false','message'=>'Bu Bayide Yetkiniz Bulunmamaktadır.']);

        $expertises = \App\Models\Expertise::where('sase_no',$request->sase_no)
            ->whereIn('car_id',$cars)
            ->whereDate('belge_tarihi',\Carbon\Carbon::parse($request->belge_tarihi)->format('Y-m-d'))
            ->where('status',1)
            ->where('branch_id',$branch->id)
            ->get();
        if (count($expertises) < 1)
            return response()->json(['success'=>'false','message'=>'Ekspertiz Bulunamadı']);
        foreach ($expertises as $expertise){
            if (!$expertise){
                return response()->json(['success'=>'false','message'=>'Kayıt Bulunamadı']);
            }elseif ($expertise->ftp_ok == 1){
                return response()->json(['success'=>'false','message'=>'Kayıt Kapanmış']);
            }elseif ($expertise->getStockhasOne?->getStock?->component != 1){
                return response()->json(['success'=>'false','message'=>'Komponent Stok Bulunamadı!']);
            }else{
                $componentDyno = \App\Models\ComponentDyno::where('expertise_id',$expertise->id)->first();
                if (!$componentDyno)
                    $componentDyno = new \App\Models\ComponentDyno();
                $componentDyno->expertise_id = $expertise->id;
                $componentDyno->measured_kw = (float)replaceFloat($request->measured_kw);
                $componentDyno->measured_hp = (float)replaceFloat($request->measured_hp);
                $componentDyno->calculated_kw = (float)replaceFloat($request->calculated_kw);
                $componentDyno->calculated_hp = (float)replaceFloat($request->calculated_hp);
                $componentDyno->calculated_rpm = (float)replaceFloat($request->calculated_rpm);
                $componentDyno->transfer_kw = (float)replaceFloat($request->transfer_kw);
                $componentDyno->transfer_hp = (float)replaceFloat($request->transfer_hp);
                $componentDyno->transfer_rpm = (float)replaceFloat($request->transfer_rpm);
                $componentDyno->type = 'auto';
                $componentDyno->save();

                $expertise->komponent_kontrol = 1;
                $expertise->komponent_kontrol_user = $authUser->id;
                $expertise->save();
            }
        }
        return response()->json(['success'=>'true','message'=>'Kayıt Başarılı']);
    });
});

Route::get('/updatedPlusCardBalance', function () {
    // İlk olarak PlusCardCrediAndPuanAdd kayıtlarını gruplandırarak topluca çekiyoruz
    $adds = PlusCardCrediAndPuanAdd::where('balance_type', 'credits')
        ->where('valid_date', '<=', now()->format('Y-m-d'))
        ->where('payment_type', '!=', 'expired_transfer')
        ->get()
        ->groupBy('card_id');

    // PlusCard ID'lerini topluyoruz
    $plusCardIds = $adds->keys()->toArray();

    // PlusCard ve ilgili müşteri bilgilerini eager loading ile topluca çekiyoruz
    $plusCards = PlusCard::with('getCustomer')->whereIn('id', $plusCardIds)->get()->keyBy('id');

    // Silinen kredi ve puan kayıtlarını topluca çekiyoruz
    $removeCounts = \App\Models\PlusCardCrediAndPuanRemove::whereIn('plus_card_credi_and_puan_add_id', $adds->flatten()->pluck('id'))
        ->where('created_at', '<', '2024-10-01')  // Burada 1 Ekim 2024 tarihinden önceki kayıtları alıyoruz
        ->selectRaw('plus_card_credi_and_puan_add_id, SUM(amount) as total_removed')
        ->groupBy('plus_card_credi_and_puan_add_id')
        ->pluck('total_removed', 'plus_card_credi_and_puan_add_id');


    // ExpertisePayment'ı topluca çekiyoruz
    $expertisePayments = ExpertisePayment::where('type', 'plus_kart')
        ->whereIn('payment_detail', $adds->flatten()->pluck('id'))
        ->selectRaw('payment_detail, COUNT(*) as total_expertises')
        ->groupBy('payment_detail')
        ->pluck('total_expertises', 'payment_detail');

    $branches = \App\Models\Branch::select('id','kisa_ad')->get()->keyBy('id')->toArray();


    echo "<link id=\"style\" href=\"/assets/libs/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\" >";
    echo "<div class='table-responsive'>";
    echo "<table class='table table-bordered table-striped' style='white-space: nowrap'>";
    echo "<thead>";
    echo "<tr style='position:sticky;top: 0;'>";
    echo "<th>#</th>";
    echo "<th>Yükleme ID</th>";
    echo "<th>Kart No</th>";
    echo "<th>Kart ID</th>";
    echo "<th>Yükleme Yapan Bayi</th>";
    echo "<th>Yükleme Tarihi</th>";
    echo "<th>Son Kullanma Tarihi</th>";
    echo "<th>Müşteri Ad</th>";
    echo "<th>Müşteri Telefon</th>";
    echo "<th>Yükleme Birim Fiyat</th>";
    echo "<th>Yükleme Adet</th>";
    echo "<th>Kalan Adet</th>";
    echo "<th>Bölüm Sonucu</th>";
    echo "<th>Yeni Bakiye</th>";
    echo "</tr>";
    echo "</thead>";
    echo "<tbody>";

    // Kayıtları döngüyle yazdırıyoruz
    foreach ($adds as $cardID => $addGroup) {
        $plusCard = $plusCards->get($cardID);

        if ($plusCard) {
            $customer = $plusCard->getCustomer;

            foreach ($addGroup as $index => $item) {
                $user = \App\Models\User::select('branch_id')->where('id',$item->user_id)->first();
                // ExpertisePayment ve Remove işlemleri için toplu olarak getirilen verileri kullanıyoruz
                $itemExpertisesCount = $expertisePayments->get($item->id, 0); // Eğer yoksa 0
                $removeCount = $removeCounts->get($item->id, 0); // Eğer yoksa 0
                $left = $item->credi - $itemExpertisesCount - $removeCount;
                echo "<tr>
                    <td>".($index+1)."</td>
                    <td>{$item->id}</td>
                    <td>{$plusCard->no}</td>
                    <td>{$plusCard->system_id}</td>
                    <td>" .($user ? $branches[$user->branch_id]['kisa_ad'] : '') . "</td>
                    <td>{$item->created_at->translatedFormat('d F Y')}</td>
                    <td>".\Carbon\Carbon::make($item->valid_date)->translatedFormat('d F Y')."</td>
                    <td>{$customer->fullName}</td>
                    <td>" . ($customer->telephone ? $customer->telephone : $customer->cep) . "</td>
                    <td>{$item->unit_price}₺</td>
                    <td>{$item->credi}</td>
                    <td>" . $left . "</td>
                    <td>" . (number_format($left * $item->unit_price / 2600,2)) . "</td>
                    <td>" . round($left * $item->unit_price / 2600) . "</td>
                </tr>";
            }
        }
    }

    echo "</tbody>";
    echo "</table>";
    echo "</div>";
})->middleware('userAuth');


// New API
Route::prefix('v1')->name('api.v1.')->group(function () {
    // Customer Routes
    Route::prefix('customer')->name('customer.')->group(function () {
        // Auth routes
        // Route::post('login', [App\Http\Controllers\Api\V1\Customer\AuthController::class, 'login']);
        // Route::post('login-verify', [App\Http\Controllers\Api\V1\Customer\AuthController::class, 'loginVerify']);

        // Protected routes
        // Route::middleware(['auth:sanctum'])->group(function () {
        // Route::post('logout', [App\Http\Controllers\Api\V1\Customer\AuthController::class, 'logout']);

        // Reports
        Route::apiResource(
            'expertise-reports',
            App\Http\Controllers\Api\V1\Customer\ExpertiseReportController::class
        )->only(['show']);

        //            Route::get('expertise-reports/{report}/download', [App\Http\Controllers\Api\V1\Customer\ReportController::class, 'download']);
        // });
    });

    // Admin Routes
    //    Route::prefix('admin')->name('admin.')->group(function () {
    //        // Auth routes
    //        Route::post('login', [App\Http\Controllers\Api\V1\Admin\AuthController::class, 'login']);
    //
    //        // Protected routes
    //        Route::middleware(['auth:sanctum', 'admin'])->group(function () {
    //            Route::post('logout', [App\Http\Controllers\Api\V1\Admin\AuthController::class, 'logout']);
    //
    //            // Dashboard
    //            Route::get('dashboard', [App\Http\Controllers\Api\V1\Admin\DashboardController::class, 'index']);
    //
    //        });
    //    });
});


