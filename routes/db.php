<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\DB;

Route::middleware('userAuth')->group(function () {
    Route::get('/lostPlusCards', function () {
        $rows = DB::table('expertises')
            ->join('customers', 'expertises.alici_id', '=', 'customers.id')
            ->join('t400_srvbaslik', 'expertises.uuid', '=', 't400_srvbaslik.t400_uq')
            ->select(
                'expertises.id',
                'customers.kod',
                'expertises.created_at',
                't400_srvbaslik.T400_harcanan_plus_UQ'
            )
            ->whereNotIn('expertises.id', function ($query) {
                $query->select('expertise_id')
                    ->from('expertise_payments');
            })
            ->get();
        echo "Toplam ".count($rows)." kayıt bulundu <br>";

        $insert = [];

        foreach ($rows as $row) {
            $customers = DB::table('customers')
                ->select('id', 'kod')
                ->where('kod', $row->kod)
                ->get();
            if (count($customers) > 1){
                echo $row->kod . " " . count($customers) . " adet bulundu. <br>";
                foreach ($customers as $customer) {
                    $plusCard = DB::table('plus_cards')
                        ->where('customer_id', $customer->id)
                        ->select('id')
                        ->first();

                    if ($plusCard) {
                        echo $customers[0]['kod'] . " " . $plusCard->id ." eşleşti. <br>";
                        $kuponTakip = DB::table('t404_kupontakip')
                            ->where('T404_UQ', $row->T400_harcanan_plus_UQ)
                            ->select('T404_fiyat')
                            ->first();

                        if ($kuponTakip) {
                            $insert[] = [
                                $row->id,
                                0,
                                $kuponTakip->T404_fiyat,
                                'plus_kart',
                                $plusCard->id,
                                $row->created_at,
                                $row->created_at,
                                2,
                            ];
                        }

                        break; // Exit the inner loop after finding a matching plusCard
                    }
                }
            }else{
                echo $row->kod . " tek kayıt. <br>";
            }

        }

//        dd($insert);
    });


    Route::get('{query}',[\App\Http\Controllers\NewMigrationController::class,'query']);
});
