<?php

use App\Models\UserBranch;
use Illuminate\Support\Facades\Route;
use App\Models\Branch;
use App\Models\Customer;
use App\Models\Expertise;
use App\Models\PlusCard;
use Illuminate\Support\Facades\DB;


Route::get('/test-2-plus-card',function(){
    $plus_cards = \App\Models\PlusCard::select('customer_id', \DB::raw('MAX(id) as max_id'))
        ->where('customer_id', '>=',0)
        ->where(function ($query) {
            $query->where('credits_update', 2)
                ->orWhere('points_update', 2);
        })
        ->groupBy('customer_id')
        ->get();
    $filtered_plus_cards = \App\Models\PlusCard::whereIn('id', $plus_cards->pluck('max_id'))->get();
    foreach($filtered_plus_cards as $pc) {
        $kod = null;
        $belge_kod = null;
        if(!empty($pc->getCustomer->kod)){
            $kod = $pc->getCustomer->kod;
        }
        if(!empty($pc->getBranch->belge_kod)){
            $belge_kod = $pc->getBranch->belge_kod;
        }

        if($pc->credits_update == 2){
            $balance_credits = DB::connection('mysql2')
                ->table('T404_KUPONTAKIP')
                ->join('T203_STOKLAR','T203_STOKLAR.T203_UQ','=','T404_KUPONTAKIP.T404_stok_UQ')
                ->where('T404_hesap_UQ', $kod)
                ->where('T404_kayittipi', 1)
                ->where('T404_islemtipi', 2)
                ->get();
            foreach ($balance_credits as $bc) {

                $total_credits_balance = 0;

                $balance_payment = DB::connection('mysql2')
                    ->table('T404_KUPONTAKIP')
                    ->where('T404_hesap_UQ', $pc->getCustomer->kod)
                    ->where('T404_kayittipi', '1')
                    ->where('T404_islemtipi',  '-1')
                    ->where('T404_iliski_UQ', $bc->T404_UQ)
                    ->sum('T404_miktar');


                $total_credits_balance = $total_credits_balance + (floatval($bc->T404_miktar) - floatval($balance_payment));
                if ($total_credits_balance > 0) {
                    echo $pc->no." - ".$total_credits_balance."<br>";
                    $plus_card_definitions = \App\Models\PlusCardsDefinitions::where('stok_uq',$bc->T404_stok_UQ)->first();
                    if(!empty($plus_card_definitions)){
                        $new_balance = new \App\Models\PlusCardCrediAndPuanAdd();
                        $new_balance->user_id = 1;
                        $new_balance->stock_id = 7;
                        $new_balance->definitions_id = $plus_card_definitions->id;
                        $new_balance->balance_type = "credits";
                        $new_balance->card_id = $pc->id;
                        $new_balance->unit_price = $bc->T404_fiyat;
                        $new_balance->commission = $bc->T404_komisyon;
                        $new_balance->credits_amount = $bc->T404_tutar;
                        $new_balance->credi = $total_credits_balance;
                        $new_balance->puan = 0;
                        $new_balance->puan_branche_id = 0;
                        $new_balance->odenen_kdv_dahil_fiyat = $bc->T404_tutar;
                        $new_balance->payment_type = "yeni_sistem_bakiye_aktirimi";
                        $new_balance->case = 0;
                        $new_balance->valid_date = $bc->T404_sonkullanimtarihi;
//                        $new_balance->save();
                    }else{
                    }
                }

            }

        }
        if($pc->points_update == 2) {
            $kod = null;
            $belge_kod = null;
            if(!empty($pc->getCustomer->kod)){
                $kod = $pc->getCustomer->kod;
            }
            if(!empty($pc->getBranch->belge_kod)){
                $belge_kod = $pc->getBranch->belge_kod;
            }
            $balance_points = DB::connection('mysql2')
                ->table('T404_KUPONTAKIP')
                ->select(
                    'T404_UQ',
                    'T404_miktar',
                    'T404_subekodu',
                    'T404_fiyat',
                    'T404_komisyon',
                    'T404_tutar',
                    'T404_sonkullanimtarihi',
                )
                ->join('T203_STOKLAR','T203_STOKLAR.T203_UQ','=','T404_KUPONTAKIP.T404_stok_UQ')
                ->where('T404_hesap_UQ', $kod)
                ->where('T404_kayittipi', 2)
                ->where('T404_islemtipi', 2)
                ->get();

            foreach($balance_points as $bc){
                $total_points_balance = 0;
                $balance_payment = DB::connection('mysql2')
                    ->table('T404_KUPONTAKIP')
                    ->where('T404_hesap_UQ', $pc->getCustomer->kod)
                    ->where('T404_kayittipi', 2)
                    ->where('T404_islemtipi', -1)
                    ->where('T404_iliski_UQ', $bc->T404_UQ)
                    ->sum('T404_miktar');

                $total_points_balance = $total_points_balance + (floatval($bc->T404_miktar) - floatval($balance_payment));
                if($total_points_balance > 0){
                    echo $pc->no." - ".$total_points_balance."<br>";

                    $subekodu = null;
                    if($bc->T404_subekodu < 10){
                        $subekodu = "00".$bc->T404_subekodu;
                    }elseif($bc->T404_subekodu < 100){
                        $subekodu = "0".$bc->T404_subekodu;
                    }else{
                        $subekodu =$bc->T404_subekodu;
                    }
                    $branches = \App\Models\Branch::where('belge_kod',$subekodu)->first();
                    if(!empty($branches)){
                        $new_balance = new \App\Models\PlusCardCrediAndPuanAdd();
                        $new_balance->user_id = 1;
                        $new_balance->stock_id = 7;
                        $new_balance->definitions_id = 0;
                        $new_balance->balance_type = "points";
                        $new_balance->card_id = $pc->id;
                        $new_balance->unit_price = $bc->T404_fiyat;
                        $new_balance->commission = $bc->T404_komisyon;
                        $new_balance->credits_amount = $bc->T404_tutar;
                        $new_balance->credi = 0;
                        $new_balance->puan = $total_points_balance;
                        $new_balance->puan_branche_id = $branches->id;
                        $new_balance->odenen_kdv_dahil_fiyat = $bc->T404_tutar;
                        $new_balance->payment_type = "yeni_sistem_bakiye_aktirimi";
                        $new_balance->case = 0;
                        $new_balance->valid_date = $bc->T404_sonkullanimtarihi;
//                        $new_balance->save();

                    }else{
                        echo "şube kod bulunamadı - ".$bc->T404_subekodu."<br>";
                    }

                }
            }
        }


        $pc_update = \App\Models\PlusCard::find($pc->id);
        $pc_update->credits_update = 1;
        $pc_update->points_update = 1;
//        $pc_update->save();
    }
});
Route::get('plus_card_transfer',function(){
    $old_card = DB::connection('mysql2')
        ->table('T212_KARTNUMARALARI')
        ->where('new_data',2)
        ->get();
    foreach($old_card as $old){
        $new_no = str_replace(' ','',$old->T212_kartno);
        $check = PlusCard::where('no',$new_no)->count();
        if($check == 0){
            $customer_id = null;
            if(!empty($old->T212_musteri_UQ)){
                $customer = Customer::where('kod',$old->T212_musteri_UQ)
                    ->first();
                $customer_id = !empty($customer) ? $customer->id : null;
            }
            $branches = Branch::where('belge_kod',$old->T212_subekodu)->first();
            $new_card = new PlusCard();
            $new_card->karttipi = $old->T212_karttipi;
            $new_card->system_id = $old->T212_kartid;
            $new_card->no = $new_no;
            $new_card->branch_id = $branches->id ?? 1;
            $new_card->customer_id = $customer_id;
            $new_card->kredi = "0.00";
            $new_card->valid_date_if = 0;
            $new_card->valid_date = "2025-01-17";
//            $new_card->save();
        }

        DB::connection('mysql2')->table('T212_KARTNUMARALARI')
            ->where('T212_ID', $old->T212_ID)
            ->update(['new_data'=>1]);
    }
});
Route::get('/cari_transfer_list',function(){
    \App\Jobs\CustomerTransfer::dispatch();
    //$return = \App\Jobs\TransferCarsJobNew::dispatch();
    dd("bekleyin");
});
Route::get('plus_card_repeat_data',function(){
    $uniqueRows = \App\Models\PlusCardCrediAndPuanAdd::selectRaw('MIN(id) as id, stock_id, definitions_id, balance_type, card_id, unit_price, credi')
        ->where('payment_type', 'yeni_sistem_bakiye_aktirimi')
        ->groupBy('stock_id', 'definitions_id', 'balance_type', 'card_id', 'unit_price', 'credi')
        ->get();
    foreach($uniqueRows as $item){
        $delete = \App\Models\PlusCardCrediAndPuanAdd::find($item->id);
        $delete->delete();
    }
    dd(count($uniqueRows));
});

Route::get('/plus_card_expertise',function(){
    $expertise_payment = \App\Models\ExpertisePayment::where('type','plus_kart')
        ->where('update_data',2)
        ->get();
    $i = 1;
    foreach($expertise_payment as $item){
        $check = \App\Models\PlusCardCrediAndPuanAdd::find($item->plus_card_odeme_id);
        if(empty($check)){
            $odeme = \App\Models\PlusCardCrediAndPuanAdd::where('card_id',$item->plus_card_id)->orderBy('id','asc')->first();
            if(!empty($odeme)){
                $update_expertise = \App\Models\ExpertisePayment::find($item->id);
                $update_expertise->plus_card_odeme_id = $odeme->id;
//                $update_expertise->save();

                $update_data_colum = \App\Models\ExpertisePayment::find($item->id);
                $update_data_colum->update_data = 1;
//                $update_data_colum->save();
            }else{
                echo $item->plus_card_id.",";
            }
        }
    }
});

Route::get('/branchesForUmranoto',function (){
    $branches = \App\Models\Branch::where('status',1)->select('unvan','il_kodu','ilce_kodu','yetkili_ad_soyad','yetkili_dahili','email','mahalle','cadde','sokak','semt','konum','lat','lng')->get();
    return response()->json(['branches'=>$branches]);
});

Route::middleware('userAuth')->group(function (){
    Route::get('/api/check', function () {
        $expertises = \App\Models\Expertise::whereHas('getPayments', function ($query) {
            return $query->where('type', 'acik_hesap')->whereDate('created_at', '>=', '2024-03-22');
        })->get();
        $branches = \App\Models\Branch::all();
        $subeler = [];
        foreach ($branches as $branch){
            $subeler[$branch->id] = $branch->kisa_ad;
        }

        // HTML tablo başlangıcı
        echo '<table border="1">';
        echo '<tr><th>Sıra</th><th>Tarih</th><th>Belge Numarası</th><th>Bayi</th><th>Durum</th><th>Plus Card Var/Yok</th><th>Ekspertiz Linki</th><th>Cari Linki</th><th>Plus Card Detay</th><th>Puan</th><th>Kredi</th><th>Telefon</th><th>Bulunamadı</th></tr>';

        foreach ($expertises as $index => $expertise) {
            echo '<tr>';

            // Sıra numarası
            echo '<td>' . ($index + 1) . '</td>';
            echo '<td>' . ($expertise->created_at->format('d.m.Y H:i')) . '</td>';
            echo '<td>' . ($expertise->belge_no) . '</td>';
            echo '<td>' . ($subeler[$expertise->branch_id] ?? '') . '</td>';

            // Durum (Plus Card var/yok)
            $status = count($expertise->getAlici->getPlusCards) ? '<span style="color: forestgreen">Plus Card Var</span>' : '<span style="color: red">Plus Card Yok</span>';
            echo '<td>' . $status . '</td>';

            // Ekspertiz Linki
            echo '<td><a style="color:#aa00ff" target="_blank" href="' . route('expertises.edit', [$expertise->uuid]) . '">Ekspertiz Linki</a></td>';

            // Cari Linki
            echo '<td><a style="color:#c97855" target="_blank" href="' . route('customers.edit', [$expertise->getAlici]) . '">Cari Linki (' . $expertise->getAlici->fullName . ')</a></td>';

            // Plus Card Detay
            echo '<td>';
            if (count($expertise->getAlici->getPlusCards)) {
                foreach ($expertise->getAlici->getPlusCards as $getPlusCard) {
                    echo '<a style="color:#416cd7" target="_blank" href="' . route('plus-cards.edit', [$getPlusCard]) . '">Plus Card Detay</a> Puan:' . $getPlusCard->getTotalBalance()['points'] . ' Kredi:' . $getPlusCard->getTotalBalance()['credits'] . '<br>';
                }
            }
            echo '</td>';

            // Telefon
            echo '<td><span onclick="copy(' . str_replace(' ', '', $expertise->getAlici->phone()) . ')">' . $expertise->getAlici->phone() . '</span></td>';
            echo '<td><input type="checkbox" name="checklist[]" value="'.$expertise->id.'"></td>';
            echo '</tr>';
        }

        // HTML tablo sonu
        echo '</table>';

        // Kopyalama için JavaScript
        echo '<script>function copy(txt){navigator.clipboard.writeText(txt).then(() => {}).catch(err => {});}</script>';
        echo '<script>function copy(txt){navigator.clipboard.writeText(txt).then(() => {}).catch(err => {});}</script>';
    });

    Route::get('/api/check',function (){
        $expertises = \App\Models\Expertise::whereHas('getPayments', function ($query) {
            return $query->where('type', 'acik_hesap')->whereDate('created_at', '>=', '2024-03-22');
        })->get();
        $branches = \App\Models\Branch::all();
        $subeler = [];
        foreach ($branches as $branch){
            $subeler[$branch->id] = $branch->kisa_ad;
        }
        $checkedItems = \App\Models\CheckListControl::pluck('expertise_id')->toArray();


        return view('pages.check_list',compact(['expertises','subeler','checkedItems']));
    });

    Route::get('/maxExpertiseUsers',function (){
        if (isset($_GET['id']) && isset($_GET['type'])){
            Expertise::where([$_GET['type']=>$_GET['id'],'manuel_save'=>0])->delete();
        }
        $expertises = DB::table('expertises')
            ->select('cari_id', DB::raw('COUNT(*) as count'))
            ->where('manuel_save', 0)
            ->groupBy('cari_id')
            ->having('count', 5)
            ->get();
        foreach ($expertises as $customerId => $count){
            $customer = Customer::where('id',$customerId)->first();
            if ($customer)
                echo "<p>$customer->fullName => $count => <a href='/maxExpertiseUsers?id=$customer->id&type=cari_id'>Sonlandır</a> </p>";
        }
        $expertises = DB::table('expertises')
            ->select('satici_id', DB::raw('COUNT(*) as count'))
            ->where('manuel_save', 0)
            ->groupBy('satici_id')
            ->having('count', 5)
            ->get();
        foreach ($expertises as $customerId => $count){
            $customer = Customer::where('id',$customerId)->first();
            if ($customer)
                echo "<p>$customer->fullName => $count => <a href='/maxExpertiseUsers?id=$customer->id&type=satici_id'>Sonlandır</a> </p>";
        }
        $expertises = DB::table('expertises')
            ->select('alici_id', DB::raw('COUNT(*) as count'))
            ->where('manuel_save', 0)
            ->groupBy('alici_id')
            ->having('count', 5)
            ->get();
        foreach ($expertises as $customerId => $count){
            $customer = Customer::where('id',$customerId)->first();
            if ($customer)
                echo "<p>$customer->fullName => $count => <a href='/maxExpertiseUsers?id=$customer->id&type=alici_id'>Sonlandır</a> </p>";
        }
    });

    Route::get('/api/checkNewPlusCardWallets',function (){
        $hlxPlusCards =  DB::connection('mysql')
            ->table('hlx_plus_try')
            ->whereNotNull('customer_id')
            ->orderBy('no')
            ->get();
        echo "<link id=\"style\" href=\"/assets/libs/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\" >";
        echo "<table class='table table-bordered table-striped'>";
        echo "<tr style='position:sticky;top: 0;'>";
        echo "<th>No</th>";
        echo "<th>Sistem ID</th>";
        echo "<th>Müşteri Adı</th>";
        echo "<th>Eski Kod</th>";
        echo "<th>Yeni Kod</th>";
        echo "<th>Telefon</th>";
        echo "<th>Deha Kalan Kredi</th>";
        echo "<th>Deha Kalan Puan</th>";
        echo "<th>Umram Yüklenen Kredi</th>";
        echo "<th>Umram Yüklenen Puan</th>";
        echo "<th>Umram Harcamalar</th>";
        echo "<th>Kalan</th>";
        echo "</tr>";

        foreach ($hlxPlusCards as $hlxPlusCard){
            $customer = Customer::where('id',$hlxPlusCard->customer_id)->first();
            $eskiSistemKredi = DB::connection('mysql')
                ->table('plus_card_credi_and_puan_add_try')
                ->where('card_id',$hlxPlusCard->id)
                ->where('balance_type','credits')
                ->where('payment_type','yeni_sistem_bakiye_aktirimi')
                ->sum('credi');
            $eskiSistemPuan = DB::connection('mysql')
                ->table('plus_card_credi_and_puan_add_try')
                ->where('card_id',$hlxPlusCard->id)
                ->where('balance_type','points')
                ->where('payment_type','yeni_sistem_bakiye_aktirimi')
                ->sum('puan');

            $yeniSistemKredi = DB::connection('mysql')
                ->table('plus_card_credi_and_puan_add_try')
                ->where('card_id',$hlxPlusCard->id)
                ->where('balance_type','credits')
                ->where('payment_type','!=','yeni_sistem_bakiye_aktirimi')
                ->sum('credi');
            $yeniSistemPuan = DB::connection('mysql')
                ->table('plus_card_credi_and_puan_add_try')
                ->where('card_id',$hlxPlusCard->id)
                ->where('balance_type','points')
                ->where('payment_type','!=','yeni_sistem_bakiye_aktirimi')
                ->sum('puan');
            $harcamalar = DB::connection('mysql')
                ->table('expertise_payments_try')
                ->where('plus_card_id',$hlxPlusCard->id)
                ->count();
            echo "<tr>";
            echo "<td>" . $hlxPlusCard->no . "</td>";
            echo "<td>" . $hlxPlusCard->system_id . "</td>";
            $customerName = isset($customer) ? $customer->fullName : '';
            echo "<td>" . $customerName . "</td>";
            echo "<td>" . $customer?->eski_cari_kod . "</td>";
            echo "<td>" . $customer?->cari_kod . "</td>";
            echo "<td>" . $customer?->phone() . "</td>";
            echo "<td>" . $eskiSistemKredi . "</td>";
            echo "<td>" . $eskiSistemPuan . "</td>";
            echo "<td>" . $yeniSistemKredi . "</td>";
            echo "<td>" . $yeniSistemPuan . "</td>";
            echo "<td>" . $harcamalar . "</td>";
            echo "<td>" . ($eskiSistemKredi + $eskiSistemPuan + $yeniSistemKredi + $yeniSistemPuan - $harcamalar) . "</td>";
            echo "</tr>";
        }
    });

});
Route::post('/plus-card-info',function (\Illuminate\Http\Request $request){
    $hlxPlusCard =  DB::connection('mysql')
        ->table('hlx_plus_try')
        ->where('no',$request->no)
        ->first();
    if ($hlxPlusCard){
        $customer = Customer::where('id',$hlxPlusCard->customer_id)->first();
        $eskiSistemKredi = DB::connection('mysql')
            ->table('plus_card_credi_and_puan_add_try')
            ->where('card_id',$hlxPlusCard->id)
            ->where('balance_type','credits')
            ->where('payment_type','yeni_sistem_bakiye_aktirimi')
            ->sum('credi');
        $eskiSistemPuan = DB::connection('mysql')
            ->table('plus_card_credi_and_puan_add_try')
            ->where('card_id',$hlxPlusCard->id)
            ->where('balance_type','points')
            ->where('payment_type','yeni_sistem_bakiye_aktirimi')
            ->sum('puan');

        return response()->json([
            'PLUSKART_NO' => $hlxPlusCard->no,
            'SISTEM_ID' => $hlxPlusCard->system_id,
            'CARI_UNVAN' => isset($customer) ? $customer->fullName : '',
            'CARI_ESKI_UQ' => $customer?->kod,
            'CARI_ESKI_KOD' => $customer?->eski_cari_kod,
            'CARI_YENI_KOD' => $customer?->cari_kod,
            'CARI_TELEFON' => $customer?->phone(),
            'DEVIR_KREDI' => (int)$eskiSistemKredi,
            'DEVIR_PUAN' => (int)$eskiSistemPuan,
            'SONUC'=>true
        ]);
    }else{
        return response()->json([
            'SONUC'=>false
        ]);
    }
});

Route::get('/excel_reader',function(){
//    $array = Excel::toArray(new \App\Imports\OtoSorguImport(),'storage/os_price.xlsx');
//dd($array);
    Excel::import(new \App\Imports\OtoSorguImport(),'storage/os_price_modified.xlsx');
    // \App\Jobs\OtoSorgu::dispatch();
});


Route::get('/plusCardWalletMinus', function () {
    // Cache the items for 2 hours (120 minutes)
    $key = 'plus_card_wallet_minus';
    if (isset($_GET['reset']))
        $key .= rand(1,99999);
    $items = \Illuminate\Support\Facades\Cache::remember($key, 120, function () {
        return \App\Models\PlusCardCrediAndPuanAdd::get()->filter(function ($item) {
            return ($item->credi - $item->getRemoves->sum('amount') - $item->getUsages->count()) < 0;
        })->groupBy('card_id');
    });

    echo "<link id=\"style\" href=\"/assets/libs/bootstrap/css/bootstrap.min.css\" rel=\"stylesheet\" >";
    // HTML Başlangıcı
    echo '<table class="table table-bordered table-striped">
            <thead>
                <tr>
                    <th>Kart No</th>
                    <th>Yükleme Yapan Bayi</th>
                    <th>Müşteri Unvanı</th>
                    <th>Müşteri Telefon</th>
                    <th>Yükleme ID</th>
                    <th>Bakiye</th>
                    <th>Güncel Toplam Bakiye</th>
                </tr>
            </thead>
            <tbody>';

    // Her kart için tablo satırlarını oluşturuyoruz
    foreach ($items as $cardID => $values) {
        $plusCard = $values[0]->getPlusCard;
        if ($plusCard){
            $plusCardBranch = $plusCard?->getBranch;
            $plusCardCustomer = $plusCard?->getCustomer;
            foreach ($values as $value) {
                echo '<tr>
                    <td><a href="https://umram.online/plus-cards/' . htmlspecialchars($cardID) . '/edit" target="_blank">Kart Detay</a></td>
                    <td>'.$plusCardBranch?->kisa_ad.'</td>
                    <td>'.$plusCardCustomer?->fullName.'</td>
                    <td>'.($plusCardCustomer?->cep ?? $plusCardCustomer?->telefon).'</td>
                    <td>' . htmlspecialchars($value->id) . '</td>
                    <td>' . htmlspecialchars($value->credi - $value->getRemoves->sum('amount') - $value->getUsages->count()) . '</td>
                    <td>'.$plusCard->getTotalBalance()['credits'].'</td>
                  </tr>';
            }
        }

    }

    // HTML Sonu
    echo '</tbody>
          </table>';
})->middleware('userAuth');

Route::get('/setUserBranches',function (){
   $users = DB::table('users2')->get();
    $bulk = [];
    $branchIds = DB::table('branches2')->pluck('id')->toArray();
   foreach ($users as $user){
       $userBranches = DB::table('user_branches2')->where('user_id',$user->id)->pluck('branch_id')->toArray();
       if ($user->type == 'admin' && $user->status == 1){

           foreach ($branchIds as $branchId){
               if (!in_array($branchId,$userBranches)){
                   $bulk[] = [
                       'user_id' => $user->id,
                       'branch_id' => $branchId,
                   ];
               }
           }
       }else{
           if (!in_array($user->branch_id,$userBranches)){
               $bulk[] = [
                   'user_id' => $user->id,
                   'branch_id' => $user->branch_id,
               ];
           }
       }
   }

    DB::table('user_branches2')->insert($bulk);
})->middleware('userAuth');


